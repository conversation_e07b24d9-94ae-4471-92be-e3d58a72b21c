import { CC<PERSON>nteger, Component, Enum, Label, Layout, Node, Prefab, ScrollView, Tween, UITransform, Vec3, _decorator, instantiate, isValid, log, math, tween, v3, warn } from "cc";

const { ccclass, property, menu, integer } = _decorator;

@ccclass
@menu("用户脚本组件/滚动视图优化组件")
export default class ScrollViewProCom extends ScrollView {


    @property({ displayName: '分帧时间' })
    public intervalT = 0

    @property({ type: Enum({ 节点: 0, 预制体: 1 }), displayName: "item类型" })
    private type: number = 0;

    @property({
        type: Node, visible() {
            return this.type == 0;
        }, displayName: "item节点"
    })
    private itemNode: Node = null;

    @property({
        type: Prefab, visible() {
            return this.type == 1;
        }, displayName: "item预制体"
    })
    private itemPrefab: Prefab = null;

    /** */
    private intervalTimer: any = null;
    public layout: Layout = null;
    private _item: Node = null;
    private nodePool: Array<Node> = null;
    private curShowDatas: Array<ScrollViewProData> = [];

    private allDatas: Array<[number, any]> = [];
    private func: Function = null;

    protected onLoad(): void {

        if (null == this.node.getComponent(ScrollView)) {
            warn('lack of scrollview component');
            return
        };
        this.content = this.node.getComponent(ScrollView).content;
        if (null == this.content) {
            warn('scrollview lack of content');
            return
        };
        this.layout = this.content.getComponent(Layout);
        if (null == this.layout) {
            warn('content lackof layout component');
            return
        };
        this.layout.enabled = false;
        this.node.on(ScrollView.EventType.SCROLLING, this.onScrolling, this);
        this.node.on(Node.EventType.SIZE_CHANGED, this.onSizeChanged, this);
        this.nodePool = [];
        for (let i: number = this.content.children.length - 1; i >= 0; i--) {
            this.nodePool.push(this.content.children[i]);
            this.content.children[i].setPosition(Number.MAX_VALUE, Number.MAX_VALUE);
        }
        this.updateContentSize();
        this.updateListView();
    }

    protected onDestroy(): void {
        this.node.off(ScrollView.EventType.SCROLLING, this.onScrolling, this);
        this.node.off(Node.EventType.SIZE_CHANGED, this.onSizeChanged, this);
        clearInterval(this.intervalTimer);
    }

    /**
     * 设置视图
     * @param datas 用户数据
     * @param func 回调
     */
    public setView(datas: Array<any>, func: (n: Node, data: any, index: number) => void): void {
        this.updateAllDatas(datas);
        this.func = func;
        this.updateContentSize();
        this.updateListView(true);
    }

    private onSizeChanged(): void {
        this.updateListView();
    }

    public onScrolling(): void {
        this.updateListView();
    }

    private get item(): Node {
        if (null == this._item) {
            if (0 == this.type && null != this.itemNode) {
                this._item = instantiate(this.itemNode);
            } else if (1 == this.type && null != this.itemPrefab) {
                this._item = instantiate(this.itemPrefab);
            }
        }

        return this._item;
    }

    /**更新view */
    public updateListView(executeAllCallback: boolean = false): void {
        let datasNeedShow = this.getDatasNeedShow();
        // log('datasNeedShow', datasNeedShow, this.curShowDatas);
        //先把当前展示的item中不需要的移除
        for (let i: number = this.curShowDatas.length - 1; i >= 0; i--) {
            for (var j = 0; j < datasNeedShow.length; j++) {

                if (datasNeedShow[j][1] == this.curShowDatas[i].userData) {
                    break;
                }
            }

            if (j >= datasNeedShow.length) {//不再需要了

                this.nodePool.push(this.curShowDatas[i].n);
                this.curShowDatas[i].n.setPosition(Number.MAX_VALUE, Number.MAX_VALUE);
                this.curShowDatas.splice(i, 1);
            }
        }

        let dataNeedInstantiate: Array<Array<any>> = [];

        let index: number = 0;
        //再添加需要显示的item和更新现有item的坐标
        for (let i: number = 0; i < datasNeedShow.length; i++) {
            for (var j: number = 0; j < this.curShowDatas.length; j++) {
                if (this.curShowDatas[j].userData == datasNeedShow[i][1]) {
                    break;
                }
            }
            if (j < this.curShowDatas.length) {//找到了，更新位置
                let curShowData = this.curShowDatas[j];
                if (curShowData.index != datasNeedShow[i][0]) {//位置不一样
                    curShowData.index = datasNeedShow[i][0];
                    let targetPos: Vec3 = this.getItemPosByIdx(curShowData.index);
                    curShowData.n.setPosition(targetPos);
                }
                true == executeAllCallback && true == isValid(this.func) && this.func(curShowData.n, curShowData.userData, curShowData.index);
            }
            else {//没找到，添加
                let newData = new ScrollViewProData(datasNeedShow[i][0], datasNeedShow[i][1]);
                newData.n = this.nodePool.pop();
                if (null == newData.n) {
                    dataNeedInstantiate.push([i, newData]);
                } else {
                    newData.n.setPosition(this.getItemPosByIdx(newData.index));
                    this.curShowDatas.splice(i, 0, newData);//添加位置是i
                    true == isValid(this.func) && this.func(newData.n, newData.userData, newData.index);
                }
            }
        }

        //分帧添加
        clearInterval(this.intervalTimer);
        this.intervalTimer = setInterval(() => {
            if (index >= dataNeedInstantiate.length) {
                clearInterval(this.intervalTimer);
                return;
            }
            let newData = dataNeedInstantiate[index][1];
            let pos = dataNeedInstantiate[index][0];
            newData.n = instantiate(this.item);
            this.content.addChild(newData.n);
            newData.n.setPosition(this.getItemPosByIdx(newData.index));
            this.curShowDatas.splice(pos, 0, newData);//添加位置是i
            true == isValid(this.func) && this.func(newData.n, newData.userData, newData.index);
            index++;
        }, this.intervalT);
    }

    /**更新总数据 */
    private updateAllDatas(datas: Array<any>): void {
        this.allDatas = [];
        for (let i: number = 0; i < datas.length; i++) {
            this.allDatas.push([i, datas[i]]);
        }
    }

    /**更新content尺寸 */
    private updateContentSize(): void {
        if (null == this.content || null == this.layout) return;
        switch (this.layout.type) {
            case Layout.Type.VERTICAL:
                this.content.getComponent(UITransform).height =
                    this.layout.paddingTop + this.layout.paddingBottom + this.allDatas.length * (this.layout.spacingY + this.item.getComponent(UITransform).height * this.item.scale.y) - this.layout.spacingY;
                break;
            case Layout.Type.HORIZONTAL:
                this.content.getComponent(UITransform).width =
                    this.layout.paddingLeft + this.layout.paddingRight + this.allDatas.length * (this.layout.spacingX + this.item.getComponent(UITransform).width * this.item.scale.x) - this.layout.spacingX;
                break;
            case Layout.Type.GRID:
                /**一行最多有多少个 */
                let cols = Math.floor((this.content.getComponent(UITransform).width - this.layout.paddingLeft - this.layout.paddingRight + this.layout.spacingX)
                    / (this.item.getComponent(UITransform).width * this.item.scale.x + this.layout.spacingX));
                /**一共多少行 */
                let rows = Math.ceil(this.allDatas.length / cols);
                this.content.getComponent(UITransform).height =
                    this.layout.paddingTop + this.layout.paddingBottom + rows * (this.layout.spacingY + this.item.getComponent(UITransform).height * this.item.scale.y) - this.layout.spacingY;


                break;
            default:
                break;
        }
    }

    /**获取需要展示的数据 */
    private getDatasNeedShow(): Array<[number, any]> {
        if (null == this.content || null == this.layout) return [];
        switch (this.layout.type) {
            case Layout.Type.VERTICAL:
                var contentHead: number = this.content.position.y + (1 - this.content.getComponent(UITransform).anchorY) * this.content.getComponent(UITransform).height - (1 - this.node.getComponent(UITransform).anchorY) * this.node.getComponent(UITransform).height;
                if (0 > contentHead) contentHead = 0;
                var contendTail: number = this.node.getComponent(UITransform).height + this.content.position.y;
                if (contendTail < contentHead) contendTail = contentHead;
                if (this.content.getComponent(UITransform).height - this.layout.paddingBottom < contendTail) contendTail = this.content.getComponent(UITransform).height - this.layout.paddingBottom;

                var startIndex = Math.floor(
                    (contentHead - this.layout.paddingTop) / (this.item.getComponent(UITransform).height + this.layout.spacingY)
                );//多显示一个
                if (this.allDatas.length <= startIndex) startIndex = this.allDatas.length - 1;
                if (0 > startIndex) startIndex = 0;

                var endIndex = Math.ceil(
                    (contendTail - this.layout.paddingTop) / (this.item.getComponent(UITransform).height + this.layout.spacingY)
                );//多显示一个
                if (this.allDatas.length <= endIndex) endIndex = this.allDatas.length - 1;
                if (endIndex < startIndex) endIndex = startIndex;
                break;
            case Layout.Type.HORIZONTAL:
                var contentHead: number = -(this.content.position.x - this.content.getComponent(UITransform).anchorX * this.content.getComponent(UITransform).width) + this.node.getComponent(UITransform).anchorX * this.node.getComponent(UITransform).width;
                if (0 > contentHead) contentHead = 0;
                var contendTail: number = this.node.getComponent(UITransform).width - this.content.position.x;
                if (contendTail < contentHead) contendTail = contentHead;
                if (this.content.getComponent(UITransform).width - this.layout.paddingRight < contendTail) contendTail = this.content.getComponent(UITransform).width - this.layout.paddingRight;

                var startIndex = Math.floor(
                    (contentHead - this.layout.paddingLeft) / (this.item.getComponent(UITransform).width + this.layout.spacingX)
                );//多显示一个
                if (this.allDatas.length <= startIndex) startIndex = this.allDatas.length - 1;
                if (0 > startIndex) startIndex = 0;

                var endIndex = Math.ceil(
                    (contendTail - this.layout.paddingLeft) / (this.item.getComponent(UITransform).width + this.layout.spacingX)
                );//多显示一个
                if (this.allDatas.length <= endIndex) endIndex = this.allDatas.length - 1;
                if (endIndex < startIndex) endIndex = startIndex;

                break;
            case Layout.Type.GRID:
                var contentHead: number = this.content.position.y + (1 - this.content.getComponent(UITransform).anchorY) * this.content.getComponent(UITransform).height - (1 - this.node.getComponent(UITransform).anchorY) * this.node.getComponent(UITransform).height;
                if (0 > contentHead) contentHead = 0;
                var contendTail: number = this.node.getComponent(UITransform).height + this.content.position.y;
                if (contendTail < contentHead) contendTail = contentHead;
                if (this.content.getComponent(UITransform).height - this.layout.paddingBottom < contendTail) contendTail = this.content.getComponent(UITransform).height - this.layout.paddingBottom;

                /**一行最多有多少个 */
                let cols = Math.floor((this.content.getComponent(UITransform).width - this.layout.paddingLeft - this.layout.paddingRight + this.layout.spacingX)
                    / (this.item.getComponent(UITransform).width + this.layout.spacingX));
                var startIndex = Math.floor(
                    (contentHead - this.layout.paddingTop) / (this.item.getComponent(UITransform).height + this.layout.spacingY)
                ) * cols;
                if (this.allDatas.length <= startIndex) startIndex = this.allDatas.length - 1;
                if (0 > startIndex) startIndex = 0;

                var endIndex = Math.ceil(
                    (contendTail - this.layout.paddingTop) / (this.item.getComponent(UITransform).height + this.layout.spacingY)
                ) * cols - 1;
                if (this.allDatas.length <= endIndex) endIndex = this.allDatas.length - 1;
                if (endIndex < startIndex) endIndex = startIndex;
                break;
            default:
                break;
        }

        let ret: Array<[number, any]> = [];
        for (let i: number = startIndex; i < Math.min(this.allDatas.length, endIndex + 1); i++) {
            ret.push(this.allDatas[i]);
        }
        return ret;
    }

    /**根据节点下标获取节点应该所处的位置 */
    public getItemPosByIdx(index: number): Vec3 {
        if (null == this.content || null == this.layout) return v3();
        switch (this.layout.type) {
            case Layout.Type.VERTICAL:
                var deltaY = this.layout.paddingTop
                    + (1 - this.content.getComponent(UITransform).anchorY) * this.content.getComponent(UITransform).height
                    + (1 - this.item.getComponent(UITransform).anchorY) * this.item.getComponent(UITransform).height * this.item.scale.y
                    + index * (this.item.getComponent(UITransform).height * this.item.scale.y + this.layout.spacingY);
                return v3(0, -deltaY, 0);
            case Layout.Type.HORIZONTAL:
                var deltaX = this.layout.paddingLeft
                    - this.content.getComponent(UITransform).anchorX * this.content.getComponent(UITransform).width
                    + (this.item.getComponent(UITransform).anchorX) * this.item.getComponent(UITransform).width * this.item.scale.x
                    + index * (this.item.getComponent(UITransform).width * this.item.scale.x + this.layout.spacingX);
                return v3(deltaX, 0, 0);
            case Layout.Type.GRID:
                /**一行最多有多少个 */
                let cols = Math.floor((this.content.getComponent(UITransform).width - this.layout.paddingLeft - this.layout.paddingRight + this.layout.spacingX)
                    / (this.item.getComponent(UITransform).width * this.item.scale.x + this.layout.spacingX));
                var deltaY = this.layout.paddingTop
                    + (1 - this.content.getComponent(UITransform).anchorY) * this.content.getComponent(UITransform).height
                    + (1 - this.item.getComponent(UITransform).anchorY) * this.item.getComponent(UITransform).height * this.item.scale.y
                    + Math.floor(index / cols) * (this.item.getComponent(UITransform).height * this.item.scale.y + this.layout.spacingY);
                var deltaX = this.layout.paddingLeft
                    + (this.item.getComponent(UITransform).anchorX * this.item.getComponent(UITransform).width * this.item.scale.x) - this.content.getComponent(UITransform).anchorX * this.content.getComponent(UITransform).width
                    + (index % cols) * (this.item.getComponent(UITransform).width * this.item.scale.x + this.layout.spacingX)
                return v3(deltaX, -deltaY, 0);
            default:
                return v3();
        }
    }
    /**
     * 移动到相应序号item的位置
     * @param index 序号
     */
    public scrollToTargetIndex(index: number) {
        let pos = this.getItemPosByIdx(index);
        let offsetY = /* this.view.height / 2 */ - this.item.getComponent(UITransform).height / 2;

        let t = Math.abs(this.content.position.y + pos.y) / 3000;
        tween(this.content)
            .to(t, { position: v3(0, -pos.y + offsetY) }, {
                onUpdate: (target, ratio) => {
                    this.onScrolling();
                }
            })
            .start()
    }
}

class ScrollViewProData {
    n: Node;
    /**用户数据 */
    userData: any;
    /**节点在数据中的下标，根据index调整节点在content中的位置 */
    index: number;

    constructor(index: number, userData: any) {
        this.index = index;
        this.userData = userData;
    }
}