[1, ["17AzVa3bpOs7Bp4mHwly+I@6c48a"], ["_textureSource"], ["cc.SpriteFrame"], 0, [{"name": "huodou-idle_07", "rect": {"x": 144, "y": 139, "width": 629, "height": 437}, "offset": {"x": 3, "y": -69.5}, "originalSize": {"width": 911, "height": 576}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-314.5, -218.5, 0, 314.5, -218.5, 0, -314.5, 218.5, 0, 314.5, 218.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [144, 437, 773, 437, 144, 0, 773, 0], "nuv": [0.15806805708013172, 0, 0.8485181119648738, 0, 0.15806805708013172, 0.7586805555555556, 0.8485181119648738, 0.7586805555555556], "minPos": {"x": -314.5, "y": -218.5, "z": 0}, "maxPos": {"x": 314.5, "y": 218.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]]