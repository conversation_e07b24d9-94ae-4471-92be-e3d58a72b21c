import CCCSctiptSingleton from '../../tools/Exp/CCCSctiptSingleton'
import { GameSocket } from './GameSocket'
 

export interface backType<T> {
	isSucc: true
	info?: T
	code: number
}

export interface backErrorType {
	isSucc: false
	code?: number
}

/**
 * 超时时间配置
 */
const outTime = 5
export default class MessageCtrl extends CCCSctiptSingleton {
	protected readonly succFalse: backErrorType = { isSucc: false }

	protected protocolSwitch: 0 | 1 = 1
	private _cmd: number

	protected set cmd(v: number) {
		if (v < 0) console.error('未注册消息')
		this._cmd = v
	}

	private _subCmd: number
	protected set subCmd(v: number) {
		if (v < 0) console.error('未注册消息')
		this._subCmd = v
	}

	/**
	 *  监听socket消息回调
	 * @protected
	 */
	protected addListen() {
		console.log('addListen', this._cmd, this._subCmd)
		GameSocket.onListenMessage((this._cmd << 16) + this._subCmd, this)
	}

	send(msg?: any) {
		GameSocket.mainSocket?.send({
			/** 协议开关，用于一些协议级别的开关控制，比如 安全加密校验等。 : 0 不校验 */
			protocolSwitch: this.protocolSwitch,
			/** 消息ID */
			cmd: this._cmd,
			/** 二级消息ID */
			subCmd: this._subCmd,
			/** 数据 */
			data: msg == null ? new Uint8Array(0) : msg
		})
	}

	resolve: (value: any | PromiseLike<any>) => void
	reject: (reason?: any) => void

	sendSync(msg?: any): Promise<backType<any> | backErrorType>
	sendSync<T>(msg?: any): Promise<backType<T> | backErrorType>
	sendSync<T>(msg?: any) {
		return new Promise((resolve, reject) => {
			try {
				this.scheduleOnce(this.clearResolve, outTime)
				this.resolve = resolve
				this.reject = reject
				this.send(msg)
			} catch (e) {
				reject(e)
			}
		}) as Promise<backType<T> | backErrorType>
	}

	/**
	 * 回调处理
	 * @param code 0为成功
	 * @param data 成功传入数据,失败传入错误信息
	 */
	receive(code: number, data: any) {
		if (this.reject) this.unschedule(this.clearResolve)

		if (!this.resolve) {
			console.warn(`接收异常;cmd: ${this.cmd};subCmd: ${this.subCmd};code: ${code}`, data)
			return
		}

		if (code === 0) {
			this.resolve({ isSucc: true, info: data, code } as backType<any>)
		} else {
			console.error(code, data)
			this.resolve({ ...this.succFalse, code })
		}
		this.resolve = null
		this.reject = null
	}

	private clearResolve() {
		if (this.reject) {
			console.error('发送超时')
			this.reject(this.succFalse)
		}
		this.resolve = null
		this.reject = null
	}
}
