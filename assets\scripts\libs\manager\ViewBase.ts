import { BlockInputEvents, Button, CCClass, Color, Component, Enum, Graphics, Layers, Node, Skeleton, Sprite, UITransform, Vec2, Vec3, _decorator, js, log, macro, sp, tween, v3, view } from "cc";

import { EventManager, EventManagerCallFunc } from "./EventManager";
import { Sound } from "../utils/Sound";
import Tool from "../utils/Tool";
import { xcore } from "../xcore";


const E_ANIM_STYLE = Enum({
    None: 0,
    Scale: 1,
    DrawerBottom: 2,
    Ui: 3
})

const { ccclass, property } = _decorator;

@ccclass('ViewBase')
export class ViewBase extends Component {

    @property
    public maskOpacity: number = 200

    @property({ type: E_ANIM_STYLE, tooltip: '弹窗打开动画类型' })
    private animStyle: number = E_ANIM_STYLE.Scale

    @property(Node)
    public ndRoot: Node = null;

    @property(Sprite)
    public sprFrame: Sprite = null;

    @property(Button)
    public btnClose: Button = null;



    public _viewName: string = null
    private _ndBg: Node = null;

    private _eventList: Map<string, EventManagerCallFunc> = new Map();


    // LIFE-CYCLE CALLBACKS:


    protected onLoadCompleted() { }
    protected onOpenCompleted() { }
    protected onDisableCompleted() { }
    protected onDestroyCompleted() { }
    protected onShowCompleted() { }
    onBeforeClose() { }

    onLoad() {
        if (this.animStyle != E_ANIM_STYLE.Ui) {
            this.createBgGraphics();
        }
        this.onLoadCompleted();

    }
    public setData(data) {

    }
    public closeSelf() {
        this.onBeforeClose();
        this.scheduleOnce(() => {
            this.node.destroy();
        })

    }
    private addBaseListener() {
        this.btnClose && this.btnClose.node.on('click', () => {
            this.closeSelf();
        }, this)
    }

    /** 销毁节点前释放动态加载进来的图片或spine素材 */
    public releaseAssets() {
        let releaseAsset = Tool.getComponentPropertys(this);
        for (let i = 0; i < releaseAsset.length; i++) {
            let asset = releaseAsset[i];
            if (asset instanceof Sprite && asset.spriteFrame && asset.spriteFrame[`_resAsset`]) {
                xcore.res.releaseAsset(asset.spriteFrame[`_resAsset`])
            } else if (asset instanceof sp.Skeleton && asset.skeletonData) {
                xcore.res.releaseSpineAsset(asset.node);
            }
        }
    }
    addEventListener(event: string, cb: Function) {
        let EventManagerCallFunc = function (eventName: string, eventData: any) {
            cb(eventData);
        }
        this._eventList.set(event, EventManagerCallFunc);
        EventManager.getInstance().addEventListener(event, EventManagerCallFunc);
    }

    createBgGraphics() {
        let winSize = view.getVisibleSize();
        this._ndBg = new Node();
        this._ndBg.name = 'ndMaskBg';
        this._ndBg.addComponent(UITransform);
        this._ndBg.addComponent(Graphics);
        this._ndBg.addComponent(BlockInputEvents);
        this._ndBg.getComponent(UITransform).width = winSize.width;
        this._ndBg.getComponent(UITransform).height = winSize.height;
        this._ndBg.position = new Vec3();
        let ctx = this._ndBg.getComponent(Graphics);
        ctx.rect(- winSize.width / 2, - winSize.height / 2, winSize.width, winSize.height);
        ctx.fillColor = new Color(0, 0, 0, this.maskOpacity);
        ctx.fill();
        this._ndBg.layer = Layers.Enum.UI_2D;
        this._ndBg.parent = this.node;
        Tool.setChildrenNodeSortByPriority(this._ndBg, -1);
    }
    removeBg() {
        this._ndBg.destroy()
    }
    addButtonEvent(buttonNode: Node | Button, cb: Function, target: Component) {
        let btn;

        if (buttonNode instanceof Node) {
            btn = buttonNode.getComponent(Button) || buttonNode.addComponent(Button);
        } else {
            btn = buttonNode;
        }
        btn.node.off('click');
        btn.node.on('click', cb, target);
    }

    onShow() {
        log("BaseUI onShow  " + this.node.name);
        this.onShowCompleted();
        this.popAnim(() => {
            this.addBaseListener();
            this.onOpenCompleted();
        })
    }

    async onEnable() {

    }

    onDisable() {

        this.onDisableCompleted();
    }

    protected onDestroy() {
        //移除addEventListener监听事件
        this._eventList.forEach((func, event) => {
            EventManager.getInstance().removeEventListener(event, func);
        })
        this.onDestroyCompleted();
    }

    //展示动画
    public popAnim(cb?: Function) {
        if (this.ndRoot) {
            switch (this.animStyle) {
                case E_ANIM_STYLE.Scale:
                    this.ndRoot.setScale(v3(0, 0, 0));
                    tween(this.ndRoot)
                        .to(0.15, { scale: v3(1, 1, 1) }, { easing: "backOut" })
                        .call(() => { cb && cb() })
                        .start()
                    break;
                case E_ANIM_STYLE.DrawerBottom:

                    let startPosY = -view.getVisibleSize().height / 2 - (this.sprFrame.node.getComponent(UITransform).height / 2);
                    let endPosY = -view.getVisibleSize().height / 2 //+ (this.sprFrame.node.getComponent(UITransform).height / 2);
                    this.ndRoot.setPosition(v3(0, startPosY));
                    tween(this.ndRoot)
                        .to(0.15, { position: v3(0, endPosY) })
                        .call(() => { cb && cb() })
                        .start()

                    break;

                default:
                    cb && cb()
                    break;
            }
        }
    }

    //隐藏动画
    public async hideAnim() {

        return new Promise((resolve, reject) => {
            if (this.ndRoot) {
                switch (this.animStyle) {
                    case E_ANIM_STYLE.Scale:
                        tween(this.ndRoot)
                            .to(0.15, { scale: v3(0, 0, 0) }, { easing: "sineOut" })
                            .call(() => {

                                resolve('')
                            })
                            .start()
                        break
                    case E_ANIM_STYLE.DrawerBottom:
                        let endPosY = -view.getVisibleSize().height / 2 - (this.sprFrame.node.getComponent(UITransform).height);
                        tween(this.ndRoot)
                            .to(0.15, { position: v3(0, endPosY) })
                            .call(() => {

                                resolve('')
                            })
                            .start()


                        break;
                    case E_ANIM_STYLE.Ui:
                        resolve('')
                        break
                    default:

                        resolve('')
                        break;
                }

            } else {

                resolve('')
            }
        })

    }
    // update (dt) {}
}
