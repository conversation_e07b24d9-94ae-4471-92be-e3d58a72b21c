[1, ["52fHu7D8hGm5vLaoALoXCl", "023hs6Z9hMN7n00LscdOzW@f9941", "e8YGnkLQpCN5bnM/tgb9x6@f9941", "46IdxunHlHYYawWmS60uVs@f9941", "be4B41TMJAoKWHhzxwerRq@f9941", "05iPojnNhACoKYBElUdqb7"], ["node", "_spriteFrame", "_font", "_parent", "_target", "itemPrefab", "root", "svContent", "btnClose", "sprFrame", "ndRoot", "data"], [["cc.Node", ["_name", "_layer", "_active", "_components", "_prefab", "_children", "_parent", "_lpos"], 0, 9, 4, 2, 1, 5], ["cc.Node", ["_name", "_layer", "_parent", "_components", "_prefab", "_lpos", "_children"], 1, 1, 12, 4, 5, 2], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.Sprite", ["_sizeMode", "node", "__prefab", "_spriteFrame"], 2, 1, 4, 6], ["cc.Label", ["_string", "_actualFontSize", "_isSystemFontUsed", "_fontSize", "_enableOutline", "node", "__prefab", "_outlineColor", "_font", "_color"], -2, 1, 4, 5, 6, 5], ["cc.<PERSON><PERSON>", ["_transition", "_zoomScale", "node", "__prefab", "_normalColor", "_target"], 1, 1, 4, 5, 1], ["cc.Layout", ["_resizeMode", "_layoutType", "_spacingX", "_spacingY", "_constraintNum", "node", "__prefab"], -2, 1, 4], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.Mask", ["node", "__prefab"], 3, 1, 4], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node", "__prefab"], 0, 1, 4], ["cc.Graphics", ["node", "__prefab", "_fillColor"], 3, 1, 4, 5], ["d2bd8Ybyq5JP6Lhg2nKR4ii", ["horizontal", "type", "node", "__prefab", "_content"], 1, 1, 4, 1], ["da346jZRGpOOZaVh1wPlKmi", ["node", "__prefab", "ndRoot", "sprFrame", "btnClose", "svContent"], 3, 1, 4, 1, 1, 1, 1]], [[8, 0, 2], [9, 0, 1, 2, 3, 4, 5, 5], [2, 0, 1, 2, 1], [0, 0, 1, 6, 3, 4, 7, 3], [2, 0, 1, 2, 3, 1], [3, 0, 1, 2, 3, 2], [0, 0, 1, 5, 3, 4, 3], [0, 0, 1, 6, 3, 4, 3], [2, 0, 1, 1], [3, 1, 2, 3, 1], [5, 0, 1, 2, 3, 4, 5, 3], [7, 0, 2], [0, 0, 1, 6, 5, 3, 4, 3], [0, 0, 1, 6, 5, 3, 4, 7, 3], [0, 0, 1, 5, 3, 4, 7, 3], [0, 0, 2, 1, 5, 3, 4, 7, 4], [0, 0, 2, 1, 6, 3, 4, 4], [1, 0, 1, 2, 3, 4, 3], [1, 0, 1, 2, 3, 4, 5, 3], [1, 0, 1, 2, 6, 3, 4, 5, 3], [3, 1, 2, 1], [4, 0, 1, 2, 4, 5, 6, 9, 7, 8, 5], [4, 0, 1, 3, 2, 5, 6, 7, 8, 5], [4, 0, 1, 3, 2, 5, 6, 9, 7, 8, 5], [5, 0, 2, 3, 4, 5, 2], [6, 0, 1, 2, 3, 4, 5, 6, 6], [6, 0, 1, 2, 5, 6, 4], [10, 0, 1, 1], [11, 0, 1, 2, 3, 4, 4], [12, 0, 1, 2, 1], [13, 0, 1, 2, 3, 4, 3], [14, 0, 1, 2, 3, 4, 5, 1]], [[11, "ViewExchange"], [6, "ViewExchange", 33554432, [-8], [[8, -2, [0, "f6KqSViWFCFZ3CDEMZZjPE"]], [31, -7, [0, "baVSUZKi5DL6Y/vdA3knyE"], -6, -5, -4, -3]], [1, "bfdHlCZ6lDvbkMLNxgq+W3", null, null, null, -1, 0]], [12, "ndRoot", 33554432, 1, [-10, -11, -12, -13, -14], [[8, -9, [0, "e9UYs58o1K/JGIt6gOo+sN"]]], [1, "27K078E4tPg76z722mZ7/c", null, null, null, 1, 0]], [14, "btnScoreRank", 33554432, [-19, -20], [[2, -15, [0, "28hKsa3NFOZoxNY9goZNkU"], [5, 170, 66]], [5, 0, -16, [0, "11ZAWgerxHuZqQKxXvkSbz"], 4], [10, 3, 1.05, -18, [0, "1cIoA57oBIo4Fo5Roz2tvf"], [4, 4292269782], -17]], [1, "40JjNx5jVLt6iWpqYL7ryM", null, null, null, 1, 0], [1, 0, 4.242999999999938, 0]], [15, "btnLevRank", false, 33554432, [-25, -26], [[2, -21, [0, "54C65vT4tNcoxCeG2frY5d"], [5, 170, 66]], [5, 0, -22, [0, "22rZB+YqhPvboRo0WErJNa"], 7], [10, 3, 1.05, -24, [0, "bbYhvH8sNMAoma6Hgw2d/h"], [4, 4292269782], -23]], [1, "ae8iY05MVOKIdAs5NB6cv2", null, null, null, 1, 0], [1, 90, 4.242999999999938, 0]], [6, "view", 33554432, [-31], [[4, -27, [0, "f4RE/Xn29A96QfFD/A5Ww5"], [5, 750, 1140], [0, 0.5, 1]], [27, -28, [0, "3dXs3m9SZMVYcBjcf4Bg8o"]], [28, 45, 240, 250, -29, [0, "a0QlTb/O9FbKiOmiCyU68N"]], [29, -30, [0, "29XaOHvKRDmpSu4slCc7vk"], [4, 16777215]]], [1, "69HXj95WRJi7/eJCvyHOjA", null, null, null, 1, 0]], [18, "btnClose", 33554432, 2, [[[2, -32, [0, "759lSxf+5Mkp9YoQJQMsZJ"], [5, 112, 113]], [9, -33, [0, "a7T9YfL5pP3J8qpaDpj5Ut"], 1], -34], 4, 4, 1], [1, "dc71Keu8dFd4IlUaBb8kC8", null, null, null, 1, 0], [1, 389.288, 681.7049999999999, 0]], [13, "ndBtns", 33554432, 2, [3, 4], [[2, -35, [0, "30Qix/kS9CZL+VzsY9+/NA"], [5, 170, 100]], [26, 1, 1, 10, -36, [0, "22BCpByQhFrocsyCo7GhbB"]]], [1, "32CqT0wJND6a86WqVlb62C", null, null, null, 1, 0], [1, 0, 537.3340000000001, 0]], [19, "svV", 33554432, 2, [5], [[[4, -37, [0, "421mFMZntIBp9OrIoRyASd"], [5, 750, 1140], [0, 0.5, 1]], -38], 4, 1], [1, "7fMTCEK6NPRZf9mULOYaoN", null, null, null, 1, 0], [1, 0, 500.89200000000005, 0]], [7, "content", 33554432, 5, [[4, -39, [0, "66BuwKKYRMErWjEuqel0wh"], [5, 780, 0], [0, 0.5, 1]], [25, 1, 3, 13, 20, -1.8, -40, [0, "7cyEcAKzZNUb/ZzxGqgKb4"]]], [1, "63dO+vA61DVqSFzfGlKab0", null, null, null, 1, 0]], [17, "sprFrame", 33554432, 2, [[[2, -41, [0, "edOR2FgEVCLoJjkunso4zl"], [5, 820, 1380]], -42], 4, 1], [1, "86jzrfwCFJBowZhp8rUbcW", null, null, null, 1, 0]], [3, "lbTitle", 33554432, 2, [[2, -43, [0, "dezN1uaG9ATaN7w8WwyXmY"], [5, 84, 54.4]], [21, "兑换", 40, false, true, -44, [0, "923mPVUDRCyYTBf+A3qp/n"], [4, 4289062911], [4, 4280050606], 0]], [1, "bedWapffZLnIHnCUq0JQry", null, null, null, 1, 0], [1, 0, 649.5920000000001, 0]], [7, "ndOn", 33554432, 3, [[2, -45, [0, "34Ev4HrqtJoJSIh5TELWrD"], [5, 170, 66]], [5, 0, -46, [0, "4fIij7iORF45UPxTPj5t0i"], 2]], [1, "d9MDMzKulN44t0gxC4fQSU", null, null, null, 1, 0]], [3, "Label", 33554432, 3, [[2, -47, [0, "ccjDlEabdKxpWypvxc0qPv"], [5, 120, 50.4]], [22, "皮肤碎片", 30, 30, false, -48, [0, "aeSdet7dBLDasqPiAwwniI"], [4, 4280507528], 3]], [1, "68gMxIQR1Ga6j6q6PoO74q", null, null, null, 1, 0], [1, 0, 2.129000000000133, 0]], [16, "ndOn", false, 33554432, 4, [[2, -49, [0, "31PDNLwrVOjZ4mMUUQieEf"], [5, 170, 66]], [9, -50, [0, "85HY96jFpI94Oa5fWmQcur"], 5]], [1, "89xQzKDN1KK5kY66TTw5E2", null, null, null, 1, 0]], [3, "Label", 33554432, 4, [[2, -51, [0, "a7we/bXY1F75oOV/naHT35"], [5, 0, 50.4]], [23, "", 30, 30, false, -52, [0, "bd/QBQdAFGfL813PenJf0M"], [4, 4279772212], [4, 4280507528], 6]], [1, "33U+PAj/tA57tU5S6UI4kA", null, null, null, 1, 0], [1, 0, 2.129000000000133, 0]], [20, 10, [0, "b6jlHdsv9JhaM6/5K/zs+F"]], [24, 3, 6, [0, "658ct8FnJP5YcSmDwxlaYK"], [4, 4292269782], 6], [30, false, 1, 8, [0, "24gdveZVZJfJ3U6HEM5H6L"], 9]], 0, [0, 6, 1, 0, 0, 1, 0, 7, 18, 0, 8, 17, 0, 9, 16, 0, 10, 2, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, -1, 10, 0, -2, 11, 0, -3, 6, 0, -4, 8, 0, -5, 7, 0, 0, 3, 0, 0, 3, 0, 4, 3, 0, 0, 3, 0, -1, 12, 0, -2, 13, 0, 0, 4, 0, 0, 4, 0, 4, 4, 0, 0, 4, 0, -1, 14, 0, -2, 15, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, -1, 9, 0, 0, 6, 0, 0, 6, 0, -3, 17, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, -2, 18, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, -2, 16, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, 11, 1, 3, 3, 7, 4, 3, 7, 5, 3, 8, 52], [0, 0, 0, 0, 0, 0, 0, 0, 16, 18], [2, 1, 1, 2, 1, 1, 2, 1, 1, 5], [0, 3, 1, 0, 2, 1, 0, 2, 4, 5]]