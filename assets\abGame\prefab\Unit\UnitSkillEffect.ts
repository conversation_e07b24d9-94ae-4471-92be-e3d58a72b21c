import { _decorator, Animation, AnimationClip, bezier, Color, Component, easing, log, Node, sp, Sprite, SpriteAtlas, SpriteFrame, Tween, tween, TweenSystem, UITransform, v3, Vec3, view } from 'cc';
import { EffectMgr, IEffectData } from '../../scripts/EffectMgr';
import Tool from '../../../scripts/libs/utils/Tool';
import { xcore } from '../../../scripts/libs/xcore';
import { C_Bundle, E_EVENT, E_RoleType, E_SkillType, IAnimaData } from '../../../scripts/ConstGlobal';
import { Vec3Util } from '../../../scripts/libs/utils/Vec3Util';
import { ConfigHelper } from 'db://assets/scripts/config/ConfigHelper';



const { ccclass, property } = _decorator;

@ccclass('UnitSkillEffect')
export class UnitSkillEffect extends Component {



    @property(Animation)
    private anim: Animation


    private speed: number = 700;

    private skillType: E_SkillType
    private atkJsonId: string
    /**1直线 2 曲线 */
    private atkType: number
    private animId01: string
    private animId02: string
    private deadAnim: string
    private sound: string



    private animConfig: any;


    private offPos: Vec3 = v3(-5000, -5000)

    private _goastNodes: Node[] = [];

    private _tw: Tween<Node>


    start() {

    }

    setData(skillType?: E_SkillType, atkJsonId?: string, animId01?: string, animId02?: string, sound?: string) {
        this.skillType = skillType;
        this.atkJsonId = atkJsonId;
        this.animId01 = animId01;
        this.animId02 = animId02;
        this.sound = sound;
        if (this.animId01) {
            this.animConfig = ConfigHelper.getInstance().getAnimConfigByJsonId(this.animId01)
        } else if (this.animId02) {
            this.animConfig = ConfigHelper.getInstance().getAnimConfigByJsonId(this.animId02)
            //log("受击config", this.animConfig)
        }
        if (this.atkJsonId) {

            let heroConfig = ConfigHelper.getInstance().getHeroConfigByJsonId(this.atkJsonId);
            let config = ConfigHelper.getInstance().getEffectConfigByJsonId(heroConfig.attacKeffect);
            this.animConfig = ConfigHelper.getInstance().getAnimConfigByJsonId(config.Animation);
            this.atkType = heroConfig.trajectory;

        }
        //妖族技能
        if (this.skillType == E_SkillType.MoveSpeed || this.skillType == E_SkillType.SkillSpeed || this.skillType == E_SkillType.Dizziness) {
            //log('skilleffect:', this.skillType, this.animId01, this.animConfig)
        }

    }


    async play(data: IEffectData) {

        //播放死亡动画
        if (data.deadanim) {
            this.deadAnim = data.deadanim;
            this.node.scale = v3(1, 1, 1);
            this.node.active = true;
            this.node.setPosition(v3(data.pos0.x, data.pos0.y));
            await this.animUpdate();
            this.scheduleOnce(() => {
                this.killSkill(data.cb);
            }, data.speed || 1)
            return
        }
        // log("start offset", data.offsetpos)
        let fromPos = data.pos0 ? v3(data.pos0.x + (data.offsetpos ? data.offsetpos.x : 0), data.pos0.y + (data.offsetpos ? data.offsetpos.y : 0), 1) : null;
        let toPos = data.pos1 ? v3(data.pos1.x, data.pos1.y, 1) : null
        this.node.scale = v3(fromPos ? 1 : (data.roledir || 1), 1, 1);
        this.node.active = true;
        let duration = this.animConfig.duration;
        if (data.speed && data.speed < duration) {
            duration = data.speed;
        }

        await this.animUpdate();
        /* "技能目标
            1 本人 
            2 目标 
            3 路径 
            0 没有  
         */
        if (this.animConfig.target == 1) {
            this.node.setPosition(fromPos);
            this.scheduleOnce(() => {
                this.killSkill(data.cb);
            }, duration)

        }
        else if (this.animConfig.target == 2) {
            this.node.setPosition(toPos);

            this.node.setSiblingIndex(this.node.position.y);
            //effct效果的受击动画
            if (this.animId01 && this.animId02) {
                //log("受击动画" + this.animId02);
                xcore.event.raiseEvent(E_EVENT.HurtEffect, {
                    pos1: toPos, animId02: this.animId02, roledir: data.roledir,
                    formRoleType: data.formRoleType
                })
            }

            this.scheduleOnce(() => {
                if (this.sound) {
                    xcore.sound.remotePlayOneShot(this.sound)
                }
                this.killSkill(data.cb);

            }, duration)
        }
        else if (this.animConfig.target == 3) {

            //陨石
            if (this.animConfig.jsonId == '340309') {
                let rx = Tool.randomNumber(-260, 260);
                fromPos = v3(fromPos.x + rx, view.getVisibleSize().height / 2 + 100)

            }
            this.node.setPosition(fromPos);
            let t = Math.min(Vec3Util.distance(fromPos, toPos) / this.speed, duration);
            TweenSystem.instance.ActionManager.removeAllActionsFromTarget(this.node);

            //普攻 弓箭攻击路径
            if (this.atkJsonId && data.formRoleType != E_RoleType.TowerPoint && this.atkType == 2) {
                toPos.y += 100;
                EffectMgr.getInstance().arrowByTarget(fromPos, toPos, (groups, dir, isLine) => {
                    this.doAction(groups, dir, isLine, duration, data.cb, true, () => {
                        //effct效果的受击动画
                        if (this.animId01 && this.animId02) {
                            //log("受击动画" + this.animId02)
                            xcore.event.raiseEvent(E_EVENT.HurtEffect, {
                                pos1: toPos, animId02: this.animId02, roledir: data.roledir,
                                formRoleType: data.formRoleType
                            })
                        }
                    });

                })
            }

            //飞沙走石弹道
            else if (this.animConfig.jsonId == '340312') {
                this.node.angle = 0;
                if (this.sound) {
                    xcore.sound.remotePlayOneShot(this.sound)
                }
                EffectMgr.getInstance().arrowByTarget(fromPos, toPos, (groups, dir, isLine) => {
                    this.doAction(groups, dir, isLine, duration, () => {
                        //effct效果的受击动画
                        if (this.animId01 && this.animId02) {
                            //log("受击动画" + this.animId02)
                            xcore.event.raiseEvent(E_EVENT.HurtEffect, {
                                pos1: toPos, animId02: this.animId02, roledir: data.roledir,
                                formRoleType: data.formRoleType
                            })
                        }

                        data.cb && data.cb()
                    }, false);

                })

            }
            //直线路径
            else {
                if (this.animConfig.jsonId != '340309' && (data.formRoleType == E_RoleType.Hero || data.formRoleType == E_RoleType.TowerPoint)) {
                    toPos.y += 100;
                }

                this.node.angle = Tool.getAngleByV3(fromPos, toPos);
                // if (this.skillType == E_SkillType.LineAttack) {
                //     this.scheduleOnce(() => {
                //         this.followAnim(t, toPos);
                //     })
                // }
                tween(this.node)
                    .to(t, { position: toPos, scale: v3(1.2, 1.2) }, { easing: easing.cubicIn })

                    .call(() => {
                        //effct效果的受击动画
                        if (this.animId01 && this.animId02) {
                            //log("受击动画" + this.animId02)
                            xcore.event.raiseEvent(E_EVENT.HurtEffect, {
                                pos1: toPos, animId02: this.animId02, roledir: data.roledir,
                                formRoleType: data.formRoleType
                            })
                        }
                        if (this.sound) {
                            xcore.sound.remotePlayOneShot(this.sound)
                        }
                        this.killSkill(data.cb);
                    }).start();
            }
        }
        else {
            this.node.setPosition(fromPos);
            this.killSkill(data.cb);
        }


    }

    killSkill(cb?) {
        if (!this.offPos) {
            this.offPos = v3(-5000, -5000);
        }
        this.node.setPosition(this.offPos);
        cb && cb();
        if (this.deadAnim) {
            EffectMgr.getInstance().killSkillEffectByType(this.deadAnim, this)
        } else if (this.skillType) {
            EffectMgr.getInstance().killSkillEffectByType(this.skillType, this);
        } else if (this.atkJsonId) {
            EffectMgr.getInstance().killSkillEffectByType(this.atkJsonId, this);
        } else if (this.animId01) {
            EffectMgr.getInstance().killSkillEffectByType(this.animId01, this);
        } else if (this.animId02) {
            EffectMgr.getInstance().killSkillEffectByType(this.animId02, this);
        }

        // if (this._goastNodes.length > 0) {
        //     for (let i = 0; i < this._goastNodes.length; i++) {
        //         this._goastNodes[i].destroy();
        //         this._goastNodes[i] = null;
        //     }
        //     this._goastNodes = []
        // }
    }

    async animUpdate() {
        let animConfig
        let animaData;
        //死亡动画
        if (this.deadAnim) {
            animaData = {
                'sample': 9,
                'duration': 1,
                'speed': 1,
                'wrapMode': 2,
                'path': 'common',
                'name': '42'
            }
        }
        else if (this.animId01) {
            animConfig = ConfigHelper.getInstance().getAnimConfigByJsonId(this.animId01)


            animaData = {
                'sample': animConfig.sample,
                'duration': animConfig.duration,
                'speed': 1,
                'wrapMode': animConfig.wrapMode,
                'path': animConfig.path,
                'name': animConfig.name,
                'offsetX': animConfig.XAxisOffset || 0,
                'offsetY': animConfig.YAxisOffset || 0
            }
        }
        else if (this.animId02) {
            animConfig = ConfigHelper.getInstance().getAnimConfigByJsonId(this.animId02)


            animaData = {
                'sample': animConfig.sample,
                'duration': animConfig.duration,
                'speed': 1,
                'wrapMode': animConfig.wrapMode,
                'path': animConfig.path,
                'name': animConfig.name,
                'offsetX': animConfig.XAxisOffset || 0,
                'offsetY': animConfig.YAxisOffset || 0
            }
        }
        //普攻
        else if (this.atkJsonId) {

            let heroConfig = ConfigHelper.getInstance().getHeroConfigByJsonId(this.atkJsonId);
            let config = ConfigHelper.getInstance().getEffectConfigByJsonId(heroConfig.attacKeffect);
            animConfig = ConfigHelper.getInstance().getAnimConfigByJsonId(config.Animation);

            //log('普攻', this.effectName , animConfig);
            animaData = {
                'sample': animConfig.sample,
                'duration': animConfig.duration,
                'speed': 1,
                'wrapMode': animConfig.wrapMode,
                'path': animConfig.path,
                'name': animConfig.name,
                'offsetX': animConfig.XAxisOffset || 0,
                'offsetY': animConfig.YAxisOffset || 0
            }
        }

        let atlas = xcore.res.getAtlas('skilleffect');

        for (let i = 0; i < animaData.sample; i++) {
            let name = `${animaData.name}_${i < 10 ? `0${i}` : i}`;
            let sf = atlas.getSpriteFrame(name);
            if (!sf) {
                // sf = await xcore.res.remoteLoadSprite(xcore.gameData.cospath + `image/${animaData.path}/${name}.png`)// await xcore.res.bundleLoadSprite(C_Bundle.abGame, `./res/anim/${animaData.path}/${name}`)
                sf = await xcore.res.bundleLoadSprite('resources', `./res/image/${animaData.path}/${name}`)// await xcore.res.bundleLoadSprite(C_Bundle.abGame, `./res/anim/${animaData.path}/${name}`)
                xcore.res.addAtlasSprite('skilleffect', name, sf);
            }
        }

        /* if (this.atkJsonId) {
            let name = `${animaData.name}_00`;
            this.anim.getComponent(Sprite).spriteFrame = atlas.spriteFrames[name]
        } else {
            await Tool.createAnim(this.anim, animaData, atlas);
        } */ await Tool.createAnim(this.anim, animaData, atlas);
        this.anim.node.setPosition(v3(animaData.offsetX, animaData.offsetY))

        if (animConfig) {
            //缩放配置
            if (animConfig.XCenterPoint == undefined || animConfig.XCenterPoint == null) {
                animConfig.XCenterPoint = 0.5;
            } else if (animConfig.XCenterPoint == "") {
                animConfig.XCenterPoint = 0;
            }
            if (animConfig.YCenterPoint == undefined || animConfig.YCenterPoint == null) {
                animConfig.YCenterPoint = 0.5;
            } else if (animConfig.YCenterPoint == "") {
                animConfig.YCenterPoint = 0;
            }
            //配置动画中心位置
            let animUITransform = this.anim.node.getComponent(UITransform);
            animUITransform?.setAnchorPoint(animConfig.XCenterPoint, animConfig.YCenterPoint);
        }




    }

    followAnim(time: number, tagetPos: Vec3) {
        return
        let spriteFrame = this.anim.getComponent(Sprite)?.spriteFrame;
        if (!spriteFrame) {
            log("!spriteFrame")
        }
        for (let i = 0; i < 4; i++) {
            let newNode = new Node();
            newNode.parent = this.node.parent;
            newNode.scale = this.node.scale;
            newNode.setSiblingIndex(0);
            let sprite = newNode.addComponent(Sprite);
            sprite.spriteFrame = spriteFrame;
            sprite.color = new Color(255, 255, 255, 255 / (i + 1.5));
            this._goastNodes.push(newNode);
        }
        for (let i = 0; i < this._goastNodes.length; i++) {
            let node = this._goastNodes[i];
            if (node.position != tagetPos) {
                node.setPosition(this.node.position);
                node.angle = this.node.angle;
                let _tween = tween(node);
                _tween.stop();
                let rad = (i + 0.3) / this._goastNodes.length;
                node.scale = v3(1 - (rad * 0.3), 1 - (rad * 0.3), 1 - (rad * 0.3))
                _tween.to(rad * time + time, { position: tagetPos }).start();
            }
        }
    }

    doAction(groups, dir, isLine: boolean = false, t: number, cb: Function = null, isAngle: boolean = true, actEnd?: Function) {
        this._tw = tween(this.node);

        this.node.angle = 0;
        this.node.active = true;

        if (isLine) {
            this.node.angle = Tool.getAngleByV3(groups[0], groups[1]);

            this._tw
                .to(t, {
                    position: groups[1],
                })
                .call(() => {
                    this._tw.stop();
                    this.killSkill(cb);
                    //Game.Instance._gameManager.putArrow(this.node);
                    actEnd && actEnd()

                }).start();


            return

        }


        for (let i = 0; i < groups.length; i++) {
            if (i == 0) {
                this.node.angle = isAngle ? 90 - (dir * 30) : 0;
                continue
            }

            let theta = Tool.getAngleByV3(groups[i - 1], groups[i]);
            if (theta < 0 && dir == -1) {
                theta += 360;
            }

            this._tw.to(t / groups.length,
                { position: v3(groups[i].x, groups[i].y), angle: isAngle ? theta : 0 },
            )
        }

        this._tw.call(() => {
            this._tw.stop();
            this.killSkill(cb);
            actEnd && actEnd()


        })
        this._tw.start();
    }

    update(deltaTime: number) {

    }
}


