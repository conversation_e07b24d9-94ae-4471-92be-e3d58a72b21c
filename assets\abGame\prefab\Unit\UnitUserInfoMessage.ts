import { _decorator, Component, easing, Label, log, Node, size, Sprite, tween, Tween, UITransform, Vec3, view, } from 'cc';
import { GiftMessageMgr } from '../../scripts/GiftMessageMgr';
import { xcore } from 'db://assets/scripts/libs/xcore';
import { ConfigHelper } from 'db://assets/scripts/config/ConfigHelper';
const { ccclass, property } = _decorator;

@ccclass('UnitUserInfoMessage')
export class UnitUserInfoMessage extends Component {
    @property(Label)
    private lbUserName: Label = null;

    @property(Label)
    private lbSkinName: Label = null;

    @property(Sprite)
    private sprAvatar: Sprite = null;

    @property(Label)
    private lbAtk: Label = null;

    @property(Label)
    private lbAtkSpeed: Label = null;

    /**暴击伤害百分比 */
    @property(Label)
    private lbBaoHurtRat: Label = null;

    /**暴击率 */
    @property(Label)
    private lbBaoAtkRat: Label = null;

    @property(Label)
    private lbSkillCd: Label = null;

    @property(Label)
    private lbHp: Label = null;


    private tw: Tween
    private fromPos: Vec3 = null
    private toPos: Vec3 = null


    setData(data: any) {
        log("E_GiftMessageType.UserInfo", data)
        if (!this.fromPos) {
            this.fromPos = new Vec3(view.getVisibleSize().width / 2 + this.node.getComponent(UITransform).width / 2, -view.getVisibleSize().height / 2 + this.node.getComponent(UITransform).height + 240, 0)
        }
        if (!this.toPos) {
            this.toPos = new Vec3(view.getVisibleSize().width / 2 - this.node.getComponent(UITransform).width / 2 - 80, -view.getVisibleSize().height / 2 + this.node.getComponent(UITransform).height + 240, 0)

        }

        this.node.setPosition(this.fromPos)
        this.tw = tween(this.node)
            .to(0.4, { position: this.toPos })
            .delay(4.5)
            .call(() => {
                this.kill();
            })
        this.tw.start();
        let configs = ConfigHelper.getInstance().getBeadConfigs();
        for (let i = 0; i < configs.length; i++) {
            let config = configs[i];
            let type = config.jsonId;

            let dimonddata;

            switch (type) {
                /**金珠 攻击加成*/
                case '260001':

                    dimonddata = {
                        lev: data.dimonEffects.attackLev,
                        num: data.dimonEffects.attackDmNum,
                        prop: data.dimonEffects.attack,
                        txt: `攻击+${data.dimonEffects.attack}`
                    }
                    this.lbAtk.string = `${data.minAtkNum + dimonddata.prop}~${data.maxAtkNum + dimonddata.prop}`;
                    break;
                /**木珠 法宝cd*/
                case '260002':
                    dimonddata = {
                        lev: data.dimonEffects.skillcdLev,
                        num: data.dimonEffects.skillcdDmNum,
                        prop: data.dimonEffects.skillcd,
                        txt: `法宝速度+${Math.floor(data.dimonEffects.skillcd * 100)}%`
                    }
                    this.lbSkillCd.string = `+${Math.floor(data.dimonEffects.skillcd * 100)}%`;
                    break;
                /**水珠 攻速加成*/
                case '260003':
                    dimonddata = {
                        lev: data.dimonEffects.atkspeedLev,
                        num: data.dimonEffects.atkspeedDmNum,
                        prop: data.dimonEffects.atkspeed,
                        txt: `攻速+${Math.floor(data.dimonEffects.atkspeed * 100)}%`
                    }
                    this.lbAtkSpeed.string = `${data.speed.toFixed(2)}S`;
                    break;
                /**火珠 暴击伤害*/
                case '260004':
                    dimonddata = {
                        lev: data.dimonEffects.atkmLev,
                        num: data.dimonEffects.atkmDmNum,
                        prop: data.dimonEffects.atkm,
                        txt: `暴击伤害+${Math.floor(data.dimonEffects.atkm * 100)}%`
                    }
                    this.lbBaoHurtRat.string = Math.floor((data.dimonEffects.baseAtkM + dimonddata.prop) * 100) + '%';
                    break;
                /**土珠 城门血量*/
                case '260005':
                    dimonddata = {
                        lev: data.dimonEffects.hpLev,
                        num: data.dimonEffects.hpDmNum,
                        prop: data.dimonEffects.hp,
                        txt: `城门血量+${data.dimonEffects.hp}`
                    }
                    this.lbHp.string = `+${dimonddata.prop}`;
                    break;

                default:
                    break;
            }

        }

        this.lbUserName.string = data.name || '匿名用户';
        xcore.res.remoteLoadSprite(data.avatar, this.sprAvatar, size(90, 90));
        this.lbBaoAtkRat.string = Math.floor(data.dimonEffects.atkr * 100) + '%';
        let skinConfig = ConfigHelper.getInstance().getSkinConfigByJsonId(data.skinId);
        if (skinConfig) {
            this.lbSkinName.string = skinConfig.name;
        }
    }
    kill() {
        this.node.removeFromParent();
        GiftMessageMgr.getInstance().killUserInfoMessage(this);
    }
}


