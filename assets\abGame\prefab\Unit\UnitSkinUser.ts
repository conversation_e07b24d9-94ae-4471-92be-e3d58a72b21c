import { _decorator, Button, Component, Label, Node, Prefab, size, Sprite } from 'cc';
import ListItem from 'db://assets/scripts/components/ListItem';
import { Role } from '../../scripts/Role';
import Tool from 'db://assets/scripts/libs/utils/Tool';
import { ConfigHelper } from 'db://assets/scripts/config/ConfigHelper';
import { xcore } from 'db://assets/scripts/libs/xcore';
import { C_View, E_EVENT } from 'db://assets/scripts/ConstGlobal';
import { UnitSkinShow } from './UnitSkinShow';
const { ccclass, property } = _decorator;

@ccclass('UnitSkinUser')
export class UnitSkinUser extends ListItem {

    @property(Node)
    private ndUserData: Node = null;

    @property(Prefab)
    private pfbUnitSkinShow: Prefab = null;

    @property(Node)
    private ndList: Node = null;

    @property(Sprite)
    private sprAvatar: Sprite = null;

    @property(Label)
    private lbName: Label = null;

    @property(Label)
    private lbScore: Label = null;

    @property(Button)
    private btnOption: Button = null;

    @property(Button)
    private btnOption2: Button = null;

    @property(Button)
    private btnOption3: Button = null;

    @property(Label)
    private lbOptionTxt: Label = null;

    private _isOpen: boolean = false;
    private _role: Role = null;

    onLoad(): void {
        this.btnOption.node.on('click', () => {
            /* this._isOpen = !this._isOpen;
            this.refreshSkins(); */
            xcore.ui.addView(C_View.ViewUserInfo, { role: this._role, index: 0 });
        }, this)
        this.btnOption2.node.on('click', () => {
            /* this._isOpen = !this._isOpen;
            this.refreshSkins(); */
            xcore.ui.addView(C_View.ViewUserInfo, { role: this._role, index: 1 });
        }, this)
        this.btnOption3.node.on('click', () => {
            /* this._isOpen = !this._isOpen;
            this.refreshSkins(); */
            xcore.ui.addView(C_View.ViewUserInfo, { role: this._role, index: 2 });
        }, this)
    }

    setData(role: Role, index: number): void {
        this._role = role;
        this.lbName.string = role.data.nickName || '匿名用户';
        this.lbScore.string = (role.data.weekScore || 0).toString();
        this._isOpen = false;
        this.refreshSkins();
        xcore.res.remoteLoadSprite(role.data.iconUrl, this.sprAvatar, size(90, 90));
    }

    refreshSkins() {
        this.ndList.active = this._isOpen;
        // this.lbOptionTxt.string = this._isOpen ? '收起' : '展开'
        if (this._isOpen) {
            let configs = ConfigHelper.getInstance().getSkinConfgs();
            configs.sort((a, b) => b.sort - a.sort)
            Tool.asyncModifyChildren(this, this.ndList, this.pfbUnitSkinShow, configs.length, (nd, i) => {
                nd.getComponent(UnitSkinShow).setData(configs[i], i, this._role, (skinId) => {
                    this.refreshSkins();
                });
            })
        }
    }
}


