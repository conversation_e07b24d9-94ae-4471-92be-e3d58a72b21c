const { app, BrowserWindow, ipcMain, clipboard, nativeImage, screen } = require('electron');



const resolution = require("./node-win-screen-resolution-master/lib/index.cjs");


// 子进程模块，用来执行nircmd指令
const { exec } = require('child_process');
const fs = require("fs");
const path = require('path');
const log4js = require('log4js');

// 获取分辨率的脚本
// 获取屏幕当前分辨率
var current = resolution.current();
// 声明窗口的变量
let win;

// 默认分辨率，就是打开游戏时的分辨率
let defaultSizeX = "1080";
let defaultSizeY = "1920";


// 当app完成初始化时，创建一个窗口
app.on('ready', createWindow);

// 在最后一个窗口被关闭时退出应用
app.on('window-all-closed', () => {
    app.quit();
});
app.commandLine.appendSwitch('no-sandbox');
// 创建一个窗口
function createWindow() {
    initLogConfig()
    let size = screen.getPrimaryDisplay().workAreaSize;
    let height = size.height;
    let width = size.width;
    if (height >= 1920) {
        height = 1920;
        width = 1080;
    } else {
        width = parseInt(height * 1080 / 1920);
    }

    // 创建一个宽800，高600的窗口
    win = new BrowserWindow({
        // 窗口宽高
        width: width,
        height: height,

        // width 和 height 将设置为 web 页面的尺寸(译注: 不包含边框), 这意味着窗口的实际尺寸将包括窗口边框的大小，稍微会大一点。 默认值为 false.
        useContentSize: false,


        resizable: true,
        // 标题   默认窗口标题 默认为"Electron"。 如果由loadURL()加载的HTML文件中含有标签<title>，此属性将被忽略。
        title: "守护南天门",

        // 网页功能设置，必须写这些，如果不写Cocos就不能调用封装好的事件
        webPreferences: {
            nodeIntegration: true,
            enableRemoteModule: true,
            contextIsolation: false,

        },

    })
    // 设置应用图标
    const iconPath = path.join(__dirname, 'assets/icon.jpg'); // 替换为你的图标文件路径
    win.setIcon(nativeImage.createFromPath(iconPath));

    // 窗口中显示的网页
    // __dirname,表示main.js所在的目录路径
    // let path = fs.readFileSync(__dirname + "/path.txt", "utf8");
    win.loadURL(__dirname + "/web/index.html");

    // 全屏
    win.setFullScreen(false);

    // 最大化窗口
    // win.maximize();

    // 删除窗口的菜单栏
    win.removeMenu();
    // win.openDevTools();


    // 任务栏图标是否闪烁
    win.flashFrame(true);


    // 监听窗体关闭事件，当窗体已经关闭时，将win赋值为null，垃圾回收。
    win.on('closed', () => {
        win = null;
    })

    // 窗口关闭时，设置成默认的分辨率
    win.on("close", () => {
        setDefaultResolution();
    })



    //以下事件供渲染进程调用

    // 全屏
    ipcMain.on('e_fullScreen', (event) => {
        win.setFullScreen(true);
    })
    ipcMain.on('e_sendquery', (event) => {
        event.returnValue = process.argv
    })
    // 窗口化
    ipcMain.on('e_window', (event) => {
        win.setFullScreen(false);
    })

    // 打开开发者工具
    ipcMain.on('e_openDevTools', (event) => {
        win.openDevTools();
    })

    // 关闭开发者工具
    ipcMain.on('e_closeDevTools', (event) => {
        win.closeDevTools();
    })

    // 当前是否全屏
    ipcMain.on('e_isFullScreen', (event) => {
        event.returnValue = win.isFullScreen();
    });
    // Writing settings
    ipcMain.on('e_writeFile', (event, name, data) => {
        const userDataPath = './userData/';
        const settingsFilePath = path.join(userDataPath, name);
        fs.writeFileSync(settingsFilePath, JSON.stringify(data));

        //console.log("e_writeFile", name, data);
    })
    // Reading settings
    ipcMain.on('e_readFile', (event, name) => {
        const userDataPath = './userData/';
        const settingsFilePath = path.join(userDataPath, name);
        if (!fs.existsSync(settingsFilePath)) {
            fs.writeFileSync(settingsFilePath, JSON.stringify({}));
        }
        let read = fs.readFileSync(settingsFilePath, 'utf-8');
        event.returnValue = read;

    })
    // 设置分辨率，修改的电脑的分辨率
    ipcMain.on('e_setResolution', (event, width, height) => {
        // 在当前目录下打开cmd并且输入nircmd.exe setdisplay 要设置的宽度 要设置的高度 32
        // 三个参数分别是   要设置的宽度   要设置的高度   位色-->一般写32就可以

        let a = path.resolve(app.getAppPath(), '..');
        let b = path.resolve(a, "..");
        exec(b + `\\nircmd.exe setdisplay ${width} ${height} 32`);
    });

    // 设置窗口大小
    ipcMain.on('e_setSize', (event, width, height) => {
        // 缩小窗口
        win.unmaximize()
        // 设置大小
        win.setSize(Number(width), Number(height));
        // 窗口移动到中心
        win.center();
    });

    // 移动窗口到中心
    ipcMain.on('e_center', (event) => {
        win.center();
    });

    // 自定义nircmd命令
    ipcMain.on('e_nircmdUD', (event, order) => {
        // 将传入的命令执行，前面已经加了nircmd.exe
        let a = path.resolve(app.getAppPath(), '..');
        let b = path.resolve(a, "..");
        exec(b + `\\nircmd.exe ${order}`, (err) => {
            if (err) {
                console.log(err);
            } else {
                console.log("meicuo");
            }
        });
    });

    // 获取一些信息
    ipcMain.on('e_getMassage', (event) => {
        event.returnValue = `CPU操作位数：${process.arch}\n 当前应用程序位于：${app.getAppPath()}\n 正在使用的Chromium版本：${process.versions.chrome}\n 正在使用的V8版本：${process.versions.v8}\n 正在使用的node版本：${process.versions.node}\n 剪贴板内容${clipboard.readText()}`
    });


    // 获取分辨率的脚本
    // 获取屏幕当前分辨率
    var current = resolution.current();
    // 获取屏幕支持的所有分辨率
    var available = resolution.list();


    // console.log(`Current screen resolution is ${current.width}x${current.height}`);
    // console.log(`Available resolutions (${available.length}):`);
    // console.log(available);

    // 获取屏幕支持的所有分辨率，是一个object的数组，width为宽height为高
    ipcMain.on('e_getAllResolutions', (event) => {
        // 用for循环组成一个string的数组
        let str = [];
        for (let i = 0; i < available.length; i++) {
            str.push(available[i].width + "×" + available[i].height);
        }

        // console.log("all_result", str);

        // 返回一个string的数组
        event.returnValue = str;
    });

    // 获取当前的屏幕分辨率，是一个object变量，width为宽height为高
    ipcMain.on('e_getCurrentResolution', (event) => {
        let str = current.width + "×" + current.height;
        // console.log("cur_result:", current.width + "×" + current.height);

        // 返回一个string
        event.returnValue = str;
    });



    // 设置默认分辨率为当前分辨率
    // defaultSizeX = current.width;
    // defaultSizeY = current.height;



    // 退出游戏
    ipcMain.on('e_quit', (event) => {
        win.close();
    });

    ipcMain.on('e_logErr', (event, type, err) => {
        loger(type, err);
    })
    /* 
    测试 屏幕支持的分辨率获取和当前分辨率获取
    const current = resolution.current();
    console.log(`Current screen resolution is ${current.width}x${current.height}`);

    const available = resolution.list();
    console.log(`Available resolutions (${available.length}):`);
    console.log(available);
     */


    /* 
    // 必须在打包好的HTML文件里面加上这三句，不然Cocos的代码找不到electron
    <script>
        window.electron = require('electron');
    </script>
     */

}

// 设置为默认分辨率，就是刚进入游戏时的分辨率
function setDefaultResolution() {
    let a = path.resolve(app.getAppPath(), '..');
    let b = path.resolve(a, "..");

    exec(b + `\\nircmd.exe setdisplay ${defaultSizeX} ${defaultSizeY} 32`);
    // console.log("close window");
}
function initLogConfig() {
  
    // 配置日志记录器
    log4js.configure({
        appenders: {
            out: { type: "stdout" }, // 输出到控制台
            app: {
                type: "file", // 使用 file appender 进行文件输出
                filename: "logs/app.log", // 日志文件路径（不需要扩展名，自动添加）
                pattern: "yyyy-MM-dd", // 日志文件轮换的模式，按天轮换
                alwaysIncludePattern: true, // 始终在文件名中添加日期后缀
                daysToKeep: 30, // 保留最近 90 天的日志文件
                compress: true, // 启用日志文件压缩（.gz 格式）
            },
        },
        categories: {
            default: { appenders: ["out", "app"], level: "info" }, // 设置日志级别和输出方式
        },
    });
}

function loger(type, err) {
  
    // 获取记录器
    const logger = log4js.getLogger();
    if (type == 'trace') {
        // 记录不同级别的日志
        logger.trace(err);   // 最细粒度的日志
    } else if (type == 'debug') {
        logger.debug(err);   // 调试信息
    } else if (type == 'info') {
        logger.info(err);    // 常规信息
    } else if (type == 'warn') {
        logger.warn(err);     // 警告信息
    } else if (type == 'error') {
        logger.error(err);  // 错误信息
    }


}