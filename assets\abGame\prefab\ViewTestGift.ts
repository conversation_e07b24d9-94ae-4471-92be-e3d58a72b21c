import { _decorator, Button, Component, instantiate, Label, log, Node, Toggle } from 'cc';
import { ViewBase } from '../../scripts/libs/manager/ViewBase';
import { C_GiftKey, E_EVENT, E_RoleType } from '../../scripts/ConstGlobal';
import { IUser } from '../scripts/FightMgr';
import { xcore } from '../../scripts/libs/xcore';
import { GiftMgr } from '../scripts/GiftMgr';
import Tool from '../../scripts/libs/utils/Tool';
import { ConfigHelper } from '../../scripts/config/ConfigHelper';
const { ccclass, property } = _decorator;

/**
 * 礼物指令本地测试
 * 默认20个可选用户
 * togSelectGroup 可选择是否自动加入阵营
 * 
 */
@ccclass('ViewTestGift')
export class ViewTestGift extends ViewBase {

    @property(Node)
    private ndBtn: Node = null;

    @property(Node)
    private ndBtnUser: Node = null;

    @property(Button)
    private btnRandomTest: Button = null;

    @property(Label)
    private lbDesc: Label = null;

    @property(Toggle)
    private togSelectGroup: Toggle = null;

    @property(Node)
    private ndContent: Node = null;

    @property(Node)
    private ndContentUser: Node = null;

    private _userId: string
    private _group: string

    private _heroUser: Map<string, IUser> = new Map();


    private _giftMgr: GiftMgr


    setData(data) {
        this._giftMgr = data.giftMgr;
        this._heroUser = data.heroUser;

        /*  this._giftMgr.switchIfRandomSelectGroup(this.togSelectGroup.isChecked);
         this.togSelectGroup.node.on('toggle', (toggle: Toggle) => {
             this._giftMgr.switchIfRandomSelectGroup(toggle.isChecked);
         }, this); */
    }

    start() {
        this.addEventListener(E_EVENT.BaseInfo, this.onRefreshData.bind(this))
        let users = [
            'a01', 'a02', 'a03', 'a04', 'a05',
            'a06', 'a07', 'a08', 'a09', 'a10',
            'a11', 'a12', 'a13', 'a14', 'a15',
            'a16', 'a17', 'a18', 'a19', 'a20'
        ]

        for (let i = 0; i < users.length; i++) {
            let userId = users[i];

            let btn = instantiate(this.ndBtnUser);
            btn.parent = this.ndContentUser;
            btn.getChildByName('Label').getComponent(Label).string = userId;
            btn.on('click', this.onSelectUser.bind(this, userId, btn), this);
        }

        for (const key in C_GiftKey) {
            if (Object.prototype.hasOwnProperty.call(C_GiftKey, key)) {
                let v = C_GiftKey[key];
                if (v == C_GiftKey.Skin1 || v == C_GiftKey.Skin2 || v == C_GiftKey.Skin3 || v == C_GiftKey.Skin4 || v == C_GiftKey.Skin5 || v == C_GiftKey.Skin6 || v == C_GiftKey.Skin7
                    || v == C_GiftKey.Skin8 || v == C_GiftKey.Skin9 || v == C_GiftKey.Skin10 || v == C_GiftKey.Skin11 || v == C_GiftKey.Skin12 || v == C_GiftKey.Skin13 || v == C_GiftKey.Skin14
                    || v == C_GiftKey.Skin15 || v == C_GiftKey.Skin16 || v == C_GiftKey.Skin17 || v == C_GiftKey.Skin18
                    || v == C_GiftKey.Skin19 || v == C_GiftKey.Skin20 || v == C_GiftKey.Skin21 || v == C_GiftKey.Skin22 || v == C_GiftKey.Skin23
                    || v == C_GiftKey.Skin24 || v == C_GiftKey.Skin25

                ) {
                    let config = ConfigHelper.getInstance().getSkinConfigByJsonId(v);

                    let btn = instantiate(this.ndBtn);
                    btn.parent = this.ndContent;
                    btn.getChildByName('Label').getComponent(Label).string = config.barrage;
                    btn.on('click', this.onGiftKey.bind(this, config.barrage), this);
                } else if (v != C_GiftKey.JoinHero && v != C_GiftKey.JoinMonster
                    /* && v != C_GiftKey.Skin8 && v != C_GiftKey.Skin9 && v != C_GiftKey.Skin10 && v != C_GiftKey.Skin11 && v != C_GiftKey.Skin12 && v != C_GiftKey.Skin13 && v != C_GiftKey.Skin14
                    && v != C_GiftKey.Skin15 && v != C_GiftKey.Skin16 && v != C_GiftKey.Skin17 && v != C_GiftKey.Skin18 */
                ) {
                    let btn = instantiate(this.ndBtn);
                    btn.parent = this.ndContent;
                    btn.getChildByName('Label').getComponent(Label).string = v;
                    btn.on('click', this.onGiftKey.bind(this, v), this);
                }

            }
        }

        this.btnRandomTest.node.on('click', () => {
            let rankNum = Tool.randomNumber(4, 10);
            for (let i = 0; i < rankNum; i++) {
                let userId = Tool.guid();
                let keys = [ /**加入仙族 */
                  /*   JoinHero:  */'1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1',
                    /**加入妖族 */
                    /* JoinMonster:  */'2', '2', '2', '2', '2', '2', '2', '2', '2', '2', '2', '2', '2', '2',
                    /**点赞 */
                  /*   Like:  */'300001', '300001', '300001', '300001', '300001', '300001', , '300001', '300001', '300001', '300001', '300001',
                    /**666 */
                 /*    SixSix:  */'300002', '300002', '300002', '300002', '300002', '300002', '300002', '300002',

                    /**仙女棒 */
                  /*   Gift01:  */'300003', '300003', '300003', '300003', '300003',
                    /**药丸 */
                 /*    Gift02:  */'300004', '300004', '300004',
                    /**魔法镜 */
                 /*    Gift03:  */'300005', '300005',
                    /**甜甜圈 */
                   /*  Gift04:  */'300006', '300006', '300006', '300006', '300006', '300006', '300006', '300006', '300006', '300006', '300006', '300006', '300006', '300006', '300006', '300006', '300006', '300006', '300006', '300006', '300006', '300006', '300006', '300006', '300006',
                    /**恶魔炸弹 */
                 /*    Gift05: */ '300007',
                    /**神秘空投 */
                  /*   Gift06: */ '300008',
                ]

                keys = Tool.randomSortArray(keys);

                for (let i = 0; i < 3; i++) {
                    let key = keys[i];
                    this._giftMgr.addGifts([
                        {
                            userId: userId,
                            key: key,
                            num: Tool.randomNumber(1, 20)
                        }
                    ])
                }
            }

            this.closeSelf()
        }, this)



    }
    onRefreshData(data: any) {
        this._heroUser = data.heroUserInfo;


        if (this._userId) {

            this.onSelectUser(this._userId);
        }
    }
    onGiftKey(key: string) {
        log(key);

        if (!this._userId) {
            xcore.ui.showToast('请先选择用户');
            return
        }
        let txt
        if (key == C_GiftKey.ExchangeBossFight) {
            let txts = [];
            let configs1 = ConfigHelper.getInstance().getDungeonConfigs();

            for (let i = 0; i < configs1.length; i++) {
                let config = configs1[i];
                txts.push(config.name);
            }
            /* for (let i = 0; i < configs2.length; i++) {
                let config = configs2[i];
                if (config.exchangeTypeId == '470001') {
                    txts.push(config.name);
                }
            }
 */
            txt = txts[Math.floor(Math.random() * txts.length)]

        } else if (key == C_GiftKey.ExchangeWing) {
            let txts = [];
            let configs2 = ConfigHelper.getInstance().getExchangeConfigs();
            for (let i = 0; i < configs2.length; i++) {
                let config = configs2[i];
                if (config.exchangeTypeId == '470001') {
                    txts.push(config.name);
                }
            }
            txt = txts[Math.floor(Math.random() * txts.length)]
        }
        this._giftMgr.addGifts([

            {
                userId: this._userId,
                key: key,
                num: 1,
                content: txt
            }
        ])
    }

    onSelectUser(userId: string, targetNd?: Node) {
        if (targetNd) {
            this.ndContentUser.children.forEach(e => {
                e.getChildByName('tagOn').active = false;
            });
            targetNd.getChildByName('tagOn').active = true;
        }

        this._userId = userId;
        let userData = this.findUser(userId);
        this._group = userData ? userData.type : null;

        this.lbDesc.string = `userId:${this._userId}\ngourp:${this._group}\n`;

        if (userData) {
            userData.giftKey.forEach(e => {
                this.lbDesc.string += (e.key + 'x' + e.num + ' ')
            })
        }

    }
    /**查找用户 */
    findUser(userId: string, type?: E_RoleType): IUser {
        let user = this._heroUser.get(userId);


        return user
    }
}


