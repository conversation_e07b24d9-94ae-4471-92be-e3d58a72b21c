import { _decorator, Component, Node } from 'cc';
import { ViewBase } from '../../scripts/libs/manager/ViewBase';
import ScrollViewProCom from '../../scripts/libs/utils/ScrollViewProCom';
import { ConfigHelper } from '../../scripts/config/ConfigHelper';
import { UnitSelectGameType } from './Unit/UnitSelectGameType';
const { ccclass, property } = _decorator;

@ccclass('ViewSelectGameType')
export class ViewSelectGameType extends ViewBase {
    @property(ScrollViewProCom)
    private svContent: ScrollViewProCom = null;

    protected onLoadCompleted(): void {
        let configs = ConfigHelper.getInstance().getDungeonConfigs();
        this.svContent.setView(configs, (n: Node, data: any, index: number) => {
            n.getComponent(UnitSelectGameType).setData(data);
        })
    }
}


