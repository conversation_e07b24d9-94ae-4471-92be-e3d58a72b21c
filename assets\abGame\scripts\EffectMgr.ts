import { Vec3, Node, instantiate, log, Vec2, v3, v2 } from "cc";
import { C_Bundle, E_AtkStyle, E_BuffType, E_EVENT, E_RoleType, E_SkillType, E_SkinType } from "../../scripts/ConstGlobal";
import { Singleton } from "../../scripts/libs/utils/Singleton";
import { xcore } from "../../scripts/libs/xcore";
import { Collection } from "../../scripts/libs/utils/Collection";

import { UnitSkillEffect } from "../prefab/Unit/UnitSkillEffect";
import { UnitHurtTips } from "../prefab/Unit/UnitHurtTips";
import Tool from "../../scripts/libs/utils/Tool";
import { UnitLighting } from "../prefab/Unit/UnitLighting";
import { Role } from "./Role";
import { UnitNewsEffect } from "../prefab/Unit/UnitNewsEffect";
import { UnitLighting2 } from "../prefab/Unit/UnitLighting2";
import { UnitSkillTips } from "../prefab/Unit/UnitSkillTips";
export interface IHurtTips {
    posx: number
    posy: number
    num: number
    cb: Function
}
export interface IEffectData {

    pos0?: Vec2,
    pos1: Vec2,
    skill?: E_SkillType,
    cb?: Function,

    //**仙族 普通攻击与皮肤绑定 */
    normalatk?: E_SkinType
    atkStyle: E_AtkStyle
    offsetpos?: Vec3
    offsetpos2?: Vec3
    //释放动画
    animId01?: string
    //受击动画
    animId02?: string

    lightningTargets?: Role[]

    deadanim?: string
    delay?: number
    speed?: number
    roledir?: number
    range?: number

    formRoleType?: E_RoleType

    checkFunc?: Function
    sound?: string
}

/**
 * 技能攻击效果管理器
 * 主要处理攻击特效的显示和对象池管理
 */
export class EffectMgr extends Singleton {

    //技能特效父节点
    targetParentNd: Node = null;
    targetParentNd2: Node = null;
    targetParentNd3: Node = null;
    targetParentNd4: Node = null;


    effectPools: Collection<string, UnitSkillEffect[]> = new Collection();

    /**雷电对象池 */
    lightingPool: UnitLighting2[] = [];

    //受击伤害数值效果对象池
    hurtTipsPool: UnitHurtTips[] = [];

    newsPool: UnitNewsEffect[] = [];

    skillTipsPool: UnitSkillTips[] = [];

    private _G: number = -2000;
    private _V: number = 1000;

    private linearVelocity: Vec2

    setEffectParentNd(heroEffect: Node, monsterEffect: Node, hurtEffect: Node, hurtTips: Node) {
        this.targetParentNd = heroEffect;
        this.targetParentNd2 = monsterEffect;
        this.targetParentNd3 = hurtEffect;
        this.targetParentNd4 = hurtTips;
        xcore.event.addEventListener(E_EVENT.SkillEffect, this.ShowSkillEffect, this);
        xcore.event.addEventListener(E_EVENT.HeroAtkEffect, this.ShowHeroAtkEffect, this);
        xcore.event.addEventListener(E_EVENT.MonsterAtkEffect, this.ShowMonsterAtkEffect, this);
        xcore.event.addEventListener(E_EVENT.HurtEffect, this.ShowMonsterHurtEffect, this);
        xcore.event.addEventListener(E_EVENT.HurtTips, this.ShowHurtTips, this);
        xcore.event.addEventListener(E_EVENT.MonsterDead, this.ShowMonsterDead, this)
        xcore.event.addEventListener(E_EVENT.NewsEffect, this.ShowNewsEffect, this)
        xcore.event.addEventListener(E_EVENT.SkillTips, this.ShowSkillTips, this)
    }
    destroy() {

        xcore.event.removeEventListener(E_EVENT.SkillEffect, this.ShowSkillEffect, this);
        xcore.event.removeEventListener(E_EVENT.HeroAtkEffect, this.ShowHeroAtkEffect, this);
        xcore.event.removeEventListener(E_EVENT.MonsterAtkEffect, this.ShowMonsterAtkEffect, this);
        xcore.event.removeEventListener(E_EVENT.HurtEffect, this.ShowMonsterHurtEffect, this);
        xcore.event.removeEventListener(E_EVENT.HurtTips, this.ShowHurtTips, this);
        xcore.event.removeEventListener(E_EVENT.MonsterDead, this.ShowMonsterDead, this);
        xcore.event.removeEventListener(E_EVENT.NewsEffect, this.ShowNewsEffect, this);
        xcore.event.removeEventListener(E_EVENT.SkillTips, this.ShowSkillTips, this);
        this.effectPools.forEach(pool => {
            pool.length = 0;
        })
        this.effectPools.clear();
        this.newsPool.length = 0;
        this.hurtTipsPool.length = 0;
        this.lightingPool.length = 0;
        this.skillTipsPool.length = 0;
    }

    refreshZindex() {
        let children = this.targetParentNd.children.concat();
        children.sort((a: any, b: any): number => {
            return b.position.y - a.position.y
        });
        let maxIndex = children.length;
        for (const node of children) {
            node.setSiblingIndex(maxIndex);
        }
    }


    /**
     *  技能特效显示
     * @param event 
     * @param data 特效数据 包括skill类型和目标坐标
     */
    async ShowSkillEffect(event: string, data: IEffectData) {
        if (!this.effectPools) {
            this.effectPools = new Collection();
        }
        if (data.delay) {
            await Tool.asyncDelay(data.delay)
        }

        //雷电效果
        if (data.lightningTargets && data.lightningTargets.length > 0) {

            await this.ShowLightings(data.lightningTargets, data.animId01);
            data.cb && data.cb(1);
            if (data.sound) {
                xcore.sound.remotePlayOneShot(data.sound)
            }
            return
        }

        if (!data.animId01 && !data.animId02 && !data.normalatk) {
            data.cb && data.cb()
            return
        }
        //技能效果
        if (data.skill) {

            let effect = await this.getSkillEffectByType(data.skill, null, data.animId01, data.animId02, null, data.sound);

            effect.node.parent = this.targetParentNd;
            effect.play(data);
        }

        this.refreshZindex();
    }
    /**仙族普攻特效 */
    async ShowHeroAtkEffect(event: string, data: IEffectData) {
        if (data.delay) {
            await Tool.asyncDelay(data.delay)
        }
        let effect = await this.getSkillEffectByType(null, data.normalatk, data.animId01, data.animId02);
        effect.node.parent = this.targetParentNd;
        effect.play(data);
    }
    /**怪物攻击特效  */
    async ShowMonsterAtkEffect(event: string, data: IEffectData) {
        if (data.delay) {
            await Tool.asyncDelay(data.delay)
        }
        if (data.checkFunc) {
            let alive = data.checkFunc();
            if (!alive) {
                return
            }

            data.checkFunc = null;
        }
        let effect = await this.getSkillEffectByType(null, null, data.animId01, data.animId02);
        if (data.atkStyle == E_AtkStyle.Near) {
            effect.node.parent = this.targetParentNd3;
        } else {
            effect.node.parent = this.targetParentNd2;
        }

        effect.play(data);
    }
    //受击动画
    async ShowMonsterHurtEffect(event: string, data: IEffectData) {
        if (data.delay) {
            await Tool.asyncDelay(data.delay)
        }

        let effect = await this.getSkillEffectByType(null, null, null, data.animId02);


        if (data.formRoleType == E_RoleType.Monster) {
            effect.node.parent = this.targetParentNd3;
        } else {
            effect.node.parent = this.targetParentNd2;
        }

        effect.play(data);
    }
    /**伤害数值提示 */
    async ShowHurtTips(event: string, data: IHurtTips) {

        if (!this.hurtTipsPool) {
            this.hurtTipsPool = [];
        }
        let tips = null;
        if (this.hurtTipsPool.length <= 0) {
            let pfb = await xcore.res.bundleLoadPrefab(C_Bundle.abGame, './prefab/Unit/UnitHurtTips');
            tips = instantiate(pfb).getComponent(UnitHurtTips);
        } else {
            tips = this.hurtTipsPool.shift();
        }
        tips.node.parent = this.targetParentNd4;
        tips.setData(data, (tips) => {
            this.killHurtTips(tips);
        });
    }
    async ShowMonsterDead(event: string, data: IEffectData) {

        let poolName = 'deadeffect'

        let effect = await this.getSkillEffectByType(null, null, null, null, poolName);
        effect.node.parent = this.targetParentNd;
        data.deadanim = poolName
        effect.play(data);
    }

    //获取特效节点
    async getSkillEffectByType(skillType?: E_SkillType, atkJsonId?: E_SkinType, animId01?: string, animId02?: string, poolName?: string, sound?: string): Promise<UnitSkillEffect> {
        if (!this.effectPools) {
            this.effectPools = new Collection();
        }
        let effect = null as UnitSkillEffect;

        let effectName;
        if (poolName) {
            effectName = poolName;
        } else {
            effectName = skillType ? skillType : (atkJsonId || (animId01 ? animId01 : animId02))
        }

        let effects = this.effectPools.get(effectName)
        if (!effects) {
            this.effectPools.set(effectName, []);
            effects = this.effectPools.get(effectName);
        }

        if (!effects || effects.length <= 0) {
            let pfb = await xcore.res.bundleLoadPrefab(C_Bundle.abGame, './prefab/Unit/UnitSkillEffect');
            effect = instantiate(pfb).getComponent(UnitSkillEffect);
        } else {
            effect = effects.shift();
        }

        effect.getComponent(UnitSkillEffect).setData(skillType, atkJsonId, animId01, animId02, sound);
        return effect;
    }

    async ShowSkillTips(event: string, data: any) {
        if (!this.skillTipsPool) {
            this.skillTipsPool = [];
        }
        let tips = null as UnitSkillTips;
        if (this.skillTipsPool.length <= 0) {
            let pfb = await xcore.res.bundleLoadPrefab(C_Bundle.abGame, './prefab/Unit/UnitSkillTips');
            tips = instantiate(pfb).getComponent(UnitSkillTips);
        } else {
            tips = this.skillTipsPool.shift();
        }
        tips.node.parent = this.targetParentNd4;
        tips.setData(data);
        return tips;
    }

    async ShowNewsEffect(event: string, data: any) {
        if (!this.newsPool) {
            this.newsPool = [];
        }
        let news = null as UnitNewsEffect;
        if (this.newsPool.length <= 0) {
            let pfb = await xcore.res.bundleLoadPrefab(C_Bundle.abGame, './prefab/Unit/UnitNewsEffect');
            news = instantiate(pfb).getComponent(UnitNewsEffect);
        } else {
            news = this.newsPool.shift();
        }
        news.node.parent = this.targetParentNd4;
        news.setData(data);
        return news;
    }

    /**雷电特效 */
    async ShowLightings(targets: any[], animJsonId: string) {

        for (let i = 0; i < targets.length; i++) {

            let endPos = v3(targets[i].x, targets[i].y);


            await this.createLighting2(animJsonId, endPos)

            /* if (data.skill) {
                let effectData = Tool.cloneObj(data);
               

                effectxcore.gameDatapos1.set(targets[i].x, targets[i].y);
                let effect = await this.getSkillEffectByType(effectxcore.gameDataskill, null, effectxcore.gameDataanimId01, effectxcore.gameDataanimId02);
                effect.node.parent = this.targetParentNd;
                effect.play(effectData);

            } */
        }
    }
    async createLighting(startPos: Vec3, endPos: Vec3) {
        /* if (!this.lightingPool) {
            this.lightingPool = [];
        }
        let light = null as UnitLighting2;
        if (!this.lightingPool || this.lightingPool.length <= 0) {
            let pfb = await xcore.res.bundleLoadPrefab(C_Bundle.abGame, './prefab/Unit/UnitLighting');
            light = instantiate(pfb).getComponent(UnitLighting);
        } else {
            light = this.lightingPool.shift();
        }


        light.setData(startPos, endPos);
        light.node.parent = this.targetParentNd; */
    }

    async createLighting2(animJsonId: string, endPos: Vec3) {
        if (!this.lightingPool) {
            this.lightingPool = [];
        }
        let light = null as UnitLighting2;
        if (!this.lightingPool || this.lightingPool.length <= 0) {
            let pfb = await xcore.res.bundleLoadPrefab(C_Bundle.abGame, './prefab/Unit/UnitLighting2');
            light = instantiate(pfb).getComponent(UnitLighting2);
        } else {
            light = this.lightingPool.shift();
        }

        light.setData(animJsonId, endPos);
        light.node.parent = this.targetParentNd;
    }

    //回收effect对象
    killSkillEffectByType(id: string, effect: UnitSkillEffect) {
        //effect.node.active = false;
        effect?.node?.removeFromParent();
        let effects = this.effectPools.get(id);
        if (effects) {
            effects.push(effect)
        }

    }
    //回收伤害数值提示对象
    killHurtTips(tips: UnitHurtTips) {
        tips.node.removeFromParent();
        this.hurtTipsPool.push(tips);
    }

    killNewsEffect(news: UnitNewsEffect) {
        news.node.removeFromParent();
        this.newsPool.push(news);
    }

    killLighting(light: UnitLighting2) {
        light.node.removeFromParent();
        this.lightingPool.push(light);
    }
    killSkillTips(tips: UnitSkillTips) {
        tips.node.removeFromParent();
        this.skillTipsPool.push(tips);
    }
    /**
       *  获取弓箭轨迹坐标数组
       * @param startPos 开始坐标
       * @param endPos   目标坐标
       * @param posNum   轨迹坐标点数量
       * @returns        轨迹坐标数组 
       */
    arrowByTarget(startPos: Vec3, endPos: Vec3, cb: Function) {
        if (!startPos || !endPos) return
        let groups = [];

        const s = endPos.x - startPos.x;
        const h = endPos.y - startPos.y;
        this.linearVelocity = v2(0, 0)
        let dir = startPos.x > endPos.x ? -1 : 1;
        if (s * s + h * h < 4000) {
            groups = [{ x: startPos.x, y: startPos.y }, { x: endPos.x, y: endPos.y }]

            cb(groups, dir, true)


        } else {
            const a = this._G * s / (2 * this._V * this._V);
            const b = 1;
            const c = a - h / s;
            const delta = b * b - 4 * a * c;
            if (delta >= 0) {
                // 一元二次方程求根公式
                const t = (-b - Math.sqrt(delta)) / (2 * a); // 高抛 tan 值
                const alpha = Math.atan(t) + (s < 0 ? Math.PI : 0);
                const v_x = Math.cos(alpha) * this._V;
                const v_y = Math.sin(alpha) * this._V;
                this.linearVelocity.x = v_x;
                this.linearVelocity.y = v_y;
            } else {
                this.linearVelocity = Vec2.ZERO;
            }

            if (this.linearVelocity.x) {
                const dt = 0.2;
                for (let count = 0; count < 100; count++) {
                    const time = dt * count;
                    // s = v_x * t
                    const dx = this.linearVelocity.x * time;
                    // h = v_y * t + 0.5 * a * t * t
                    const dy = this.linearVelocity.y * time + 0.5 * this._G * time * time;
                    // 当前时间点坐标
                    const targetX = startPos.x + dx;
                    const targetY = startPos.y + dy;
                    //到达结束节点
                    if (targetY < endPos.y) {
                        if (dir == 1 && targetX > endPos.x || dir == -1 && targetX < endPos.x) {
                            groups.push({ x: endPos.x, y: endPos.y });
                            break
                        }
                    };
                    groups.push({ x: targetX, y: targetY });
                }
            }

            cb(groups, dir, false)
        }
    }


}
