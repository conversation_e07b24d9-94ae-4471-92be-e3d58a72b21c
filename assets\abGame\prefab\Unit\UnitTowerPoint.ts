import { _decorator, Component, Label, log, math, Node, sp, Sprite, v2, v3, Vec2, Animation, UITransform, SpriteAtlas, Vec3, Color } from 'cc';

import { Role } from '../../scripts/Role';
import { C_Bundle, E_RoleState, E_RoleType } from '../../../scripts/ConstGlobal';

import Tool from '../../../scripts/libs/utils/Tool';
import { xcore } from '../../../scripts/libs/xcore';
import { ConfigHelper } from 'db://assets/scripts/config/ConfigHelper';
const { ccclass, property } = _decorator;

@ccclass('UnitTowerPoint')
export class UnitTowerPoint extends Component {

    @property(Animation)
    private anim: Animation

    @property(Label)
    private lbLev: Label = null;

    @property(sp.Skeleton)
    private bgAnim: sp.Skeleton = null;


    private fromRole: Role

    private state: E_RoleState = E_RoleState.None;

    private tempPos: Vec3 = new Vec3()

    private tempScale: Vec3 = new Vec3();



    finishCb: Function

    protected onLoad(): void {

    }

    init(fromRole: Role) {

        this.fromRole = fromRole;

        this.node.active = true;
        this.state = E_RoleState.None;
        this.clearAnim();
        let lev = this.fromRole.data.lev
        if (this.lbLev.string != `Lv.${lev}`) {
            this.lbLev.string = `Lv.${lev}`;

            let towerpointconfig = ConfigHelper.getInstance().getTowerPointByType(fromRole.data.monsterType, lev);
            if (towerpointconfig.fazhen) {
                this.bgAnim.enabled = true;
                xcore.res.bundleLoadSpine(C_Bundle.abGame, `./res/anim/towerpoint/${towerpointconfig.fazhen}`, this.bgAnim).then(() => {
                    this.bgAnim.setAnimation(0, 'animation', true)
                });
            } else {
                this.bgAnim.enabled = false;
            }


        }



    }
    getCompAnimNode() {
        return this.anim?.node
    }
    async doAttackAnim(cb, atkSpeed: number) {

        this.updateAnim(cb, atkSpeed)
    }
    clear(isTrueDestroy: boolean = false) {
        this.clearAnim();
        if (!this.node || !this.node.isValid) return
        if (isTrueDestroy) {
            this.node.destroy();
        } else {
            this.node.active = false;
            this.setPosAtonce(v2(-3000, -3000));
        }

    }
    onDead() { }
    clearAnim() {
        if (this.fromRole?.data?.type == E_RoleType.Tower) return
        if (this.anim) {
            this.anim.getComponent(Sprite).spriteFrame = null;
            this.anim.getComponent(Animation).enabled = false;
        }
    }
    setPosAtonce(pos: Vec2) {
        this.node?.setPosition(v3(pos.x, pos.y));
    }

    getFromRole() {
        if (!this.fromRole) return null
        return this.fromRole
    }
    getRoleType() {
        if (!this.fromRole) return null
        return this.fromRole.data.type;
    }
    setRankInfo() {

    }
    setLev(lev: number) {

    }
    //角色状态切换动画
    switchState() {
        switch (this.state) {
            case E_RoleState.None:

                break;
            case E_RoleState.Idle:

            case E_RoleState.Move:
                this.updateAnim();
                break;
            case E_RoleState.Attack:
                this.updateAnim();
                break;
            case E_RoleState.Hurt:

                break;
            case E_RoleState.Skill:

                this.updateAnim();
                break;
            case E_RoleState.WaitRelive:

                break;
            case E_RoleState.Dizziness:

                break

            case E_RoleState.Dead:
                this.clearAnim();
                break;

            default:
                break;
        }
    }
    update(dt) {
        if (!this.fromRole || !this.fromRole.data) return
        //缩放或方向转变
        if (this.fromRole.data.scale.y != this.anim.node.scale.y || (this.fromRole.data.moveDir > 0 && this.anim.node.scale.x < 0) || (this.fromRole.data.moveDir < 0 && this.anim.node.scale.x > 0)) {
            this.tempScale.set(this.fromRole.data.moveDir * this.fromRole.data.scale.y, this.fromRole.data.scale.y);

        }
        if (this.tempScale != this.anim.node.scale) {

            this.anim.node.setScale(this.tempScale);

        }


        //这里可以插值实现平滑移动
        if (this.node.position.x != this.fromRole.data.pos.x
            || this.node.position.y != this.fromRole.data.pos.y) {
            //let tempPos = this.node.position.clone();
            //逻辑坐标映射成creator里的坐标,简单除10好了
            this.node.getPosition(this.tempPos);
            this.tempPos = this.tempPos.lerp(v3(this.fromRole.data.pos.x, this.fromRole.data.pos.y,), 0.3);
            this.node.setPosition(this.tempPos);
            //log("this.tempPos", this.tempPos.x, this.tempPos.y)
        }

        if (this.state != this.fromRole.data.state) {
            this.state = this.fromRole.data.state;
            this.switchState();
        }
    }
    playHit() { }
    setHp() {

    }
    setGiftTitleInfo(config: any) {

    }
    setColor(color: number, time: number) {

    }
    async updateAnim(finishCb?, atkSpeed: number = 1) {

        if (this.state == E_RoleState.Move || this.state == E_RoleState.Attack || this.state == E_RoleState.Idle || this.state == E_RoleState.Skill) {
            let config = ConfigHelper.getInstance().getAnimConfigByJsonId((this.state == E_RoleState.Move || this.state == E_RoleState.Idle) ? this.fromRole.data.moveAnimation : (this.state == E_RoleState.Skill ? this.fromRole.data.skillAnimation : this.fromRole.data.attackAnimation));
            let duration = config.duration
            if (atkSpeed < duration) {
                duration = atkSpeed;

            }

            let animaData = {
                'sample': config.sample,
                'duration': config.duration,
                'speed': 1,
                'wrapMode': config.wrapMode,
                'path': config.path,
                'name': config.name
            }
            let atlasName = animaData.name.split('-')[0] || 'default';
            let atlas = xcore.res.getAtlas(atlasName);

            //配置动画中心位置
            let animUITransform = this.anim.node.getComponent(UITransform);

            for (let i = 0; i < animaData.sample; i++) {
                let name = `${animaData.name}_${i < 10 ? `0${i}` : i}`;
                let sf = atlas.getSpriteFrame(name)
                if (!sf) {
                    // let sf = await xcore.res.remoteLoadSprite(xcore.gameData.cospath + `image/${animaData.path}/${name}.png`) //await xcore.res.bundleLoadSprite(C_Bundle.abGame, `./res/anim/${animaData.path}/${name}`)
                    let sf = await xcore.res.bundleLoadSprite('resources', `./res/image/${animaData.path}/${name}`) //await xcore.res.bundleLoadSprite(C_Bundle.abGame, `./res/anim/${animaData.path}/${name}`)
                    xcore.res.addAtlasSprite(atlasName, name, sf);

                }
            }

            //缩放配置
            if (config.XCenterPoint == undefined || config.XCenterPoint == null) {
                config.XCenterPoint = 0.5;
            } else if (config.XCenterPoint == "") {
                config.XCenterPoint = 0;
            }
            if (config.YCenterPoint == undefined || config.YCenterPoint == null) {
                config.YCenterPoint = 0.5;
            } else if (config.YCenterPoint == "") {
                config.YCenterPoint = 0;
            }




            await Tool.createAnim(this.anim, animaData, atlas);

            if (animUITransform && animUITransform.node.isValid) {
                animUITransform.setAnchorPoint(config.XCenterPoint, config.YCenterPoint);
            }


            //log('animUITransform:', animUITransform.anchorPoint.x, animUITransform.anchorPoint.y, 'tempAncPos:', this.tempAncPos.x, this.tempAncPos.y, 'widht height::', firstWidth, firstHeight)
            if (finishCb) {
                finishCb(config.duration);

            }
        }


    }
}


