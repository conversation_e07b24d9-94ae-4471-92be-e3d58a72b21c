{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "d5f610dc-5c99-4403-afd6-9cb836c7c3a9", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "d5f610dc-5c99-4403-afd6-9cb836c7c3a9@6c48a", "displayName": "huxian-attack_08", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "d5f610dc-5c99-4403-afd6-9cb836c7c3a9", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "d5f610dc-5c99-4403-afd6-9cb836c7c3a9@f9941", "displayName": "huxian-attack_08", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": -39, "offsetY": -10, "trimX": 14, "trimY": 35, "width": 144, "height": 150, "rawWidth": 250, "rawHeight": 200, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-72, -75, 0, 72, -75, 0, -72, 75, 0, 72, 75, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [14, 165, 158, 165, 14, 15, 158, 15], "nuv": [0.056, 0.075, 0.632, 0.075, 0.056, 0.825, 0.632, 0.825], "minPos": [-72, -75, 0], "maxPos": [72, 75, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "d5f610dc-5c99-4403-afd6-9cb836c7c3a9@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "d5f610dc-5c99-4403-afd6-9cb836c7c3a9@6c48a"}}