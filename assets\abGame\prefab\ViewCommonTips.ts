import { _decorator, Component, Label, Node } from 'cc';
import { ViewBase } from '../../scripts/libs/manager/ViewBase';
const { ccclass, property } = _decorator;

@ccclass('ViewCommonTips')
export class ViewCommonTips extends ViewBase {
    @property(Label)
    private lbTitle: Label = null;
    @property(Label)
    private lbDesc: Label = null;

    public setData(data: any): void {
        this.lbDesc.string = data.desc;
    }
}


