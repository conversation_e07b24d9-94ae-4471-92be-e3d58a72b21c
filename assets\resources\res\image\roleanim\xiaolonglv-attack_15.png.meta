{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "b97a50d8-63e2-4b1b-970c-3e5b26803752", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "b97a50d8-63e2-4b1b-970c-3e5b26803752@6c48a", "displayName": "xiaolonglv-attack_15", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "b97a50d8-63e2-4b1b-970c-3e5b26803752", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "b97a50d8-63e2-4b1b-970c-3e5b26803752@f9941", "displayName": "xiaolonglv-attack_15", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": -25.5, "offsetY": -13, "trimX": 9, "trimY": 46, "width": 131, "height": 134, "rawWidth": 200, "rawHeight": 200, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-65.5, -67, 0, 65.5, -67, 0, -65.5, 67, 0, 65.5, 67, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [9, 154, 140, 154, 9, 20, 140, 20], "nuv": [0.045, 0.1, 0.7, 0.1, 0.045, 0.77, 0.7, 0.77], "minPos": [-65.5, -67, 0], "maxPos": [65.5, 67, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "b97a50d8-63e2-4b1b-970c-3e5b26803752@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "b97a50d8-63e2-4b1b-970c-3e5b26803752@6c48a"}}