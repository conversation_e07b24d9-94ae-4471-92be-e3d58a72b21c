import { error } from "cc";
import { Singleton } from "./Singleton";

function isJSON(str) {
    if (typeof str == 'string') {
        try {
            JSON.parse(str);
            return true;
        } catch (e) {
            return false;
        }
    }
    return false
}
declare type T_SDK_RequestConfig = {
    baseURL?: string
    method?: string // POST GET
    path?: string
    timeout?: number

    headers?: Record<string, any>
    params?: Record<string, string | number | boolean>
    data?: any

}
declare type T_SDK_Response = {
    code: number
    message: string
    data?: any
}

export class HttpRequest extends Singleton {
    private requestFulfilled: (config) => any
    private responseFulfilled: (config) => any

    public defaults: T_SDK_RequestConfig = {
        timeout: 5000,
        headers: {},
        params: {},
    }
    //请求头信息
    public addHeader(name: string, value: string) {
        this.defaults.headers[name] = value;
    }

    private mergeDefaultConfig(config: T_SDK_RequestConfig) {
        if (!config.baseURL) {
            config.baseURL = this.defaults.baseURL
        }

        if (this.defaults.headers) {
            if (config.headers) {
                config.headers = { ...this.defaults.headers, ...config.headers }
            } else {
                config.headers = { ...this.defaults.headers }
            }
        }

        if (this.defaults.params) {
            if (config.params) {
                config.params = { ...this.defaults.params, ...config.params }
            } else {
                config.params = { ...this.defaults.params }
            }
        }


        return config
    }

    private formatConfig(config: T_SDK_RequestConfig) {
        if (config.baseURL && config.baseURL.endsWith('/')) {
            config.baseURL = config.baseURL.substring(0, config.baseURL.length - 1)
        }
        if (config.path && !config.path.startsWith('/')) {
            config.path = '/' + config.path
        }
        return config
    }

    private doRequest(config: T_SDK_RequestConfig): Promise<T_SDK_Response> {
        return new Promise(async (resolve, reject) => {
            if (this.requestFulfilled) {
                const temp = this.requestFulfilled({ ...config })
                if (temp) {
                    config = temp
                }
            }

            const handleResponse = (result: any) => {
                if (result.response) {
                    if (isJSON(result.response)) {
                        result.data = JSON.parse(result.response)
                    } else {
                        result.data = result.response
                    }
                    delete result.response
                }

                if (this.responseFulfilled) {
                    result = this.responseFulfilled(result)
                    if (result instanceof Error) {
                        reject(result)
                    } else {
                        resolve(result)
                    }
                } else {
                    resolve(result)
                }
            }
            let paramStr = ''
            for (const key in config.params) {
                if (config.params[key] !== null && config.params[key] !== undefined) {
                    if (paramStr) {
                        paramStr += `&${key}=${config.params[key]}`
                    } else {
                        paramStr += `?${key}=${config.params[key]}`
                    }
                }
            }

            let url = `${config.baseURL}${config.path}${paramStr}`
            const xhr = new XMLHttpRequest()
            xhr.timeout = config.timeout
            xhr.open(config.method, url)

            for (const key in config.headers) {
                if (config.headers[key] !== null && config.headers[key] !== undefined) {
                    xhr.setRequestHeader(key, config.headers[key]);
                }
            }

            xhr.onloadend = () => {
                let { response, readyState, responseURL, status } = xhr
                let result: any = { response, readyState, responseURL, status, path: config.path }

                if (xhr.readyState == 4 && (xhr.status >= 200 && xhr.status < 400)) {
                    handleResponse(result)
                } else {
                    console.log('onloadend-error:', result);
                    error(status)
                    reject(status)
                }
            };

            if (config.data && (config.headers['content-type'] === 'application/json' || config.headers['Content-Type'] === 'application/json')) {
                xhr.send(JSON.stringify(config.data))
            } else {
                xhr.send(config.data)
            }
        })
    }

    public raw(url: string, method: string = 'GET', data?: any, headers?: Record<string, string>): Promise<T_SDK_Response> {
        return new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest()
            xhr.open(method, url)
            if (headers) {
                for (const key in headers) {
                    if (headers[key] !== null && headers[key] !== undefined) {
                        xhr.setRequestHeader(key, headers[key]);
                    }
                }
            }
            xhr.onloadend = () => {
                if (xhr.readyState == 4 && (xhr.status >= 200 && xhr.status < 400)) {
                    if (isJSON(xhr.response)) {
                        resolve(JSON.parse(xhr.response))
                    } else {
                        resolve(xhr.response)
                    }
                } else {
                    reject(xhr.status)
                }
            };

            if (data && headers && (headers['content-type'] === 'application/json' || headers['Content-Type'] === 'application/json')) {
                xhr.send(JSON.stringify(data))
            } else {
                xhr.send(data)
            }
        })
    }

    public get(path: string, config?: T_SDK_RequestConfig) {
        config = config || {}
        config.method = 'GET'
        config.path = path
        config = this.mergeDefaultConfig(config)
        config = this.formatConfig(config)
        return this.doRequest(config)
    }
    /**
     * 
     * @param path 路径
     * @param data body
     * @param config params-》query
     * @returns 
     */
    public post(path: string, data?: any, config?: T_SDK_RequestConfig) {
        config = config || {}
        config.data = data
        config.method = 'POST'
        config.path = path
        config = this.mergeDefaultConfig(config)
        config = this.formatConfig(config)
        return this.doRequest(config)
    }

    public setRequestInterceptor(fulfilled: (config) => any) {
        this.requestFulfilled = fulfilled
    }

    public setResponseInterceptor(fulfilled: (config) => any) {
        this.responseFulfilled = fulfilled
    }

}

