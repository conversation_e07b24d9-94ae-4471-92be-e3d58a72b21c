export class SimpleDelegate {

    private _fnQuene;
    private _waitingQuene;

    constructor() {
        this._fnQuene = [];
        this._waitingQuene = [];
    }

    do() {
        let len = this._waitingQuene.length; //缓存本次执行委托时已有的操作数量
        //首先调用所有缓存的操作
        for (let i = 0; i < len; i++) {
            this._waitingQuene[i]();
        }
        this._waitingQuene.length = 0;
        len = this._fnQuene.length;    //缓存当前函数队列的长度
        for (let i = 0; i < len; i++) {
            this._fnQuene[i]();
        }
        this._fnQuene.length = 0;
    }

    add(fn: Function) {
        this._waitingQuene.push(() => {
            this._fnQuene.push(fn);
        })
    }

    remove(fn: Function) {
        this._waitingQuene.push(() => {
            this._fnQuene.splice(this._fnQuene.indexOf(fn), 1);
        })
    }


}