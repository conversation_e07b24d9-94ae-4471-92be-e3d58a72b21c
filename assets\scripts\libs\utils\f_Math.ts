import { FixedPointNum } from "./FixedPointNum";

let kScale = new FixedPointNum(1000);
let halfF = new FixedPointNum(2)
//floating-point number math 定点数计算
export class f_Math {
    private static randomSeed = 5;
    private static randomSeedFixedPointNum = new FixedPointNum(233280);
    private static sinArr: FixedPointNum[] = [0, 17, 35, 52, 70, 87, 105, 122, 139, 156, 174, 191, 208, 225, 242, 259, 276, 292, 309, 326, 342, 358, 375, 391, 407, 423, 438, 454, 469, 485, 500, 515, 530, 545, 559, 574, 588, 602, 616, 629, 643, 656, 669, 682, 695, 707, 719, 731, 743, 755, 766, 777, 788, 799, 809, 819, 829, 839, 848, 857, 866, 875, 883, 891, 899, 906, 914, 921, 927, 934, 940, 946, 951, 956, 961, 966, 970, 974, 978, 982, 985, 988, 990, 993, 995, 996, 998, 999, 999, 1000, 1000, 1000, 999, 999, 998, 996, 995, 993, 990, 988, 985, 982, 978, 974, 970, 966, 961, 956, 951, 946, 940, 934, 927, 921, 914, 906, 899, 891, 883, 875, 866, 857, 848, 839, 829, 819, 809, 799, 788, 777, 766, 755, 743, 731, 719, 707, 695, 682, 669, 656, 643, 629, 616, 602, 588, 574, 559, 545, 530, 515, 500, 485, 469, 454, 438, 423, 407, 391, 375, 358, 342, 326, 309, 292, 276, 259, 242, 225, 208, 191, 174, 156, 139, 122, 105, 87, 70, 52, 35, 17, 0, -17, -35, -52, -70, -87, -105, -122, -139, -156, -174, -191, -208, -225, -242, -259, -276, -292, -309, -326, -342, -358, -375, -391, -407, -423, -438, -454, -469, -485, -500, -515, -530, -545, -559, -574, -588, -602, -616, -629, -643, -656, -669, -682, -695, -707, -719, -731, -743, -755, -766, -777, -788, -799, -809, -819, -829, -839, -848, -857, -866, -875, -883, -891, -899, -906, -914, -921, -927, -934, -940, -946, -951, -956, -961, -966, -970, -974, -978, -982, -985, -988, -990, -993, -995, -996, -998, -999, -999, -1000, -1000, -1000, -999, -999, -998, -996, -995, -993, -990, -988, -985, -982, -978, -974, -970, -966, -961, -956, -951, -946, -940, -934, -927, -921, -914, -906, -899, -891, -883, -875, -866, -857, -848, -839, -829, -819, -809, -799, -788, -777, -766, -755, -743, -731, -719, -707, -695, -682, -669, -656, -643, -629, -616, -602, -588, -574, -559, -545, -530, -515, -500, -485, -469, -454, -438, -423, -407, -391, -375, -358, -342, -326, -309, -292, -276, -259, -242, -225, -208, -191, -174, -156, -139, -122, -105, -87, -70, -52, -35, -17] as any;
    private static cosArr: FixedPointNum[] = [1000, 1000, 999, 999, 998, 996, 995, 993, 990, 988, 985, 982, 978, 974, 970, 966, 961, 956, 951, 946, 940, 934, 927, 921, 914, 906, 899, 891, 883, 875, 866, 857, 848, 839, 829, 819, 809, 799, 788, 777, 766, 755, 743, 731, 719, 707, 695, 682, 669, 656, 643, 629, 616, 602, 588, 574, 559, 545, 530, 515, 500, 485, 469, 454, 438, 423, 407, 391, 375, 358, 342, 326, 309, 292, 276, 259, 242, 225, 208, 191, 174, 156, 139, 122, 105, 87, 70, 52, 35, 17, 0, -17, -35, -52, -70, -87, -105, -122, -139, -156, -174, -191, -208, -225, -242, -259, -276, -292, -309, -326, -342, -358, -375, -391, -407, -423, -438, -454, -469, -485, -500, -515, -530, -545, -559, -574, -588, -602, -616, -629, -643, -656, -669, -682, -695, -707, -719, -731, -743, -755, -766, -777, -788, -799, -809, -819, -829, -839, -848, -857, -866, -875, -883, -891, -899, -906, -914, -921, -927, -934, -940, -946, -951, -956, -961, -966, -970, -974, -978, -982, -985, -988, -990, -993, -995, -996, -998, -999, -999, -1000, -1000, -1000, -999, -999, -998, -996, -995, -993, -990, -988, -985, -982, -978, -974, -970, -966, -961, -956, -951, -946, -940, -934, -927, -921, -914, -906, -899, -891, -883, -875, -866, -857, -848, -839, -829, -819, -809, -799, -788, -777, -766, -755, -743, -731, -719, -707, -695, -682, -669, -656, -643, -629, -616, -602, -588, -574, -559, -545, -530, -515, -500, -485, -469, -454, -438, -423, -407, -391, -375, -358, -342, -326, -309, -292, -276, -259, -242, -225, -208, -191, -174, -156, -139, -122, -105, -87, -70, -52, -35, -17, 0, 17, 35, 52, 70, 87, 105, 122, 139, 156, 174, 191, 208, 225, 242, 259, 276, 292, 309, 326, 342, 358, 375, 391, 407, 423, 438, 454, 469, 485, 500, 515, 530, 545, 559, 574, 588, 602, 616, 629, 643, 656, 669, 682, 695, 707, 719, 731, 743, 755, 766, 777, 788, 799, 809, 819, 829, 839, 848, 857, 866, 875, 883, 891, 899, 906, 914, 921, 927, 934, 940, 946, 951, 956, 961, 966, 970, 974, 978, 982, 985, 988, 990, 993, 995, 996, 998, 999, 999, 1000] as any;
    private static tanArr = [0, 17, 35, 52, 70, 87, 105, 123, 141, 158, 176, 194, 213, 231, 249, 268, 287, 306, 325, 344, 364, 384, 404, 424, 445, 466, 488, 510, 532, 554, 577, 601, 625, 649, 675, 700, 727, 754, 781, 810, 839, 869, 900, 933, 966, 1000, 1036, 1072, 1111, 1150, 1192, 1235, 1280, 1327, 1376, 1428, 1483, 1540, 1600, 1664, 1732, 1804, 1881, 1963, 2050, 2145, 2246, 2356, 2475, 2605, 2747, 2904, 3078, 3271, 3487, 3732, 4011, 4331, 4705, 5145, 5671, 6314, 7115, 8144, 9514, 11430, 14301, 19081, 28636, 57290, Infinity];

    //设置随机数种子
    static setRandSeed(seed: number) {
        this.randomSeed = seed;
    }

    static getRandomSeed() {
        return this.randomSeed;
    }

    static random() {
        this.randomSeed = (this.randomSeed * 9301 + 49297) % 233280;
        return new FixedPointNum(this.randomSeed).div(this.randomSeedFixedPointNum);
    }
    //初始化 三角函数查表  
    public static initSinCos() {
        for (let i = 0; i < this.sinArr.length; i++) {
            this.sinArr[i] = new FixedPointNum(this.sinArr[i] as any).div(kScale);
        }
        for (let i = 0; i < this.cosArr.length; i++) {
            this.cosArr[i] = new FixedPointNum(this.cosArr[i] as any).div(kScale);
        }

    }

    static sin(angleInt: number) {
        angleInt = angleInt % 360;
        if (angleInt < 0) {
            angleInt += 360;
        }
        return this.sinArr[angleInt];
    }

    static cos(angleInt: number) {
        angleInt = angleInt % 360;
        if (angleInt < 0) {
            angleInt += 360;
        }
        return this.cosArr[angleInt];
    }
    static tan(angleInt: number) {
        angleInt = angleInt % 360;
        if (angleInt < 0) {
            angleInt += 360;
        }
        return this.tanArr[angleInt];
    }
    static atan2(y: number, x: number) {
        if (x === 0) {
            if (y > 0) {
                return 90;
            } else {
                return -90;
            }
        }
        let tmp = new FixedPointNum(y).div(new FixedPointNum(x)).mul(kScale).floor();
        if (tmp < 0) {
            tmp = -tmp;
        }
        let angle = 0;
        let tanArr = this.tanArr;
        while (tmp > tanArr[angle]) {
            angle++;
        }
        if (y >= 0) {
            if (x <= 0) {
                angle = 180 - angle;
            }
        } else if (x <= 0) {
            angle += 180;
        } else {
            angle = 360 - angle;
        }
        return angle;
    }

    static sqrt(x: number, n?: FixedPointNum): FixedPointNum {
        let tmpx = new FixedPointNum(x)
        n = n || tmpx.div(halfF);

        let res = tmpx.div(n).add(n).div(halfF) /* (x / n + n) / 2 */;
        if (
            res.mul(res).toNumber() == tmpx.toNumber() || res.toNumber() == tmpx.div(res).add(res).div(halfF).toNumber()
        ) {
            return res;
        } else {
            return this.sqrt(x, res)           //迭代
        }
    }

    static randomFloat() {
        return this.randomBetween(0, 9) / 10
    }

    /**
     * 随机获取数组下标
     * （传入 3，则返回0/1/2）
     */
    static randomIntNum(num: number) {
        if (num === 0) {
            num = 1;
        }
        let tmp = this.random().mul(new FixedPointNum(num)).floor();
        return tmp;
    }


    /**
     * 随机数组中的一个元素
     */
    static randomArrElement<T>(arr: T[]) {
        return arr[this.randomIntNum(arr.length)];
    }

    /**
     * 范围随机
     * 传入1和3，则返回 1/2/3
     * @param num1 
     * @param num2 
     */
    static randomBetween(num1: number, num2: number) {
        return num1 + this.randomIntNum(num2 - num1 + 1);
    }

    random
}
f_Math.initSinCos();





