{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "3c82425d-5da7-4b7f-8762-07b5fc4d868c", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "3c82425d-5da7-4b7f-8762-07b5fc4d868c@6c48a", "displayName": "xiaolonglv-attack_09", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "3c82425d-5da7-4b7f-8762-07b5fc4d868c", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "3c82425d-5da7-4b7f-8762-07b5fc4d868c@f9941", "displayName": "xiaolonglv-attack_09", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": -27, "offsetY": -16, "trimX": 28, "trimY": 45, "width": 90, "height": 142, "rawWidth": 200, "rawHeight": 200, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-45, -71, 0, 45, -71, 0, -45, 71, 0, 45, 71, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [28, 155, 118, 155, 28, 13, 118, 13], "nuv": [0.14, 0.065, 0.59, 0.065, 0.14, 0.775, 0.59, 0.775], "minPos": [-45, -71, 0], "maxPos": [45, 71, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "3c82425d-5da7-4b7f-8762-07b5fc4d868c@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "3c82425d-5da7-4b7f-8762-07b5fc4d868c@6c48a"}}