import { instantiate, log, v2, v3, Vec2, Vec3, Node, UITransform, warn } from "cc"
import { C_Bundle, C_GiftSkill, E_BuffType, E_EVENT, E_GiftMessageType, E_RoleState, E_RoleType, E_SkillType } from "../../scripts/ConstGlobal"
import { Role } from "./Role"
import { UnitSkillComp } from "../prefab/Unit/UnitSkillComp"
import { xcore } from "../../scripts/libs/xcore"
import { FightMgr } from "./FightMgr"

import Tool from "../../scripts/libs/utils/Tool"
import { ConfigHelper } from "../../scripts/config/ConfigHelper"

/**
 * 技能逻辑
 * 
 */
export class Skill {

    /**配置表技能jsonId */
    skillid: string
    /**技能类型 */
    skillType: E_SkillType
    /**技能次数 -100 不限次数 */
    useNum: number = 1
    /**释放速度 间隔  */
    speed: number = 3
    /**技能攻击力 */
    //  skillatkNum: number

    skillatkMax: number
    skillatkMix: number
    normalskillAtk: number
    healNum: number
    constructor() {

    }
    /**技能攻击力 */
    public get skillatkNum(): number {
        if (!this.skillatkMax || !this.skillatkMix) {
            return this.normalskillAtk
        }
        return Tool.randomNumber(this.skillatkMix, this.skillatkMax);
    }


    skillLastTime: number

    /**释放范围 a.0单体 b.>=10000全局 c.其他 像素范围*/
    distance: number = 0
    /**等级 */
    level: number = 0

    /**攻击特效id */
    animId01: string
    /**受击特效id */
    animId02: string


    /**角色引用 */
    fromRole: Role



    /**技能作用目标   
    1.敌方
    2.自己
    3.自己及友军"
    */
    skillTarget: number = 1

    /**走时 */
    tickTime: number = 0;
    /**是否常驻技能 */
    isLongSkill: boolean = false

    /**表现层 */
    comp: UnitSkillComp = null;

    nowSpeed: number

    lightningNum: number = 0;

    offsetPos: Vec3 = v3();
    tempPos: Vec3 = v3();

    /**技能附带buffid */
    buffJsonId: string
    hpDeclinePercentage: number = 0;
    cloneNum: number = 0;
    cloneJsonId: string

    sound: string;
    skillname: string
    createComp: boolean = false;

    private isSkinSkill: boolean = null;
    private isDestroy: boolean = false;


    //初始化
    async init(role: Role, skillType: E_SkillType, lev: number = 1, jsonId?: string, num: number = 1, isLongSkll?: boolean) {

        this.fromRole = role;
        let config
        if (jsonId) {
            config = ConfigHelper.getInstance().getSkillConfigByJsonId(jsonId);
        } else {
            config = ConfigHelper.getInstance().getSkillConfigByType(skillType, lev);
        }

        log('skill config', skillType, lev, config, num, 'buff:', config.state);
        if (!config) {
            //this.destroy();
            return
        }
        this.isDestroy = false;
        this.useNum = 1;
        this.isLongSkill = null;
        this.skillname = config.name;
        this.skillType = skillType || config.type;
        this.skillTarget = config.skillTarget;
        this.distance = config.skillRange;
        this.lightningNum = config.lightningNum;
        this.level = lev;
        this.animId01 = config.skillAnimation;
        this.animId02 = config.impactAnimation;
        this.tickTime = 0;
        this.buffJsonId = config.state;
        this.skillid = config.jsonId;
        this.sound = config.soundEffects;
        this.checkIfSkinSkill();
        if (this.skillType == E_SkillType.GreatAttack) {
            this.useNum = lev;
            lev = 1;
        }
        //仙族常驻技能 间隔释放 不会消失
        if (/* this.skillType == E_SkillType.GreatAttack ||  */this.skillType == E_SkillType.GreatFire ||
            this.skillType == E_SkillType.GreatLightning || this.skillType == E_SkillType.GreatRock ||
            this.skillType == E_SkillType.GreatSkyRock || this.skillType == E_SkillType.GreatFires || this.skillType == E_SkillType.LineAttack
        ) {
            this.isLongSkill = true;
        }
        //妖族的都是携带buff技能，可以一直存在
        else if (this.fromRole.data.type == E_RoleType.Monster) {
            this.isLongSkill = true;
        }
        //小技能
        else {
            let weaponConfig = ConfigHelper.getInstance().getWeaponConfigByType(this.skillType);
            this.useNum = weaponConfig ? (weaponConfig.skillNum * num) : num;
            if (this.useNum <= 0) {
                this.useNum = 1;
            }
            log("userNum init:", this.useNum, weaponConfig);
        }

        if (skillType == E_SkillType.Invincible) {
            log("塔防无敌状态", this.useNum);
            this.speed = 0.01;
            this.normalskillAtk = config.guardTime || 10;
        }
        else if (skillType == E_SkillType.AddHp) {
            this.speed = config.skillCooldown;
            this.skillLastTime = config.healingTime;
            this.healNum = config.healing;

        } else if (skillType == E_SkillType.Weak) {
            this.speed = 0.01;
            this.isLongSkill = null;
            this.useNum = 1;
        }
        else {
            this.speed = config.skillCooldown;
            if (config.skillDamage) {
                let atts = config.skillDamage.toString().split('|');
                this.skillatkMix = parseInt(atts[0] || 0);
                this.skillatkMax = parseInt(atts[1] || 0);
                this.normalskillAtk = parseInt(atts[0] || 0);
            }

            this.hpDeclinePercentage = Number(config.hpDeclinePercentage) || 0;
            this.cloneNum = config.humanClone || 0;
            this.cloneJsonId = config.cloneMonsterId || null;
            //法宝
            if (this.isLongSkill && this.fromRole.data.type == E_RoleType.Hero) {
                this.createSkillComp(skillType);
            }
        }
        this.refreshSpeed()
        if (this.fromRole.data.type == E_RoleType.TowerPoint) {
            log('炮塔技能：', this.isLongSkill, skillType, config, this.nowSpeed)
        }
        if (isLongSkll) {
            this.isLongSkill = isLongSkll;
        }
    }
    refreshSpeed() {
        if (!this.speed) {
            this.nowSpeed = null;
        } else {
            this.nowSpeed = this.speed * this.fromRole.data.skillSpeedBuff * (this.speed / (this.speed * (1 + this.fromRole.data.dimonEffects.skillcd)));
        }


    }
    getSkillSpeed() {
        return this.nowSpeed
    }
    // 实例化表现层 
    async createSkillComp(type: E_SkillType, offset: Vec3 = v3(0, 0)) {
        //怪物测不用显示技能buff表现层
        /* if (this.fromRole.data.type == E_RoleType.Monster) {
            return
        } */
        if (!this.isLongSkill || this.isSkinSkill || this.createComp) {
            return
        }
        this.createComp = true;

        if (!this.comp || !this.comp.node || !this.comp.node.isValid || (this.comp && this.comp.getType() != type)) {

            const pfbSkill = await xcore.res.bundleLoadPrefab(C_Bundle.abGame, './prefab/Unit/UnitSkillComp');
            let nd = instantiate(pfbSkill)
            nd.parent = FightMgr.getInstance().skillParentNd;
            this.comp = nd.getComponent(UnitSkillComp);

            log('newCreate', type)
        } else {

            log('oldCreate', type, this.comp.getType())
        }
        /*  const pfbSkill = await xcore.res.bundleLoadPrefab(C_Bundle.abGame, './prefab/Unit/UnitSkillComp');
         let nd = instantiate(pfbSkill)
         nd.parent = FightMgr.getInstance().skillParentNd;
         this.comp = nd.getComponent(UnitSkillComp); */

        log('createSkillComp', this.comp, this.isLongSkill, type, this)
        this.resetSkillCompPos(type, offset);
        this.comp.node.active = true;
        this.createComp = false;
        return this.comp
    }

    resetSkillCompPos(type, offset: Vec3 = v3(0, 0)) {
        if (!this.comp) return
        if (this.skillType == E_SkillType.GreatFire) {
            this.offsetPos = v3(-60, 20)
        } else if (this.skillType == E_SkillType.GreatFires) {
            this.offsetPos = v3(-80, 90)
        } else if (this.skillType == E_SkillType.GreatLightning) {
            this.offsetPos = v3(80, 90)
        } else if (this.skillType == E_SkillType.GreatRock) {
            this.offsetPos = v3(60, 20)
        } else if (this.skillType == E_SkillType.GreatSkyRock) {
            this.offsetPos = v3(0, 160 + offset.y)
        } else {
            this.offsetPos = v3();
        }
        this.comp.setData({
            jsonId: this.skillid,
            roleData: this.fromRole.data,
            targetSkill: this,
            type: type || this.skillType,
            lev: this.level,
            offsetPos: this.offsetPos
        });
    }
    //查配置 判断该技能是否有附带buff
    checkIfHaveBuff(buffJsonId: string, targets?: Role[], isEffectAtonce: boolean = false) {
        if (!buffJsonId) return


        let buff = this.fromRole.checkIfHaveBuff(buffJsonId);
        if (buff) {
            buff.setTargets(targets, isEffectAtonce);

        }

    }

    /**技能释放 */
    async releaseSkill() {
        //log("releaseSkill", this.useNum, this.skillType)
        if (!this.isLongSkill) {

            if (this.useNum <= 0) {
                return
            }
        }

        switch (this.skillType) {
            //塔防无敌技能特殊处理 走单独流程
            case E_SkillType.Invincible:
                this.fromRole.buffTower(this.skillatkNum);
                if (!this.isLongSkill) {
                    if (this.useNum > 0) {
                        this.useNum -= 1;
                    }
                    if (this.useNum <= 0) {
                        this.destroy();
                    }
                }
                break;

            case E_SkillType.AddHp:

                FightMgr.getInstance().saveTower(this.healNum, this.skillLastTime, this.fromRole.data.userId)
                if (this.useNum > 0) {
                    this.useNum -= 1;
                }
                if (this.useNum <= 0) {
                    this.destroy();
                }
                break;
            case E_SkillType.CreateMore:
                break
            default:
                // 仙族释放目标筛选
                if (this.fromRole.data.type == E_RoleType.Hero || this.fromRole.data.type == E_RoleType.TowerPoint) {
                    //log("releaseSkill hero", this.skillType)

                    let roles = this.fromRole.getNearEnemys(1);
                    let orgRole = roles[0];
                    if (!orgRole) return
                    let orgPos = orgRole.data.pos;
                    let buffTargets = [];
                    if (this.skillTarget == 1) {
                        let attNum = 300;
                        if (this.distance == 0) {
                            attNum = 1;
                        } else if (this.distance >= 10000) {
                            attNum = 1000;
                        }
                        if (this.lightningNum) {
                            attNum = this.lightningNum;
                            this.distance = 1000;
                        }
                        if (this.skillType == E_SkillType.LineAttack) {
                            let lineBox = new Node().addComponent(UITransform)
                            this.tempPos.set((this.fromRole.data.pos.x + orgPos.x) / 2, (this.fromRole.data.pos.y + orgPos.y) / 2 + 100);
                            lineBox.node.setPosition(this.tempPos);

                            // 假设坐标a和b在二维平面上，坐标差为
                            let dx = this.fromRole.data.pos.x - orgPos.x;
                            let dy = this.fromRole.data.pos.y - orgPos.y;

                            //线段ab与x轴的夹角可以用反正切函数计算：arctan2
                            let angle = Math.atan2(dy, dx) * 180 / Math.PI
                            lineBox.node.angle = angle;
                            lineBox.width = 100;
                            lineBox.height = Math.sqrt((this.fromRole.data.pos.y - orgPos.y) * (this.fromRole.data.pos.y - orgPos.y) + (this.fromRole.data.pos.x - orgPos.x) * (this.fromRole.data.pos.x - orgPos.x))
                            buffTargets = FightMgr.getInstance().checkLineMonster(lineBox.node, this.distance, attNum);
                            // if (buffTargets.length > 0) {

                            //     this.fromRole.setState(E_RoleState.Attack)
                            //     this.fromRole.backTempState(E_RoleState.Idle, 1000);
                            // }
                            //xcore.event.raiseEvent(E_EVENT.GraphicsEffectRange2, { x: this.tempPos.x, y: this.tempPos.y, width: 100, height: lineBox.height, angle })
                            // log("linebox:", lineBox, angle, lineBox.height, this.fromRole.data.pos, orgPos, this.isLongSkill)
                        } else {
                            buffTargets = FightMgr.getInstance().checkNearMonster(orgRole.comp?.node, this.distance, attNum);
                        }

                    } else if (this.skillTarget == 2) {
                        buffTargets = [this.fromRole]
                    } else if (this.skillTarget == 3) {
                        buffTargets = FightMgr.getInstance().getHeroRoles();
                    }
                    // if (this.fromRole.data.type != E_RoleType.TowerPoint && this.isLongSkill && buffTargets.length > 0 && !this.isSkinSkill) {
                    //     xcore.event.raiseEvent(E_EVENT.GiftMessage, {
                    //         type: E_GiftMessageType.UseProp,
                    //         userId: this.fromRole.data.userId,
                    //         name: this.fromRole.data.nickName,
                    //         avatar: this.fromRole.data.iconUrl,
                    //         skillType: this.skillType,
                    //         num: 1,
                    //         lev: this.level,
                    //     })
                    // }
                    if (this.isLongSkill) {

                    } else if (buffTargets.length > 0) {
                        this.useNum -= 1;
                        //log(this.skillType + '类型 单次技能释放' + '剩余次数', this.useNum);
                    }

                    if (this.skillType == E_SkillType.MoveSpeed || this.skillType == E_SkillType.SkillSpeed ||
                        this.skillType == E_SkillType.Dizziness || this.skillType == E_SkillType.Crazy || this.skillType == E_SkillType.Weak ||
                        this.skillType == E_SkillType.MoveSpeed2 || this.skillType == E_SkillType.SkillSpeed2 ||
                        this.skillType == E_SkillType.Dizziness2 || this.skillType == E_SkillType.Crazy2 || this.skillType == E_SkillType.Weak2
                    ) {
                        this.checkHeroBuff(buffTargets);
                        return
                    }

                    let effectData = {
                        pos0: this.fromRole.data.pos,
                        pos1: orgPos,
                        skill: this.skillType,
                        offsetpos: this.offsetPos || v3(),
                        animId01: this.animId01,
                        animId02: this.animId02,
                        formRoleType: this.fromRole.data.type,
                        range: this.distance,
                        sound: this.sound,
                        //攻击表现播放完后回调
                        cb: () => {

                            //仙族普通技能攻击释放
                            for (let i = 0; i < buffTargets.length; i++) {
                                let role = buffTargets[i];
                                if (role && role.isRoleAlive()) {
                                    role.hurt(this.skillatkNum/*  * (1 + this.fromRole.data.atkNumBuff) */, this.fromRole.data.userId, this.skillType);
                                }

                            }
                            if (buffTargets.length > 0) {
                                this.checkIfHaveBuff(this.buffJsonId, buffTargets);

                            }
                            if (this.skillType == E_SkillType.SkyRock) {
                                xcore.event.raiseEvent(E_EVENT.ShakeCam);
                            }
                            if (!this.isLongSkill && this.useNum <= 0) {
                                this.destroy();
                            }
                        }
                    } as any

                    //添加雷电多目标坐标信息
                    if (this.lightningNum > 0 && buffTargets.length > 0) {
                        let lightningTargets = [];
                        for (let i = 0; i < buffTargets.length; i++) {
                            let pos = {
                                x: buffTargets[i].data.pos.x,
                                y: buffTargets[i].data.pos.y,
                            }
                            lightningTargets.push(pos);
                        }
                        effectData.lightningTargets = lightningTargets;
                    }
                    xcore.event.raiseEvent(E_EVENT.SkillEffect, effectData);
                    //范围攻击
                    // if (this.distance > 0 && this.distance < 10000) {
                    //     xcore.event.raiseEvent(E_EVENT.GraphicsEffectRange, { pos: orgPos, range: this.lightningNum > 0 ? 0 : this.distance })
                    // }
                    /*  if (!this.isLongSkill) {
                         if (buffTargets.length > 0) {
                             this.useNum -= 1;
                             log(this.skillType + '类型 单次技能释放' + '剩余次数', this.useNum);
                         }
 
                     } else {
                         log(this.skillType + '类型 常驻技能释放');
                     } */
                }
                //妖族 技能buff
                else if (this.fromRole.data.type == E_RoleType.Monster) {

                    //log("releaseSkill monster", this.skillType)

                    this.checkMonsterBuff();


                }



                break;
        }

    }
    checkIfSkinSkill() {
        // 遍历键
        let values = C_GiftSkill
        this.isSkinSkill = true;
        values.forEach(e => {
            if (e == this.skillType) {
                this.isSkinSkill = false;
            }
        })

        log('this.isSkinSkill', this.isSkinSkill, this.skillType)
    }
    createSound() {
        if (this.sound) {
            xcore.sound.remotePlayOneShot(this.sound);
        }
    }
    // 非常驻技能添加释放次数
    addNum(num: number) {
        let config = ConfigHelper.getInstance().getWeaponConfigByType(this.skillType)

        this.useNum += !config ? 1 : ((config.skillNum || 1) * num);
        log('userNum addNum:+', this.useNum);
        if (this.skillType == E_SkillType.AddHp) {
            warn('回血add')
        }
    }
    checkHeroBuff(buffTargets: any[]) {

        if (buffTargets.length > 0) {
            this.checkIfHaveBuff(this.buffJsonId, buffTargets, true);
            let isSkillAnim = this.fromRole.checkSkillAnim();
            if (isSkillAnim) {
                xcore.event.raiseEvent(E_EVENT.SkillEffect, {
                    pos0: this.fromRole.data.pos,
                    skill: this.skillType,
                    offsetpos: this.offsetPos || v3(),
                    // lightningTargets: this.lightningNum ? buffTargets : null,
                    animId01: this.animId01,

                });
            }
            if (this.fromRole.data.type == E_RoleType.Monster) {
                xcore.event.raiseEvent(E_EVENT.SkillTips, {
                    txt: this.skillname,
                    pos: this.fromRole.data.pos,
                })
            }

            if (this.useNum > 0) {
                this.useNum -= 1;
            }
        }
    }
    checkMonsterBuff() {
        let buffTargets = [];
        if (this.skillTarget == 1) {
            buffTargets = FightMgr.getInstance().getHeroRoles();
        } else if (this.skillTarget == 2) {
            buffTargets = [this.fromRole]
        } else if (this.skillTarget == 3) {
            let attNum = 50;
            if (this.distance == 0) {
                attNum = 1;
            } else if (this.distance >= 10000) {
                attNum = 1000;
            }
            if (this.lightningNum) {
                attNum = this.lightningNum;
            }
            buffTargets = FightMgr.getInstance().checkNearMonster(this.fromRole.comp.node, this.distance, attNum);
        }

        if (buffTargets.length > 0) {
            this.checkIfHaveBuff(this.buffJsonId, buffTargets, true);
            let isSkillAnim = this.fromRole.checkSkillAnim();
            if (isSkillAnim) {
                xcore.event.raiseEvent(E_EVENT.SkillEffect, {
                    pos0: this.fromRole.data.pos,
                    skill: this.skillType,
                    offsetpos: this.offsetPos || v3(),
                    // lightningTargets: this.lightningNum ? buffTargets : null,
                    animId01: this.animId01,

                });

            }

            if (this.fromRole.data.type == E_RoleType.Monster) {
                xcore.event.raiseEvent(E_EVENT.SkillTips, {
                    txt: this.skillname,
                    pos: this.fromRole.data.pos,
                })

            }
            if (this.useNum > 0) {
                this.useNum -= 1;
            }
        }
    }

    checkHpReduceSkill(hpRat: number) {

        switch (this.skillType) {

            case E_SkillType.Crazy:
                if (hpRat <= this.hpDeclinePercentage) {
                    // log("crazy crazy", this.buffJsonId)
                    this.checkMonsterBuff();
                    this.skillType = null;
                }
                break
            case E_SkillType.CreateMore:
                //log("CreateMore1", hpRat, this.hpDeclinePercentage)
                if (hpRat <= this.hpDeclinePercentage) {

                    this.skillType = null;
                    let createMoreFun = () => {
                        for (let i = 0; i < this.cloneNum; i++) {

                            let pos = Tool.getRandomPosInCircle(this.fromRole.data.pos, 100)
                            //log("衍生怪物：", this.cloneJsonId, this.fromRole.data.pos, pos)
                            xcore.event.raiseEvent(E_EVENT.CrateMoreMonster, { jsonId: this.cloneJsonId, pos: v2(pos.x, pos.y) })
                        }
                    }

                    let isSkillAnim = this.fromRole.checkSkillAnim(() => {
                        createMoreFun && createMoreFun()
                    });
                    if (this.hpDeclinePercentage <= 0) {
                        createMoreFun()
                        createMoreFun = null;
                    }
                    if (isSkillAnim) {

                        xcore.event.raiseEvent(E_EVENT.SkillEffect, {
                            pos0: this.fromRole.data.pos,
                            skill: this.skillType,
                            offsetpos: this.offsetPos || v3(),
                            // lightningTargets: this.lightningNum ? buffTargets : null,
                            animId01: this.animId01,

                        });

                    }
                    if (this.fromRole.data.type == E_RoleType.Monster) {
                        xcore.event.raiseEvent(E_EVENT.SkillTips, {
                            txt: this.skillname,
                            pos: this.fromRole.data.pos,
                        })

                    }

                }

                break;

        }

    }

    //技能升级
    levelUp(toLev: number = 1, skillType: E_SkillType) {
        this.init(this.fromRole, skillType, toLev);
        log('技能升级')

    }
    checkIfAbleUpLev(toLev: number) {
        return this.level < toLev
    }
    getLev() {
        return this.level
    }
    hide() {
        this.comp.node.active = false;
    }
    show() {
        this.comp.node.active = true;
    }
    onDestroy() {

        if (this.comp && this.comp.node && this.comp.node.isValid) {
            //this.comp.node.active = false;
            this.comp.node.destroy();
            this.comp = null;
            log('skill onDestroy')
        }
        this.isLongSkill = false;
    }

    destroy() {
        if (this.isDestroy) return
        this.isDestroy = true;
        this.onDestroy();
        this.fromRole.delectSkill(this);

    }

    tick(dt) {
        if (!this.nowSpeed) {
            return
        }
        this.tickTime += dt;

        if (this.tickTime >= this.nowSpeed) {
            //技能释放
            if (this.tickTime % this.nowSpeed <= dt) {
                if (this.isLongSkill || this.useNum > 0) {
                    this.releaseSkill();
                }

            }
        }
        //this.buffs.forEach(buff => buff.tick(dt));

    }
}