[1, ["8e2/RTk8lFwqBUpnjs3bc+@6c48a"], ["_textureSource"], ["cc.SpriteFrame"], 0, [{"name": "huodou-idle_04", "rect": {"x": 143, "y": 148, "width": 668, "height": 428}, "offset": {"x": 21.5, "y": -74}, "originalSize": {"width": 911, "height": 576}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-334, -214, 0, 334, -214, 0, -334, 214, 0, 334, 214, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [143, 428, 811, 428, 143, 0, 811, 0], "nuv": [0.15697036223929747, 0, 0.8902305159165752, 0, 0.15697036223929747, 0.7430555555555556, 0.8902305159165752, 0.7430555555555556], "minPos": {"x": -334, "y": -214, "z": 0}, "maxPos": {"x": 334, "y": 214, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]]