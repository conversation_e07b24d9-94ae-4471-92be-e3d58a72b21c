import { _decorator, Component, Label, Node, size, Sprite } from 'cc';
import { ConfigHelper } from 'db://assets/scripts/config/ConfigHelper';
import { xcore } from 'db://assets/scripts/libs/xcore';
const { ccclass, property } = _decorator;

@ccclass('UnitDimond')
export class UnitDimond extends Component {

    @property(Label)
    private lbName: Label = null;
    @property(Label)
    private lbNum: Label = null;
    @property(Sprite)
    private sprIcon: Sprite = null;

    setData(data, num) {
        this.lbName.string = data.tips;
        this.lbNum.string = '+' + num.toString();
        let config = ConfigHelper.getInstance().getBeadConfigById(data.skinFragmentId);
        let path = `./res/image/${config.path}/${config.picture}`;
        xcore.res.remoteLoadSprite(path, this.sprIcon, size(90, 90))
    }
}


