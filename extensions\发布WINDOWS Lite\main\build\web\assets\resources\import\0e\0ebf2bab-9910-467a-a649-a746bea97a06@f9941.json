[1, ["0evyurmRBGeqZJp0a+qXoG@6c48a"], ["_textureSource"], ["cc.SpriteFrame"], 0, [{"name": "huodou-idle_10", "rect": {"x": 145, "y": 119, "width": 611, "height": 457}, "offset": {"x": -5, "y": -59.5}, "originalSize": {"width": 911, "height": 576}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-305.5, -228.5, 0, 305.5, -228.5, 0, -305.5, 228.5, 0, 305.5, 228.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [145, 457, 756, 457, 145, 0, 756, 0], "nuv": [0.15916575192096596, 0, 0.8298572996706916, 0, 0.15916575192096596, 0.7934027777777778, 0.8298572996706916, 0.7934027777777778], "minPos": {"x": -305.5, "y": -228.5, "z": 0}, "maxPos": {"x": 305.5, "y": 228.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]]