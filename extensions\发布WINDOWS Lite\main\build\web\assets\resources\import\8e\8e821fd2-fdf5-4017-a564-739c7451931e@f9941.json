[1, ["8egh/S/fVAF6Vkc5x0UZMe@6c48a"], ["_textureSource"], ["cc.SpriteFrame"], 0, [{"name": "huodou-idle_17", "rect": {"x": 144, "y": 160, "width": 633, "height": 416}, "offset": {"x": 5, "y": -80}, "originalSize": {"width": 911, "height": 576}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-316.5, -208, 0, 316.5, -208, 0, -316.5, 208, 0, 316.5, 208, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [144, 416, 777, 416, 144, 0, 777, 0], "nuv": [0.15806805708013172, 0, 0.8529088913282108, 0, 0.15806805708013172, 0.7222222222222222, 0.8529088913282108, 0.7222222222222222], "minPos": {"x": -316.5, "y": -208, "z": 0}, "maxPos": {"x": 316.5, "y": 208, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]]