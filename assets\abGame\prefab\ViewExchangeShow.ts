import { _decorator, Component, Label, Node, size, Sprite } from 'cc';
import { ViewBase } from '../../scripts/libs/manager/ViewBase';
import { ConfigHelper } from '../../scripts/config/ConfigHelper';
import { xcore } from '../../scripts/libs/xcore';
const { ccclass, property } = _decorator;

@ccclass('ViewExchangeShow')
export class ViewExchangeShow extends ViewBase {

    @property(Sprite)
    private sprIcon: Sprite = null;

    @property(Label)
    private lbName: Label = null;

    @property(Label)
    private lbProp: Label = null;

    public setData(config: any): void {
        this.lbName.string = config.name;
        let debrisConfig = ConfigHelper.getInstance().getDebriConfigByJsonId(config.skinFragmentId);
        let path = `./res/image/${debrisConfig.path}/${debrisConfig.icon}`;
        xcore.res.remoteLoadSprite(path, this.sprIcon, size(424, 600));
    }
}


