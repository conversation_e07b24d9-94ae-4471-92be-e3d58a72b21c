{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "3944ae8f-8831-49f1-868e-ed5cf9598e1e", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "3944ae8f-8831-49f1-868e-ed5cf9598e1e@6c48a", "displayName": "tianpeng-attack_17", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "3944ae8f-8831-49f1-868e-ed5cf9598e1e", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "3944ae8f-8831-49f1-868e-ed5cf9598e1e@f9941", "displayName": "tianpeng-attack_17", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": -14.5, "offsetY": -15, "trimX": 42, "trimY": 47, "width": 137, "height": 136, "rawWidth": 250, "rawHeight": 200, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-68.5, -68, 0, 68.5, -68, 0, -68.5, 68, 0, 68.5, 68, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [42, 153, 179, 153, 42, 17, 179, 17], "nuv": [0.168, 0.085, 0.716, 0.085, 0.168, 0.765, 0.716, 0.765], "minPos": [-68.5, -68, 0], "maxPos": [68.5, 68, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "3944ae8f-8831-49f1-868e-ed5cf9598e1e@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "3944ae8f-8831-49f1-868e-ed5cf9598e1e@6c48a"}}