[1, ["05mlRcejlEB4FcLXuy50Lo@6c48a"], ["_textureSource"], ["cc.SpriteFrame"], 0, [{"name": "baize-idle_13", "rect": {"x": 172, "y": 0, "width": 551, "height": 537}, "offset": {"x": 27, "y": 30.5}, "originalSize": {"width": 841, "height": 598}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-275.5, -268.5, 0, 275.5, -268.5, 0, -275.5, 268.5, 0, 275.5, 268.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [172, 598, 723, 598, 172, 61, 723, 61], "nuv": [0.20451843043995244, 0.1020066889632107, 0.8596908442330559, 0.1020066889632107, 0.20451843043995244, 1, 0.8596908442330559, 1], "minPos": {"x": -275.5, "y": -268.5, "z": 0}, "maxPos": {"x": 275.5, "y": 268.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]]