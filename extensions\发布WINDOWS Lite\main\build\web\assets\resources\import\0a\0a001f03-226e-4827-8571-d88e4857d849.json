[1, 0, 0, [["cc.Json<PERSON>set", ["_name", "json"], 1]], [[0, 0, 1, 3]], [[0, "animation", [{"jsonId": "340001", "sample": 7, "duration": 1, "speed": 1, "wrapMode": 1, "path": "atkeffect", "name": "1", "target": 2, "XAxisOffset": "", "YAxisOffset": "", "notes": "160001通用刀光"}, {"jsonId": "340002", "sample": 7, "duration": 1, "speed": 1, "wrapMode": 1, "path": "atkeffect", "name": "2", "target": 2, "XAxisOffset": "", "YAxisOffset": "", "notes": "160002蓝色刀光"}, {"jsonId": "340003", "sample": 10, "duration": 1, "speed": 1, "wrapMode": 1, "path": "atkeffect", "name": "3", "target": 2, "XAxisOffset": "", "YAxisOffset": "", "notes": "160003紫色刀光"}, {"jsonId": "340004", "sample": 10, "duration": 1, "speed": 1, "wrapMode": 1, "path": "atkeffect", "name": "4", "target": 2, "XAxisOffset": "", "YAxisOffset": "", "notes": "160004红色刀光"}, {"jsonId": "340005", "sample": 10, "duration": 1, "speed": 1, "wrapMode": 1, "path": "atkeffect", "name": "5", "target": 2, "XAxisOffset": "", "YAxisOffset": "", "notes": "160005横向红色刀光"}, {"jsonId": "340006", "sample": 11, "duration": 1, "speed": 1, "wrapMode": 2, "path": "atkeffect", "name": "6", "target": 3, "XAxisOffset": "", "YAxisOffset": "", "notes": "160006火红火球"}, {"jsonId": "340007", "sample": 7, "duration": 1, "speed": 1, "wrapMode": 2, "path": "atkeffect", "name": "7", "target": 3, "XAxisOffset": "-50", "YAxisOffset": "", "notes": "160007蓝色光球"}, {"jsonId": "340008", "sample": 1, "duration": 1, "speed": 1, "wrapMode": 2, "path": "atkeffect", "name": "yumao", "target": 3, "XAxisOffset": "", "YAxisOffset": "", "notes": "羽毛攻击"}, {"jsonId": "340101", "sample": 5, "duration": 1, "speed": 1, "wrapMode": 2, "path": "atkeffect", "name": "25", "target": 2, "XAxisOffset": "", "YAxisOffset": "", "notes": "火球受击"}, {"jsonId": "340102", "sample": 8, "duration": 1, "speed": 1, "wrapMode": 2, "path": "atkeffect", "name": "26", "target": 2, "XAxisOffset": "", "YAxisOffset": "", "notes": "蓝色光球受击"}, {"jsonId": "340201", "sample": 12, "duration": 1, "speed": 1, "wrapMode": 1, "path": "skilleffect", "name": "20", "target": 1, "XAxisOffset": "", "YAxisOffset": "", "notes": "大鹏加速光环"}, {"jsonId": "340202", "sample": 15, "duration": 1, "speed": 1, "wrapMode": 1, "path": "skilleffect", "name": "41", "target": 1, "XAxisOffset": "", "YAxisOffset": "", "notes": "孙悟空迟缓术"}, {"jsonId": "340203", "sample": 19, "duration": 1, "speed": 1, "wrapMode": 1, "path": "skilleffect", "name": "38", "target": 1, "XAxisOffset": "", "YAxisOffset": "", "notes": "牛魔王踩地板"}, {"jsonId": "340301", "sample": 10, "duration": 1, "speed": 1, "wrapMode": 2, "path": "skilleffect", "name": "31", "target": 3, "XAxisOffset": "-100", "YAxisOffset": "", "notes": "点赞小飞剑"}, {"jsonId": "340302", "sample": 5, "duration": 1, "speed": 1, "wrapMode": 1, "path": "skilleffect", "name": "44", "target": 2, "XAxisOffset": "", "YAxisOffset": "", "notes": "点赞小飞剑受击"}, {"jsonId": "340303", "sample": 12, "duration": 1, "speed": 1, "wrapMode": 2, "path": "skilleffect", "name": "30", "target": 3, "XAxisOffset": "", "YAxisOffset": "", "notes": "点赞大飞剑"}, {"jsonId": "340304", "sample": 9, "duration": 1, "speed": 1, "wrapMode": 1, "path": "skilleffect", "name": "35", "target": 2, "XAxisOffset": "", "YAxisOffset": "", "notes": "点赞大飞剑受击"}, {"jsonId": "340305", "sample": 14, "duration": 1, "speed": 1, "wrapMode": 2, "path": "skilleffect", "name": "36", "target": 3, "XAxisOffset": "", "YAxisOffset": "", "notes": "礼物天火技能"}, {"jsonId": "340306", "sample": 13, "duration": 1, "speed": 1, "wrapMode": 1, "path": "skilleffect", "name": "40", "target": 2, "XAxisOffset": "", "YAxisOffset": "", "notes": "礼物天火技能命中"}, {"jsonId": "340307", "sample": 12, "duration": 1, "speed": 1, "wrapMode": 1, "path": "skilleffect", "name": "37", "target": 2, "XAxisOffset": "", "YAxisOffset": "", "notes": "礼物飞沙技能"}, {"jsonId": "340308", "sample": 20, "duration": 1, "speed": 1, "wrapMode": 1, "path": "skilleffect", "name": "39", "target": 2, "XAxisOffset": "", "YAxisOffset": "160", "notes": "礼物三味真火技能"}, {"jsonId": "340309", "sample": 7, "duration": 1, "speed": 1, "wrapMode": 2, "path": "skilleffect", "name": "34", "target": 3, "XAxisOffset": "-150", "YAxisOffset": "", "notes": "礼物技能天外陨石"}, {"jsonId": "340310", "sample": 18, "duration": 1, "speed": 1, "wrapMode": 1, "path": "skilleffect", "name": "33", "target": 2, "XAxisOffset": "", "YAxisOffset": "20", "notes": "礼物技能天外陨石受击"}, {"jsonId": "340311", "sample": 18, "duration": 1, "speed": 1, "wrapMode": 1, "path": "skilleffect", "name": "42", "target": 2, "XAxisOffset": "", "YAxisOffset": "", "notes": "闪电动画"}, {"jsonId": "340312", "sample": 12, "duration": 1, "speed": 1, "wrapMode": 1, "path": "skilleffect", "name": "43", "target": 3, "XAxisOffset": "", "YAxisOffset": "", "notes": "飞沙走石弹道"}, {"jsonId": "340401", "sample": 7, "duration": 1, "speed": 1, "wrapMode": 1, "path": "<PERSON><PERSON><PERSON>", "name": "bianfujing-attack", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "蝙蝠怪攻击"}, {"jsonId": "340402", "sample": 8, "duration": 1, "speed": 1, "wrapMode": 1, "path": "<PERSON><PERSON><PERSON>", "name": "xiaozuanfeng-attack", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "小钻风"}, {"jsonId": "340403", "sample": 7, "duration": 1, "speed": 1, "wrapMode": 1, "path": "<PERSON><PERSON><PERSON>", "name": "lingligui-attack", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "伶俐鬼"}, {"jsonId": "340404", "sample": 7, "duration": 1, "speed": 1, "wrapMode": 1, "path": "<PERSON><PERSON><PERSON>", "name": "zhuyao-attack", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "野猪精"}, {"jsonId": "340405", "sample": 6, "duration": 1, "speed": 1, "wrapMode": 1, "path": "<PERSON><PERSON><PERSON>", "name": "dapeng-attack", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "大鹏"}, {"jsonId": "340406", "sample": 8, "duration": 1, "speed": 1, "wrapMode": 1, "path": "<PERSON><PERSON><PERSON>", "name": "jurenyuan-attack", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "巨力猿"}, {"jsonId": "340407", "sample": 8, "duration": 1, "speed": 1, "wrapMode": 1, "path": "<PERSON><PERSON><PERSON>", "name": "xiniujing-attack", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "犀牛精"}, {"jsonId": "340408", "sample": 7, "duration": 1, "speed": 1, "wrapMode": 1, "path": "<PERSON><PERSON><PERSON>", "name": "bian<PERSON>wang-attack", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "蝙蝠王"}, {"jsonId": "340409", "sample": 6, "duration": 1, "speed": 1, "wrapMode": 1, "path": "<PERSON><PERSON><PERSON>", "name": "zongzuanfeng-attack", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "总钻风"}, {"jsonId": "340410", "sample": 7, "duration": 1, "speed": 1, "wrapMode": 1, "path": "<PERSON><PERSON><PERSON>", "name": "honghaier-attack", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "红孩儿"}, {"jsonId": "340411", "sample": 8, "duration": 1, "speed": 1, "wrapMode": 1, "path": "<PERSON><PERSON><PERSON>", "name": "heixiongjing-attack", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "黑熊精"}, {"jsonId": "340412", "sample": 7, "duration": 1, "speed": 1, "wrapMode": 1, "path": "<PERSON><PERSON><PERSON>", "name": "jinchidapeng-attack", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "金翅大鹏"}, {"jsonId": "340413", "sample": 9, "duration": 1, "speed": 1, "wrapMode": 1, "path": "<PERSON><PERSON><PERSON>", "name": "sunwukong-attack", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "孙悟空"}, {"jsonId": "340414", "sample": 7, "duration": 1, "speed": 1, "wrapMode": 1, "path": "<PERSON><PERSON><PERSON>", "name": "niumowang-attack", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "牛魔王"}, {"jsonId": "340501", "sample": 5, "duration": 1, "speed": 1, "wrapMode": 2, "path": "<PERSON><PERSON><PERSON>", "name": "bianfujing-move", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "蝙蝠怪移动"}, {"jsonId": "340502", "sample": 8, "duration": 1, "speed": 1, "wrapMode": 2, "path": "<PERSON><PERSON><PERSON>", "name": "xia<PERSON><PERSON><PERSON>-move", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "小钻风"}, {"jsonId": "340503", "sample": 7, "duration": 1, "speed": 1, "wrapMode": 2, "path": "<PERSON><PERSON><PERSON>", "name": "lingligui-move", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "伶俐鬼"}, {"jsonId": "340504", "sample": 8, "duration": 1, "speed": 1, "wrapMode": 2, "path": "<PERSON><PERSON><PERSON>", "name": "zhuyao-move", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "野猪精"}, {"jsonId": "340505", "sample": 7, "duration": 1, "speed": 1, "wrapMode": 2, "path": "<PERSON><PERSON><PERSON>", "name": "dapeng-move", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "大鹏"}, {"jsonId": "340506", "sample": 8, "duration": 1, "speed": 1, "wrapMode": 2, "path": "<PERSON><PERSON><PERSON>", "name": "juren<PERSON>-move", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "巨力猿"}, {"jsonId": "340507", "sample": 8, "duration": 1, "speed": 1, "wrapMode": 2, "path": "<PERSON><PERSON><PERSON>", "name": "xiniujing-move", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "犀牛精"}, {"jsonId": "340508", "sample": 5, "duration": 1, "speed": 1, "wrapMode": 2, "path": "<PERSON><PERSON><PERSON>", "name": "bian<PERSON>wang-move", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "蝙蝠王"}, {"jsonId": "340509", "sample": 6, "duration": 1, "speed": 1, "wrapMode": 2, "path": "<PERSON><PERSON><PERSON>", "name": "zongzuanfeng-move", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "总钻风"}, {"jsonId": "340510", "sample": 8, "duration": 1, "speed": 1, "wrapMode": 2, "path": "<PERSON><PERSON><PERSON>", "name": "honghaier-move", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "红孩儿"}, {"jsonId": "340511", "sample": 8, "duration": 1, "speed": 1, "wrapMode": 2, "path": "<PERSON><PERSON><PERSON>", "name": "heixiongjing-move", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "黑熊精"}, {"jsonId": "340512", "sample": 7, "duration": 1, "speed": 1, "wrapMode": 2, "path": "<PERSON><PERSON><PERSON>", "name": "jinchidapeng-move", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "金翅大鹏"}, {"jsonId": "340513", "sample": 8, "duration": 1, "speed": 1, "wrapMode": 2, "path": "<PERSON><PERSON><PERSON>", "name": "sunwukong-move", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "孙悟空"}, {"jsonId": "340514", "sample": 8, "duration": 1, "speed": 1, "wrapMode": 2, "path": "<PERSON><PERSON><PERSON>", "name": "niumowang-move", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "牛魔王"}, {"jsonId": "340613", "sample": 20, "duration": 1, "speed": 1, "wrapMode": 1, "path": "<PERSON><PERSON><PERSON>", "name": "sunwukong-skill", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "孙悟空技能动画"}, {"jsonId": "340614", "sample": 16, "duration": 1, "speed": 1, "wrapMode": 1, "path": "<PERSON><PERSON><PERSON>", "name": "niumowang-skill", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "牛魔王技能动画"}, {"jsonId": "340701", "sample": 6, "duration": 1, "speed": 1, "wrapMode": 1, "path": "<PERSON><PERSON><PERSON>", "name": "bin01-attack", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "天兵01攻击"}, {"jsonId": "340702", "sample": 6, "duration": 1, "speed": 1, "wrapMode": 1, "path": "<PERSON><PERSON><PERSON>", "name": "bin02-attack", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "天兵02攻击"}, {"jsonId": "340703", "sample": 6, "duration": 1, "speed": 1, "wrapMode": 1, "path": "<PERSON><PERSON><PERSON>", "name": "bin03-attack", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "天兵03攻击"}, {"jsonId": "340704", "sample": 6, "duration": 1, "speed": 1, "wrapMode": 1, "path": "<PERSON><PERSON><PERSON>", "name": "bin04-attack", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "天兵04攻击"}, {"jsonId": "340705", "sample": 7, "duration": 1, "speed": 1, "wrapMode": 1, "path": "<PERSON><PERSON><PERSON>", "name": "bin05-attack", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "天兵05攻击"}, {"jsonId": "340706", "sample": 10, "duration": 1, "speed": 1, "wrapMode": 1, "path": "<PERSON><PERSON><PERSON>", "name": "bin06-attack", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "天兵06攻击"}, {"jsonId": "340707", "sample": 10, "duration": 1, "speed": 1, "wrapMode": 1, "path": "<PERSON><PERSON><PERSON>", "name": "bin07-attack", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "天兵07攻击"}, {"jsonId": "340801", "sample": 7, "duration": 1, "speed": 1, "wrapMode": 2, "path": "<PERSON><PERSON><PERSON>", "name": "bin01-move", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "天兵01移动"}, {"jsonId": "340802", "sample": 7, "duration": 1, "speed": 1, "wrapMode": 2, "path": "<PERSON><PERSON><PERSON>", "name": "bin02-move", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "天兵02移动"}, {"jsonId": "340803", "sample": 7, "duration": 1, "speed": 1, "wrapMode": 2, "path": "<PERSON><PERSON><PERSON>", "name": "bin03-move", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "天兵03移动"}, {"jsonId": "340804", "sample": 7, "duration": 1, "speed": 1, "wrapMode": 2, "path": "<PERSON><PERSON><PERSON>", "name": "bin04-move", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "天兵04移动"}, {"jsonId": "340805", "sample": 8, "duration": 1, "speed": 1, "wrapMode": 2, "path": "<PERSON><PERSON><PERSON>", "name": "bin05-move", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "天兵05移动"}, {"jsonId": "340806", "sample": 10, "duration": 1, "speed": 1, "wrapMode": 2, "path": "<PERSON><PERSON><PERSON>", "name": "bin06-move", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "天兵06移动"}, {"jsonId": "340807", "sample": 6, "duration": 1, "speed": 1, "wrapMode": 2, "path": "<PERSON><PERSON><PERSON>", "name": "bin07-move", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "天兵07移动"}]]], 0, 0, [], [], []]