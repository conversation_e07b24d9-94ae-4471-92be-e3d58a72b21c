import { _decorator, Button, Component, Label, Node, size, Sprite } from 'cc';
import { ViewBase } from '../../scripts/libs/manager/ViewBase';
import { xcore } from '../../scripts/libs/xcore';
import { C_View, E_EVENT } from '../../scripts/ConstGlobal';

import TimeUtil from '../../scripts/libs/utils/TimeUtil';
import Tool from '../../scripts/libs/utils/Tool';
const { ccclass, property } = _decorator;

@ccclass('ViewGameOver')
export class ViewGameOver extends ViewBase {

    @property(Label)
    private lbDesc: Label = null;

    @property(Button)
    private btnCheckRank: Button = null;

    @property(Label)
    private lbBtnTxt: Label = null;

    //title
    @property(Node)
    private ndTitleCross: Node = null;

    @property(Node)
    private ndTitleWin: Node = null;

    @property(Node)
    private ndTitleFail: Node = null;


    //
    @property(Node)
    private ndFightFirst: Node = null;

    @property(Sprite)
    private sprAvatarFightFirst: Sprite = null;

    @property(Label)
    private lbNameFightFirst: Label = null;


    @property(Node)
    private ndMostKillBoss: Node = null;

    @property(Sprite)
    private sprAvatarMostKillBoss: Sprite = null;

    @property(Label)
    private lbNameMostKillBoss: Label = null;

    @property(Node)
    private ndMostKillMonster: Node = null;

    @property(Sprite)
    private sprAvatarMostKillMonster: Sprite = null;

    @property(Label)
    private lbNameMostKillMonster: Label = null;

    @property(Node)
    private ndMostHelpful: Node = null;

    @property(Sprite)
    private sprAvatarMostHelpful: Sprite = null;

    @property(Label)
    private lbNameMostHelpful: Label = null;

    @property(Node)
    private ndTime: Node = null;

    @property(Label)
    private lbTime: Label = null;

    @property(Node)
    private ndUserNum: Node = null;

    @property(Label)
    private lbUserNum: Label = null;

    private _data: any
    private _time: number = 11;



    public async setData(data: any) {


       // console.log('结算数据:', data)
        this._data = data;
        this.ndTitleCross.active = data.isCross;
        this.ndTitleWin.active = data.isWin && !data.isCross;
        this.ndTitleFail.active = !data.isWin;

        if (data.firstFightUser) {
            this.lbNameFightFirst.string = Tool.sub(data.firstFightUser.nickName || data.firstFightUser.userId, 10, true);
            xcore.res.remoteLoadSprite(data.firstFightUser.iconUrl, this.sprAvatarFightFirst, size(106, 71060));
        }


        if (data.firstKillBossUser) {
            this.lbNameMostKillBoss.string = Tool.sub(data.firstKillBossUser.nickName || data.firstKillBossUser.userId, 10, true);
            xcore.res.remoteLoadSprite(data.firstKillBossUser.iconUrl, this.sprAvatarMostKillBoss, size(106, 106));
        }


        if (data.firstKillMonsterUser) {
            this.lbNameMostKillMonster.string = Tool.sub(data.firstKillMonsterUser.nickName || data.firstKillMonsterUser.userId, 10, true);
            xcore.res.remoteLoadSprite(data.firstKillMonsterUser.iconUrl, this.sprAvatarMostKillMonster, size(106, 106));
        }


        if (data.firstHelpTowerUser) {
            this.lbNameMostHelpful.string = Tool.sub(data.firstHelpTowerUser.nickName || data.firstHelpTowerUser.userId, 10, true);
            xcore.res.remoteLoadSprite(data.firstHelpTowerUser.iconUrl, this.sprAvatarMostHelpful, size(106, 106));
        }

        this.ndFightFirst.active = !!data.firstFightUser;
        this.ndMostKillBoss.active = !!data.firstKillBossUser;
        this.ndMostKillMonster.active = !!data.firstKillMonsterUser;
        this.ndMostHelpful.active = !!data.firstHelpTowerUser;
        this.ndTime.active = !!data.corssTime;
        this.ndUserNum.active = !!data.totalUserNum;

        this.lbTime.string = TimeUtil.formatTime(Math.floor(data.corssTime) * 1000, 2);
        this.lbUserNum.string = data.totalUserNum;



        this.btnCheckRank.node.on('click', () => {
            xcore.ui.addView(C_View.ViewFightRank, this._data);
            //this.btnCheckRank.interactable = false;
            this.closeSelf();


        }, this)
        // let time = TimeUtil.getLastDayByIndex(TimeUtil.formatTimestampToDate(new Date().getTime(), '-'), 6);
        // let key = "rank:" + xcore.gameData.appId + ":" + xcore.gameData.channelId + ":week:" + time;
        // let key2 = "rank:" + xcore.gameData.appId + ":" + xcore.gameData.channelId + ":level:week:" + time;
        // Net.getRankInfo(key, 0, 9);
        // Net.getRankInfo(key2, 0, 9);
    }

    update(deltaTime: number) {
        if (this._time > 0) {
            this._time -= deltaTime;
            this.lbBtnTxt.string = `查看排行榜(${Math.floor(this._time)})`
            if (this._time <= 0) {
                this.lbBtnTxt.string = `查看排行榜`
                this.btnCheckRank.interactable = false;
                xcore.ui.addView(C_View.ViewFightRank, this._data);
                this.closeSelf();
            }
        }

    }
}


