import { _decorator, Component, Label, log, Node, size, Sprite, v3, Vec3 } from 'cc';
import { xcore } from '../../../scripts/libs/xcore';
import Tool from 'db://assets/scripts/libs/utils/Tool';
import { StringUtil } from 'db://assets/scripts/libs/utils/StringUtil';
import { ConfigHelper } from 'db://assets/scripts/config/ConfigHelper';
import { C_View } from 'db://assets/scripts/ConstGlobal';
const { ccclass, property } = _decorator;
@ccclass('UnitRank')
export class UnitRank extends Component {
    @property(Sprite)
    private sprAvatar: Sprite = null;

    @property(Sprite)
    private sprRankNum: Sprite = null;


    @property(Label)
    private lbRankNum: Label = null;

    @property(Label)
    private lbName: Label = null;

    @property(Label)
    private lbScore: Label = null;

    @property(Label)
    private lbGiftTitle: Label = null;

    @property(Node)
    private ndWing: Node = null;

    @property(Sprite)
    private sprWing: Sprite = null;

    @property(Label)
    private lbWing: Label = null;

    private _imgUrl: string

    private _imgFunc: Function

    private _wingName: string;
    private _wingImg: string;
    private _wingProp: string;

    private _tempVec1: Vec3 = v3(110, 0, 0)
    private _tempVec2: Vec3 = v3(290, 0, 0)


    protected onLoad(): void {
        this.ndWing.on('click', () => {
            if (this._wingImg) {
                xcore.ui.addView(C_View.ViewWingShow, { img: this._wingImg, name: this._wingName, prop: this._wingProp, })
            }
        }, this)
    }



    setData(data: any, type) {
        if (!data) {
            this.node.active = false;
            return
        }
        this.node.active = true;
        this.lbRankNum.string = data.rank;
        this.lbName.string = StringUtil.sub(data.nickName || '匿名用户', 10, true);
        this.lbScore.string = data.score;
        this._imgUrl = data.avatarUrl;
        if (!this._imgFunc) {
            this._imgFunc = Tool.debounce(this.refreshImg, 60, false);
        }
        this.lbGiftTitle.string = ''
        this._imgFunc();
        /*  this.lbName.string = nickName;
         xcore.res.remoteLoadSprite(iconUrl, this.sprAvatar, size(70, 70));
         this.lbRankNum.string = `${rank + 1}`;
         this.lbScore.string = data.score; */
        this._wingImg = null;
        if (type == 0) {
            let wingConfig = ConfigHelper.getInstance().getWingConfigByRankIndex(data.rank);
            if (wingConfig) {
                let wingSkinConfig = ConfigHelper.getInstance().getWingSkinConfigByRankIndex(data.rank);
                this._wingImg = `./res/image/${wingConfig.path}/${wingConfig.picture}`;
                this._wingName = wingConfig.name;
                this._wingProp = wingSkinConfig.skillDescribe;
                this.lbWing.string = this._wingName;
                this.ndWing.active = true;
                log("wingConfig", wingConfig);
                xcore.res.bundleLoadSprite('resources', this._wingImg, this.sprWing, size(68, 68));
            } else {
                this.ndWing.active = false;
            }
            this.lbScore.node.setPosition(this._tempVec1);
        } else {
            this.lbScore.node.setPosition(this._tempVec2);
            this.ndWing.active = false;
            if (type == 3) {
                let config = ConfigHelper.getInstance().getGiftRankTitleByScore(data.score);
                if (config) {
                    this.lbGiftTitle.string = `${config.giftLevel}\n${config.jsonId}`;
                }

            }
        }

    }

    refreshImg() {
        xcore.res.remoteLoadSprite(this._imgUrl, this.sprAvatar, size(70, 70));
    }
}


