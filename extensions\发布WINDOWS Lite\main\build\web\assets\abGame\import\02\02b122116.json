[1, ["a324rJxlxLYb6QhBlA1fvE@f9941", "73UARKop9LxLOrdW82Jd8w@f9941", "52fHu7D8hGm5vLaoALoXCl", "d1fi5FTrdHzpp/Gh2Ms3Gt@f9941", "1bj4StaSZEFKamgUvmHKn1@f9941", "ff3reVIcZLdo1ocsK9Lga3@f9941", "9bBYHzhH5F25cHFMhUL3oq@f9941", "2f2O7wuLdF87A/uXBS6oHb@f9941", "d6DSoWSetL0rN2ZvPbA/Z5@f9941", "96ZqriH7hJVYiIY+5Sr5Tr@f9941", "b1ZASwemlIMJsWX0Q4vCi9@f9941", "efTwNrnMdJOIahOnWe8oLI@f9941", "a2WMIAjDpHDJHfwAyHw1LY@f9941", "4a4qNwzy1MkJ2/lkjqHHqf@f9941", "d9lUEGBZVD8YaCFFOtdRUo@f9941", "2f2O7wuLdF87A/uXBS6oHb@6c48a", "96ZqriH7hJVYiIY+5Sr5Tr@6c48a", "b1ZASwemlIMJsWX0Q4vCi9@6c48a", "d6DSoWSetL0rN2ZvPbA/Z5@6c48a", "efTwNrnMdJOIahOnWe8oLI@6c48a"], ["node", "_spriteFrame", "_textureSource", "_font", "root", "lbName", "spr<PERSON><PERSON><PERSON>", "lbPower", "lbScore", "sprRank", "lbRank", "data"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_layer", "_components", "_prefab", "_parent", "_children", "_lpos"], 1, 9, 4, 1, 2, 5], ["cc.Label", ["_string", "_actualFontSize", "_lineHeight", "_isSystemFontUsed", "_fontSize", "node", "__prefab", "_color"], -2, 1, 4, 5], ["cc.Node", ["_name", "_layer", "_parent", "_components", "_prefab", "_lpos", "_lscale"], 1, 1, 12, 4, 5, 5], ["cc.Sprite", ["node", "__prefab", "_spriteFrame"], 3, 1, 4, 6], ["cc.Prefab", ["_name"], 2], ["cc.UITransform", ["node", "__prefab", "_contentSize"], 3, 1, 4, 5], ["cc.CompPrefabInfo", ["fileId"], 2], ["be5f5GnY21C/LoOdSTYtvfx", ["node", "__prefab", "lbRank", "sprRank", "lbScore", "lbPower", "spr<PERSON><PERSON><PERSON>", "lbName", "sfRanks", "ndSkills", "lbSkillNums"], 3, 1, 4, 1, 1, 1, 1, 1, 1, 3, 2, 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.Layout", ["_resizeMode", "_layoutType", "_spacingX", "node", "__prefab"], 0, 1, 4], ["cc.Mask", ["_type", "node", "__prefab"], 2, 1, 4], ["cc.Graphics", ["node", "__prefab", "_fillColor"], 3, 1, 4, 5]], [[7, 0, 2], [6, 0, 1, 2, 1], [9, 0, 1, 2, 3, 4, 5, 5], [4, 0, 1, 2, 1], [3, 0, 1, 2, 3, 4, 5, 6, 3], [1, 0, 1, 4, 2, 3, 6, 3], [1, 0, 1, 4, 5, 2, 3, 6, 3], [1, 0, 1, 4, 2, 3, 3], [2, 0, 1, 4, 5, 6, 4], [3, 0, 1, 2, 3, 4, 5, 3], [4, 0, 1, 1], [2, 0, 1, 2, 3, 5, 6, 7, 5], [5, 0, 2], [1, 0, 1, 5, 2, 3, 6, 3], [1, 0, 1, 4, 5, 2, 3, 3], [8, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 1], [10, 0, 1, 2, 3, 4, 4], [11, 0, 1, 2, 2], [12, 0, 1, 2, 1], [2, 0, 1, 4, 2, 3, 5, 6, 7, 6], [2, 0, 1, 2, 3, 5, 6, 5]], [[[[12, "UnitPowerRank"], [13, "UnitPowerRank", 33554432, [-20, -21, -22, -23, -24, -25, -26, -27, -28, -29], [[1, -2, [0, "52mWev4s1Im5tKIOYLu+lr"], [5, 800, 100]], [15, -19, [0, "9dG9hDEgFKj7TSsonA6t6A"], -18, -17, -16, -15, -14, -13, [18, 19, 20, 21], [-8, -9, -10, -11, -12], [-3, -4, -5, -6, -7]]], [2, "5en/MOef5MqpC3T8o943n2", null, null, null, -1, 0], [1, 0, -50, 0]], [6, "ndSkill", 33554432, 1, [-32, -33, -34, -35, -36], [[1, -30, [0, "cabYkFAK1KSLkLQ20+qWPr"], [5, 264, 100]], [16, 1, 1, 6, -31, [0, "4c3GC37EdML4QDhFgZR10z"]]], [2, "fbS0VHkiVNPb/tG+6ADkcY", null, null, null, 1, 0], [1, 256.476, 0, 0]], [6, "ndSkill01", 33554432, 2, [-39, -40, -41], [[1, -37, [0, "ff+aHLFKpJfpeFUTVIpsJU"], [5, 48, 48]], [3, -38, [0, "9eqOhYDWJHu5eb+EjfJhvb"], 5]], [2, "55pZg+M2JKsqYsdAFoA4OH", null, null, null, 1, 0], [1, -108, 0, 0]], [6, "ndSkill02", 33554432, 2, [-44, -45, -46], [[1, -42, [0, "58yzQiby5Cd64veOuw3mUp"], [5, 48, 48]], [3, -43, [0, "5b83VhOLJJLZY8AFuZ88Yw"], 8]], [2, "db8y+V93ZNErCJms6bCUDj", null, null, null, 1, 0], [1, -54, 0, 0]], [14, "ndSkill03", 33554432, 2, [-49, -50, -51], [[1, -47, [0, "20R0fHaAtOFqhapaYuANuG"], [5, 48, 48]], [3, -48, [0, "14nqZ7+URDwafdpkyb82je"], 11]], [2, "2aTdRiT/5La7bGRYNg2BJw", null, null, null, 1, 0]], [6, "ndSkill04", 33554432, 2, [-54, -55, -56], [[1, -52, [0, "3b/Pd5y/JKtJJD8twoqM5V"], [5, 48, 48]], [3, -53, [0, "55z9w7d2dLJ79Gm/rtVFFq"], 14]], [2, "5aQdE6HABKLbqxRj5mjGEY", null, null, null, 1, 0], [1, 54, 0, 0]], [6, "ndSkill05", 33554432, 2, [-59, -60, -61], [[1, -57, [0, "14xU4A3ddF+rQAB+TE5pRw"], [5, 48, 48]], [3, -58, [0, "abOaCJMVVC/pUOFtyb49Sd"], 17]], [2, "63v1KicTBIXqjxiQ5M7ayK", null, null, null, 1, 0], [1, 108, 0, 0]], [6, "Node", 33554432, 1, [-65], [[1, -62, [0, "abTEkIU2ZP9bWxVaIiZxUQ"], [5, 68, 68]], [17, 1, -63, [0, "96G2jAhVdKiqMDI6/VDEUI"]], [18, -64, [0, "32+VJhT3REoKU2QpMRC/Fm"], [4, 16777215]]], [2, "9dOrq/MqBMa5L6qsMTfQsR", null, null, null, 1, 0], [1, -218.437, 4.586999999999989, 0]], [7, "sprFrame", 33554432, 1, [[1, -66, [0, "81kk+cFMFB3oewNeONiWEX"], [5, 800, 96]], [3, -67, [0, "06dk9fEalLhbhC4QQyMIJV"], 0]], [2, "3dsSKVnfRHyL8ob36rpQ8n", null, null, null, 1, 0]], [5, "sprAFrame", 33554432, 1, [[1, -68, [0, "6b1wxhtv5KAJx6NKpJx8l0"], [5, 70, 70]], [3, -69, [0, "fdSUbg7/VAjaIYnF4h8Vo3"], 1]], [2, "27J4HMd85LoI/aGRBbNxa6", null, null, null, 1, 0], [1, -218.43200000000002, 5.4109999999999445, 0]], [9, "spr<PERSON><PERSON><PERSON>", 33554432, 8, [[[1, -70, [0, "b04OMosC9BtKS0FDJhOJnS"], [5, 70, 70]], -71], 4, 1], [2, "edMPzQUnJGXa9WvOOJ0tLl", null, null, null, 1, 0], [1, 0, 1.6889999999999645, 0]], [5, "sprMask", 33554432, 1, [[1, -72, [0, "a6anYrmWhG6bVJXTRAyKZ/"], [5, 70, 70]], [3, -73, [0, "73VMBnKaxLsLCMVsKzKLxd"], 2]], [2, "01i/JUhodFl5QSTuJ0WpKC", null, null, null, 1, 0], [1, -218.437, 6.275999999999954, 0]], [9, "sprRank", 33554432, 1, [[[1, -74, [0, "e1M4W0ONNDgJfcZhEd0TeH"], [5, 61, 61]], -75], 4, 1], [2, "ad/LqHBMJMPa+eUxFT4/pU", null, null, null, 1, 0], [1, -349.372, 0, 0]], [4, "lbRank", 33554432, 1, [[[1, -76, [0, "f0fXj4++ZHfa1MPIO1N0cn"], [5, 46, 63]], -77], 4, 1], [2, "58hRiHyHdLG770a9sQOTTy", null, null, null, 1, 0], [1, -349.372, 0, 0], [1, 0.5, 0.5, 1]], [4, "lbName", 33554432, 1, [[[1, -78, [0, "3fhrQ44wNACIp3txJMijqJ"], [5, 120, 63]], -79], 4, 1], [2, "a3tlGKSOJBnbYSI3sIBBzo", null, null, null, 1, 0], [1, -220.21800000000002, -26.34699999999998, 0], [1, 0.5, 0.5, 1]], [4, "lbScore", 33554432, 1, [[[1, -80, [0, "09pMdY9/NKZZncErVUM0wk"], [5, 36.79998779296875, 63]], -81], 4, 1], [2, "95bMC0X/tBF5TiP41ovDv7", null, null, null, 1, 0], [1, -77.12200000000001, 0, 0], [1, 0.5, 0.5, 1]], [4, "lbPower", 33554432, 1, [[[1, -82, [0, "a9XIZE9NxN1KO6RrZxsbsN"], [5, 36.79998779296875, 63]], -83], 4, 1], [2, "8bFRoAW3hENq2VvoLGCCbL", null, null, null, 1, 0], [1, 31.621999999999957, 0, 0], [1, 0.5, 0.5, 1]], [5, "sprNum", 33554432, 3, [[1, -84, [0, "53UuZoNR9BNakIG+f57Mj5"], [5, 25, 25]], [3, -85, [0, "8eBn73bO9FBZS6nhX731N0"], 3]], [2, "21Q6QupbtKrZ9ZrxbIUjTG", null, null, null, 1, 0], [1, 16, 21.789999999999964, 0]], [4, "lbNum", 33554432, 3, [[[1, -86, [0, "356zsC8LBKeKdrsOzezQMo"], [5, 19.98046875, 50.4]], -87], 4, 1], [2, "3acEfkeelGZa5iDUjuyhKQ", null, null, null, 1, 0], [1, 15.634999999999991, 21.929999999999836, 0], [1, 0.5, 0.5, 1]], [7, "sprIcon", 33554432, 3, [[1, -88, [0, "17Laq/XVJCx7CDEFXHGXPx"], [5, 30, 31]], [3, -89, [0, "70QPP/0+9Py7nYGW5VfcGZ"], 4]], [2, "14gsh0+BZKNpDHhCAs0fxu", null, null, null, 1, 0]], [5, "sprNum", 33554432, 4, [[1, -90, [0, "94mmadA1ZOH4G6lHuiIaWo"], [5, 25, 25]], [3, -91, [0, "82h5SP7chH+4AmiPgq7lmo"], 6]], [2, "c9mUfYi8RFGaPibvX0OV8e", null, null, null, 1, 0], [1, 16, 21.789999999999964, 0]], [4, "lbNum", 33554432, 4, [[[1, -92, [0, "0dC2Q8wAxJJpDbzKuP1xAD"], [5, 19.98046875, 50.4]], -93], 4, 1], [2, "3fLHuQ08FDtIFQh1qJOEwH", null, null, null, 1, 0], [1, 15.634999999999991, 21.929999999999836, 0], [1, 0.5, 0.5, 1]], [7, "sprIcon", 33554432, 4, [[1, -94, [0, "4eSfgUqlRJbI31X5vxR6Li"], [5, 27, 27]], [3, -95, [0, "edXwyA7nRDi792jrx148rn"], 7]], [2, "b6Bl8hLkVE7p6ymmVJX7aB", null, null, null, 1, 0]], [5, "sprNum", 33554432, 5, [[1, -96, [0, "1733VVHG5GaJ/MhMGD2jCx"], [5, 25, 25]], [3, -97, [0, "3e8kM/pltPMpVjcxYVQP+9"], 9]], [2, "41YeWzFnFFpo82xmaxR3So", null, null, null, 1, 0], [1, 16, 21.789999999999964, 0]], [4, "lbNum", 33554432, 5, [[[1, -98, [0, "daH1s/ZjxPVbXhwAIR63PM"], [5, 19.98046875, 50.4]], -99], 4, 1], [2, "80eWsMsK1KGpeeQYgexf6+", null, null, null, 1, 0], [1, 15.634999999999991, 21.929999999999836, 0], [1, 0.5, 0.5, 1]], [7, "sprIcon", 33554432, 5, [[1, -100, [0, "08wTkUD45Fc4dcBCaJNUp5"], [5, 31, 31]], [3, -101, [0, "41zbjbx6RMFot1wqUrQmbW"], 10]], [2, "3960O6oqVAVYxQkLAwfmoS", null, null, null, 1, 0]], [5, "sprNum", 33554432, 6, [[1, -102, [0, "816dLej+5J1LCqMscp+MW1"], [5, 25, 25]], [3, -103, [0, "4eaw4eCZlORpETYJEBAWtj"], 12]], [2, "8fFPt2AytL6Lm9IdpDt8fh", null, null, null, 1, 0], [1, 16, 21.789999999999964, 0]], [4, "lbNum", 33554432, 6, [[[1, -104, [0, "25tblvjYhFvIXtyPDhtGZk"], [5, 19.98046875, 50.4]], -105], 4, 1], [2, "78yiQQwkxMO7Fgs5XOk3ee", null, null, null, 1, 0], [1, 15.634999999999991, 21.929999999999836, 0], [1, 0.5, 0.5, 1]], [7, "sprIcon", 33554432, 6, [[1, -106, [0, "bd2yuVejlIpquDpIShA/pr"], [5, 32, 30]], [3, -107, [0, "2fr7McEdZNyb/gnHXDaPh9"], 13]], [2, "edYp4P9rtNj5W6VwkO7wjA", null, null, null, 1, 0]], [5, "sprNum", 33554432, 7, [[1, -108, [0, "ff2v4jU+5PLaZuy+fO9DEk"], [5, 25, 25]], [3, -109, [0, "769a34xEtHWYg23Y+7QkU4"], 15]], [2, "1aNEMZO5ZII7vSgWW8hGE4", null, null, null, 1, 0], [1, 16, 21.789999999999964, 0]], [4, "lbNum", 33554432, 7, [[[1, -110, [0, "fcyFaPlV9EXLDNOW8jrs8R"], [5, 19.98046875, 50.4]], -111], 4, 1], [2, "83vFOtkmRIUZC7bwJeqzzl", null, null, null, 1, 0], [1, 15.634999999999991, 21.929999999999836, 0], [1, 0.5, 0.5, 1]], [7, "sprIcon", 33554432, 7, [[1, -112, [0, "c5OjOJZ8xHY7rUrpAWw77Z"], [5, 23, 31]], [3, -113, [0, "e62tr7g4FDy51kbJMZjv28"], 16]], [2, "0ciPuXj4tIEqY21/RQiT2K", null, null, null, 1, 0]], [10, 11, [0, "e4pTYhBWNED6vACeX+yd5l"]], [10, 13, [0, "11G5gXHjBKBYf6siNGCDoV"]], [19, "--", 50, 50, 50, false, 14, [0, "3f5B0LJd9PPYxYHkLpnGZ4"], [4, 4292538105]], [20, "用户名", 40, 50, false, 15, [0, "83yxbsh8dAVYW4Vya1zcVR"]], [11, "--", 40, 50, false, 16, [0, "a4NzsktilHn5PUDCovjgf/"], [4, 4281925425]], [11, "--", 40, 50, false, 17, [0, "c1amvV33FKMIT0gzHo8uOm"], [4, 4280953599]], [8, "--", 30, 30, 19, [0, "abfmWQDHlAJJ+MZprw3rtl"]], [8, "--", 30, 30, 22, [0, "00o1RY/SRPzqUxS7D/7Nee"]], [8, "--", 30, 30, 25, [0, "dcT2PZ/7FF0r1Gzkx9zPAs"]], [8, "--", 30, 30, 28, [0, "6cfZajgXhHmblqzeutQ216"]], [8, "--", 30, 30, 31, [0, "d0b4IQagJEYZ4OnL3E7lUz"]]], 0, [0, 4, 1, 0, 0, 1, 0, -1, 39, 0, -2, 40, 0, -3, 41, 0, -4, 42, 0, -5, 43, 0, -1, 3, 0, -2, 4, 0, -3, 5, 0, -4, 6, 0, -5, 7, 0, 5, 36, 0, 6, 33, 0, 7, 38, 0, 8, 37, 0, 9, 34, 0, 10, 35, 0, 0, 1, 0, -1, 9, 0, -2, 10, 0, -3, 8, 0, -4, 12, 0, -5, 13, 0, -6, 14, 0, -7, 15, 0, -8, 16, 0, -9, 17, 0, -10, 2, 0, 0, 2, 0, 0, 2, 0, -1, 3, 0, -2, 4, 0, -3, 5, 0, -4, 6, 0, -5, 7, 0, 0, 3, 0, 0, 3, 0, -1, 18, 0, -2, 19, 0, -3, 20, 0, 0, 4, 0, 0, 4, 0, -1, 21, 0, -2, 22, 0, -3, 23, 0, 0, 5, 0, 0, 5, 0, -1, 24, 0, -2, 25, 0, -3, 26, 0, 0, 6, 0, 0, 6, 0, -1, 27, 0, -2, 28, 0, -3, 29, 0, 0, 7, 0, 0, 7, 0, -1, 30, 0, -2, 31, 0, -3, 32, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, -1, 11, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, -2, 33, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, -2, 34, 0, 0, 14, 0, -2, 35, 0, 0, 15, 0, -2, 36, 0, 0, 16, 0, -2, 37, 0, 0, 17, 0, -2, 38, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, -2, 39, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, 0, 21, 0, 0, 22, 0, -2, 40, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, -2, 41, 0, 0, 26, 0, 0, 26, 0, 0, 27, 0, 0, 27, 0, 0, 28, 0, -2, 42, 0, 0, 29, 0, 0, 29, 0, 0, 30, 0, 0, 30, 0, 0, 31, 0, -2, 43, 0, 0, 32, 0, 0, 32, 0, 11, 1, 113], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 34, 35, 36, 37, 38], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, -1, -2, -3, -4, 1, 3, 3, 3, 3], [4, 5, 6, 0, 7, 1, 0, 8, 1, 0, 9, 1, 0, 10, 1, 0, 11, 1, 12, 13, 14, 3, 3, 2, 2, 2, 2]], [[{"name": "game_icon_skill02", "rect": {"x": 1, "y": 1, "width": 30, "height": 31}, "offset": {"x": 0, "y": -0.5}, "originalSize": {"width": 32, "height": 32}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-15, -15.5, 0, 15, -15.5, 0, -15, 15.5, 0, 15, 15.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [1, 31, 31, 31, 1, 0, 31, 0], "nuv": [0.03125, 0, 0.96875, 0, 0.03125, 0.96875, 0.96875, 0.96875], "minPos": {"x": -15, "y": -15.5, "z": 0}, "maxPos": {"x": 15, "y": 15.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [15]], [[{"name": "game_icon_skill03", "rect": {"x": 0, "y": 1, "width": 31, "height": 31}, "offset": {"x": 0, "y": -0.5}, "originalSize": {"width": 31, "height": 32}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-15.5, -15.5, 0, 15.5, -15.5, 0, -15.5, 15.5, 0, 15.5, 15.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 31, 31, 31, 0, 0, 31, 0], "nuv": [0, 0, 1, 0, 0, 0.96875, 1, 0.96875], "minPos": {"x": -15.5, "y": -15.5, "z": 0}, "maxPos": {"x": 15.5, "y": 15.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [16]], [[{"name": "game_icon_skill04", "rect": {"x": 0, "y": 1, "width": 32, "height": 30}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 32, "height": 32}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-16, -15, 0, 16, -15, 0, -16, 15, 0, 16, 15, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 31, 32, 31, 0, 1, 32, 1], "nuv": [0, 0.03125, 1, 0.03125, 0, 0.96875, 1, 0.96875], "minPos": {"x": -16, "y": -15, "z": 0}, "maxPos": {"x": 16, "y": 15, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [17]], [[{"name": "game_icon_skill01", "rect": {"x": 3, "y": 0, "width": 27, "height": 27}, "offset": {"x": 0.5, "y": 0}, "originalSize": {"width": 32, "height": 27}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-13.5, -13.5, 0, 13.5, -13.5, 0, -13.5, 13.5, 0, 13.5, 13.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [3, 27, 30, 27, 3, 0, 30, 0], "nuv": [0.09375, 0, 0.9375, 0, 0.09375, 1, 0.9375, 1], "minPos": {"x": -13.5, "y": -13.5, "z": 0}, "maxPos": {"x": 13.5, "y": 13.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [18]], [[{"name": "game_icon_skill05", "rect": {"x": 5, "y": 0, "width": 23, "height": 31}, "offset": {"x": 0.5, "y": 0}, "originalSize": {"width": 32, "height": 31}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-11.5, -15.5, 0, 11.5, -15.5, 0, -11.5, 15.5, 0, 11.5, 15.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [5, 31, 28, 31, 5, 0, 28, 0], "nuv": [0.15625, 0, 0.875, 0, 0.15625, 1, 0.875, 1], "minPos": {"x": -11.5, "y": -15.5, "z": 0}, "maxPos": {"x": 11.5, "y": 15.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [19]]]]