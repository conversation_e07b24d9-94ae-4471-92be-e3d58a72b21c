import { _decorator, Component, Label, Node, size, Sprite, SpriteFrame } from 'cc';
import { ConfigHelper } from 'db://assets/scripts/config/ConfigHelper';
import { C_View } from 'db://assets/scripts/ConstGlobal';
import { xcore } from 'db://assets/scripts/libs/xcore';
const { ccclass, property } = _decorator;

@ccclass('UnitExchange')
export class UnitExchange extends Component {
    @property([SpriteFrame])
    private sfFrames: SpriteFrame[] = [];
    @property([SpriteFrame])
    private sfOns: SpriteFrame[] = [];

    @property(Sprite)
    private sprBg: Sprite = null;

    @property(Sprite)
    private sprOn: Sprite = null;

    @property(Sprite)
    private sprIcon: Sprite = null;

    @property(Label)
    private lbName: Label = null;

    @property(Label)
    private lbCost: Label = null;

    private _config: any = null;
    private _skinId: string = null;

    protected onLoad(): void {
        this.node.on('click', () => {


            xcore.ui.addView(C_View.ViewSkinDetail,
                {
                    skinId: this._skinId,
                    skinName: this._config.name,

                }
            )
        }, this);
    }

    setData(config: any) {
        this._config = config;
        this.lbName.string = config.name;
        this.lbCost.string = `${config.yingYunConsume}灵韵兑换`;
        let skinConfig = ConfigHelper.getInstance().getSkinConfigByDebrisId(this._config.skinFragmentId)
        let debrisConfig = ConfigHelper.getInstance().getDebriConfigByJsonId(config.skinFragmentId);
        this._skinId = skinConfig.jsonId;
        let path = `./res/image/${debrisConfig.path}/${debrisConfig.icon}`;
        xcore.res.remoteLoadSprite(path, this.sprIcon, size(230, 300));
        this.sprBg.spriteFrame = this.sfFrames[skinConfig.quality - 1];
        this.sprOn.spriteFrame = this.sfOns[skinConfig.quality - 1];
    }






}


