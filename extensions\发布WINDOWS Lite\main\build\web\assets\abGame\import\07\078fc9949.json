[1, ["52fHu7D8hGm5vLaoALoXCl", "023hs6Z9hMN7n00LscdOzW@f9941", "e8YGnkLQpCN5bnM/tgb9x6@f9941", "1bvbi2IFNPgbGy3H7UlWSA@f9941", "dclhO4Tt5E7LRdiPIIioSG@f9941", "6aJS5bORNNepru/00ht7oP@f9941", "09B5+DVYpC/6lpV8lwjQps@f9941", "00YULWD2BBwKKTHthRCpVm@f9941", "76OyCZqdRMNY6wLhQvK40f@f9941", "ff3reVIcZLdo1ocsK9Lga3@f9941", "9bBYHzhH5F25cHFMhUL3oq@f9941", "80D+DR79JLt5qxfF5qarGl@f9941", "c4Ivt8SBZB25pLPpGg1u1P@f9941", "46IdxunHlHYYawWmS60uVs@f9941", "aedBSY9blB5IZeO3T3kxHe", "d3UmROkVNMrbKZ0mq6h0QI", "b0CM1Wby5OnZSyKCsVcBUb@f9941", "24XDq4PXhBbrHMzRu7y8Do@f9941", "0eFnc4nb1HZ7+pddg2JORw@f9941", "e4Bfprf71Arbhxs0w3BQN3@f9941", "1b1aT19HpIH581RzN3FSap@f9941", "5fjWVImFpHU4L57AjwtCkk@f9941", "a1hwVthSBM1aaC078Ba20L@f9941", "9fOGNCS6NLRrPHfAM2gfhG@f9941", "32K7bDPx1GG4+LjicPvUti@f9941", "020TEjX7VDX7SAokJvIRlP@f9941", "7dj5uJT9FMn6OrOOx83tfK@f9941"], ["node", "_font", "_spriteFrame", "root", "data", "_parent", "sprFrame", "lbName", "btnTagBaseinfo", "btnTagDimond", "btnTagSkin", "lbHp", "lbSkillCd", "lbBaoAtkRat", "lbBaoHurtRat", "lbAtkSpeed", "lbAtk", "spr<PERSON><PERSON><PERSON>", "lbSkinName", "lbUserName", "ndList", "ndUser", "ndDimondDesc", "ndDimond", "ndBaseInfo", "ndSv", "btnClose", "ndRoot", "sv", "pfbUnitSkinShow", "pfbUnitDimondDesc", "btnOption", "ndIsSelect", "lbUnlock", "lbUnlockDesc", "ndUnlock", "lbLeftDay", "ndLeftDay", "ndIconDetail", "spr<PERSON><PERSON>", "sprOn", "lbProp", "lbNum", "sprIcon"], [["cc.Label", ["_string", "_actualFontSize", "_isSystemFontUsed", "_fontSize", "_enableOutline", "_lineHeight", "node", "__prefab", "_color", "_outlineColor", "_font"], -3, 1, 4, 5, 5, 6], ["cc.Node", ["_name", "_layer", "_active", "_components", "_prefab", "_parent", "_lpos", "_children", "_lscale"], 0, 9, 4, 1, 5, 2, 5], ["cc.Sprite", ["_type", "_sizeMode", "node", "__prefab", "_spriteFrame", "_color"], 1, 1, 4, 6, 5], ["cc.Node", ["_name", "_layer", "_active", "_parent", "_components", "_prefab", "_lpos", "_lscale", "_children"], 0, 1, 12, 4, 5, 5, 2], ["cc.Layout", ["_resizeMode", "_layoutType", "_spacingX", "_affectedByScale", "_paddingTop", "_spacingY", "node", "__prefab"], -3, 1, 4], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.<PERSON><PERSON>", ["_transition", "_zoomScale", "node", "__prefab", "_normalColor", "_target"], 1, 1, 4, 5, 1], ["cc.Mask", ["_type", "node", "__prefab"], 2, 1, 4], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["ee2e0X5HCZH0Kb1klG6WfpJ", ["node", "__prefab", "ndRoot", "sprFrame", "btnClose", "ndSv", "ndBaseInfo", "ndDimond", "ndDimondDesc", "ndUser", "ndList", "lbUserName", "lbSkinName", "spr<PERSON><PERSON><PERSON>", "lbAtk", "lbAtkSpeed", "lbBaoHurtRat", "lbBaoAtkRat", "lbSkillCd", "lbHp", "btnTagSkin", "btnTagDimond", "btnTagBaseinfo", "pfbUnitSkinShow", "pfbUnitDimondDesc"], 3, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 6, 6], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.Widget", ["_alignFlags", "_left", "_top", "node", "__prefab"], 0, 1, 4], ["d1a26AN+WBOrL2OAdolqlDt", ["node", "__prefab", "sv"], 3, 1, 4, 1], ["cc.Graphics", ["node", "__prefab", "_fillColor"], 3, 1, 4, 5], ["<PERSON><PERSON>", ["horizontal", "node", "__prefab", "_content"], 2, 1, 4, 1], ["85715aLYYRLGZP6ewIXAbd1", ["node", "__prefab", "sfFrames", "sfOns", "sprFrame", "sprOn", "spr<PERSON><PERSON>", "ndIconDetail", "ndLeftDay", "lbLeftDay", "lbName", "ndUnlock", "lbUnlockDesc", "lbUnlock", "ndIsSelect", "btnOption"], 3, 1, 4, 3, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["2f33eSmzKxJg6dSxESSAIV7", ["node", "__prefab", "sprIcon", "lbName", "lbNum", "lbProp"], 3, 1, 4, 1, 1, 1, 1]], [[9, 0, 2], [11, 0, 1, 2, 3, 4, 5, 5], [5, 0, 1, 2, 1], [1, 0, 1, 5, 3, 4, 6, 3], [2, 2, 3, 4, 1], [3, 0, 1, 3, 4, 5, 6, 3], [5, 0, 1, 2, 3, 1], [1, 0, 1, 5, 7, 3, 4, 6, 3], [0, 0, 1, 2, 6, 7, 8, 9, 4], [0, 0, 1, 2, 4, 6, 7, 9, 10, 5], [2, 2, 3, 1], [3, 0, 1, 3, 8, 4, 5, 6, 3], [3, 0, 1, 3, 4, 5, 3], [5, 0, 1, 1], [8, 0, 2], [1, 0, 1, 7, 3, 4, 3], [1, 0, 1, 7, 3, 4, 6, 3], [3, 0, 1, 3, 4, 5, 6, 7, 3], [0, 0, 1, 3, 2, 6, 7, 8, 10, 5], [0, 0, 1, 3, 2, 6, 7, 8, 9, 5], [6, 0, 1, 2, 3, 3], [1, 0, 1, 5, 3, 4, 3], [1, 0, 2, 1, 5, 3, 4, 4], [1, 0, 1, 5, 3, 4, 6, 8, 3], [2, 1, 2, 3, 4, 2], [14, 0, 1, 2, 1], [4, 0, 1, 2, 3, 6, 7, 5], [0, 0, 1, 2, 6, 7, 10, 4], [1, 0, 1, 5, 7, 3, 4, 3], [1, 0, 2, 1, 5, 7, 3, 4, 6, 4], [1, 0, 2, 1, 5, 3, 4, 6, 8, 4], [3, 0, 2, 1, 3, 4, 5, 6, 7, 4], [10, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 1], [2, 0, 1, 2, 3, 4, 3], [2, 0, 2, 3, 4, 2], [2, 0, 2, 3, 2], [2, 1, 2, 3, 5, 4, 2], [12, 0, 1, 2, 3, 4, 4], [13, 0, 1, 2, 1], [7, 0, 1, 2, 2], [7, 1, 2, 1], [4, 0, 1, 4, 2, 5, 6, 7, 6], [4, 0, 1, 6, 7, 3], [4, 0, 1, 3, 6, 7, 4], [0, 0, 1, 2, 6, 7, 8, 9, 10, 4], [0, 0, 1, 3, 2, 4, 6, 7, 8, 9, 10, 6], [0, 0, 1, 2, 4, 6, 7, 9, 5], [0, 0, 1, 3, 2, 6, 7, 10, 5], [0, 0, 1, 2, 6, 7, 8, 4], [0, 0, 1, 3, 5, 2, 6, 7, 6], [0, 0, 1, 3, 2, 6, 7, 5], [0, 0, 1, 3, 2, 6, 7, 8, 5], [6, 0, 2, 3, 4, 5, 2], [6, 2, 3, 1], [15, 0, 1, 2, 3, 2], [16, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 1], [17, 0, 1, 2, 3, 4, 5, 1]], [[[[14, "ViewUserInfo"], [15, "ViewUserInfo", 33554432, [-25], [[13, -2, [0, "86+kxtfKVN15m+ujnOT+XX"]], [32, -24, [0, "a2IXQpJu1KWI+k2YKy4Hb/"], -23, -22, -21, -20, -19, -18, -17, -16, -15, -14, -13, -12, -11, -10, -9, -8, -7, -6, -5, -4, -3, 29, 30]], [1, "c2UyYXJuxOnYRi+M6ohlqd", null, null, null, -1, 0]], [15, "ndBaseInfo", 33554432, [-27, -28, -29, -30, -31, -32, -33, -34, -35, -36, -37, -38], [[13, -26, [0, "91nkVbkaRMD5nbxhxJBz4l"]]], [1, "a7bJrL2rZGcY2INvqewVJ6", null, null, null, 1, 0]], [28, "ndRoot", 33554432, 1, [-40, -41, -42, -43, -44, -45, -46, -47, 2, -48], [[13, -39, [0, "32HcsFGd5NMotz8pn7seVl"]]], [1, "37DirWLL1ER6Xd/j6M50e2", null, null, null, 1, 0]], [7, "ndUser", 33554432, 3, [-51, -52, -53, -54, -55, -56, -57], [[2, -49, [0, "011plMif9K7Yx101MdUTdf"], [5, 700, 230]], [33, 1, 0, -50, [0, "9c6ikv31tBZZg2xgMWlH95"], 4]], [1, "80wwM/cuJKmb1OnE74Kecb", null, null, null, 1, 0], [1, 0, 359.722, 0]], [5, "btnClose", 33554432, 3, [[[2, -58, [0, "ffaS9It4tFg5u6ODMN9kMg"], [5, 112, 113]], [34, 1, -59, [0, "ceCh5idX9PrbkClHp9oruk"], 7], -60, [37, 9, 361.706, -558.135, -61, [0, "cfCiuThPNOU5zv7i64svk7"]]], 4, 4, 1, 4], [1, "02NjBI3INFAJFjYmDx2yZN", null, null, null, 1, 0], [1, 367.706, 551.635, 0]], [11, "svShow", 33554432, 3, [-66], [[[2, -62, [0, "10W/zagx1GOq7ieYVindRQ"], [5, 760, 1000]], -63, [38, -65, [0, "17uBR2baZMxqcmvYFSWQde"], -64]], 4, 1, 4], [1, "55mvKyLFNDlbRGUYtFerdL", null, null, null, 1, 0], [1, 0, -18.68399999999997, 0]], [11, "btnSkin", 33554432, 3, [-70, -71], [[[2, -67, [0, "cclcjTJxFGNbBMEy3xg+yQ"], [5, 218, 66]], [4, -68, [0, "d9+l5OfiREJ6nuZChFPqFr"], 10], -69], 4, 4, 1], [1, "84wnj5VKVGhaS0dn6BwFTK", null, null, null, 1, 0], [1, -274.495, -583.185, 0]], [11, "btnDimond", 33554432, 3, [-75, -76], [[[2, -72, [0, "04xTLy+VNL9Y5uxzuZ9sav"], [5, 218, 66]], [4, -73, [0, "e1GrMCznhFvLEEOUuervZT"], 13], -74], 4, 4, 1], [1, "322AAZ97JBxJBKuHEaA6gh", null, null, null, 1, 0], [1, -58.07650000000001, -583.185, 0]], [11, "btnBaseInfo", 33554432, 3, [-80, -81], [[[2, -77, [0, "efc4vM2DhBYZYVKKy5e9k9"], [5, 218, 66]], [4, -78, [0, "f9CfUTgJFEFoH7RMeYDXFi"], 16], -79], 4, 4, 1], [1, "6f0Z/AMSFFwIEp5ATS1SNg", null, null, null, 1, 0], [1, 158.34199999999998, -583.185, 0]], [7, "Node", 33554432, 4, [-85], [[2, -82, [0, "c21hY6y+ZPSYi0ys7OHHwO"], [5, 90, 90]], [39, 1, -83, [0, "e5BnqujH5Njq/nOdY1u6br"]], [25, -84, [0, "adNQya4+9GnoLbN4fSt2oH"], [4, 16777215]]], [1, "2fGk6tjl5JfocDTGNjf+VR", null, null, null, 1, 0], [1, -263.127, 13.269000000000005, 0]], [7, "view", 33554432, 6, [-89], [[6, -86, [0, "da8jC+DqlN26tOafi+ti1E"], [5, 800, 1000], [0, 0.5, 1]], [40, -87, [0, "fctfUjm6dHK7/sibsxeY8K"]], [25, -88, [0, "cdvhgeZWBJgoXrXN6B7RBA"], [4, 16777215]]], [1, "8ffQ2ACTBLZpJaXuoCfQeV", null, null, null, 1, 0], [1, 0, 500, 0]], [21, "content", 33554432, 11, [[6, -90, [0, "20Xh5JjxxKGqiOSeoBBWm0"], [5, 700, 20], [0, 0.5, 1]], [41, 1, 3, 20, 20, 20, -91, [0, "21+2QAJS1EPq/eH8OtXrFU"]]], [1, "367E76P5hFhIs0KYwUN8fm", null, null, null, 1, 0]], [3, "lbDimondDesc", 33554432, 4, [[6, -92, [0, "37RFB8rAZAz7QM8xxsqT+J"], [5, 376.8399963378906, 50.4], [0, 0, 0.5]], [44, "灵珠每月1日零点重置", 40, false, -93, [0, "f0kDeQl9xOha7vn3TxJIm1"], [4, 4282532418], [4, 4278219007], 3]], [1, "93tp7eSuRA9qbEIIpajLJz", null, null, null, 1, 0], [1, -139.75900000000001, -79.71800000000007, 0]], [7, "game_title_viewuserinfo", 33554432, 3, [-96], [[2, -94, [0, "feuSiDynVLaYD3l912PWJj"], [5, 294, 77]], [4, -95, [0, "39Z5knoxxG+Z2pB3ulUV40"], 6]], [1, "5dBhfYXy9BRZztHixA9ZJm", null, null, null, 1, 0], [1, 0, 550.692, 0]], [7, "1", 33554432, 2, [-99], [[2, -97, [0, "31bJq0/uFLd7fw31ofpJCS"], [5, 169, 63]], [4, -98, [0, "f5ia7DYUNHboWlPRnitugZ"], 18]], [1, "ddPJkx2WxKkLOyIMvc52Iw", null, null, null, 1, 0], [1, -154.56, 114.03899999999999, 0]], [7, "4", 33554432, 2, [-102], [[2, -100, [0, "ceLlbv/2tFKL0XWudGDrt9"], [5, 169, 63]], [4, -101, [0, "122kfilu1AwYUD/KwmvUjr"], 20]], [1, "6f47Ym/WRKSK2vQvCD695v", null, null, null, 1, 0], [1, -154.56, 18.052999999999997, 0]], [7, "2", 33554432, 2, [-105], [[2, -103, [0, "98PHnNDAFGtLTks3TC9jje"], [5, 169, 63]], [4, -104, [0, "1aR9z046ZHRqNxslUJA7QJ"], 22]], [1, "e1EyQpVfxD8oNrkgAmxuII", null, null, null, 1, 0], [1, -154.56, -78.05600000000004, 0]], [7, "5", 33554432, 2, [-108], [[2, -106, [0, "e1hY2gt6dK1YjAylZoLMS+"], [5, 169, 63]], [4, -107, [0, "2dqu/fNUpBo4l7X03pWjDM"], 24]], [1, "bfQim4Pg9K3qF6Rc9+kdnj", null, null, null, 1, 0], [1, -154.56, -174.16499999999996, 0]], [7, "3", 33554432, 2, [-111], [[2, -109, [0, "4bk6mYK+BJyqLh+/GQgBgB"], [5, 286, 72]], [4, -110, [0, "12nPevjhdDjpJkJanPPWSz"], 26]], [1, "133UXE8BFCCaPyzadkvGV7", null, null, null, 1, 0], [1, -96.06, -270.274, 0]], [7, "6", 33554432, 2, [-114], [[2, -112, [0, "43CZCgNCJHyIaDsrFqrRw6"], [5, 286, 72]], [4, -113, [0, "51tZFtELBIbpuKvlSdmRlR"], 28]], [1, "95T8x319lL3Yd+kaLdxyr6", null, null, null, 1, 0], [1, -96.06, -366.384, 0]], [3, "ndDimond", 33554432, 3, [[2, -115, [0, "32G5eSoHhCTr9LOdopuR9m"], [5, 100, 0]], [42, 1, 2, -116, [0, "dfpZg+A31P76fBY3RC8rVH"]]], [1, "413UR8m5RC5JBd7acqCPNY", null, null, null, 1, 0], [1, 0, -132.72299999999996, 0]], [12, "sprFrame", 33554432, 3, [[[2, -117, [0, "ecM24dA6RKmbUHpMlMyHzF"], [5, 768, 1109]], -118], 4, 1], [1, "42JdKUNZpOLYzMmbxShEo3", null, null, null, 1, 0]], [3, "0", 33554432, 4, [[2, -119, [0, "b76z6AHD5Idqhz+HgOWQUY"], [5, 329, 72]], [4, -120, [0, "3760VbIPdHtay+czN3Kj3p"], 0]], [1, "c6hLHsFcBD6YIUDlknBNln", null, null, null, 1, 0], [1, -2.8759999999999764, 38.52099999999996, 0]], [5, "lbUserName", 33554432, 4, [[[6, -121, [0, "9es639vKZEo4iS0gh3w9gB"], [5, 164, 54.4], [0, 0, 0.5]], -122], 4, 1], [1, "77W479n5FDOprzfLXKe0wo", null, null, null, 1, 0], [1, -139.75900000000001, 45.97000000000003, 0]], [5, "lbSkinName", 33554432, 4, [[[6, -123, [0, "23p8ItRMNLerch0w53miCQ"], [5, 160, 50.4], [0, 0, 0.5]], -124], 4, 1], [1, "fbaLf2DbhPWrE+eSXJ5l3A", null, null, null, 1, 0], [1, -139.75900000000001, -26.200000000000045, 0]], [3, "sprAFrame", 33554432, 4, [[2, -125, [0, "c97gyk+A9G2riiu31NmU1O"], [5, 90, 90]], [24, 0, -126, [0, "82BSxVnLJAh7DwF1mjKkRj"], 1]], [1, "baDozjIsBH2KME0jBZyOkH", null, null, null, 1, 0], [1, -263.122, 14.093000000000075, 0]], [5, "spr<PERSON><PERSON><PERSON>", 33554432, 10, [[[2, -127, [0, "09x0+kbO5O6aaayA4xJSsD"], [5, 90, 90]], -128], 4, 1], [1, "39QubErNdM0qDSlySrU4bT", null, null, null, 1, 0], [1, 0, 1.6889999999999645, 0]], [3, "sprMask", 33554432, 4, [[2, -129, [0, "740xBXk4VJULywNOhBKIW8"], [5, 90, 90]], [24, 0, -130, [0, "56xBiWHylDN74okNFxAMRx"], 2]], [1, "8fsDnLaqJLq7BtG0API58H", null, null, null, 1, 0], [1, -263.127, 14.958000000000084, 0]], [3, "Label", 33554432, 14, [[2, -131, [0, "52cUFYoTVBz7TDGL1VgkIk"], [5, 148, 54.4]], [45, "个人信息", 36, 36, false, true, -132, [0, "2cEwzOjKdIJIMLb72VYbD5"], [4, 4292933887], [4, 4278223283], 5]], [1, "2bNrRG/2xO3Yop23095d5B", null, null, null, 1, 0], [1, 0, 4.5470000000000255, 0]], [21, "ndOn", 33554432, 7, [[2, -133, [0, "eaQHqtSelCgavh/Ux5lS/e"], [5, 218, 66]], [4, -134, [0, "36NsEem7ZOq7GDptYgp9+r"], 8]], [1, "13UrzDOJZJnIRWxl+VzMY3", null, null, null, 1, 0]], [3, "Label", 33554432, 7, [[2, -135, [0, "a2duasVvNJ0KGDOH9ZosXu"], [5, 120, 50.4]], [18, "皮肤展示", 30, 30, false, -136, [0, "18a7K6GIpM9KVXByRbKcPV"], [4, 4291482321], 9]], [1, "e4VGQW579KLaTX/ML2tmtz", null, null, null, 1, 0], [1, 0, 2.720999999999947, 0]], [22, "ndOn", false, 33554432, 8, [[2, -137, [0, "6as9VABtVMjY4wQxPLyQ45"], [5, 218, 66]], [4, -138, [0, "24Hzw6aYFNYrVEcL69uBgJ"], 11]], [1, "3a9fdJhNxH9rXTJDAB36iE", null, null, null, 1, 0]], [3, "Label", 33554432, 8, [[2, -139, [0, "efbUNfXsNJha4j/K9fZxJt"], [5, 120, 50.4]], [18, "灵珠属性", 30, 30, false, -140, [0, "6eKL8RkhJPALpWDLY4vXMx"], [4, 4279772212], 12]], [1, "4cU5cd3eZJgY2A2qeyfDbO", null, null, null, 1, 0], [1, 0, 2.721, 0]], [22, "ndOn", false, 33554432, 9, [[2, -141, [0, "20m1TJTfBNMILRYY5YUcvj"], [5, 218, 66]], [4, -142, [0, "0dR759cxhKJ4LGepzAURon"], 14]], [1, "d4hqSW7pJETKNGvWbBsOIl", null, null, null, 1, 0]], [3, "Label", 33554432, 9, [[2, -143, [0, "74z4cdp8hKxJESGdggjCqW"], [5, 120, 50.4]], [18, "皮肤属性", 30, 30, false, -144, [0, "c8lcB8TdZILry3ofddTxoL"], [4, 4279772212], 15]], [1, "a8K24rNs9K34eyqUTq795S", null, null, null, 1, 0], [1, 0, 2.720999999999947, 0]], [3, "Label", 33554432, 15, [[2, -145, [0, "7bq5GesQxK3KJzhjUjLU33"], [5, 114, 54.4]], [9, "攻击：", 40, false, true, -146, [0, "5ebmZhccRAzY23vAomWi7b"], [4, 4278190335], 17]], [1, "ebGt2C1p9BGIdB/ylLqx1z", null, null, null, 1, 0], [1, -8.355999999999995, 2.4950000000001182, 0]], [3, "Label", 33554432, 16, [[2, -147, [0, "02LVF1upNFKoRxvnThnEHV"], [5, 114, 54.4]], [9, "攻速：", 40, false, true, -148, [0, "23jmDTmc5MpZUwS0r/nNP4"], [4, 4278190335], 19]], [1, "dbEl3NTlpH0beWL83QT6Zs", null, null, null, 1, 0], [1, -8.355999999999995, 2.4950000000000045, 0]], [3, "Label", 33554432, 17, [[2, -149, [0, "708L72YNZJ7p568YrV7P0r"], [5, 114, 54.4]], [9, "爆伤：", 40, false, true, -150, [0, "a1wKAvXKVB/qv+Rl50B6+M"], [4, 4294115328], 21]], [1, "47UCGCnWhHBbJ5QSmFCZjh", null, null, null, 1, 0], [1, -8.355999999999995, 2.4950000000000045, 0]], [3, "Label", 33554432, 18, [[2, -151, [0, "58W+Qz6qZBlYZatHd2wc1p"], [5, 154, 54.4]], [9, "暴击率：", 40, false, true, -152, [0, "18it2YvnNJ7peawMhhHmO/"], [4, 4294115328], 23]], [1, "8exDjzXE9CVrWIjeD0jZ/Z", null, null, null, 1, 0], [1, 11.644000000000005, 2.4950000000000045, 0]], [3, "Label", 33554432, 19, [[2, -153, [0, "fbKNyTzSJJw41/XQL4xIDi"], [5, 194, 54.4]], [9, "法宝速度：", 40, false, true, -154, [0, "f2yl9vfjZGxpW0JwRmm7SO"], [4, 4278219007], 25]], [1, "3f3zWenehAvYYFF5QThEC8", null, null, null, 1, 0], [1, -26.855999999999995, 2.4950000000000045, 0]], [3, "Label", 33554432, 20, [[2, -155, [0, "97OvBPtVpIFb9Bg2cRkM5Y"], [5, 234, 54.4]], [9, "南天门血量：", 40, false, true, -156, [0, "19RfbVw01DkpHNyBzuQRU6"], [4, 4278219007], 27]], [1, "74yFGdO51A1rB9+3l1sY+J", null, null, null, 1, 0], [1, -6.8559999999999945, 2.4950000000000045, 0]], [5, "lbAtk", 33554432, 2, [[[6, -157, [0, "f22TlpeAxEpofK60/DGspp"], [5, 221.83998107910156, 50.4], [0, 0, 0.5]], -158], 4, 1], [1, "81Y0gJHRxEp4f5zTo1KnyB", null, null, null, 1, 0], [1, 0.23699999999996635, 114.03899999999999, 0]], [5, "lbAtkSpeed", 33554432, 2, [[[6, -159, [0, "53VpJ99FBBTrMzpioSG9Vb"], [5, 62.47999572753906, 50.4], [0, 0, 0.5]], -160], 4, 1], [1, "93ct0nO4ZPHJTQPPGAwkRz", null, null, null, 1, 0], [1, 0.23699999999996635, 18.052999999999997, 0]], [5, "lbBaoHurt", 33554432, 2, [[[6, -161, [0, "93nbfVhFpBDrnhAZQHGoXB"], [5, 62.47999572753906, 50.4], [0, 0, 0.5]], -162], 4, 1], [1, "00ZAmGK61AgqONEMm6ZuVG", null, null, null, 1, 0], [1, 0.23699999999996635, -78.68700000000001, 0]], [5, "lbBaoAtk", 33554432, 2, [[[6, -163, [0, "06xICmrcFM8bNdA+hVyXRe"], [5, 62.47999572753906, 50.4], [0, 0, 0.5]], -164], 4, 1], [1, "09eZXxU+VC2pg7WOehik2c", null, null, null, 1, 0], [1, 0.23699999999996635, -176.91999999999996, 0]], [5, "lbSkillCd", 33554432, 2, [[[6, -165, [0, "ed3tlotT9DpYstyTctj+qx"], [5, 62.47999572753906, 50.4], [0, 0, 0.5]], -166], 4, 1], [1, "e0riLQmaVEWItBHht79S7z", null, null, null, 1, 0], [1, 87.80899999999997, -264.568, 0]], [5, "lbHp", 33554432, 2, [[[6, -167, [0, "71IVdX3yBBwrWfkzsJ+r5z"], [5, 98.55999755859375, 50.4], [0, 0, 0.5]], -168], 4, 1], [1, "e9J9xldSlN+aFbzY1ZI2PS", null, null, null, 1, 0], [1, 87.80899999999997, -367.549, 0]], [35, 1, 22, [0, "cdB7Q9LYxH0Jua257ntzI9"]], [46, "用户名称", 40, false, true, 24, [0, "96phginQdGDq4PpV+difSe"], [4, 4282664004]], [8, "皮肤名称", 40, false, 25, [0, "fcvsGM5NxKJb6tMVMjiIYM"], [4, 4278190330], [4, 4278219007]], [10, 27, [0, "9ceULP9oZNOKq10fHVUxHo"]], [52, 3, 5, [0, "f1urwVlMRG5qCuamy0cYMu"], [4, 4292269782], 5], [54, false, 6, [0, "a8yBCx4oFAJ5maSJAd6gvN"], 12], [20, 3, 1.02, 7, [0, "4beLJi8qJLDYuGYPqkHEMV"]], [20, 3, 1.02, 8, [0, "99V18y285D/KFxIGIb8cjZ"]], [20, 3, 1.02, 9, [0, "deia6pBl5BqZ3+IzGRkr2K"]], [8, "5000~8000", 40, false, 42, [0, "7cu6vktrhHs6oDTxMdq7Fa"], [4, 4278190330], [4, 4278219007]], [8, "0.5", 40, false, 43, [0, "12UZTb8ElKcK5htmBNUrXX"], [4, 4278190330], [4, 4278219007]], [8, "0.5", 40, false, 44, [0, "107mrLLkhBrLfcEuiYqBBT"], [4, 4294926858], [4, 4278219007]], [8, "0.5", 40, false, 45, [0, "e6g5RdNtJJNoXXqhYJzjgM"], [4, 4294926858], [4, 4278219007]], [8, "0.5", 40, false, 46, [0, "1frwkGdEhMka5jG0wArvkH"], [4, 4278219007], [4, 4278219007]], [8, "5000", 40, false, 47, [0, "d2RH9DHvVJ+rgUEpdzvaJO"], [4, 4278219007], [4, 4278219007]]], 0, [0, 3, 1, 0, 0, 1, 0, 8, 56, 0, 9, 55, 0, 10, 54, 0, 11, 62, 0, 12, 61, 0, 13, 60, 0, 14, 59, 0, 15, 58, 0, 16, 57, 0, 17, 51, 0, 18, 50, 0, 19, 49, 0, 20, 12, 0, 21, 4, 0, 22, 13, 0, 23, 21, 0, 24, 2, 0, 25, 6, 0, 26, 52, 0, 6, 48, 0, 27, 3, 0, 0, 1, 0, -1, 3, 0, 0, 2, 0, -1, 15, 0, -2, 16, 0, -3, 17, 0, -4, 18, 0, -5, 19, 0, -6, 20, 0, -7, 42, 0, -8, 43, 0, -9, 44, 0, -10, 45, 0, -11, 46, 0, -12, 47, 0, 0, 3, 0, -1, 22, 0, -2, 4, 0, -3, 14, 0, -4, 5, 0, -5, 6, 0, -6, 7, 0, -7, 8, 0, -8, 9, 0, -10, 21, 0, 0, 4, 0, 0, 4, 0, -1, 23, 0, -2, 24, 0, -3, 25, 0, -4, 26, 0, -5, 10, 0, -6, 28, 0, -7, 13, 0, 0, 5, 0, 0, 5, 0, -3, 52, 0, 0, 5, 0, 0, 6, 0, -2, 53, 0, 28, 53, 0, 0, 6, 0, -1, 11, 0, 0, 7, 0, 0, 7, 0, -3, 54, 0, -1, 30, 0, -2, 31, 0, 0, 8, 0, 0, 8, 0, -3, 55, 0, -1, 32, 0, -2, 33, 0, 0, 9, 0, 0, 9, 0, -3, 56, 0, -1, 34, 0, -2, 35, 0, 0, 10, 0, 0, 10, 0, 0, 10, 0, -1, 27, 0, 0, 11, 0, 0, 11, 0, 0, 11, 0, -1, 12, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, -1, 29, 0, 0, 15, 0, 0, 15, 0, -1, 36, 0, 0, 16, 0, 0, 16, 0, -1, 37, 0, 0, 17, 0, 0, 17, 0, -1, 38, 0, 0, 18, 0, 0, 18, 0, -1, 39, 0, 0, 19, 0, 0, 19, 0, -1, 40, 0, 0, 20, 0, 0, 20, 0, -1, 41, 0, 0, 21, 0, 0, 21, 0, 0, 22, 0, -2, 48, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, -2, 49, 0, 0, 25, 0, -2, 50, 0, 0, 26, 0, 0, 26, 0, 0, 27, 0, -2, 51, 0, 0, 28, 0, 0, 28, 0, 0, 29, 0, 0, 29, 0, 0, 30, 0, 0, 30, 0, 0, 31, 0, 0, 31, 0, 0, 32, 0, 0, 32, 0, 0, 33, 0, 0, 33, 0, 0, 34, 0, 0, 34, 0, 0, 35, 0, 0, 35, 0, 0, 36, 0, 0, 36, 0, 0, 37, 0, 0, 37, 0, 0, 38, 0, 0, 38, 0, 0, 39, 0, 0, 39, 0, 0, 40, 0, 0, 40, 0, 0, 41, 0, 0, 41, 0, 0, 42, 0, -2, 57, 0, 0, 43, 0, -2, 58, 0, 0, 44, 0, -2, 59, 0, 0, 45, 0, -2, 60, 0, 0, 46, 0, -2, 61, 0, 0, 47, 0, -2, 62, 0, 4, 1, 2, 5, 3, 168], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 49, 50, 57, 58, 59, 60, 61, 62], [2, 2, 2, 1, 2, 1, 2, 2, 2, 1, 2, 2, 1, 2, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 29, 30, 2, 1, 1, 1, 1, 1, 1, 1, 1], [8, 9, 10, 0, 11, 0, 12, 13, 1, 0, 2, 1, 0, 2, 1, 0, 2, 0, 3, 0, 3, 0, 4, 0, 4, 0, 5, 0, 5, 14, 15, 16, 0, 0, 0, 0, 0, 0, 0, 0]], [[[14, "UnitSkinShow"], [16, "UnitSkinShow", 33554432, [-16, -17, -18, -19, -20, -21, -22, -23], [[2, -2, [0, "29hHPoTlxJpKF/UVVKnf0O"], [5, 220, 300]], [55, -15, [0, "43qlqNk+pD7p0eYLi4NUrE"], [6, 7, 8, 9], [10, 11, 12, 13], -14, -13, -12, -11, -10, -9, -8, -7, -6, -5, -4, -3]], [1, "c4uQxWRfhFno1NfUIaH4N+", null, null, null, -1, 0], [1, -240, -150, 0]], [16, "list", 33554432, [-26, -27, -28], [[2, -24, [0, "9bM5eGSKVP25DZ/gRhE5bB"], [5, 80.83999633789062, 20]], [26, 1, 1, 2, true, -25, [0, "f9UwRXoIZNWIR/zP5N9uht"]]], [1, "a6XUD8P1xKtLsm+WW/wGFy", null, null, null, 1, 0], [1, 0, -11.34, 0]], [16, "list", 33554432, [-31, -32, -33], [[6, -29, [0, "a2FW4HzztBMr2LfgJeFmIy"], [5, 69.59999084472656, 29], [0, 1, 0.5]], [26, 1, 1, 2, true, -30, [0, "f6Zfq0PHBEZJIaQSDl90V3"]]], [1, "53SHmjMNJPdIdZAxU+lZpk", null, null, null, 1, 0], [1, 72.15100000000001, 0.5550000000000637, 0]], [29, "ndLeftDay", false, 33554432, 1, [3], [[2, -34, [0, "4aQiyguSBGYoO2oK80NaT9"], [5, 167, 29]], [4, -35, [0, "6dMrHL0JFNDrUeTtdRdtMT"], 3]], [1, "2coBFLL3JE9Y/rHIZEvhUf", null, null, null, 1, 0], [1, 22.029999999999973, 128.50199999999995, 0]], [7, "Node", 33554432, 1, [-38, 2], [[2, -36, [0, "5drm+UrwBBYIQ4WcqCkdQi"], [5, 100, 42.68]], [43, 1, 2, true, -37, [0, "e12NN1bCVBZ6CZkCbRdCUX"]]], [1, "0fWfCRu0xIJqAYzS6i3DPL", null, null, null, 1, 0], [1, 0, -109.69299999999998, 0]], [3, "ndDetail", 33554432, 1, [[2, -39, [0, "a69iEY5MZB/a67Ak4lz+xg"], [5, 83, 29]], [4, -40, [0, "5dXNCKcH5O2I8aP10sxV8K"], 0]], [1, "6dXS2Ucz9FUoQaSOYO+pJn", null, null, null, 1, 0], [1, 64.19999999999999, 109.23699999999997, 0]], [3, "ndSelected", 33554432, 1, [[2, -41, [0, "48/axFy81L97w+w8CoBQ1v"], [5, 206, 60]], [4, -42, [0, "9ff0kp1tpLrp+S6draaaYk"], 5]], [1, "55dLRPXJ9BL5etyg7G7QGv", null, null, null, 1, 0], [1, 2.3039999999999736, 7.802000000000021, 0]], [12, "sprFrame", 33554432, 1, [[[2, -43, [0, "65k3skCRRNpqJSY+whRnb3"], [5, 212, 298]], -44], 4, 1], [1, "71ezCUc7pICLVm81zlDUOz", null, null, null, 1, 0]], [12, "spr<PERSON><PERSON>", 33554432, 1, [[[2, -45, [0, "8aDU6ba4RD0r2DymXzbwba"], [5, 212, 298]], -46], 4, 1], [1, "66Hvn8vVNBd7pHuPFWTp7b", null, null, null, 1, 0]], [5, "sprOn", 33554432, 1, [[[2, -47, [0, "a908famYxP7LNeT5k3eP5B"], [5, 237, 315]], -48], 4, 1], [1, "b0IKrMz5VBvanv8w/afoZB", null, null, null, 1, 0], [1, -5.0090000000000146, 0, 0]], [23, "Label", 33554432, 3, [[2, -49, [0, "4bB++9Mo9IfojbkMdnXrXN"], [5, 80, 50.4]], [27, "等级", 40, false, -50, [0, "d9WgtNGqZDTo3afEQUXP9Q"], 1]], [1, "62ok83KwJAPZqfSI4KGxF9", null, null, null, 1, 0], [1, -49.59999084472656, 0, 0], [1, 0.5, 0.5, 1]], [17, "lbDay", 33554432, 3, [[[2, -51, [0, "80MnBAPptFy7YF5FFNE7nJ"], [5, 55.199981689453125, 50.4]], -52], 4, 1], [1, "58RHVhqNFIfpRankK480ss", null, null, null, 1, 0], [1, -13.799995422363281, 0, 0], [1, 0.5, 0.5, 1]], [30, "Label-002", false, 33554432, 3, [[2, -53, [0, "52qo1mCNJHjaEDwL7l9QtG"], [5, 40, 50.4]], [27, "天", 40, false, -54, [0, "deLc/GQ3ZOhZssLdqTE6j/"], 2]], [1, "1chDc+wBxBQ5A/+lPT6pwy", null, null, null, 1, 0], [1, -10, 0, 0], [1, 0.5, 0.5, 1]], [17, "lbName", 33554432, 5, [[[2, -55, [0, "73nbFPN1VB3ZJZrQDYCtad"], [5, 33.1199951171875, 45.36]], -56], 4, 1], [1, "71qPGiqYRJvbGE9fPK835h", null, null, null, 1, 0], [1, 0, 10, 0], [1, 0.5, 0.5, 1]], [31, "Label", false, 33554432, 2, [[[2, -57, [0, "e8/V5r/yBGbKaCSPOmOYbV"], [5, 72, 50.4]], -58], 4, 1], [1, "74rK/t/XpLT4GomXGDKT/z", null, null, null, 1, 0], [1, -59.41999816894531, 0, 0], [1, 0.5, 0.5, 1]], [17, "lbScore", 33554432, 2, [[[2, -59, [0, "3asQ1lsO5HwK0zI54dWE8K"], [5, 49.67999267578125, 50.4]], -60], 4, 1], [1, "e87lnHrVRGI4w7slYLwOmZ", null, null, null, 1, 0], [1, -28, 0, 0], [1, 0.5, 0.5, 1]], [23, "Label-002", 33554432, 2, [[2, -61, [0, "9bIZFSIehBBLojEBaOY+ln"], [5, 108, 50.4]], [47, "可解锁", 36, 36, false, -62, [0, "314P8ONfdB0L1UnqRr1UWT"], 4]], [1, "90U0kPqttOIYpWGTblQgfV", null, null, null, 1, 0], [1, 13.419998168945312, 0, 0], [1, 0.5, 0.5, 1]], [12, "btnClick", 33554432, 1, [[[2, -63, [0, "351SRABCJEErNqH4qSZbUC"], [5, 180, 260]], -64], 4, 1], [1, "56fo11cJlHibqUQTxkFF1n", null, null, null, 1, 0]], [10, 8, [0, "1bjAampwBAvLc4WoDifghz"]], [10, 9, [0, "cdOIAHqj1GHIjLOqQgW3rh"]], [10, 10, [0, "a26+9tjwlO5baGGFzchCbA"]], [48, "---", 40, false, 12, [0, "1bUUGMrgFLl6tRU4qmSSu6"], [4, 4281137495]], [49, "--", 36, 36, 36, false, 14, [0, "e8RuMMVSVCuocDYVmMP7Ln"]], [50, "积分", 36, 36, false, 15, [0, "f0ocbOwZpPbqKblo9/NAXd"]], [51, "---", 36, 36, false, 16, [0, "f6n0UJwrVAsaNGQ8uqMIEs"], [4, 4283552508]], [53, 18, [0, "ebWYqVa1xA6IPHf4wvBLcD"]]], 0, [0, 3, 1, 0, 0, 1, 0, 31, 26, 0, 32, 7, 0, 33, 25, 0, 34, 24, 0, 35, 2, 0, 7, 23, 0, 36, 22, 0, 37, 4, 0, 38, 6, 0, 39, 20, 0, 40, 21, 0, 6, 19, 0, 0, 1, 0, -1, 8, 0, -2, 9, 0, -3, 10, 0, -4, 6, 0, -5, 4, 0, -6, 5, 0, -7, 18, 0, -8, 7, 0, 0, 2, 0, 0, 2, 0, -1, 15, 0, -2, 16, 0, -3, 17, 0, 0, 3, 0, 0, 3, 0, -1, 11, 0, -2, 12, 0, -3, 13, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, -1, 14, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, -2, 19, 0, 0, 9, 0, -2, 20, 0, 0, 10, 0, -2, 21, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, -2, 22, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, -2, 23, 0, 0, 15, 0, -2, 24, 0, 0, 16, 0, -2, 25, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, -2, 26, 0, 4, 1, 2, 5, 5, 3, 5, 4, 64], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 21, 22, 23, 24, 25], [2, 1, 1, 2, 1, 2, -1, -2, -3, -4, -1, -2, -3, -4, 2, 2, 1, 1, 1, 1], [17, 0, 0, 18, 0, 19, 6, 20, 21, 22, 7, 23, 24, 25, 6, 7, 0, 0, 0, 0]], [[[14, "UnitDimondDesc"], [15, "UnitDimondDesc", 33554432, [-8, -9, -10, -11, -12], [[2, -2, [0, "e96VrvoXRDP7ZhRLgZEN0m"], [5, 760, 150]], [56, -7, [0, "93cKp6O6dPxpXW1y7wYfQj"], -6, -5, -4, -3]], [1, "90mT9rgf1EmK3GRz/3c+XU", null, null, null, -1, 0]], [5, "sprIcon", 33554432, 1, [[[13, -13, [0, "22mmWY5rNH2p2fF3Nd4Qgt"]], -14], 4, 1], [1, "c90u2wuWREK79JVc9BtUr0", null, null, null, 1, 0], [1, -284.93399999999997, 0, 0]], [3, "default_sprite_splash", 33554432, 1, [[2, -15, [0, "22r9jth9NFj6CcfoLjTgyu"], [5, 700, 3]], [36, 0, -16, [0, "00YZWYS+BKbY8QuoMFaRJD"], [4, 4279844696], 0]], [1, "8bhbe/zTNLNL7jRK+fhGAg", null, null, null, 1, 0], [1, 0, -75, 0]], [5, "lbName", 33554432, 1, [[[6, -17, [0, "59pP8rEKhK/q+jG7+giOlw"], [5, 72, 50.4], [0, 0, 0.5]], -18], 4, 1], [1, "666Nqov8hDnZzk06yfpR7k", null, null, null, 1, 0], [1, -163.618, 49.636999999999944, 0]], [5, "lbNum", 33554432, 1, [[[6, -19, [0, "03cNnZcrBHir35Sx4+XUyT"], [5, 237.52798461914062, 50.4], [0, 0, 0.5]], -20], 4, 1], [1, "39QIuUMr1BvZyRrpXekWmg", null, null, null, 1, 0], [1, -163.618, 2.1689999999999827, 0]], [5, "lbProp", 33554432, 1, [[[6, -21, [0, "cfYknaa2pKLZcelszUJiDK"], [5, 99, 50.4], [0, 0, 0.5]], -22], 4, 1], [1, "cdhyvlB5pDRKdlvfMxiVzn", null, null, null, 1, 0], [1, -163.618, -45.298, 0]], [10, 2, [0, "a88OgR/4lOZJ+BKytAWtTT"]], [19, "名称", 36, 36, false, 4, [0, "91P5xDJfpLz4HJ5Isb0/FY"], [4, 4281087532], [4, 4278219007]], [19, "持有数量：000", 36, 36, false, 5, [0, "d5WkR9oP9L25/2ND/jS78F"], [4, 4281087532], [4, 4278219007]], [19, "属性：", 36, 36, false, 6, [0, "caklFY73pMM5GZBntYWDv/"], [4, 4281087532], [4, 4278219007]]], 0, [0, 3, 1, 0, 0, 1, 0, 41, 10, 0, 42, 9, 0, 7, 8, 0, 43, 7, 0, 0, 1, 0, -1, 2, 0, -2, 3, 0, -3, 4, 0, -4, 5, 0, -5, 6, 0, 0, 2, 0, -2, 7, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, -2, 8, 0, 0, 5, 0, -2, 9, 0, 0, 6, 0, -2, 10, 0, 4, 1, 22], [0, 8, 9, 10], [2, 1, 1, 1], [26, 0, 0, 0]]]]