{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "afd94e2e-7646-43d7-a4d2-6ff32d7d8c22", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "afd94e2e-7646-43d7-a4d2-6ff32d7d8c22@6c48a", "displayName": "game_frame_skilltips", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "afd94e2e-7646-43d7-a4d2-6ff32d7d8c22", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "afd94e2e-7646-43d7-a4d2-6ff32d7d8c22@f9941", "displayName": "game_frame_skilltips", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 334, "height": 81, "rawWidth": 334, "rawHeight": 81, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-167, -40.5, 0, 167, -40.5, 0, -167, 40.5, 0, 167, 40.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 81, 334, 81, 0, 0, 334, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-167, -40.5, 0], "maxPos": [167, 40.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "afd94e2e-7646-43d7-a4d2-6ff32d7d8c22@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "afd94e2e-7646-43d7-a4d2-6ff32d7d8c22@6c48a"}}