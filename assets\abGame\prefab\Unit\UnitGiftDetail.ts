import { _decorator, Component, log, Node, RichText, Sprite } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('UnitGiftDetail')
export class UnitGiftDetail extends Component {
    @property(Sprite)
    private sprGift: Sprite = null;

    @property(RichText)
    private lbName: RichText = null;

    @property(RichText)
    private lbDesc: RichText = null;



    setData(data: any) {
        log(data)
    }
}


