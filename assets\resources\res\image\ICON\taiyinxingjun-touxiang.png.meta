{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "195dee61-086b-4bc0-be3e-0ae162404b5b", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "195dee61-086b-4bc0-be3e-0ae162404b5b@6c48a", "displayName": "taiyinxingjun-toux<PERSON>g", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "195dee61-086b-4bc0-be3e-0ae162404b5b", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "195dee61-086b-4bc0-be3e-0ae162404b5b@f9941", "displayName": "taiyinxingjun-toux<PERSON>g", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 212, "height": 298, "rawWidth": 212, "rawHeight": 298, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-106, -149, 0, 106, -149, 0, -106, 149, 0, 106, 149, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 298, 212, 298, 0, 0, 212, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-106, -149, 0], "maxPos": [106, 149, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "195dee61-086b-4bc0-be3e-0ae162404b5b@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "195dee61-086b-4bc0-be3e-0ae162404b5b@6c48a"}}