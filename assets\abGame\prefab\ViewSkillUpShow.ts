import { _decorator, Component, Label, Node, size, sp, Sprite, tween } from 'cc';
import { ViewBase } from '../../scripts/libs/manager/ViewBase';
import { C_Bundle, E_SkillType } from '../../scripts/ConstGlobal';
import { xcore } from '../../scripts/libs/xcore';
import { IGiftMessage } from '../scripts/GiftMessageMgr';
const { ccclass, property } = _decorator;

@ccclass('ViewSkillUpShow')
export class ViewSkillUpShow extends ViewBase {
    @property(sp.Skeleton)
    private anim: sp.Skeleton = null;

    @property(Sprite)
    private sprAvatar: Sprite = null;

    @property(Label)
    private lbName: Label = null;

    @property(Label)
    private lbDesc: Label = null;

    private _cb: Function = null;
    private _time: number = 5;

    async setData(data: any) {

        let message = data.message as IGiftMessage;

        let txt
        let animPath
        switch (message.skillType) {
            case E_SkillType.GreatFire:
                txt = '朱雀扇'
                animPath = './res/anim/skilleffect/zhuque';
                break;
            case E_SkillType.GreatLightning:
                txt = '五雷钉'
                animPath = './res/anim/skilleffect/leilingzhu';
                break;
            case E_SkillType.GreatRock:
                txt = '混元伞'
                animPath = './res/anim/skilleffect/hunyuansan';
                break;
            case E_SkillType.GreatSkyRock:
                txt = '女娲石'
                animPath = './res/anim/skilleffect/lvwashi';
                break;
            case E_SkillType.GreatFires:
                txt = '神火炉';
                animPath = './res/anim/skilleffect/bagualu';
                break;

            default:
                break;
        }
        txt = txt + `(Lv.${message.lev})`;
        this.lbDesc.string = txt;
        this.lbName.string = message.name || '匿名用户';
        if (message.avatar) {
            xcore.res.remoteLoadSprite(message.avatar, this.sprAvatar, size(100, 100))
        }
        await xcore.res.bundleLoadSpine(C_Bundle.abGame, animPath, this.anim);
        this.anim.setAnimation(0, 'animation')
        this._cb = data.cb;
        this._time = 2.3;
        
    }

    protected onDestroyCompleted(): void {
        this._cb && this._cb();
    }

    protected update(dt: number): void {
        this._time -= dt;
        if (this._time <= 0) {
            this.closeSelf()
        }
    }
}


