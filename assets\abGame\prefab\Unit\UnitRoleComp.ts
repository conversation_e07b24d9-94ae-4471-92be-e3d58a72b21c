import { _decorator, Component, Label, log, math, Node, sp, Sprite, v2, v3, Vec2, Animation, UITransform, SpriteAtlas, Vec3, Material, Color, tween, Tween, size, warn, SpriteFrame } from 'cc';

import { Role } from '../../scripts/Role';
import { C_Bundle, E_RoleState, E_RoleType } from '../../../scripts/ConstGlobal';

import Tool from '../../../scripts/libs/utils/Tool';
import { xcore } from '../../../scripts/libs/xcore';
import { ConfigHelper } from 'db://assets/scripts/config/ConfigHelper';
import { StringUtil } from 'db://assets/scripts/libs/utils/StringUtil';

const { ccclass, property } = _decorator;

@ccclass('UnitRoleComp')
export class UnitRoleComp extends Component {

    @property(Animation)
    private anim: Animation

    @property(Label)
    private lbHp: Label = null;

    @property(Label)
    private lbLev: Label = null;

    @property([SpriteFrame])
    private sfRoleNameBgs: SpriteFrame[] = [];

    @property([SpriteFrame])
    private sfPgsBgs: SpriteFrame[] = [];

    @property([SpriteFrame])
    private sfPgsBars: SpriteFrame[] = [];

    @property(Sprite)
    private sprRoleName: Sprite = null;

    @property(Label)
    private lbRoleName: Label = null;

    @property(Label)
    private lbName: Label = null;

    @property(Label)
    private lbDesc: Label = null;

    @property(Node)
    private ndUserDetail: Node = null;

    @property(Node)
    private ndPgs: Node = null;

    @property(Sprite)
    private sprBar: Sprite = null;

    @property(Sprite)
    private sprLevBg: Sprite = null;

    @property(Sprite)
    private sprGiftTitle: Sprite = null;

    @property(Label)
    private lbGiftTitle: Label = null;

    @property(Label)
    private lbInvincibleTime: Label = null;

    @property(sp.Skeleton)
    private animIconInvincible: sp.Skeleton = null;

    @property(Node)
    private ndUserData: Node = null;

    @property(Node)
    private ndAvatar: Node = null;

    @property(Sprite)
    private sprAvatar: Sprite = null;

    @property(sp.Skeleton)
    private animAddHp: sp.Skeleton = null;

    @property(Sprite)
    private sprWingAnim: Sprite = null;

    private fromRole: Role

    private state: E_RoleState = E_RoleState.None;
    private tempPos: Vec3 = new Vec3()
    private tempScale: Vec3 = new Vec3();
    private tempScale2: Vec3 = new Vec3();
    private tempPgsScale: Vec3 = new Vec3();

    //刷新塔防显示效果
    private unpdateTowerShowFunc: Function



    private sprRole: Sprite = null;

    private colorTime: number = 0;
    private tempColorRed: Color = new Color(240, 0, 0, 200)
    private tempColorGreen: Color = new Color(0, 240, 0, 200)
    private tempColorDefault: Color = new Color(255, 255, 255, 255)
    private tempColorHurt: Color = new Color(255, 255, 255, 255)
    private tempColorOutline: Color = new Color(255, 255, 255, 255)
    private tempColorZero: Color = new Color(255, 255, 255, 255)
    private tempLevBgPos: Vec3 = new Vec3();
    private tempLevTxtPos: Vec3 = new Vec3();


    private _tempTween: Tween

    finishCb: Function

    init(fromRole: Role) {
        this.unscheduleAllCallbacks();
        this.fromRole = fromRole;
        this.tempScale?.set(0, 0);
        if (this.node && this.node.isValid) {
            this.node.active = true;
        }
        this.state = E_RoleState.None;
        if (!this.sprRole && this.anim) {
            this.sprRole = this.anim.getComponent(Sprite);
        }

        this.clearAnim();
        //if (this.animIconInvincible) {
        //this.animIconInvincible.node.active = false;
        //}
        if (this.lbName) {
            this.lbName.string = `${StringUtil.sub(fromRole.data.nickName || '匿名用户', 9, true)}`;
        }

        //头像
        if (this.sprAvatar) {
            if (fromRole.data.iconUrl) {
                this.ndAvatar.active = true;
                xcore.res.remoteLoadSprite(fromRole.data.iconUrl, this.sprAvatar, size(68, 68))
            } else {
                this.ndAvatar.active = false;
            }

        }
        if (this.ndUserDetail) {
            this.ndUserDetail.active = fromRole.data.type == E_RoleType.Hero;
        }
        if (this.ndPgs) {
            this.ndPgs.active = false;

        }
        if (this.sprRoleName) {
            this.sprRoleName.spriteFrame = null;
            this.lbRoleName.string = '';
            this.sprRoleName.node.active = false;
            if (this.fromRole.data.type == E_RoleType.Monster) {
                this.ndPgs.setPosition(Vec3.ZERO)
                this.lbRoleName.string = this.fromRole.data.monsterName;
                //小boss
                if (this.fromRole.checkIfSmallBoss()) {
                    this.ndPgs.getComponent(Sprite).spriteFrame = this.sfPgsBgs[1];
                    this.sprBar.spriteFrame = this.sfPgsBars[1];
                    this.sprRoleName.spriteFrame = this.sfRoleNameBgs[0];
                    this.sprRoleName.node.active = true;
                    this.ndPgs.setPosition(v3(0, 260 / this.fromRole.data.scale.x, 0))
                }
                //大boss
                else if (this.fromRole.checkIfBigBoss()) {
                    this.ndPgs.getComponent(Sprite).spriteFrame = this.sfPgsBgs[2]
                    this.sprBar.spriteFrame = this.sfPgsBars[2];
                    this.sprRoleName.spriteFrame = this.sfRoleNameBgs[1];
                    this.sprRoleName.node.active = true;
                    this.ndPgs.setPosition(v3(0, 430, 0))
                }
                //普通怪物
                else {
                    this.ndPgs.getComponent(Sprite).spriteFrame = this.sfPgsBgs[0]
                    this.sprBar.spriteFrame = this.sfPgsBars[0];

                }
            }
        }



    }
    setHp() {
        if (!this.ndPgs.active) {
            this.ndPgs.active = true;
        }
        this.lbHp.string = this.fromRole ? `${this.fromRole.data.hpNum}/${this.fromRole.data.maxHp}` : '0';
        let range = this.fromRole ? Math.max(0.01, this.fromRole.data.hpNum / this.fromRole.data.maxHp) : 0
        this.sprBar.fillRange = range;
        if (this.fromRole.data.type == E_RoleType.Tower) {
            if (!this.unpdateTowerShowFunc) {
                this.unpdateTowerShowFunc = Tool.throttle((num) => {

                    //城墙出现倒塌的破损口
                    if (num <= 0.3) {
                        if (!this.sprRole.spriteFrame || this.sprRole?.spriteFrame.name != 'tower_03') {
                            xcore.res.bundleLoadSprite(C_Bundle.abGame, './res/img_unpack/tower_03', this.sprRole)
                        }
                    }
                    //城墙出现破损
                    else if (num <= 0.5) {
                        if (!this.sprRole.spriteFrame || this.sprRole?.spriteFrame.name != 'tower_02') {
                            xcore.res.bundleLoadSprite(C_Bundle.abGame, './res/img_unpack/tower_02', this.sprRole)
                        }
                    }
                    //城墙正常状态
                    else {
                        if (!this.sprRole.spriteFrame || this.sprRole?.spriteFrame.name != 'tower_01') {
                            xcore.res.bundleLoadSprite(C_Bundle.abGame, './res/img_unpack/tower_01', this.sprRole)
                        }
                    }

                }, 3000)

            }
            this.unpdateTowerShowFunc(range)

        }

    }
    getCompAnimNode() {
        return this.anim?.node
    }
    setRankInfo(offset: Vec3 = v3(0, 0)) {
        if (this.fromRole && this.fromRole.data.type == E_RoleType.Hero && this.lbDesc) {
            let rank = this.fromRole.getUserRank();
            this.lbDesc.string = rank < 0 ? '' : `全服第${rank + 1}`;
            let lev = rank + 1;
            let pathName;
            this.tempLevTxtPos.set(0, 0);
            let rankConfig = ConfigHelper.getInstance().getRankRewardByRankIndex(lev);
            if (rankConfig) {
                if (rankConfig.rewardType == 1 && rankConfig.rewardId) {
                    this.updateWingAnim(rankConfig.rewardId);
                } else if (this.sprWingAnim) {
                    this.sprWingAnim.spriteFrame = null;
                    this.sprWingAnim.getComponent(Animation).enabled = false;
                }
            } else if (this.sprWingAnim) {
                this.sprWingAnim.spriteFrame = null;
                this.sprWingAnim.getComponent(Animation).enabled = false;
            }
            if (lev <= 0) {
                this.tempLevBgPos.set(0, 0);
                pathName = null;/* 'game_role_levbg00' */
                this.lbDesc.enableOutline = false;

            }
            else if (lev == 1) {
                this.tempLevBgPos.set(0, 10 + offset.y)
                pathName = 'game_role_levbg01';
                this.lbDesc.enableOutline = true;
                this.tempColorOutline.set(180, 38, 38, 255)
                this.tempLevTxtPos.set(0, -9)
                this.lbDesc.outlineColor = this.tempColorOutline;
            } else if (lev == 2) {
                this.tempLevBgPos.set(0, 2 + offset.y)
                pathName = 'game_role_levbg02';
                this.tempLevTxtPos.set(0, 0)
                this.lbDesc.enableOutline = true;
                this.tempColorOutline.set(38, 58, 180, 255)
                this.lbDesc.outlineColor = this.tempColorOutline;
            } else if (lev == 3) {
                this.tempLevBgPos.set(0, 2 + offset.y)
                pathName = 'game_role_levbg03';
                this.tempLevTxtPos.set(0, -2)
                this.lbDesc.enableOutline = true;
                this.tempColorOutline.set(135, 63, 35, 255)
                this.lbDesc.outlineColor = this.tempColorOutline;
            } else if (lev <= 100) {
                this.tempLevBgPos.set(0, -2 + offset.y)
                pathName = 'game_role_levbg04';
                this.lbDesc.enableOutline = true;
                this.tempColorOutline.set(71, 71, 100, 255)
                this.tempLevTxtPos.set(0, 4)
                this.lbDesc.outlineColor = this.tempColorOutline;
            } else {
                this.tempLevBgPos.set(0, 0 + offset.y)
                pathName = 'game_role_levbg00';
                this.lbDesc.enableOutline = false;
            }
            let path = pathName ? `./res/img_unpack/${pathName}` : null;
            //前100名次
            if (path) {
                xcore.res.bundleLoadSprite(C_Bundle.abGame, path, this.sprLevBg)
                this.ndUserData.setPosition(v3(0, 42 + offset.y))
                this.sprGiftTitle.node.setPosition(v3(0, 150));
                /* this.lbName.node.setPosition(v3(0, 40 + offset.y));
                if (this.ndAvatar) {
                    this.ndAvatar.setPosition(v3(-80, 40 + offset.y));
                } */
            }
            //未上榜
            else {
                this.sprLevBg.spriteFrame = null;
                this.ndUserData.setPosition(v3(0, 20 + offset.y))
                /* this.lbName.node.setPosition(v3(0, 30 + offset.y));
                if (this.ndAvatar) {
                    this.ndAvatar.setPosition(v3(-80, 30 + offset.y));
                } */
            }

            this.sprLevBg.node.setPosition(this.tempLevBgPos);
            this.lbDesc.node.setPosition(this.tempLevTxtPos);
        }
    }
    setGiftTitleInfo(config: any) {
        this.sprGiftTitle.node.active = true;
        let path = `./res/image/${config.path}/${config.picture}`;
        xcore.res.remoteLoadSprite(path, this.sprGiftTitle, size(90, 90))
        this.lbGiftTitle.string = config.jsonId;

    }
    setColor(color?: number, time?: number) {
        this._tempTween && this._tempTween.stop();
        this.colorTime = time || 0;
        if (this.sprRole) {
            if (color == 1) {
                this.sprRole.color = this.tempColorRed
            } else if (color == 2) {
                this.sprRole.color = this.tempColorGreen
            } else {
                this.colorTime = 0;
                this.sprRole.color = this.tempColorDefault

            }
        }
        if (color) {
            log("color::::::", color, time)
        }
    }
    setLev(lev: number) {
        if (this.lbLev) {
            this.lbLev.string = `南天门等级${lev}`;
        }
    }
    async doAttackAnim(cb, atkSpeed: number) {

        this.updateAnim(cb, atkSpeed)
    }
    clear(isTrueDestroy: boolean = false) {
        this.clearAnim();
        if (this.node && this.node.isValid) {
            if (isTrueDestroy) {
                this.node.destroy();
            } else {
                this.node.active = false;
                this.setPosAtonce(v2(-3000, -3000));
            }
        }


    }
    clearAnim() {
        if (this.fromRole?.data?.type == E_RoleType.Tower) return
        if (this.anim) {
            this.sprRole.spriteFrame = null
            this.anim.getComponent(Animation).enabled = false;
        }

        this.setColor();
    }
    setPosAtonce(pos: Vec2) {
        this.node?.setPosition(v3(pos.x, pos.y));
    }


    getFromRole() {
        if (!this.fromRole) return null
        return this.fromRole
    }
    getRoleType() {
        if (!this.fromRole) return null
        return this.fromRole.data.type;
    }


    //角色状态切换动画
    switchState() {
        switch (this.state) {
            case E_RoleState.None:

                break;
            case E_RoleState.Idle:

            case E_RoleState.Move:
                this.updateAnim();
                break;
            case E_RoleState.Attack:
                this.updateAnim();
                break;
            case E_RoleState.Hurt:
                //this.playHit()
                break;
            case E_RoleState.Skill:

                this.updateAnim();
                break;
            case E_RoleState.WaitRelive:

                break;
            case E_RoleState.Dizziness:

                break

            case E_RoleState.Dead:


                break;

            default:
                break;
        }
    }
    onDead() {
        this.setColor();
        this.clearAnim();
        this.node.removeFromParent();
    }
    playHit() {
        //if (this.fromRole.data.type == E_RoleType.Tower) return
        if (this.colorTime > 0) {
            this._tempTween && this._tempTween.stop()
            return
        }
        if (!this._tempTween) {
            let self = this;
            this.tempColorHurt.set(255, 120, 120, 220)
            this._tempTween = new Tween(this.sprRole)
                .to(0.2, { color: this.tempColorHurt })
                .to(0.05, { color: this.tempColorZero })
                .to(0.2, { color: this.tempColorHurt })
                .to(0.2, { color: this.tempColorHurt })
                .to(0.05, { color: this.tempColorZero })
                .to(0.2, { color: this.tempColorHurt })
                .to(0.2, { color: this.tempColorHurt })
                .to(0.05, { color: this.tempColorZero })
                .to(0.2, { color: this.tempColorHurt })
                .call(() => {
                    //self.tempColorHurt.set(255, 255, 255, 255)
                    self.sprRole.color = self.tempColorZero;
                    this._tempTween.stop();
                })
        }

        this._tempTween.stop();

        //this.sprRole.color = this.tempColorHurt
        this._tempTween.start()


    }
    update(dt) {
        if (!this.fromRole || !this.fromRole.data) return
        //无敌状态显示
        if (this.fromRole.data.type == E_RoleType.Tower) {
            /*  if (this.fromRole.data.invincibleTime > 0) {
                 if (!this.animIconInvincible.node.active) {
                     this.animIconInvincible.node.active = true;UnitHurtTips
                 }
                 if (this.lbInvincibleTime) {
                     this.lbInvincibleTime.string = `${Math.ceil(this.fromRole.data.invincibleTime)}`;
                 }
             } else if (this.animIconInvincible.node.active) {
                 this.animIconInvincible.node.active = false;
                 if (this.lbInvincibleTime) {
                     this.lbInvincibleTime.string = ``;
                 }
             } */
            if (this.state != E_RoleState.Dead) {
                if (this.fromRole.data.auotSaveHpTime > 0) {
                    if (this.animAddHp) {
                        if (!this.animAddHp.node.active) {
                            this.animAddHp.node.active = true;
                        }
                    }

                } else if (this.animAddHp.node.active) {
                    this.animAddHp.node.active = false;

                }
            }


            return
        }
        //缩放或方向转变
        if (this.fromRole.data.scale.y != this.anim.node.scale.y || (this.fromRole.data.moveDir > 0 && this.anim.node.scale.x < 0) || (this.fromRole.data.moveDir < 0 && this.anim.node.scale.x > 0)) {
            this.tempScale.set(this.fromRole.data.moveDir * this.fromRole.data.scale.y, this.fromRole.data.scale.y);

        }
        if (this.tempScale != this.anim.node.scale) {
            this.tempPgsScale.set(this.fromRole.data.moveDir / this.anim.node.scale.y, 1 / this.anim.node.scale.y)
            this.anim.node.setScale(this.tempScale);
            this.ndPgs.setScale(this.tempPgsScale);
            this.tempScale2.set(this.tempScale.x / Math.abs(this.tempScale.x), this.tempScale.y / Math.abs(this.tempScale.y))
            this.sprWingAnim.node.setScale(this.tempScale2);
        }


        //这里可以插值实现平滑移动
        if (this.node.position.x != this.fromRole.data.pos.x
            || this.node.position.y != this.fromRole.data.pos.y) {
            //let tempPos = this.node.position.clone();
            //逻辑坐标映射成creator里的坐标,简单除10好了
            this.node.getPosition(this.tempPos);
            this.tempPos = this.tempPos.lerp(v3(this.fromRole.data.pos.x, this.fromRole.data.pos.y,), 0.3);
            this.node.setPosition(this.tempPos);
            //log("this.tempPos", this.tempPos.x, this.tempPos.y)
        }
        if (this.colorTime > 0) {
            this.colorTime -= dt;
            if (this.colorTime <= 0) {
                this.setColor();
            }
        }
        if (this.state != this.fromRole.data.state) {
            this.state = this.fromRole.data.state;
            this.switchState();
        }
    }

    async updateAnim(finishCb?, atkSpeed: number = 1) {

        if (this.state == E_RoleState.Move || this.state == E_RoleState.Attack || this.state == E_RoleState.Idle || this.state == E_RoleState.Skill) {
            let config = ConfigHelper.getInstance().getAnimConfigByJsonId((this.state == E_RoleState.Move || this.state == E_RoleState.Idle) ? this.fromRole.data.moveAnimation : (this.state == E_RoleState.Skill ? this.fromRole.data.skillAnimation : this.fromRole.data.attackAnimation));
            if (!config) {
                warn('config null:', this.fromRole.data.jsonId, this.state, this.fromRole.data.moveAnimation, this.fromRole.data.skillAnimation, this.fromRole.data.attackAnimation)
                return
            }
            let duration = config.duration
            if (atkSpeed < duration) {
                duration = atkSpeed;
            }

            let animaData = {
                'sample': config.sample,
                'duration': duration,
                'speed': 1,
                'wrapMode': config.wrapMode,
                'path': config.path,
                'name': config.name
            }
            let atlasName = animaData.name.split('-')[0] || 'default';
            let atlas = xcore.res.getAtlas(atlasName);

            //配置动画中心位置
            let animUITransform = this.anim.node.getComponent(UITransform);
            let firstWidth = animUITransform.width;
            let firstHeight = animUITransform.height;
            for (let i = 0; i < animaData.sample; i++) {
                let name = `${animaData.name}_${i < 10 ? `0${i}` : i}`;
                let sf = atlas?.getSpriteFrame(name)
                if (!sf) {
                    // let sf = await xcore.res.remoteLoadSprite(xcore.gameData.cospath + `image/${animaData.path}/${name}.png`) //await xcore.res.bundleLoadSprite(C_Bundle.abGame, `./res/anim/${animaData.path}/${name}`)
                    let sf = await xcore.res.bundleLoadSprite('resources', `./res/image/${animaData.path}/${name}`) //await xcore.res.bundleLoadSprite(C_Bundle.abGame, `./res/anim/${animaData.path}/${name}`)
                    xcore.res.addAtlasSprite(atlasName, name, sf);
                    firstWidth = sf.width;
                    firstHeight = sf.height;
                }
            }

            //缩放配置
            if (config.XCenterPoint == undefined || config.XCenterPoint == null) {
                config.XCenterPoint = 0.5;
            } else if (config.XCenterPoint == "") {
                config.XCenterPoint = 0;
            }
            if (config.YCenterPoint == undefined || config.YCenterPoint == null) {
                config.YCenterPoint = 0.5;
            } else if (config.YCenterPoint == "") {
                config.YCenterPoint = 0;
            }



            await Tool.createAnim(this.anim, animaData, atlas);

            animUITransform?.setAnchorPoint(Number(config.XCenterPoint), Number(config.YCenterPoint));



            if (finishCb) {
                finishCb(duration);

            } else if (this.state == E_RoleState.Skill) {
                this.scheduleOnce(() => {
                    if (this.fromRole && this.fromRole.isRoleAlive()) {
                        this.fromRole.setState(E_RoleState.Idle);
                    }
                }, duration / 2)
            }
        }


    }
    async updateWingAnim(jsonId: string) {
        let wingconfig = ConfigHelper.getInstance().getWingConfigByJsonId(jsonId);
        log(wingconfig);
        if (wingconfig) {
            if (wingconfig.animationId) {
                let config = ConfigHelper.getInstance().getAnimConfigByJsonId(wingconfig.animationId);
                if (!config) {
                    return;
                }
                this.sprWingAnim.getComponent(Animation).enabled = true;
                let animaData = {
                    'sample': config.sample,
                    'duration': config.duration,
                    'speed': 1,
                    'wrapMode': config.wrapMode,
                    'path': config.path,
                    'name': config.name
                }
                let atlasName = animaData.name.split('-')[0] || 'default';
                let atlas = xcore.res.getAtlas(atlasName);

                //配置动画中心位置
                let animUITransform = this.sprWingAnim.node.getComponent(UITransform);
                let firstWidth = animUITransform.width;
                let firstHeight = animUITransform.height;
                for (let i = 0; i < animaData.sample; i++) {
                    let name = `${animaData.name}_${i < 10 ? `0${i}` : i}`;
                    let sf = atlas?.getSpriteFrame(name)
                    if (!sf) {
                        // let sf = await xcore.res.remoteLoadSprite(xcore.gameData.cospath + `image/${animaData.path}/${name}.png`) //await xcore.res.bundleLoadSprite(C_Bundle.abGame, `./res/anim/${animaData.path}/${name}`)
                        let sf = await xcore.res.bundleLoadSprite('resources', `./res/image/${animaData.path}/${name}`) //await xcore.res.bundleLoadSprite(C_Bundle.abGame, `./res/anim/${animaData.path}/${name}`)
                        xcore.res.addAtlasSprite(atlasName, name, sf);
                        firstWidth = sf.width;
                        firstHeight = sf.height;
                    }
                }

                //缩放配置
                if (config.XCenterPoint == undefined || config.XCenterPoint == null) {
                    config.XCenterPoint = 0.5;
                } else if (config.XCenterPoint == "") {
                    config.XCenterPoint = 0;
                }
                if (config.YCenterPoint == undefined || config.YCenterPoint == null) {
                    config.YCenterPoint = 0.5;
                } else if (config.YCenterPoint == "") {
                    config.YCenterPoint = 0;
                }

                animUITransform?.setAnchorPoint(Number(config.XCenterPoint), Number(config.YCenterPoint));

                this.sprWingAnim.node.setPosition(v3(10, 210, 0))

                await Tool.createAnim(this.sprWingAnim.getComponent(Animation), animaData, atlas);
                log('wingConfig', config, this.anim.node.position, this.anim.getComponent(UITransform).anchorPoint, this.sprWingAnim.node.position, this.sprWingAnim.node.getComponent(UITransform).anchorPoint)

            }
        }
    }
}


