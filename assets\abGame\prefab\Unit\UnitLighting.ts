import { _decorator, Component, find, Node, v3, Vec3 } from 'cc';
import { EffectMgr } from '../../scripts/EffectMgr';
const { ccclass, property } = _decorator;

@ccclass('UnitLighting')
export class UnitLighting extends Component {

    @property(Node)
    private ndLight: Node = null;

    setData(startPos: Vec3, endPos: Vec3) {
        let distance = Vec3.distance(startPos, endPos)
        let direction = endPos.subtract(startPos);
        let newFacingAngle = Math.atan2(direction.y, direction.x) * 180 / Math.PI;


        this.node.setPosition(startPos);
        this.node.eulerAngles = v3(0, 0, newFacingAngle);
        this.ndLight.scale = v3(1, distance / 100, 1);

        this.scheduleOnce(() => {
           // EffectMgr.getInstance().killLighting(this);
        }, 0.5)
    }
}


