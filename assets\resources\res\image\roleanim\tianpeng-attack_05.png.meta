{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "5ddc7855-1e6b-4bc0-9e3d-96c3d2442a30", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "5ddc7855-1e6b-4bc0-9e3d-96c3d2442a30@6c48a", "displayName": "tianpeng-attack_05", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "5ddc7855-1e6b-4bc0-9e3d-96c3d2442a30", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "5ddc7855-1e6b-4bc0-9e3d-96c3d2442a30@f9941", "displayName": "tianpeng-attack_05", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": -42.5, "offsetY": -12, "trimX": 35, "trimY": 33, "width": 95, "height": 158, "rawWidth": 250, "rawHeight": 200, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-47.5, -79, 0, 47.5, -79, 0, -47.5, 79, 0, 47.5, 79, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [35, 167, 130, 167, 35, 9, 130, 9], "nuv": [0.14, 0.045, 0.52, 0.045, 0.14, 0.835, 0.52, 0.835], "minPos": [-47.5, -79, 0], "maxPos": [47.5, 79, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "5ddc7855-1e6b-4bc0-9e3d-96c3d2442a30@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "5ddc7855-1e6b-4bc0-9e3d-96c3d2442a30@6c48a"}}