[1, ["1emgh7U61ILbqAlp3s3By3@6c48a"], ["_textureSource"], ["cc.SpriteFrame"], 0, [{"name": "huodou-idle_08", "rect": {"x": 145, "y": 119, "width": 625, "height": 457}, "offset": {"x": 2, "y": -59.5}, "originalSize": {"width": 911, "height": 576}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-312.5, -228.5, 0, 312.5, -228.5, 0, -312.5, 228.5, 0, 312.5, 228.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [145, 457, 770, 457, 145, 0, 770, 0], "nuv": [0.15916575192096596, 0, 0.845225027442371, 0, 0.15916575192096596, 0.7934027777777778, 0.845225027442371, 0.7934027777777778], "minPos": {"x": -312.5, "y": -228.5, "z": 0}, "maxPos": {"x": 312.5, "y": 228.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]]