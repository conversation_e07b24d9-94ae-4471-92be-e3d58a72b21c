
import { log } from "cc";

import { E_EVENT, E_GiftMessageType } from "./ConstGlobal";
import { xcore } from "./libs/xcore";
import TimeUtil from "./libs/utils/TimeUtil";
import { ArrayUtil } from "./libs/utils/ArrayUtil";






// 公司局域网环境 String wsUrl = "ws://192.168.5.24:10088/websocket"; 
// 开发环境 String wsUrl = "ws://42.194.174.223:10188/websocket"; 
// 生产环境 String wsUrl = "ws://barrage-game.xiaoyisz.com:10088/websocket";

//生产地址baseurl  https://apig.xiaoyisz.com/live-game-ntm-v1


export default new class Net {
    //登录
    login(channel: string, channelId: string, code: string, userName: string, password: string, appId: string, combatId: string) {
        return xcore.http.post('ga/public/api/login', { channel, channelId, code, userName, password, appId, combatId }).then(res => {
            xcore.http.defaults.headers['Authorization'] = res.data;
        })
    }
    /** 获取服务器时间戳 */
    getServerTimeStamp() {

        return xcore.http.get('ga/public/api/getServerTimeStamp').then(res => TimeUtil.initServerTime(res.data))
    }
    //重新登录
    relogin(channel: string, channelId: string, code: string, userName: string, password: string, appId: string, combatId: string) {
        return xcore.http.post('ga/refreshLogin', { channel, channelId, code, userName, password, appId, combatId }).then(res => {
            xcore.http.defaults.headers['Authorization'] = res.data;
        })
    }

    //玩家进游戏
    roleJoin(nickName: string, avatarUrl: string, playerId: string) {
        return xcore.http.post('ga/player/joinGame', { nickName, avatarUrl, playerId, appId: xcore.gameData.appId, channelId: xcore.gameData.channelId }).then(res => {
            if (!xcore.gameData.oldRankInfo) {
                xcore.gameData.oldRankInfo = {}
            }
            let rankInfo = res.data.userIdScoreVo;
            if (rankInfo && rankInfo.rank != null) {
                rankInfo.rank += 1;
            }
            xcore.gameData.oldRankInfo[`${playerId}`] = rankInfo;

            //console.log("oldrankinfo:", xcore.gameData.oldRankInfo);
        });
    }
    /**
     * 
     * @param key 排行榜key  rank:appId:channelId:{week|month|forever}:YYYY-MM-DD
     * @param playerIds 用户userid[] 
     * @param isJoin 是否是加入游戏时查看
     * @returns 
     */
    getRoleRankInfo(key: string, playerIds: string[], cb?: Function, isJoin: boolean = true) {
        return xcore.http.post('ga/rank/getRankByPlayerIds', { key, playerIds }).then(res => {
            let datas = res.data;
            cb && cb(datas)
            if (isJoin) {
                datas.forEach(user => {

                    //排行榜前100名
                    if (user.rank >= 0 && user.rank <= 99) {

                        xcore.event.raiseEvent(E_EVENT.GiftMessage, {
                            type: E_GiftMessageType.RankBoss,
                            userId: user.playerId,
                            name: user.nickName,
                            avatar: user.avatarUrl,
                            num: 1,
                            lev: user.rank
                        })
                    }

                });
            }


        })
    }
    //主播信息
    getLiverInfo() {
        return xcore.http.get('ga/anchor/getAnchorInfo').then(res => xcore.gameData.baseInfo = res.data);
    }

    //战斗id
    getCombatId() {
        return xcore.http.post('ga/public/api/combatId').then(res => xcore.gameData.combatId = res.data);
    }

    //开始游戏
    startFight(combatId: string, level: number) {
        return xcore.http.post('ga/api/combat/tiktok/start', { combatId, level });
    }

    settleFight(combatId: string, userList: any[], giftList: any[], scoreList: any[], redScore: number = 0, blueScore: number = 0, maxLevel: number = 0) {
        return xcore.http.post('ga/api/combat/tiktok/end', { combatId, userList, giftList, scoreList, redScore, blueScore, maxLevel });
    }


    //用户排行榜信息
    //积分排行榜的key: "rank:" + appId + ":" + channelId + ":week:"+2024-10-26    请求地址  /ga/rank/getRnak
    //玩家通关排行榜的key:  "rank:" + appId + ":" + channelId + ":level:week:"+2024-10-26  请求地址 /ga/rank/getRnak
    //rank:ttdb73adce4447fe4a10:1006:week:2024-10-26
    getRankInfo(key: string, start: number, end: number) {
        return xcore.http.get('ga/rank/getRank', { params: { key, start, end } }).then(res => {
            //通关排名
            if (key.indexOf('level') != -1) {
                if (!xcore.gameData.roundRankInfo) {
                    xcore.gameData.roundRankInfo = res.data;
                } else {
                    if (res.data.length > 0) {
                        let arr = ArrayUtil.noRepeated(xcore.gameData.roundRankInfo.concat(res.data))
                        xcore.gameData.roundRankInfo = arr;
                    } else {
                        xcore.gameData.roundRankInfo = xcore.gameData.roundRankInfo;
                    }

                }

            } else if (key.indexOf('gift') != -1) {
                if (!xcore.gameData.giftRankInfo) {
                    xcore.gameData.giftRankInfo = res.data;
                } else {
                    if (res.data.length > 0) {
                        let arr = ArrayUtil.noRepeated(xcore.gameData.giftRankInfo.concat(res.data))
                        xcore.gameData.giftRankInfo = arr;
                    } else {
                        xcore.gameData.giftRankInfo = xcore.gameData.giftRankInfo;
                    }

                }
            }
            // 积分排名
            else {
                if (!xcore.gameData.scoreRankInfo) {
                    xcore.gameData.scoreRankInfo = res.data;
                } else {
                    if (res.data.length > 0) {
                        let arr = ArrayUtil.noRepeated(xcore.gameData.scoreRankInfo.concat(res.data))
                        xcore.gameData.scoreRankInfo = arr;
                    } else {
                        xcore.gameData.scoreRankInfo = xcore.gameData.scoreRankInfo;
                    }


                }

            }
        })
    }

    //主播
    //主播通关排行榜的key: "rank:" + appId + ":" + channelId + ":anchor:level:week:"+2024-10-26 请求地址  /ga/anchor/rank/getRank
    getLiverRankInfo(key: string, start: number, end: number) {
        return xcore.http.get('ga/anchor/rank/getRank', { params: { key, start, end } }).then(res => {
            if (!xcore.gameData.liverRankInfo) {
                xcore.gameData.liverRankInfo = res.data;
            } else {
                if (res.data.length > 0) {
                    let arr = ArrayUtil.noRepeated(xcore.gameData.liverRankInfo.concat(res.data))

                    xcore.gameData.liverRankInfo = arr
                } else {
                    xcore.gameData.liverRankInfo = xcore.gameData.liverRankInfo;
                }


            }

        })
    }
    getUserRankInfoByUserIds(key: string, playerIds: string[]) {
        return xcore.http.post('ga/rank/getRankByPlayerIds', { key, playerIds });
    }

    updateTokenToIo() {
        return xcore.http.get('ga/api/loginIoGame')

    }
    updateAnchorRankGameLev(key: string, id: string) {
        /*  let key = ConfigHelper.getInstance().getLiverKeyByLev(); */
        let playerIds = [id];
        return xcore.http.post('ga/anchor/rank/getRankByPlayerIds', { key, playerIds }).then(res => {
            xcore.gameData.gameLev = res.data[0].score
        });

    }
    //礼物置顶
    updateGiftTopInfo(combatId: string, gifts: string[]) {
        return xcore.http.post('ga/api/combat/tiktok/gift/top', { combatId, gifts }).then(res => xcore.gameData.giftInfo = res.data);
    }

    //主播刷礼物
    //https://dev-api.xiaoyisz.com/live-game-notify-service/ga/public/api/tiktok/notify/1006

    //查碎片数量
    getDebrisInfo(playerIds: string, cb?) {
        return xcore.http.get('ga/player/getPropFragment', { params: { playerIds } }).then(res => {
            let datas = res.data;
            cb && cb(datas)
        })
    }
    //添加碎片数量
    addDebris(playerId: string, num: number, prop: string) {
        return xcore.http.get('/ga/player/addPropFragment', { params: { playerId, num, prop } })
    }
    //获取用户皮肤信息
    getSKinInfo(playerId: string, cb?) {
        return xcore.http.get('ga/player/getProp', { params: { playerId } }).then(res => {
            let datas = res.data;
            cb && cb(datas);
        })
    }
    //兑换皮肤
    exchangeSkin(playerId: string, prop: string, num: number, time: number, fragment: string) {
        return xcore.http.get('ga/player/propFragmentToProp', { params: { playerId, prop, num, time, fragment } })
    }
    /**
     * 直接获取皮肤 不用消耗碎片
     * @param playerId 
     * @param type 0:抽奖,1:赠送,2:签到,3:兑换,4积分解锁
     * @param props {num,time,prop}
     * @returns 
     */
    rewardSkin(playerId: string, type: number, props: any[]) {
        return xcore.http.post('ga/player/setProp', { playerId, type, props })
    }

    setSelectSkin(playerId: string, prop: string) {
        return xcore.http.get('ga/player/setPropUse', { params: { playerId, prop } })
    }

    getDimondInfo(playerId: string) {
        return xcore.http.get('ga/player/getBaozhu', { params: { playerId } })
    }

    addDimond(playerId: string, type: string, num: number) {
        return xcore.http.get('ga/player/addBaozhu', { params: { playerId, type, num } })
    }

    getPlayerOperate(playerId: string, key: string) {

        if (xcore.gameData.playersOperation[playerId] && xcore.gameData.playersOperation[playerId][key]) {
            return xcore.gameData.playersOperation[playerId][key]
        }

        return xcore.http.get('ga/player/getConfig', { params: { playerId, key } }).then(res => {
            if (!xcore.gameData.playersOperation[playerId]) {
                xcore.gameData.playersOperation[playerId] = {}
            }
            xcore.gameData.playersOperation[playerId][key] = res.data;

            console.log('getPlayerOperate', playerId, xcore.gameData.playersOperation[playerId][key])

        })
    }
    setPlayerOperate(playerId: string, key: string, value: string) {
        return xcore.http.get('ga/player/setConfig', { params: { playerId, key, value } }).then(res => {
            if (!xcore.gameData.playersOperation[playerId]) {
                xcore.gameData.playersOperation[playerId] = {}
            }
            xcore.gameData.playersOperation[playerId][key] = value;
            console.log("setPlayerOperate", playerId, key, xcore.gameData.playersOperation[playerId][key])
        })
    }
    setPlayersOperate(datas: { playerId: string, key: string, value: string }[]) {
        return xcore.http.post('ga/player/setConfigs', datas)

    }
    /**扣除灵韵 */
    costGold(num: number, openId: string) {
        return xcore.http.get('ga/rank/reduceGold', { params: { num, openId } })

    }
}

