System.register("chunks:///_virtual/abMain",["./Main.ts"],(function(){return{setters:[null],execute:function(){}}}));

System.register("chunks:///_virtual/Main.ts",["./rollupPluginModLoBabelHelpers.js","cc","./xcore.ts","./ConstGlobal.ts","./Tool.ts"],(function(e){var n,t,i,o,r,a,s,l,c,u,d,b,h,f,p,g,m,y,w;return{setters:[function(e){n=e.applyDecoratedDescriptor,t=e.inheritsLoose,i=e.initializerDefineProperty,o=e.assertThisInitialized,r=e.asyncToGenerator,a=e.regeneratorRuntime},function(e){s=e.cclegacy,l=e._decorator,c=e.But<PERSON>,u=e.Label,d=e.Node,b=e.log,h=e.Component},function(e){f=e.xcore,p=e.gameVersion},function(e){g=e.C_View,m=e.C_Bundle,y=e.C_Scene},function(e){w=e.default}],execute:function(){var M,k,_,v,B,L,z,C,S,T,D,I,V,N,P,G,R,U,F,x,E,H,J,Q,j,A,O;s._RF.push({},"fe5bfG0oEJEgpUaQhPtGIzQ","Main",void 0);var q=l.ccclass,K=l.property;e("Main",(M=q("Main"),k=K(c),_=K(c),v=K(c),B=K(c),L=K(c),z=K(c),C=K(c),S=K(c),T=K(u),D=K(d),I=K(d),V=K([d]),M((G=n((P=function(e){function n(){for(var n,t=arguments.length,r=new Array(t),a=0;a<t;a++)r[a]=arguments[a];return n=e.call.apply(e,[this].concat(r))||this,i(n,"btnJoin",G,o(n)),i(n,"btnSetting",R,o(n)),i(n,"btnRank",U,o(n)),i(n,"btnModeEasy",F,o(n)),i(n,"btnModeDifficult",x,o(n)),i(n,"btnModeHell",E,o(n)),i(n,"btnModeBoss",H,o(n)),i(n,"btnModePass",J,o(n)),i(n,"lbVersion",Q,o(n)),i(n,"ndUITop",j,o(n)),i(n,"ndUIBottom",A,o(n)),i(n,"ndIconSelects",O,o(n)),n.doFinishLoadingPgsCmd=null,n._gameMode=0,n._time=0,n}t(n,e);var s=n.prototype;return s.onLoad=function(){var e=r(a().mark((function e(){var n,t=this;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this.ndUITop.active=!1,this.ndUIBottom.active=!1,n={resLoading:{sprBg:{bundleName:"resources",path:"img_unpack/ready_loading_bg"},sprLogo:{bundleName:"resources",path:"img_unpack/ready_loading_logo"}},resLoadingPgs:{sprBg:{bundleName:"resources",path:"img_unpack/ready_loadingpgs_bg"},sprBar:{bundleName:"resources",path:"img_unpack/ready_loadingpgs_bar"}}},e.next=5,f.ui.showLoadingUI(n,this.onLoginFinish.bind(this));case 5:return this.doFinishLoadingPgsCmd=e.sent,e.next=8,this.preloadGameRes();case 8:this.scheduleOnce((function(){t.doFinishLoadingPgsCmd&&t.doFinishLoadingPgsCmd(),t.ndUITop.active=!0,t.ndUIBottom.active=!0,t.addButtonListener()}),2),f.sound.play("./res/sound/bgm",f.gameData.soundData.music,"resources",!0),this.lbVersion.string="v"+p;case 11:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}(),s.addButtonListener=function(){this.btnJoin.node.on("click",this.gameStart,this),this.btnModeEasy.node.on("click",this.switchMode.bind(this,0),this),this.btnModeDifficult.node.on("click",this.switchMode.bind(this,1),this),this.btnModeHell.node.on("click",this.switchMode.bind(this,2),this),this.btnRank.node.on("click",(function(){f.ui.addView(g.ViewRank)}),this),this.btnSetting.node.on("click",(function(){f.ui.addView(g.ViewSetting)}),this),this.btnModePass.node.on("click",(function(){f.ui.addView(g.ViewSelectGameMode)}),this),this.btnModeBoss.node.on("click",(function(){f.ui.addView(g.ViewSelectGameType)}),this),this.node.getChildByName("btnTest")&&this.node.getChildByName("btnTest").on("click",this.openDebug,this)},s.removeListener=function(){this.btnJoin.node.off("click"),this.btnModeEasy.node.off("click"),this.btnModeDifficult.node.off("click"),this.btnModeHell.node.off("click"),this.btnRank.node.off("click"),this.btnSetting.node.off("click"),this.node.getChildByName("btnTest")&&this.node.getChildByName("btnTest").off("click")},s.openDebug=function(){this._time+=1,this._time>10&&(this.node.getChildByName("btnTest")&&(this.node.getChildByName("btnTest").active=!1),w.openDevTool(),b("openDebug"))},s.gameStart=function(){this.removeListener(),b("游戏难度",this._gameMode),f.ui.switchScene(m.abGame,y.Game)},s.switchMode=function(e){f.gameData.gameMode=e,this.gameStart()},s.preloadGameRes=function(){var e=r(a().mark((function e(){return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),s.onLoginFinish=function(){},n}(h)).prototype,"btnJoin",[k],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),R=n(P.prototype,"btnSetting",[_],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),U=n(P.prototype,"btnRank",[v],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),F=n(P.prototype,"btnModeEasy",[B],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),x=n(P.prototype,"btnModeDifficult",[L],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),E=n(P.prototype,"btnModeHell",[z],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),H=n(P.prototype,"btnModeBoss",[C],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),J=n(P.prototype,"btnModePass",[S],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Q=n(P.prototype,"lbVersion",[T],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),j=n(P.prototype,"ndUITop",[D],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),A=n(P.prototype,"ndUIBottom",[I],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),O=n(P.prototype,"ndIconSelects",[V],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return[]}}),N=P))||N));s._RF.pop()}}}));

(function(r) {
  r('virtual:///prerequisite-imports/abMain', 'chunks:///_virtual/abMain'); 
})(function(mid, cid) {
    System.register(mid, [cid], function (_export, _context) {
    return {
        setters: [function(_m) {
            var _exportObj = {};

            for (var _key in _m) {
              if (_key !== "default" && _key !== "__esModule") _exportObj[_key] = _m[_key];
            }
      
            _export(_exportObj);
        }],
        execute: function () { }
    };
    });
});