[1, ["52fHu7D8hGm5vLaoALoXCl", "57UgcWSMhKGYrPQcn4d3+w@f9941", "11GXWtY2NJP4Ag0Z1AgJKY@6c48a", "ccLysvHmVK5oMLazfZZm+V@f9941", "bbYRJ74X1KKL6d80jHcozB@f9941", "69qH8Ge/pO67EE1Ap1T+cT@f9941", "11GXWtY2NJP4Ag0Z1AgJKY@f9941", "23+FQZT3RLNYkK76x09RAB@f9941", "41Xr0ILK1KeZhrAveJ6ptp@f9941", "26hA86zmVLKYZ30Cd+lOot@f9941", "44GFel8ChP9Z5zkilwkTkq@f9941", "3bvJBntRxNdLTqkOiS7Nx7@f9941", "bbYRJ74X1KKL6d80jHcozB@6c48a", "ccLysvHmVK5oMLazfZZm+V@6c48a"], ["node", "_spriteFrame", "_font", "_textureSource", "_userDefinedFont", "root", "lbKillDesc", "ndKill", "sprReleaseSkill", "ndReleaseSkill", "lbGiftNum", "sprGift", "ndGift", "sprBigSkill", "ndBigSkill", "lbSmallSkillDesc", "lbSmallSkillNum", "ndSmallSkill", "spr<PERSON><PERSON><PERSON>", "ndAvatar", "lbDesc", "lbName", "sprPgsBar", "lbPgs", "ndPgs", "ndDetail", "ndFrame02", "ndFrame01", "data"], [["cc.Sprite", ["_sizeMode", "_type", "_fillRange", "node", "__prefab", "_spriteFrame"], 0, 1, 4, 6], ["cc.Node", ["_name", "_layer", "_active", "_components", "_prefab", "_children", "_parent", "_lpos"], 0, 9, 4, 2, 1, 5], "cc.SpriteFrame", ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_lineHeight", "_isSystemFontUsed", "_enableOutline", "_outlineWidth", "node", "__prefab", "_color", "_outlineColor"], -4, 1, 4, 5, 5], ["cc.Node", ["_name", "_layer", "_parent", "_components", "_prefab", "_lpos"], 1, 1, 12, 4, 5], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["25fb0bdkxdKKKktxQuqB5M3", ["node", "__prefab", "ndFrame01", "ndFrame02", "ndDetail", "ndPgs", "lbPgs", "sprPgsBar", "lbName", "lbDesc", "ndAvatar", "spr<PERSON><PERSON><PERSON>", "ndSmallSkill", "lbSmallSkillNum", "lbSmallSkillDesc", "ndBigSkill", "sprBigSkill", "ndGift", "sprGift", "lbGiftNum", "ndReleaseSkill", "sprReleaseSkill", "ndKill", "lbKillDesc"], 3, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.Layout", ["_resizeMode", "_layoutType", "_spacingY", "node", "__prefab"], 0, 1, 4], ["cc.Mask", ["_type", "node", "__prefab"], 2, 1, 4], ["cc.Graphics", ["node", "__prefab", "_fillColor"], 3, 1, 4, 5], ["cc.RichText", ["_lineHeight", "_string", "_fontSize", "_isSystemFontUsed", "node", "__prefab"], -1, 1, 4]], [[7, 0, 2], [9, 0, 1, 2, 3, 4, 5, 5], [3, 0, 1, 2, 1], [1, 0, 1, 6, 5, 3, 4, 7, 3], [5, 0, 1, 2, 3, 4, 5, 3], [3, 0, 1, 1], [1, 0, 1, 6, 3, 4, 7, 3], [0, 3, 4, 5, 1], [5, 0, 1, 2, 3, 4, 3], [3, 0, 1, 2, 3, 1], [0, 3, 4, 1], [1, 0, 2, 1, 6, 5, 3, 4, 7, 4], [0, 0, 3, 4, 5, 2], [4, 0, 1, 2, 3, 4, 7, 8, 9, 6], [4, 0, 1, 2, 3, 4, 5, 7, 8, 9, 10, 7], [13, 0, 1, 2, 3, 4, 5, 5], [6, 0, 2], [1, 0, 1, 5, 3, 4, 3], [8, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 1], [10, 0, 1, 2, 3, 4, 4], [0, 1, 2, 3, 4, 3], [0, 0, 3, 4, 2], [11, 0, 1, 2, 2], [12, 0, 1, 2, 1], [4, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 8]], [[[{"name": "game_giftmessage_title01", "rect": {"x": 0, "y": 0, "width": 133, "height": 66}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 133, "height": 66}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-66.5, -33, 0, 66.5, -33, 0, -66.5, 33, 0, 66.5, 33, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 66, 133, 66, 0, 0, 133, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -66.5, "y": -33, "z": 0}, "maxPos": {"x": 66.5, "y": 33, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [2], 0, [0], [3], [2]], [[[16, "UnitGiftMessage"], [17, "UnitGiftMessage", 33554432, [-26, -27, -28, -29, -30, -31, -32, -33, -34], [[9, -2, [0, "bfcpdpGYVCbqTI0RjSzg8/"], [5, 700, 1000], [0, 0, 0]], [18, -25, [0, "94GNXkpIVHs5HOB1CXexgu"], -24, -23, -22, -21, -20, -19, -18, -17, -16, -15, -14, -13, -12, -11, -10, -9, -8, -7, -6, -5, -4, -3]], [1, "bd2kMC9khChKK6TSdxT1D4", null, null, null, -1, 0]], [3, "ndDetail", 33554432, 1, [-37, -38, -39], [[2, -35, [0, "ecL84yCLtLNKMz5Kek8kIE"], [5, 100, 135.24]], [19, 1, 2, 6, -36, [0, "14gxyn1g5EU5q47PsZocN8"]]], [1, "53UPPYSVVDE4jvOTZRpkel", null, null, null, 1, 0], [1, 334.33299999999997, 72.36899999999991, 0]], [3, "ndPgs", 33554432, 2, [-42, -43], [[2, -40, [0, "906R9njL1Kx7c+5bDhtWpK"], [5, 193, 30]], [12, 0, -41, [0, "baZUBO1dZH67Kdz9GH/Thh"], 4]], [1, "11vIklB4BLQKJykEYTjdVP", null, null, null, 1, 0], [1, -75.35300000000007, 8.820000000000007, 0]], [3, "ndAvatar", 33554432, 1, [-45, -46, -47], [[5, -44, [0, "ec1pekWS1Hf46wwmHnFXnc"]]], [1, "98z86VJQ5NUJXm/AIJEQ5O", null, null, null, 1, 0], [1, 344.586, 91.84500000000003, 0]], [3, "ndGift", 33554432, 1, [-49, -50], [[5, -48, [0, "bcYEOyBTVDFpl/Rwi/TB14"]]], [1, "fdxuvrAK9NuIB2tf62xKo4", null, null, null, 1, 0], [1, 300, 100, 0]], [11, "ndFrame02", false, 33554432, 1, [-53], [[2, -51, [0, "98yGqxO4hAvq1Qdir9ETFg"], [5, 531, 527]], [7, -52, [0, "abo8Od9BtJKKxfqWZE0ppn"], 1]], [1, "d5+fnpZO1GGZmbZujK092K", null, null, null, 1, 0], [1, 273.55600000000004, 261.7370000000001, 0]], [3, "sprFrame01", 33554432, 1, [-55, -56], [[2, -54, [0, "3fw0+PhLtEQKrhVUmZb42m"], [5, 674, 178]]], [1, "9f4KGP+XFCF4wYROFTGeUg", null, null, null, 1, 0], [1, 344.586, 91.84500000000003, 0]], [3, "Node", 33554432, 4, [-60], [[5, -57, [0, "c81LXFCG5Ltp1HAnwF3wNm"]], [22, 1, -58, [0, "abn8SDwzVBRrd+52soVHAT"]], [23, -59, [0, "d0oNAR7eNPopJ0mXcnXR2z"], [4, 16777215]]], [1, "497hxxw+RH3Z9HwP5jzQ2a", null, null, null, 1, 0], [1, -259.885, 2.2737367544323206e-13, 0]], [3, "ndSmallSkill", 33554432, 1, [-62, -63], [[5, -61, [0, "f3k+yHzsVEQoX4mp2S8uPc"]]], [1, "b2t9Y//OlLNojscczqkvE8", null, null, null, 1, 0], [1, 300, 100, 0]], [3, "ndReleaseSkill", 33554432, 1, [-65], [[5, -64, [0, "c97aNgKQpALYYVj6pdxLzW"]]], [1, "a8II1avINNDKJ9sqr/uCIK", null, null, null, 1, 0], [1, 300, 100, 0]], [11, "ndKill", false, 33554432, 1, [-67], [[5, -66, [0, "e3vie2/pRIU7kuDaUzEQLC"]]], [1, "42lj6q3vFDKZQSncZqQ7dz", null, null, null, 1, 0], [1, 300, 100, 0]], [6, "game_giftmessage_title02", 33554432, 6, [[2, -68, [0, "f6RAjB+uVKirlsu2fxLBkU"], [5, 232, 59]], [7, -69, [0, "0eN/1u+91E7ZSrmd3XrjzJ"], 0]], [1, "c109IZlJdARbW3+u+v7eFF", null, null, null, 1, 0], [1, -126.65499999999997, 215.38599999999997, 0]], [6, "game_giftmessage_frame02", 33554432, 7, [[2, -70, [0, "dbzHJ9J3BA5bebUcZJ57qm"], [5, 799, 190]], [7, -71, [0, "59CDtAH0tJqb6kSrKXZdP6"], 2]], [1, "9ciSSOW0ZN2ojVQ8anNuF0", null, null, null, 1, 0], [1, 65.09199999999998, 7.888999999999896, 0]], [6, "game_giftmessage_title01", 33554432, 7, [[2, -72, [0, "0cPX8GxPdBHJlSytpBSgN7"], [5, 133, 66]], [7, -73, [0, "9egySjwXhFVI7J8bcbBNYl"], 3]], [1, "03xrHpempFfLbG50JTeS3o", null, null, null, 1, 0], [1, -238.06799999999998, 113.52299999999991, 0]], [4, "lbName", 33554432, 2, [[[9, -74, [0, "26S/I22fNGN5ee03uLX0i3"], [5, 154, 37.8], [0, 0, 0.5]], -75], 4, 1], [1, "45+yjGmzRAZJEWxZuNThiz", null, null, null, 1, 0], [1, -171.639, 48.72000000000003, 0]], [8, "sprBar", 33554432, 3, [[[2, -76, [0, "80L9SuMNJPf7B51P82/t/E"], [5, 189, 25]], -77], 4, 1], [1, "9flCniPzVK37BXPqOlgNOQ", null, null, null, 1, 0]], [8, "lbPgs", 33554432, 3, [[[2, -78, [0, "09fZgheOFHXoArE5kAMHy6"], [5, 35.39796447753906, 27.72]], -79], 4, 1], [1, "e3IB9bXj1OZp4Uk1xGlWh+", null, null, null, 1, 0]], [4, "lbDesc", 33554432, 2, [[[2, -80, [0, "bc2jPQIM1LKYQLWovPbrTW"], [5, 206.78399658203125, 55.44]], -81], 4, 1], [1, "e5p+xWfRdItKuH1E9febIT", null, null, null, 1, 0], [1, -77.21199999999999, -39.899999999999864, 0]], [6, "sprAvatarBg", 33554432, 4, [[2, -82, [0, "73yiu0W05CjLXjA4aV8hHv"], [5, 113, 113]], [7, -83, [0, "368q5b/L5F1JazQfWzzj6X"], 5]], [1, "04prPdGo1PlYstJfDLLEyv", null, null, null, 1, 0], [1, -257.885, 0, 0]], [8, "spr<PERSON><PERSON><PERSON>", 33554432, 8, [[[2, -84, [0, "f6IZhiUxlJ7aYMkEGVzHDu"], [5, 104, 104]], -85], 4, 1], [1, "a00dIl5U5BApC/se7/AV7t", null, null, null, 1, 0]], [6, "sprAvatarMask", 33554432, 4, [[2, -86, [0, "c3bmuDUGpAMrRVukpbewK7"], [5, 104, 104]], [12, 0, -87, [0, "08N4r3GWBItZvqFFb8FEps"], 6]], [1, "ecrELB4fNIU4QIoAnC4riV", null, null, null, 1, 0], [1, -257.885, 0, 0]], [4, "lbSmallSkillNum", 33554432, 9, [[[2, -88, [0, "5dm3CsT9dKTqu/KOmuU2r7"], [5, 314.1199645996094, 83.6]], -89], 4, 1], [1, "7dAgCNSIFP+LYTL6g4US/U", null, null, null, 1, 0], [1, 210.75800000000004, 32.42900000000009, 0]], [4, "lbSmallSkillDesc", 33554432, 9, [[[2, -90, [0, "c0sFqlIsNLSp71Bd5lxOAx"], [5, 214, 79.6]], -91], 4, 1], [1, "682ZjMnhxLvKEsZr1xUQj9", null, null, null, 1, 0], [1, 210.75800000000004, -34.82099999999991, 0]], [3, "ndBigSkill", 33554432, 1, [-93], [[5, -92, [0, "beLi7ZTOxEhq5h7nalIfTR"]]], [1, "83S36e3g1MEa5GmFTVXdJO", null, null, null, 1, 0], [1, 300, 100, 0]], [4, "sprBigSkill", 33554432, 24, [[[2, -94, [0, "c9SPQR+55K1YSzO3d5jze1"], [5, 40, 36]], -95], 4, 1], [1, "eeYcFIlqdIkqIl5xb9Mrqj", null, null, null, 1, 0], [1, -12.100000000000023, 217.25700000000006, 0]], [4, "sprGift", 33554432, 5, [[[2, -96, [0, "b0BItL2gZJH5AKUIpx0Msy"], [5, 40, 36]], -97], 4, 1], [1, "e178x3QyFEcL6U7n4efHSg", null, null, null, 1, 0], [1, 204.3510000000001, -1.8130000000001019, 0]], [4, "lbGiftNum", 33554432, 5, [[[9, -98, [0, "a418jhAOFEpqETxAcCYLMw"], [5, 57.404998779296875, 79.6], [0, 0, 0.5]], -99], 4, 1], [1, "05ShgqW6JKC5ZvdtfSJBc3", null, null, null, 1, 0], [1, 275.876, -35.19599999999991, 0]], [4, "sprReleaseSkill", 33554432, 10, [[[2, -100, [0, "ad06KoBXVKja7C0HNpN0zn"], [5, 40, 36]], -101], 4, 1], [1, "be93FD6K9GdK7YRQMoK2GD", null, null, null, 1, 0], [1, 207.39200000000005, -1.8130000000001019, 0]], [4, "lbKillDesc", 33554432, 11, [[[2, -102, [0, "6fen1yki9PbrKXAPxn0JSR"], [5, 101.39997863769531, 37.8]], -103], 4, 1], [1, "3bR1w1OPlL2KizUVNhOuZq", null, null, null, 1, 0], [1, -11.701000000000022, -25.045000000000073, 0]], [13, "玩家一般七个字", 22, 22, 30, false, 15, [0, "26knUwXFVJU5t47k/BB/5q"], [4, 4294965492]], [20, 3, 0.5, 16, [0, "92izrqmlNGrpdA9OgV5asr"]], [13, "0/5", 22, 22, 22, false, 17, [0, "85EU9LFoNAkJkSlD0XZH3/"], [4, 4294965492]], [15, 44, "<color=#FFE348>朱雀扇释放3级天火</color>", 24, false, 18, [0, "60uJWKCP9F64CyUMnP9/wz"]], [21, 0, 20, [0, "e2FjyDfSlMVI+iIBEMbHnX"]], [24, "闪电X9999", 60, 60, 60, false, true, 4, 22, [0, "e8YhIYoE5DmK+QwIdl6qMN"], [4, 4282967039], [4, 4280098273]], [14, "闪电多段攻击", 35, 35, 60, false, true, 23, [0, "1bNOqd3YBB8rrt898RLRCW"], [4, 4282967039], [4, 4280098273]], [10, 25, [0, "25NqAtF6FEK5cLNc/bXKpm"]], [10, 26, [0, "b9oi4vUqhJF4X2QMGrKHro"]], [14, "x1", 55, 55, 60, false, true, 27, [0, "909PvD0QxEAYx5+vPNo2pn"], [4, 4282967039], [4, 4280098273]], [10, 28, [0, "3chXwUG89KYYetevmAd6pT"]], [15, 30, "<color=#FFE348>击杀boss</color> ", 24, false, 29, [0, "87d65gUVRB7aOj7jA1GUeq"]]], 0, [0, 5, 1, 0, 0, 1, 0, 6, 41, 0, 7, 11, 0, 8, 40, 0, 9, 10, 0, 10, 39, 0, 11, 38, 0, 12, 5, 0, 13, 37, 0, 14, 5, 0, 15, 36, 0, 16, 35, 0, 17, 9, 0, 18, 34, 0, 19, 4, 0, 20, 33, 0, 21, 30, 0, 22, 31, 0, 23, 32, 0, 24, 3, 0, 25, 2, 0, 26, 6, 0, 27, 7, 0, 0, 1, 0, -1, 6, 0, -2, 7, 0, -3, 2, 0, -4, 4, 0, -5, 9, 0, -6, 24, 0, -7, 5, 0, -8, 10, 0, -9, 11, 0, 0, 2, 0, 0, 2, 0, -1, 15, 0, -2, 3, 0, -3, 18, 0, 0, 3, 0, 0, 3, 0, -1, 16, 0, -2, 17, 0, 0, 4, 0, -1, 19, 0, -2, 8, 0, -3, 21, 0, 0, 5, 0, -1, 26, 0, -2, 27, 0, 0, 6, 0, 0, 6, 0, -1, 12, 0, 0, 7, 0, -1, 13, 0, -2, 14, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, -1, 20, 0, 0, 9, 0, -1, 22, 0, -2, 23, 0, 0, 10, 0, -1, 28, 0, 0, 11, 0, -1, 29, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, -2, 30, 0, 0, 16, 0, -2, 31, 0, 0, 17, 0, -2, 32, 0, 0, 18, 0, -2, 33, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, -2, 34, 0, 0, 21, 0, 0, 21, 0, 0, 22, 0, -2, 35, 0, 0, 23, 0, -2, 36, 0, 0, 24, 0, -1, 25, 0, 0, 25, 0, -2, 37, 0, 0, 26, 0, -2, 38, 0, 0, 27, 0, -2, 39, 0, 0, 28, 0, -2, 40, 0, 0, 29, 0, -2, 41, 0, 28, 1, 103], [0, 0, 0, 0, 0, 0, 0, 30, 31, 32, 33, 33, 34, 35, 36, 37, 38, 39, 40, 41, 41], [1, 1, 1, 1, 1, 1, 1, 2, 1, 2, 2, 4, 1, 2, 2, 1, 1, 2, 1, 2, 4], [3, 4, 5, 6, 7, 8, 9, 0, 10, 0, 0, 0, 11, 0, 0, 1, 1, 0, 1, 0, 0]], [[{"name": "game_giftmessage_frame03", "rect": {"x": 1, "y": 0, "width": 531, "height": 527}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 533, "height": 527}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-265.5, -263.5, 0, 265.5, -263.5, 0, -265.5, 263.5, 0, 265.5, 263.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [1, 527, 532, 527, 1, 0, 532, 0], "nuv": [0.001876172607879925, 0, 0.99812382739212, 0, 0.001876172607879925, 1, 0.99812382739212, 1], "minPos": {"x": -265.5, "y": -263.5, "z": 0}, "maxPos": {"x": 265.5, "y": 263.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [2], 0, [0], [3], [12]], [[{"name": "game_giftmessage_title02", "rect": {"x": 0, "y": 0, "width": 232, "height": 59}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 232, "height": 59}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-116, -29.5, 0, 116, -29.5, 0, -116, 29.5, 0, 116, 29.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 59, 232, 59, 0, 0, 232, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -116, "y": -29.5, "z": 0}, "maxPos": {"x": 116, "y": 29.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [2], 0, [0], [3], [13]]]]