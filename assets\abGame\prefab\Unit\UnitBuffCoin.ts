import { _decorator, Component, Node, Sprite, v3, Vec3 } from 'cc';
import { xcore } from '../../../scripts/libs/xcore';
import { Role } from '../../scripts/Role';
import { RoleData } from '../../scripts/RoleData';

const { ccclass, property } = _decorator;

@ccclass('UnitBuffCoin')
export class UnitBuffCoin extends Component {

    @property(Sprite)
    private sprIcon: Sprite = null;

    targetRoleData: RoleData

    private offsetPos: Vec3 = v3(0, -16);

    private tempPos: Vec3 = v3();


    hideTimeout: any

    setData(fromRole: Role, icon: string, delayTime: number, index: number = 1) {
        this.targetRoleData = fromRole.data;
        // xcore.res.remoteLoadSprite(xcore.gameData.cospath + `image/common/${icon}.png`, this.sprIcon);
        xcore.res.bundleLoadSprite('resources', `./res/image/common/${icon}`, this.sprIcon);
        this.node.active = true;

        this.hideTimeout && clearTimeout(this.hideTimeout);
        this.hideTimeout = setTimeout(() => {
            if (this.node) {
                this.node.active = false;
            }
        }, delayTime * 1000);
    }

    setPos(index: number) {
        this.offsetPos.set(-50 + (160 * index / 5), -16);
        this.node.setPosition(v3(this.targetRoleData.pos.x + this.offsetPos.x, this.targetRoleData.pos.y + this.offsetPos.y))
    }

    hide() {
        this.hideTimeout && clearTimeout(this.hideTimeout);
        this.node.active = false;
    }

    kill() {
        this.hideTimeout && clearTimeout(this.hideTimeout);
        this.node.destroy();
    }

    protected update(dt: number): void {
        //这里可以插值实现平滑移动,demo简单的瞬移过去把,反正仅作显示, 数据处理帧会做好判断
        if (this.node.position.x != this.targetRoleData.pos.x + this.offsetPos.x
            || this.node.position.y != this.targetRoleData.pos.y + this.offsetPos.y) {
            //let tempPos = this.node.position.clone();
            //逻辑坐标映射成creator里的坐标,简单除10好了
            //this.node.getPosition(this.tempPos);
            //this.tempPos = this.tempPos.lerp(v3(this.targetRoleData.pos.x + this.offsetPos.x, this.targetRoleData.pos.y + this.offsetPos.y,), 0.3);
            this.tempPos.set(this.targetRoleData.pos.x + this.offsetPos.x, this.targetRoleData.pos.y + this.offsetPos.y)
            this.node.setPosition(this.tempPos);
        }

    }
}


