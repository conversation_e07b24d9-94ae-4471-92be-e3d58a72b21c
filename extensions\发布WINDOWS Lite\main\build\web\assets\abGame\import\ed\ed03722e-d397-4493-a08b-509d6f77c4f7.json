[1, ["52fHu7D8hGm5vLaoALoXCl", "79kJqBL2xEMqzCr69g4Mgu@f9941", "d1AXBiGWRE2JLrzp4UPBUf@f9941", "3cp0mY9EhP9I1oenMJ0hJU@f9941", "9dDfGb+o5GPa/zRH3+4Rl8@f9941", "6dQetOxcZM3ZKEiIYvXcM6@f9941", "88Lfz7faJGJYgI7vA60c+y@f9941", "20BPU4ceBDQabfUErPIhBr@f9941", "0f1xdkmVpPpYFEtLhweI5Z@f9941"], ["node", "_font", "_spriteFrame", "root", "sprWingAnim", "spr<PERSON><PERSON><PERSON>", "ndAvatar", "ndUserData", "lbGiftTitle", "sprGiftTitle", "sprLevBg", "sprBar", "ndPgs", "ndUserDetail", "lbDesc", "lbName", "lbRoleName", "sprRoleName", "lbHp", "anim", "data", "_parent"], [["cc.Node", ["_name", "_layer", "_active", "_parent", "_components", "_prefab", "_lpos", "_children", "_lscale"], 0, 1, 12, 4, 5, 2, 5], ["cc.Label", ["_string", "_actualFontSize", "_isSystemFontUsed", "_fontSize", "_enableOutline", "_lineHeight", "_enableWrapText", "node", "__prefab", "_outlineColor", "_color"], -4, 1, 4, 5, 5], ["cc.Node", ["_name", "_layer", "_children", "_components", "_prefab", "_parent", "_lpos", "_lscale"], 1, 2, 9, 4, 1, 5, 5], ["cc.Sprite", ["_type", "_fillRange", "node", "__prefab", "_spriteFrame"], 1, 1, 4, 6], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.Animation", ["node", "__prefab"], 3, 1, 4], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.Mask", ["_type", "node", "__prefab"], 2, 1, 4], ["cc.Graphics", ["node", "__prefab", "_fillColor"], 3, 1, 4, 5], ["cc.Layout", ["_resizeMode", "_layoutType", "_affectedByScale", "node", "__prefab"], 0, 1, 4], ["29221bHNehCFYfFcJLvXSB6", ["node", "__prefab", "anim", "lbHp", "sfRoleNameBgs", "sfPgsBgs", "sfPgsBars", "sprRoleName", "lbRoleName", "lbName", "lbDesc", "ndUserDetail", "ndPgs", "sprBar", "sprLevBg", "sprGiftTitle", "lbGiftTitle", "ndUserData", "ndAvatar", "spr<PERSON><PERSON><PERSON>", "sprWingAnim"], 3, 1, 4, 1, 1, 3, 3, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]], [[6, 0, 2], [8, 0, 1, 2, 3, 4, 5, 5], [4, 0, 1, 2, 1], [3, 2, 3, 1], [0, 0, 1, 3, 7, 4, 5, 3], [0, 0, 1, 3, 4, 5, 8, 3], [2, 0, 1, 2, 3, 4, 3], [2, 0, 1, 5, 2, 3, 4, 6, 3], [0, 0, 1, 3, 4, 5, 3], [7, 0, 1, 1], [5, 0, 2], [2, 0, 1, 5, 2, 3, 4, 6, 7, 3], [0, 0, 2, 1, 3, 4, 5, 6, 4], [0, 0, 2, 1, 3, 7, 4, 5, 6, 4], [0, 0, 1, 3, 4, 5, 6, 3], [0, 0, 1, 3, 4, 5, 6, 8, 3], [4, 0, 1, 2, 3, 1], [3, 0, 1, 2, 3, 3], [3, 2, 3, 4, 1], [1, 0, 1, 3, 7, 8, 4], [1, 0, 1, 3, 5, 2, 4, 7, 8, 10, 9, 7], [1, 0, 1, 2, 7, 8, 9, 4], [1, 0, 1, 6, 2, 4, 7, 8, 9, 6], [1, 0, 1, 3, 2, 7, 8, 9, 5], [9, 0, 1, 2, 2], [10, 0, 1, 2, 1], [11, 0, 1, 2, 3, 4, 4], [12, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 1]], [[10, "UnitRole"], [6, "UnitRole", 33554432, [-20, -21, -22], [[2, -2, [0, "a8pXjRTrlG5qE5zi3qgK+H"], [5, 360, 360]], [27, -19, [0, "65vKa0GLJN3YOH1kiedIYI"], -18, -17, [1, 2], [3, 4, 5], [6, 7, 8], -16, -15, -14, -13, -12, -11, -10, -9, -8, -7, -6, -5, -4, -3]], [1, "76a8g1PW9NR646TXXchuu2", null, null, null, -1, 0]], [6, "ndPgs", 33554432, [-25, -26, -27], [[2, -23, [0, "16BcgSrtxD46zlhmfVPDa5"], [5, 80, 12]], [18, -24, [0, "0aXmp0JQxLiI1Ejo71pg4C"], 0]], [1, "5eJYpka9pCYY/vJrERyeTv", null, null, null, 1, 0]], [7, "ndUserDetail", 33554432, 1, [-29, -30, -31], [[2, -28, [0, "adLa1jHepGDI8a5VzODuRg"], [5, 124, 20]]], [1, "e4Y9UEZu9Id6uu8Ix8oo7J", null, null, null, 1, 0], [1, 0, 200, 0]], [7, "ndUserData", 33554432, 3, [-34, -35], [[2, -32, [0, "85dUDG12pCza9ajBfkD6XT"], [5, 54.399993896484375, 100]], [26, 1, 1, true, -33, [0, "4aC/8CwE5PSKi246zFTOMH"]]], [1, "15wIozEMxJN625VKaWkQ+a", null, null, null, 1, 0], [1, 0, 36, 0]], [11, "Node", 33554432, 4, [-39], [[2, -36, [0, "92urmFk3tBKY41mh+bSTC2"], [5, 68, 68]], [24, 1, -37, [0, "5aekfw8wVJuo9+PCPSycmN"]], [25, -38, [0, "ceUEvoIRFAroZHbR+eg9Vk"], [4, 16777215]]], [1, "3bpTbLP89BXq6JA07v5vS3", null, null, null, 1, 0], [1, -10.199996948242188, 0, 0], [1, 0.5, 0.5, 1]], [4, "sprAnim", 33554432, 1, [2], [[[2, -40, [0, "cfx/vXBjlDjK8KMc3js2h9"], [5, 117, 164]], [3, -41, [0, "88m/YtOAxG7YXXm+n/BTPS"]], -42], 4, 4, 1], [1, "85o9s0KBZBhokRa/WOvcc7", null, null, null, 1, 0]], [8, "sprWingAnim", 33554432, 1, [[[16, -43, [0, "ebUA0YCTZODoSQtv04/IrG"], [5, 284, 147], [0, 0.5, 0.3]], -44, [9, -45, [0, "a9HShYtf1EgI50U22JVXAo"]]], 4, 1, 4], [1, "02Nt05NPdOrrkmgZF0f96r", null, null, null, 1, 0]], [13, "sprRoleName", false, 33554432, 2, [-48], [[[2, -46, [0, "77Zhy8vw1AMKlvllJm65DJ"], [5, 40, 36]], -47], 4, 1], [1, "6bFI5tRZBOnrmYmaW9RWrc", null, null, null, 1, 0], [1, 0, 43.26999999999998, 0]], [4, "Sprite", 33554432, 3, [-51], [[[2, -49, [0, "8cKnHIsfZO353ZsCG+oqdT"], [5, 135, 48]], -50], 4, 1], [1, "e0JS/zfOBBtYyxzo7iK8Gf", null, null, null, 1, 0]], [4, "sprGiftTitle", 33554432, 3, [-54], [[[2, -52, [0, "aa+LeCuZdOw7vfP2OoqIZy"], [5, 135, 48]], -53], 4, 1], [1, "70UQCzhRFGO61m0b17vVng", null, null, null, 1, 0]], [8, "sprBar", 33554432, 2, [[[2, -55, [0, "8bIUc4t9dKgLTVUUixItpg"], [5, 78, 10]], -56], 4, 1], [1, "25embh7RtNsovAr2skmniG", null, null, null, 1, 0]], [12, "lbHp", false, 33554432, 2, [[[2, -57, [0, "80AKJ4TchAwJeRT6pSQXJT"], [5, 22.24609375, 50.4]], -58], 4, 1], [1, "c4HolxyY9IWZSkWwceivCo", null, null, null, 1, 0], [1, 0, 2.517, 0]], [5, "lbRoleName", 33554432, 8, [[[2, -59, [0, "fc0EdEOVpMQJWdEEY2ZJ2g"], [5, 61.199981689453125, 81.6]], -60], 4, 1], [1, "aa9QLJOf1Nw5D3XKS0VW/6", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [5, "lbDesc", 33554432, 9, [[[2, -61, [0, "f8gkNEm9NIppYfHed0PsEE"], [5, 36.79998779296875, 50.4]], -62], 4, 1], [1, "5fJrD0culEM53sgWQtHaD4", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [14, "spr<PERSON><PERSON><PERSON>", 33554432, 5, [[[2, -63, [0, "e3j/Bj9KlD7byilnZ2pD8Y"], [5, 70, 70]], -64], 4, 1], [1, "976yHew1BGCa0YKUnEEweE", null, null, null, 1, 0], [1, 0, 1.6889999999999645, 0]], [15, "lbName", 33554432, 4, [[[2, -65, [0, "a1y6KJTs1ENI0fA2OYH858"], [5, 40.79998779296875, 54.4]], -66], 4, 1], [1, "77W4yD/tRD96FbFvn8SVsH", null, null, null, 1, 0], [1, 17, 0, 0], [1, 0.5, 0.5, 1]], [5, "lbDesc", 33554432, 10, [[[2, -67, [0, "2dIFoKBLhHD41b+TczO4j+"], [5, 31.279998779296875, 50.4]], -68], 4, 1], [1, "f9w2F/oG9GEpotMlSzfXWX", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [3, 7, [0, "92Rb837d9IHZ3vAuhiaYoo"]], [17, 3, 1, 11, [0, "fcb50v559OHpKfx1m5mAlr"]], [19, "12", 20, 20, 12, [0, "a1WlLUGedBGLWKRNft9uKc"]], [20, "--", 60, 60, 60, false, true, 13, [0, "0cB/UdFG5LKZn6uQXDhDlG"], [4, 4281456383], [4, 4280295671]], [3, 8, [0, "97n+DKxyRATpqbG0Jbu9w4"]], [9, 6, [0, "0epuhp0TpBM5pSg9tzDNaj"]], [21, "--", 40, false, 14, [0, "cd0ZjcojJEj72pVxko5TAG"], [4, 4280690356]], [3, 9, [0, "fduae45+lHtKg9koiGLmfA"]], [3, 15, [0, "e1E/885rZFJrbsKQGN/qgn"]], [22, "--", 40, false, false, true, 16, [0, "16wv1IKlBE+pmseTnx+8cw"], [4, 4288956210]], [23, "--", 34, 34, false, 17, [0, "cbgeZAXS5GHoDu+cI3+VnM"], [4, 4280690356]], [3, 10, [0, "5cP/Hp7Y5CdKMbZ4mmw4nD"]]], 0, [0, 3, 1, 0, 0, 1, 0, 4, 18, 0, 5, 26, 0, 6, 5, 0, 7, 4, 0, 8, 28, 0, 9, 29, 0, 10, 25, 0, 11, 19, 0, 12, 2, 0, 13, 3, 0, 14, 24, 0, 15, 27, 0, 16, 21, 0, 17, 22, 0, 18, 20, 0, 19, 23, 0, 0, 1, 0, -1, 7, 0, -2, 6, 0, -3, 3, 0, 0, 2, 0, 0, 2, 0, -1, 11, 0, -2, 12, 0, -3, 8, 0, 0, 3, 0, -1, 9, 0, -2, 4, 0, -3, 10, 0, 0, 4, 0, 0, 4, 0, -1, 5, 0, -2, 16, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, -1, 15, 0, 0, 6, 0, 0, 6, 0, -3, 23, 0, 0, 7, 0, -2, 18, 0, 0, 7, 0, 0, 8, 0, -2, 22, 0, -1, 13, 0, 0, 9, 0, -2, 25, 0, -1, 14, 0, 0, 10, 0, -2, 29, 0, -1, 17, 0, 0, 11, 0, -2, 19, 0, 0, 12, 0, -2, 20, 0, 0, 13, 0, -2, 21, 0, 0, 14, 0, -2, 24, 0, 0, 15, 0, -2, 26, 0, 0, 16, 0, -2, 27, 0, 0, 17, 0, -2, 28, 0, 20, 1, 2, 21, 6, 68], [0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 21, 24, 27, 28], [2, -1, -2, -1, -2, -3, -1, -2, -3, 2, 1, 1, 1, 1], [1, 3, 4, 1, 5, 6, 2, 7, 8, 2, 0, 0, 0, 0]]