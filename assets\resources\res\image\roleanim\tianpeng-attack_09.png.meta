{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "7a872a71-dad9-4a88-9b43-a60ee6f8957c", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "7a872a71-dad9-4a88-9b43-a60ee6f8957c@6c48a", "displayName": "tianpeng-attack_09", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "7a872a71-dad9-4a88-9b43-a60ee6f8957c", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "7a872a71-dad9-4a88-9b43-a60ee6f8957c@f9941", "displayName": "tianpeng-attack_09", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": -5.5, "offsetY": -17, "trimX": 46, "trimY": 58, "width": 147, "height": 118, "rawWidth": 250, "rawHeight": 200, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-73.5, -59, 0, 73.5, -59, 0, -73.5, 59, 0, 73.5, 59, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [46, 142, 193, 142, 46, 24, 193, 24], "nuv": [0.184, 0.12, 0.772, 0.12, 0.184, 0.71, 0.772, 0.71], "minPos": [-73.5, -59, 0], "maxPos": [73.5, 59, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "7a872a71-dad9-4a88-9b43-a60ee6f8957c@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "7a872a71-dad9-4a88-9b43-a60ee6f8957c@6c48a"}}