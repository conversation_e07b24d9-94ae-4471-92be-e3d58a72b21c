import { v3, Vec2, Vec3 } from "cc"
import { E_AtkStyle, E_AtkType, E_MonsterTag, E_MonsterType, E_RoleState, E_RoleType, E_SkinType, E_TowerPointType } from "../../scripts/ConstGlobal"
import Tool from "../../scripts/libs/utils/Tool"


/**
 * 角色数值
 */
export class RoleData {
    /**用户id */
    userId: string
    //用户名
    nickName: string
    //头像
    iconUrl: string

    weekScore: number

    /**类型 */
    type: E_RoleType
    /**怪物种类 */
    monsterType: E_MonsterType | E_TowerPointType
    /**怪物标签  */
    monsterTag: E_MonsterTag
    monsterName: string
    //怪物积分
    monsterScore: number
    /**等级 */
    lev: number
    /**最高等级 */
    maxLev: number = 5;
    /**角色id 具体角色 */
    jsonId: string = '001'
    /**位置 */
    pos: Vec2
    /**血量 */
    hpNum: number
    /**总血量 */
    maxHp: number

    atkOffsetPos: Vec2

    /**最小攻击力 */
    minAtkNum: number
    /**最大攻击力 */
    maxAtkNum: number
    /**常用 */
    normalAtkNum: number = 10;
    /**缩放 */
    scale: Vec3 = v3(1, 1, 1);

    /**防御 */
    defenseNum: number = 0;


    //宝珠加成
    dimonEffects: {
        /**攻击加成 */
        attack: number,
        attackLev: number
        attackDmNum: number
        /**攻速加成 */
        atkspeed: number,
        atkspeedLev: number
        atkspeedDmNum: number
        /**技能cd加成 */
        skillcd: number,
        skillcdLev: number
        skillcdDmNum: number,
        atkr: number
        atkrLev: number,
        baseAtkM: number,
        /**暴击伤害 */
        atkm: number,
        atkmLev: number
        atkmDmNum: number
        /**塔血加成 */
        hp: number,
        hpLev: number
        hpDmNum: number
    }


    skinId: E_SkinType = E_SkinType.Skin1

    skinLev: number = 1
    /**可移动范围 左 */
    moveLeft: number
    /**可移动范围 右 */
    moveRight: number

    moveDir: number = 1;

    moveAnimation: string
    attackAnimation: string
    skillAnimation: string
    attackEffect: string

    attacKeffectInterval: number = 0;

    hurtEffect: string

    atkAnimJsonId: string
    /**
     * 技能目标
        1本人 
        2目标 
        3 路径 
     */
    atkAnimTargetType: number

    /**敌人攻击 */
    public get atkNum(): number {
        if (!this.minAtkNum || !this.maxAtkNum) {
            return this.normalAtkNum
        }
        return Tool.randomNumber(this.minAtkNum, this.maxAtkNum);
    }





    /**每秒攻击次数 */
    atkSpeed: number
    /**攻击类型 */
    atkType: E_AtkType
    /**攻击风格 近战 远程 */
    atkStyle: E_AtkStyle
    /** 多重攻击次数   */
    atkPropNum: number
    /**攻击范围数值 */
    atkRag: number

    /**每秒移动距离 */
    moveSpeed: number
    /**tick时间 */
    tickTime: number
    /**角色状态 */
    state: E_RoleState
    /**临时状态 */
    tempState: E_RoleState

    /**是否无敌 */
    isInvincible: boolean = false;

    invincibleTime: number = 0;

    /**是否是会复活的怪物 是否常驻怪物*/
    isLongMonster: boolean = false;
    /**复活时间 */
    reliveTime: number = 0;


    /******************************** role所受buff效果数值 ***********************************/


    //眩晕时间buff
    dizzinessTime: number = 0;

    //移动速度buff 
    moveSpeedBuff: number = 1;
    moveSpeedBuffTime: number = 0;

    //攻击力buff 
    atkNumBuff: number = 0;
    atkNumBuffTime: number = 0;

    //攻击速度 间隔 buff
    atkSpeedBuff: number = 1;
    atkSpeedBuffTime: number = 0;

    //技能释放速度 buff
    skillSpeedBuff: number = 1;
    skillSpeedBuffTime: number = 0;

    //灼烧
    autoHurtNum: number = 0;
    autoHurtEachTime: number = 0;
    autoHurtTime: number = 0;
    autoHurtEffectTime: number

    //恢复生命
    auotSaveHpTime: number = 0;
    autoSaveHpNum: number = 0;
    autoSaveHpEachNum: number = 0;

    //弱点 
    skillWeakNum: number = 0;
    skillWeakIds: string[]



}