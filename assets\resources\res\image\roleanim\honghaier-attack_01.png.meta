{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "80565f44-43d4-4a45-a3e2-2a3f702286fc", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "80565f44-43d4-4a45-a3e2-2a3f702286fc@6c48a", "displayName": "honghaier-attack_01", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "80565f44-43d4-4a45-a3e2-2a3f702286fc", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "80565f44-43d4-4a45-a3e2-2a3f702286fc@f9941", "displayName": "honghaier-attack_01", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": -36, "offsetY": -91, "trimX": 38, "trimY": 185, "width": 152, "height": 112, "rawWidth": 300, "rawHeight": 300, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-76, -56, 0, 76, -56, 0, -76, 56, 0, 76, 56, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [38, 115, 190, 115, 38, 3, 190, 3], "nuv": [0.12666666666666668, 0.01, 0.6333333333333333, 0.01, 0.12666666666666668, 0.38333333333333336, 0.6333333333333333, 0.38333333333333336], "minPos": [-76, -56, 0], "maxPos": [76, 56, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "80565f44-43d4-4a45-a3e2-2a3f702286fc@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "80565f44-43d4-4a45-a3e2-2a3f702286fc@6c48a"}}