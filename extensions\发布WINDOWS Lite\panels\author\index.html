<div class="ScrollView">
        
    <div class="SVcontent">


        <div class="title0">
            <ui-label value="关于作者"></ui-label>
        </div>
        <div>
            <p class="content-text0">
                作者：property<br>
                CocosCreator最小开发者<br>
                目前准高一<br><br>
            </p>

            <p class="content-text0">
                本人第一次做插件，之前从没开发过插件，甚至几乎没用过插件。由于本人技术有限，插件难免存在漏洞，诚恳地希望您批评指正，最后感谢您使用我的插件<br>
                遇到使用问题可以联系我！<br><br>
            </p>
            
            <p class="content-text0">
                联系方式<br>
                微信：propertycai<br>
                QQ：2573126576<br>
                技术Q群：1130122408<br>
                高一不出意外一个月回一次家，消息可能不能及时回复，但是看到了一定会回复！<br>
                2022.7.29
            </p>
        </div>
        
        
        
        <div>
            <hr class="hr" />
        </div>
        
        
        
        <div class="title1">
            <ui-label value="关于插件"></ui-label>
        </div>
        <div>
            <p class="content-text0">
                这是一款基于electron的发布Windows插件<br><br>
                本插件永久免费！！！<br>
                本插件永久免费！！！<br>
                本插件永久免费！！！<br><br>

                插件亮点<br>
                ·   无需复杂配置，一键发布Windows<br>
                ·   提供好用的原生API<br>
                ·   可在编辑器内预览效果<br>
                ·   提供打包面板<br>
                ·   打包速度快<br>
                ·   带有示例项目<br>
                ·   免费<br><br>

                插件在Windows电脑上开发，开发时使用的引擎版本是3.5.2，但理论上本插件适用所有3.x版本引擎。<br>
                插件只能在Windows电脑的CocosCreator上运行
            </p>
        </div>
        
        
        
        <div>
            <hr class="hr" />
        </div>
        
        
        
        <div class="title2">
            <ui-label value="使用教程"></ui-label>
        </div>
        <div>
            <p class="content-text0">
                使用教程<br>
                B站的视频都有详细讲哦~<br><br>
                <ui-button class="open_BV1">点我直达</ui-button>
            </p>
        </div>
        
        
        
        <div>
            <hr class="hr" />
        </div>



        <div class="title2">
            <ui-label value="失败解决方法"></ui-label>
        </div>
        <div>
            <p class="content-text0">
                发布失败的解决方法<br><br>

                首先要确保电脑安装了nodejs<br>
                并且全局安装了electron和electron-packager<br>
                一定要全局安装<br><br>

                按照输出的log去解决错误<br>

                log乱码大概率是electron-packager没有安装好<br>

                <!-- <ui-button class="open_BV2">点我直达</ui-button> -->
            </p>
        </div>
        
        
        
        <div>
            <hr class="hr" />
        </div>
        
        
        
        <div class="title4">
            <ui-label value="打赏作者"></ui-label>
        </div>
        <div>
            <p class="content-text0">
                免费插件，制作不易<br>
                高中很累<br>
                请property喝杯咖啡吧！<br><br>

                我会在下次更新插件的时候特别鸣谢你<br>
                <!-- afdian-property -->

                <ui-button class="reward">点我打赏</ui-button><br>
                <img alt="图片加载失败了呢" class="AAA"><br>
                <!-- <img src="afdian-property.jpg" width="400" height="600"/><br> -->
                感激不尽<br>
            </p>        
        </div>
        
        
        
        <div>
            <hr class="hr" />
        </div>

        

    </div>
</div>