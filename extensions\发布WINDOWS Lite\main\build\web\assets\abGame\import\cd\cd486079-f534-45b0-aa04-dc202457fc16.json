[1, ["52fHu7D8hGm5vLaoALoXCl", "35k5HDRJBGDIF+NsrwWXsD@f9941", "c4QFjyHkxBQoGsOs+ecoqi@f9941"], ["node", "_font", "_spriteFrame", "root", "lbDesc", "lbTitle", "btnClose", "sprFrame", "ndRoot", "data"], [["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_lineHeight", "_isSystemFontUsed", "_horizontalAlign", "_overflow", "node", "__prefab", "_color", "_font"], -4, 1, 4, 5, 6], ["cc.Node", ["_name", "_layer", "_components", "_prefab", "_children", "_parent", "_lscale"], 1, 9, 4, 2, 1, 5], ["cc.Node", ["_name", "_layer", "_parent", "_components", "_prefab", "_lpos", "_children", "_lscale"], 1, 1, 12, 4, 5, 2, 5], ["cc.UITransform", ["node", "__prefab", "_contentSize"], 3, 1, 4, 5], ["cc.Sprite", ["_sizeMode", "_type", "node", "__prefab", "_spriteFrame"], 1, 1, 4, 6], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.<PERSON><PERSON>", ["_transition", "_zoomScale", "node", "__prefab", "_target"], 1, 1, 4, 1], ["f188dWveQBAapRIKZegZT1A", ["node", "__prefab", "ndRoot", "sprFrame", "btnClose", "lbTitle", "lbDesc"], 3, 1, 4, 1, 1, 1, 1, 1]], [[6, 0, 2], [7, 0, 1, 2, 3, 4, 5, 5], [3, 0, 1, 2, 1], [2, 0, 1, 2, 6, 3, 4, 5, 3], [2, 0, 1, 2, 3, 4, 5, 7, 3], [5, 0, 2], [1, 0, 1, 4, 2, 3, 3], [1, 0, 1, 5, 2, 3, 6, 3], [3, 0, 1, 1], [0, 0, 1, 2, 3, 4, 7, 8, 10, 6], [0, 0, 1, 2, 3, 4, 7, 8, 9, 6], [0, 0, 5, 1, 2, 3, 6, 4, 7, 8, 9, 8], [8, 0, 1, 2, 3, 4, 3], [4, 0, 2, 3, 4, 2], [4, 1, 0, 2, 3, 3], [9, 0, 1, 2, 3, 4, 5, 6, 1]], [[5, "ViewCommonTips"], [6, "ViewCommonTips", 33554432, [-9], [[8, -2, [0, "deoQQdD4pOHIQAJaFFB4il"]], [15, -8, [0, "92JEB7/aRK7YGS9GEEbBCi"], -7, -6, -5, -4, -3]], [1, "eciSgX7VJAXYRvRxcl/ubd", null, null, null, -1, 0]], [3, "ndCore", 33554432, 1, [-12, -13, -14], [[[2, -10, [0, "93MqEhlldK3I8rVG0DiwA8"], [5, 756, 536.2]], -11], 4, 1], [1, "a1gszGb71D0L+bWPV4bHBV", null, null, null, 1, 0], [1, 0, 20.164999999999964, 0]], [3, "btnClose", 33554432, 2, [-18], [[[2, -15, [0, "e0HylSHDdCqJbbjAKxOYzE"], [5, 280, 90]], -16, [13, 0, -17, [0, "dffhjr4qtHaqLJbT0Dke1z"], 1]], 4, 1, 4], [1, "4c1WGW5itGvZKXxAUGlgS5", null, null, null, 1, 0], [1, 0, -186.75300000000004, 0]], [7, "txt", 33554432, 3, [[2, -19, [0, "ce4dyeRwxAOaylYyiLpnoR"], [5, 180, 100.8]], [9, "知道了", 60, 60, 80, false, -20, [0, "20+qAA0IhMZZTvzyvdwuVA"], 0]], [1, "42WP3GvqdDuZD4SfoKtXj5", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [4, "lbTitle", 33554432, 2, [[[2, -21, [0, "8eIo8thX5FNq20uX4qQm+b"], [5, 160, 100.8]], -22], 4, 1], [1, "d7FVeiKZNJ44yCX+tDGI/L", null, null, null, 1, 0], [1, 0, 232.2070000000001, 0], [1, 0.5, 0.5, 1]], [4, "lbDesc", 33554432, 2, [[[2, -23, [0, "49ZPKVnHNDIaSlHvjuu5yB"], [5, 1200, 511.2]], -24], 4, 1], [1, "94ODmM8nNPXotOmikwIHul", null, null, null, 1, 0], [1, 0, 26.144999999999982, 0], [1, 0.5, 0.5, 1]], [12, 3, 1.1, 3, [0, "90in0KcipIpZmLrZCEh2qk"], 3], [10, "提示", 80, 80, 80, false, 5, [0, "96mMcQRLZM3J6QAyP3kovx"], [4, 4289062911]], [11, "温馨提示温馨提示温馨提示温馨提示温馨提示温馨提示温馨提示温馨提示温馨提示温馨提示温馨提示温馨提示", 0, 80, 80, 120, 3, false, 6, [0, "feAJxiabBAZY/d+6MX7wB4"], [4, 4279772212]], [14, 1, 0, 2, [0, "d4KpPSG4FI/olS1JKGYmta"]]], 0, [0, 3, 1, 0, 0, 1, 0, 4, 9, 0, 5, 8, 0, 6, 7, 0, 7, 10, 0, 8, 2, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, -2, 10, 0, -1, 3, 0, -2, 5, 0, -3, 6, 0, 0, 3, 0, -2, 7, 0, 0, 3, 0, -1, 4, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, -2, 8, 0, 0, 6, 0, -2, 9, 0, 9, 1, 24], [0, 0, 8, 9, 10], [1, 2, 1, 1, 2], [0, 1, 0, 0, 2]]