[1, ["29wSFh8EBFCZ8rtCLWp4fb@f9941", "canFzbnwVALq2791ZaFlQH@f9941", "d2q29gw1FNjLxsGymR7dla@f9941", "00AYUD+O5ADIKDEpRR60rr@f9941", "1bmHua9D9MDpzXK3dGeVjq@f9941", "c4QFjyHkxBQoGsOs+ecoqi@f9941", "20g1ukYUVPvKWKBRznAKo+@f9941", "54TknWPwVPqJqeCR+Y/Czo@f9941", "95EkngnxZFbYuFpsqVTaFr@f9941"], ["node", "_spriteFrame", "_target", "_normalSprite", "_hoverSprite", "_pressedSprite", "_disabledSprite", "root", "lbMusic", "lbId", "btnConfirm", "sprMusicBar", "slMusic", "lbSound", "sprSoundBar", "slSound", "btnClose", "sprFrame", "ndRoot", "data"], [["cc.Node", ["_name", "_layer", "_active", "_components", "_prefab", "_parent", "_children", "_lpos"], 0, 9, 4, 1, 2, 5], ["cc.Sprite", ["_type", "_sizeMode", "_fillRange", "node", "__prefab", "_spriteFrame"], 0, 1, 4, 6], ["cc.Node", ["_name", "_layer", "_parent", "_components", "_prefab", "_lpos", "_children"], 1, 1, 12, 4, 5, 2], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_isBold", "node", "__prefab", "_color"], -1, 1, 4, 5], ["cc.<PERSON><PERSON>", ["_transition", "node", "__prefab", "_normalColor", "_target"], 2, 1, 4, 5, 1], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.Slider", ["_progress", "node", "__prefab", "_handle"], 2, 1, 4, 1], ["a34dbvDGXxPTaih7bRx60eO", ["node", "__prefab", "ndRoot", "sprFrame", "btnClose", "slSound", "sprSoundBar", "lbSound", "slMusic", "sprMusicBar", "btnConfirm", "lbId", "lbMusic"], 3, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]], [[7, 0, 2], [8, 0, 1, 2, 3, 4, 5, 5], [3, 0, 1, 2, 1], [2, 0, 1, 2, 3, 4, 5, 3], [4, 0, 1, 2, 3, 4, 5, 6, 5], [0, 0, 1, 5, 3, 4, 7, 3], [0, 0, 1, 5, 6, 3, 4, 7, 3], [2, 0, 1, 2, 3, 4, 3], [3, 0, 1, 1], [4, 0, 1, 2, 4, 5, 4], [0, 0, 1, 5, 3, 4, 3], [2, 0, 1, 2, 6, 3, 4, 3], [3, 0, 1, 2, 3, 1], [1, 0, 1, 3, 4, 5, 3], [1, 1, 3, 4, 2], [1, 0, 3, 4, 5, 2], [1, 0, 2, 3, 4, 3], [1, 3, 4, 5, 1], [5, 0, 1, 2, 3, 4, 2], [5, 1, 2, 3, 4, 1], [9, 0, 1, 2, 3, 2], [6, 0, 2], [0, 0, 1, 6, 3, 4, 3], [0, 0, 1, 5, 6, 3, 4, 3], [0, 0, 2, 1, 5, 6, 3, 4, 7, 4], [0, 0, 2, 1, 5, 6, 3, 4, 4], [1, 3, 4, 1], [4, 0, 1, 2, 3, 4, 5, 5], [10, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 1]], [[21, "ViewSetting"], [22, "ViewSetting", 33554432, [-15], [[8, -2, [0, "0cdc+uzaZGBIrxjcDuHQEz"]], [28, -14, [0, "5fLqKwOKFHcaoXoy/bdhsd"], -13, -12, -11, -10, -9, -8, -7, -6, -5, -4, -3]], [1, "70muaxh+BMnqrFesWT18I4", null, null, null, -1, 0]], [23, "ndRoot", 33554432, 1, [-17, -18, -19, -20, -21, -22, -23, -24], [[8, -16, [0, "db2TRMdBpE1ocFEg/BEYlA"]]], [1, "a25cfgfe5O46OnaSVhDAYD", null, null, null, 1, 0]], [24, "ndSound", false, 33554432, 2, [-26, -27, -28, -29], [[2, -25, [0, "b1hMQ8ezxPhZq9gJLJjLDk"], [5, 272, 29]]], [1, "besujPnd5EaanMnTdcnplM", null, null, null, 1, 0], [1, -2.8630000000000564, 75.71499999999992, 0]], [6, "ndMusic", 33554432, 2, [-31, -32, -33, -34], [[2, -30, [0, "85A2+LeOhGBrVN+zS2PcRL"], [5, 272, 29]]], [1, "e7tmM8eQVK64QpYcmpCQYf", null, null, null, 1, 0], [1, -2.8630000000000564, 4.836000000000013, 0]], [3, "btnClose", 33554432, 2, [[[2, -35, [0, "feCU0UmNRELpbrxo83BHFX"], [5, 180, 60]], [13, 1, 0, -36, [0, "92F6s0fbtKbLAPhBBI0uwH"], 0], -37], 4, 4, 1], [1, "abKIvNgFNDs6tJOqo0fu1K", null, null, null, 1, 0], [1, -160, -190.80200000000002, 0]], [3, "btnConfirm", 33554432, 2, [[[2, -38, [0, "38BUm81NBFJ4MVSZq4QDr5"], [5, 180, 60]], [13, 1, 0, -39, [0, "c5IHhtEfVHkr1wqjdfTSy+"], 1], -40], 4, 4, 1], [1, "a9gQPKOTtHsKPj+8JoVrZT", null, null, null, 1, 0], [1, 160, -190.80200000000002, 0]], [11, "Slide<PERSON>", 33554432, 3, [-44], [[[2, -41, [0, "41o4gsATBIXb0+s5HU1hzZ"], [5, 272, 29]], [15, 1, -42, [0, "65RbFQGy1HkZ76S+OAXpNb"], 2], -43], 4, 4, 1], [1, "70WvOBhFJNba5HXCigH/dj", null, null, null, 1, 0]], [3, "<PERSON><PERSON>", 33554432, 7, [[[2, -45, [0, "46RwywvlxOKqd4Dytazcjx"], [5, 50, 50]], -46, [19, -48, [0, "d65KL9CZhJc7Udxuyo3tmf"], [4, 4292269782], -47]], 4, 1, 4], [1, "ffPl5C1wdDoba0HGvdqXE/", null, null, null, 1, 0], [1, -76.16, 0, 0]], [11, "Slide<PERSON>", 33554432, 4, [-52], [[[2, -49, [0, "57DfBzLZpOEp3T+UFJFbNx"], [5, 272, 29]], [15, 1, -50, [0, "56VWOcX3pPg7UquMyVQoOQ"], 3], -51], 4, 4, 1], [1, "edwy5eRBpEKZw+UAUosY1A", null, null, null, 1, 0]], [3, "<PERSON><PERSON>", 33554432, 9, [[[2, -53, [0, "a6I+rwNqlI86vHhspMi+In"], [5, 50, 50]], -54, [19, -56, [0, "61wiCWCOJCG5F8swEp5gKs"], [4, 4292269782], -55]], 4, 1, 4], [1, "2frVFNNglEK4IGEvgPtWww", null, null, null, 1, 0], [1, -76.16, 0, 0]], [25, "ndResolution", false, 33554432, 2, [-58, -59, -60], [[8, -57, [0, "92T51WGkxCeYW3ExMMODmt"]]], [1, "b6RXzoDrZNaYIjDe6SgPji", null, null, null, 1, 0]], [6, "btnSmall", 33554432, 11, [-63], [[2, -61, [0, "0fume3mo1HKKazdnvtZA2N"], [5, 152, 54]], [17, -62, [0, "e9z+wgCTBFhIkJF8vuYnIr"], 4]], [1, "ad8/+RlzNOnaSQhvpYe98c", null, null, null, 1, 0], [1, -200, 0, 0]], [6, "btnBig", 33554432, 11, [-66], [[2, -64, [0, "8c9w4bwD9JXq3AeL+A+qUt"], [5, 152, 54]], [17, -65, [0, "cbbVh+E09HupqVf5dErLyF"], 5]], [1, "e6hxMEBhpGdZQdui2LUkKf", null, null, null, 1, 0], [1, 200, 0, 0]], [7, "sprFrame", 33554432, 2, [[[2, -67, [0, "7feIdNEctMyYfA3sLNhhvQ"], [5, 706, 512]], -68], 4, 1], [1, "a0LiQQ79xN2a0/cxlPJteS", null, null, null, 1, 0]], [5, "lbTitle", 33554432, 2, [[2, -69, [0, "5co5qhtqhDjr94MAeUeJvA"], [5, 72, 50.4]], [9, "设置", 36, 36, -70, [0, "19EGk8hblPmIrgyQ1Xbun0"]]], [1, "01euwDFkFBMKk1pLdDnMb+", null, null, null, 1, 0], [1, 0, 223.58799999999997, 0]], [7, "sprBar", 33554432, 3, [[[2, -71, [0, "09Wkf/5olO+JJRwxjUf+4h"], [5, 268, 25]], -72], 4, 1], [1, "a8XBb0SvFNL44UgyaqRRn7", null, null, null, 1, 0]], [5, "lbTxt", 33554432, 3, [[2, -73, [0, "28BNLRi09HT68NjHXWyiw7"], [5, 84, 50.4]], [4, "音效：", 28, 28, true, -74, [0, "2csEv7Sh9CMYWbQgIHN2hw"], [4, 4279772212]]], [1, "11pN8w8EVO9oNHmNAXB2Sl", null, null, null, 1, 0], [1, -181.481, 0.7570000000000618, 0]], [3, "lbNum", 33554432, 3, [[[12, -75, [0, "b3r72FdWxBKpoedDwQ9wI+"], [5, 46.716796875, 50.4], [0, 0, 0.5]], -76], 4, 1], [1, "dcpqm9fmVKcKIE5sCtNsvS", null, null, null, 1, 0], [1, 160.094, 0.7570000000000618, 0]], [7, "sprBar", 33554432, 4, [[[2, -77, [0, "1955uLFBlFJKV8XVCCCBgS"], [5, 268, 25]], -78], 4, 1], [1, "abjjbpzGpDQ7xdPMMM9dr5", null, null, null, 1, 0]], [5, "lbTxt", 33554432, 4, [[2, -79, [0, "ef97LX+BFBDr6NmfHFEfju"], [5, 112, 50.4]], [4, "背景音：", 28, 28, true, -80, [0, "35yrGkoddCq5+nbEZeJHFv"], [4, 4279772212]]], [1, "be2GDztv9LxLOA5NrmLPI4", null, null, null, 1, 0], [1, -195.481, 0.7570000000000618, 0]], [3, "lbNum", 33554432, 4, [[[12, -81, [0, "c3u4w3/eZD3674/2VdQfws"], [5, 46.716796875, 50.4], [0, 0, 0.5]], -82], 4, 1], [1, "32mxoHJEhDwYRel7Xy5Ggw", null, null, null, 1, 0], [1, 160.094, 0.7570000000000618, 0]], [5, "lbTxt", 33554432, 11, [[2, -83, [0, "24HhYG7eVCMofISQGwjPxg"], [5, 168, 50.4]], [4, "当前画面比例", 28, 28, true, -84, [0, "fckpNwaUNNJLtwszg5UN5l"], [4, 4279772212]]], [1, "53oiDEQi1EgZYO3WYQda7a", null, null, null, 1, 0], [1, 0, 135.67200000000003, 0]], [10, "Label", 33554432, 12, [[2, -85, [0, "15Jbk/tqZKqZhEqDKFWnkw"], [5, 60, 50.4]], [9, "缩小", 30, 30, -86, [0, "bbcSXsvNJL9o4Ko9YteZGw"]]], [1, "87p9WLclpCWJ0uwQ7lnrCn", null, null, null, 1, 0]], [10, "Label", 33554432, 13, [[2, -87, [0, "faXU0TcdZFT71YgDOuKryR"], [5, 60, 50.4]], [9, "放大", 30, 30, -88, [0, "9393TE/d1I4oOHZ98iRNdA"]]], [1, "de2gta4kJPX4uIwGTRs+gA", null, null, null, 1, 0]], [3, "lbId", 33554432, 2, [[[2, -89, [0, "d4/CoXI3ZJQbw5q3wuH6aj"], [5, 0, 50.4]], -90], 4, 1], [1, "fbWmPolu5BZIO9rHw1qGzP", null, null, null, 1, 0], [1, 0, -320.159, 0]], [26, 14, [0, "3cHo60SVpMyJ/TjwTEq89q"]], [18, 3, 5, [0, "d52pEboiNM3KSrh+1EyF6H"], [4, 4292269782], 5], [18, 3, 6, [0, "57uT9/7AVL4ZzGCl0JfN3o"], [4, 4292269782], 6], [14, 0, 8, [0, "989kcYS3BDbJlGOyDX5sW0"]], [20, 0.22, 7, [0, "15zI9YT1FMuZRlYzdSq+Ia"], 29], [16, 3, 1, 16, [0, "9bdKm2RwNOApRSp9nQa2bj"]], [4, "100", 28, 28, true, 18, [0, "13Q2LSUghGlp5QnvZdWJjq"], [4, 4279772212]], [14, 0, 10, [0, "d1Va/Q7CxMsqIk3ki74SiD"]], [20, 0.22, 9, [0, "10VO/7QfdLW7qVRYPU8k85"], 33], [16, 3, 1, 19, [0, "e8QJlSVjdAk48vAiKCKEdg"]], [4, "100", 28, 28, true, 21, [0, "889XrW1/dAjqZtDlzGCLbo"], [4, 4279772212]], [27, "", 28, 28, true, 25, [0, "c8+UivElNC6KeQvdEiH0wB"]]], 0, [0, 7, 1, 0, 0, 1, 0, 8, 36, 0, 9, 37, 0, 10, 28, 0, 11, 35, 0, 12, 34, 0, 13, 32, 0, 14, 31, 0, 15, 30, 0, 16, 27, 0, 17, 26, 0, 18, 2, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, -1, 14, 0, -2, 5, 0, -3, 6, 0, -4, 15, 0, -5, 3, 0, -6, 4, 0, -7, 11, 0, -8, 25, 0, 0, 3, 0, -1, 7, 0, -2, 16, 0, -3, 17, 0, -4, 18, 0, 0, 4, 0, -1, 9, 0, -2, 19, 0, -3, 20, 0, -4, 21, 0, 0, 5, 0, 0, 5, 0, -3, 27, 0, 0, 6, 0, 0, 6, 0, -3, 28, 0, 0, 7, 0, 0, 7, 0, -3, 30, 0, -1, 8, 0, 0, 8, 0, -2, 29, 0, 2, 8, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, -3, 34, 0, -1, 10, 0, 0, 10, 0, -2, 33, 0, 2, 10, 0, 0, 10, 0, 0, 11, 0, -1, 22, 0, -2, 12, 0, -3, 13, 0, 0, 12, 0, 0, 12, 0, -1, 23, 0, 0, 13, 0, 0, 13, 0, -1, 24, 0, 0, 14, 0, -2, 26, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, -2, 31, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, -2, 32, 0, 0, 19, 0, -2, 35, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, -2, 36, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, -2, 37, 0, 19, 1, 90], [0, 0, 0, 0, 0, 0, 26, 28, 28, 28, 28, 31, 35], [1, 1, 1, 1, 1, 1, 1, 3, 4, 5, 6, 1, 1], [4, 0, 1, 1, 2, 2, 5, 0, 6, 7, 8, 3, 3]]