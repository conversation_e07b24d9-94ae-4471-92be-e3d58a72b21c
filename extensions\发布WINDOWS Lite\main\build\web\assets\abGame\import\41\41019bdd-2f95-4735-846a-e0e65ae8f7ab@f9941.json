[1, ["41AZvdL5VHNYRq4OZa6Per@6c48a"], ["_textureSource"], ["cc.SpriteFrame"], 0, [{"name": "tian<PERSON>", "rect": {"x": 2, "y": 2, "width": 2013, "height": 2039}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 2017, "height": 2043}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-1006.5, -1019.5, 0, 1006.5, -1019.5, 0, -1006.5, 1019.5, 0, 1006.5, 1019.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [2, 2041, 2015, 2041, 2, 2, 2015, 2], "nuv": [0.0009915716410510659, 0.0009789525208027412, 0.999008428358949, 0.0009789525208027412, 0.0009915716410510659, 0.9990210474791973, 0.999008428358949, 0.9990210474791973], "minPos": {"x": -1006.5, "y": -1019.5, "z": 0}, "maxPos": {"x": 1006.5, "y": 1019.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]]