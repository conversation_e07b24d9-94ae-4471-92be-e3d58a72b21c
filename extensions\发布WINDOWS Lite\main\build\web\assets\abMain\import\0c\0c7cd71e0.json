[1, ["52fHu7D8hGm5vLaoALoXCl", "45go8ltQ1MUqWR4ZSRpiuM@f9941", "57UgcWSMhKGYrPQcn4d3+w@f9941", "45go8ltQ1MUqWR4ZSRpiuM@6c48a", "57UgcWSMhKGYrPQcn4d3+w@6c48a", "71qHJEE/1M3bnMDq+NJ/+E@6c48a", "71qHJEE/1M3bnMDq+NJ/+E@f9941", "bbuhM/HXtBOrynzf1cHMme@f9941", "d2WfaXAoFE1oMmugDXSpHD@f9941", "d1Q4EL3gVKR7fj90P/3SeJ@f9941", "29Vj821JxOOJ3vZNQHIdkS@f9941", "dfY+90orZM6bUTZObb8740@f9941", "46amII9lpO2bPbVg4yf9HH@f9941", "a6HGGCGI1LX5mSMegT6BXH@f9941", "37CDiVfFxLwKS5C6el7uJO@f9941", "cf9C91cjFEhrYSOFfeFfF7@f9941", "bbuhM/HXtBOrynzf1cHMme@6c48a", "d1Q4EL3gVKR7fj90P/3SeJ@6c48a", "d2WfaXAoFE1oMmugDXSpHD@6c48a"], ["node", "_spriteFrame", "_font", "_textureSource", "_parent", "_cameraComponent", "ndUITop", "lbVersion", "btnModePass", "btnModeBoss", "btnModeHell", "btnModeDifficult", "btnModeEasy", "btnRank", "btnSetting", "btnJoin", "_target", "scene"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_layer", "_id", "_active", "_components", "_lpos", "_parent", "_children"], -1, 9, 5, 1, 2], ["cc.Node", ["_name", "_layer", "_active", "_parent", "_components", "_lpos", "_children"], 0, 1, 12, 5, 2], ["cc.Label", ["_string", "_actualFontSize", "_isSystemFontUsed", "_enableOutline", "_outlineWidth", "_fontSize", "node", "_outlineColor", "_font", "_color"], -3, 1, 5, 6, 5], ["cc.Widget", ["_alignFlags", "_bottom", "_top", "node"], 0, 1], ["cc.<PERSON><PERSON>", ["_transition", "_zoomScale", "node", "_normalColor", "_target"], 1, 1, 5, 1], ["cc.UITransform", ["node", "_contentSize"], 3, 1, 5], ["cc.Sprite", ["_type", "_sizeMode", "node", "_spriteFrame"], 1, 1, 6], ["cc.SceneAsset", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_lpos"], 2, 1, 2, 5], ["cc.<PERSON>", ["node", "_cameraComponent"], 3, 1, 1], ["fe5bfG0oEJEgpUaQhPtGIzQ", ["node", "btnJoin", "btnSetting", "btnRank", "btnModeEasy", "btnModeDifficult", "btnModeHell", "btnModeBoss", "btnModePass", "lbVersion", "ndUITop", "ndUIBottom", "ndIconSelects"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2], ["cc.Scene", ["_name", "_children", "_prefab", "_globals"], 2, 2, 4, 4], ["cc.PrefabInfo", ["root", "asset", "fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots"], -3], ["cc.SceneGlobals", ["ambient", "shadows", "_skybox", "fog", "octree", "skin", "lightProbeInfo", "postSettings"], 3, 4, 4, 4, 4, 4, 4, 4, 4], ["cc.AmbientInfo", ["_skyColorHDR", "_groundAlbedoHDR"], 3, 5, 5], ["cc.ShadowsInfo", ["_shadowColor", "_size"], 3, 5, 5], ["cc.SkyboxInfo", [], 3], ["cc.FogInfo", [], 3], ["cc.OctreeInfo", [], 3], ["cc.SkinInfo", ["_enabled"], 2], ["cc.LightProbeInfo", [], 3], ["cc.PostSettingsInfo", [], 3], ["cc.Camera", ["_projection", "_orthoHeight", "_near", "_visibility", "node", "_color"], -1, 1, 5]], [[6, 0, 1, 1], [7, 2, 3, 1], [1, 0, 1, 6, 4, 5, 3], [1, 0, 3, 1, 6, 4, 5, 4], [5, 0, 2, 2], [3, 0, 1, 2, 3, 4, 6, 7, 8, 6], [3, 0, 1, 5, 2, 4, 6, 9, 7, 8, 6], [2, 0, 2, 1, 3, 6, 4, 5, 4], [5, 0, 1, 2, 3], [2, 0, 1, 3, 6, 4, 5, 3], [2, 0, 1, 3, 4, 5, 3], [6, 0, 1], [8, 0, 2], [1, 0, 1, 7, 4, 5, 3], [1, 0, 1, 2, 7, 4, 5, 4], [1, 0, 1, 6, 7, 4, 5, 3], [1, 0, 1, 6, 4, 3], [2, 0, 1, 3, 4, 3], [9, 0, 1, 2, 3, 2], [4, 0, 1, 3, 3], [4, 0, 2, 1, 3, 4], [4, 0, 2, 3, 3], [10, 0, 1, 1], [11, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 1], [7, 0, 1, 2, 3], [5, 2, 3, 4, 1], [3, 0, 1, 2, 3, 6, 7, 8, 5], [3, 0, 1, 6, 3], [12, 0, 1, 2, 3, 2], [13, 0, 1, 2, 3, 4, 5, 7], [14, 0, 1, 2, 3, 4, 5, 6, 7, 1], [15, 0, 1, 1], [16, 0, 1, 1], [17, 1], [18, 1], [19, 1], [20, 0, 2], [21, 1], [22, 1], [23, 0, 1, 2, 3, 4, 5, 5]], [[[{"name": "default_radio_button_on", "rect": {"x": 1, "y": 1, "width": 30, "height": 30}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 32, "height": 32}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-15, -15, 0, 15, -15, 0, -15, 15, 0, 15, 15, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [1, 31, 31, 31, 1, 1, 31, 1], "nuv": [0.03125, 0.03125, 0.96875, 0.03125, 0.03125, 0.96875, 0.96875, 0.96875], "minPos": {"x": -15, "y": -15, "z": 0}, "maxPos": {"x": 15, "y": 15, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [3], [3]], [[{"name": "default_sprite", "rect": {"x": 0, "y": 2, "width": 40, "height": 36}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 40, "height": 40}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-20, -18, 0, 20, -18, 0, -20, 18, 0, 20, 18, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 38, 40, 38, 0, 2, 40, 2], "nuv": [0, 0.05, 1, 0.05, 0, 0.95, 1, 0.95], "minPos": {"x": -20, "y": -18, "z": 0}, "maxPos": {"x": 20, "y": 18, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [3], [4]], [[{"name": "main_select_bg", "rect": {"x": 0, "y": 0, "width": 1080, "height": 1920}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 1080, "height": 1920}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-540, -960, 0, 540, -960, 0, -540, 960, 0, 540, 960, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 1920, 1080, 1920, 0, 0, 1080, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -540, "y": -960, "z": 0}, "maxPos": {"x": 540, "y": 960, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [3], [5]], [[[12, "Main"], [13, "ndUIBottom", 33554432, [-3, -4, -5, -6, -7, -8, -9], [[11, -1], [19, 4, 100, -2]], [1, 0, -810, 0]], [14, "<PERSON><PERSON>", 33554432, "beI88Z2HpFELqR4T5EMHpg", [-28, -29, -30, 1, -31], [[0, -10, [5, 1080, 1920]], [22, -12, -11], [20, 45, 5.684341886080802e-14, 5.684341886080802e-14, -13], [23, -27, -26, -25, -24, -23, -22, -21, -20, -19, -18, -17, 1, [-14, -15, -16]]], [1, 540, 960, 0]], [15, "ndUITop", 33554432, 2, [-34, -35, -36], [[11, -32], [21, 1, 100, -33]], [1, 0, 810, 0]], [9, "btnModePass", 33554432, 1, [-40, -41, -42], [[[0, -37, [5, 675, 206]], [1, -38, 7], -39], 4, 4, 1], [1, 0, 955.992, 0]], [9, "btnModeBoss", 33554432, 1, [-46, -47, -48], [[[0, -43, [5, 675, 206]], [1, -44, 11], -45], 4, 4, 1], [1, 0, 676.398, 0]], [7, "btnModeEasy", false, 33554432, 1, [-52, -53, -54], [[[0, -49, [5, 643, 197]], -50, [1, -51, 15]], 4, 1, 4], [1, 0, 1073.2, 0]], [7, "btnModeDifficult", false, 33554432, 1, [-58, -59, -60], [[[0, -55, [5, 643, 196]], -56, [1, -57, 19]], 4, 1, 4], [1, 0, 796.341, 0]], [7, "btnModeHell", false, 33554432, 1, [-64, -65, -66], [[[0, -61, [5, 643, 196]], -62, [1, -63, 23]], 4, 1, 4], [1, 0, 519.482, 0]], [7, "btnJoin", false, 33554432, 1, [-70], [[[0, -67, [5, 349, 93]], -68, [1, -69, 25]], 4, 1, 4], [1, 0, 233.692, 0]], [2, "btnTest", 33554432, 2, [[0, -71, [5, 60, 60]], [24, 1, 0, -72], [25, -74, [4, 4292269782], -73]], [1, -234.07799999999997, -719.45, 0]], [10, "btnSetting", 33554432, 3, [[[0, -75, [5, 112, 113]], -76, [1, -77, 1]], 4, 1, 4], [1, -400, 0, 0]], [10, "btnRank", 33554432, 3, [[[0, -78, [5, 99, 100]], -79, [1, -80, 2]], 4, 1, 4], [1, 400, 0, 0]], [3, "iconSelect", false, 33554432, 6, [[0, -81, [5, 30, 30]], [1, -82, 13]], [1, 213.66999999999996, 36.59800000000007, 0]], [3, "iconSelect", false, 33554432, 7, [[0, -83, [5, 40, 36]], [1, -84, 16]], [1, 241.78, 38.331, 0]], [3, "iconSelect", false, 33554432, 8, [[0, -85, [5, 40, 36]], [1, -86, 20]], [1, 241.78, 38.331, 0]], [16, "main_select_bg", 33554432, 2, [[0, -87, [5, 1080, 1920]], [1, -88, 0]]], [3, "main_title_01", false, 33554432, 3, [[0, -89, [5, 749, 173]], [1, -90, 3]], [1, 65.72900000000004, -248.15300000000002, 0]], [17, "lbVersion", 33554432, 1, [[[0, -91, [5, 0, 50.4]], -92], 4, 1]], [2, "Label", 33554432, 4, [[0, -93, [5, 166, 56.4]], [5, "闯关模式", 40, false, true, 3, -94, [4, 4280233593], 4]], [1, -213.99400000000003, 44.27099999999996, 0]], [3, "iconSelect", false, 33554432, 4, [[0, -95, [5, 30, 30]], [1, -96, 5]], [1, 213.66999999999996, 36.59800000000007, 0]], [2, "Label-002", 33554432, 4, [[0, -97, [5, 240, 50.4]], [6, "通关奖励传说皮肤", 30, 30, false, 3, -98, [4, 4281090172], [4, 4280236409], 6]], [1, -167.223, -45.728000000000065, 0]], [2, "Label", 33554432, 5, [[0, -99, [5, 166, 56.4]], [5, "挑战模式", 40, false, true, 3, -100, [4, 4289534740], 8]], [1, -213.99400000000003, 44.27099999999996, 0]], [3, "iconSelect", false, 33554432, 5, [[0, -101, [5, 30, 30]], [1, -102, 9]], [1, 213.66999999999996, 36.59800000000007, 0]], [2, "Label-002", 33554432, 5, [[0, -103, [5, 240, 50.4]], [6, "掉落大量皮肤碎片", 30, 30, false, 3, -104, [4, 4294952875], [4, 4280236409], 10]], [1, -167.223, -45.728000000000065, 0]], [2, "Label", 33554432, 6, [[0, -105, [5, 86, 56.4]], [5, "简单", 40, false, true, 3, -106, [4, 4289534740], 12]], [1, -213.99400000000003, 44.27099999999996, 0]], [2, "Label-002", 33554432, 6, [[0, -107, [5, 322.5, 50.4]], [6, "掉落绿色、蓝色品质碎片", 30, 30, false, 3, -108, [4, 4286661136], [4, 4280236409], 14]], [1, -124.67399999999998, -45.728000000000065, 0]], [2, "Label-001", 33554432, 7, [[0, -109, [5, 86, 56.4]], [5, "中等", 40, false, true, 3, -110, [4, 4280236409], 17]], [1, -213.994, 44.271, 0]], [2, "Label-002", 33554432, 7, [[0, -111, [5, 322.5, 50.4]], [6, "掉落蓝色、紫色品质碎片", 30, 30, false, 3, -112, [4, 4280170386], [4, 4280236409], 18]], [1, -124.67399267578128, -34.226, 0]], [2, "Label-001", 33554432, 8, [[0, -113, [5, 86, 56.4]], [5, "困难", 40, false, true, 3, -114, [4, 4289466750], 21]], [1, -213.99400000000003, 44.27099999999996, 0]], [2, "Label-002", 33554432, 8, [[0, -115, [5, 322.5, 50.4]], [6, "掉落紫色、橙色品质碎片", 30, 30, false, 3, -116, [4, 4291432047], [4, 4280236409], 22]], [1, -124.67399267578128, -34.226, 0]], [2, "Label", 33554432, 9, [[0, -117, [5, 84, 54.4]], [26, "开始", 40, false, true, -118, [4, 4279517442], 24]], [1, -2.6409999999999627, 4.098000000000013, 0]], [28, "Main", [2], [29, null, null, "83c43e1a-3242-4364-9e1b-33f73089ef6f", null, null, null], [30, [31, [2, 0, 0, 0, 0.520833125], [2, 0, 0, 0, 0]], [32, [4, 4283190348], [0, 512, 512]], [33], [34], [35], [36, false], [37], [38]]], [18, "Camera", 2, [-119], [1, 0, 0, 1000]], [39, 0, 960, 0, 1108344832, 33, [4, 4278190080]], [4, 3, 11], [4, 3, 12], [27, "", 40, 18], [8, 3, 1.1, 4], [8, 3, 1.1, 5], [8, 3, 1.02, 6], [4, 3, 7], [4, 3, 8], [4, 3, 9]], 0, [0, 0, 1, 0, 0, 1, 0, -1, 18, 0, -2, 4, 0, -3, 5, 0, -4, 6, 0, -5, 7, 0, -6, 8, 0, -7, 9, 0, 0, 2, 0, 5, 34, 0, 0, 2, 0, 0, 2, 0, -1, 13, 0, -2, 14, 0, -3, 15, 0, 6, 3, 0, 7, 37, 0, 8, 38, 0, 9, 39, 0, 10, 42, 0, 11, 41, 0, 12, 40, 0, 13, 36, 0, 14, 35, 0, 15, 43, 0, 0, 2, 0, -1, 33, 0, -2, 16, 0, -3, 3, 0, -5, 10, 0, 0, 3, 0, 0, 3, 0, -1, 11, 0, -2, 12, 0, -3, 17, 0, 0, 4, 0, 0, 4, 0, -3, 38, 0, -1, 19, 0, -2, 20, 0, -3, 21, 0, 0, 5, 0, 0, 5, 0, -3, 39, 0, -1, 22, 0, -2, 23, 0, -3, 24, 0, 0, 6, 0, -2, 40, 0, 0, 6, 0, -1, 25, 0, -2, 13, 0, -3, 26, 0, 0, 7, 0, -2, 41, 0, 0, 7, 0, -1, 14, 0, -2, 27, 0, -3, 28, 0, 0, 8, 0, -2, 42, 0, 0, 8, 0, -1, 15, 0, -2, 29, 0, -3, 30, 0, 0, 9, 0, -2, 43, 0, 0, 9, 0, -1, 31, 0, 0, 10, 0, 0, 10, 0, 16, 10, 0, 0, 10, 0, 0, 11, 0, -2, 35, 0, 0, 11, 0, 0, 12, 0, -2, 36, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, -2, 37, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, 0, 21, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, 0, 26, 0, 0, 27, 0, 0, 27, 0, 0, 28, 0, 0, 28, 0, 0, 29, 0, 0, 29, 0, 0, 30, 0, 0, 30, 0, 0, 31, 0, 0, 31, 0, -1, 34, 0, 17, 32, 1, 4, 2, 2, 4, 32, 119], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 1, 1, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 1, 2, 2, 1, 1, 2, 2, 1, 2, 1], [6, 7, 8, 9, 0, 1, 0, 10, 0, 1, 0, 11, 0, 1, 0, 12, 2, 0, 0, 13, 2, 0, 0, 14, 0, 15]], [[{"name": "main_btn_setting", "rect": {"x": 3, "y": 3, "width": 112, "height": 113}, "offset": {"x": -4, "y": 2.5}, "originalSize": {"width": 126, "height": 124}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-56, -56.5, 0, 56, -56.5, 0, -56, 56.5, 0, 56, 56.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [3, 121, 115, 121, 3, 8, 115, 8], "nuv": [0.023809523809523808, 0.06451612903225806, 0.9126984126984127, 0.06451612903225806, 0.023809523809523808, 0.9758064516129032, 0.9126984126984127, 0.9758064516129032], "minPos": {"x": -56, "y": -56.5, "z": 0}, "maxPos": {"x": 56, "y": 56.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [3], [16]], [[{"name": "main_title_01", "rect": {"x": 0, "y": 0, "width": 749, "height": 173}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 749, "height": 173}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-374.5, -86.5, 0, 374.5, -86.5, 0, -374.5, 86.5, 0, 374.5, 86.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 173, 749, 173, 0, 0, 749, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -374.5, "y": -86.5, "z": 0}, "maxPos": {"x": 374.5, "y": 86.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [3], [17]], [[{"name": "main_btn_rank", "rect": {"x": 0, "y": 0, "width": 99, "height": 100}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 99, "height": 100}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-49.5, -50, 0, 49.5, -50, 0, -49.5, 50, 0, 49.5, 50, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 100, 99, 100, 0, 0, 99, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -49.5, "y": -50, "z": 0}, "maxPos": {"x": 49.5, "y": 50, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [3], [18]]]]