import { _decorator, Component, Node, Sprite } from 'cc';
import { xcore } from '../../../scripts/libs/xcore';
const { ccclass, property } = _decorator;
interface ILoadingRes {
    sprBg?: {
        bundleName: string,
        path: string
    }
    sprLogo?: {
        bundleName: string,
        path: string
    }
}
@ccclass('UILoading')
export class UILoading extends Component {

    @property(Sprite)
    private sprLogo: Sprite = null;

    @property(Sprite)
    private sprBg: Sprite = null;
    

    /**
     * 
     * @param loadingRes loading页的背景图和logo图资源路径
     */
    initUI(loadingRes?: ILoadingRes) {
        if (loadingRes) {
            for (const key in loadingRes) {
                if (Object.prototype.hasOwnProperty.call(loadingRes, key)) {
                    const resData = loadingRes[key];
                    xcore.res.bundleLoadSprite(resData.bundleName, resData.path, this[key]);
                }
            }
        }
    }


}


