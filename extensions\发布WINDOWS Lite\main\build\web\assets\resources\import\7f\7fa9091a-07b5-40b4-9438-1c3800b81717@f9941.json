[1, ["7fqQkaB7VAtJQ4HDgAuBcX@6c48a"], ["_textureSource"], ["cc.SpriteFrame"], 0, [{"name": "huodou-idle_15", "rect": {"x": 144, "y": 151, "width": 633, "height": 425}, "offset": {"x": 5, "y": -75.5}, "originalSize": {"width": 911, "height": 576}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-316.5, -212.5, 0, 316.5, -212.5, 0, -316.5, 212.5, 0, 316.5, 212.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [144, 425, 777, 425, 144, 0, 777, 0], "nuv": [0.15806805708013172, 0, 0.8529088913282108, 0, 0.15806805708013172, 0.7378472222222222, 0.8529088913282108, 0.7378472222222222], "minPos": {"x": -316.5, "y": -212.5, "z": 0}, "maxPos": {"x": 316.5, "y": 212.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]]