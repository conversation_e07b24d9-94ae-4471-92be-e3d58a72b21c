import { _decorator, Component, easing, Label, Node, size, Sprite, tween, Tween, UITransform, v3, Vec3, view } from 'cc';
import { GiftMessageMgr } from '../../scripts/GiftMessageMgr';
import { xcore } from 'db://assets/scripts/libs/xcore';

const { ccclass, property } = _decorator;

@ccclass('UnitTaskRewardMessage')
export class UnitTaskRewardMessage extends Component {
    @property(Sprite)
    private sprAvatar: Sprite = null;

    @property(Label)
    private lbName: Label = null;


    @property(Label)
    private lbRewardNum: Label = null;

    private toPos: Vec3 = v3(0, 0);
    private fromPos: Vec3 = v3(0, 0)
    private tw: Tween


    setData(data) {
        this.toPos.set(-view.getVisibleSize().width / 2 + 90, 0);
        this.fromPos.set(-view.getVisibleSize().width / 2 - this.node.getComponent(UITransform).width,0);
        this.node.setPosition(this.fromPos);
        this.tw = tween(this.node)

            .to(0.4, { position: this.toPos }, { easing: easing.cubicIn })
            .delay(1.2)
            .call(() => {
                this.kill();
            })
        this.tw.start();
        this.lbName.string = data.name || '匿名用户';
        xcore.res.remoteLoadSprite(data.avatar, this.sprAvatar, size(70, 70));
        //  this.lbReward.string = data.desc;
        this.lbRewardNum.string = '灵珠x' + data.totalNum;
    }
    kill() {
        this.node.removeFromParent();
        GiftMessageMgr.getInstance().killTaskRewardMessage(this);
    }

}


