
/** 正则工具 */
export class RegexUtil {
    /**
     * 判断字符是否为双字节字符（如中文字符）
     * @param string 原字符串
     */
    static isDoubleWord(string: string): boolean {
        return /[^\x00-\xff]/.test(string);
    }

    static isMobilePhone(phone: string): boolean {

        return /^[1][3,4,5,6,7,8,9][0-9]{9}$/.test(phone)
    }

    static isIdCard(id: string): boolean {
        return /^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9xX]$/.test(id);

    }

    

}
