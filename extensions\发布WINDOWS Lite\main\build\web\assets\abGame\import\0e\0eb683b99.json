[1, ["52fHu7D8hGm5vLaoALoXCl", "ff3reVIcZLdo1ocsK9Lga3@f9941", "9bBYHzhH5F25cHFMhUL3oq@f9941", "5clNBx7a9IIY6cHtiklFjU@f9941", "e6thSVya5LJ788DxHC8Z45@f9941", "2c8hCyiEhL3KBAMa5+iwT7@f9941", "5clNBx7a9IIY6cHtiklFjU@6c48a"], ["node", "_spriteFrame", "_font", "root", "lbWing", "sprWing", "ndWing", "lbGiftTitle", "lbScore", "lbName", "lbRankNum", "sprRankNum", "spr<PERSON><PERSON><PERSON>", "data", "_textureSource"], [["cc.Node", ["_name", "_layer", "_active", "_components", "_prefab", "_lpos", "_parent", "_children"], 0, 9, 4, 5, 1, 2], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_isSystemFontUsed", "node", "__prefab", "_color", "_font"], -1, 1, 4, 5, 6], ["cc.Sprite", ["_sizeMode", "node", "__prefab", "_spriteFrame"], 2, 1, 4, 6], ["cc.Node", ["_name", "_layer", "_parent", "_components", "_prefab", "_lpos", "_lscale"], 1, 1, 12, 4, 5, 5], ["cc.UITransform", ["node", "__prefab", "_contentSize"], 3, 1, 4, 5], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["48442l44o5I/oK4m3BpY7QE", ["node", "__prefab", "spr<PERSON><PERSON><PERSON>", "sprRankNum", "lbRankNum", "lbName", "lbScore", "lbGiftTitle", "ndWing", "sprWing", "lbWing"], 3, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.<PERSON><PERSON>", ["node", "__prefab"], 3, 1, 4], ["cc.Mask", ["_type", "node", "__prefab"], 2, 1, 4], ["cc.Graphics", ["node", "__prefab", "_fillColor"], 3, 1, 4, 5]], [[7, 0, 2], [9, 0, 1, 2, 3, 4, 5, 5], [4, 0, 1, 2, 1], [3, 0, 1, 2, 3, 4, 5, 3], [0, 0, 1, 6, 3, 4, 5, 3], [2, 1, 2, 3, 1], [2, 1, 2, 1], [1, 0, 1, 2, 3, 4, 5, 6, 5], [6, 0, 2], [0, 0, 1, 7, 3, 4, 5, 3], [0, 0, 2, 1, 6, 7, 3, 4, 5, 4], [0, 0, 1, 6, 7, 3, 4, 5, 3], [0, 0, 1, 6, 3, 4, 3], [3, 0, 1, 2, 3, 4, 5, 6, 3], [4, 0, 1, 1], [2, 0, 1, 2, 3, 2], [8, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 1], [10, 0, 1, 1], [11, 0, 1, 2, 2], [12, 0, 1, 2, 1], [1, 0, 1, 2, 3, 4, 5, 6, 7, 5], [1, 0, 1, 2, 3, 4, 5, 5], [1, 0, 1, 2, 4, 5, 6, 4]], [[[[8, "UnitRank"], [9, "UnitRank", 33554432, [-14, -15, -16, -17, -18, -19, -20, -21, -22, -23], [[2, -2, [0, "26+LEFWf1HnYfHXapv5ELj"], [5, 736, 96]], [5, -3, [0, "2cknrXPqVCgbAWOPRfdS/s"], 4], [16, -13, [0, "5eNlr+XNFH/4ewOykw6CLQ"], -12, -11, -10, -9, -8, -7, -6, -5, -4]], [1, "f0M2LyMDhEALuI7yPwe841", null, null, null, -1, 0], [1, 0, -48, 0]], [10, "ndWing", false, 33554432, 1, [-26, -27, -28], [[14, -24, [0, "a9g6luFy9F35hHhqvifEW5"]], [17, -25, [0, "f4q3lBP1ZPOY/NMCXor5sJ"]]], [1, "ecqcSXO+ZMLJdWwEmNiAyZ", null, null, null, 1, 0], [1, 290, 0, 0]], [11, "Node", 33554432, 1, [-32], [[2, -29, [0, "89kZTV0khNGJt6cMvxP/Qi"], [5, 68, 68]], [18, 1, -30, [0, "38TsgYJmZPRqYZhscR30/c"]], [19, -31, [0, "edHmMeaoNOJYhNL2oERL4V"], [4, 16777215]]], [1, "8aCBlUZ4pOv60URVLP2xS0", null, null, null, 1, 0], [1, -225.599, 0, 0]], [4, "sprAFrame", 33554432, 1, [[2, -33, [0, "aaLkYex81LurRMjLG6STZ0"], [5, 70, 70]], [5, -34, [0, "08Wxa1DiBCbqwjgXlva/Sd"], 0]], [1, "cbNKKt3hFMRqc6XKtiv9xk", null, null, null, 1, 0], [1, -225.594, 0.8239999999998417, 0]], [3, "spr<PERSON><PERSON><PERSON>", 33554432, 3, [[[2, -35, [0, "5aUwI7f5REzqPp+3ljUqtr"], [5, 70, 70]], -36], 4, 1], [1, "16Gcu7kHRFzLAYVpSnT4/Y", null, null, null, 1, 0], [1, 0, 1.6889999999999645, 0]], [4, "sprMask", 33554432, 1, [[2, -37, [0, "4f3KnOiBtE5ZDQh+8hWP2Z"], [5, 70, 70]], [5, -38, [0, "0440ULHuFPw5rrbD/QWNTh"], 1]], [1, "592rr3kSxDPqWlQXeHlYPB", null, null, null, 1, 0], [1, -225.599, 1.689, 0]], [3, "sprRankNum", 33554432, 1, [[[2, -39, [0, "1dW2iu42RHkac+Ik0ADFi7"], [5, 61, 61]], -40], 4, 1], [1, "8dlIk72RBCUIEr6a8rmkcP", null, null, null, 1, 0], [1, -325.744, 0, 0]], [3, "lbRankNum", 33554432, 1, [[[2, -41, [0, "20NwSbPTlEO5VulWEoCOrb"], [5, 8.419998168945312, 50.4]], -42], 4, 1], [1, "72t2CoZTBDj7rrSNBcgJZ9", null, null, null, 1, 0], [1, -325.803, 0.8, 0]], [3, "lbName", 33554432, 1, [[[2, -43, [0, "2bClEtSwNKMraeJ9kR/bX5"], [5, 0, 50.4]], -44], 4, 1], [1, "01YKYYI8ZKjrTnhAHzlGpw", null, null, null, 1, 0], [1, -87.08800000000002, 0.7999999999999545, 0]], [3, "lbScore", 33554432, 1, [[[2, -45, [0, "adui0YwRtCFLv/N1htino1"], [5, 36.95997619628906, 50.4]], -46], 4, 1], [1, "14aDBmn9tA0rr4DRnd8T+I", null, null, null, 1, 0], [1, 290, 0, 0]], [4, "Label-007", 33554432, 1, [[2, -47, [0, "9dcRyYaYZNUJDhjAM3vU3Q"], [5, 0, 50.4]], [20, "", 20, 20, false, -48, [0, "bf4NWM7glGS6iHRKlWLEEp"], [4, 4279772212], 2]], [1, "78tXBDlEVIA6ddcvYjYHZ3", null, null, null, 1, 0], [1, 311.484, -12.722, 0]], [3, "Sprite", 33554432, 2, [[[2, -49, [0, "a1ZtkxfWlHD68NogkJoQ7b"], [5, 40, 36]], -50], 4, 1], [1, "51WECpmGlDdJq6I93W4hyR", null, null, null, 1, 0], [1, 0, 9.116999999999962, 0]], [13, "Label", 33554432, 2, [[[2, -51, [0, "67v+en2WFPb5/IP4Mqw6XR"], [5, 120, 50.4]], -52], 4, 1], [1, "e7Y7pIov9BpbxU31idCcId", null, null, null, 1, 0], [1, 0, -27.214000000000055, 0], [1, 0.5, 0.5, 0.5]], [12, "game_uibottom_frame03", 33554432, 2, [[2, -53, [0, "ffXJchBwJOaZw/SCbKD04k"], [5, 86, 86]], [15, 0, -54, [0, "33a49bnVxPcoi3BbCmzEsj"], 3]], [1, "59xfiUz/lPQYIvJL6QMX2N", null, null, null, 1, 0]], [3, "lbGiftTitle", 33554432, 1, [[[2, -55, [0, "83fOrt4vJO4p1n5OMLX3xT"], [5, 0, 50.4]], -56], 4, 1], [1, "ddFPExNrRBNZd8H2ZiWWPB", null, null, null, 1, 0], [1, 105.96100000000001, 1.1859999999999218, 0]], [6, 5, [0, "57h6ZqTelH+5dlJ5EC2UAg"]], [6, 7, [0, "ecJLLbvLNJD7ntDHLWBIrw"]], [21, "1", 20, 20, false, 8, [0, "b46nsFmblMK66BXn5i2R47"]], [7, "", 20, 20, false, 9, [0, "1fK4TGcfVPXI5DfV5QcIP/"], [4, 4279772212]], [7, "999", 20, 20, false, 10, [0, "125bhY8h5CIqEy4ZDwMRQk"], [4, 4278859720]], [6, 12, [0, "03sB5yol5Al7IxkuXKP+ZQ"]], [22, "皮肤名称", 30, 30, 13, [0, "95NO73QnRFhKWBsQNEscwC"], [4, 4278859720]], [7, "", 20, 20, false, 15, [0, "4c40PZbTZGgqDgCykf37a1"], [4, 4279772212]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, 4, 22, 0, 5, 21, 0, 6, 2, 0, 7, 23, 0, 8, 20, 0, 9, 19, 0, 10, 18, 0, 11, 17, 0, 12, 16, 0, 0, 1, 0, -1, 4, 0, -2, 3, 0, -3, 6, 0, -4, 7, 0, -5, 8, 0, -6, 9, 0, -7, 10, 0, -8, 11, 0, -9, 2, 0, -10, 15, 0, 0, 2, 0, 0, 2, 0, -1, 12, 0, -2, 13, 0, -3, 14, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -1, 5, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, -2, 16, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, -2, 17, 0, 0, 8, 0, -2, 18, 0, 0, 9, 0, -2, 19, 0, 0, 10, 0, -2, 20, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, -2, 21, 0, 0, 13, 0, -2, 22, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, -2, 23, 0, 13, 1, 56], [0, 0, 0, 0, 0, 17, 18, 19, 20, 23], [1, 1, 2, 1, 1, 1, 2, 2, 2, 2], [1, 2, 0, 3, 4, 5, 0, 0, 0, 0]], [[{"name": "game_uibottom_frame03", "rect": {"x": 0, "y": 0, "width": 96, "height": 96}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 96, "height": 96}, "rotated": false, "capInsets": [20, 20, 20, 20], "vertices": {"rawPosition": [-48, -48, 0, 48, -48, 0, -48, 48, 0, 48, 48, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 96, 96, 96, 0, 0, 96, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -48, "y": -48, "z": 0}, "maxPos": {"x": 48, "y": 48, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [5], 0, [0], [14], [6]]]]