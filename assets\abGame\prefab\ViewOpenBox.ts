import { _decorator, Button, Component, log, Node, sp } from 'cc';
import { ViewBase } from '../../scripts/libs/manager/ViewBase';
import { xcore } from '../../scripts/libs/xcore';
import { C_View } from '../../scripts/ConstGlobal';
import { ConfigHelper } from '../../scripts/config/ConfigHelper';
const { ccclass, property } = _decorator;

@ccclass('ViewOpenBox')
export class ViewOpenBox extends ViewBase {
    @property(sp.Skeleton)
    private anim: sp.Skeleton = null

    @property(Button)
    private btnOption: Button = null;

    private _data: any = null;



    protected onLoadCompleted(): void {
        this.btnOption.node.on('click', () => {
            this.checkRewards(this._data);
            this.node.destroy();
            xcore.ui.closeView(C_View.ViewOpenBox);
        }, this)
    }

    setData(data) {
        this._data = data;
        this.openBox(data);
        this.btnClose.node.off('click');
        this.btnClose.node.on('click', () => {
            this.checkRewards(data);
            this.closeSelf();
        }, this)
    }

    checkRewards(data) {
        let users = data.users;
        if (data.dimondId) {
            //弹出宝珠奖励
            xcore.ui.addView(C_View.ViewDimondReward, {
                id: data.dimondId,
                freeid: data.freeDimondId,
                users,
                cb: () => {
                    if (!data.lotteryId) {
                        data.cb && data.cb()
                    } else {
                        xcore.ui.addView(C_View.ViewSkinDebrisReward, {
                            isPassLottery: data.isPassLottery,
                            id: data.lotteryId,
                            freeid: data.freeid,
                            users,
                            cb: data.cb
                        })
                    }

                }
            })
        }
        //抽奖
        else if (data.lotteryId) {
            xcore.ui.addView(C_View.ViewSkinDebrisReward, {
                isPassLottery: data.isPassLottery,
                id: data.lotteryId,
                freeid: data.freeid,
                users,
                cb: data.cb
            })
        }
    }

    openBox(data) {
        this.anim.setAnimation(0, 'open', false);
        this.anim.setCompleteListener(() => {
            if (!this.node || !this.node.isValid) return
            this.checkRewards(data);
            this.closeSelf();

        })
    }

}


