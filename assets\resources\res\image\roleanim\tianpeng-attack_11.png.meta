{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "8eac8ed4-6892-422a-be4d-27e413b1b1c1", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "8eac8ed4-6892-422a-be4d-27e413b1b1c1@6c48a", "displayName": "tianpeng-attack_11", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "8eac8ed4-6892-422a-be4d-27e413b1b1c1", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "8eac8ed4-6892-422a-be4d-27e413b1b1c1@f9941", "displayName": "tianpeng-attack_11", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": -4.5, "offsetY": -15, "trimX": 47, "trimY": 54, "width": 147, "height": 122, "rawWidth": 250, "rawHeight": 200, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-73.5, -61, 0, 73.5, -61, 0, -73.5, 61, 0, 73.5, 61, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [47, 146, 194, 146, 47, 24, 194, 24], "nuv": [0.188, 0.12, 0.776, 0.12, 0.188, 0.73, 0.776, 0.73], "minPos": [-73.5, -61, 0], "maxPos": [73.5, 61, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "8eac8ed4-6892-422a-be4d-27e413b1b1c1@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "8eac8ed4-6892-422a-be4d-27e413b1b1c1@6c48a"}}