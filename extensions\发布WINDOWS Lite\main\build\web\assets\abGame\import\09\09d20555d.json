[1, ["20g1ukYUVPvKWKBRznAKo+@f9941", "54TknWPwVPqJqeCR+Y/Czo@f9941", "95EkngnxZFbYuFpsqVTaFr@f9941", "11vcSwZKhOt6Knn7niM+l3@f9941", "7dyltG3WBNtLHeX9EVgqeQ@f9941", "57UgcWSMhKGYrPQcn4d3+w@f9941", "7dj5uJT9FMn6OrOOx83tfK@f9941", "15jn5SMiBM15aUcT4OboJ4@f9941", "11vcSwZKhOt6Knn7niM+l3@6c48a", "15jn5SMiBM15aUcT4OboJ4@6c48a", "57UgcWSMhKGYrPQcn4d3+w@6c48a"], ["node", "_spriteFrame", "_normalSprite", "_hoverSprite", "_pressedSprite", "_disabledSprite", "_textureSource", "root", "ndContentUser", "ndContent", "togSelectGroup", "lbDesc", "btnRandomTest", "ndBtnUser", "ndBtn", "btnClose", "sprFrame", "ndRoot", "_target", "data", "_parent"], [["cc.Node", ["_name", "_layer", "_active", "_components", "_prefab", "_parent", "_children", "_lpos"], 0, 9, 4, 1, 2, 5], ["cc.Sprite", ["_sizeMode", "_type", "node", "__prefab", "_spriteFrame", "_color"], 1, 1, 4, 6, 5], ["cc.Label", ["_actualFontSize", "_fontSize", "_string", "_lineHeight", "_overflow", "node", "__prefab", "_color"], -2, 1, 4, 5], "cc.SpriteFrame", ["cc.Node", ["_name", "_layer", "_parent", "_components", "_prefab", "_lpos", "_children"], 1, 1, 12, 4, 5, 2], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.<PERSON><PERSON>", ["_transition", "_zoomScale", "node", "__prefab", "_normalColor", "_target", "_normalSprite", "_hoverSprite", "_pressedSprite", "_disabledSprite"], 1, 1, 4, 5, 1, 6, 6, 6, 6], ["cc.Layout", ["_resizeMode", "_layoutType", "_spacingY", "_spacingX", "node", "__prefab"], -1, 1, 4], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["d81a4c+NrRPHavj7IWxdY0B", ["node", "__prefab", "ndRoot", "sprFrame", "btnClose", "ndBtn", "ndBtnUser", "btnRandomTest", "lbDesc", "togSelectGroup", "ndContent", "ndContentUser"], 3, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.Mask", ["node", "__prefab"], 3, 1, 4], ["cc.Graphics", ["node", "__prefab", "_fillColor"], 3, 1, 4, 5], ["<PERSON><PERSON>", ["bounceDuration", "brake", "horizontal", "node", "__prefab", "_content"], 0, 1, 4, 1], ["cc.Toggle", ["node", "__prefab", "_normalColor", "_target", "_checkMark"], 3, 1, 4, 5, 1, 1]], [[9, 0, 2], [11, 0, 1, 2, 3, 4, 5, 5], [5, 0, 1, 2, 1], [0, 0, 1, 5, 3, 4, 7, 3], [0, 0, 1, 5, 3, 4, 3], [0, 0, 1, 5, 6, 3, 4, 3], [1, 1, 0, 2, 3, 4, 3], [0, 0, 1, 6, 3, 4, 3], [4, 0, 1, 2, 6, 3, 4, 5, 3], [4, 0, 1, 2, 3, 4, 5, 3], [4, 0, 1, 2, 3, 4, 3], [5, 0, 1, 1], [5, 0, 1, 2, 3, 1], [6, 0, 2, 3, 4, 5, 2], [1, 2, 3, 4, 1], [1, 0, 2, 3, 2], [2, 2, 0, 1, 3, 4, 5, 6, 7, 6], [2, 2, 0, 1, 5, 6, 7, 4], [8, 0, 2], [0, 0, 1, 5, 6, 3, 4, 7, 3], [0, 0, 2, 1, 5, 3, 4, 7, 4], [10, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 1], [6, 0, 1, 2, 3, 3], [6, 0, 2, 3, 4, 5, 6, 7, 8, 9, 2], [1, 0, 2, 3, 5, 4, 2], [12, 0, 1, 1], [13, 0, 1, 2, 1], [7, 0, 1, 2, 4, 5, 4], [7, 0, 1, 3, 2, 4, 5, 5], [14, 0, 1, 2, 3, 4, 5, 4], [2, 0, 1, 5, 6, 3], [2, 2, 0, 1, 5, 6, 4], [15, 0, 1, 2, 3, 4, 1]], [[[[18, "ViewTestGift"], [7, "ViewTestGift", 33554432, [-14], [[11, -2, [0, "76Ds1qRXROtoqEczhT8+Bg"]], [21, -13, [0, "1frwtOEy5GYYCy58f5iR0Q"], -12, -11, -10, -9, -8, -7, -6, -5, -4, -3]], [1, "13O2TTy+FPtY192bW+Robr", null, null, null, -1, 0]], [5, "ndRoot", 33554432, 1, [-16, -17, -18, -19, -20, -21, -22, -23, -24, -25], [[11, -15, [0, "02ieot0PtAZIZvNz4AU7nq"]]], [1, "d7ze3X5OtLUa4Z6jU2/TSP", null, null, null, 1, 0]], [5, "btnUser", 33554432, 2, [-28, -29, -30], [[2, -26, [0, "0bIAp3w/dCC5mU44n5uoUE"], [5, 340, 60]], [22, 3, 1.05, -27, [0, "18upWkhQpFl6Nr8UTdGMEJ"]]], [1, "4cA9Rn0ypL6onHfBvqb5f7", null, null, null, 1, 0]], [5, "btnOption", 33554432, 2, [-35], [[2, -31, [0, "69XHbqiXBPWLpjjB67F6JQ"], [5, 100, 60]], [6, 1, 0, -32, [0, "61I/zfiURL4bdWGr5UqAQM"], 2], [23, 2, -34, [0, "dblncwzPtAzIO4IRK50rme"], [4, 4292269782], -33, 3, 4, 5, 6]], [1, "05Fq5KBYJL2ayKoNBi2T+w", null, null, null, 1, 0]], [8, "Toggle", 33554432, 2, [-39, -40], [[[2, -36, [0, "cccGmrhUxEwZNZy183/lTw"], [5, 28, 28]], [14, -37, [0, "00+8mnaRRFdKZMdPHzJjs6"], 8], -38], 4, 4, 1], [1, "b8qDj69W1AbKh9Q632d6vV", null, null, null, 1, 0], [1, 83.547, -656.357, 0]], [8, "btnRandomCreateTest", 33554432, 2, [-44], [[[2, -41, [0, "7aBbL3CkFCia6yGWoFFhYJ"], [5, 100, 60]], [6, 1, 0, -42, [0, "bdKAkuqhVCwZouWLfzKb19"], 9], -43], 4, 4, 1], [1, "80RI5h6ulAiadEbr5GS3la", null, null, null, 1, 0], [1, -5.618000000000052, 640.4269999999999, 0]], [9, "btnClose", 33554432, 2, [[[2, -45, [0, "55F/gfjU1Ezrf+Xy9U1H4I"], [5, 60, 60]], [6, 1, 0, -46, [0, "55VTlgea9IuZe9aFlxrVlj"], 7], -47], 4, 4, 1], [1, "c3HVR3GvhANany7jA/0zy8", null, null, null, 1, 0], [1, 307.202, 689.981, 0]], [7, "view", 33554432, [-51], [[2, -48, [0, "2aSYovZM1BQofnGi3SKsaW"], [5, 340, 600]], [25, -49, [0, "58ZTiKk5VOtb/Bwq0Brx86"]], [26, -50, [0, "71geKE7IlDlodX8J0wZz2L"], [4, 16777215]]], [1, "19zEh6P5pKp6aX0ujcFiNT", null, null, null, 1, 0]], [3, "content", 33554432, 8, [[12, -52, [0, "65G6W3LzRFjbnicZgTc6nU"], [5, 340, -10], [0, 0.5, 1]], [27, 1, 2, 10, -53, [0, "8eOCCEYHZBmoult0Fz3Mt8"]]], [1, "80aQQEzN5C1LvQB6RQGxOZ", null, null, null, 1, 0], [1, 0, 300, 0]], [3, "ndContentKey", 33554432, 2, [[12, -54, [0, "51tQt/0uFAT5folFIVXwYm"], [5, 340, 0], [0, 0.5, 1]], [28, 1, 3, 10, 10, -55, [0, "d4X6W4ieJBZ42iLErZe7Dm"]]], [1, "c6L7ZBwIZJsZWd3eRGWkBB", null, null, null, 1, 0], [1, -176.59, 242.694, 0]], [19, "user", 33554432, 2, [8], [[2, -56, [0, "06m2Bb4GFNUa3Vu2I2osp0"], [5, 340, 600]], [29, 0.23, 0.75, false, -57, [0, "aawCQmad1FeLIZ4PdoF7pk"], 9]], [1, "923Hs4cC9LcIqs/laW1Crr", null, null, null, 1, 0], [1, 157.755, 37.482, 0]], [4, "Sprite", 33554432, 3, [[2, -58, [0, "69vaR9Ek9JKbDNGxVkRxt9"], [5, 340, 60]], [24, 0, -59, [0, "22euUEuz9DjrXjsz/15kPX"], [4, 4290032820], 0]], [1, "d5Qvm28GZODoRusK8ycQiQ", null, null, null, 1, 0]], [4, "Label", 33554432, 3, [[2, -60, [0, "36HJjHTLRK4pMI4cGUkZtG"], [5, 42.255859375, 50.4]], [30, 20, 20, -61, [0, "27pBIQX7RHzqsg5vCTwKV+"]]], [1, "7ehvVyaR9Kh7rnBdh+BmH7", null, null, null, 1, 0]], [20, "tagOn", false, 33554432, 3, [[2, -62, [0, "49HobvaApDabrHPQPmnO5Y"], [5, 40, 36]], [14, -63, [0, "ffLXsUVX9EzZPELwnMkS7h"], 1]], [1, "af//T279pNbaeTe5yzlxl9", null, null, null, 1, 0], [1, 136.889, -1.016, 0]], [4, "Label", 33554432, 4, [[2, -64, [0, "9b68smaZZCB5TSrV6Fq0dv"], [5, 80, 30.240000000000002]], [16, "", 21, 20, 24, 2, -65, [0, "89vM3qIuxBdoPsge8HEGdV"], [4, 4278190291]]], [1, "eehG9CQZRHtpfpBELSSWr9", null, null, null, 1, 0]], [10, "sprFrame", 33554432, 2, [[[2, -66, [0, "faWz5NuDBPx7KJuN+yFhxB"], [5, 760, 1200]], -67], 4, 1], [1, "9dLuAw4K5IVYZajMDHCi0o", null, null, null, 1, 0]], [9, "lbDesc", 33554432, 2, [[[2, -68, [0, "f7qoiM+ypF1K7rZIuWv7kg"], [5, 120, 50.4]], -69], 4, 1], [1, "95+kF73h1EyYCcaVEoO3+0", null, null, null, 1, 0], [1, 0, 469.037, 0]], [3, "Label", 33554432, 2, [[2, -70, [0, "34HflLDN1MubCtpoSxQ5f2"], [5, 40, 50.4]], [17, "指令", 20, 20, -71, [0, "01+d0FMlhON6Kn5ZcE3Wv6"], [4, 4280295456]]], [1, "39S3heLFNBPJu3KY+Zxv6w", null, null, null, 1, 0], [1, -176.59, 268.242, 0]], [10, "Checkmark", 33554432, 5, [[[2, -72, [0, "a9JHn0CEZGsrw3wcB/qTL3"], [5, 26, 26]], -73], 4, 1], [1, "175L9BSFRC4rRXL5w3pO9e", null, null, null, 1, 0]], [3, "Label", 33554432, 5, [[2, -74, [0, "e1QJkgPGlC75fScR5KCv7y"], [5, 160, 50.4]], [31, "是否自动选择阵营", 20, 20, -75, [0, "f6952RyoZMYZ534KijzPFO"]]], [1, "53cGywo/JPWoSSEg4Ql//h", null, null, null, 1, 0], [1, -102.01, 0, 0]], [4, "Label", 33554432, 6, [[2, -76, [0, "dcxK9ex+BC+401tT8YVx1N"], [5, 80, 30.240000000000002]], [16, "", 21, 20, 24, 2, -77, [0, "747uMppGJJmatmn0fiBd6J"], [4, 4278190291]]], [1, "44FH2/u2dPY4YKEGmCTp1S", null, null, null, 1, 0]], [15, 0, 16, [0, "01ZY+4PN1GZps5ZPphfHUs"]], [13, 2, 7, [0, "ba2V1J/vtPrI3UJKSij0Xp"], [4, 4292269782], 7], [17, "暂未选择用户", 20, 20, 17, [0, "9dSkAJ1kRGTrP8iwyyN/oX"], [4, 4280295456]], [15, 0, 19, [0, "a3V6HX+cpCdqaHMj3vPFoR"]], [32, 5, [0, "f5MhKsYxpL27padEbdv8zt"], [4, 4292269782], 5, 25], [13, 2, 6, [0, "b5EaLpoulDV7FZZkXVSKhV"], [4, 4292269782], 6]], 0, [0, 7, 1, 0, 0, 1, 0, 8, 9, 0, 9, 10, 0, 10, 26, 0, 11, 24, 0, 12, 27, 0, 13, 3, 0, 14, 4, 0, 15, 23, 0, 16, 22, 0, 17, 2, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, -1, 3, 0, -2, 4, 0, -3, 16, 0, -4, 7, 0, -5, 17, 0, -6, 18, 0, -7, 10, 0, -8, 11, 0, -9, 5, 0, -10, 6, 0, 0, 3, 0, 0, 3, 0, -1, 12, 0, -2, 13, 0, -3, 14, 0, 0, 4, 0, 0, 4, 0, 18, 4, 0, 0, 4, 0, -1, 15, 0, 0, 5, 0, 0, 5, 0, -3, 26, 0, -1, 19, 0, -2, 20, 0, 0, 6, 0, 0, 6, 0, -3, 27, 0, -1, 21, 0, 0, 7, 0, 0, 7, 0, -3, 23, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, -1, 9, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, -2, 22, 0, 0, 17, 0, -2, 24, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, -2, 25, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, 0, 21, 0, 19, 1, 8, 20, 11, 77], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 23, 23, 23, 23, 25, 26, 27, 27, 27, 27], [1, 1, 1, 2, 3, 4, 5, 1, 1, 1, 1, 2, 3, 4, 5, 1, 2, 2, 3, 4, 5], [4, 5, 0, 0, 0, 1, 2, 0, 3, 0, 6, 0, 0, 1, 2, 7, 3, 0, 0, 1, 2]], [[{"name": "default_toggle_normal", "rect": {"x": 0, "y": 0, "width": 28, "height": 28}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 28, "height": 28}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-14, -14, 0, 14, -14, 0, -14, 14, 0, 14, 14, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 28, 28, 28, 0, 0, 28, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -14, "y": -14, "z": 0}, "maxPos": {"x": 14, "y": 14, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [3], 0, [0], [6], [8]], [[{"name": "default_toggle_checkmark", "rect": {"x": 4, "y": 5, "width": 20, "height": 18}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 28, "height": 28}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-10, -9, 0, 10, -9, 0, -10, 9, 0, 10, 9, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [4, 23, 24, 23, 4, 5, 24, 5], "nuv": [0.14285714285714285, 0.17857142857142858, 0.8571428571428571, 0.17857142857142858, 0.14285714285714285, 0.8214285714285714, 0.8571428571428571, 0.8214285714285714], "minPos": {"x": -10, "y": -9, "z": 0}, "maxPos": {"x": 10, "y": 9, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [3], 0, [0], [6], [9]], [[{"name": "default_sprite", "rect": {"x": 0, "y": 2, "width": 40, "height": 36}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 40, "height": 40}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-20, -18, 0, 20, -18, 0, -20, 18, 0, 20, 18, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 38, 40, 38, 0, 2, 40, 2], "nuv": [0, 0.05, 1, 0.05, 0, 0.95, 1, 0.95], "minPos": {"x": -20, "y": -18, "z": 0}, "maxPos": {"x": 20, "y": 18, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [3], 0, [0], [6], [10]]]]