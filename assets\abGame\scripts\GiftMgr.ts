import { log, math, warn } from "cc";
import { C_GiftKey, C_HeroGiftKeyToSkillType, E_EVENT, E_GiftMessageType, E_RoleType, E_SkillType } from "../../scripts/ConstGlobal";

import { FightMgr, IUser } from "./FightMgr";

import { xcore } from "../../scripts/libs/xcore";

import { IGameStatusPush } from "../../scripts/components/GameSocketCtrl";
import { ConfigHelper } from "../../scripts/config/ConfigHelper";
import App from "../../scripts/App";


export interface IGift {
    /**用户 */
    userId: string
    nickName?: string
    avatar?: string

    /**指令 */
    key: string,
    //礼物类型
    giftType?: string
    /**数量 */
    num: number
    price?: number
    content?: string
}

export class GiftMgr {

    private _fightMgr: FightMgr

    /**每秒同步次数 */
    private _syncRate: number = 10;
    /**同步礼物 */
    private _interval: any
    /**礼物准备队列 */
    private _gifts: IGift[] = [];

    /**等待中的礼物队列 */
    private _waitGifts: IGift[] = [];



    constructor() {

        this._gifts = [];
        this._fightMgr = FightMgr.getInstance();

        this.addListener();
    }

    //添加监听
    addListener() {
        /**指令同步间隔 */
        this._interval = setInterval(this.syncGift.bind(this), 1000 / this._syncRate);
        xcore.event.addEventListener(E_EVENT.Gift, this.onRefreshGifts, this);
    }
    //移除监听
    removeListener() {
        this._interval && clearInterval(this._interval);
        xcore.event.removeEventListener(E_EVENT.Gift, this.onRefreshGifts, this);
    }
    //礼物消息同步 
    onRefreshGifts(event: string, msgs: IGameStatusPush[]) {
        log('onRefreshGifts:', msgs)
        //重新拼接礼物消息
        let datas = [] as IGift[];
        for (let i = 0; i < msgs.length; i++) {
            let msg = msgs[i];
            let data = {} as IGift;
            switch (msg.msg_type) {
                //弹幕
                case 'live_comment':
                    //加入游戏
                    if (msg.content == '1') {
                        data = {
                            key: C_GiftKey.JoinHero,
                            userId: msg.sec_openid,
                            avatar: msg.avatar_url,
                            nickName: msg.nickname,
                            num: 1
                        }
                    }
                    //666
                    else if (msg.content == '666') {
                        let commentkey = ConfigHelper.getInstance().getGiftJsonIdByType('2');
                        if (commentkey) {
                            data = {
                                key: commentkey,
                                userId: msg.sec_openid,
                                avatar: msg.avatar_url,
                                nickName: msg.nickname,
                                num: 1
                            }
                        }

                    }
                    //换皮肤
                    else if (msg.content.indexOf("换") !== -1) {

                        let jsonId = ConfigHelper.getInstance().getSkinJsonIdByContent(msg.content);
                        data = {
                            key: jsonId || C_GiftKey.Skin1,
                            userId: msg.sec_openid,
                            avatar: msg.avatar_url,
                            nickName: msg.nickname,
                            num: 1
                        }
                        // console.log("换皮肤jsonId", jsonId, data.key)
                        /*  let skinkey = ConfigHelper.getInstance().getSkinJsonIdByContent(msg.content);
                         if (skinkey) {
                             data = {
                                 key: skinkey,
                                 userId: msg.sec_openid,
                                 avatar: msg.avatar_url,
                                 nickName: msg.nickname,
                                 num: 1
                             }
                         } */
                    }
                    else if (msg.content == '左' || msg.content == '右') {

                        data = {
                            key: (msg.content == '左' ? C_GiftKey.MoveLeft : C_GiftKey.MoveRight),
                            userId: msg.sec_openid,
                            avatar: msg.avatar_url,
                            nickName: msg.nickname,
                            num: 1
                        }
                    }
                    else if (msg.content == '宝珠' || msg.content == '属性') {
                        data = {
                            key: C_GiftKey.Baozhu,
                            userId: msg.sec_openid,
                            avatar: msg.avatar_url,
                            nickName: msg.nickname,
                            num: 1
                        }
                    } else if (msg.content.indexOf("兑换") !== -1) {
                        console.log("兑换", msg.content)
                        let txts1 = [];
                        let txts2 = [];
                        let configs1 = ConfigHelper.getInstance().getDungeonConfigs();
                        let configs2 = ConfigHelper.getInstance().getExchangeConfigs();
                        for (let i = 0; i < configs1.length; i++) {
                            let config = configs1[i];
                            txts1.push(config.name);
                        }
                        for (let i = 0; i < configs2.length; i++) {
                            let config = configs2[i];
                            if (config.exchangeTypeId == '470001') {
                                txts2.push(config.name);
                            }
                        }
                        let selectTxt1;
                        let selectTxt2;
                        txts1.forEach(txt => {
                            if (msg.content.indexOf(txt) !== -1) {
                                selectTxt1 = txt;
                            }
                        });
                        txts2.forEach(txt => {
                            if (msg.content.indexOf(txt) !== -1) {
                                selectTxt1 = txt;
                            }
                        });
                        if (selectTxt1) {
                            data = {
                                key: C_GiftKey.ExchangeBossFight,
                                content: selectTxt1,
                                userId: msg.sec_openid,
                                avatar: msg.avatar_url,
                                nickName: msg.nickname,
                                num: 1
                            }
                            console.log('兑换副本次数 ')
                        } else if (selectTxt2) {
                            data = {
                                key: C_GiftKey.ExchangeWing,
                                content: selectTxt1,
                                userId: msg.sec_openid,
                                avatar: msg.avatar_url,
                                nickName: msg.nickname,
                                num: 1
                            }
                            console.log('兑换碎片 ')
                        }

                    }
                    break;
                //点赞
                case 'live_like':
                    let likekey = ConfigHelper.getInstance().getGiftJsonIdByType('1');
                    if (likekey) {
                        data = {
                            key: likekey,
                            userId: msg.sec_openid,
                            avatar: msg.avatar_url,
                            nickName: msg.nickname,
                            num: 1
                        }
                    }

                    break;
                //礼物
                case 'live_gift':
                    let giftkey = ConfigHelper.getInstance().getGiftJsonIdByGiftId(msg.sec_gift_id);

                    if (giftkey) {
                        data = {
                            key: giftkey,
                            userId: msg.sec_openid,
                            avatar: msg.avatar_url,
                            nickName: msg.nickname,
                            num: msg.gift_num
                        }
                    }

                    break;


                default:
                    break;
            }
            //
            if (data.key) {
                datas.push(data);
            }
            data = null;
        }

        this.addGifts(datas);
    }

    /**把礼物指令消息同步到游戏中展示效果 */
    syncGift() {
        //为开始或准备开始游戏前刷的礼物不同步
        if (!this._fightMgr.checkIfAbleGift()) return
        let gifts = this._gifts;
        this._gifts = [];
        this.emitGifts(gifts);
        App.getInstance().updateOperation();
    }

    /**命令添加礼物消息 */
    addGifts(gifts: IGift[]) {
        this._gifts = this._gifts.concat(gifts);
        log('addGifts:', gifts)
        if (!xcore.gameData.giftData) { xcore.gameData.giftData = {} };
        if (!xcore.gameData.giftData[xcore.gameData.combatId]) {
            xcore.gameData.giftData[xcore.gameData.combatId] = [];
        }
        xcore.gameData.giftData[xcore.gameData.combatId] = xcore.gameData.giftData[xcore.gameData.combatId].concat(gifts);
    }

    destroy() {
        this.removeListener();
    }
    /**礼物和仙族技能对应关系 */
    getSkilTypeByGiftKey(giftKey: string): E_SkillType {
        return C_HeroGiftKeyToSkillType[giftKey];

    }


    /**同步用户指令到战斗管理器  */
    emitGifts(gifts: IGift[]) {

        for (let i = 0; i < gifts.length; i++) {
            let gift = gifts[i];

            switch (gift.key) {
                case C_GiftKey.JoinMonster:
                case C_GiftKey.JoinHero:
                    this._fightMgr.addUser(gift.userId, E_RoleType.Hero, gift.nickName, gift.avatar);

                    break;
                //添加技能or生成怪物
                case C_GiftKey.Like:
                case C_GiftKey.SixSix:
                case C_GiftKey.Gift01:


                case C_GiftKey.Gift02:
                case C_GiftKey.Gift03:
                case C_GiftKey.Gift04:
                case C_GiftKey.Gift05:
                case C_GiftKey.Gift06:


                    let user = this.checkIfJoinGroup(gift)
                    if (!user) {

                        return
                    }
                    let key;
                    if (user.type == E_RoleType.Hero) {
                        key = this.getSkilTypeByGiftKey(gift.key) as E_SkillType;
                        this._fightMgr.addGift(user, gift.key, key, gift.num);

                    }
                    if (gift.key == C_GiftKey.Gift01) {
                        this._fightMgr.updateTowerPoint(gift.num);
                    }
                    //电池
                    if (gift.key == C_GiftKey.Gift04) {
                        this._fightMgr.upLevTower(gift.num);
                    }
                    break;


                /**更换皮肤 */

                case C_GiftKey.Skin1:
                case C_GiftKey.Skin2:
                case C_GiftKey.Skin3:
                case C_GiftKey.Skin4:
                case C_GiftKey.Skin5:
                case C_GiftKey.Skin6:
                case C_GiftKey.Skin7:
                case C_GiftKey.Skin8:
                case C_GiftKey.Skin9:
                case C_GiftKey.Skin10:
                case C_GiftKey.Skin11:
                case C_GiftKey.Skin12:
                case C_GiftKey.Skin13:
                case C_GiftKey.Skin14:
                case C_GiftKey.Skin15:
                case C_GiftKey.Skin16:
                case C_GiftKey.Skin17:
                case C_GiftKey.Skin18:
                case C_GiftKey.Skin19:
                case C_GiftKey.Skin20:
                case C_GiftKey.Skin21:
                case C_GiftKey.Skin22:
                case C_GiftKey.Skin23:
                case C_GiftKey.Skin24:
                case C_GiftKey.Skin25:
                    let userData = this.checkIfJoinGroup(gift, true);
                    if (!userData) {
                        log('换肤失败,请先加入游戏')
                        return
                    }
                    let content = ConfigHelper.getInstance().getSkinJsonIdByContent(gift.key);
                    this._fightMgr.doSwitchSkin(userData, content, gift.key);
                    break;
                case C_GiftKey.MoveLeft:
                case C_GiftKey.MoveRight:
                    let moveuserData = this.checkIfJoinGroup(gift, true);
                    if (!moveuserData) {
                        return
                    }
                    if (gift.key == C_GiftKey.MoveRight) {
                        moveuserData.role?.moveRight();
                    } else if (gift.key == C_GiftKey.MoveLeft) {
                        moveuserData.role?.moveLeft();
                    }
                    break
                case C_GiftKey.Baozhu:

                    let baozhuUserData = this.checkIfJoinGroup(gift, true);
                    if (!baozhuUserData) {
                        log('换肤失败,请先加入游戏')
                        return
                    }
                    xcore.event.raiseEvent(E_EVENT.GiftMessage, {
                        type: E_GiftMessageType.UserInfo,
                        userId: baozhuUserData.userId,
                        name: baozhuUserData.nickName,
                        avatar: baozhuUserData.iconUrl,
                        minAtkNum: baozhuUserData.role.data.minAtkNum,
                        maxAtkNum: baozhuUserData.role.data.maxAtkNum,
                        skinId: baozhuUserData.role.data.skinId,
                        dimonEffects: baozhuUserData.role.data.dimonEffects,
                        speed: baozhuUserData.role.getAtkSpeed()
                    })

                    break
                case C_GiftKey.ExchangeBossFight:
                    console.log('gift.Exchange:', gift)
                    this._fightMgr.checkLingyunExchange(gift.userId, E_RoleType.Hero, gift.nickName, gift.avatar, gift.content)
                    break
                case C_GiftKey.ExchangeWing:
                       console.log('gift.Exchange2:', gift)
                    this._fightMgr.checkExchangeWing(gift.userId, gift.content)
                    break
                default:
                    if (gift.key.indexOf("换") !== -1) {

                        let userData = this.checkIfJoinGroup(gift, true);
                        if (!userData) {
                            return
                        }

                        this._fightMgr.doSwitchSkin(userData, gift.key);
                    }

                    break;
            }
        }
    }

    //判断用户是否已加入游戏，未加入的自动先加入
    checkIfJoinGroup(gift: IGift, isSkinKey: boolean = false): IUser {

        //游戏未开始
        if (!this._fightMgr.checkIfAbleGift()) {
            log(`游戏暂未开始 ${gift.key}指令无法生效`);
            this.backToWaitGiftGroup({
                userId: gift.userId,
                key: gift.key,
                num: gift.num
            })
            return null
        }
        let user = this._fightMgr.findUser(gift.userId);

        if (!user) {
            if (isSkinKey) return null
            this._fightMgr.addUser(gift.userId, E_RoleType.Hero, gift.nickName, gift.avatar);
            user = this._fightMgr.findUser(gift.userId);
        }
        return user
    }
    //游戏未开始时，推入礼物等待列表
    backToWaitGiftGroup(gift: IGift) {
        this._waitGifts.push(gift);
    }

}