{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "4deeec5b-e64a-4eca-924a-d332f42a1d07", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "4deeec5b-e64a-4eca-924a-d332f42a1d07@6c48a", "displayName": "tianpeng-attack_04", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "4deeec5b-e64a-4eca-924a-d332f42a1d07", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "4deeec5b-e64a-4eca-924a-d332f42a1d07@f9941", "displayName": "tianpeng-attack_04", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 5, "offsetY": -7.5, "trimX": 45, "trimY": 36, "width": 170, "height": 143, "rawWidth": 250, "rawHeight": 200, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-85, -71.5, 0, 85, -71.5, 0, -85, 71.5, 0, 85, 71.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [45, 164, 215, 164, 45, 21, 215, 21], "nuv": [0.18, 0.105, 0.86, 0.105, 0.18, 0.82, 0.86, 0.82], "minPos": [-85, -71.5, 0], "maxPos": [85, 71.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "4deeec5b-e64a-4eca-924a-d332f42a1d07@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "4deeec5b-e64a-4eca-924a-d332f42a1d07@6c48a"}}