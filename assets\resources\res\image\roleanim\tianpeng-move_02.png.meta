{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "0edaa845-7cd7-4028-a2e8-dac922215272", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "0edaa845-7cd7-4028-a2e8-dac922215272@6c48a", "displayName": "tianpeng-move_02", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "0edaa845-7cd7-4028-a2e8-dac922215272", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "0edaa845-7cd7-4028-a2e8-dac922215272@f9941", "displayName": "tianpeng-move_02", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": -5, "offsetY": -14, "trimX": 61, "trimY": 45, "width": 118, "height": 138, "rawWidth": 250, "rawHeight": 200, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-59, -69, 0, 59, -69, 0, -59, 69, 0, 59, 69, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [61, 155, 179, 155, 61, 17, 179, 17], "nuv": [0.244, 0.085, 0.716, 0.085, 0.244, 0.775, 0.716, 0.775], "minPos": [-59, -69, 0], "maxPos": [59, 69, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "0edaa845-7cd7-4028-a2e8-dac922215272@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "0edaa845-7cd7-4028-a2e8-dac922215272@6c48a"}}