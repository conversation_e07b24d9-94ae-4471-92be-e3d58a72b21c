{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "c081a0cc-8fd0-4284-a1f3-76171dc17fb2", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "c081a0cc-8fd0-4284-a1f3-76171dc17fb2@6c48a", "displayName": "xiaolonglv-attack_06", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "c081a0cc-8fd0-4284-a1f3-76171dc17fb2", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "c081a0cc-8fd0-4284-a1f3-76171dc17fb2@f9941", "displayName": "xiaolonglv-attack_06", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": -35.5, "offsetY": -11, "trimX": 16, "trimY": 40, "width": 97, "height": 142, "rawWidth": 200, "rawHeight": 200, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-48.5, -71, 0, 48.5, -71, 0, -48.5, 71, 0, 48.5, 71, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [16, 160, 113, 160, 16, 18, 113, 18], "nuv": [0.08, 0.09, 0.565, 0.09, 0.08, 0.8, 0.565, 0.8], "minPos": [-48.5, -71, 0], "maxPos": [48.5, 71, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "c081a0cc-8fd0-4284-a1f3-76171dc17fb2@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "c081a0cc-8fd0-4284-a1f3-76171dc17fb2@6c48a"}}