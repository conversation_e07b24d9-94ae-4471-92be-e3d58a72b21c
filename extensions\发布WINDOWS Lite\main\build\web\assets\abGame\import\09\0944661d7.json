[1, ["52fHu7D8hGm5vLaoALoXCl", "03sNPD7AxHb6Gmvcw8pNrD@6c48a", "2aHUZqwgVHk6PJ1pjcZGzg@6c48a", "9eBf17r6BDAbzgTV5UmvMX@f9941", "2aHUZqwgVHk6PJ1pjcZGzg@f9941", "f0OY3+tghENoQunTr2ytSt@f9941", "03sNPD7AxHb6Gmvcw8pNrD@f9941", "4bbyTqTcNPnrmVLDfyjIrt@f9941", "57UgcWSMhKGYrPQcn4d3+w@f9941", "9eBf17r6BDAbzgTV5UmvMX@6c48a", "f0OY3+tghENoQunTr2ytSt@6c48a"], ["node", "_spriteFrame", "_textureSource", "_font", "root", "lbDesc", "btnClose", "sprFrame", "ndRoot", "data"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_layer", "_components", "_prefab", "_parent", "_children", "_lpos"], 1, 9, 4, 1, 2, 5], ["cc.Label", ["_string", "_actualFontSize", "_isSystemFontUsed", "_horizontalAlign", "_lineHeight", "_fontSize", "_overflow", "node", "__prefab", "_font", "_color"], -4, 1, 4, 6, 5], ["cc.Node", ["_name", "_layer", "_parent", "_components", "_prefab", "_lpos", "_children"], 1, 1, 12, 4, 5, 2], ["cc.UITransform", ["node", "__prefab", "_contentSize"], 3, 1, 4, 5], ["cc.Sprite", ["node", "__prefab", "_spriteFrame"], 3, 1, 4, 6], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["f8d39vUb1ZJHbtDd3rUR5g1", ["node", "__prefab", "ndRoot", "sprFrame", "btnClose", "lbDesc"], 3, 1, 4, 1, 1, 1, 1], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.<PERSON><PERSON>", ["_transition", "_zoomScale", "node", "__prefab"], 1, 1, 4]], [[7, 0, 2], [9, 0, 1, 2, 3, 4, 5, 5], [4, 0, 1, 2, 1], [1, 0, 1, 4, 2, 3, 6, 3], [5, 0, 1, 2, 1], [4, 0, 1, 1], [6, 0, 2], [1, 0, 1, 5, 2, 3, 3], [1, 0, 1, 4, 5, 2, 3, 3], [1, 0, 1, 4, 2, 3, 3], [3, 0, 1, 2, 6, 3, 4, 5, 3], [3, 0, 1, 2, 3, 4, 3], [3, 0, 1, 2, 3, 4, 5, 3], [8, 0, 1, 2, 3, 4, 5, 1], [5, 0, 1, 1], [2, 0, 3, 1, 4, 2, 7, 8, 9, 6], [2, 0, 3, 1, 5, 4, 2, 7, 8, 10, 9, 7], [2, 0, 1, 2, 7, 8, 10, 9, 4], [2, 0, 3, 1, 5, 4, 6, 2, 7, 8, 8], [10, 0, 1, 2, 3, 3]], [[[{"name": "ViewCrossRewardDesc_spr_01", "rect": {"x": 0, "y": 0, "width": 600, "height": 353}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 600, "height": 353}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-300, -176.5, 0, 300, -176.5, 0, -300, 176.5, 0, 300, 176.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 353, 600, 353, 0, 0, 600, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -300, "y": -176.5, "z": 0}, "maxPos": {"x": 300, "y": 176.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [1]], [[{"name": "ViewCrossRewardDesc_spr_03", "rect": {"x": 0, "y": 0, "width": 976, "height": 1188}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 976, "height": 1188}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-488, -594, 0, 488, -594, 0, -488, 594, 0, 488, 594, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 1188, 976, 1188, 0, 0, 976, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -488, "y": -594, "z": 0}, "maxPos": {"x": 488, "y": 594, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [2]], [[[6, "ViewCrossRewardDesc"], [7, "ViewCrossRewardDesc", 33554432, [-8], [[5, -2, [0, "0bFuqVCplGxrcBd/a+y+2x"]], [13, -7, [0, "4eAq0rCyRIwpBVydk5ZVdK"], -6, -5, -4, -3]], [1, "4e242xnsNIUrfk2SgaweBG", null, null, null, -1, 0]], [8, "ndCore", 33554432, 1, [-10, -11, -12, -13, -14, -15, -16, -17, -18], [[5, -9, [0, "85q8NcJDZMQavqLkEOtXia"]]], [1, "db1KA5CwpHVK6XswSdyF1N", null, null, null, 1, 0]], [10, "btnClose", 33554432, 2, [-22], [[[2, -19, [0, "11WUAqknRBA6jjvalG6Vxy"], [5, 301, 92]], [4, -20, [0, "b7HNRRGI1GYpNACigGRXnS"], 7], -21], 4, 4, 1], [1, "20CiTXep1PgqFtc1qfJq/t", null, null, null, 1, 0], [1, 0, -604.562, 0]], [11, "sprFrame", 33554432, 2, [[[2, -23, [0, "23rnq5MGpOrLkjWRdLlCS3"], [5, 40, 36]], -24], 4, 1], [1, "15AtONIpdEFLKTvQT6eIcY", null, null, null, 1, 0]], [3, "ViewCrossRewardDesc_spr_04", 33554432, 2, [[2, -25, [0, "18B1Pkt+ZLlZrrtF7fLM87"], [5, 729, 326]], [4, -26, [0, "c7Qqz9+cVFrqyD0tnWrGFd"], 0]], [1, "04W9EnsTxOiKdK8OrxJm1A", null, null, null, 1, 0], [1, 0, -353.28499999999997, 0]], [3, "ViewCrossRewardDesc_spr_03", 33554432, 2, [[2, -27, [0, "e1VdhemThBpJpSbCrCrNY7"], [5, 976, 1188]], [4, -28, [0, "5f78PWUGJKbZ5AwOfaaFJ4"], 1]], [1, "acZnZTiCBLQ68x7ZmtkYWc", null, null, null, 1, 0], [1, 0, 180.63599999999997, 0]], [3, "ViewCrossRewardDesc_spr_02", 33554432, 2, [[2, -29, [0, "190aq4qslC4Z2gMRjGg6Yb"], [5, 828, 422]], [4, -30, [0, "eeTzl6zK5Cc52T/tS/Wzqu"], 2]], [1, "4frhAhpJZIjr0+ll9egZiM", null, null, null, 1, 0], [1, 0, 55.58100000000002, 0]], [3, "ViewCrossRewardDesc_spr_01", 33554432, 2, [[2, -31, [0, "73rfOQ/ihMM6lG8dyDo3pt"], [5, 600, 353]], [4, -32, [0, "08TMwuOURG/YLhEyyFKE5H"], 3]], [1, "d1RJ2HC4NCiLH8RVs4pIMM", null, null, null, 1, 0], [1, 0, 464.095, 0]], [3, "lbRule-001", 33554432, 2, [[2, -33, [0, "97+/5onVdGtIwholT4IG/8"], [5, 338.239990234375, 63]], [15, "首次通关100%获得", 0, 40, 50, false, -34, [0, "750yC2v2ZJUrgt+8smZDNR"], 4]], [1, "4eP3FDlu9OdpQ/NQtkUAan", null, null, null, 1, 0], [1, -117.25599999999997, 377.00800000000004, 0]], [3, "lbRule-002", 33554432, 2, [[2, -35, [0, "00+9cqTkBNLJtWf+dACXS2"], [5, 224, 75.6]], [16, "传说皮肤", 0, 56, 56, 60, false, -36, [0, "2a02Ftz0dJmZsgtciU6XkP"], [4, 4281984511], 5]], [1, "4bvN8NfARITYsXqZREMTDz", null, null, null, 1, 0], [1, 168.55399999999997, 380.20000000000005, 0]], [9, "Label", 33554432, 3, [[2, -37, [0, "89/brf2fJEo7Z7Cus/B4ac"], [5, 80, 50.4]], [17, "关闭", 40, false, -38, [0, "30uoa+A7xMGqLlp9FHe8al"], [4, 4282074287], 6]], [1, "8e0RKy2EFI4ZnF96pWMRDY", null, null, null, 1, 0]], [12, "lbRule", 33554432, 2, [[[2, -39, [0, "81327ff8FKL7jEqBNrYhZ2"], [5, 680, 63]], -40], 4, 1], [1, "f0SgX1Bf1B0bRcC5DXRZKn", null, null, null, 1, 0], [1, 0, -353.28499999999997, 0]], [14, 4, [0, "abOLHDj15Oz4ubTGR3Y11x"]], [19, 3, 1.02, 3, [0, "6cMuB1o4RPB4Z0Qii0Khvb"]], [18, "规则说明：", 0, 36, 36, 50, 3, false, 12, [0, "d7entAViNEn5nX1VO8qbjp"]]], 0, [0, 4, 1, 0, 0, 1, 0, 5, 15, 0, 6, 14, 0, 7, 13, 0, 8, 2, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, -1, 4, 0, -2, 5, 0, -3, 6, 0, -4, 7, 0, -5, 8, 0, -6, 9, 0, -7, 10, 0, -8, 3, 0, -9, 12, 0, 0, 3, 0, 0, 3, 0, -3, 14, 0, -1, 11, 0, 0, 4, 0, -2, 13, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, -2, 15, 0, 9, 1, 40], [0, 0, 0, 0, 0, 0, 0, 0, 13, 15], [1, 1, 1, 1, 3, 3, 3, 1, 1, 3], [3, 4, 5, 6, 0, 0, 0, 7, 8, 0]], [[{"name": "ViewCrossRewardDesc_spr_04", "rect": {"x": 0, "y": 0, "width": 729, "height": 326}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 729, "height": 326}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-364.5, -163, 0, 364.5, -163, 0, -364.5, 163, 0, 364.5, 163, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 326, 729, 326, 0, 0, 729, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -364.5, "y": -163, "z": 0}, "maxPos": {"x": 364.5, "y": 163, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [9]], [[{"name": "ViewCrossRewardDesc_spr_02", "rect": {"x": 0, "y": 0, "width": 828, "height": 422}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 828, "height": 422}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-414, -211, 0, 414, -211, 0, -414, 211, 0, 414, 211, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 422, 828, 422, 0, 0, 828, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -414, "y": -211, "z": 0}, "maxPos": {"x": 414, "y": 211, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [10]]]]