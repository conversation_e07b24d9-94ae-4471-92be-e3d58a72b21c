[1, ["ddbz1KAh1IQIrIfgE+r728@6c48a"], ["_textureSource"], ["cc.SpriteFrame"], 0, [{"name": "huodou-idle_13", "rect": {"x": 145, "y": 100, "width": 611, "height": 476}, "offset": {"x": -5, "y": -50}, "originalSize": {"width": 911, "height": 576}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-305.5, -238, 0, 305.5, -238, 0, -305.5, 238, 0, 305.5, 238, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [145, 476, 756, 476, 145, 0, 756, 0], "nuv": [0.15916575192096596, 0, 0.8298572996706916, 0, 0.15916575192096596, 0.8263888888888888, 0.8298572996706916, 0.8263888888888888], "minPos": {"x": -305.5, "y": -238, "z": 0}, "maxPos": {"x": 305.5, "y": 238, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]]