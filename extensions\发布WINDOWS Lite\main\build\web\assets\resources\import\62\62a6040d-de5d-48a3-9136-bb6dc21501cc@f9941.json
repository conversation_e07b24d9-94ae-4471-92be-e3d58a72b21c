[1, ["62pgQN3l1Io5E2u23CFQHM@6c48a"], ["_textureSource"], ["cc.SpriteFrame"], 0, [{"name": "huodou-idle_02", "rect": {"x": 144, "y": 174, "width": 650, "height": 402}, "offset": {"x": 13.5, "y": -87}, "originalSize": {"width": 911, "height": 576}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-325, -201, 0, 325, -201, 0, -325, 201, 0, 325, 201, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [144, 402, 794, 402, 144, 0, 794, 0], "nuv": [0.15806805708013172, 0, 0.8715697036223929, 0, 0.15806805708013172, 0.6979166666666666, 0.8715697036223929, 0.6979166666666666], "minPos": {"x": -325, "y": -201, "z": 0}, "maxPos": {"x": 325, "y": 201, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]]