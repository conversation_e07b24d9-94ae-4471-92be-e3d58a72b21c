System.register("chunks:///_virtual/resources",["./UILoading.ts","./UILoadingProgress.ts","./ViewLogin.ts","./ViewScrollViewTemplate.ts","./ViewTemp.ts"],(function(){return{setters:[null,null,null,null,null],execute:function(){}}}));

System.register("chunks:///_virtual/UILoading.ts",["./rollupPluginModLoBabelHelpers.js","cc","./xcore.ts"],(function(r){var e,t,i,n,o,a,c,l,p;return{setters:[function(r){e=r.applyDecoratedDescriptor,t=r.inheritsLoose,i=r.initializerDefineProperty,n=r.assertThisInitialized},function(r){o=r.cclegacy,a=r._decorator,c=r.Sprite,l=r.Component},function(r){p=r.xcore}],execute:function(){var u,s,f,g,d,y,b;o._RF.push({},"dbbf0iVgXZCyIePYAmz4xFM","UILoading",void 0);var h=a.ccclass,L=a.property;r("UILoading",(u=h("UILoading"),s=L(c),f=L(c),u((y=e((d=function(r){function e(){for(var e,t=arguments.length,o=new Array(t),a=0;a<t;a++)o[a]=arguments[a];return e=r.call.apply(r,[this].concat(o))||this,i(e,"sprLogo",y,n(e)),i(e,"sprBg",b,n(e)),e}return t(e,r),e.prototype.initUI=function(r){if(r)for(var e in r)if(Object.prototype.hasOwnProperty.call(r,e)){var t=r[e];p.res.bundleLoadSprite(t.bundleName,t.path,this[e])}},e}(l)).prototype,"sprLogo",[s],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),b=e(d.prototype,"sprBg",[f],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),g=d))||g));o._RF.pop()}}}));

System.register("chunks:///_virtual/UILoadingProgress.ts",["./rollupPluginModLoBabelHelpers.js","cc","./xcore.ts"],(function(t){var i,e,r,n,o,s,a,l,g,u,c,h,d,p,f,b;return{setters:[function(t){i=t.applyDecoratedDescriptor,e=t.inheritsLoose,r=t.initializerDefineProperty,n=t.assertThisInitialized,o=t.asyncToGenerator,s=t.regeneratorRuntime},function(t){a=t.cclegacy,l=t._decorator,g=t.Label,u=t.Sprite,c=t.CCFloat,h=t.v3,d=t.view,p=t.tween,f=t.Component},function(t){b=t.xcore}],execute:function(){var x,T,_,m,y,L,P,v,I,w,B,F,U,z,k;a._RF.push({},"7bd31kjyQpHuJlVxEROB29B","UILoadingProgress",void 0);var R=l.ccclass,S=l.property;t("UILoadingProgress",(x=R("UILoadingProgress"),T=S(g),_=S(g),m=S(u),y=S(u),L=S({type:c,displayName:"距离底部长度"}),P=S({type:c,displayName:"进度条底文字切换间隔"}),x((w=i((I=function(t){function i(){for(var i,e=arguments.length,o=new Array(e),s=0;s<e;s++)o[s]=arguments[s];return i=t.call.apply(t,[this].concat(o))||this,r(i,"lbProgressTxt",w,n(i)),r(i,"lbProgressVal",B,n(i)),r(i,"sprBg",F,n(i)),r(i,"sprBar",U,n(i)),r(i,"lenToBottom",z,n(i)),r(i,"switchTimeLoadingTxt",k,n(i)),i._loadingTxtIndex=0,i._loadingTxts=["资源加载中","资源加载中.","资源加载中..","资源加载中..."],i._isLoadingStart=!1,i._isLoadingFinished=!1,i._timer=0,i}e(i,t);var a=i.prototype;return a.initUI=function(t,i,e){var r=this;return this._loadingTxtIndex=0,this.lbProgressVal.string="0%",this.lbProgressTxt.string=this._loadingTxts[this._loadingTxtIndex]||"",t&&t.length>0&&(this._loadingTxts=t),this.loadRes(e),this.onLoginFinish=i,this.node.setPosition(h(0,-d.getVisibleSize().height/2+this.lenToBottom)),function(){r._isLoadingFinished=!0}},a.loadRes=function(){var t=o(s().mark((function t(i){var e,r;return s().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!i){t.next=10;break}t.t0=s().keys(i);case 2:if((t.t1=t.t0()).done){t.next=10;break}if(e=t.t1.value,!Object.prototype.hasOwnProperty.call(i,e)){t.next=8;break}return r=i[e],t.next=8,b.res.bundleLoadSprite(r.bundleName,r.path,this[e]);case 8:t.next=2;break;case 10:this._isLoadingStart=!0;case 11:case"end":return t.stop()}}),t,this)})));return function(i){return t.apply(this,arguments)}}(),a.doProgressLoading=function(t){var i=this;if(this._isLoadingStart&&this.sprBar.spriteFrame){this._timer+=t;var e=1-1/(1+this._timer);if(this._isLoadingFinished){this._isLoadingStart=!1;var r=this.lbProgressVal;p(this.sprBar).to(.3,{fillRange:1},{onUpdate:function(t,i){r.string=Math.floor(100*Math.min(e+(1-e)*i,1))+"%"}}).call((function(){i.lbProgressVal.string="100%",i.lbProgressTxt.string="加载完成"})).delay(.2).call((function(){i.onLoginFinish(),b.ui.closeUI("UILoading"),b.ui.closeUI("UILoadingProgress")})).start()}else if(this.lbProgressVal.string=Math.floor(100*e)+"%",this.sprBar.fillRange=e,this._timer%this.switchTimeLoadingTxt<=t){var n=this._loadingTxts.length;this.lbProgressTxt.string=this._loadingTxts[this._loadingTxtIndex%n]||"",this._loadingTxtIndex++,this._loadingTxtIndex>=n&&(this._loadingTxtIndex=0)}}},a.onLoginFinish=function(){},a.update=function(t){this.doProgressLoading(t)},i}(f)).prototype,"lbProgressTxt",[T],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),B=i(I.prototype,"lbProgressVal",[_],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),F=i(I.prototype,"sprBg",[m],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),U=i(I.prototype,"sprBar",[y],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),z=i(I.prototype,"lenToBottom",[L],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 160}}),k=i(I.prototype,"switchTimeLoadingTxt",[P],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return.3}}),v=I))||v));a._RF.pop()}}}));

System.register("chunks:///_virtual/ViewLogin.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ViewBase.ts","./xcore.ts","./App.ts","./ConstGlobal.ts","./Net.ts","./Tool.ts"],(function(e){var n,t,a,o,r,i,c,s,u,l,g,p,f,d,m,h,b,w,x,v;return{setters:[function(e){n=e.applyDecoratedDescriptor,t=e.inheritsLoose,a=e.initializerDefineProperty,o=e.assertThisInitialized,r=e.asyncToGenerator,i=e.regeneratorRuntime},function(e){c=e.cclegacy,s=e._decorator,u=e.EditBox,l=e.Button,g=e.Sprite,p=e.log},function(e){f=e.ViewBase},function(e){d=e.xcore},function(e){m=e.default},function(e){h=e.C_Bundle,b=e.C_Scene,w=e.E_Channel},function(e){x=e.default},function(e){v=e.default}],execute:function(){var y,k,L,D,T,I,_;c._RF.push({},"da5ebcAY+pPHoAKTa2kUGpr","ViewLogin",void 0);var C=s.ccclass,z=s.property;e("ViewLogin",(y=C("ViewLogin"),k=z(u),L=z(l),y((I=n((T=function(e){function n(){for(var n,t=arguments.length,r=new Array(t),i=0;i<t;i++)r[i]=arguments[i];return n=e.call.apply(e,[this].concat(r))||this,a(n,"edTxt",I,o(n)),a(n,"btnLogin",_,o(n)),n}t(n,e);var c=n.prototype;return c.onLoadCompleted=function(){var e=this;this.btnLogin.node.on("click",r(i().mark((function n(){return i().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(e.edTxt.string){n.next=3;break}return d.ui.showToast("请先输入匹配码"),n.abrupt("return");case 3:return n.prev=3,e.btnLogin.interactable=!1,e.btnLogin.getComponent(g).grayscale=!0,n.next=8,e.login();case 8:return n.next=10,e.loadRes();case 10:m.getInstance().initOperation(),d.ui.switchScene(h.abMain,b.Main),n.next=19;break;case 14:n.prev=14,n.t0=n.catch(3),e.btnLogin.interactable=!0,e.btnLogin.getComponent(g).grayscale=!1,d.ui.showToast("登录失败 err:"+n.t0);case 19:case"end":return n.stop()}}),n,null,[[3,14]])}))),this)},c.loadRes=function(){var e=r(i().mark((function e(){return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if("https://gz-cdn-1258783731.cos.ap-guangzhou.myqcloud.com/2024/2nantianmen/game_configs/dev/_total.json",d.config){e.next=5;break}return e.next=4,d.res.remoteLoadJson("https://gz-cdn-1258783731.cos.ap-guangzhou.myqcloud.com/2024/2nantianmen/game_configs/dev/_total.json");case 4:d.config=e.sent;case 5:p("gameConfig:",d.config);case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),c.login=function(){var e=r(i().mark((function e(){var n,t=this;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=v.getCommandLineArgs(),e.next=3,x.getCombatId();case 3:return console.log("query",n,n.token),n.token?(d.gameData.token=n.token,d.channel=w.TIKTOK):(d.gameData.appId="65e700e54ad6a",d.gameData.channelId="1008",d.channel=w.GAME560,d.gameData.userName="akayip",d.gameData.password="841892819"),e.next=8,x.login(d.channel,d.gameData.channelId,d.gameData.token,d.gameData.userName,d.gameData.password,d.gameData.appId,d.gameData.combatId);case 8:return e.next=10,x.getLiverInfo();case 10:if(d.channel!=w.TIKTOK){e.next=13;break}return e.next=13,x.updateTokenToIo();case 13:return console.log("liveinfo",d.gameData.baseInfo),e.abrupt("return",new Promise((function(e,n){t.scheduleOnce((function(){e(!0)}),.5)})));case 15:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),n}(f)).prototype,"edTxt",[k],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),_=n(T.prototype,"btnLogin",[L],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),D=T))||D));c._RF.pop()}}}));

System.register("chunks:///_virtual/ViewScrollViewTemplate.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ViewBase.ts","./ScrollViewProCom.ts"],(function(e){var r,i,t,n,l,o,a,c,s,u;return{setters:[function(e){r=e.applyDecoratedDescriptor,i=e.inheritsLoose,t=e.initializerDefineProperty,n=e.assertThisInitialized,l=e.asyncToGenerator,o=e.regeneratorRuntime},function(e){a=e.cclegacy,c=e._decorator},function(e){s=e.ViewBase},function(e){u=e.default}],execute:function(){var p,f,w,y,v,h,m,V,b,d,g;a._RF.push({},"bc186inezRO/Jh6kclgRY5R","ViewScrollViewTemplate",void 0);var S=c.ccclass,z=c.property;e("ViewScrollViewTemplate",(p=S("ViewScrollViewTemplate"),f=z({type:u,displayName:"垂直布局列表"}),w=z({type:u,displayName:"横向布局列表"}),y=z({type:u,displayName:"网格布局列表"}),v=z({type:u,displayName:"复合布局列表"}),p((V=r((m=function(e){function r(){for(var r,i=arguments.length,l=new Array(i),o=0;o<i;o++)l[o]=arguments[o];return r=e.call.apply(e,[this].concat(l))||this,t(r,"scrollviewV",V,n(r)),t(r,"scrollviewH",b,n(r)),t(r,"scrollviewG",d,n(r)),t(r,"scrollviewM",g,n(r)),r}i(r,e);var a=r.prototype;return a.start=function(){this.refreshSv()},a.refreshSv=function(){var e=l(o().mark((function e(){var r,i;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:for(r=[],i=0;i<1e3;i++)r.push([1,2,3,4,5,6]);this.scrollviewV.setView(r,(function(e,r,i){}));case 3:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}(),r}(s)).prototype,"scrollviewV",[f],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),b=r(m.prototype,"scrollviewH",[w],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),d=r(m.prototype,"scrollviewG",[y],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),g=r(m.prototype,"scrollviewM",[v],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),h=m))||h));a._RF.pop()}}}));

System.register("chunks:///_virtual/ViewTemp.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ViewBase.ts"],(function(e){var t,n,r,i,o,a,u,c,p,s;return{setters:[function(e){t=e.applyDecoratedDescriptor,n=e.inheritsLoose,r=e.initializerDefineProperty,i=e.assertThisInitialized,o=e.asyncToGenerator,a=e.regeneratorRuntime},function(e){u=e.cclegacy,c=e._decorator,p=e.Button},function(e){s=e.ViewBase}],execute:function(){var l,f,d,v,y;u._RF.push({},"abdd1C3BepLkb8UGiBLzWVm","ViewTemp",void 0);var m=c.ccclass,w=c.property;e("ViewTemp",(l=m("ViewTemp"),f=w(p),l((y=t((v=function(e){function t(){for(var t,n=arguments.length,o=new Array(n),a=0;a<n;a++)o[a]=arguments[a];return t=e.call.apply(e,[this].concat(o))||this,r(t,"btnCut",y,i(t)),t._tag=void 0,t._txt=void 0,t}n(t,e);var u=t.prototype;return u.start=function(){},u.onOpenCompleted=function(){var e=o(a().mark((function e(){return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),u.update=function(e){},t}(s)).prototype,"btnCut",[f],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),d=v))||d));u._RF.pop()}}}));

(function(r) {
  r('virtual:///prerequisite-imports/resources', 'chunks:///_virtual/resources'); 
})(function(mid, cid) {
    System.register(mid, [cid], function (_export, _context) {
    return {
        setters: [function(_m) {
            var _exportObj = {};

            for (var _key in _m) {
              if (_key !== "default" && _key !== "__esModule") _exportObj[_key] = _m[_key];
            }
      
            _export(_exportObj);
        }],
        execute: function () { }
    };
    });
});