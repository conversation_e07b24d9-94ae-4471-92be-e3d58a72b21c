import { _decorator, Component, easing, Label, log, Node, RichText, size, Sprite, SpriteFrame, tween, Tween, UITransform, v2, v3, Vec3, view } from 'cc';
import { GiftMessageMgr, IGiftMessage } from '../../scripts/GiftMessageMgr';

import { xcore } from '../../../scripts/libs/xcore';
import { C_GreatSkill, C_HeroGiftKeyToSkillType, E_GiftMessageType, E_SkillType } from '../../../scripts/ConstGlobal';
import { ConfigHelper } from 'db://assets/scripts/config/ConfigHelper';
import { StringUtil } from 'db://assets/scripts/libs/utils/StringUtil';

const { ccclass, property } = _decorator;

@ccclass('UnitGiftMessage')
export class UnitGiftMessage extends Component {

    @property(Node)
    private ndFrame01: Node = null;

    @property(Node)
    private ndFrame02: Node = null;


    @property(Node)
    private ndDetail: Node = null;

    @property(Node)
    private ndPgs: Node = null;

    @property(Label)
    private lbPgs: Label = null;

    @property(Sprite)
    private sprPgsBar: Sprite = null;

    @property(Label)
    private lbName: Label = null;

    @property(RichText)
    private lbDesc: RichText = null;


    @property(Node)
    private ndAvatar: Node = null;

    @property(Sprite)
    private sprAvatar: Sprite = null;

    @property(Node)
    private ndSmallSkill: Node = null;

    @property(Label)
    private lbSmallSkillNum: Label = null;

    @property(Label)
    private lbSmallSkillDesc: Label = null;


    @property(Node)
    private ndBigSkill: Node = null;

    @property(Sprite)
    private sprBigSkill: Sprite = null;


    @property(Node)
    private ndGift: Node = null;

    @property(Sprite)
    private sprGift: Sprite = null;

    @property(Label)
    private lbGiftNum: Label = null;



    @property(Node)
    private ndReleaseSkill: Node = null;

    @property(Sprite)
    private sprReleaseSkill: Sprite = null;

    @property(Node)
    private ndKill: Node = null;

    @property(RichText)
    private lbKillDesc: RichText = null;


    private _defaultAvatar: SpriteFrame = null;



    private tw: Tween

    private toPos: Vec3 = v3(0, 0);
    private fromPos: Vec3 = v3(0, 0)

    protected onLoad(): void {
        this.node.on(Node.EventType.TOUCH_END, () => {
            this.kill();
        }, this)
    }

    setData(data: IGiftMessage, index: number) {
        if (!this._defaultAvatar) {
            this._defaultAvatar = this.sprAvatar.spriteFrame;
        }
        this.ndFrame01.active = false;
        this.ndFrame02.active = false;
        this.ndDetail.active = false;
        this.ndSmallSkill.active = false;
        this.ndBigSkill.active = false;
        this.ndGift.active = false;
        this.ndReleaseSkill.active = false;
        this.ndKill.active = false;
        this.sprAvatar.spriteFrame = null;
        this.sprGift.spriteFrame = null;
        this.sprBigSkill.spriteFrame = null;
        this.sprGift.spriteFrame = null;
        this.sprReleaseSkill.spriteFrame = null;
        this.lbDesc.string = '';
        this.lbGiftNum.string = '';
        this.lbKillDesc.string = '';
        this.lbName.string = '';
        this.lbPgs.string = '';
        this.lbSmallSkillDesc.string = '';
        this.lbSmallSkillNum.string = '';

        //基础消息显示
        this.lbName.string = StringUtil.sub(data.name || `匿名用户${data.userId}`, 10, true);
        if (data.avatar) {
            xcore.res.remoteLoadSprite(data.avatar, this.sprAvatar, size(110, 110));
        } else if (this._defaultAvatar) {
            this.sprAvatar.spriteFrame = this._defaultAvatar;
        }

        if (data.type == E_GiftMessageType.GetBigSkill) {
            this.toPos.set(-view.getVisibleSize().width / 2 + 90, -550);
            this.fromPos.set(-view.getVisibleSize().width / 2 - this.node.getComponent(UITransform).width, -550);
        } else {
            this.toPos.set(-view.getVisibleSize().width / 2 + 90, 200 * index);
            this.fromPos.set(-view.getVisibleSize().width / 2 - this.node.getComponent(UITransform).width, 200 * index);
        }
        this.node.setPosition(this.fromPos);
        this.tw = tween(this.node)
            .delay(index * 0.1)
            .to(0.4, { position: this.toPos }, { easing: easing.cubicIn })
            .delay(1.2)
            .call(() => {
                this.kill();
            })
        this.tw.start();

        //log("addMessage", data,);

        switch (data.type) {
            case E_GiftMessageType.Gift:
                this.ndGift.active = true;
                this.ndDetail.active = true;
                this.ndFrame01.active = true;
                let giftConfig = ConfigHelper.getInstance().getGiftConfigByJsonId(data.giftType);

                if (giftConfig.douyinGiftPicture) {
                    //xcore.res.remoteLoadSprite(xcore.gameData.cospath + `image/common/${giftConfig.douyinGiftPicture}.png`, this.sprGift, size(150, 150));
                    xcore.res.bundleLoadSprite('resources', `./res/image/common/${giftConfig.douyinGiftPicture}`, this.sprGift, size(150, 150));
                    this.lbGiftNum.string = `x${data.num}`;
                } else {
                    this.lbGiftNum.string = `${giftConfig.name}x${data.num}`;
                }

                //兑换的法宝进度条消息
                let skillType = C_HeroGiftKeyToSkillType[data.giftType];
                let greatSkill = C_GreatSkill[skillType];
                if (skillType == E_SkillType.AddHp) {
                    this.ndPgs.active = false;
                }

                if (greatSkill) {
                    let [maxNum, skillConfig, minNum] = ConfigHelper.getInstance().getGreateSkillAbleLevelNeedGiftNum(greatSkill, data.totalNum);
                    let xNum = maxNum - data.totalNum;
                    //log("skillConfig:::", skillConfig, minNum, maxNum, data.totalNum);
                    let bigweaponConfig = ConfigHelper.getInstance().getWeaponConfigBySkillJsonId(skillConfig.jsonId);
                    if (xNum <= 0) {
                        this.ndPgs.active = false;
                        //this.lbDesc.string = `<size=24><color=#F4F8FF>召唤</color><color=#F86024>${skillConfig.name}</color></size>`;
                        let name = '技能';
                        if (skillConfig.name) {
                            let names = skillConfig.name.split('级');
                            name = names[names.length - 1]
                        }
                        this.lbDesc.string = `<size=24><color=#F86024>${giftConfig.name}</color><color=#F4F8FF>已升至满级</color></size>`;
                        log("最大等级")
                    } else {
                        this.ndPgs.active = true;
                        this.lbPgs.string = `${data.totalNum}/${maxNum}`;
                        this.sprPgsBar.fillRange = data.totalNum / maxNum;
                        if (data.totalNum < minNum) {
                            this.lbDesc.string = `<size=24><color=#F4F8FF>还差${xNum}个召唤</color><color=#F86024>${bigweaponConfig.name}</color></size>`;
                        } else {
                            this.lbDesc.string = `<size=24><color=#F4F8FF>还差${xNum}个升级</color><color=#F86024>${bigweaponConfig.name}</color></size>`;
                        }

                    }
                    // if (bigweaponConfig) {
                    //     xcore.res.remoteLoadSprite(xcore.gameData.cospath + `image/common/${bigweaponConfig.icon}.png`, this.sprGift, size(150, 150));
                    //     this.lbGiftNum.string = `x1`;
                    // }
                }
                else {
                    let config = ConfigHelper.getInstance().getSkillConfigByType(skillType);
                    this.lbDesc.string = `<size=24><color=#F4F8FF>召唤${config.name}</color></size>`;
                }


                break
            case E_GiftMessageType.GetSmallSkill:
                this.ndSmallSkill.active = true;
                this.ndFrame01.active = true;
                this.ndDetail.active = true;
                this.ndPgs.active = false;
                this.lbDesc.string = `<size=44><color=#5AE040><size>召唤</color></size>`

                let skillConfig = ConfigHelper.getInstance().getSkillConfigByType(data.skillType);
                let weaponConfig = ConfigHelper.getInstance().getWeaponConfigBySkillJsonId(skillConfig.jsonId);

                this.lbSmallSkillNum.string = `${skillConfig.name}x${data.num * (weaponConfig?.skillNum || 1)}`;
                this.lbSmallSkillDesc.string = weaponConfig.describe;

                break
            case E_GiftMessageType.GetBigSkill:
                this.ndBigSkill.active = true;
                this.ndFrame02.active = true;
                this.ndDetail.active = true;
                this.ndPgs.active = false;
                let bigskillConfig = ConfigHelper.getInstance().getSkillConfigByType(data.skillType, data.lev);
                let bigweaponConfig = ConfigHelper.getInstance().getWeaponConfigBySkillJsonId(bigskillConfig.jsonId);
                this.lbDesc.string = `<size=38><color=#FB7906><size>召唤${bigskillConfig.name}</color></size>`;

                // 
                if (bigweaponConfig.icon) {
                    // xcore.res.remoteLoadSprite(xcore.gameData.cospath + `image/common/${bigweaponConfig.icon}.png`, this.sprBigSkill, size(300, 300));
                    xcore.res.bundleLoadSprite('resources', `./res/image/common/${bigweaponConfig.icon}`, this.sprBigSkill, size(300, 300));
                }
                // 
                else {

                }
                break
            case E_GiftMessageType.UseProp:
                this.ndReleaseSkill.active = true;
                this.ndDetail.active = true;
                this.ndPgs.active = false;
                this.ndFrame01.active = true;

                let propConfig = ConfigHelper.getInstance().getSkillConfigByType(data.skillType, data.lev);
                let propweaponConfig = ConfigHelper.getInstance().getWeaponConfigBySkillJsonId(propConfig.jsonId);

                this.lbDesc.string = `<size=28><color=#FFE348>释放${propConfig.name}</color></size>`;

                if (propweaponConfig && propweaponConfig.icon) {
                    //xcore.res.remoteLoadSprite(xcore.gameData.cospath + `image/common/${propweaponConfig.icon}.png`, this.sprReleaseSkill, size(200, 200));
                    xcore.res.bundleLoadSprite('resources', `./res/image/common/${propweaponConfig.icon}`, this.sprReleaseSkill, size(200, 200));
                }

                break
            case E_GiftMessageType.Kill:

                this.ndFrame01.active = true;
                this.ndDetail.active = true;
                this.ndPgs.active = false;
                this.lbDesc.string = `<size=24><color=#5AE040>击杀boss ${data.monsterName}</color></size>`;



                break
            default:
                break

        }
    }

    kill() {
        this.node.removeFromParent();
        GiftMessageMgr.getInstance().killMessage(this);
    }

}


