
import { Game, game, log } from "cc";
 

import { Singleton } from "./Singleton";
import Tool from "./Tool"
import { C_Runtime, T_Dict } from "../../ConstGlobal";
 


export class WxWeb extends Singleton {

    private core: any;

    async init(core) {
        await Tool.loadSDK('//res.wx.qq.com/open/js/jweixin-1.6.0.js')
        console.log('wx sdk init')
        this.core = core;
        //@ts-ignore
        wx.error(function (res) {
            log('wxerr', res)
            // config信息验证失败会执行error函数，如签名过期导致验证失败，具体错误信息可以打开config的debug模式查看，也可以在返回的res参数中查看，对于SPA可以在这里更新签名。
        });

        // index.ts会最初步环境判断，这里二次判断环境是保险起见
        if ((window as any).__wxjs_environment === 'miniprogram') {

            this.core.runtime = C_Runtime.program_wx;
        }
        //@ts-ignore
        wx.miniProgram.getEnv(res => {
            if (res.miniprogram) {

                this.core.runtime = C_Runtime.program_wx;
            }
        })

        // @ts-ignore
        if (window.WeixinJSBridge) {
            // @ts-ignore
            WeixinJSBridge.on('onPageStateChange', function (res) {
                if (res.active === 'true' || res.active === true) {
                    game.emit(Game.EVENT_SHOW)
                } else {
                    game.emit(Game.EVENT_HIDE)
                }
            })
        }


    }


    async wxJsApiConfig(wxConfigInfo: T_Dict, debug: boolean = false, cb?: Function): Promise<any> {
        //@ts-ignore
        wx.ready(function () {
            console.log('wxready')
            cb && cb()
            // config信息验证后会执行ready方法，所有接口调用都必须在config接口获得结果之后，config是一个客户端的异步操作，所以如果需要在页面加载时就调用相关接口，则须把相关接口放在ready函数中调用来确保正确执行。对于用户触发时才调用的接口，则可以直接调用，不需要放在ready函数中。
        });

        // const jsApiList = await this.officialCheckApis(['updateAppMessageShareData', 'updateTimelineShareData'])
        const jsApiList = ['scanQRCode']
        const initInfo = {
            debug, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
            appId: wxConfigInfo.appId, // 必填，公众号的唯一标识
            timestamp: wxConfigInfo.timestamp, // 必填，生成签名的时间戳
            nonceStr: wxConfigInfo.nonceStr, // 必填，生成签名的随机串
            signature: wxConfigInfo.signature, // 必填，签名
            jsApiList, // 必填，需要使用的JS接口列表

        }

        console.log("wxinitInfo", initInfo)
        //@ts-ignore
        wx.config(initInfo)
    }

   

    /**
     * 
     * @param needResult  默认为0，扫描结果由微信处理，1则直接返回扫描结果
     * @param scanType  可以指定扫二维码(qrCode)还是一维码(barCode)，默认二者都有
     * @returns 当needResult 为 1 时，扫码返回的结果
     */
    wxScanQRCode(needResult: number = 0): Promise<any> {
        return new Promise<any>((resolve, reject) => {
            if (this.core.runtime != C_Runtime.program_wx) {
                reject('请在小程序内进行尝试');
            }
            try {
                //@ts-ignore
                wx.scanQRCode({
                    needResult: needResult,
                    scanType: ["qrCode", "barCode"],
                    success: res => {
                        resolve(res.resultStr);
                    }
                });



            } catch (err) {

                reject(err)
            }

        })
    }


    /** 调用小程序跳转 */
    navigateTo(info: { url: string, success?: any }): void {
        if (this.core.runtime == C_Runtime.program_wx) {
            console.log(info)
            //@ts-ignore
            wx.miniProgram.navigateTo(info)
        } else {
            console.log('请在小程序内进行尝试', info.url)
        }
    }

    /** 调用小程序返回 */
    navigateBack(): void {
        if (this.core.runtime == C_Runtime.program_wx) {
            //@ts-ignore
            wx.miniProgram.navigateBack()
        } else {
            console.log('请在小程序内进行尝试')
        }
    }

    /** 切换小程序tab */
    switchTab(info: { url: string }): void {
        if (this.core.runtime == C_Runtime.program_wx) {
            console.log(info)
            //@ts-ignore
            wx.miniProgram.switchTab(info)
        } else {
            console.log('请在小程序内进行尝试')
        }
    }

    /** 切换小程序tab */
    reLaunch(info: { url: string }): void {
        if (this.core.runtime == C_Runtime.program_wx) {
            console.log(info)
            //@ts-ignore
            wx.miniProgram.reLaunch(info)
        } else {
            console.log('请在小程序内进行尝试')
        }
    }

    /** 切换小程序tab */
    redirectTo(info: { url: string }): void {
        if (this.core.runtime == C_Runtime.program_wx) {
            console.log(info)
            //@ts-ignore
            wx.miniProgram.redirectTo(info)
        } else {
            console.log('请在小程序内进行尝试')
        }
    }

    /** 向小程序发送消息 */
    postMessage(info: { data: any }) {
        if (this.core.runtime == C_Runtime.program_wx) {
            //@ts-ignore
            wx.miniProgram.postMessage(info)
        } else {
            console.log('请在小程序内进行尝试')
        }
    }

    /** 关闭公众号页 */
    closeWindow() {
        //@ts-ignore
        if (wx) {
            // @ts-ignore
            wx.closeWindow();
        }
    }
}