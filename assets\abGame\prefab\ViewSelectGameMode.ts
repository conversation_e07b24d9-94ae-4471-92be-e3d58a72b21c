import { _decorator, <PERSON><PERSON>, Component, director, log, Node } from 'cc';
import { ViewBase } from '../../scripts/libs/manager/ViewBase';
import { xcore } from '../../scripts/libs/xcore';
import { C_Bundle, C_Scene, E_EVENT, E_GameMode } from '../../scripts/ConstGlobal';
const { ccclass, property } = _decorator;

@ccclass('ViewSelectGameMode')
export class ViewSelectGameMode extends ViewBase {
    @property(Button)
    private btnModeE: Button = null;

    @property(Button)
    private btnModeM: Button = null;

    @property(Button)
    private btnModeD: Button = null;

    @property(Button)
    private btnBack: Button = null;

    private _isBack: boolean = false;


    protected onLoadCompleted(): void {
        this.btnModeE.node.on('click', () => {
            xcore.gameData.gameMode = E_GameMode.Easy;
            this.closeSelf()
        }, this);
        this.btnModeM.node.on('click', () => {
            xcore.gameData.gameMode = E_GameMode.Middle;
            this.closeSelf()
        }, this);
        this.btnModeD.node.on('click', () => {
            xcore.gameData.gameMode = E_GameMode.Diffecult;
            this.closeSelf()
        }, this);

        this.btnBack.node.on('click', () => {
            this._isBack = true;
            this.closeSelf();
        }, this);
    }

    protected onDestroyCompleted(): void {
        if (director.getScene().name === 'Main' && !this._isBack) {
            log('游戏难度', xcore.gameData.gameMode);
            xcore.gameData.gameType = 0;
            xcore.ui.switchScene(C_Bundle.abGame, C_Scene.Game);
        }
        xcore.event.raiseEvent(E_EVENT.GameConfig)
    }
}


