{"name": "typescript", "author": "Microsoft Corp.", "homepage": "https://www.typescriptlang.org/", "version": "4.7.4", "license": "Apache-2.0", "description": "TypeScript is a language for application scale JavaScript development", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript"], "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "repository": {"type": "git", "url": "https://github.com/Microsoft/TypeScript.git"}, "main": "./lib/typescript.js", "typings": "./lib/typescript.d.ts", "bin": {"tsc": "./bin/tsc", "tsserver": "./bin/tsserver"}, "engines": {"node": ">=4.2.0"}, "packageManager": "npm@6.14.15", "devDependencies": {"@octokit/rest": "latest", "@types/chai": "latest", "@types/convert-source-map": "latest", "@types/glob": "latest", "@types/gulp": "^4.0.9", "@types/gulp-concat": "latest", "@types/gulp-newer": "latest", "@types/gulp-rename": "0.0.33", "@types/gulp-sourcemaps": "0.0.32", "@types/merge2": "latest", "@types/microsoft__typescript-etw": "latest", "@types/minimatch": "latest", "@types/minimist": "latest", "@types/mkdirp": "latest", "@types/mocha": "latest", "@types/ms": "latest", "@types/node": "latest", "@types/node-fetch": "^2.3.4", "@types/q": "latest", "@types/source-map-support": "latest", "@types/xml2js": "^0.4.0", "@typescript-eslint/eslint-plugin": "^4.28.0", "@typescript-eslint/experimental-utils": "^4.28.0", "@typescript-eslint/parser": "^4.28.0", "async": "latest", "azure-devops-node-api": "^11.0.1", "chai": "latest", "chalk": "^4.1.2", "convert-source-map": "latest", "del": "5.1.0", "diff": "^4.0.2", "eslint": "7.12.1", "eslint-formatter-autolinkable-stylish": "1.1.4", "eslint-plugin-import": "2.22.1", "eslint-plugin-jsdoc": "30.7.6", "eslint-plugin-no-null": "1.0.2", "fancy-log": "latest", "fs-extra": "^9.0.0", "glob": "latest", "gulp": "^4.0.0", "gulp-concat": "latest", "gulp-insert": "latest", "gulp-newer": "latest", "gulp-rename": "latest", "gulp-sourcemaps": "latest", "merge2": "latest", "minimist": "latest", "mkdirp": "latest", "mocha": "latest", "mocha-fivemat-progress-reporter": "latest", "ms": "^2.1.3", "node-fetch": "^2.6.1", "prex": "^0.4.3", "q": "latest", "source-map-support": "latest", "typescript": "^4.5.5", "vinyl": "latest", "vinyl-sourcemaps-apply": "latest", "xml2js": "^0.4.19"}, "scripts": {"prepare": "gulp build-eslint-rules", "pretest": "gulp tests", "test": "gulp runtests-parallel --light=false", "test:eslint-rules": "gulp run-eslint-rules-tests", "build": "npm run build:compiler && npm run build:tests", "build:compiler": "gulp local", "build:tests": "gulp tests", "start": "node lib/tsc", "clean": "gulp clean", "gulp": "gulp", "lint": "gulp lint", "lint:ci": "gulp lint --ci", "lint:compiler": "gulp lint-compiler", "lint:scripts": "gulp lint-scripts", "setup-hooks": "node scripts/link-hooks.js"}, "browser": {"fs": false, "os": false, "path": false, "crypto": false, "buffer": false, "@microsoft/typescript-etw": false, "source-map-support": false, "inspector": false}, "volta": {"node": "14.15.5"}}