[1, ["0dxjQbjChPhLLI3e0GK+7Y@6c48a", "14zwa5dfpEMY21PBoE9WCT@6c48a"], 0, [["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3]], [[0, 0, 1, 2, 3, 4, 5]], [[0, "bagualu", "\nbagualu.png\nsize: 1831,1764\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nbagalu_0000_1\n  rotate: false\n  xy: 2, 1519\n  size: 258, 243\n  orig: 1080, 1920\n  offset: 411, 926\n  index: -1\nbagalu_0001_2\n  rotate: true\n  xy: 262, 1504\n  size: 258, 307\n  orig: 1080, 1920\n  offset: 411, 862\n  index: -1\nbagalu_0002_3\n  rotate: true\n  xy: 571, 1413\n  size: 349, 374\n  orig: 1080, 1920\n  offset: 337, 848\n  index: -1\nbagalu_0003_4\n  rotate: true\n  xy: 947, 1347\n  size: 415, 429\n  orig: 1080, 1920\n  offset: 317, 844\n  index: -1\nbagalu_0004_5\n  rotate: true\n  xy: 1378, 1338\n  size: 424, 435\n  orig: 1080, 1920\n  offset: 313, 843\n  index: -1\nbagalu_0005_6\n  rotate: false\n  xy: 442, 973\n  size: 451, 438\n  orig: 1080, 1920\n  offset: 312, 840\n  index: -1\nbagalu_0006_7\n  rotate: true\n  xy: 2, 1068\n  size: 434, 438\n  orig: 1080, 1920\n  offset: 311, 840\n  index: -1\nbagalu_0007_8\n  rotate: false\n  xy: 895, 904\n  size: 453, 441\n  orig: 1080, 1920\n  offset: 311, 837\n  index: -1\nbagalu_0008_9\n  rotate: false\n  xy: 1350, 895\n  size: 447, 441\n  orig: 1080, 1920\n  offset: 311, 837\n  index: -1\nbagalu_0009_10\n  rotate: false\n  xy: 2, 529\n  size: 450, 442\n  orig: 1080, 1920\n  offset: 311, 836\n  index: -1\nbagalu_0010_11\n  rotate: false\n  xy: 914, 450\n  size: 448, 443\n  orig: 1080, 1920\n  offset: 311, 835\n  index: -1\nbagalu_0011_12\n  rotate: false\n  xy: 1364, 450\n  size: 458, 443\n  orig: 1080, 1920\n  offset: 311, 835\n  index: -1\nbagalu_0012_13\n  rotate: false\n  xy: 454, 460\n  size: 458, 442\n  orig: 1080, 1920\n  offset: 311, 836\n  index: -1\nbagalu_0013_14\n  rotate: false\n  xy: 911, 3\n  size: 458, 445\n  orig: 1080, 1920\n  offset: 312, 833\n  index: -1\nbagalu_0015_16\n  rotate: true\n  xy: 2, 74\n  size: 453, 449\n  orig: 1080, 1920\n  offset: 312, 829\n  index: -1\nbagalu_0016_17\n  rotate: false\n  xy: 453, 15\n  size: 456, 443\n  orig: 1080, 1920\n  offset: 312, 835\n  index: -1\nbagalu_0017_18\n  rotate: false\n  xy: 1371, 2\n  size: 458, 446\n  orig: 1080, 1920\n  offset: 312, 832\n  index: -1\n\nbagualu2.png\nsize: 1835,1384\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nbagalu_0014_15\n  rotate: true\n  xy: 922, 2\n  size: 456, 447\n  orig: 1080, 1920\n  offset: 312, 831\n  index: -1\nbagalu_0018_19\n  rotate: true\n  xy: 1371, 2\n  size: 457, 449\n  orig: 1080, 1920\n  offset: 310, 829\n  index: -1\nbagalu_0019_21\n  rotate: false\n  xy: 2, 2\n  size: 458, 454\n  orig: 1080, 1920\n  offset: 311, 824\n  index: -1\nbagalu_0020_22\n  rotate: true\n  xy: 926, 461\n  size: 459, 452\n  orig: 1080, 1920\n  offset: 310, 826\n  index: -1\nbagalu_0021_23\n  rotate: false\n  xy: 462, 3\n  size: 458, 453\n  orig: 1080, 1920\n  offset: 310, 825\n  index: -1\nbagalu_0022_24\n  rotate: true\n  xy: 1380, 462\n  size: 458, 453\n  orig: 1080, 1920\n  offset: 310, 825\n  index: -1\nbagalu_0023_25\n  rotate: false\n  xy: 464, 922\n  size: 460, 460\n  orig: 1080, 1920\n  offset: 310, 818\n  index: -1\nbagalu_0024_26\n  rotate: false\n  xy: 464, 460\n  size: 460, 460\n  orig: 1080, 1920\n  offset: 310, 818\n  index: -1\nbagalu_0025_27\n  rotate: false\n  xy: 1371, 922\n  size: 460, 460\n  orig: 1080, 1920\n  offset: 310, 818\n  index: -1\nbagalu_0026_28\n  rotate: false\n  xy: 2, 458\n  size: 460, 461\n  orig: 1080, 1920\n  offset: 310, 817\n  index: -1\nbagalu_0027_30\n  rotate: false\n  xy: 2, 921\n  size: 460, 461\n  orig: 1080, 1920\n  offset: 310, 817\n  index: -1\n", ["bagualu.png", "bagualu2.png"], {"skeleton": {"hash": "yrTJRrCHYISM12MwYHY68xAYQYE", "spine": "3.8.97", "x": -810, "y": -1440, "width": 1620, "height": 2880, "images": "", "audio": ""}, "bones": [{"name": "root"}, {"name": "bone2", "parent": "root", "scaleX": 1.5, "scaleY": 1.5}], "slots": [{"name": "bagalu_0000_1", "bone": "bone2", "attachment": "bagalu_0027_30"}], "skins": [{"name": "default", "attachments": {"bagalu_0000_1": {"bagalu_0001_2": {"width": 1080, "height": 1920}, "bagalu_0002_3": {"width": 1080, "height": 1920}, "bagalu_0003_4": {"width": 1080, "height": 1920}, "bagalu_0004_5": {"width": 1080, "height": 1920}, "bagalu_0005_6": {"width": 1080, "height": 1920}, "bagalu_0006_7": {"width": 1080, "height": 1920}, "bagalu_0007_8": {"width": 1080, "height": 1920}, "bagalu_0008_9": {"width": 1080, "height": 1920}, "bagalu_0009_10": {"width": 1080, "height": 1920}, "bagalu_0010_11": {"width": 1080, "height": 1920}, "bagalu_0011_12": {"width": 1080, "height": 1920}, "bagalu_0012_13": {"width": 1080, "height": 1920}, "bagalu_0013_14": {"width": 1080, "height": 1920}, "bagalu_0014_15": {"width": 1080, "height": 1920}, "bagalu_0015_16": {"width": 1080, "height": 1920}, "bagalu_0016_17": {"width": 1080, "height": 1920}, "bagalu_0017_18": {"width": 1080, "height": 1920}, "bagalu_0018_19": {"width": 1080, "height": 1920}, "bagalu_0019_21": {"width": 1080, "height": 1920}, "bagalu_0020_22": {"width": 1080, "height": 1920}, "bagalu_0021_23": {"width": 1080, "height": 1920}, "bagalu_0022_24": {"width": 1080, "height": 1920}, "bagalu_0023_25": {"width": 1080, "height": 1920}, "bagalu_0024_26": {"width": 1080, "height": 1920}, "bagalu_0025_27": {"width": 1080, "height": 1920}, "bagalu_0026_28": {"width": 1080, "height": 1920}, "bagalu_0027_30": {"width": 1080, "height": 1920}, "bagalu_0000_1": {"width": 1080, "height": 1920}}}}], "events": {"event_hit": {}}, "animations": {"animation": {"slots": {"bagalu_0000_1": {"attachment": [{"name": "bagalu_0000_1"}, {"time": 0.0333, "name": "bagalu_0001_2"}, {"time": 0.1, "name": "bagalu_0002_3"}, {"time": 0.1333, "name": "bagalu_0003_4"}, {"time": 0.2, "name": "bagalu_0004_5"}, {"time": 0.2333, "name": "bagalu_0005_6"}, {"time": 0.3, "name": "bagalu_0006_7"}, {"time": 0.3333, "name": "bagalu_0007_8"}, {"time": 0.4, "name": "bagalu_0008_9"}, {"time": 0.4333, "name": "bagalu_0009_10"}, {"time": 0.5, "name": "bagalu_0010_11"}, {"time": 0.5333, "name": "bagalu_0011_12"}, {"time": 0.6, "name": "bagalu_0012_13"}, {"time": 0.6333, "name": "bagalu_0013_14"}, {"time": 0.7, "name": "bagalu_0014_15"}, {"time": 0.7333, "name": "bagalu_0015_16"}, {"time": 0.8, "name": "bagalu_0016_17"}, {"time": 0.8333, "name": "bagalu_0017_18"}, {"time": 0.9, "name": "bagalu_0018_19"}, {"time": 0.9333, "name": "bagalu_0019_21"}, {"time": 1, "name": "bagalu_0020_22"}, {"time": 1.0333, "name": "bagalu_0021_23"}, {"time": 1.0667, "name": "bagalu_0022_24"}, {"time": 1.1333, "name": "bagalu_0023_25"}, {"time": 1.1667, "name": "bagalu_0024_26"}, {"time": 1.2333, "name": "bagalu_0025_27"}, {"time": 1.2667, "name": "bagalu_0026_28"}, {"time": 1.3333, "name": "bagalu_0027_30"}]}}, "bones": {"bone2": {"translate": [{"x": 2.06, "y": -57.02}, {"time": 1.3333, "y": 35.6}]}}}}}, [0, 1]]], 0, 0, [0, 0], [-1, -2], [0, 1]]