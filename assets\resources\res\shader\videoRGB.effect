// Effect Syntax Guide: https://github.com/cocos-creator/docs-3d/blob/master/zh/material-system/effect-syntax.md

CCEffect %{
  techniques:
  - name: opaque
    passes:
    - vert: unlit-vs:vert # builtin header
      frag: unlit-fs:frag
      properties: &props
        texture0: { value: white}
        mainColor: { value: [1, 1, 1, 1], editor: { type: color } }
        transFlag: { value: 1.0 }
        offset: {
          value: 0.5, editor: { range: [0.0, 0.6] }
        }
  - name: transparent
    passes:
    - vert: legacy/main-functions/general-vs:vert # builtin header
      frag: unlit-fs:frag
      blendState:
        targets:
        - blend: true
          blendSrc: src_alpha
          blendDst: one_minus_src_alpha
          blendSrcAlpha: src_alpha
          blendDstAlpha: one_minus_src_alpha
      properties: *props
}%

CCProgram unlit-vs %{
  precision highp float;
  #include <legacy/input-standard>
  #include <builtin/uniforms/cc-global>
  
  in vec4 a_color;
  #if HAS_SECOND_UV
    in vec2 a_texCoord1;
  #endif

  out vec3 v_position;
  out vec3 v_normal;
  out vec3 v_tangent;
  out vec3 v_bitangent;
  out vec2 v_uv;
  out vec2 v_uv1;
  out vec4 v_color;

  vec4 vert () {
    StandardVertInput In;
    CCVertInput(In);

    v_uv = a_texCoord;
    #if HAS_SECOND_UV
      v_uv1 = a_texCoord1;
    #endif

    v_color = a_color;
    return cc_matViewProj * In.position;
  }
}%

CCProgram unlit-fs %{
  precision highp float;
  #include <legacy/output>
  
  in vec2 v_uv;
  
  uniform sampler2D texture0; 

  uniform Constant {
    vec4 mainColor;
  };

  uniform ARGS{
    float transFlag;
    float offset;
  };

  vec4 frag () {
    vec4 col = vec4(1, 1, 1, 1);

    vec2 uv = v_uv;
    if (transFlag > 0.0) {
      uv.y = uv.y + (uv.y - 0.5) * uv.x * offset;
    } else if (transFlag < 0.0) {
      uv.y = uv.y + (uv.y - 0.5) * (1.0 - uv.x) * offset;
    }

    col.rgb = texture(texture0, uv).rgb;
    if (uv.y < 0.0 || uv.y > 1.0) {
      col.a = 0.0;
    }
  
    col = mainColor * col;
    return CCFragOutput(col);
  }
}%
