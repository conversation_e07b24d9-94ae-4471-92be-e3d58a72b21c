{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "b84f3911-487a-40ad-a7fb-a8839027c9a4", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "b84f3911-487a-40ad-a7fb-a8839027c9a4@6c48a", "displayName": "tianpeng-move_01", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "b84f3911-487a-40ad-a7fb-a8839027c9a4", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "b84f3911-487a-40ad-a7fb-a8839027c9a4@f9941", "displayName": "tianpeng-move_01", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": -4, "offsetY": -15, "trimX": 60, "trimY": 48, "width": 122, "height": 134, "rawWidth": 250, "rawHeight": 200, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-61, -67, 0, 61, -67, 0, -61, 67, 0, 61, 67, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [60, 152, 182, 152, 60, 18, 182, 18], "nuv": [0.24, 0.09, 0.728, 0.09, 0.24, 0.76, 0.728, 0.76], "minPos": [-61, -67, 0], "maxPos": [61, 67, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "b84f3911-487a-40ad-a7fb-a8839027c9a4@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "b84f3911-487a-40ad-a7fb-a8839027c9a4@6c48a"}}