import { _decorator, Component, Label, log, Node, size, Sprite, SpriteFrame, UITransform } from 'cc';
import { StringUtil } from 'db://assets/scripts/libs/utils/StringUtil';
import { xcore } from 'db://assets/scripts/libs/xcore';
import { Skill } from '../../scripts/Skill';
import { E_SkillType } from 'db://assets/scripts/ConstGlobal';
const { ccclass, property } = _decorator;

@ccclass('UnitPowerRank')
export class UnitPowerRank extends Component {

    @property(Label)
    private lbRank: Label = null;

    @property(Sprite)
    private sprRank: Sprite = null;

    @property(Label)
    private lbScore: Label = null;

    @property(Label)
    private lbPower: Label = null;

    @property(Sprite)
    private sprAvatar: Sprite = null;

    @property(Label)
    private lbName: Label = null;

    @property([SpriteFrame])
    private sfRanks: SpriteFrame[] = [];


    @property([Node])
    private ndSkills: Node[] = []


    @property([Label])
    private lbSkillNums: Label[] = []


    setData(data: any, rank: number, index: number) {
        this.lbRank.string = `${rank + 1}`;
        log(data);
        let score = data.score || 0;
        if (index == 2) {
            score = data.killscore || 0;
        } else if (index == 3) {
            score = data.giftscore || 0;
        }
        this.lbScore.string = StringUtil.numberToTenThousand(score);
        this.lbPower.string = StringUtil.numberToTenThousand(data.atkPower || 0);

        this.lbName.string = StringUtil.sub(data.nickName || '匿名用户', 9, true);

        xcore.res.remoteLoadSprite(data.iconUrl, this.sprAvatar, size(70, 70));
        this.sprRank.spriteFrame = this.sfRanks[rank <= 2 ? rank : 3];
        this.lbRank.node.active = rank > 2;
        this.onRefreshSkills(data.role.skills);
    }

    onRefreshSkills(skills: Map<string, Skill>) {

        let skillTypes = [E_SkillType.GreatFire, E_SkillType.GreatLightning, E_SkillType.GreatRock, E_SkillType.GreatFires, E_SkillType.GreatSkyRock]
        for (let i = 0; i < skillTypes.length; i++) {
            let nd = this.ndSkills[i]
            let skill = skills.get(skillTypes[i]);
            nd.getComponent(Sprite).grayscale = !skill;
            nd.children.forEach(e => {
                if (e.getComponent(Sprite)) {
                    e.getComponent(Sprite).grayscale = !skill;
                }
            })
            nd.getChildByName('lbNum').getComponent(Label).string = skill ? skill.level.toString() : '0';

        }

    }
}


