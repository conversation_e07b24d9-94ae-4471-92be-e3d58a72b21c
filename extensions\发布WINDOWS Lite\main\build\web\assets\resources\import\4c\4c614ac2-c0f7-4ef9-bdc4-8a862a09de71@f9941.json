[1, ["4cYUrCwPdO+b3EioYqCd5x@6c48a"], ["_textureSource"], ["cc.SpriteFrame"], 0, [{"name": "huodou-idle_11", "rect": {"x": 145, "y": 115, "width": 594, "height": 461}, "offset": {"x": -13.5, "y": -57.5}, "originalSize": {"width": 911, "height": 576}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-297, -230.5, 0, 297, -230.5, 0, -297, 230.5, 0, 297, 230.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [145, 461, 739, 461, 145, 0, 739, 0], "nuv": [0.15916575192096596, 0, 0.8111964873765093, 0, 0.15916575192096596, 0.8003472222222222, 0.8111964873765093, 0.8003472222222222], "minPos": {"x": -297, "y": -230.5, "z": 0}, "maxPos": {"x": 297, "y": 230.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]]