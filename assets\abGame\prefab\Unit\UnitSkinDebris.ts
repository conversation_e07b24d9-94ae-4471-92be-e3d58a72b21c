import { _decorator, Component, Label, Node, size, Sprite, SpriteFrame } from 'cc';
import { ConfigHelper } from 'db://assets/scripts/config/ConfigHelper';
import Tool from 'db://assets/scripts/libs/utils/Tool';
import { xcore } from 'db://assets/scripts/libs/xcore';
const { ccclass, property } = _decorator;

@ccclass('UnitSkinDebris')
export class UnitSkinDebris extends Component {

    @property([SpriteFrame])
    private sfFrams: SpriteFrame[] = []

    @property([SpriteFrame])
    private sfMasks: SpriteFrame[] = []

    @property(Sprite)
    private sprFrame: Sprite = null;

    @property(Sprite)
    private sprMask: Sprite = null;

    @property(Sprite)
    private sprDebris: Sprite = null;


    @property(Label)
    private lbName: Label = null;

    @property(Label)
    private lbNum: Label = null;

    setData(lotteryConfig, num: number) {

        if (lotteryConfig) {
            // xcore.res.remoteLoadSprite(lotteryConfig.icon, this.sprDebris, size(60, 60));
            this.lbName.string = lotteryConfig.tips;
            this.lbNum.string = '+' + num.toString();
            let debrisConfig = ConfigHelper.getInstance().getDebriConfigByJsonId(lotteryConfig.skinFragmentId);
            this.sprFrame.spriteFrame = this.sfFrams[debrisConfig.quality - 1]
            this.sprMask.spriteFrame = this.sfMasks[debrisConfig.quality - 1]

            // let path = xcore.gameData.cospath + `image/${debrisConfig.path}/${debrisConfig.icon}.png`;
            let path = `./res/image/${debrisConfig.path}/${debrisConfig.icon}`;
            this.refreshImage(path);
        } else {
            this.node.active = false;
        }


    }

    async refreshImage(url: string) {
        //  let sf = await xcore.res.remoteLoadSprite(url) //await xcore.res.bundleLoadSprite(C_Bundle.abGame, `./res/anim/${animaData.path}/${name}`)
        let sf = await xcore.res.bundleLoadSprite('resources', url) //await xcore.res.bundleLoadSprite(C_Bundle.abGame, `./res/anim/${animaData.path}/${name}`)

        this.sprDebris.spriteFrame = sf;
        Tool.resizeSprite(this.sprDebris, size(80, 80))
    }
}


