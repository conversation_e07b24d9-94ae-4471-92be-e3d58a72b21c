import { _decorator, Component, instantiate, Label, log, Node, Prefab, size, Sprite } from 'cc';
import { ConfigHelper } from 'db://assets/scripts/config/ConfigHelper';
import Tool from 'db://assets/scripts/libs/utils/Tool';
import { xcore } from 'db://assets/scripts/libs/xcore';
import { UnitDimond } from './UnitDimond';
import Net from 'db://assets/scripts/Net';
import { FightMgr } from '../../scripts/FightMgr';
const { ccclass, property } = _decorator;

@ccclass('UnitDimondReward')
export class UnitDimondReward extends Component {
    @property(Node)
    private ndDetail: Node = null;
    @property(Label)
    private lbRank: Label = null;

    @property(Label)
    private lbName: Label = null;

    @property(Sprite)
    private sprAvatar: Sprite = null;

    @property(Node)
    private ndContent: Node = null;

    @property(Prefab)
    private pfbUnitDimond: Prefab = null;

    async setData(datas, index) {
        log(datas, index)
        if (!datas.user) {
            this.ndDetail.active = false;
            return
        }
        let user = datas.user[1];
        this.lbName.string = user.nickName || '匿名用户';
        this.lbRank.string = index + 1;
        xcore.res.remoteLoadSprite(user.iconUrl, this.sprAvatar, size(70, 70))
        for (let i = 0; i < datas.ids.length; i++) {
            let id = datas.ids[i];
            let config = ConfigHelper.getInstance().getLotteryDebrisConfigByJsonId(id, datas.targetId);
            let userData = FightMgr.getInstance().findUser(user.userId);
            let addNum = 0;
            if (userData) {
                let config = ConfigHelper.getInstance().getWingSkinConfigByRankIndex(userData.rank);
                if (config) {
                    addNum = Number(config.number);
                }
                log("getWingSkinConfigByRankIndex", userData, config, addNum)
            }


            //rewardNum normalrewardNum  hardrewardNum
            let nums = null;
            if (xcore.gameData.gameMode == 1) {
                nums = config.normalRewardNum?.split("|");
            } else if (xcore.gameData.gameMode == 2) {
                nums = config.hardRewardNum?.split("|");
            }
            if (!nums) {
                nums = config.rewardNum.split("|");
            }
            log('nums', config, nums);
            let num = 1;
            if (nums[0] && nums[1]) {
                num = Tool.randomNumber(parseInt(nums[0]), parseInt(nums[1]));
            } else if (nums[0]) {
                num = parseInt(nums[0]);
            }
            let dimond = instantiate(this.pfbUnitDimond);
            dimond.parent = this.ndContent;
            num += addNum;
            dimond.getComponent(UnitDimond).setData(config, num);
            await Net.addDimond(user.userId, config.skinFragmentId, num);
        }

        FightMgr.getInstance().updateDimondInfo(user.userId);
    }


}


