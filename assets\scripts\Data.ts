
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "cc";
import { C_Runtime, E_EVENT, T_Dict, listenDataType } from "./ConstGlobal"
import { EventManager } from "./libs/manager/EventManager";
import { Collection } from "./libs/utils/Collection";
//"build-proto:pbjs": "pbjs --dependency protobufjs/minimal.js --target static-module --wrap commonjs --out ./Proto/proto.js ./Proto/*.proto",
//  "build-proto:pbts": "pbts --main --out ./Proto/proto.d.ts ./Proto/*.js"
export default new class Data {

    appId: string
    token: string
    // 本地数据使用
    runtime: string = C_Runtime.other
    //战斗id
    combatId: string = null;
    /**基础数据 */
    baseInfo: T_Dict = null;
    channelId: string

    gameJoinTime: string
    soundData: T_Dict = {};
    giftData: T_Dict = {}

    userName: string
    password: string
    giftInfo: T_Dict = null;
    query: T_Dict
    cospath: 'https://gz-cdn-1258783731.cos.ap-guangzhou.myqcloud.com/2024/livegametower/dev/';

    //游戏模式
    gameType: number = 0
    
    gameTypeJsonId: string = null;
    
    //游戏难道
    gameMode: number = 0;
    //游戏关卡
    gameSelectLev: number = 0;

    gameLev: number = 0;


    oldRankInfo: T_Dict

    scoreRankInfo: T_Dict[] = []
    roundRankInfo: T_Dict[] = []
    liverRankInfo: T_Dict[] = [];
    giftRankInfo: T_Dict[] = [];


    playersOperation: T_Dict = {};

    //
    animationClips: Collection<string, AnimationClip> = null;

    constructor() {

        this.cospath = 'https://gz-cdn-1258783731.cos.ap-guangzhou.myqcloud.com/2024/livegametower/dev/'
        const definesClass: listenDataType[] = [
            { key: 'baseInfo', boardcast: E_EVENT.BaseInfo },
            { key: 'scoreRankInfo', boardcast: E_EVENT.RankInfo },
            { key: 'roundRankInfo', boardcast: E_EVENT.RankInfo },
            { key: 'liverRankInfo', boardcast: E_EVENT.RankInfo },
            { key: 'giftRankInfo', boardcast: E_EVENT.RankInfo },



        ]
        Data.listenDataChange(this, definesClass)
    }

    // 监听数据变化
    static listenDataChange(cls: any, list: Array<listenDataType>) {
        list.forEach(e => {
            Object.defineProperty(cls, e.key, {
                get() {
                    cls._store = cls._store || {}
                    return cls._store[e.key]
                },
                set(data) {
                    cls._store = cls._store || {}
                    if (e.modifyFunc) {
                        const result = e.modifyFunc(data, cls._store[e.key])
                        if (result) {
                            data = result
                        }
                    }
                    cls._store[e.key] = data
                    if (e.boardcast) {
                        EventManager.getInstance().raiseEvent(e.boardcast, data);
                    }
                }
            })
        })
    }



}
