{"importBase": "import", "nativeBase": "native", "name": "a<PERSON><PERSON><PERSON>", "deps": ["resources"], "uuids": ["04ACSAHMRDwYC5KqP7xWFt@6c48a", "04ACSAHMRDwYC5KqP7xWFt", "04ACSAHMRDwYC5KqP7xWFt@f9941", "0c7cd71e0", "0f5e3d09d", "83xD4aMkJDZJ4bM/cwie9v", "1esZgr26FAK6Xi87GBxRZJ@6c48a", "3a2AVyhldD7reC6A7cDP+m@6c48a", "51PKDozJtMb522mqYvkp7B@6c48a", "71qHJEE/1M3bnMDq+NJ/+E@6c48a", "71qHJEE/1M3bnMDq+NJ/+E@f9941", "bbuhM/HXtBOrynzf1cHMme@6c48a", "bbuhM/HXtBOrynzf1cHMme@f9941", "d1Q4EL3gVKR7fj90P/3SeJ@6c48a", "d1Q4EL3gVKR7fj90P/3SeJ@f9941", "d2WfaXAoFE1oMmugDXSpHD@6c48a", "d2WfaXAoFE1oMmugDXSpHD@f9941", "fcZZ9g4xtO7pCMhZuCIvNT@6c48a", "1esZgr26FAK6Xi87GBxRZJ", "1esZgr26FAK6Xi87GBxRZJ@f9941", "29Vj821JxOOJ3vZNQHIdkS@f9941", "37CDiVfFxLwKS5C6el7uJO@f9941", "3a2AVyhldD7reC6A7cDP+m", "3a2AVyhldD7reC6A7cDP+m@f9941", "45go8ltQ1MUqWR4ZSRpiuM", "45go8ltQ1MUqWR4ZSRpiuM@6c48a", "45go8ltQ1MUqWR4ZSRpiuM@f9941", "46amII9lpO2bPbVg4yf9HH@f9941", "51PKDozJtMb522mqYvkp7B", "51PKDozJtMb522mqYvkp7B@f9941", "52fHu7D8hGm5vLaoALoXCl", "57UgcWSMhKGYrPQcn4d3+w", "57UgcWSMhKGYrPQcn4d3+w@6c48a", "57UgcWSMhKGYrPQcn4d3+w@f9941", "71qHJEE/1M3bnMDq+NJ/+E", "a6HGGCGI1LX5mSMegT6BXH@f9941", "bbuhM/HXtBOrynzf1cHMme", "cf9C91cjFEhrYSOFfeFfF7@f9941", "d1Q4EL3gVKR7fj90P/3SeJ", "d2WfaXAoFE1oMmugDXSpHD", "dfY+90orZM6bUTZObb8740@f9941", "fcZZ9g4xtO7pCMhZuCIvNT", "fcZZ9g4xtO7pCMhZuCIvNT@f9941"], "paths": {"0": ["res/img_unpack/main_frame_01/texture", 2, 1], "1": ["res/img_unpack/main_frame_01", 1, 1], "2": ["res/img_unpack/main_frame_01/spriteFrame", 3, 1], "5": ["Main", 0, 1], "6": ["res/img_pack/main_btn_sound/texture", 2, 1], "7": ["res/img_pack/main_btn_news/texture", 2, 1], "8": ["res/img_pack/main_btn_skin/texture", 2, 1], "9": ["res/img_unpack/main_select_bg/texture", 2, 1], "10": ["res/img_unpack/main_select_bg/spriteFrame", 3, 1], "11": ["res/img_pack/main_btn_setting/texture", 2, 1], "12": ["res/img_pack/main_btn_setting/spriteFrame", 3, 1], "13": ["res/img_pack/main_title_01/texture", 2, 1], "14": ["res/img_pack/main_title_01/spriteFrame", 3, 1], "15": ["res/img_pack/main_btn_rank/texture", 2, 1], "16": ["res/img_pack/main_btn_rank/spriteFrame", 3, 1], "17": ["res/img_pack/main_btn_rank02/texture", 2, 1], "18": ["res/img_pack/main_btn_sound", 1, 1], "19": ["res/img_pack/main_btn_sound/spriteFrame", 3, 1], "22": ["res/img_pack/main_btn_news", 1, 1], "23": ["res/img_pack/main_btn_news/spriteFrame", 3, 1], "28": ["res/img_pack/main_btn_skin", 1, 1], "29": ["res/img_pack/main_btn_skin/spriteFrame", 3, 1], "34": ["res/img_unpack/main_select_bg", 1, 1], "36": ["res/img_pack/main_btn_setting", 1, 1], "38": ["res/img_pack/main_title_01", 1, 1], "39": ["res/img_pack/main_btn_rank", 1, 1], "41": ["res/img_pack/main_btn_rank02", 1, 1], "42": ["res/img_pack/main_btn_rank02/spriteFrame", 3, 1]}, "scenes": {"db://assets/abMain/Main.scene": 5}, "packs": {"0c7cd71e0": [26, 33, 10, 5, 12, 14, 16], "0f5e3d09d": [6, 7, 9, 8, 25, 0, 32, 11, 15, 13, 17]}, "versions": {"import": [], "native": []}, "redirect": [20, "0", 21, "0", 27, "0", 30, "0", 35, "0", 37, "0", 40, "0"], "debug": false, "extensionMap": {}, "hasPreloadScript": true, "dependencyRelationships": {}, "types": ["cc.SceneAsset", "cc.ImageAsset", "cc.Texture2D", "cc.SpriteFrame"]}