import { log } from "cc";
import { E_BuffType, E_RoleType, E_SkillType } from "../../scripts/ConstGlobal";
import { Role } from "./Role";

import { FightMgr } from "./FightMgr";
import { ConfigHelper } from "../../scripts/config/ConfigHelper";

export class Buff {
    public type: E_BuffType


    /**
     * 仙族技能附加debuff有释放时间和释放范围
     * 妖族的debuff只有释放间隔，生命周期跟怪物角色周期一致
     */

    /**持续时间 */
    effectTime: number = null;
    /**释放间隔  */
    effectEachTime: number = null;
    /**效果值 影响角色属性的buff才有*/
    effectNum: number = null;
    /**预留 记录可能用到的jsonId */
    effectIds: string[] = [];
    /**伤害值 伤害血量的buff才有*/
    hurtNum: number = null;
    /**作用目标角色 如果设置为null则全局释放（通常妖族技能buff释放为全局释放给仙族）*/
    targets: Role[] = []

    icon: string
    /**1红色 2 绿色 */
    color: number


    private tickTime: number = 0;

    private isDestroy: boolean = false;


    //初始化
    init(jsonId: string) {
        let buffconfig = ConfigHelper.getInstance().getBuffConfigByJsonId(jsonId);
        this.type = buffconfig.type;
        this.icon = buffconfig.icon;
        this.color = buffconfig.colour;
        this.tickTime = 0;
        this.targets = [];
        this.isDestroy = false;
        switch (this.type) {
            //灼烧： 伤害范围 伤害值 伤害间隔
            case E_BuffType.Fire:
                this.hurtNum = buffconfig.hurt;
                this.effectEachTime = buffconfig.hurtInterval;
                this.effectTime = buffconfig.stateDescribe;
                //log("灼烧：", buffconfig)
                break;
            //移动速度buff
            case E_BuffType.MoveSpeed:
                this.effectEachTime = 0.05;
                this.effectTime = buffconfig.stateDescribe;
                this.effectNum = buffconfig.percentage;
                //log("移动速度buff", buffconfig);
                //this.releaseBuff();
                break;
            //眩晕buff效果 单次释放后注销效果
            case E_BuffType.Dizziness:
                this.effectEachTime = 0.05;
                this.effectTime = 0.09;
                this.effectNum = buffconfig.stateDescribe;
                //log("眩晕buff效果", buffconfig)
                break;
            //攻击力buff
            case E_BuffType.AttackNum:
                this.effectNum = buffconfig.percentage;
                //log("攻击力buff", buffconfig)
                break;
            //攻击速度buff
            case E_BuffType.AttackSpeed:
                this.effectEachTime = 0.05;
                this.effectTime = buffconfig.stateDescribe;
                this.effectNum = buffconfig.percentage;
                //log("攻击速度buff", buffconfig)

                break;
            //技能释放速度
            case E_BuffType.SkillSpeed:
                this.effectEachTime = 0.05;
                this.effectTime = buffconfig.stateDescribe;
                this.effectNum = buffconfig.percentage;
                //log("技能释放速度", buffconfig)
                break;
            case E_BuffType.HurtWeak:
                this.effectNum = buffconfig.percentage;
                this.effectIds = buffconfig.weakness ? buffconfig.weakness.split('|') : [];
                break

            default:
                break;
        }
        //log("buff config:", buffconfig, this.effectEachTime, this.effectTime, typeof E_BuffType.Dizziness, typeof this.type);
    }

    //设置buff释放目标 通常是仙族技能释放时设置目标
    setTargets(roles: Role[], isEffectAtonce: boolean = false) {
        this.targets = roles;
        if (isEffectAtonce) {
            this.releaseBuff();
        }
        //this.releaseBuff();
    }

    destroy() {
        if (this.isDestroy) return
        this.isDestroy = true;
        FightMgr.getInstance().killBuff(this);
        this.targets = null
    }

    //buff效果释放  
    releaseBuff() {
        //log("releaseBuff", this.type, this.targets.length);
        if (!this.targets || this.targets.length <= 0) return

        switch (this.type) {
            //灼烧： 伤害范围 伤害值 伤害间隔
            case E_BuffType.Fire:
                this.targets.forEach(target => {
                    if (target && target.isRoleAlive()) {

                        target.setBuffAutoHurt(this.hurtNum, this.effectEachTime, this.effectTime, this.icon, this.color);
                    }
                })
                this.destroy();
                break;
            case E_BuffType.MoveSpeed:
                this.targets.forEach(target => {
                    if (target && target.isRoleAlive()) {
                        target.setBuffSpeed(this.effectNum, this.effectTime, this.icon, this.color);
                    }
                })

                this.destroy();
                break;
            case E_BuffType.Dizziness:
                this.targets.forEach(target => {
                    if (target && target.isRoleAlive()) {
                        target.setDizzTime(this.effectNum, this.icon, this.color);
                    }
                })
                this.destroy();
                break;
            case E_BuffType.AttackNum:
                this.targets.forEach(target => {
                    if (target && target.isRoleAlive()) {
                        target.setBuffAtk(this.effectNum, 3, this.icon, this.color);
                    }
                })
                this.destroy();
                break;
            case E_BuffType.AttackSpeed:
                this.targets.forEach(target => {
                    if (target && target.isRoleAlive()) {
                        target.setBuffAtkSpeed(this.effectNum, this.effectTime, this.icon, this.color);
                    }
                })
                this.destroy();
                break;
            case E_BuffType.SkillSpeed:
                this.targets.forEach(target => {
                    if (target && target.isRoleAlive()) {
                        target.setBuffSkillSpeed(this.effectNum, this.effectTime, this.icon, this.color);
                    }
                })
                this.destroy();
                break;

            case E_BuffType.HurtWeak:
                this.targets.forEach(target => {
                    if (target && target.isRoleAlive()) {
                        target.setWeakState(this.effectIds, this.effectNum);
                    }
                })
                this.destroy();
                break
            default:
                break;
        }


    }

    tick(dt) {
        this.tickTime += dt;

        //buff持续时间倒计时
        if (this.effectTime) {
            if (this.tickTime >= this.effectTime) {
                this.destroy();
                return
            }
        }
        //间隔释放
        if (this.effectEachTime && this.tickTime > this.effectEachTime) {
            if (this.tickTime % this.effectEachTime <= dt) {
                this.releaseBuff();
            }
        }
    }
}