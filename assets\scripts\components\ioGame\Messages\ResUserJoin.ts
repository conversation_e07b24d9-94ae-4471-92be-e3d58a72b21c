import { RoomUserPushPb } from '../../../protos/room'
import BroadcastCtrl from '../BroadcastCtrl'
import { MessageCmdEvent, MessageRoomSubCmd } from '../MessageEvent'
import { _decorator } from 'cc'
const { ccclass } = _decorator

/**
 * 加入房间成功
 */
@ccclass()
export default class ResUserJoin extends BroadcastCtrl {
	////////////////////消息单例////////////////////
	public static get inst() {
		return this.getInst(ResUserJoin)
	}

	constructor() {
		super()
		this.cmd = MessageCmdEvent.roomCmd
		this.subCmd = MessageRoomSubCmd.roomUserPush
		this.addListen()
	}

	receive(code: number, data: any) {
		super.receive(code, RoomUserPushPb.decode(data))
	}
}
