import { _decorator, Component, instantiate, Label, log, Node, Prefab, size, Sprite } from 'cc';
import { UnitSkinDebris } from './UnitSkinDebris';
import { xcore } from 'db://assets/scripts/libs/xcore';
import { ConfigHelper } from 'db://assets/scripts/config/ConfigHelper';
import Net from 'db://assets/scripts/Net';
import { C_View, E_EVENT, E_GiftMessageType } from 'db://assets/scripts/ConstGlobal';
import TimeUtil from 'db://assets/scripts/libs/utils/TimeUtil';
import Tool from 'db://assets/scripts/libs/utils/Tool';
import { FightMgr } from '../../scripts/FightMgr';
const { ccclass, property } = _decorator;

@ccclass('UnitSkinDebrisReward')
export class UnitSkinDebrisReward extends Component {

    @property(Node)
    private ndDetail: Node = null;
    @property(Label)
    private lbRank: Label = null;

    @property(Label)
    private lbName: Label = null;

    @property(Sprite)
    private sprAvatar: Sprite = null;

    @property(Node)
    private ndContent: Node = null;

    @property(Prefab)
    private pfbUnitSkinDebris: Prefab = null;

    async setData(datas, index) {
        log(datas, index)
        if (!datas.user) {
            this.ndDetail.active = false;
            return
        }
        let user = datas.user[1];
        this.lbName.string = user.nickName || '匿名用户';
        this.lbRank.string = index + 1;





        xcore.res.remoteLoadSprite(user.iconUrl, this.sprAvatar, size(70, 70))

        for (let i = 0; i < datas.ids.length; i++) {
            let id = datas.ids[i];

            let config = ConfigHelper.getInstance().getLotteryDebrisConfigByJsonId(id, datas.targetId);
            if (config) {
                //rewardNum normalrewardNum  hardrewardNum

                let userData = FightMgr.getInstance().findUser(user.userId);
                let addNum = 0;
                if (userData) {
                    let config = ConfigHelper.getInstance().getWingSkinConfigByRankIndex( userData.rank);
                    if (config) {
                        addNum = Number(config.number);
                    }
                    log("getWingSkinConfigByRankIndex::", userData, config, addNum)
                }



                let nums = null;
                if (xcore.gameData.gameMode == 1) {
                    nums = config.normalRewardNum?.split("|");
                } else if (xcore.gameData.gameMode == 2) {
                    nums = config.hardRewardNum?.split("|");
                }
                if (!nums) {
                    nums = config.rewardNum.split("|");
                }
                let num = 1;
                if (nums[0] && nums[1]) {
                    num = Tool.randomNumber(parseInt(nums[0]), parseInt(nums[1]));
                } else if (nums[0]) {
                    num = parseInt(nums[0]);
                }
                num += addNum;
                let debris = instantiate(this.pfbUnitSkinDebris);
                debris.parent = this.ndContent;
                debris.getComponent(UnitSkinDebris).setData(config, num);
                let d = user.debris.find(e => e.prop == id);

                log('nums:', config, nums)

                await Net.addDebris(user.userId, num, id);
                if (d) {
                    d.num += num;
                } else {
                    user.debris.push({
                        playerId: user.userId,
                        prop: id,
                        num: num
                    })
                }
                let skinConfig = ConfigHelper.getInstance().getSkinConfigByDebrisId(id);
                if (skinConfig) {
                    this.checkIfExchangeSkin(user, id, skinConfig.skinFragmentNum, skinConfig.jsonId, skinConfig.period, id)

                }
            } else {
                log("lackof config", id)
            }


        }
    }

    checkIfExchangeSkin(user, skinFragment, skinFragmentNum, skinId, time, debrisId) {
        let isHaveSkin = user.skins.find(e => e.prop == skinId)
        let maxLev = ConfigHelper.getInstance().getSkinMaxLevelBySkinId(skinId);
        log("maxLev:", maxLev, isHaveSkin?.level, isHaveSkin)
        if (!isHaveSkin || isHaveSkin?.level < maxLev) {
            let debris = user.debris.find(e => e.prop == skinFragment);
            let isAbleUnlock = debris?.num >= skinFragmentNum;

            // log("checkIfRewardSkin:", isAbleUnlock, skinFragment, debris?.num, skinFragmentNum, time, debrisId, skinId);
            if (isAbleUnlock) {
                Net.exchangeSkin(user.userId, skinId, skinFragmentNum, time, debrisId);
                let id = skinId;
                debris.num -= skinFragmentNum;
                let skin = { prop: skinId, time: TimeUtil.getServerTime() + (time * 1000), level: isHaveSkin ? isHaveSkin.level += 1 : 1 }
                log("up", skin)
                user.skins.push(skin)
                //  xcore.ui.addView(C_View.ViewSkinReward, { skinId: id, user, lev: skin.level }, 1);

                xcore.event.raiseEvent(E_EVENT.GiftMessage, {
                    type: E_GiftMessageType.SkinReward,
                    skinId: id, user, lev: skin.level
                })
            }
        } else {
            log('已有皮肤' + skinId)
        }


    }
}


