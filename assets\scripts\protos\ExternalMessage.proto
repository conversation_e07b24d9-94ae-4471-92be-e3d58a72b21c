syntax = "proto3";
package com.iohao.message;

// 对外服数据协议
message ExternalMessage {
  // 请求命令类型: 0 心跳，1 业务
  int32 cmdCode = 1;
  // 协议开关，用于一些协议级别的开关控制，比如 安全加密校验等。 : 0 不校验
  int32 protocolSwitch = 2;
  // 业务路由（高16为主, 低16为子）
  int32 cmdMerge = 3;
  // 响应码: 0:成功, 其他为有错误
  sint32 responseStatus = 4;
  // 验证信息（错误消息、异常消息），通常情况下 responseStatus == -1001 时， 会有值
  string validMsg = 5;
  // 业务请求数据
  bytes data = 6;
}

// int 包装类
message IntValue {
  // int 值
  sint32 value = 1;
}

// int list 包装类
message IntValueList {
  // intList、intArray
  repeated sint32 values = 1;
}

// long 包装类
message LongValue {
  // long 值
  sint64 value = 1;
}

// long list 包装类
message LongValueList {
  // longList、longArray
  repeated sint64 values = 1;
}

// string 包装类
message StringValue {
  // string 值
  string value = 1;
}

// string list 包装类
message StringValueList {
  // stringList、stringArray
  repeated string values = 1;
}

// bool 包装类
message BoolValue {
  // bool 值
  bool value = 1;
}

// bool list 包装类
message BoolValueList {
  // boolList、boolArray
  repeated bool values = 1;
}

// ------- 以下是废弃的 -------
// ------- 废弃原因-1：属性名称不统一
// ------- 废弃原因-2：命名以 pb 结尾，这会导致在使用非 pb 协议时，会产生歧义

// int 包装类 -- 已经废弃的，由 IntValue 代替
message IntPb {
  // int 值
  sint32 intValue = 1;
}

// int list 包装类 -- 已经废弃的，由 IntValueList 代替
message IntListPb {
  // intList
  repeated sint32 intValues = 1;
}

// long 包装类 -- 已经废弃的，由 LongValue 代替
message LongPb {
  // long 值
  sint64 longValue = 1;
}

// long list 包装类 -- 已经废弃的，由 LongValueList 代替
message LongListPb {
  // longList
  repeated sint64 longValues = 1;
}