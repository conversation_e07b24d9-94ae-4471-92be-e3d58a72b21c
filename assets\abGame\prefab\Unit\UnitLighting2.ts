import { _decorator, Component, Node, Animation, v3, Vec3, log, SpriteAtlas, UITransform } from 'cc';
import { EffectMgr } from '../../scripts/EffectMgr';

import { xcore } from '../../../scripts/libs/xcore';
import Tool from '../../../scripts/libs/utils/Tool';
import { ConfigHelper } from 'db://assets/scripts/config/ConfigHelper';
const { ccclass, property } = _decorator;

@ccclass('UnitLighting2')
export class UnitLighting2 extends Component {

    @property(Animation)
    private anim: Animation

    private pos: Vec3 = new Vec3()
    private offsetPos: Vec3 = new Vec3();


    async setData(animJsonId: string, endPos: Vec3) {
        let animConfig = ConfigHelper.getInstance().getAnimConfigByJsonId(animJsonId);
        this.pos.set(endPos.x, endPos.y);
        this.node.setPosition(this.pos);

        let animaData = {
            'sample': animConfig.sample,
            'duration': animConfig.duration,
            'speed': 1,
            'wrapMode': animConfig.wrapMode,
            'path': animConfig.path,
            'name': animConfig.name,
            'offsetX': animConfig.XAxisOffset || 0,
            'offsetY': animConfig.YAxisOffset || 0
        }
        let atlas = xcore.res.getAtlas('lighting');

        for (let i = 0; i < animaData.sample; i++) {
            let name = `${animaData.name}_${i < 10 ? `0${i}` : i}`;

            let sf = atlas.getSpriteFrame(name)
            if (!sf) {
                //sf = await xcore.res.remoteLoadSprite(xcore.gameData.cospath + `image/${animaData.path}/${name}.png`)// await xcore.res.bundleLoadSprite(C_Bundle.abGame, `./res/anim/${animaData.path}/${name}`)
                sf = await xcore.res.bundleLoadSprite('resources',  `./res/image/${animaData.path}/${name}`)// await xcore.res.bundleLoadSprite(C_Bundle.abGame, `./res/anim/${animaData.path}/${name}`)
                xcore.res.addAtlasSprite('lighting', name, sf);
            }


        }

        //缩放配置
        if (animConfig.XCenterPoint == undefined || animConfig.XCenterPoint == null) {
            animConfig.XCenterPoint = 0.5;
        } else if (animConfig.XCenterPoint == "") {
            animConfig.XCenterPoint = 0;
        }
        if (animConfig.YCenterPoint == undefined || animConfig.YCenterPoint == null) {
            animConfig.YCenterPoint = 0.5;
        } else if (animConfig.YCenterPoint == "") {
            animConfig.YCenterPoint = 0;
        }


        
        await Tool.createAnim(this.anim, animaData, atlas);
        this.offsetPos.set(animaData.offsetX, animaData.offsetY)
        this.anim.node.setPosition(this.offsetPos);
        //配置动画中心位置
        let animUITransform = this.anim.node.getComponent(UITransform);
        animUITransform?.setAnchorPoint(animConfig.XCenterPoint, animConfig.YCenterPoint);
        this.scheduleOnce(() => {
            EffectMgr.getInstance().killLighting(this);
        }, animaData.duration)

    }
}


