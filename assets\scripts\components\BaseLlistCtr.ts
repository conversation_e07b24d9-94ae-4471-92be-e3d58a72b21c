import { _decorator, Component, log, Node, ScrollView, warn } from 'cc';

import List from './List';
import ListItem from './ListItem';

const { ccclass, property } = _decorator;

@ccclass('BaseListCtr')
export default abstract class BaseListCtr extends Component {
    @property(ScrollView)
    public sv: ScrollView = null;

    public list: List = null;

    data: any[];
    public _scrollToIndex: number = null;
    // LIFE-CYCLE CALLBACKS:

    onLoadCompleted() {

        this.list = this.sv.getComponent(List);
        this.sv.node.on('scroll-to-top', this.onScrollToTop, this);
        this.sv.node.on('scroll-to-bottom', this.onScrollToBottom, this);
        this.sv.node.on('scroll-to-left', this.onScrollToLeft, this);
        this.sv.node.on('scroll-to-right', this.onScrollToRight, this);
        this.onLoadCompletedB();
    }

    onLoadCompletedB() { }

    abstract onScrollToTop(): void
    abstract onScrollToBottom(): void
    abstract onScrollToLeft(): void
    abstract onScrollToRight(): void

    //数据初始化
    refreshListData(data: any) {
        //log('refreshListData', data)
        if (!this.list) {
            this.list = this.sv.getComponent(List);
            if (!this.list) {
                warn('检查ScrollView上是否挂载List');
                return
            }

        }
        if (!data) return
        data = data.filter(e => e);
        this.data = data;
        this.list.numItems = this.data.length;
    }

    //垂直列表渲染器
    onListVRender(item: Node, idx: number) {
        item.getComponent(ListItem).setData(this.data[idx], idx);
        this._scrollToIndex = idx;
        //cc.log('_scrollToIndex', this._scrollToIndex)
    }

    //水平列表渲染器
    onListHRender(item: Node, idx: number) {
        // item.getComponent(ListItem).title.getComponent(cc.Label).string = this.data[idx].name + '';

    }

    //网格列表渲染器
    onListGridRender(item: Node, idx: number) {
        //  item.getComponent(ListItem).title.getComponent(cc.Label).string = this.data[idx].name + '';

    }

    //网格列表2渲染器
    onListGrid2Render(item: Node, idx: number) {
        // item.getComponent(ListItem).title.getComponent(cc.Label).string = this.data[idx].name + '';

    }

    //当列表项被选择...
    onListSelected(item: any, selectedId: number, lastSelectedId: number, val: number) {
        // if (!item)
        //     return;
        // let list: List = item.getComponent(ListItem).list;
        // let str: string = '当前操作List为：' + list.node.name + '，当前选择的是：' + selectedId + '，上一次选择的是：' + lastSelectedId;
        // if (list.selectedMode == 2) { //如果是多选模式
        //     str += '，当前值为：' + val;
        // }
        // cc.log(str);
    }
    // update (dt) {}
}


