{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "5de0f041-53bb-47e6-b077-1080e82c796b", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "5de0f041-53bb-47e6-b077-1080e82c796b@6c48a", "displayName": "honghaier-icon", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "5de0f041-53bb-47e6-b077-1080e82c796b", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "5de0f041-53bb-47e6-b077-1080e82c796b@f9941", "displayName": "honghaier-icon", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 3.5, "offsetY": 0, "trimX": 7, "trimY": 0, "width": 89, "height": 96, "rawWidth": 96, "rawHeight": 96, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-44.5, -48, 0, 44.5, -48, 0, -44.5, 48, 0, 44.5, 48, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [7, 96, 96, 96, 7, 0, 96, 0], "nuv": [0.07291666666666667, 0, 1, 0, 0.07291666666666667, 1, 1, 1], "minPos": [-44.5, -48, 0], "maxPos": [44.5, 48, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "5de0f041-53bb-47e6-b077-1080e82c796b@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "5de0f041-53bb-47e6-b077-1080e82c796b@6c48a"}}