import { _decorator, Button, Component, Label, log, Node, size, Sprite, SpriteFrame } from 'cc';
import ListItem from 'db://assets/scripts/components/ListItem';
import { Role } from '../../scripts/Role';
import { ConfigHelper } from 'db://assets/scripts/config/ConfigHelper';
import { xcore } from 'db://assets/scripts/libs/xcore';
import { FightMgr } from '../../scripts/FightMgr';
import Net from 'db://assets/scripts/Net';
import TimeUtil from 'db://assets/scripts/libs/utils/TimeUtil';
import { C_View } from 'db://assets/scripts/ConstGlobal';


const { ccclass, property } = _decorator;

@ccclass('UnitSkinShow')
export class UnitSkinShow extends ListItem {

    @property([SpriteFrame])
    private sfFrames: SpriteFrame[] = [];
    @property([SpriteFrame])
    private sfOns: SpriteFrame[] = [];

    @property(Sprite)
    private sprFrame: Sprite = null;

    @property(Sprite)
    private sprOn: Sprite = null;

    @property(Sprite)
    private sprSkin: Sprite = null;

    @property(Node)
    private ndIconDetail: Node = null;

    @property(Node)
    private ndLeftDay: Node = null;

    @property(Label)
    private lbLeftDay: Label = null;

    @property(Label)
    private lbName: Label = null;

    @property(Node)
    private ndUnlock: Node = null;

    @property(Label)
    private lbUnlockDesc: Label = null;

    @property(Label)
    private lbUnlock: Label = null;

    @property(Node)
    private ndIsSelect: Node = null;

    @property(Button)
    private btnOption: Button = null;

    private _data: any
    private _index: number
    private _fromRole?: Role
    private _leftTime: number = 0;
    private _userId: string = null;
    private _skinId: string = null;

    private _isAbleSelect: boolean = false;




    async setData(data: any, index: number, fromRole?: Role, selectCb?: Function) {
        this._data = data;
        this._index = index;
        this._fromRole = fromRole;
        this.lbName.string = data.name;
        this.lbUnlockDesc.string = data.skin == 1 ? '积分' : '';
        this.sprFrame.grayscale = false;
        this.sprSkin.grayscale = false;
        this.sprOn.grayscale = false;
        this.sprFrame.spriteFrame = this.sfFrames[data.quality - 1];
        this.sprOn.spriteFrame = this.sfOns[data.quality - 1];
        this.ndLeftDay.active = false;
        let user = null;
        this._userId = null;
        this._skinId = data.jsonId;
        let skin = null
        let isHaveSkin = false;
        let isAbleUnlock = false;
        this._leftTime = null;
        this.ndIsSelect.active = false;
        this.btnOption.node.off('click')
        if (fromRole) {
            this.ndIconDetail.active = false;
            user = FightMgr.getInstance().findUser(fromRole.data.userId);
            this._userId = user?.role.data.userId;
            skin = user?.skins.find(e => e.prop == this._skinId);
            this.btnOption.node.on('click', this.onSelectItem.bind(this, selectCb), this);
            // log('user:::', user, skin);

        } else {
            this.ndIconDetail.active = true;
            this.btnOption.node.on('click', () => {
                xcore.ui.addView(C_View.ViewSkinDetail,
                    {
                        skinId: this._skinId,
                        skinName: data.name,

                    }
                )
            }, this);
        }

        //积分解锁
        if (data.skin == 1) {
            this.lbUnlock.string = `${data.openCondition}积分`;
            isHaveSkin = !!skin || !fromRole || fromRole.data.weekScore >= data.openCondition;
            if (user && !skin && isHaveSkin) {
                let timeDiff = 60 * 60 * 24 * 30// Math.floor(TimeUtil.getTimeUntilNextSaturday() / 1000);
                await Net.rewardSkin(this._userId, 4, [{
                    num: 1, prop: this._skinId,
                    time: timeDiff
                }])
                FightMgr.getInstance().updateSkinInfo(this._userId)
                skin = { prop: this._skinId, time: TimeUtil.getServerTime() + (timeDiff * 1000) }
                user.skins.push(skin)
            }
            if (data.openCondition <= 0) {
                this.ndUnlock.active = false;
            }
        }
        //碎片解锁
        else if (data.skin == 2) {


            let dribConfig = ConfigHelper.getInstance().getDebriConfigByJsonId(data.skinFragment);
            this.lbUnlockDesc.string = dribConfig.name;
            this.lbUnlock.string = `${data.skinFragmentNum}碎片`;;

            if (user) {
                let skinConfig = ConfigHelper.getInstance().getSkinLevelConfigBySkinId(this._skinId, skin?.level);
                this.ndUnlock.active = false;
                let maxLev = ConfigHelper.getInstance().getSkinMaxLevelBySkinId(this._skinId);
                log("maxLev::", maxLev, skin?.level, skin, skinConfig, skinConfig?.skinFragmentNum)
                isHaveSkin = !!skin
                let debris = user.debris.find(e => e.prop == data.skinFragment);
                isAbleUnlock = debris?.num >= (skinConfig?.skinFragmentNum || 10000);
                //没有皮肤可解锁皮肤 自动解锁
                if ((!isHaveSkin || skin?.level < maxLev) && isAbleUnlock) {
                    this.autoUnlockSkin(this._userId, this._skinId, skinConfig.skinFragmentNum, data.period, data.skinFragment);

                    debris.num -= skinConfig.skinFragmentNum;
                    skin = { prop: this._skinId, time: TimeUtil.getServerTime() + (data.period * 1000), level: isHaveSkin ? skin.level += 1 : 1 }
                    log("up", skin)
                    user.skins.push(skin)
                    isHaveSkin = true;
                    this.scheduleOnce(() => {
                        this.setData(this._data, this._index, this._fromRole);
                    })

                }
                if (!skin || skin?.level < maxLev) {
                    let nowConfig = ConfigHelper.getInstance().getSkinLevelConfigBySkinId(this._skinId, skin?.level);
                    this.lbName.string = `${data.name}(${debris ? debris.num : 0}/${nowConfig?.skinFragmentNum || data.skinFragmentNum})`;
                } else {
                    this.lbName.string = `${data.name}`;
                }
                if (isHaveSkin) {
                    this.ndLeftDay.active = true;
                    this._leftTime = skin.time - TimeUtil.getServerTime();

                    this.lbLeftDay.string = `Lv.${skin.level}`
                }


            } else {
                isHaveSkin = true;
                this.lbName.string = data.name;
            }




        }
        if (user && skin) {
            this.ndIsSelect.active = skin.useStatus == 1;
            this._isAbleSelect = skin.useStatus != 1;
        } else {
            this._isAbleSelect = false;
        }
        this.sprFrame.grayscale = !isHaveSkin;
        this.sprSkin.grayscale = !isHaveSkin;
        this.sprOn.grayscale = !isHaveSkin;
        this.refreshSkin(this._skinId);

    }

    refreshSkin(_skinId) {
        let skinConfig = ConfigHelper.getInstance().getSkinConfigByJsonId(_skinId);
        // xcore.res.remoteLoadSprite(xcore.gameData.cospath + `image/${skinConfig.path}/${skinConfig.skinRole}.png`, this.sprSkin, size(200, 290))
        xcore.res.bundleLoadSprite('resources', `./res/image/${skinConfig.path}/${skinConfig.skinRole}`, this.sprSkin, size(200, 290));
        log("skinid", this._skinId, skinConfig)
    }

    async autoUnlockSkin(userId: string, skinId: string, num: number, time: number, debrisId: string) {
        await Net.exchangeSkin(userId, skinId, num, time, debrisId);

    }

    async onSelectItem(selectCb?) {
        if (!this._isAbleSelect) { return }
        this.btnOption.enabled = false;
        try {
            await Net.setSelectSkin(this._userId, this._skinId);
            let user = FightMgr.getInstance().findUser(this._userId);
            let selectSkin = user.skins.find(e => e.prop == this._skinId);

            user.skins.forEach(e => {
                e.useStatus = 0;
            })
            selectSkin.useStatus = 1;
            // log("切换皮肤：", user.skins, this._skinId)
            this._isAbleSelect = true;
            this.btnOption.enabled = true;
            let u = FightMgr.getInstance().findUser(this._userId);
            FightMgr.getInstance().doSwitchSkin(u, null, this._skinId);
            if (selectCb) {
                selectCb(this._userId, this._skinId)
            }
        } catch (err) {
            this.btnOption.enabled = true;
        }


    }

    protected update(dt: number): void {
        /*  if (this._leftTime > 0) {
             this._leftTime -= dt;
             let second = Math.ceil(this._leftTime / 1000)
             let d = Math.floor(second / 86400)
             let h = Math.floor((second % 86400) / 3600)
             let m = Math.floor((second % 3600) / 60)
             let s = second % 60;
             if (h > 0 || m > 0 || s > 0) {
                 d += 1;
             }
 
             this.lbLeftDay.string = d.toString();
         } */
    }
}


