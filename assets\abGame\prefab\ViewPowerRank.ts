import { _decorator, Button, Component, Label, log, Node } from 'cc';
import { ViewBase } from '../../scripts/libs/manager/ViewBase';
import ScrollViewProCom from '../../scripts/libs/utils/ScrollViewProCom';
import { FightMgr, IUser } from '../scripts/FightMgr';
import { UnitPowerRank } from './Unit/UnitPowerRank';
const { ccclass, property } = _decorator;

@ccclass('ViewPowerRank')
export class ViewPowerRank extends ViewBase {

    @property(ScrollViewProCom)
    private svRankContent: ScrollViewProCom = null;

    @property(Button)
    private btnScoreRank: Button = null;

    @property(Node)
    private ndScoreRankOn: Node = null;

    @property(Label)
    private lbScoreRank: Label = null;

    @property(Button)
    private btnPowerRank: Button = null;

    @property(Node)
    private ndPowerRankOn: Node = null;

    @property(Button)
    private btnGiftRank: Button = null;

    @property(Node)
    private ndGiftRankOn: Node = null;

    @property(Node)
    private ndEmpty: Node = null;

    @property(Label)
    private lbPowerRank: Label = null;

    private _users: Map<string, IUser>
    private _selectIndex: number = 0


    onLoadCompleted(): void {
        this.btnScoreRank.node.on('click', this.onClickRank.bind(this, 1), this);
        this.btnPowerRank.node.on('click', this.onClickRank.bind(this, 2), this);
        this.btnGiftRank.node.on('click', this.onClickRank.bind(this, 3), this);
        this.onClickRank(1);
    }
    onClickRank(index) {

        if (this._selectIndex == index) return
        this._selectIndex = index;
        this.ndScoreRankOn.active = this._selectIndex == 1;
        this.ndPowerRankOn.active = this._selectIndex == 2;
        this.ndGiftRankOn.active = this._selectIndex == 3;

        this._users = FightMgr.getInstance().getIUsers();
        var arrayObj = Array.from(this._users);
        if (this._selectIndex == 1) {
            arrayObj.sort(function (a, b) { return b[1].score - a[1].score });
        } else if (this._selectIndex == 2) {
            arrayObj.sort(function (a, b) { return b[1].killscore - a[1].killscore });
        } else {
            arrayObj.sort(function (a, b) { return b[1].giftscore - a[1].giftscore });
        }


        this.ndEmpty.active = arrayObj.length <= 0;

        this.svRankContent.setView(arrayObj, (n, data, i) => {

            n.getComponent(UnitPowerRank).setData(data[1], i, this._selectIndex)
        });
        this.svRankContent.scrollToTop(0.1)
    }

}


