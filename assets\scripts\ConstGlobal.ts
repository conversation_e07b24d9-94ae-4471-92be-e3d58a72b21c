import { AnimationClip, Size, SpriteFrame } from "cc"


export type listenDataType = { key: string, boardcast: string, modifyFunc?: Function }
export interface popNewsData {
    title?: string
    desc: string
    btnTxt?: string
    sprUrl?: string
    sprBundle?: string
    spriteframe?: SpriteFrame
    sprSize?: Size | number
    cb?: Function
    layoutY?: number
}
export type T_Dict = Record<string, any>
export interface IUIConfigClass {
    /**分包名称  默认resources */
    bundleName?: string;
    /**标识  名称  脚本名称 */
    viewName: string;
    /**相应bundle下的加载路径 */
    path?: string;
    /**当前弹窗banner情况  */
    bannerId?: string;
    /**时否展示loading过渡 */
    isShowLoading?: boolean;
    /**0->马上展示  大于零->优先级显示 1优先级最高 */
    priority?: number
    /**初始化数据 */
    data?: any
    openCb?: Function
}
export enum E_UILAYER {
    /**背景层级 */
    BG_LAYER = 1,
    /**内容层级 */
    CONTENT_LAYER = 10,

    UI_LAYER = 15,

    /**viwe层 弹窗 */
    VIEW_LAYER = 20,

    /**loading层级 */
    LOADING_LAYER = 25,

    /**tips层级 */
    TIPS_LAYER = 30,
    /**引导层级 */
    GUIDE_LAYER = 35,
    /**toast层级 */
    TOAST_LAVER = 40,
}
//
export enum E_EVENT {
    GameShow = 'GameShow',
    GameHide = 'GameHide',
    StroageData = 'StroageData',
    UiClose = 'UiClose',
    ShowGuide = 'ShowGuide',
    HideGuide = 'HideGuide',
    BaseInfo = 'BaseInfo',
    RankInfo = 'RankInfo',
    GameMode = 'GameMode',
    GameSelectLev = 'GameSelectLev',
    GameLogin = 'GameLogin',
    GameBack = 'GameBack',
    GameStart = 'GameStart',
    GameOver = 'GameOver',
    GameReplay = 'GameReplay',
    GameOut = 'GameOut',
    GameResume = 'GameResume',

    CrateMoreMonster = 'CrateMoreMonster',


    Gift = 'Gift',
    /**技能特效 */
    SkillEffect = 'SkillEffect',
    /**仙族普攻特效 */
    HeroAtkEffect = 'HeroAtkEffect',
    /**妖族普攻特效 */
    MonsterAtkEffect = 'MonsterAtkEffect',
    /**受击特效 */
    HurtEffect = 'HurtEffect',
    /**死亡特效 */
    MonsterDead = 'MonsterDead',

    NewsEffect = 'NewsEffect',
    SkillTips = 'SkillTips',

    GameConfig = 'GameConfig',

    HurtTips = 'HurtTips',

    Round = 'Round',
    NextGameLevel = 'NextGameLevel',

    GameScore = 'GameScore',

    GiftMessage = 'GiftMessage',
    //测试
    GraphicsEffectRange = 'GraphicsEffectRange',
    GraphicsEffectRange2 = 'GraphicsEffectRange2',

    ShakeCam = 'ShakeCam',

    RefreshList = 'RefreshList'

}
export enum E_GiftMessageType {
    /**刷礼物信息 */
    Gift,
    /**获取技能 */
    GetSmallSkill,
    /**获取大技能（法宝）有进度条 */
    GetBigSkill,
    /**使用法宝 */
    UseProp,
    /**击杀Boss提示 */
    Kill,
    /**排名靠前大哥进场 */
    RankBoss,
    /**技能升级 */
    SkillUp,
    /**用户宝珠信息 */
    UserInfo,
    SkinReward,
    TaskReward,
}
export enum E_GameState {
    None,
    /**停止 */
    Stop,
    /**暂停 */
    Pause,
    /**继续 */
    Resume,
    /**游戏中 */
    Gameing
}

/* 点赞
666
礼物1
礼物2
礼物3
礼物4
礼物5
礼物6 */
export const C_GiftKey = {
    /**加入仙族 */
    JoinHero: '1',
    /**加入妖族 */
    JoinMonster: '2',
    /**点赞 */
    Like: '300001',
    /**666 */
    SixSix: '300002',

    /**仙女棒 */
    Gift01: '300003',
    /**药丸 */
    Gift02: '300004',
    /**魔法镜 */
    Gift03: '300005',
    /**甜甜圈 */
    Gift04: '300006',
    /**恶魔炸弹 */
    Gift05: '300007',
    /**神秘空投 */
    Gift06: '300008',


    /**换肤1 */
    Skin1: '170001',
    /**换肤2 */
    Skin2: '170002',
    /**换肤3 */
    Skin3: '170003',
    /**换肤4 */
    Skin4: '170004',
    /**换肤5 */
    Skin5: '170005',
    /**换肤6 */
    Skin6: '170006',
    /**换肤7 */
    Skin7: '170007',


    Skin8: '171001',
    Skin9: '171002',
    Skin10: '171003',
    Skin11: '171004',
    Skin12: '171005',
    Skin13: '171006',
    Skin14: '171007',
    Skin15: '171008',
    Skin16: '171009',
    Skin17: '171010',
    Skin18: '171011',
    Skin19: '171012',
    Skin20: '171013',
    Skin21: '171014',
    Skin22: '171015',
    Skin23: '171016',
    Skin24: '171017',
    Skin25: '171018',

    MoveLeft: 'ml',
    MoveRight: 'mr',
    Baozhu: 'baozhu',
    ExchangeBossFight: 'exchangebossfight',
    ExchangeWing: 'exchangewing'
}
export enum E_SkinType {
    Skin1 = '170001',
    Skin2 = '170002',
    Skin3 = '170003',
    Skin4 = '170004',
    Skin5 = '170005',
    Skin6 = '170006',
    Skin7 = '170007',

    Skin8 = '171001',
    Skin9 = '171002',
    Skin10 = '171003',
    Skin11 = '171004',
    Skin12 = '171005',
    Skin13 = '171006',
    Skin14 = '171007',
    Skin15 = '171008',
    Skin16 = '171009',
    Skin17 = '171010',
    Skin18 = '171011',
    Skin19 = '171012',
    Skin20 = '171013',
    Skin21 = '171014',
    Skin22 = '171015',
    Skin23 = '171016',
    Skin24 = '171017',
    Skin25 = '171018',
}
export enum E_SkinUnlockType {
    Score = 1,
    Debris = 2
}
export enum E_SkinLayer {
    /**普通 */
    Normal = 1,
    /**稀有 */
    Good = 2,
    /**史诗级 */
    Legend = 3,
    /**传说级 */
    Epic = 4
}
//技能类型
export enum E_SkillType {
    /**飞剑 */
    Attack = '1',
    /**巨型飞剑 */
    GreatAttack = '2',
    /**天火 */
    Fire = '3',
    /**火灵珠 */
    GreatFire = '203',
    /**闪电 */
    Lightning = '4',
    /**五雷钉 */
    GreatLightning = '204',
    /**飞沙走石 */
    Rock = '5',
    /**混元伞 */
    GreatRock = '205',
    /**三味真火 */
    Fires = '7',
    /**八卦炉 */
    GreatFires = '207',
    /**护天阵 */
    Invincible = '6',
    AddHp = '9',
    /**天外陨石 */
    SkyRock = '8',
    /**女娲石 */
    GreatSkyRock = '208',


    /** 妖族 增加附近移动速度 */
    MoveSpeed = '101',
    /** 妖族 降低对方攻击速度 （全局）*/
    SkillSpeed = '102',
    /** 妖族 牛魔王 眩晕 （全局）*/
    Dizziness = '103',

    /**狂暴生命值下降到 指定值 后获得攻击加速 */
    Crazy = '104',
    /**弱点 受到指定类型技能增加伤害 */
    Weak = '105',
    /**分身 血量低于 指定值 分身指定个指定怪物 */
    CreateMore = '106',
    /**剑气横斩 */
    LineAttack = '107',

    MoveSpeed2 = '401',
    SkillSpeed2 = '402',
    Dizziness2 = '403',
    Crazy2 = '404',
    Weak2 = '405',
    CreateMore2 = '406'

}
//礼物技能
export const C_GiftSkill = [
    /**飞剑 */
    '1',
    /**巨型飞剑 */
    '2',
    /**天火 */
    '3',
    /**火灵珠 */
    '203',
    /**闪电 */
    '4',
    /**五雷钉 */
    '204',
    /**飞沙走石 */
    '5',
    /**混元伞 */
    '205',
    /**三味真火 */
    '7',
    /**八卦炉 */
    '207',
    /**护天阵 */
    '6',
    '9',
    /**天外陨石 */
    '8',
    /**女娲石 */
    '208',


    /** 妖族 增加附近移动速度 */
    '101',
    /** 妖族 降低对方攻击速度 （全局）*/
    '102',
    /** 妖族 牛魔王 眩晕 （全局）*/
    '103',

    /**狂暴生命值下降到 指定值 后获得攻击加速 */
    '104',
    /**弱点 受到指定类型技能增加伤害 */
    '105',
    /**分身 血量低于 指定值 分身指定个指定怪物 */
    '106',
    /**剑气横斩 */
    '107'
]
//礼物升级
export const C_GreatSkill = {
    [`${E_SkillType.Attack}`]: E_SkillType.GreatAttack,
    [`${E_SkillType.Fire}`]: E_SkillType.GreatFire,
    [`${E_SkillType.Lightning}`]: E_SkillType.GreatLightning,
    [`${E_SkillType.Rock}`]: E_SkillType.GreatRock,
    [`${E_SkillType.Fires}`]: E_SkillType.GreatFires,
    [`${E_SkillType.SkyRock}`]: E_SkillType.GreatSkyRock,

}
//礼物升级
export const C_HeroGiftKeyToSkillType = {
    [`${C_GiftKey.Like}`]: E_SkillType.Attack,
    [`${C_GiftKey.SixSix}`]: E_SkillType.Attack,
    [`${C_GiftKey.Gift01}`]: E_SkillType.Fire,
    [`${C_GiftKey.Gift02}`]: E_SkillType.Lightning,
    [`${C_GiftKey.Gift03}`]: E_SkillType.Rock,
    [`${C_GiftKey.Gift04}`]: E_SkillType.AddHp,
    [`${C_GiftKey.Gift05}`]: E_SkillType.Fires,
    [`${C_GiftKey.Gift06}`]: E_SkillType.SkyRock,

}
//礼物升级
export const C_MonsterGiftKeyToSkillType = {
    [`${C_GiftKey.Like}`]: E_SkillType.Attack,
    [`${C_GiftKey.SixSix}`]: E_SkillType.Attack,
    [`${C_GiftKey.Gift01}`]: E_SkillType.Fire,
    [`${C_GiftKey.Gift02}`]: E_SkillType.Lightning,
    [`${C_GiftKey.Gift03}`]: E_SkillType.Rock,
    [`${C_GiftKey.Gift04}`]: E_SkillType.AddHp,
    [`${C_GiftKey.Gift05}`]: E_SkillType.Fires,
    [`${C_GiftKey.Gift06}`]: E_SkillType.SkyRock,

}

export const C_TowerPointGiftKey = C_GiftKey.Gift01;

export enum E_TowerPointType {
    Point1 = '1',
    Point2 = '2',
    Point3 = '3',
    Point4 = '4',
}
export enum E_MonsterType {
    /**蝙蝠精 */
    Bat = '1',
    /**蝙蝠王 */
    GreatBat = '1001',
    /**小钻风 */
    Zuanfeng = '2',
    /**总钻风 */
    GreateZuanfeng = '1002',
    /**伶俐鬼 */
    Child = '3',
    /**红孩儿 */
    GreatChild = '1003',
    /**野猪精 */
    Pig = '4',
    /**黑熊精 */
    GreatPig = '1004',
    /**大鹏 */
    Eagle = '5',
    /**金翅大鹏 */
    GreatEagle = '1005',
    /**巨力猿 */
    Monkey = '6',
    /**孙悟空 */
    GreatMonkey = '1006',
    /**犀牛精 */
    Cattle = '7',
    /**牛魔王 */
    GreatCattle = '1007',
    /**白泽 */
    Baize = '2021',
    /**祸斗 */
    Huodou = '2022',
    /**九尾 */
    Jiuwei = '2023',
    /**穷奇 */
    Qiongqi = '2024',
    /**狻猊 */
    Suanni = '2025',
    /**饕餮 */
    Taotie = '2026',



}
export enum E_MonsterTag {
    normal = 1,
    smallboss = 2,
    bigboss = 3,
}
export const C_GreatMonster = {
    [`${E_MonsterType.Bat}`]: E_MonsterType.GreatBat,
    [`${E_MonsterType.Zuanfeng}`]: E_MonsterType.GreateZuanfeng,
    [`${E_MonsterType.Child}`]: E_MonsterType.GreatChild,
    [`${E_MonsterType.Pig}`]: E_MonsterType.GreatPig,
    [`${E_MonsterType.Eagle}`]: E_MonsterType.GreatEagle,
    [`${E_MonsterType.Monkey}`]: E_MonsterType.GreatMonkey,
    [`${E_MonsterType.Cattle}`]: E_MonsterType.GreatCattle,

}
/* 状态类型
"状态类型
1.灼烧
2.增减角色移动速度（百分比）
3.晕眩
4.增减加攻击力（百分比）
5.增减攻击间隔（百分比）
6.增减技能试放间隔（百分比）" */
export enum E_BuffType {
    //灼烧
    Fire = 1,
    //增减角色移动速度（百分比）
    MoveSpeed = 2,
    //晕眩
    Dizziness = 3,
    //增减加攻击力（百分比）
    AttackNum = 4,
    //增减攻击间隔（百分比）
    AttackSpeed = 5,
    //增减技能试放间隔（百分比）
    SkillSpeed = 6,
    /**弱点 */
    HurtWeak = 7
}

export enum E_RoleState {
    /**无 */
    None,
    /**默认 */
    Idle,
    /**移动 */
    Move,
    /**攻击 */
    Attack,
    /**skill */
    Skill,
    /**受击 */
    Dizziness,
    Hurt,
    /**常驻角色等待复活状态 */
    WaitRelive,
    /**死亡 */
    Dead,
}

export enum E_RoleType {
    /**修仙方 防守方 */
    Hero = 'Hero',
    /**妖怪方 进攻方*/
    Monster = 'Monster',
    /**塔方 */
    Tower = 'Tower',
    /**出怪点 */
    MonsterPoint = 'MonsterPoint',

    /**塔驻点 */
    TowerPoint = 'TowerPoint'
}


/**
 * 
 */
export enum E_AtkType {
    /**技能攻击 */
    Skill,
    /** 单体攻击 */
    Single,
    /** 多单位攻击 */
    Multiple,
    /** 范围攻击 */
    Aoe,
    /**弹跳攻击 */
    Jump
}
export enum E_AtkStyle {
    /**远程 */
    Near = 1,
    /**近战 */
    LongDistance = 2,
    /**Multiple */
    Multiple = 5
}
export enum E_JumpType {
    h5 = `h5`,
    MiniProgram = 'MiniProgram',
}
export const C_Runtime = {
    /**微信内置浏览器 */
    browser_wx: 'browser_wx',
    /**微信小程序嵌入页 */
    program_wx: 'program_wx',
    /**其他浏览器 */
    other: 'other',

};
/**任务类型 */
export enum E_TaskType {
    /**登录任务 */
    login = 1,
    /**邀请任务 */
    invite = 2,
    /**分享任务 */
    share = 3,
    /**游戏内任务 */
    gameAim = 4,
    /**内部跳转任务 */
    jumpInside = 5,
    /**外部跳转任务 */
    jumpOutside = 6,
}

//分包名称
export const C_Bundle = {
    abMain: 'abMain',
    abGame: 'abGame'
}
export enum E_GameMode {
    Easy,
    Middle,
    Diffecult
}
export enum E_GameType {
    Day,
    Week,
    Month,
}
//场景
export const C_Scene = {
    Ready: 'Ready',
    Main: 'Main',
    Game: 'Game'
}

export const C_View = {
    ViewLogin: 'ViewLogin',
    ViewMatch: 'ViewMatch',
    ViewGameOver: 'ViewGameOver',
    ViewTestGift: 'ViewTestGift',
    ViewSelectGameLevel: 'ViewSelectGameLevel',
    ViewSelectGameMode: 'ViewSelectGameMode',
    ViewGameSetting: 'ViewGameSetting',
    ViewGameSound: 'ViewGameSound',
    ViewFightRank: 'ViewFightRank',
    ViewRank: 'ViewRank',
    ViewSkinSelect: 'ViewSkinSelect',
    ViewRoundToast: 'ViewRoundToast',
    ViewSetting: 'ViewSetting',
    ViewCommonVideo: 'ViewCommonVideo',
    ViewBossMsg: 'ViewBossMsg',
    ViewCommonTips: 'ViewCommonTips',
    ViewSkinShow: 'ViewSkinShow',
    ViewOpenBox: 'ViewOpenBox',
    ViewSkinDebrisReward: 'ViewSkinDebrisReward',
    ViewSkinReward: 'ViewSkinReward',
    ViewSkinDetail: 'ViewSkinDetail',
    ViewPowerRank: 'ViewPowerRank',
    ViewTowerLevUp: 'ViewTowerLevUp',
    ViewCrossRewardDesc: 'ViewCrossRewardDesc',
    ViewUserInfo: 'ViewUserInfo',
    ViewDimondReward: 'ViewDimondReward',
    ViewWingShow: 'ViewWingShow',
    ViewTaskRewardDesc: 'ViewTaskRewardDesc',
    ViewSelectGameType: 'ViewSelectGameType',
    ViewExchange: 'ViewExchange',
    ViewExchangeShow: 'ViewExchangeShow'

}
export interface IAnimaData {
    'path': string,
    'sample': number,
    'duration': number,
    'speed': number,
    'wrapMode': AnimationClip.WrapMode,
    'name': string
}

export enum E_Channel {
    TIKTOK = 'TIKTOK',
    UHO = 'UHO',
    GAME560 = 'GAME560'
}