[1, ["52fHu7D8hGm5vLaoALoXCl", "ff3reVIcZLdo1ocsK9Lga3@f9941", "9bBYHzhH5F25cHFMhUL3oq@f9941", "b5YLPadOZMx60zj6fdCuKY@f9941"], ["node", "_spriteFrame", "_font", "root", "lbName", "lbUserName", "spr<PERSON><PERSON><PERSON>", "role", "sprBg", "anim", "btnClose", "sprFrame", "ndRoot", "data"], [["cc.Node", ["_name", "_layer", "_components", "_prefab", "_children", "_parent", "_lpos"], 1, 9, 4, 2, 1, 5], ["cc.Sprite", ["_sizeMode", "_type", "node", "__prefab", "_color", "_spriteFrame"], 1, 1, 4, 5, 6], ["cc.Node", ["_name", "_layer", "_parent", "_components", "_prefab", "_lpos", "_lscale"], 1, 1, 12, 4, 5, 5], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_lineHeight", "_isSystemFontUsed", "_horizontalAlign", "_enableOutline", "_outlineWidth", "node", "__prefab", "_color", "_outlineColor", "_font"], -5, 1, 4, 5, 5, 6], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.<PERSON><PERSON>", ["_zoomScale", "node", "__prefab", "_normalColor", "_target"], 2, 1, 4, 5, 1], ["sp.Skeleton", ["_preCacheMode", "node", "__prefab"], 2, 1, 4], ["cc.Animation", ["node", "__prefab"], 3, 1, 4], ["cc.Mask", ["_type", "node", "__prefab"], 2, 1, 4], ["cc.Graphics", ["node", "__prefab", "_fillColor"], 3, 1, 4, 5], ["cc.Layout", ["_resizeMode", "_layoutType", "node", "__prefab"], 1, 1, 4], ["c2b2be7eWlHjbxO/JHn4WXA", ["node", "__prefab", "ndRoot", "sprFrame", "btnClose", "anim", "sprBg", "role", "spr<PERSON><PERSON><PERSON>", "lbUserName", "lbName"], 3, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1]], [[6, 0, 2], [7, 0, 1, 2, 3, 4, 5, 5], [3, 0, 1, 2, 1], [2, 0, 1, 2, 3, 4, 5, 3], [0, 0, 1, 5, 2, 3, 6, 3], [2, 0, 1, 2, 3, 4, 3], [1, 2, 3, 1], [0, 0, 1, 5, 4, 2, 3, 6, 3], [3, 0, 1, 1], [1, 2, 3, 5, 1], [5, 0, 2], [0, 0, 1, 4, 2, 3, 3], [0, 0, 1, 5, 4, 2, 3, 3], [2, 0, 1, 2, 3, 4, 5, 6, 3], [3, 0, 1, 3, 1], [1, 0, 2, 3, 4, 2], [1, 1, 0, 2, 3, 3], [8, 0, 1, 2, 3, 4, 2], [9, 0, 1, 2, 2], [10, 0, 1, 1], [11, 0, 1, 2, 2], [12, 0, 1, 2, 1], [4, 0, 1, 2, 3, 4, 8, 9, 6], [4, 0, 5, 1, 2, 3, 4, 6, 7, 8, 9, 10, 11, 12, 9], [4, 0, 5, 1, 2, 3, 4, 6, 7, 8, 9, 10, 11, 9], [13, 0, 1, 2, 3, 3], [14, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 1]], [[10, "ViewSkinReward"], [11, "ViewSkinReward", 33554432, [-13], [[8, -2, [0, "69rEGqkkhLWZCawx9Tk2IF"]], [26, -12, [0, "13FDSzwgJDR6bwWkUP2N0o"], -11, -10, -9, -8, -7, -6, -5, -4, -3]], [1, "e97VuNJgVAlpLkBBxsiecF", null, null, null, -1, 0]], [12, "ndCore", 33554432, 1, [-15, -16, -17, -18, -19, -20, -21, -22, -23, -24], [[8, -14, [0, "bccShkt5xCEKbTTeeB4rZf"]]], [1, "8bwKyZCNtHDpUA4ty/bZZk", null, null, null, 1, 0]], [5, "btnClose", 33554432, 2, [[[2, -25, [0, "1bYAsWKmVEx7HhI5X66E1T"], [5, 1280, 2000]], [16, 1, 0, -26, [0, "4dBr0JHqhBOYpPrxA6bTf1"]], -27], 4, 4, 1], [1, "60NCndVWJIRIadSdDKVMDU", null, null, null, 1, 0]], [7, "Node-002", 33554432, 2, [-31], [[2, -28, [0, "16j00AASZNQ5w8kg5ecdtE"], [5, 70, 70]], [20, 1, -29, [0, "5eUogs3QZBGJaev2OXZtcl"]], [21, -30, [0, "2df+0nhchPzYvubZxbNgil"], [4, 16777215]]], [1, "95kMeq9Q5G7qcSamXqdGY1", null, null, null, 1, 0], [1, 0, 450.452, 0]], [7, "Node", 33554432, 2, [-34, -35], [[2, -32, [0, "ffB/eW4uVPQYIdy/F+aNZ0"], [5, 249.5999755859375, 100]], [25, 1, 1, -33, [0, "ddNMZSpmRFmr0rQYJmr7+w"]]], [1, "deGNYRDwNCrLIJL46aHEyP", null, null, null, 1, 0], [1, 0, 314.0930000000001, 0]], [3, "role", 33554432, 2, [[[2, -36, [0, "88stLjuX1Lt7f38U4Akd4j"], [5, 600, 600]], [6, -37, [0, "ec7EAvR3xE0pH5hYCU6g78"]], -38], 4, 4, 1], [1, "f9BktTT05ACr+Cw0pNzCc8", null, null, null, 1, 0], [1, 0, -214.99, 0]], [5, "sprFrame", 33554432, 2, [[[2, -39, [0, "a1WANPeThHsZS2U5InxtZs"], [5, 900, 1160]], -40], 4, 1], [1, "740pnqhiNNYrBIkVASSeOK", null, null, null, 1, 0]], [3, "sprBg", 33554432, 2, [[[2, -41, [0, "aa++MDN/dJc4n6MK4BA4ZS"], [5, 457, 276]], -42], 4, 1], [1, "8cH04jG/FLzbITtfNs6kHg", null, null, null, 1, 0], [1, 0, -323.197, 0]], [3, "anim", 33554432, 2, [[[14, -43, [0, "79h1qEmJJGwIyjYbMQI6Tx"], [0, 0.5, 0.5050208409627278]], -44], 4, 1], [1, "eds/MksaBNjZAmP7060I9l", null, null, null, 1, 0], [1, 0, -205.68600000000004, 0]], [4, "sprAFrame", 33554432, 2, [[2, -45, [0, "48xvjoKr9NW4dAOnsVueTA"], [5, 70, 70]], [9, -46, [0, "7eUa8tMllFvb5S0YbpP47x"], 0]], [1, "7f5KbzeBpHOo5siIpXTXkn", null, null, null, 1, 0], [1, 0, 449.587, 0]], [5, "spr<PERSON><PERSON><PERSON>", 33554432, 4, [[[2, -47, [0, "b12b5zr/NEJ79AV6iR0BSC"], [5, 99, 100]], -48], 4, 1], [1, "42t6VmcXVA27MHZUN4DWeB", null, null, null, 1, 0]], [4, "sprMask", 33554432, 2, [[2, -49, [0, "0dIqMknPhLR57PI1mpq7Fp"], [5, 70, 70]], [9, -50, [0, "0af951J8pFGa0FjTcuy7pp"], 1]], [1, "14uQfuU55OI4gaUa+S7mFR", null, null, null, 1, 0], [1, 0, 450.452, 0]], [13, "lbName", 33554432, 2, [[[2, -51, [0, "deUnXRwttFspZPFa7I5MoU"], [5, 240, 100.8]], -52], 4, 1], [1, "a8xFLXB99LDLB+aT/3vDkJ", null, null, null, 1, 0], [1, 0, 391.6980000000001, 0], [1, 0.5, 0.5, 1]], [4, "lbRoleName-001", 33554432, 5, [[2, -53, [0, "b8wqWzTD1MIpugogoxMNHR"], [5, 168, 108.8]], [23, "解锁", 0, 80, 80, 80, false, true, 4, -54, [0, "a7rVaja79FVL/0kGjT4YuJ"], [4, 4291819775], [4, 4280098273], 2]], [1, "ebtniRYNVHQI0+tBOjvyKG", null, null, null, 1, 0], [1, -40.79998779296875, 5.676999999999907, 0]], [3, "lbRoleName", 33554432, 5, [[[2, -55, [0, "78/7UUP3tG/aqvpbPnUhIS"], [5, 81.5999755859375, 108.8]], -56], 4, 1], [1, "72QuFccCxGdYRIUMrnbtDc", null, null, null, 1, 0], [1, 84, 5.676999999999907, 0]], [15, 0, 7, [0, "50WGHf42lORpBT4F46oDur"], [4, 1426063360]], [6, 8, [0, "feFNf6awpLBIIu4ATSGHcp"]], [17, 1.02, 3, [0, "aaLDdq449KE4h164Bby2PQ"], [4, 4292269782], 3], [18, 0, 9, [0, "43G7aTF8tEAagDzmdk7uWO"]], [19, 6, [0, "ccIG7zpnpGgaX6oeJ1ke0V"]], [6, 11, [0, "bd+0kOj7BEX70symiBRInc"]], [22, "用户名名称", 48, 48, 80, false, 13, [0, "f26OUwdhtH7KKc5IFc8wus"]], [24, "--", 0, 80, 80, 80, false, true, 4, 15, [0, "2fPVxNC85EPrnkG6bAulP/"], [4, 4282967039], [4, 4280098273]]], 0, [0, 3, 1, 0, 0, 1, 0, 4, 23, 0, 5, 22, 0, 6, 21, 0, 7, 20, 0, 8, 17, 0, 9, 19, 0, 10, 18, 0, 11, 16, 0, 12, 2, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, -1, 7, 0, -2, 8, 0, -3, 3, 0, -4, 9, 0, -5, 6, 0, -6, 10, 0, -7, 4, 0, -8, 12, 0, -9, 13, 0, -10, 5, 0, 0, 3, 0, 0, 3, 0, -3, 18, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, -1, 11, 0, 0, 5, 0, 0, 5, 0, -1, 14, 0, -2, 15, 0, 0, 6, 0, 0, 6, 0, -3, 20, 0, 0, 7, 0, -2, 16, 0, 0, 8, 0, -2, 17, 0, 0, 9, 0, -2, 19, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, -2, 21, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, -2, 22, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, -2, 23, 0, 13, 1, 56], [0, 0, 0, 16, 22, 23], [1, 1, 2, 1, 2, 2], [1, 2, 0, 3, 0, 0]]