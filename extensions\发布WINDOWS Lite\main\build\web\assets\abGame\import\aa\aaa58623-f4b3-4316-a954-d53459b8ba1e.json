[1, ["52fHu7D8hGm5vLaoALoXCl", "31NQFlbStINYf80pkGFD3U@f9941", "ad23tLbu9DJ4rDiaJFr5kF@f9941", "71dtqao85A+a1mthAjZu1K@f9941", "63/tIAGrhFt4fvv2Mxh6pj@f9941", "3bvJBntRxNdLTqkOiS7Nx7@f9941", "20g1ukYUVPvKWKBRznAKo+@f9941", "44qks5UYpFYJLTOQcNncqu@f9941", "21M6Z+Do5EsZPhIOc3BAXR@f9941", "2bdsC9TkpDUawyxovu5ztR@f9941", "8dZBe2TxRCQYHkA1tEVgDS@f9941", "54TknWPwVPqJqeCR+Y/Czo@f9941", "95EkngnxZFbYuFpsqVTaFr@f9941"], ["node", "_spriteFrame", "_font", "_parent", "_normalSprite", "_hoverSprite", "_pressedSprite", "_disabledSprite", "root", "lbUserNum", "ndUserNum", "lbTime", "ndTime", "lbNameMostHelpful", "sprAvatarMostHelpful", "ndMostHelpful", "lbNameMostKillMonster", "sprAvatarMostKillMonster", "ndMostKillMonster", "lbNameMostKillBoss", "sprAvatarMostKillBoss", "ndMostKillBoss", "lbNameFightFirst", "sprAvatarFightFirst", "ndFightFirst", "ndTitleFail", "ndTitleWin", "ndTitleCross", "lbBtnTxt", "btnCheckRank", "lbDesc", "btnClose", "sprFrame", "ndRoot", "data"], [["cc.Node", ["_name", "_layer", "_active", "_components", "_prefab", "_children", "_parent", "_lpos"], 0, 9, 4, 2, 1, 5], ["cc.Node", ["_name", "_layer", "_active", "_parent", "_components", "_prefab", "_children", "_lpos"], 0, 1, 12, 4, 2, 5], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_isSystemFontUsed", "_enableOutline", "node", "__prefab", "_color", "_outlineColor", "_font"], -2, 1, 4, 5, 5, 6], ["cc.Sprite", ["_sizeMode", "_type", "node", "__prefab", "_spriteFrame"], 1, 1, 4, 6], ["cc.UITransform", ["node", "__prefab", "_contentSize"], 3, 1, 4, 5], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.<PERSON><PERSON>", ["_transition", "node", "__prefab", "_normalColor", "_target"], 2, 1, 4, 5, 1], ["cc.Mask", ["_segments", "node", "__prefab"], 2, 1, 4], ["cc.Graphics", ["node", "__prefab", "_fillColor"], 3, 1, 4, 5], ["cc.Layout", ["_resizeMode", "_layoutType", "_spacingX", "_spacingY", "node", "__prefab"], -1, 1, 4], ["792d71v7xJMv64a8rs0/smV", ["node", "__prefab", "ndRoot", "sprFrame", "btnClose", "lbDesc", "btnCheckRank", "lbBtnTxt", "ndTitleCross", "ndTitleWin", "ndTitleFail", "ndFightFirst", "sprAvatarFightFirst", "lbNameFightFirst", "ndMostKillBoss", "sprAvatarMostKillBoss", "lbNameMostKillBoss", "ndMostKillMonster", "sprAvatarMostKillMonster", "lbNameMostKillMonster", "ndMostHelpful", "sprAvatarMostHelpful", "lbNameMostHelpful", "ndTime", "lbTime", "ndUserNum", "lbUserNum"], 3, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]], [[6, 0, 2], [7, 0, 1, 2, 3, 4, 5, 5], [4, 0, 1, 2, 1], [0, 0, 1, 6, 3, 4, 7, 3], [3, 2, 3, 4, 1], [1, 0, 1, 3, 4, 5, 7, 3], [2, 0, 1, 2, 3, 5, 6, 7, 9, 5], [2, 0, 1, 2, 3, 5, 6, 7, 5], [3, 0, 2, 3, 2], [0, 0, 2, 1, 5, 3, 4, 7, 4], [0, 0, 1, 6, 5, 3, 4, 7, 3], [1, 0, 1, 3, 4, 5, 3], [9, 0, 1, 2, 2], [10, 0, 1, 2, 1], [0, 0, 2, 1, 6, 5, 3, 4, 7, 4], [4, 0, 1, 1], [8, 0, 1, 2, 3, 4, 2], [5, 0, 2], [0, 0, 1, 5, 3, 4, 3], [0, 0, 1, 6, 5, 3, 4, 3], [0, 0, 1, 6, 3, 4, 3], [0, 0, 1, 5, 3, 4, 7, 3], [1, 0, 1, 3, 6, 4, 5, 3], [1, 0, 1, 3, 6, 4, 5, 7, 3], [1, 0, 2, 1, 3, 6, 4, 5, 7, 4], [3, 1, 2, 3, 4, 2], [3, 1, 0, 2, 3, 4, 3], [2, 0, 1, 2, 3, 4, 5, 6, 8, 6], [2, 0, 1, 5, 6, 7, 3], [2, 0, 1, 2, 5, 6, 7, 4], [11, 0, 1, 2, 3, 4, 5, 5], [12, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 1]], [[17, "ViewGameOver"], [18, "ViewGameOver", 33554432, [-29], [[15, -2, [0, "185qHxW/tCuJnTtEP3DYXQ"]], [31, -28, [0, "5bQDCv8ilMr69ASSOpU+O0"], -27, -26, -25, -24, -23, -22, -21, -20, -19, -18, -17, -16, -15, -14, -13, -12, -11, -10, -9, -8, -7, -6, -5, -4, -3]], [1, "85VqSQ6VZGLYnbOetAOXTZ", null, null, null, -1, 0]], [9, "ndFightFirst", false, 33554432, [-32, -33, -34, -35, -36, -37, -38], [[2, -30, [0, "0ee0+WtB9I+6APFwPF25kj"], [5, 190, 310]], [4, -31, [0, "74G48u12ZPDan1IzGsEXoM"], 10]], [1, "dcw+szEFFGm7AAqrzc3Azb", null, null, null, 1, 0], [1, -210, 160, 0]], [9, "ndMostKillBoss", false, 33554432, [-41, -42, -43, -44, -45, -46, -47], [[2, -39, [0, "92cOQx+5hGZrKygMxPpWVF"], [5, 190, 310]], [4, -40, [0, "c4NCTjJXRLKrXCGSeRrse6"], 16]], [1, "47qCKj+ZxMmaIqMFopd/iK", null, null, null, 1, 0], [1, 0, 160, 0]], [9, "ndMostMonster", false, 33554432, [-50, -51, -52, -53, -54, -55, -56], [[2, -48, [0, "71qfildeVPhZpnfdVVd6wf"], [5, 190, 310]], [4, -49, [0, "03WKK6PyhCxJ3hdInGmFQ2"], 22]], [1, "4cBlMpFh1PAaodWrgW5Qhz", null, null, null, 1, 0], [1, 210, 160, 0]], [9, "ndMostHelpful", false, 33554432, [-59, -60, -61, -62, -63, -64, -65], [[2, -57, [0, "d7KQRhV0FB8qyTRCGXBFHB"], [5, 190, 310]], [4, -58, [0, "18NSnOaeZETbokN33F0Rgm"], 28]], [1, "a31MqG7RJIeIznuMAkgO3V", null, null, null, 1, 0], [1, -210, -160, 0]], [21, "Node", 33554432, [2, 3, 4, 5, -68, -69], [[2, -66, [0, "dfNo4LUrtOpKlTDloiLqfX"], [5, 610, 0]], [30, 1, 3, 20, 10, -67, [0, "18IzTSL2NIup1AxYr7kSsz"]]], [1, "d6fhtnrWZGWZ3SD1KxhRWp", null, null, null, 1, 0], [1, 0, 6.1749999999999545, 0]], [19, "ndRoot", 33554432, 1, [-71, -72, -73, -74, 6], [[15, -70, [0, "f2NXIc8G1IhqpNeXyYquqh"]]], [1, "a5knswA25B4ppbKDXrdEGN", null, null, null, 1, 0]], [14, "ndTime", false, 33554432, 6, [-77, -78, -79], [[2, -75, [0, "013JGj/2hFN7E+SqqXZB+q"], [5, 190, 310]], [4, -76, [0, "6dO7NPK5BG/ZLtQrajgO3W"], 31]], [1, "d4oqjZ4MxEc5PzFX7byMxc", null, null, null, 1, 0], [1, 0, -160, 0]], [14, "ndUserNum", false, 33554432, 6, [-82, -83, -84], [[2, -80, [0, "a8TGVK9qpD4oEfYvNBaAaQ"], [5, 190, 310]], [4, -81, [0, "c3HwAYRFxIYIPUQssIO/6L"], 34]], [1, "40Qta9DUlE/ZtgxELDUFZR", null, null, null, 1, 0], [1, 210, -160, 0]], [22, "sprFrame", 33554432, 7, [-87, -88, -89], [[[2, -85, [0, "5azBgA74xMJZfcQgO47Wl7"], [5, 500, 800]], -86], 4, 1], [1, "7dCKCXmiFIFqlnKZZEOjCH", null, null, null, 1, 0]], [23, "btnCheckRank", 33554432, 7, [-93], [[[2, -90, [0, "59Ai2PyzZLw6hoxDn9ODA7"], [5, 350, 87]], [25, 1, -91, [0, "45YjLrNINKiLExX0N86/WA"], 3], -92], 4, 4, 1], [1, "b06FAKQqBANb0PFjOghEJS", null, null, null, 1, 0], [1, 0.32000000000005, -407.67100000000005, 0]], [24, "btnClose", false, 33554432, 7, [-97], [[[2, -94, [0, "2fpnX2U9tJGZxJC58SyvCR"], [5, 160, 60]], [26, 1, 0, -95, [0, "79v+CICE5CI7rWT96ckoZM"], 4], -96], 4, 4, 1], [1, "c3nw+qt6hLoLodv/JjyeVJ", null, null, null, 1, 0], [1, 0, -592.5, 0]], [10, "Node", 33554432, 2, [-101], [[2, -98, [0, "67LvGSiq5CgKplmF03drkU"], [5, 104, 104]], [12, 20, -99, [0, "2aJKZbwS1IrLcSvj1vp7yz"]], [13, -100, [0, "00SzEY4aZGA7iOj7xCeieN"], [4, 16777215]]], [1, "2aurcYmGlHf69P9b6r8ZP2", null, null, null, 1, 0], [1, 0, 47.78800000000001, 0]], [10, "Node", 33554432, 3, [-105], [[2, -102, [0, "05+vssYrNE8obv+F4fZ757"], [5, 104, 104]], [12, 20, -103, [0, "10cd/chdpBg5f2Hl3tgP4P"]], [13, -104, [0, "adfArf7KFGzJ/Hdta7tpaU"], [4, 16777215]]], [1, "92I578/u9C87xycPaYHNZH", null, null, null, 1, 0], [1, 0, 47.78800000000001, 0]], [10, "Node", 33554432, 4, [-109], [[2, -106, [0, "f0q9an7eZD6rkGCG6SfCix"], [5, 104, 104]], [12, 20, -107, [0, "71LV03ShZKa7jpTLW6GuhP"]], [13, -108, [0, "1eIPuPZcFETKiLTPCVc/xA"], [4, 16777215]]], [1, "bacvnJ+ORIDKkw8AyJLA62", null, null, null, 1, 0], [1, 0, 47.78800000000001, 0]], [10, "Node", 33554432, 5, [-113], [[2, -110, [0, "b7vLVjJIdD35qvEfcErBv1"], [5, 104, 104]], [12, 20, -111, [0, "37JvtBPBlDdY/0dXX9+FdU"]], [13, -112, [0, "82j40kHAhPf4+eMwVZ5Ea8"], [4, 16777215]]], [1, "32D0dh21FF2Z7hQ9gjVeGl", null, null, null, 1, 0], [1, 0, 47.78800000000001, 0]], [3, "ndTitleCross", 33554432, 10, [[2, -114, [0, "c0J5/6yEVEGIQ7P2E32Nge"], [5, 807, 523]], [4, -115, [0, "feKmGT0WtBcL5gBBB9Uou9"], 0]], [1, "13FY2G/1dOk4BJQtjoHLiZ", null, null, null, 1, 0], [1, 0, 523.152, 0]], [3, "ndTitleFail", 33554432, 10, [[2, -116, [0, "27uHbUtYVGZb7azKMEdHpD"], [5, 823, 451]], [4, -117, [0, "a2rOCAvTlDGavAMgaI+bAK"], 1]], [1, "f5jjf8IcRNXKe2AbyNVbBQ", null, null, null, 1, 0], [1, 0, 523.152, 0]], [3, "ndTitleWin", 33554432, 10, [[2, -118, [0, "3avACIojNEBLxUjpsdWL98"], [5, 807, 523]], [4, -119, [0, "0bp1WfBp9MybeGkYp5RmP+"], 2]], [1, "aaALsOpKFB57s23WlV6DEF", null, null, null, 1, 0], [1, 0, 523.152, 0]], [5, "lbBtnTxt", 33554432, 11, [[[2, -120, [0, "0e1oYiSW5FTbbrqOyOB5Ql"], [5, 205.3299560546875, 54.4]], -121], 4, 1], [1, "55/F6J5/ZFHZrPfmN12pq1", null, null, null, 1, 0], [1, 0, 2.875, 0]], [11, "Label", 33554432, 7, [[[2, -122, [0, "73JHdB7spOg40KmXPXDqCQ"], [5, 0, 50.4]], -123], 4, 1], [1, "9dxoPTc3xKk6Bi+IYF7TBk", null, null, null, 1, 0]], [20, "Label", 33554432, 12, [[2, -124, [0, "87erTG0ltJyIMu3kyr/sUM"], [5, 80, 50.4]], [29, "返回大厅", 20, 20, -125, [0, "8fpGoD8/lKrLmaWLA/abWK"], [4, 4281545523]]], [1, "ba1wioIqZPFKYxEsf16cBq", null, null, null, 1, 0]], [3, "unitframe", 33554432, 2, [[2, -126, [0, "4cR2gxaYJIQZthqA2EqWDN"], [5, 108, 108]], [4, -127, [0, "9a+0QIK<PERSON>E5aAhZD4uLtm3"], 5]], [1, "21DGaWhaBL/LFWCyjh4AlP", null, null, null, 1, 0], [1, 0, 47.96199999999999, 0]], [3, "line01", 33554432, 2, [[2, -128, [0, "30/Sk+PwhKGpvfNyStC+oR"], [5, 158, 2]], [4, -129, [0, "7dgRG/XP1PkoVnoQ8rBEwJ"], 6]], [1, "d4km3MUJpJjIkpv2JB1iA5", null, null, null, 1, 0], [1, 0, -34.67100000000005, 0]], [3, "line02", 33554432, 2, [[2, -130, [0, "54Zzrnsl1JCZkokD41bYOz"], [5, 158, 2]], [4, -131, [0, "f4Urw1bztJuqfcj7/yfw76"], 7]], [1, "1ewhnTo+BC5rHjnG3nZWgv", null, null, null, 1, 0], [1, 0, -81.47700000000009, 0]], [11, "spr<PERSON><PERSON><PERSON>", 33554432, 13, [[[2, -132, [0, "cdo+y1B0VOwaahUUFSo23k"], [5, 104, 104]], -133], 4, 1], [1, "46bf36OllIloULEighWxn7", null, null, null, 1, 0]], [3, "ndMask", 33554432, 2, [[2, -134, [0, "63g+iq1vFCdbfFfDFI9tY2"], [5, 108, 108]], [4, -135, [0, "824aYU/sZE0ZFsn0nRAO2d"], 8]], [1, "20Qj2xvV5OWq69Vgm4TLaY", null, null, null, 1, 0], [1, 0, 47.96199999999999, 0]], [3, "lbSubtitle", 33554432, 2, [[2, -136, [0, "d5M61T00ZANI9uiXoMxmpC"], [5, 120, 50.4]], [6, "战力第一", 30, 30, false, -137, [0, "1dNXO8KldPqJSXhc4RCRIZ"], [4, 4281616378], 9]], [1, "5axIsTcxNLHIleIsyEUrFd", null, null, null, 1, 0], [1, 0, -57.50700000000006, 0]], [5, "lbName", 33554432, 2, [[[2, -138, [0, "7ejc3al3hO26PAgQloURIM"], [5, 18.399993896484375, 50.4]], -139], 4, 1], [1, "444yVutzNHaYmqbGH0q0CS", null, null, null, 1, 0], [1, 0, -103.99199999999996, 0]], [3, "unitframe", 33554432, 3, [[2, -140, [0, "20TUSQplxJhpjUEPOWo1eg"], [5, 108, 108]], [4, -141, [0, "8esRh7VU9FfqXfC8D2t+jI"], 11]], [1, "17YaxAXupB2YzxYH4G2pod", null, null, null, 1, 0], [1, 0, 47.96199999999999, 0]], [3, "line01", 33554432, 3, [[2, -142, [0, "ddnVjodbBLL7+KgvhrJxUB"], [5, 158, 2]], [4, -143, [0, "f7Dphk3EpGFLEFB/ANlcQz"], 12]], [1, "abqxtkbmBI8Y8CXJlWB1Ke", null, null, null, 1, 0], [1, 0, -34.67100000000005, 0]], [3, "line02", 33554432, 3, [[2, -144, [0, "29ffah+mxJOb5+GBzcWQLX"], [5, 158, 2]], [4, -145, [0, "3cxqgKJEFEd5GmjjDsAO3Z"], 13]], [1, "1fOLw9eIdHiplJtyce5Mtw", null, null, null, 1, 0], [1, 0, -81.47700000000009, 0]], [11, "spr<PERSON><PERSON><PERSON>", 33554432, 14, [[[2, -146, [0, "718EMgg11KC5OlzjJN4SO/"], [5, 104, 104]], -147], 4, 1], [1, "d9sKkWwCdAu7R3GSH4vKe9", null, null, null, 1, 0]], [3, "ndMask", 33554432, 3, [[2, -148, [0, "6fakH+FoFDB5cKCwrEyZHX"], [5, 108, 108]], [4, -149, [0, "e0x7Uz6z9El42bScQ+q7VQ"], 14]], [1, "f3Y1wVNj9FLZsDReoQxKF6", null, null, null, 1, 0], [1, 0, 47.96199999999999, 0]], [3, "lbSubtitle", 33554432, 3, [[2, -150, [0, "60mg/4Xk1A95WnzKGgS/9K"], [5, 148.3499755859375, 50.4]], [6, "boss击杀者", 30, 30, false, -151, [0, "7dRW9o4eVNTbebTg8TZeO0"], [4, 4281616378], 15]], [1, "53axQQTz5PiJbuYmn/CnhX", null, null, null, 1, 0], [1, 0, -57.50700000000006, 0]], [5, "lbName", 33554432, 3, [[[2, -152, [0, "8bba4lYKlNKaOIRCjEWdY8"], [5, 18.399993896484375, 50.4]], -153], 4, 1], [1, "cbuY+e8KtObLc3Vn834MBB", null, null, null, 1, 0], [1, 0, -103.99199999999996, 0]], [3, "unitframe", 33554432, 4, [[2, -154, [0, "808wqxFW5DHYCSurztcde0"], [5, 108, 108]], [4, -155, [0, "104Bubym5PWIDGU5IIMCz7"], 17]], [1, "06ofqKf0ND9IibzwNimzSL", null, null, null, 1, 0], [1, 0, 47.96199999999999, 0]], [3, "line01", 33554432, 4, [[2, -156, [0, "ead4PQnvBKFYX49tnNGnOR"], [5, 158, 2]], [4, -157, [0, "14RcoK3/ZMsoQDp0yU/2sx"], 18]], [1, "e0N9iTcIdGmq3iexnZasIF", null, null, null, 1, 0], [1, 0, -34.67100000000005, 0]], [3, "line02", 33554432, 4, [[2, -158, [0, "00ANmuy+xG8aCANiTsE00Q"], [5, 158, 2]], [4, -159, [0, "d98ENCIB9B14sdYuDFUKq2"], 19]], [1, "2a0x0NE9hAPJKSfnYl32iN", null, null, null, 1, 0], [1, 0, -81.47700000000009, 0]], [5, "spr<PERSON><PERSON><PERSON>", 33554432, 15, [[[2, -160, [0, "c4ZsfgfINPzYfa8h4uT5IP"], [5, 104, 104]], -161], 4, 1], [1, "38MT5vGZBGLZrEHCWpbqy/", null, null, null, 1, 0], [1, 0, 0.17399999999997817, 0]], [3, "ndMask", 33554432, 4, [[2, -162, [0, "11k5XWv0JBoZQMt41eK1xq"], [5, 108, 108]], [4, -163, [0, "e82Hw2FSRPbbSLt5V0zPLg"], 20]], [1, "07+yMJf0lEioj9NlHA2tyC", null, null, null, 1, 0], [1, 0, 47.96199999999999, 0]], [3, "lbSubtitle", 33554432, 4, [[2, -164, [0, "a6YJ0ZT05BAYheCxsULAZ9"], [5, 120, 50.4]], [6, "杀敌最多", 30, 30, false, -165, [0, "240cYTRRVKJokZMfU/9IxA"], [4, 4281616378], 21]], [1, "54xoSztxxL576YzUm99SKB", null, null, null, 1, 0], [1, 0, -57.50700000000006, 0]], [5, "lbName", 33554432, 4, [[[2, -166, [0, "46Jf8NOilDTasNwJH819/2"], [5, 18.399993896484375, 50.4]], -167], 4, 1], [1, "c7eAQ6dSVKK4WAiTPrecLW", null, null, null, 1, 0], [1, 0, -103.99199999999996, 0]], [3, "unitframe", 33554432, 5, [[2, -168, [0, "fcKk9CuRVPdKaguYCGc10V"], [5, 108, 108]], [4, -169, [0, "8bkvmVAKFCi49Fj47CN9cr"], 23]], [1, "73zJVMZ99C8aj+A8KJyDvg", null, null, null, 1, 0], [1, 0, 47.96199999999999, 0]], [3, "line01", 33554432, 5, [[2, -170, [0, "00RnEN+CBEh4s6X3jXusUK"], [5, 158, 2]], [4, -171, [0, "18QIR7dD9CRb/TMuT8SnRu"], 24]], [1, "04Wf+oW11MDJ9uYTyhF8oK", null, null, null, 1, 0], [1, 0, -34.67100000000005, 0]], [3, "line02", 33554432, 5, [[2, -172, [0, "7fT7V8U0NGm6dVc3fo4cl5"], [5, 158, 2]], [4, -173, [0, "29TpzGpsxP3aLwh598Xmam"], 25]], [1, "92jd8AMZlI0qCfprxD3aqb", null, null, null, 1, 0], [1, 0, -81.47700000000009, 0]], [11, "spr<PERSON><PERSON><PERSON>", 33554432, 16, [[[2, -174, [0, "95PRs+OZBGOKTsZcl8XxMG"], [5, 104, 104]], -175], 4, 1], [1, "61EwMSCTBOHoYL4Z90oMHw", null, null, null, 1, 0]], [3, "ndMask", 33554432, 5, [[2, -176, [0, "3ffUhwvANDqrJEO+TQl6UV"], [5, 108, 108]], [4, -177, [0, "87Uj/Q9vNLuJc4KFCT7Nps"], 26]], [1, "62SU0/fPdPh56P5S4IPbau", null, null, null, 1, 0], [1, 0, 47.96199999999999, 0]], [3, "lbSubtitle", 33554432, 5, [[2, -178, [0, "f7j96xQNpFWJk2A2PaRj9X"], [5, 120, 50.4]], [6, "最强后援", 30, 30, false, -179, [0, "e04VNO5vdPuaAdD2eEsNg9"], [4, 4281616378], 27]], [1, "d6ckY2ZDFLfJaZyMi+bONq", null, null, null, 1, 0], [1, 0, -57.50700000000006, 0]], [5, "lbName", 33554432, 5, [[[2, -180, [0, "a5Q2JhyOxK4pvganKzbmyN"], [5, 18.399993896484375, 50.4]], -181], 4, 1], [1, "548G0nSeVGfrW5aRmcnt6b", null, null, null, 1, 0], [1, 0, -103.99199999999996, 0]], [3, "line01", 33554432, 8, [[2, -182, [0, "263OqccrZCyqkcwYv/bhrY"], [5, 158, 2]], [4, -183, [0, "11fRUeKWtAY4LiJyFswErd"], 29]], [1, "e0jlY+AUpAd7Z3j3339A7J", null, null, null, 1, 0], [1, 0, -2.52800000000002, 0]], [3, "lbSubtitle", 33554432, 8, [[2, -184, [0, "7248ffUe5Gjqmr05iVhqpW"], [5, 120, 50.4]], [6, "本局用时", 30, 30, false, -185, [0, "6bHMYQ7C1Neas9Y4Zk7FdL"], [4, 4281616378], 30]], [1, "dfAeTi7CROZYoZpTs+O1Yp", null, null, null, 1, 0], [1, 0, 24, 0]], [5, "lbTime", 33554432, 8, [[[2, -186, [0, "84NjYN66BPJIGL0ooNypVb"], [5, 55.87995910644531, 50.4]], -187], 4, 1], [1, "4fXwRlgDBHsLdDgBqzTq8f", null, null, null, 1, 0], [1, 0, -24.08050000000003, 0]], [3, "line01", 33554432, 9, [[2, -188, [0, "a7Hy07SqhJRa9JieBOJzHo"], [5, 158, 2]], [4, -189, [0, "5cDMTvc3hI1phQEyXkmvl+"], 32]], [1, "b5uhp7LupFEpJRhstIQYRi", null, null, null, 1, 0], [1, 0, -2.52800000000002, 0]], [5, "lbUserNum", 33554432, 9, [[[2, -190, [0, "84CUXX/5xNZIOnq0wJj/BS"], [5, 24.639984130859375, 50.4]], -191], 4, 1], [1, "bcu4yAaPNAoKlSjFFRY2ub", null, null, null, 1, 0], [1, 0, -24.08050000000003, 0]], [3, "lbSubtitle", 33554432, 9, [[2, -192, [0, "39C2mdmVZDq5EVasMeqVtQ"], [5, 120, 50.4]], [6, "参与人数", 30, 30, false, -193, [0, "59pry5M49PCoXVffMiKpsF"], [4, 4281616378], 33]], [1, "efhHp6P7dCIp7yRFyWlRZe", null, null, null, 1, 0], [1, 0, 24, 0]], [8, 0, 10, [0, "3egMhJD1ZHpY1UsxpKeDiD"]], [27, "查看排行榜(10)", 30, 30, false, true, 20, [0, "1bUMKsoT5MS5/a0bz6Apoe"], [4, 4287136803]], [16, 3, 11, [0, "22clxc2JpNpqLMnpJVYKhv"], [4, **********], 11], [28, "", 40, 21, [0, "19o92hp3hO+67K+UJNpMLp"], [4, 4278190272]], [16, 2, 12, [0, "afoFtjWmlDI5k87t2bbNaU"], [4, **********], 12], [8, 0, 26, [0, "3f18C7fIdMML89cGFltfSP"]], [7, "--", 20, 20, false, 29, [0, "96JC//69RBlJCLW/v9eD3h"], [4, **********]], [8, 0, 33, [0, "6dfEQ09kRG6ZUqmZ0dQYmx"]], [7, "--", 20, 20, false, 36, [0, "92he5QIoJN274ts8WmCXWK"], [4, **********]], [8, 0, 40, [0, "feR5IM3NVMbq0mVCKGP4gc"]], [7, "--", 20, 20, false, 43, [0, "faxF0CD0lFja4yq5hZNMLd"], [4, **********]], [8, 0, 47, [0, "67zDpOi55K/q+O3vF6/V4n"]], [7, "--", 20, 20, false, 50, [0, "14uQOOWh9A9rM9W2Bcsnpi"], [4, **********]], [7, "00:00", 20, 20, false, 53, [0, "c3D3hCec1F6I46dkEN4MK4"], [4, **********]], [7, "00", 20, 20, false, 55, [0, "e4jZXz1dFHxo8UGTpOz9IN"], [4, **********]]], 0, [0, 8, 1, 0, 0, 1, 0, 9, 71, 0, 10, 9, 0, 11, 70, 0, 12, 8, 0, 13, 69, 0, 14, 68, 0, 15, 5, 0, 16, 67, 0, 17, 66, 0, 18, 4, 0, 19, 65, 0, 20, 64, 0, 21, 3, 0, 22, 63, 0, 23, 62, 0, 24, 2, 0, 25, 18, 0, 26, 19, 0, 27, 17, 0, 28, 58, 0, 29, 59, 0, 30, 60, 0, 31, 61, 0, 32, 57, 0, 33, 7, 0, 0, 1, 0, -1, 7, 0, 0, 2, 0, 0, 2, 0, -1, 23, 0, -2, 24, 0, -3, 25, 0, -4, 13, 0, -5, 27, 0, -6, 28, 0, -7, 29, 0, 0, 3, 0, 0, 3, 0, -1, 30, 0, -2, 31, 0, -3, 32, 0, -4, 14, 0, -5, 34, 0, -6, 35, 0, -7, 36, 0, 0, 4, 0, 0, 4, 0, -1, 37, 0, -2, 38, 0, -3, 39, 0, -4, 15, 0, -5, 41, 0, -6, 42, 0, -7, 43, 0, 0, 5, 0, 0, 5, 0, -1, 44, 0, -2, 45, 0, -3, 46, 0, -4, 16, 0, -5, 48, 0, -6, 49, 0, -7, 50, 0, 0, 6, 0, 0, 6, 0, -5, 8, 0, -6, 9, 0, 0, 7, 0, -1, 10, 0, -2, 11, 0, -3, 21, 0, -4, 12, 0, 0, 8, 0, 0, 8, 0, -1, 51, 0, -2, 52, 0, -3, 53, 0, 0, 9, 0, 0, 9, 0, -1, 54, 0, -2, 55, 0, -3, 56, 0, 0, 10, 0, -2, 57, 0, -1, 17, 0, -2, 18, 0, -3, 19, 0, 0, 11, 0, 0, 11, 0, -3, 59, 0, -1, 20, 0, 0, 12, 0, 0, 12, 0, -3, 61, 0, -1, 22, 0, 0, 13, 0, 0, 13, 0, 0, 13, 0, -1, 26, 0, 0, 14, 0, 0, 14, 0, 0, 14, 0, -1, 33, 0, 0, 15, 0, 0, 15, 0, 0, 15, 0, -1, 40, 0, 0, 16, 0, 0, 16, 0, 0, 16, 0, -1, 47, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, -2, 58, 0, 0, 21, 0, -2, 60, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, -2, 62, 0, 0, 27, 0, 0, 27, 0, 0, 28, 0, 0, 28, 0, 0, 29, 0, -2, 63, 0, 0, 30, 0, 0, 30, 0, 0, 31, 0, 0, 31, 0, 0, 32, 0, 0, 32, 0, 0, 33, 0, -2, 64, 0, 0, 34, 0, 0, 34, 0, 0, 35, 0, 0, 35, 0, 0, 36, 0, -2, 65, 0, 0, 37, 0, 0, 37, 0, 0, 38, 0, 0, 38, 0, 0, 39, 0, 0, 39, 0, 0, 40, 0, -2, 66, 0, 0, 41, 0, 0, 41, 0, 0, 42, 0, 0, 42, 0, 0, 43, 0, -2, 67, 0, 0, 44, 0, 0, 44, 0, 0, 45, 0, 0, 45, 0, 0, 46, 0, 0, 46, 0, 0, 47, 0, -2, 68, 0, 0, 48, 0, 0, 48, 0, 0, 49, 0, 0, 49, 0, 0, 50, 0, -2, 69, 0, 0, 51, 0, 0, 51, 0, 0, 52, 0, 0, 52, 0, 0, 53, 0, -2, 70, 0, 0, 54, 0, 0, 54, 0, 0, 55, 0, -2, 71, 0, 0, 56, 0, 0, 56, 0, 34, 1, 2, 3, 6, 3, 3, 6, 4, 3, 6, 5, 3, 6, 6, 3, 7, 193], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 58, 61, 61, 61, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71], [1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 2, 1, 1, 2, 1, 1, 2, 1, 2, 4, 5, 6, 7, 1, 2, 1, 2, 1, 2, 1, 2, 2, 2], [7, 8, 9, 10, 6, 3, 1, 1, 4, 0, 2, 3, 1, 1, 4, 0, 2, 3, 1, 1, 4, 0, 2, 3, 1, 1, 4, 0, 2, 1, 0, 2, 1, 0, 2, 0, 6, 6, 11, 12, 5, 0, 5, 0, 5, 0, 5, 0, 0, 0]]