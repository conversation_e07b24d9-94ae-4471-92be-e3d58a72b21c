import { _decorator, Color, Component, Label, log, Node, Sprite } from 'cc';
import { DEBUG } from 'cc/env';
import { ConfigHelper } from 'db://assets/scripts/config/ConfigHelper';
import { xcore } from 'db://assets/scripts/libs/xcore';
const { ccclass, property } = _decorator;

@ccclass('UnitSelectGameLevel')
export class UnitSelectGameLevel extends Component {

    @property(Node)
    private ndOff: Node = null;

    @property(Node)
    private ndOn: Node = null;

    @property(Label)
    private lbLev: Label = null;


    private cb: Function = null;
    private levIndex: number

    private isAbleSelect: boolean = false;


    start() {
        this.node.on('click', () => {
            if (!this.isAbleSelect || xcore.gameData.gameLev < this.levIndex) {
                //xcore.ui.showToast('暂未解锁');
                return
            }

            this.cb && this.cb(this.levIndex)
        }, this)
    }
    setData(data: any, index: number, cb) {
        this.cb = cb;
        this.levIndex = index + 1;
        this.lbLev.string = (this.levIndex).toString();
        this.isAbleSelect = ConfigHelper.getInstance().getAbleSelectLev(this.levIndex) || this.levIndex == 1;

        if (!xcore.gameData.gameLev) {
            xcore.gameData.gameLev = 1;
        }
        /*  if (DEBUG) {
             xcore.gameData.gameLev = 120
         } */
        this.lbLev.color = xcore.gameData.gameLev < this.levIndex || !this.isAbleSelect ? new Color(180, 180, 180, 255) : new Color(52, 36, 24, 255);
        this.ndOn.getComponent(Sprite).grayscale = xcore.gameData.gameLev < this.levIndex || !this.isAbleSelect
      /*   this.ndOff.active = xcore.gameData.gameLev < this.levIndex || !this.isAbleSelect;
        this.ndOn.active = xcore.gameData.gameLev >= this.levIndex && this.isAbleSelect; */
    }

}


