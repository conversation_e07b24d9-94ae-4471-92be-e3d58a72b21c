import { _decorator, Component, VideoClip, RenderableComponent, Texture2D, loader, game, Game, Material, Sprite, SpriteFrame, gfx, VideoPlayer, log, setDisplayStats, warn, UITransform, view } from 'cc';
import { JSB } from 'cc/env';
const { ccclass, property } = _decorator;

export enum EventType {     //事件类型
    PREPARING = 1,      //准备中
    LOADED = 2,         //已加载
    READY = 3,          //准备完毕
    COMPLETED = 4,      //播放完成
    ERROR = 5,          //播放错误
    PLAYING = 6,        //播放中
    PAUSED = 7,         //暂停
    STOPPED = 8,        //停止
    BUFFER_START = 9,       //
    BUFFER_UPDATE = 10,
    BUFFER_END = 11
};

enum VideoState {       //视频状态
    ERROR = -1,         // 出错状态   
    IDLE = 0,           // 置空状态
    PREPARING = 1,      //准备中
    PREPARED = 2,       //准备完成
    PLAYING = 3,        //播放中
    PAUSED = 4,         //暂停
    STOP = 5,
    COMPLETED = 5       //播放完成
};

enum ReadyState {       //准备状态
    HAVE_NOTHING = 0,
    HAVE_METADATA = 1,
    HAVE_CURRENT_DATA = 2,
    HAVE_FUTURE_DATA = 3,
    HAVE_ENOUGH_DATA = 4
};

enum PixelFormat {  //像素格式
    NONE = -1,
    I420 = 0,        //yuv
    RGB = 2,        //rgb
    NV12 = 23,      //nv12
    NV21 = 24,      //nv21
    RGBA = 26       //rgba
};

@ccclass('MediaVideo')
export class MediaVideo extends Component {

    @property
    private _clip: VideoClip = null;            //视频资源

    private _seekTime: number = 0;               //搜寻时间 
    private _nativeDuration: number = 0;         //原生的持续时间
    private _nativeWidth: number = 0;           //原生的视频宽          
    private _nativeHeight: number = 0;          //原生的视频高
    private _currentState = VideoState.IDLE;    //当前状态
    private _targetState = VideoState.IDLE;       //目标状态       
    private _pixelFormat = PixelFormat.RGBA;             //像素格式
    private _video: any = null;
    private _texture0: Texture2D = new Texture2D();     //通道0
    private _loaded: boolean = false;                   //是否加载
    private _inBackground: boolean = false;             //是否在后台
    private _lastPlayState: boolean = false;            //上一次播放状态
    private _volume: number = -1;
    private _videoSprites: RenderableComponent[] = [];

    @property(VideoClip)
    get clip() {
        return this._clip;
    }

    set clip(value: VideoClip) {
        this._clip = value;
    }

    @property(VideoPlayer)
    videoPlayer: VideoPlayer = null;

    // loop property
    @property
    loop: boolean = false;



    @property(RenderableComponent)
    public midVideoSp: RenderableComponent = null;


    // rgb material
    @property(Material)
    protected rgbMat: Material = null;

    private _videoUrl: string = null;
    private _cb: Function = null;
    private _cb2: Function = null;

    // current position of the video which is playing
    get currentTime() {
        if (!this._video) return 0;
        if (this._isInPlaybackState()) {
            if (JSB) {
                return this._video.currentTime();
            } else {
                return this._video.currentTime;
            }
        } else {
            return this._seekTime;
        }
    }

    // seek to position
    set currentTime(value: number) {
        if (!this._video) return;
        if (this._isInPlaybackState()) {
            if (JSB) {
                this._video.seek(value);
            } else {
                this._video.currentTime = value;
            }
        } else {
            this._seekTime = value;
        }
    }

    // duration of the video
    get duration(): number {
        if (!this._video) return 0;
        if (this._nativeDuration > 0) return this._nativeDuration;
        if (JSB) {
            this._nativeDuration = this._video.duration();
        } else {
            let duration = this._video.duration;
            this._nativeDuration = isNaN(duration) ? 0 : duration;
        }
        return this._nativeDuration;
    }

    get width(): number {
        if (!this._isInPlaybackState()) return 0;
        if (this._nativeWidth > 0) return this._nativeWidth;
        if (JSB) {
            this._nativeWidth = this._video.width();
        } else {
            let width = this._video.videoWidth;
            this._nativeWidth = isNaN(width) ? 0 : width;
        }
        return this._nativeWidth;
    }

    get height(): number {
        if (!this._isInPlaybackState()) return 0;
        if (this._nativeHeight > 0) return this._nativeHeight;
        if (JSB) {
            this._nativeHeight = this._video.height();
        } else {
            let height = this._video.videoHeight;
            this._nativeHeight = isNaN(height) ? 0 : height;
        }
        return this._nativeHeight;
    }

    // not accurate because native event is async, larger than actual percentage.
    get bufferPercentage(): number {
        if (!this._video) return 0;
        if (JSB) {
            return this._video.bufferPercentage();
        } else {
            return 0;
        }
    }
    setData(data: string | VideoClip, cb?: Function, cb2?: Function) {
        if (typeof data == 'string') {
            this._videoUrl = data;
        } else if (data instanceof VideoClip) {
            this.videoPlayer.clip = data;
            this._clip = data
        }

        this._cb = cb;
        this._cb2 = cb2;
        log("this._videoUrl ", this._videoUrl, data)

        this._initialize();
        if (this._video) {
            this._updateVideoSource();
        }
    }
    start() {
        //setDisplayStats(false);
        /* this._initialize();
        if (this._video) {
            this._updateVideoSource();
        } */
    }

    /**
     * 初始化
     */
    private _initialize() {
        this._initializeBrowser();
    }

    /**
     * initialize browser player, register video event handler
     */
    private _initializeBrowser(): void {

        this._videoSprites.push(this.midVideoSp); // 中

        // @ts-ignore
        this._video = this.videoPlayer._impl._video;
        this._video.crossOrigin = 'anonymous';
        this._video.autoplay = false;
        this._video.loop = false;
        this._video.muted = false;
        this._video.addEventListener('loadedmetadata', () => this._onMetaLoaded());
        this._video.addEventListener('ended', () => this._onCompleted());
        this._loaded = false;
        let onCanPlay = () => {
            if (this._loaded || this._currentState == VideoState.PLAYING)
                return;
            if (this._video.readyState === ReadyState.HAVE_ENOUGH_DATA ||
                this._video.readyState === ReadyState.HAVE_METADATA) {
                log('_initializeBrowser: readyState=', this._video.readyState);
                this._video.currentTime = 0;
                this._loaded = true;
                this._onReadyToPlay();
            }
        };
        this._video.addEventListener('canplay', onCanPlay);
        this._video.addEventListener('canplaythrough', onCanPlay);
        this._video.addEventListener('suspend', onCanPlay);
    }

    /**
     * 处理视频资源
     */
    private _updateVideoSource() {
        let url = this._videoUrl;
        if (!url && this._clip) {
            url = this._clip.nativeUrl;
        }
        /* if (url && loader.md5Pipe) {
            url = loader.md5Pipe.transformURL(url);
        } */

        if (!url) {
            warn('video url null', this._clip)
            return
        }
        this._loaded = false;
        this._video.pause();
        this._video.src = url;

        this.node.emit('preparing', this);
    }

    /**
     * register game show and hide event handler
     */
    public onEnable(): void {
        game.on(Game.EVENT_SHOW, this._onShow, this);
        game.on(Game.EVENT_HIDE, this._onHide, this);
    }

    // unregister game show and hide event handler
    public onDisable(): void {
        game.off(Game.EVENT_SHOW, this._onShow, this);
        game.off(Game.EVENT_HIDE, this._onHide, this);
        this.stop();
    }

    private _onShow(): void {
        if (!this._inBackground) return;
        this._inBackground = false;
        if (this._lastPlayState) this.resume();
    }

    private _onHide(): void {
        if (this._inBackground) return;
        this._inBackground = true;
        this._lastPlayState = this.isPlaying();
        if (this._lastPlayState) this.pause();
    }

    update(deltaTime: number) {
        if (this._isInPlaybackState() && !JSB) {
            this._texture0.uploadData(this._video);
            this._updateMaterial(this._videoSprites);
        }
    }

    /**
     * 更新材质
     */
    protected _updateMaterial(renders: RenderableComponent[]): void {
        for (let i = 0; i < renders.length; i++) {
            const render = renders[i];
            const material = render.getSharedMaterial(0);
            if (material) {
                material.setProperty('texture0', this._texture0);
            }
        }

    }

    /**
     * 初始化材质贴图
     */
    private _initRenderTexture(renders: RenderableComponent[]) {
        for (let i = 0; i < renders.length; i++) {
            const render = renders[i];
            if (render instanceof Sprite) {
                const sprite: Sprite = render;
                if (sprite.spriteFrame === null) {
                    sprite.spriteFrame = new SpriteFrame();
                }
                let texture = new Texture2D();
                this._resetTexture(texture, this.width, this.height);
                sprite.spriteFrame.texture = texture;
            }
        }

        this._resetTexture(this._texture0, this.width, this.height);
        log("size", this.width, this.height)
        /* this.midVideoSp.node.getComponent(UITransform).width = this.width;
        this.midVideoSp.node.getComponent(UITransform).height = this.height; */
        for (let i = 0; i < renders.length; i++) {
            const render = renders[i];
            const material = render?.material;
            let flag = 0; // 中间视窗
            /* if (i === 0) { // 左边视窗
                flag = 1;
            } else if (i === 1) { // 右边视窗
                flag = -1;
            } */
            material?.setProperty('texture0', this._texture0);
            material?.setProperty('transFlag', flag);
        }

        //  log('_initRenderTexture: pixelFormat=', this._pixelFormat);
    }

    // 初始化材质
    private _initRenderMaterial(renders: RenderableComponent[]): void {
        const pixelFormat = JSB ? this._video.pixelFormat() : PixelFormat.RGB;
        if (this._pixelFormat == pixelFormat) {
            return;
        }

        for (let i = 0; i < renders.length; i++) {
            const render = renders[i];
            log(`_initRenderMaterial: i=${i}, pixelFormat=${pixelFormat}`);

            switch (pixelFormat) {
                case PixelFormat.RGB: // web默认
                    render.setSharedMaterial(this.rgbMat, 0);
                    break;
            }
        }

        this._pixelFormat = pixelFormat;
    }

    /**
     * 重置贴图状态
     * @param texture 贴图
     * @param width 宽
     * @param height 高
     */
    private _resetTexture(texture: Texture2D, width: number, height: number, format?: number) {
        texture.setFilters(Texture2D.Filter.LINEAR, Texture2D.Filter.LINEAR);
        texture.setMipFilter(Texture2D.Filter.LINEAR);
        texture.setWrapMode(Texture2D.WrapMode.CLAMP_TO_EDGE, Texture2D.WrapMode.CLAMP_TO_EDGE);

        texture.reset({
            width: width,
            height: height,
            //@ts-ignore
            format: format ? format : JSB ? gfx.Format.R8 : gfx.Format.RGB8
        });
    }

    private _onMetaLoaded() {
        this.node.emit('loaded', this);
    }

    private _onReadyToPlay() {
        log('_onReadyToPlay');
        this._initRenderMaterial(this._videoSprites);
        this._currentState = VideoState.PREPARED;
        if (this._seekTime > 0.1) {
            this.currentTime = this._seekTime;
        }
        this._initRenderTexture(this._videoSprites);
        this.node.emit('ready', this);
        /* this._targetState == VideoState.PLAYING &&  */this.play();
        this.midVideoSp.getComponent(UITransform).width = view.getVisibleSize().width;
        this.midVideoSp.getComponent(UITransform).height = view.getVisibleSize().height;
        this._cb && this._cb();
    }

    private _onCompleted() {
        log("_onCompleted", this.loop)
        if (this.loop) {
            if (this._currentState == VideoState.PLAYING) {
                this.currentTime = 0;
                this._video.play();
            }
        } else {
            this._currentState = VideoState.COMPLETED;
            this._targetState = VideoState.COMPLETED;
            this.node.emit('completed', this);
            this._cb2 && this._cb2()
        }
    }

    /**
     * 播放视频
     */
    public play() {
        if (this._isInPlaybackState()) {
            if (this._currentState == VideoState.COMPLETED) {
                this.currentTime = 0;
            }
            if (this._currentState != VideoState.PLAYING) {
                if (this._volume !== -1) {
                    this.setVolume(this._volume);
                    this._volume = -1;
                }
                this._video.play();
                this.node.emit('playing', this);
                this._currentState = VideoState.PLAYING;
                this._targetState = VideoState.PLAYING;
            }
        } else {
            this._targetState = VideoState.PLAYING;
        }
    }

    /**
     * 恢复视频
     */
    public resume() {
        if (this._isInPlaybackState() && this._currentState != VideoState.PLAYING) {
            if (JSB) {
                this._video.resume();
            } else {
                this._video.play();
            }
            this.node.emit('playing', this);
            this._currentState = VideoState.PLAYING;
            this._targetState = VideoState.PLAYING;
        } else {
            this._targetState = VideoState.PLAYING;
        }
    }

    /**
     * 暂停视频
     */
    public pause() {
        if (this._isInPlaybackState() && this._currentState != VideoState.PAUSED) {
            this._video.pause();
            this.node.emit('paused', this);
            this._currentState = VideoState.PAUSED;
            this._targetState = VideoState.PAUSED;
        } else {
            this._targetState = VideoState.PAUSED;
        }
    }

    /**
     * 停止视频
     */
    public stop() {
        this._seekTime = 0;
        if (this._isInPlaybackState() && this._currentState != VideoState.STOP) {
            this._video.pause();
            this._video.currentTime = 0;

            this.node.emit('stopped', this);
            this._currentState = VideoState.STOP;
            this._targetState = VideoState.STOP;
        } else {
            this._targetState = VideoState.STOP;
        }
    }

    /**
     * 设置音量
     * @param volume 音量 0-1
     * @returns 
     */
    public setVolume(volume) {
        if (!this._isInPlaybackState()) {
            this._volume = volume;
            return;
        }
        if (JSB) {
            this._video.setVolume(volume);
        } else {
            this._video.volume = volume;
        }
    }

    public clear() {

    }

    /**
     * 播放状态
     * @returns 播放状态
     */
    public isPlaying() {
        return this._currentState == VideoState.PLAYING || this._targetState == VideoState.PLAYING;
    }

    private _isInPlaybackState() {
        return !!this._video && this._currentState != VideoState.IDLE && this._currentState != VideoState.PREPARING && this._currentState != VideoState.ERROR;
    }
}

