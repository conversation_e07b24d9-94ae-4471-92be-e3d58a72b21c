import { sys } from "cc";
import { EventManager } from "../manager/EventManager";

// register：定时器开关注册，用于切到后台后关闭延迟存储定时器
// getAllLocalData：游戏开始时获取所有本地存储的数据
// setLocalItemDefer：延迟存储接口
// setLocalItemImmediately：立即存储接口
// getLocalItem：获取本地存储数据
// getGameDataItem：获取游戏内存数据

export class StorageUtil {
    private static gameName = "caishen";
    private static pureDataCache = {};  // 只包含成员变量，不包含成员函数
    private static gameDataRef = {};  // 游戏数据引用
    private static keyMap = {}; // 需存储的已改变的数据
    private static intervalId = null;
    private static syncLocalDataInterval = 500; // 数据存储同步间隔(单位毫秒)

    static register() {
        EventManager.getInstance().addEventListener('EVENT_HIDE', (event) => {

            if (this.intervalId) {
                clearTimeout(this.intervalId);
                this.intervalId = null;
            }
        });
        EventManager.getInstance().addEventListener('EVENT_SHOW', (event) => {

        });
    }

    static getAllLocalData(gameData: object, callback: Function) {
        let firstLoginKey = this.gameName + "_firstLogin";
        gameData[firstLoginKey] = false;
        for (let key in gameData) {
            if (typeof gameData[key] === "object") {
                this.pureDataCache[key] = { ...gameData[key] };
            }
            else {
                this.pureDataCache[key] = gameData[key];
            }
            this.gameDataRef[key] = gameData[key];
        }
        //console.log('pureDataCache: ', this.pureDataCache);
        let isFirstLogin = this.getLocalItem(firstLoginKey, true);
        if (!isFirstLogin) {
            for (let key in this.pureDataCache) {
                let value = this.pureDataCache[key];
                if (value && typeof value === "object") {
                    for (let childKey in value) {

                        let localValue = this.getLocalItem(key + childKey, value[childKey]);
                        this.pureDataCache[key][childKey] = localValue;
                        gameData[key][childKey] = localValue;

                        if (value[childKey] == localValue) {
                            if (!sys.localStorage.getItem(key + childKey)) {
                                this.setLocalItemDefer(key, { [childKey]: localValue })
                            }

                        }


                        console.log('childKey:', childKey, value[childKey], localValue,)
                    }
                }
                else {

                    let localValue = this.getLocalItem(key, value);
                    this.pureDataCache[key] = localValue;
                    gameData[key] = localValue;
                }
            }


        }
        else {

            for (let key in this.pureDataCache) {
                let value = this.pureDataCache[key];
                if (value && typeof value === "object") {
                    for (let childKey in value) {

                        this.setLocalItemImmediately(key + childKey, value[childKey]);
                    }
                }
                else {
                    this.setLocalItemImmediately(key, value);
                }
            }
        }
        callback && callback(isFirstLogin);
    }

    static setLocalItemDefer(key, value) {
        // 过滤掉函数字段并断开引用关系
        let cloneValue = { ...value };
        this.pushChangedKey(key, cloneValue);
        this.pureDataCache[key] = cloneValue;
        console.log('cloneValue', cloneValue)
    }

    static setLocalItemImmediately(key, value) {
        this._setData(key, value);
    }

    static getLocalItem(key, defaultValue?): any {
        let value = this._getData(key, defaultValue);
        if (typeof defaultValue == 'boolean') {
            value = this._toBoolean(value, defaultValue);
        }
        else if (typeof defaultValue == 'number') {
            value = this._toNumber(value, defaultValue);
        }
        else if (typeof defaultValue == 'object') {
            value = this._toJSON(value, defaultValue);
        }
        return value;
    }

    static getGameDataItem(key) {
        return this.gameDataRef[key];
    }

    private static pushChangedKey(key, value) {
        if (typeof value === "object") {
            for (let subKey in value) {
                let subValue = value[subKey];

                console.log("pushChangedKey", subValue)
                if (this.pureDataCache[key]) {

                    console.log("pushChangedKey2", JSON.stringify(subValue), JSON.stringify(this.pureDataCache[key][subKey]))
                    if (JSON.stringify(this.pureDataCache[key][subKey]) !== JSON.stringify(subValue)) {
                        this.keyMap[key + subKey] = { "key": key, "subKey": subKey };
                        this._syncLocalDataInterval();
                    }
                }
                else {
                    this.keyMap[key + subKey] = { "key": key, "subKey": subKey };
                    this._syncLocalDataInterval();
                }
            }
        }
        else {
            console.log("pushChangedKeys", JSON.stringify(value), JSON.stringify(this.pureDataCache[key]));
            if (JSON.stringify(this.pureDataCache[key]) !== JSON.stringify(value)) {
                this.keyMap[key] = { "key": key, "subKey": null };
                this._syncLocalDataInterval();
            }
        }
    }

    private static _syncLocalDataInterval() {

        console.log("_syncLocalDataInterval")
        if (!this.intervalId) {
            this.intervalId = setTimeout(() => {
                this.intervalId = null;
                this._syncLocalData();
            }, this.syncLocalDataInterval);
        }
    }

    private static _syncLocalData() {
        for (let uniKey in this.keyMap) {
            let keysObj = this.keyMap[uniKey];
            let key = keysObj["key"];
            let subKey = keysObj["subKey"];
            //console.log(keysObj, key, subKey, this.gameDataRef[key])
            if (!subKey) {
                this._setData(uniKey, this.pureDataCache[key]);
            }
            else {
                this._setData(uniKey, this.pureDataCache[key][subKey]);
            }
        }
        this.keyMap = {};
    }

    private static _setData(key, value) {
        if (typeof value === "object") {
            value = JSON.stringify(value);
        }
        sys.localStorage.setItem(key, value);
    }

    private static _getData(key, defaultValue) {
        let ret = sys.localStorage.getItem(key);
        if ((ret == null || ret == "null") && defaultValue != null) {
            ret = defaultValue;
        }
        return ret;
    }

    private static _toBoolean(src, def) {
        if (typeof src == 'boolean') {
            return src;
        }
        else if (src == null || src == "") {
            return def;
        }
        else if (src == "false") {
            return false;
        }
        else if (src == "true") {
            return true;
        }
    }

    private static _toNumber(src, def) {
        let ret = Number(src);
        if (isNaN(ret)) {
            return def;
        }
        else {
            return ret;
        }
    }

    private static _toJSON(src, def) {
        try {
            let ret = JSON.parse(src);
            if (typeof ret == 'object' && ret) {
                return ret;
            }
            else {
                return def;
            }
        }
        catch (e) {
            return def;
        }
    }
}