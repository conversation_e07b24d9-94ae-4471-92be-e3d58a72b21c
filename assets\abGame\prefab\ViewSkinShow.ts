import { _decorator, Button, Color, Component, Label, log, Node } from 'cc';
import { ViewBase } from '../../scripts/libs/manager/ViewBase';
import BaseListCtr from '../../scripts/components/BaseLlistCtr';
import { ConfigHelper } from '../../scripts/config/ConfigHelper';
import { FightMgr } from '../scripts/FightMgr';
const { ccclass, property } = _decorator;

@ccclass('ViewSkinShow')
export class ViewSkinShow extends ViewBase {

    //皮肤展示列表
    @property(BaseListCtr)
    private listShow: BaseListCtr = null;

    //当前用户皮肤切换列表
    @property(BaseListCtr)
    private listPlayer: BaseListCtr = null;

    @property(Node)
    private ndEmpty: Node = null;

    @property(Button)
    private btnShow: Button = null;

    @property(Label)
    private lbBtnShowTxt: Label = null;

    @property(Node)
    private ndShowOn: Node = null;

    @property(Button)
    private btnUserSkin: Button = null;

    @property(Label)
    private lbBtnUserSkinTxt: Label = null;


    @property(Node)
    private ndUserSkinOn: Node = null;


    private _seletIndex: number = null;
    private _configs: any[] = []
    private _colorOn: Color = new Color(210, 210, 200, 255)
    private _colorOff: Color = new Color(175, 60, 60, 255)



    protected onLoadCompleted(): void {
        this._configs = ConfigHelper.getInstance().getSkinConfgs();
        this._configs.sort((a, b) => b.sort - a.sort);
        this.onRefreshList(1);
        this.btnShow.node.on('click', this.onRefreshList.bind(this, 1), this);
        this.btnUserSkin.node.on('click', this.onRefreshList.bind(this, 2), this);
    }


    onRefreshList(index: number) {

        if (this._seletIndex == index) return
        this._seletIndex = index;
        this.listShow.node.active = this._seletIndex == 1;
        this.listPlayer.node.active = this._seletIndex == 2;
        this.ndShowOn.active = this._seletIndex == 1;
        this.ndUserSkinOn.active = this._seletIndex == 2;
        this.lbBtnShowTxt.color = this._seletIndex == 1 ? this._colorOn : this._colorOff;
        this.lbBtnUserSkinTxt.color = this._seletIndex == 1 ? this._colorOff : this._colorOn;
        this.ndEmpty.active = false;
        if (this._seletIndex == 1) {
            this.listShow.refreshListData(this._configs);
            this.listShow.sv.scrollToTop(0.1);
        }
        else if (this._seletIndex == 2) {

            let users = FightMgr.getInstance().getUsers();
            this.ndEmpty.active = users.length <= 0;
            this.listPlayer.refreshListData(users);
            this.listPlayer.sv.scrollToTop(0.1);

        }

    }

    start() {

    }

    update(deltaTime: number) {

    }
}


