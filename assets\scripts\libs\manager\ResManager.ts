import { Asset, ImageAsset, JsonAsset, Node, Prefab, Size, Sprite, SpriteAtlas, SpriteFrame, TextAsset, Texture2D, UITransform, VideoClip, assert, assetManager, log, rect, size, sp, v2 } from "cc";
import ResLoader from "./ResLoader";

import { Singleton } from "../utils/Singleton";


/**
 * 缓存管理器
 */
export class ResManager extends Singleton {

    private cacheAssetList: any[] = [];


    /**缓存图集 */
    private cacheAtlas: Map<string, SpriteAtlas> = new Map();
    private cacheMonsterAtalsNames: string[] = []
    private cacheImg2sfr: { [name: string]: SpriteFrame } = {};
    /**
     * 缓存一个资源
     * @param item 资源的item对象
     */
    public cacheAsset(item: Asset, addRef: boolean = true) {

        if (item) {
            if (item instanceof Asset) {
                addRef && item.addRef();
                // 
                if (this.cacheAssetList.filter(e => e.uuid == item.uuid).length <= 0) {
                    this.cacheAssetList.push(item);
                }
            } else {
                // 原生资源、html元素有可能走到这里，原生资源都是有对应的 Asset对应引用的，所以这里可以不处理
                console.log(`cacheItem ${item} is not  Asset`);
            }
        } else {
            console.warn(`cacheItem error, item is ${item}`);
        }
    }


    public releaseAssetList(assetList: Asset[]) {
        for (let i = 0; i < assetList.length; i++) {
            let item = assetList[i];
            //log('releaseAssetList', item)
            if (item instanceof Asset) {
                this.releaseAsset(item);
            }
        }
        assetList = null;
    }
    /**
    * 释放一个资源
    * @param item 资源的item对象
    */
    public releaseAsset(item: Asset) {
        return
        if (item) {
            item.decRef && item.decRef();

            for (let i = 0; i < this.cacheAssetList.length; i++) {
                if (item.uuid == this.cacheAssetList[i].uuid) {
                    if (item.refCount <= 0) {
                        this.cacheAssetList.splice(i, 1);
                        item = null;


                    }
                    break
                }
            }

            if (item && item.refCount <= 0) {
                item = null;
            }
        } else {
            console.warn(`releaseAsset error,  ${item}`);
        }
    }
    /**
        * 释放spine资源
        * @param ndSpine spine所在节点
        * 
        * @returns 
        */
    public releaseSpineAsset(ndSpine: Node) {
        let skeleton = ndSpine.getComponent(sp.Skeleton);
        if (!skeleton || !skeleton.skeletonData || !skeleton.skeletonData[`_depens`]) { return };
        skeleton.skeletonData.decRef();
        this.releaseAssetList(skeleton.skeletonData[`_depens`]);
        skeleton.skeletonData = null;
        ndSpine.destroy();
        ndSpine = null;
    }



    /************************************************************ 加载 ************************************************/

    /**
     * 动态加载预制体
     * @param bundleName 分包名称
     * @param path 预制体路径
     * @returns 
     */
    public async bundleLoadPrefab(bundleName: string, path: string): Promise<Prefab> {

        let res;
        try {
            if (!bundleName || bundleName === 'default' || bundleName === 'resources') {
                res = await ResLoader.load(path, Prefab) as Prefab;
            } else {
                res = await ResLoader.load(bundleName, path, Prefab) as Prefab;
            }
            this.cacheAsset(res);
            return res;
        }
        catch (err) {
            log('load prefab fail!', err);
        }
    }
    /**
     * 
     * @param bundleName 分包名称
     * @param path 图片路径
     * @param Sp 目标精灵
     */
    public async bundleLoadSprite(bundleName: string, path: string, Sp?: Sprite, lenOrSize?: number | Size) {
        let imageAsset;
        //let oldRes = Sp?.spriteFrame;
        if (!bundleName) {
            imageAsset = await ResLoader.load(path, ImageAsset);
        } else {
            imageAsset = await ResLoader.load(bundleName, path, ImageAsset);
        }
        let sf = this.cacheImg2sfr[imageAsset.nativeUrl];
        if (!sf || !sf.isValid) {
            sf = new SpriteFrame();
            const texture = new Texture2D();
            texture.image = imageAsset;
            sf.texture = texture;
            // sf[`_resAsset`] = imageAsset;
            this.cacheImg2sfr[imageAsset.nativeUrl] = sf
        }

        if (Sp && Sp.node && Sp.node.isValid) {
            Sp.spriteFrame = sf
        }
        //缓存纹理计数+1
        this.cacheAsset(imageAsset);
        //老纹理计数-1
        /* if (oldRes && oldRes[`_resAsset`]) {
            this.releaseAsset(oldRes[`_resAsset`]);
        } */
        if (Sp && Sp.node && Sp.node.isValid && lenOrSize) {

            this.resizeSprite(Sp, lenOrSize)
        }
        return sf;
    }

    public async bundleLoadAtlas(bundleName: string, path: string): Promise<SpriteAtlas> {
        let assetPlist;

        if (!bundleName) {
            assetPlist = await ResLoader.load(path, SpriteAtlas);
        } else {
            assetPlist = await ResLoader.load(bundleName, path, SpriteAtlas);
        }

        return assetPlist;

    }

    public async bundleLoadSpine(bundleName: string, path: string, skeleton: sp.Skeleton) {
        let res;
        if (!bundleName) {
            res = await ResLoader.load(path, sp.SkeletonData);
        } else {
            res = await ResLoader.load(bundleName, path, sp.SkeletonData);
        }

        // let oldRes = skeleton.skeletonData;
        if (skeleton && skeleton.node && skeleton.node.isValid) {
            skeleton.skeletonData = res;
            // @ts-ignore
            skeleton._updateSkeletonData();

            this.cacheAsset(res);
            /* if (oldRes) {
                await ResManager.substituteReleaseAsset(oldRes, res);
            } */
            return res
        }

    }

    async bundleLoadClip(bundleName: string, path: string) {
        let res;
        if (!bundleName) {
            res = await ResLoader.load(path, VideoClip);
        } else {
            res = await ResLoader.load(bundleName, path, VideoClip);
        }
        return res
    }

    /************************************* 远程加载资源 *****************************************/
    //远程加载图片
    /**
     * 
     * @param path 图片url
     * @param Sp  精灵节点
     * @param lenOrSize 精灵尺寸
     * @returns 
     */
    public async remoteLoadSprite(path: string, Sp?: Sprite, lenOrSize?: number | Size) {
        if (!path) {
            log('spr path cannot be null')
            return
        }
        //let oldRes = Sp?.spriteFrame;
        let imageAsset = await ResLoader.load(path, ImageAsset) as ImageAsset;
        let sf = this.cacheImg2sfr[imageAsset.nativeUrl];
        if (!sf || !sf.isValid) {
            sf = new SpriteFrame();
            const texture = new Texture2D();
            texture.image = imageAsset;
            sf.texture = texture;
            // sf[`_resAsset`] = imageAsset;
            this.cacheImg2sfr[imageAsset.nativeUrl] = sf
        }

        if (Sp && Sp.node && Sp.node.isValid) {
            Sp.spriteFrame = sf
        }
        //缓存纹理计数+1
        this.cacheAsset(imageAsset);
        //老纹理计数-1
        /*  if (oldRes && oldRes[`_resAsset`]) {
             this.releaseAsset(oldRes[`_resAsset`]);
         } */
        if (Sp && Sp.node && Sp.node.isValid && lenOrSize) {

            this.resizeSprite(Sp, lenOrSize)
        }

        return sf;
    }

    private resizeSprite(Sp: Sprite, lenOrSize: Size | number) {
        if (Sp && lenOrSize) {
            var sliceRect = Sp.spriteFrame.rect;
            if (lenOrSize instanceof Size) {
                var size = lenOrSize; // 不能直接设置scale，会导致layout布局占用空间

                var scale = Math.min(size.width / sliceRect.width, size.height / sliceRect.height);
                Sp.node.getComponent(UITransform).width = sliceRect.width * scale;
                Sp.node.getComponent(UITransform).height = sliceRect.height * scale;
            } else if (typeof lenOrSize === 'number') {
                var len = lenOrSize; // 不能直接设置scale，会导致layout布局占用空间

                var scale = Math.min(len / sliceRect.width, len / sliceRect.height);
                Sp.node.getComponent(UITransform).width = sliceRect.width * scale;
                Sp.node.getComponent(UITransform).height = sliceRect.height * scale;
            }
        }
    }


    //https://gz-sass-1258783731.cos.ap-guangzhou.myqcloud.com/SAAS/new/lgame/tiao_yi_tiao/theme01/dev/cocos/anim/dog.atlas
    //https://gz-sass-1258783731.cos.ap-guangzhou.myqcloud.com/SAAS/new/lgame/tiao_yi_tiao/theme01/dev/cocos/anim/dog.json
    /** 加载远程spine资源 eg. xysz.tool.loadRemoteSpine('https://www.xysz-res.com/', 'qe') */
    public async remoteLoadSpine(url: string, name: string, skeleton: sp.Skeleton): Promise<sp.SkeletonData> {
        if (!url.endsWith('/')) {
            url += '/'
        }

        try {
            const assetJson = await ResLoader.load(`${url}${name}.json`, JsonAsset);
            const assetAtlas = await ResLoader.load(`${url}${name}.atlas`, TextAsset);
            const imgNames = [];
            const textures = [];
            const lines = assetAtlas.text.split('\n');
            for (let i = 0; i < lines.length; i++) {
                const imgFile = lines[i].trim();
                if (imgFile.endsWith('.png')) {
                    imgNames.push(imgFile);
                    let texture = new Texture2D();
                    const img = await ResLoader.load(`${url}${imgFile}`, ImageAsset);
                    texture.image = img;
                    this.cacheAsset(texture);
                    textures.push(texture);
                }
            }
            const spData = new sp.SkeletonData()
            spData.skeletonJson = assetJson.json
            spData.atlasText = assetAtlas.text
            spData.textures = textures
            // @ts-ignore
            spData.textureNames = imgNames

            spData[`_depens`] = [assetJson, assetAtlas].concat(textures);


            skeleton.skeletonData = spData;
            // @ts-ignore
            skeleton._updateSkeletonData();

            this.cacheAsset(assetJson);
            this.cacheAsset(assetAtlas);

            return spData
        } catch (error) {
            return Promise.reject(error)
        }
    }


    /** 加载远程图集资源 eg. 
     * plistUrl 以plist为后缀
     *'https://gz-sass-1258783731.cos.ap-guangzhou.myqcloud.com/SAAS/new/placePage/jin_bi_xiao_zhen/theme01/dev/cocos/ui.plist';
     */
    public async remoteLoadAtlas(plistUrl: string): Promise<SpriteAtlas> {
        if (!plistUrl.endsWith('.plist')) {
            console.warn('请传入以.plist结束的地址')
            return
        }

        try {
            const assetPlist = await ResLoader.load(plistUrl, SpriteAtlas);
            const arr = plistUrl.split('/');
            const name = arr[arr.length - 1]?.split('.')[0]
            arr[arr.length - 1] = assetPlist._nativeAsset.metadata.textureFileName
            const pngUrl = arr.join('/');
            const imageAsset = await ResLoader.load(pngUrl, ImageAsset);
            const assetTexture = new Texture2D();
            assetTexture.image = imageAsset;
            let atlas = new SpriteAtlas();
            for (let key in assetPlist._nativeAsset.frames) {
                const item = assetPlist._nativeAsset.frames[key]
                const offsets = item.spriteOffset.replace(/{|}/g, '').split(',').map(e => parseInt(e))
                const sourceSizes = item.spriteSourceSize.replace(/{|}/g, '').split(',').map(e => parseInt(e))
                const rects = item.textureRect.replace(/{|}/g, '').split(',').map(e => parseInt(e))
                const sfName = key.substring(0, key.length - 4)
                let sf = new SpriteFrame();
                sf.texture = assetTexture;
                sf.rect = rect(rects[0], rects[1], rects[2], rects[3]);
                sf.offset = v2(offsets[0], offsets[1]);
                sf.originalSize = size(sourceSizes[0], sourceSizes[1]);
                atlas.spriteFrames[sfName] = sf;
            }
            atlas[`_depens`] = [assetPlist, imageAsset];
            //纹理缓存计数
            this.releaseAssetList(atlas[`_depens`]);
            atlas.name = name
            return atlas
        } catch (error) {
            return Promise.reject(error)
        }
    }

    public async remoteLoadJson(url: string): Promise<JSON> {
        try {
            const assetJson = await ResLoader.load(`${url}`, JsonAsset);
            return assetJson.json;
        } catch (error) {
            return Promise.reject(error)
        }
    }

    public async remoteLoadAny(url: string, onComplete?: Function): Promise<any> {
        try {

            const asset = await assetManager.loadRemote(url, onComplete);
            return asset;
        } catch (error) {
            return Promise.reject(error)
        }
    }



    getAtlas(name: string) {

        let atlas = this.cacheAtlas.get(name);
        if (!atlas) {
            this.cacheAtlas.set(name, new SpriteAtlas());
            atlas = this.cacheAtlas.get(name);
            atlas.addRef();
            if (!this.cacheMonsterAtalsNames) {
                this.cacheMonsterAtalsNames = []
            }
            this.cacheMonsterAtalsNames.push(name);
            log('生成图集', name, '总数》', this.cacheAtlas.size);


        }
        return atlas;
    }
    addAtlasSprite(atlasName: string, sfName: string, spr: SpriteFrame) {
        let atlas = this.getAtlas(atlasName);
        atlas.spriteFrames[sfName] = spr;

    }


    clearCache() {
        /**缓存图集 */
        this.cacheAtlas.clear();
        this.cacheMonsterAtalsNames = []
        this.cacheImg2sfr = {};
    }
}