/**
 * <AUTHOR> / http://mrdoob.com/
 */

export default class EventDispatcher {
	private _listeners = {}
	once(type, listener, target?: any, args?: any[]) {
		this.on(type, listener, target, args, 'once')
	}
	on(type, listener, target?: any, args?: any[], times?: 'once' | 'more') {
		if (this._listeners === undefined) this._listeners = {}

		const listeners = this._listeners

		if (listeners[type] === undefined) {
			listeners[type] = []
		}

		if (
			listeners[type].findIndex(
				v => v.listener === listener && (target ? v.target === target : true)
			) === -1
		) {
			listeners[type].push({
				emitTimes: times || 'more',
				listener,
				target,
				args: args || []
			})
		}
	}

	hasEventListener(type, listener, target?: any) {
		if (this._listeners === undefined) return false

		const listeners = this._listeners

		return (
			listeners[type] !== undefined &&
			listeners[type].findIndex(
				v => v.listener === listener && (target ? v.target === target : true)
			) !== -1
		)
	}

	off(type, listener, target?: any) {
		if (this._listeners === undefined) return

		const listeners = this._listeners
		const listenerArray = listeners[type]

		if (listenerArray !== undefined) {
			const index = listenerArray.findIndex(
				v => v.listener === listener && (target ? v.target === target : true)
			)

			if (index !== -1) {
				listenerArray.splice(index, 1)
			}
		}
	}

	emit(type: string, args?: any[]) {
		if (this._listeners === undefined) return

		const listeners = this._listeners
		const listenerArray = listeners[type]

		if (listenerArray !== undefined) {
			// Make a copy, in case listeners are removed while iterating.
			const array = listenerArray.slice(0)
			const arr2 = []

			for (let i = 0, l = array.length; i < l; i++) {
				array[i].listener.apply(array[i].target || this, [
					...array[i].args,
					...(args || [])
				])
				if (array[i].emitTimes === 'once') {
					arr2.push(array[i])
				}
			}

			// 针对只监听一次
			for (let i = 0, l = arr2.length; i < l; i++) {
				const { listener, target } = arr2[i]
				console.log('once' + type)
				this.off(type, listener, target)
			}
		}
	}
}
