[1, ["cf9C91cjFEhrYSOFfeFfF7@f9941", "beaqBeophDaobOfHx7uNz9@f9941", "9bnrdLPm1JOboGNesLXsaQ@f9941", "52fHu7D8hGm5vLaoALoXCl", "20g1ukYUVPvKWKBRznAKo+@f9941", "54TknWPwVPqJqeCR+Y/Czo@f9941", "95EkngnxZFbYuFpsqVTaFr@f9941"], ["node", "_spriteFrame", "_font", "_normalSprite", "_hoverSprite", "_pressedSprite", "_disabledSprite", "_cameraComponent", "btnLogin", "scene", "_parent"], [["cc.Node", ["_name", "_layer", "_id", "_obj<PERSON><PERSON>s", "_components", "_lpos", "_parent", "_children"], -1, 9, 5, 1, 2], ["cc.Sprite", ["_sizeMode", "_type", "node", "_spriteFrame"], 1, 1, 6], ["cc.SceneAsset", ["_name"], 2], ["cc.Scene", ["_name", "_children", "_prefab", "_globals"], 2, 2, 4, 4], ["cc.Node", ["_name", "_id", "_parent", "_components", "_lpos"], 1, 1, 2, 5], ["cc.Node", ["_name", "_layer", "_parent", "_children", "_components", "_lpos"], 1, 1, 2, 12, 5], ["cc.Camera", ["_projection", "_orthoHeight", "_near", "_visibility", "node", "_color"], -1, 1, 5], ["cc.UITransform", ["node", "_contentSize"], 3, 1, 5], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_lineHeight", "_overflow", "_enableWrapText", "_isSystemFontUsed", "_enableOutline", "_outlineWidth", "node", "_outlineColor", "_font"], -6, 1, 5, 6], ["cc.<PERSON><PERSON>", ["_transition", "node", "_normalColor", "_target"], 2, 1, 5, 1], ["cc.<PERSON>", ["node", "_cameraComponent"], 3, 1, 1], ["cc.Widget", ["_alignFlags", "_top", "_bottom", "node"], 0, 1], ["55676oUpURLcJ1cSCh8g9Rt", ["node", "btnLogin"], 3, 1, 1], ["cc.PrefabInfo", ["root", "asset", "fileId", "instance", "nestedPrefabInstanceRoots", "targetOverrides"], -3], ["cc.SceneGlobals", ["ambient", "shadows", "_skybox", "fog", "octree", "skin", "lightProbeInfo", "postSettings"], 3, 4, 4, 4, 4, 4, 4, 4, 4], ["cc.AmbientInfo", ["_skyColorHDR", "_groundAlbedoHDR"], 3, 5, 5], ["cc.ShadowsInfo", ["_shadowColor", "_size"], 3, 5, 5], ["cc.SkyboxInfo", [], 3], ["cc.FogInfo", [], 3], ["cc.OctreeInfo", [], 3], ["cc.SkinInfo", ["_enabled"], 2], ["cc.LightProbeInfo", [], 3], ["cc.PostSettingsInfo", [], 3]], [[7, 0, 1, 1], [2, 0, 2], [3, 0, 1, 2, 3, 2], [4, 0, 1, 2, 3, 4, 3], [0, 0, 1, 2, 7, 4, 5, 4], [0, 0, 1, 6, 4, 3], [0, 0, 1, 6, 4, 5, 3], [0, 0, 3, 1, 6, 4, 5, 4], [5, 0, 1, 2, 3, 4, 5, 3], [6, 0, 1, 2, 3, 4, 5, 5], [1, 2, 3, 1], [1, 0, 2, 3, 2], [1, 1, 2, 3, 2], [8, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 10], [9, 0, 1, 2, 3, 2], [10, 0, 1, 1], [11, 0, 1, 2, 3, 4], [12, 0, 1, 1], [13, 0, 1, 2, 3, 4, 5, 7], [14, 0, 1, 2, 3, 4, 5, 6, 7, 1], [15, 0, 1, 1], [16, 0, 1, 1], [17, 1], [18, 1], [19, 1], [20, 0, 2], [21, 1], [22, 1]], [[1, "Ready"], [4, "<PERSON><PERSON>", 33554432, "beI88Z2HpFELqR4T5EMHpg", [-7, -8, -9], [[0, -1, [5, 1080, 1920]], [15, -3, -2], [16, 45, 5.684341886080802e-14, 5.684341886080802e-14, -4], [17, -6, -5]], [1, 540, 960, 0]], [8, "btnLogin", 33554432, 1, [-13], [[[0, -10, [5, 349, 93]], [12, 1, -11, 3], -12], 4, 4, 1], [1, 0, -552.85, 0]], [2, "Ready", [-14, 1], [18, null, null, "417fe061-e810-45dc-9324-1ffb7ac06b32", null, null, []], [19, [20, [2, 0, 0, 0, 0.520833125], [2, 0, 0, 0, 0]], [21, [4, 4283190348], [0, 512, 512]], [22], [23], [24], [25, false], [26], [27]]], [5, "ready_loading_bg", 33554432, 1, [[0, -15, [5, 1080, 1920]], [10, -16, 0]]], [6, "spr<PERSON><PERSON>", 33554432, 1, [[0, -17, [5, 669, 220]], [11, 0, -18, 1]], [1, 0, 520, 0]], [7, "Label", 512, 33554432, 2, [[0, -19, [5, 100, 60]], [13, "登录", 50, 50, 49.9, 1, false, false, true, 3, -20, [4, 4280887593], 2]], [1, 0, 3.653, 0]], [3, "Camera", "ebFwiq8gBFaYpqYbdoDODe", 3, [-21], [1, 540, 960, 1000]], [9, 0, 960, 0, 1108344832, 7, [4, 4278190080]], [14, 3, 2, [4, 4292269782], 2]], 0, [0, 0, 1, 0, 7, 8, 0, 0, 1, 0, 0, 1, 0, 8, 9, 0, 0, 1, 0, -1, 4, 0, -2, 5, 0, -3, 2, 0, 0, 2, 0, 0, 2, 0, -3, 9, 0, -1, 6, 0, -1, 7, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, -1, 8, 0, 9, 3, 1, 10, 3, 21], [0, 0, 0, 0, 9, 9, 9, 9], [1, 1, 2, 1, 3, 4, 5, 6], [1, 2, 3, 0, 0, 4, 5, 6]]