/*滚动视图*/
.<PERSON><PERSON><PERSON>iew {
    display: flex;
    position: absolute;
    height: 100%;
    width: 100%;
}

/*背景*/
.SVcontent {
    width: 100%;
    height: 100%;
    background-color: rgb(107, 107, 107);
    overflow: auto;
}

/* 修改滚动条样式 */
.SVcontent::-webkit-scrollbar {
    height: 0%;
    width: 20px;
    background-color: #000000;
}

/* 修改滚动条中的小滑块 */
.SVcontent::-webkit-scrollbar-thumb {
    width: 10px;
    border-radius: 5px;
    background-color: rgb(255, 136, 0);
}

/*定义滚动条的轨道*/
.SVcontent::-webkit-scrollbar-track {
    background-color: rgb(90, 250, 255);
}

/* 定义轨道两端的按钮 */
.SVcontent::-webkit-scrollbar-button {
    background-color: rgb(111, 255, 0);
}

/*给小滑块添加hover事件，鼠标悬浮在滑块上面的样式*/
.SVcontent::-webkit-scrollbar-thumb:hover {
    background-color: rgb(255, 0, 0);
}



























.div_title {
    margin: 5px;
    text-align:center;
    font-size: 20px
}



.div_jcSet {
    margin:5px;
    margin-left: 10px;
    font-size: 15px;
    font-weight: bold;
    color: rgb(255, 94, 0);
}
.div_name {
    margin:5px;
    margin-left: 15px;
}
.div_v {
    margin:5px;
    margin-left: 15px;
}
.div_web_lj {
    margin:5px;
    margin-left: 15px;
    margin-right: 0px;
}
.div_shuchu_lj {
    margin:5px;
    margin-left: 15px;
}



.div_gjSet {
    margin:15px;
    margin-left: 10px;
    font-size: 15px;
    font-weight: bold;
    color: rgb(255, 94, 0);
}
.div_asar {
    margin:5px;
    margin-left: 15px;
}
.div_isNode {
    margin:5px;
    margin-left: 15px;
}
.div_isCover {
    margin:5px;
    margin-left: 15px;
}
.div_app_version {
    margin:5px;
    margin-left: 15px;
}
.div_icon {
    margin:5px;
    margin-left: 15px;
}
.div_jg {
    margin:5px;
    margin-left: 15px;
}
.div_log {
    margin:5px;
    margin-left: 15px;
}
.div_build_cmd_label {
    margin:5px;
    margin-left: 15px;
}
.build_cmd {
    margin:5px;
    margin-left: 15px;
    width: 600px;
}
.div_btn {
    margin:5px;
    margin-left: 10px;
}
.div_buildBtn {
    margin:5px;
    margin-left: 10px;
}



/*把路径变长*/
.web_lj {
    margin:5px;
    margin-left: 15px;
    margin-right: 0px;
    width: 700px;
}
.shuchu_lj {
    margin:5px;
    margin-left: 15px;
    margin-right: 0px;
    width: 700px;
}
.icon {
    margin:5px;
    margin-left: 15px;
    margin-right: 0px;
    width: 700px;
}