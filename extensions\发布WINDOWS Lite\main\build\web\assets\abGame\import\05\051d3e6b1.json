[1, ["52fHu7D8hGm5vLaoALoXCl", "601DNafV9Me5wmcL8bVp83@f9941", "1c2AF3xQFKk7CSX0ZeuZyc@f9941", "9bX03K6LJHe4W/p2H25qsI@f9941", "956052qDBFo58KdfAPH6Ni@f9941", "c4QFjyHkxBQoGsOs+ecoqi@f9941", "5clNBx7a9IIY6cHtiklFjU@f9941", "48kTL9p/ZFJZ9a0TTo/zUG@f9941", "afGTPe5nlMeKDobBbv07FC@f9941", "2fX+sy8XRLA4Txiw/GGT38@6c48a", "48kTL9p/ZFJZ9a0TTo/zUG@6c48a", "d7NxgZbBtCVqw/x9MzgjWj@f9941", "c5EaXFBXlNWaHWzVN6mN8Q@f9941", "6dDNF+0AtCVbK+YGFRGv3q@f9941", "c4xMt1NEZCfqPThRC9VKEo@f9941", "dcimSoLExG5pKQSsp9t72i@f9941", "2cKPKyLkxApI3jpaL+rP7F@f9941", "d69QTgmSVIepLIvzHq4Mmp@f9941", "76lM0t8cNAtqxnUnGbn4uA@f9941", "baPh3sm2ZMKINMmGwpGaxj@f9941", "7dyltG3WBNtLHeX9EVgqeQ@f9941", "c0aehoog5NVpGHG/0kFxxK@f9941", "b0SbH5u6BKuqw24Drp0Ad3@f9941", "fdAt+xSXRJXLPqhYpGU2Kh@f9941", "995tNnaOJGsqqM4EqqmzNl@f9941", "6c8t8zMlRCDLOTl262Ijyp@f9941", "2fX+sy8XRLA4Txiw/GGT38@f9941", "35k5HDRJBGDIF+NsrwWXsD@f9941", "6c8t8zMlRCDLOTl262Ijyp@6c48a", "995tNnaOJGsqqM4EqqmzNl@6c48a", "c5EaXFBXlNWaHWzVN6mN8Q@6c48a", "d7NxgZbBtCVqw/x9MzgjWj@6c48a"], ["node", "_spriteFrame", "_font", "_textureSource", "_parent", "_cameraComponent", "gp", "lbGameNextRoundStart", "ndRoundStart", "btnCrossReward", "btnGameNextRoundStart", "lbBossSub", "lbMonsterSub", "ndBossSub", "ndMonsterSub", "ndBossDetail", "ndMonsterDetail", "ndRoundDetail", "btnExchange", "btnMoveRight", "btnMoveLeft", "lbScore", "sprMonster", "lbMonsterDesc", "spr<PERSON><PERSON>", "lbBossDesc", "lbRound", "lbDesc", "lbGameTime", "ndSpaceHero", "ndSpaceMonster", "lbJoinNum", "lbTime", "lbBossMode", "lbMode", "lbLev", "btnTest", "sprTagOnlineMode", "sprTagAuto", "ndGameSpeedRight", "ndGameSpeedLeft", "lbGameSpeed", "btnGameSpeed", "btnTask", "btnOnlineMode", "btnAutoGameLevel", "btnPowerRank", "btnRank", "btnBack2", "btnBack", "btnSkin", "btnSelectLev", "btnMode2", "btnMode", "btnPlay2", "btnPlay", "btnSetting", "ndContentHurtTips", "ndContentHurtEffect", "ndContentMonsterEffect", "ndContentHeroEffect", "ndContentSkill", "ndContentBossOrSkillTips", "ndContentGiftTips", "ndContentMonster", "ndContentHero", "ndCam", "ndContentTower", "ndUISelectGameType", "ndUISelectMode", "ndUIContent", "ndUITop", "scene"], [["cc.Node", ["_name", "_layer", "_active", "_id", "_components", "_lpos", "_children", "_parent", "_lscale"], -1, 9, 5, 2, 1, 5], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_isSystemFontUsed", "_lineHeight", "_overflow", "_enableOutline", "_outlineWidth", "_isUnderline", "_horizontalAlign", "_verticalAlign", "node", "_color", "_font", "_outlineColor"], -8, 1, 5, 6, 5], ["cc.Node", ["_name", "_layer", "_active", "_parent", "_components", "_lpos", "_children", "_lscale"], 0, 1, 12, 5, 2, 5], "cc.SpriteFrame", ["cc.<PERSON><PERSON>", ["_transition", "_zoomScale", "node", "_target"], 1, 1, 1], ["cc.UITransform", ["node", "_contentSize", "_anchorPoint"], 3, 1, 5, 5], ["cc.Sprite", ["_sizeMode", "_type", "node", "_spriteFrame"], 1, 1, 6], ["cc.Widget", ["_alignFlags", "_top", "_bottom", "node"], 0, 1], ["cc.SceneAsset", ["_name"], 2], ["cc.Node", ["_name", "_layer", "_parent", "_components", "_lpos"], 1, 1, 2, 5], ["cc.<PERSON>", ["node", "_cameraComponent"], 3, 1, 1], ["690a860s+hAeKYpJUeqXIhy", ["node", "ndUITop", "ndUIContent", "ndUISelectMode", "ndUISelectGameType", "ndContentTower", "ndCam", "ndContentHero", "ndContentMonster", "ndContentGiftTips", "ndContentBossOrSkillTips", "ndContentSkill", "ndContentHeroEffect", "ndContentMonsterEffect", "ndContentHurtEffect", "ndContentHurtTips", "btnSetting", "btnPlay", "btnPlay2", "btnMode", "btnMode2", "btnSelectLev", "btnSkin", "btnBack", "btnBack2", "btnRank", "btnPowerRank", "btnAutoGameLevel", "btnOnlineMode", "btnTask", "btnGameSpeed", "lbGameSpeed", "ndGameSpeedLeft", "ndGameSpeedRight", "sprTagAuto", "sprTagOnlineMode", "btnTest", "lbLev", "lbMode", "lbBossMode", "lbTime", "lbJoinNum", "ndSpaceMonster", "ndSpaceHero", "lbGameTime", "lbDesc", "lbRound", "lbBossDesc", "spr<PERSON><PERSON>", "lbMonsterDesc", "sprMonster", "lbScore", "btnMoveLeft", "btnMoveRight", "btnExchange", "ndRoundDetail", "ndMonsterDetail", "ndBossDetail", "ndMonsterSub", "ndBossSub", "lbMonsterSub", "lbBossSub", "btnGameNextRoundStart", "btnCrossReward", "ndRoundStart", "lbGameNextRoundStart", "gp"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["cc.Scene", ["_name", "_children", "_prefab", "_globals"], 2, 2, 4, 4], ["cc.PrefabInfo", ["root", "asset", "fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots"], -3], ["cc.SceneGlobals", ["ambient", "shadows", "_skybox", "fog", "octree", "skin", "lightProbeInfo", "postSettings"], 3, 4, 4, 4, 4, 4, 4, 4, 4], ["cc.AmbientInfo", ["_skyColorHDR", "_groundAlbedoHDR"], 3, 5, 5], ["cc.ShadowsInfo", ["_shadowColor", "_size"], 3, 5, 5], ["cc.SkyboxInfo", [], 3], ["cc.FogInfo", [], 3], ["cc.OctreeInfo", [], 3], ["cc.SkinInfo", ["_enabled"], 2], ["cc.LightProbeInfo", [], 3], ["cc.PostSettingsInfo", [], 3], ["cc.Camera", ["_projection", "_orthoHeight", "_near", "_visibility", "node", "_color"], -1, 1, 5], ["cc.Graphics", ["_lineWidth", "node", "_strokeColor", "_fillColor"], 2, 1, 5, 5]], [[5, 0, 1, 1], [6, 2, 3, 1], [0, 0, 1, 7, 4, 3], [2, 0, 1, 3, 4, 5, 3], [0, 0, 1, 7, 4, 5, 8, 3], [5, 0, 1], [2, 0, 1, 3, 6, 4, 5, 3], [0, 0, 1, 7, 4, 5, 3], [6, 2, 1], [1, 0, 1, 2, 4, 3, 11, 12, 13, 6], [4, 0, 1, 2, 3, 3], [2, 0, 1, 3, 4, 5, 7, 3], [4, 0, 2, 2], [5, 0, 1, 2, 1], [6, 0, 2, 3, 2], [4, 0, 2, 3, 2], [1, 0, 1, 2, 11, 12, 4], [1, 0, 1, 2, 4, 3, 11, 12, 6], [0, 0, 1, 7, 6, 4, 5, 3], [0, 0, 2, 1, 6, 4, 5, 4], [0, 0, 2, 1, 7, 4, 5, 4], [2, 0, 1, 3, 4, 3], [2, 0, 2, 1, 3, 4, 5, 4], [2, 0, 1, 3, 4, 7, 3], [4, 2, 3, 1], [1, 0, 1, 2, 4, 3, 8, 11, 12, 13, 7], [0, 0, 1, 7, 6, 4, 3], [2, 0, 2, 1, 3, 6, 4, 5, 4], [7, 0, 1, 3, 3], [6, 1, 0, 2, 3, 3], [1, 0, 1, 11, 12, 3], [1, 0, 1, 2, 4, 3, 6, 7, 11, 14, 13, 8], [1, 0, 1, 3, 11, 12, 4], [8, 0, 2], [0, 0, 1, 3, 6, 4, 5, 4], [0, 0, 1, 6, 4, 5, 3], [0, 0, 2, 1, 7, 6, 4, 5, 4], [0, 0, 1, 6, 4, 3], [9, 0, 1, 2, 3, 4, 3], [5, 0, 2, 1], [10, 0, 1, 1], [7, 0, 1, 2, 3, 4], [7, 0, 2, 3, 3], [11, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 1], [4, 0, 1, 2, 3], [1, 0, 9, 10, 1, 2, 5, 11, 12, 7], [1, 0, 1, 2, 4, 5, 3, 11, 12, 7], [1, 0, 1, 2, 4, 3, 11, 6], [1, 0, 1, 3, 11, 4], [12, 0, 1, 2, 3, 2], [13, 0, 1, 2, 3, 4, 5, 7], [14, 0, 1, 2, 3, 4, 5, 6, 7, 1], [15, 0, 1, 1], [16, 0, 1, 1], [17, 1], [18, 1], [19, 1], [20, 0, 2], [21, 1], [22, 1], [23, 0, 1, 2, 3, 4, 5, 5], [24, 0, 1, 2, 3, 2]], [[[{"name": "game_uibottom_frame06", "rect": {"x": 0, "y": 0, "width": 132, "height": 46}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 132, "height": 46}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-66, -23, 0, 66, -23, 0, -66, 23, 0, 66, 23, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 46, 132, 46, 0, 0, 132, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -66, "y": -23, "z": 0}, "maxPos": {"x": 66, "y": 23, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [3], 0, [0], [3], [9]], [[{"name": "game_uibottom_frame01", "rect": {"x": 0, "y": 0, "width": 230, "height": 344}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 230, "height": 344}, "rotated": false, "capInsets": [20, 20, 20, 20], "vertices": {"rawPosition": [-115, -172, 0, 115, -172, 0, -115, 172, 0, 115, 172, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 344, 230, 344, 0, 0, 230, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -115, "y": -172, "z": 0}, "maxPos": {"x": 115, "y": 172, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [3], 0, [0], [3], [10]], [[[33, "Game"], [34, "<PERSON><PERSON>", 33554432, "beI88Z2HpFELqR4T5EMHpg", [-72, -73, -74, -75, -76, -77, -78, -79, -80, -81, -82, -83, -84, -85, -86, -87, -88, -89, -90, -91, -92], [[0, -1, [5, 1080, 1920]], [40, -3, -2], [41, 45, 5.684341886080802e-14, 5.684341886080802e-14, -4], [43, -71, -70, -69, -68, -67, -66, -65, -64, -63, -62, -61, -60, -59, -58, -57, -56, -55, -54, -53, -52, -51, -50, -49, -48, -47, -46, -45, -44, -43, -42, -41, -40, -39, -38, -37, -36, -35, -34, -33, -32, -31, -30, -29, -28, -27, -26, -25, -24, -23, -22, -21, -20, -19, -18, -17, -16, -15, -14, -13, -12, -11, -10, -9, -8, -7, -6, -5]], [1, 540, 960, 0]], [18, "ndUITop", 33554432, 1, [-95, -96, -97, -98, -99, -100, -101, -102, -103, -104, -105, -106, -107, -108, -109, -110, -111], [[5, -93], [28, 1, 100, -94]], [1, 0, 810, 0]], [35, "ndSelectMode", 33554432, [-114, -115, -116, -117, -118, -119, -120], [[0, -112, [5, 756, 536.2]], [29, 1, 0, -113, 29]], [1, 0, 20.164999999999964, 0]], [19, "ndSelectGameType", false, 33554432, [-123, -124, -125, -126, -127, -128], [[0, -121, [5, 756, 536.2]], [29, 1, 0, -122, 35]], [1, 0, 20.164999999999964, 0]], [19, "ndBossDetail", false, 33554432, [-131, -132, -133, -134, -135], [[0, -129, [5, 230, 344]], [1, -130, 40]], [1, -325.228, 144.837, 0]], [19, "ndMonsterDetail", false, 33554432, [-138, -139, -140, -141, -142], [[0, -136, [5, 230, 344]], [1, -137, 44]], [1, -94.52699999999999, 144.837, 0]], [6, "btnGameSpeed", 33554432, 2, [-145, -146, -147, -148], [[[0, -143, [5, 200, 100]], -144], 4, 1], [1, 348.32000000000005, -110.471, 0]], [6, "btnAutoGameLevel", 33554432, 2, [-152, -153], [[[0, -149, [5, 68, 68]], [1, -150, 3], -151], 4, 4, 1], [1, -400.69100000000003, -123.66699999999992, 0]], [6, "btnOnlineMode", 33554432, 2, [-157, -158], [[[0, -154, [5, 68, 68]], [1, -155, 5], -156], 4, 4, 1], [1, -400.69100000000003, -212.99700000000007, 0]], [6, "btnMode", 33554432, 3, [-162, -163], [[[0, -159, [5, 188, 61]], -160, [8, -161]], 4, 1, 4], [1, 195.47000000000003, -27.295000000000073, 0]], [6, "btnSelectLev", 33554432, 3, [-167, -168], [[[0, -164, [5, 188, 61]], -165, [8, -166]], 4, 1, 4], [1, 195.47000000000003, 60.979999999999905, 0]], [6, "btnType", 33554432, 4, [-172, -173], [[[0, -169, [5, 188, 61]], -170, [8, -171]], 4, 1, 4], [1, 195.47000000000003, 35.888000000000034, 0]], [18, "ndUIBottom", 33554432, 1, [-176, 5, 6, -177], [[5, -174], [28, 4, 100, -175]], [1, 0, -910, 0]], [27, "btnTest", false, 33554432, 2, [-181], [[[0, -178, [5, 200, 100]], -179, [14, 0, -180, 14]], 4, 1, 4], [1, 117.53800000000001, -138.61899999999991, 0]], [6, "btnBack", 33554432, 3, [-185], [[[0, -182, [5, 245, 73]], -183, [14, 0, -184, 24]], 4, 1, 4], [1, -160, -186, 0]], [6, "btnBack", 33554432, 4, [-189], [[[0, -186, [5, 245, 73]], -187, [14, 0, -188, 31]], 4, 1, 4], [1, -160, -186, 0]], [3, "btnRank02", 33554432, 2, [[[0, -190, [5, 99, 100]], -191, [1, -192, 11]], 4, 1, 4], [1, 400, -207.56600000000003, 0]], [3, "btnSetting", 33554432, 2, [[[0, -193, [5, 94, 95]], -194, [1, -195, 12]], 4, 1, 4], [1, -400, 0.15499999999997272, 0]], [6, "btnMoveLeft", 33554432, 2, [-199], [[[0, -196, [5, 68, 68]], [1, -197, 17], -198], 4, 4, 1], [1, -400, -341.0250000000001, 0]], [6, "btnMoveRight", 33554432, 2, [-203], [[[0, -200, [5, 68, 68]], [1, -201, 19], -202], 4, 4, 1], [1, 400, -341.0250000000001, 0]], [3, "btnCrossReward", 33554432, 2, [[[0, -204, [5, 84, 84]], [14, 0, -205, 20], -206], 4, 4, 1], [1, -314.091, 0.3330000000000837, 0]], [3, "btnExchange", 33554432, 2, [[[0, -207, [5, 99, 100]], -208, [1, -209, 22]], 4, 1, 4], [1, 282.85699999999997, -203.27999999999997, 0]], [26, "ndUIContent", 33554432, 1, [3, 4], [[5, -210]]], [3, "btnPlay", 33554432, 3, [[[0, -211, [5, 245, 73]], -212, [1, -213, 25]], 4, 1, 4], [1, 160, -186, 0]], [3, "btnPlay", 33554432, 4, [[[0, -214, [5, 245, 73]], -215, [1, -216, 32]], 4, 1, 4], [1, 160, -186, 0]], [36, "ndRoundDetail", false, 33554432, 13, [-219], [[0, -217, [5, 461, 52]], [1, -218, 36]], [1, -211.572, 345.415, 0]], [18, "ndSub", 33554432, 5, [-222], [[0, -220, [5, 132, 46]], [1, -221, 39]], [1, 2.842170943040401e-14, -120.36399999999999, 0]], [18, "ndSub", 33554432, 6, [-225], [[0, -223, [5, 132, 46]], [1, -224, 43]], [1, 0, -120.36399999999999, 0]], [37, "sprRoundStart", 33554432, [-228], [[0, -226, [5, 399, 113]], [1, -227, 45]]], [2, "ndContentBottom", 33554432, 1, [[0, -229, [5, 1080, 1920]], [1, -230, 1], [42, 4, 1.1368683772161603e-13, -231]]], [7, "btnUpGameSpeed", 33554432, 7, [[0, -232, [5, 40, 32]], [1, -233, 7]], [1, 54, 0, 0]], [7, "btnDownGameSpeed", 33554432, 7, [[0, -234, [5, 40, 32]], [1, -235, 8]], [1, -54, 0, 0]], [3, "btnSkin", 33554432, 2, [[[0, -236, [5, 83, 84]], [1, -237, 9], -238], 4, 4, 1], [1, 290.90099999999995, 0.15499999999997272, 0]], [3, "btnRank01", 33554432, 2, [[[0, -239, [5, 83, 84]], [1, -240, 10], -241], 4, 4, 1], [1, 400, 0.15499999999997272, 0]], [20, "btnSound", false, 33554432, 2, [[0, -242, [5, 94, 95]], [1, -243, 13], [12, 3, -244]], [1, -368.712, 0.155, 0]], [26, "ndScore", 33554432, 2, [-247], [[0, -245, [5, 392, 131]], [1, -246, 15]]], [3, "btnTask", 33554432, 2, [[[0, -248, [5, 84, 84]], [14, 0, -249, 21], -250], 4, 4, 1], [1, -226.57799999999997, -2.9300000000000637, 0]], [27, "btnGameNextRoundStart", false, 33554432, 13, [29], [[[0, -251, [5, 399, 113]], -252], 4, 1], [1, 0, 910, 0]], [38, "Camera", 33554432, 1, [-253], [1, 0, 0, 1000]], [7, "ndSpaceMonster", 33554432, 1, [[0, -254, [5, 820, 600]]], [1, 0, -247.538, 0]], [7, "ndSpaceHero", 33554432, 1, [[0, -255, [5, 900, 260]]], [1, 0, 399.03099999999995, 0]], [2, "sprBg", 33554432, 1, [[0, -256, [5, 1080, 1920]], [1, -257, 0]]], [2, "ndContentHero", 33554432, 1, [[5, -258]]], [7, "ndContentTower", 33554432, 1, [[0, -259, [5, 828, 304]]], [1, 0, 205.084, 0]], [21, "Graphics", 33554432, 1, [[[39, -260, [0, 0, 0]], -261], 4, 1]], [2, "ndContentHurtEffect", 33554432, 1, [[5, -262]]], [2, "ndContentMonster", 33554432, 1, [[5, -263]]], [2, "ndContentSkill", 33554432, 1, [[5, -264]]], [2, "ndContentMonsterEffect", 33554432, 1, [[5, -265]]], [2, "ndContentHeroEffect", 33554432, 1, [[5, -266]]], [2, "ndGiftTipsContent", 33554432, 1, [[5, -267]]], [2, "ndGiftPicture", 33554432, 1, [[5, -268], [8, -269]]], [3, "sprTagAuto", 33554432, 8, [[[0, -270, [5, 77, 69]], -271], 4, 1], [1, 9.15500000000003, 9.726999999999862, 0]], [4, "lbLev", 33554432, 8, [[0, -272, [5, 240, 100.8]], [9, "自动下一关", 48, 48, 80, false, -273, [4, 4278594360], 2]], [1, 101.12700000000004, -1.8640000000000327, 0], [1, 0.5, 0.5, 1]], [3, "sprOnlineMode", 33554432, 9, [[[0, -274, [5, 77, 69]], -275], 4, 1], [1, 9.15500000000003, 9.726999999999862, 0]], [4, "lbMode", 33554432, 9, [[0, -276, [5, 192, 100.8]], [9, "自动开始", 48, 48, 80, false, -277, [4, 4278594360], 4]], [1, 94.36900000000003, -1.8640000000000327, 0], [1, 0.5, 0.5, 1]], [2, "game_speed_bg", 33554432, 7, [[0, -278, [5, 190, 67]], [1, -279, 6]]], [21, "lbGameSpeed", 33554432, 7, [[[0, -280, [5, 16.6845703125, 50.4]], -281], 4, 1]], [22, "lbGameTime", false, 33554432, 2, [[[0, -282, [5, 44.4921875, 50.4]], -283], 4, 1], [1, 0, -89.452, 0]], [3, "lbDesc", 33554432, 2, [[[13, -284, [5, 760, 50.4], [0, 0, 1]], -285], 4, 1], [1, -503.188, -78.421, 0]], [2, "Label", 33554432, 14, [[0, -286, [5, 80, 50.4]], [30, "测试", 40, -287, [4, 4282532418]]]], [23, "Label", 33554432, 36, [[[0, -288, [5, 800, 151.2]], -289], 4, 1], [1, 0.5, 0.5, 1]], [4, "Label", 33554432, 19, [[0, -290, [5, 80, 100.8]], [9, "左", 80, 80, 80, false, -291, [4, 4288331727], 16]], [1, 0, 1.8990000000001146, 0], [1, 0.5, 0.5, 1]], [4, "Label", 33554432, 20, [[0, -292, [5, 80, 100.8]], [9, "右", 80, 80, 80, false, -293, [4, 4288331727], 18]], [1, 0, 1.8990000000001146, 0], [1, 0.5, 0.5, 1]], [22, "lbJoinNum", false, 33554432, 3, [[[0, -294, [5, 144.4599609375, 50.4]], -295], 4, 1], [1, 0, 306.2249999999999, 0]], [22, "lbTime", false, 33554432, 3, [[[0, -296, [5, 210.919921875, 50.4]], -297], 4, 1], [1, 0, 32.04299999999989, 0]], [4, "Label", 33554432, 15, [[0, -298, [5, 140, 108.8]], [31, "返回", 66, 66, 80, false, true, 4, -299, [4, 4278333767], 23]], [1, 0, 0.9370000000000118, 0], [1, 0.5, 0.5, 1]], [4, "Label", 33554432, 10, [[0, -300, [5, 160, 100.8]], [25, "修改", 80, 80, 80, false, true, -301, [4, 4290011158], 26]], [1, -27.790000000000077, 0, 0], [1, 0.5, 0.5, 1]], [11, "lbMode", 33554432, 10, [[[13, -302, [5, 220, 100.8], [0, 0, 0.5]], -303], 4, 1], [1, -430.37600000000003, 0, 0], [1, 0.5, 0.5, 1]], [4, "Label", 33554432, 11, [[0, -304, [5, 160, 100.8]], [25, "修改", 80, 80, 80, false, true, -305, [4, 4290011158], 27]], [1, -27.790000000000077, 0, 0], [1, 0.5, 0.5, 1]], [11, "lbLev", 33554432, 11, [[[13, -306, [5, 453.5999755859375, 100.8], [0, 0, 0.5]], -307], 4, 1], [1, -430.37600000000003, 0, 0], [1, 0.5, 0.5, 1]], [4, "lbLev", 33554432, 3, [[0, -308, [5, 320, 100.8]], [9, "关卡选择", 80, 80, 80, false, -309, [4, 4289062911], 28]], [1, 0, 232.2070000000001, 0], [1, 0.5, 0.5, 1]], [20, "lbJoinNum", false, 33554432, 4, [[0, -310, [5, 144.4599609375, 50.4]], [16, "参加人数：0", 26, 26, -311, [4, 4279772212]]], [1, 0, 306.2249999999999, 0]], [20, "lbTime", false, 33554432, 4, [[0, -312, [5, 210.919921875, 50.4]], [16, "通关时间：20分钟", 26, 26, -313, [4, 4279772212]]], [1, 0, 32.04299999999989, 0]], [4, "Label", 33554432, 16, [[0, -314, [5, 140, 108.8]], [31, "返回", 66, 66, 80, false, true, 4, -315, [4, 4278333767], 30]], [1, 0, 0.9370000000000118, 0], [1, 0.5, 0.5, 1]], [4, "Label", 33554432, 12, [[0, -316, [5, 160, 100.8]], [25, "修改", 80, 80, 80, false, true, -317, [4, 4290011158], 33]], [1, -27.790000000000077, 0, 0], [1, 0.5, 0.5, 1]], [11, "lbMode", 33554432, 12, [[[13, -318, [5, 621.52001953125, 100.8], [0, 0, 0.5]], -319], 4, 1], [1, -430.37600000000003, 0, 0], [1, 0.5, 0.5, 1]], [4, "lbLev", 33554432, 4, [[0, -320, [5, 320, 100.8]], [9, "关卡选择", 80, 80, 80, false, -321, [4, 4289062911], 34]], [1, 0, 232.2070000000001, 0], [1, 0.5, 0.5, 1]], [11, "lbRound", 33554432, 26, [[[13, -322, [5, 180, 75.6], [0, 0, 0.5]], -323], 4, 1], [1, -213.399, -1.233000000000004, 0], [1, 0.5, 0.5, 1]], [4, "lbLev-001", 33554432, 5, [[0, -324, [5, 150.7799835205078, 75.6]], [9, "BOSS", 60, 60, 60, false, -325, [4, 4284987638], 37]], [1, -59.286, 149.401, 0], [1, 0.5, 0.5, 1]], [3, "spr<PERSON><PERSON>", 33554432, 5, [[[0, -326, [5, 40, 36]], -327], 4, 1], [1, -0.37599999999994793, 44.59300000000002, 0]], [7, "ndBossMask", 33554432, 5, [[0, -328, [5, 96, 96]], [1, -329, 38]], [1, -0.37599999999994793, 44.59300000000002, 0]], [11, "lbBossDesc", 33554432, 5, [[[0, -330, [5, 250, 135.6]], -331], 4, 1], [1, 2.3570000000000277, -50.08699999999999, 0], [1, 0.5, 0.5, 1]], [23, "lbSubDesc", 33554432, 27, [[[0, -332, [5, 36.79998779296875, 50.4]], -333], 4, 1], [1, 0.5, 0.5, 1]], [4, "lbLev-002", 33554432, 6, [[0, -334, [5, 120, 75.6]], [9, "怪物", 60, 60, 60, false, -335, [4, 4293451400], 41]], [1, -66.98599999999999, 149.33800000000002, 0], [1, 0.5, 0.5, 1]], [3, "sprMonster", 33554432, 6, [[[0, -336, [5, 40, 36]], -337], 4, 1], [1, 2.4110000000000014, 44.59300000000002, 0]], [7, "ndMonsterMask", 33554432, 6, [[0, -338, [5, 96, 96]], [1, -339, 42]], [1, 2.4110000000000014, 44.59300000000002, 0]], [11, "lbMonsterDesc", 33554432, 6, [[[0, -340, [5, 299.0999755859375, 135.6]], -341], 4, 1], [1, 0.07100000000002638, -50.08699999999999, 0], [1, 0.5, 0.5, 1]], [23, "lbSubDesc", 33554432, 28, [[[0, -342, [5, 36.79998779296875, 50.4]], -343], 4, 1], [1, 0.5, 0.5, 1]], [21, "lbGameNextRoundStart", 33554432, 29, [[[0, -344, [5, 276.79998779296875, 50.4]], -345], 4, 1]], [2, "ndContentHurtTips", 33554432, 1, [[5, -346]]], [2, "ndContentBossOrSkillTips", 33554432, 1, [[5, -347]]], [49, "Game", [1], [50, null, null, "5fef0a4a-9f9e-494d-aec8-ba149d85d009", null, null, null], [51, [52, [2, 0, 0, 0, 0.520833125], [2, 0, 0, 0, 0]], [53, [4, 4283190348], [0, 512, 512]], [54], [55], [56], [57, false], [58], [59]]], [60, 0, 960, 0, 1108344832, 39, [4, 4278190080]], [61, 10, 45, [4, 4278190116], [4, 2536912639]], [7, "ndContentCloud", 33554432, 1, [[0, -348, [5, 828, 178]]], [1, 0, 139.779, 0]], [8, 53], [24, 8, 8], [8, 55], [24, 9, 9], [16, "1", 30, 30, 58, [4, 4288331727]], [24, 7, 7], [12, 3, 33], [12, 3, 34], [15, 3, 17, 17], [15, 3, 18, 18], [30, "00", 40, 59, [4, 4281216558]], [45, "", 0, 0, 26, 26, 3, 60, [4, 4280558628]], [15, 3, 14, 14], [46, "积分池：0", 91, 90, 120, 2, false, 62, [4, 4280338687]], [12, 3, 19], [12, 3, 20], [15, 3, 21, 21], [12, 3, 37], [15, 3, 22, 22], [16, "参加人数：0", 26, 26, 65, [4, 4279772212]], [16, "通关时间：20分钟", 26, 26, 66, [4, 4279772212]], [10, 3, 1.1, 15, 15], [10, 3, 1.1, 24, 24], [17, "难度：", 80, 80, 80, false, 69, [4, 4279772212]], [10, 3, 1.02, 10, 10], [17, "选择关卡：--", 80, 80, 80, false, 71, [4, 4279772212]], [10, 3, 1.02, 11, 11], [10, 3, 1.1, 16, 16], [10, 3, 1.1, 25, 25], [17, "副本Boss：九尾狐", 80, 70, 80, false, 77, [4, 4279772212]], [10, 3, 1.02, 12, 12], [47, "第一关", 60, 60, 60, false, 79], [8, 81], [17, "妖王小钻风\n第十波出现", 50, 50, 60, false, 83, [4, 4281150961]], [32, "--", 40, false, 84, [4, 4281150961]], [8, 86], [17, "蝙蝠妖\n剩余数量：50", 50, 50, 60, false, 88, [4, 4294170805]], [32, "--", 40, false, 89, [4, 4294170805]], [48, "开始挑战--关卡", 40, false, 90], [44, 3, 1.1, 38]], 0, [0, 0, 1, 0, 5, 94, 0, 0, 1, 0, 0, 1, 0, 6, 95, 0, 7, 135, 0, 8, 29, 0, 9, 113, 0, 10, 136, 0, 11, 131, 0, 12, 134, 0, 13, 27, 0, 14, 28, 0, 15, 5, 0, 16, 6, 0, 17, 26, 0, 18, 115, 0, 19, 112, 0, 20, 111, 0, 21, 110, 0, 22, 132, 0, 23, 133, 0, 24, 129, 0, 25, 130, 0, 26, 128, 0, 27, 108, 0, 28, 107, 0, 29, 41, 0, 30, 40, 0, 31, 116, 0, 32, 117, 0, 33, 126, 0, 34, 120, 0, 35, 122, 0, 36, 109, 0, 37, 99, 0, 38, 97, 0, 39, 31, 0, 40, 32, 0, 41, 101, 0, 42, 102, 0, 43, 114, 0, 44, 100, 0, 45, 98, 0, 46, 105, 0, 47, 104, 0, 48, 124, 0, 49, 118, 0, 50, 103, 0, 51, 123, 0, 52, 127, 0, 53, 121, 0, 54, 125, 0, 55, 119, 0, 56, 106, 0, 57, 91, 0, 58, 46, 0, 59, 49, 0, 60, 50, 0, 61, 48, 0, 62, 92, 0, 63, 51, 0, 64, 47, 0, 65, 43, 0, 66, 39, 0, 67, 44, 0, 68, 4, 0, 69, 3, 0, 70, 23, 0, 71, 2, 0, 0, 1, 0, -1, 39, 0, -2, 40, 0, -3, 41, 0, -4, 42, 0, -5, 43, 0, -6, 44, 0, -7, 45, 0, -8, 46, 0, -9, 96, 0, -10, 47, 0, -11, 30, 0, -12, 48, 0, -13, 49, 0, -14, 50, 0, -15, 51, 0, -16, 52, 0, -17, 2, 0, -18, 23, 0, -19, 13, 0, -20, 91, 0, -21, 92, 0, 0, 2, 0, 0, 2, 0, -1, 8, 0, -2, 9, 0, -3, 7, 0, -4, 33, 0, -5, 34, 0, -6, 17, 0, -7, 18, 0, -8, 35, 0, -9, 59, 0, -10, 60, 0, -11, 14, 0, -12, 36, 0, -13, 19, 0, -14, 20, 0, -15, 21, 0, -16, 37, 0, -17, 22, 0, 0, 3, 0, 0, 3, 0, -1, 65, 0, -2, 66, 0, -3, 15, 0, -4, 24, 0, -5, 10, 0, -6, 11, 0, -7, 72, 0, 0, 4, 0, 0, 4, 0, -1, 73, 0, -2, 74, 0, -3, 16, 0, -4, 25, 0, -5, 12, 0, -6, 78, 0, 0, 5, 0, 0, 5, 0, -1, 80, 0, -2, 81, 0, -3, 82, 0, -4, 83, 0, -5, 27, 0, 0, 6, 0, 0, 6, 0, -1, 85, 0, -2, 86, 0, -3, 87, 0, -4, 88, 0, -5, 28, 0, 0, 7, 0, -2, 102, 0, -1, 57, 0, -2, 31, 0, -3, 32, 0, -4, 58, 0, 0, 8, 0, 0, 8, 0, -3, 98, 0, -1, 53, 0, -2, 54, 0, 0, 9, 0, 0, 9, 0, -3, 100, 0, -1, 55, 0, -2, 56, 0, 0, 10, 0, -2, 121, 0, 0, 10, 0, -1, 68, 0, -2, 69, 0, 0, 11, 0, -2, 123, 0, 0, 11, 0, -1, 70, 0, -2, 71, 0, 0, 12, 0, -2, 127, 0, 0, 12, 0, -1, 76, 0, -2, 77, 0, 0, 13, 0, 0, 13, 0, -1, 26, 0, -4, 38, 0, 0, 14, 0, -2, 109, 0, 0, 14, 0, -1, 61, 0, 0, 15, 0, -2, 118, 0, 0, 15, 0, -1, 67, 0, 0, 16, 0, -2, 124, 0, 0, 16, 0, -1, 75, 0, 0, 17, 0, -2, 105, 0, 0, 17, 0, 0, 18, 0, -2, 106, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, -3, 111, 0, -1, 63, 0, 0, 20, 0, 0, 20, 0, -3, 112, 0, -1, 64, 0, 0, 21, 0, 0, 21, 0, -3, 113, 0, 0, 22, 0, -2, 115, 0, 0, 22, 0, 0, 23, 0, 0, 24, 0, -2, 119, 0, 0, 24, 0, 0, 25, 0, -2, 125, 0, 0, 25, 0, 0, 26, 0, 0, 26, 0, -1, 79, 0, 0, 27, 0, 0, 27, 0, -1, 84, 0, 0, 28, 0, 0, 28, 0, -1, 89, 0, 0, 29, 0, 0, 29, 0, -1, 90, 0, 0, 30, 0, 0, 30, 0, 0, 30, 0, 0, 31, 0, 0, 31, 0, 0, 32, 0, 0, 32, 0, 0, 33, 0, 0, 33, 0, -3, 103, 0, 0, 34, 0, 0, 34, 0, -3, 104, 0, 0, 35, 0, 0, 35, 0, 0, 35, 0, 0, 36, 0, 0, 36, 0, -1, 62, 0, 0, 37, 0, 0, 37, 0, -3, 114, 0, 0, 38, 0, -2, 136, 0, -1, 94, 0, 0, 40, 0, 0, 41, 0, 0, 42, 0, 0, 42, 0, 0, 43, 0, 0, 44, 0, 0, 45, 0, -2, 95, 0, 0, 46, 0, 0, 47, 0, 0, 48, 0, 0, 49, 0, 0, 50, 0, 0, 51, 0, 0, 52, 0, 0, 52, 0, 0, 53, 0, -2, 97, 0, 0, 54, 0, 0, 54, 0, 0, 55, 0, -2, 99, 0, 0, 56, 0, 0, 56, 0, 0, 57, 0, 0, 57, 0, 0, 58, 0, -2, 101, 0, 0, 59, 0, -2, 107, 0, 0, 60, 0, -2, 108, 0, 0, 61, 0, 0, 61, 0, 0, 62, 0, -2, 110, 0, 0, 63, 0, 0, 63, 0, 0, 64, 0, 0, 64, 0, 0, 65, 0, -2, 116, 0, 0, 66, 0, -2, 117, 0, 0, 67, 0, 0, 67, 0, 0, 68, 0, 0, 68, 0, 0, 69, 0, -2, 120, 0, 0, 70, 0, 0, 70, 0, 0, 71, 0, -2, 122, 0, 0, 72, 0, 0, 72, 0, 0, 73, 0, 0, 73, 0, 0, 74, 0, 0, 74, 0, 0, 75, 0, 0, 75, 0, 0, 76, 0, 0, 76, 0, 0, 77, 0, -2, 126, 0, 0, 78, 0, 0, 78, 0, 0, 79, 0, -2, 128, 0, 0, 80, 0, 0, 80, 0, 0, 81, 0, -2, 129, 0, 0, 82, 0, 0, 82, 0, 0, 83, 0, -2, 130, 0, 0, 84, 0, -2, 131, 0, 0, 85, 0, 0, 85, 0, 0, 86, 0, -2, 132, 0, 0, 87, 0, 0, 87, 0, 0, 88, 0, -2, 133, 0, 0, 89, 0, -2, 134, 0, 0, 90, 0, -2, 135, 0, 0, 91, 0, 0, 92, 0, 0, 96, 0, 72, 93, 1, 4, 93, 3, 4, 23, 4, 4, 23, 5, 4, 13, 6, 4, 13, 29, 4, 38, 348], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 97, 99, 110, 120, 122, 126, 128, 130, 131, 133, 134, 135], [1, 1, 2, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 1, 2, 1, 1, 1, 1, 2, 1, 1, 2, 2, 2, 1, 2, 1, 1, 2, 2, 1, 1, 2, 1, 1, 1, 2, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [11, 12, 0, 1, 0, 1, 13, 14, 15, 16, 17, 2, 18, 19, 20, 21, 0, 1, 0, 1, 22, 23, 2, 0, 3, 4, 0, 0, 0, 5, 0, 3, 4, 0, 0, 5, 24, 0, 6, 25, 7, 0, 6, 26, 7, 27, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], [[{"name": "game_uibottom_frame05", "rect": {"x": 0, "y": 0, "width": 132, "height": 46}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 132, "height": 46}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-66, -23, 0, 66, -23, 0, -66, 23, 0, 66, 23, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 46, 132, 46, 0, 0, 132, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -66, "y": -23, "z": 0}, "maxPos": {"x": 66, "y": 23, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [3], 0, [0], [3], [28]], [[{"name": "game_uibottom_subtitlebg", "rect": {"x": 0, "y": 0, "width": 461, "height": 52}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 461, "height": 52}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-230.5, -26, 0, 230.5, -26, 0, -230.5, 26, 0, 230.5, 26, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 52, 461, 52, 0, 0, 461, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -230.5, "y": -26, "z": 0}, "maxPos": {"x": 230.5, "y": 26, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [3], 0, [0], [3], [29]], [[{"name": "sence_02", "rect": {"x": 0, "y": 0, "width": 1080, "height": 1920}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 1080, "height": 1920}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-540, -960, 0, 540, -960, 0, -540, 960, 0, 540, 960, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 1920, 1080, 1920, 0, 0, 1080, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -540, "y": -960, "z": 0}, "maxPos": {"x": 540, "y": 960, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [3], 0, [0], [3], [30]], [[{"name": "sence_01", "rect": {"x": 0, "y": 0, "width": 1080, "height": 1920}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 1080, "height": 1920}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-540, -960, 0, 540, -960, 0, -540, 960, 0, 540, 960, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 1920, 1080, 1920, 0, 0, 1080, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -540, "y": -960, "z": 0}, "maxPos": {"x": 540, "y": 960, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [3], 0, [0], [3], [31]]]]