[1, ["52fHu7D8hGm5vLaoALoXCl", "54DHJyaGhI7LVJ6Hw08WHG@f9941", "c4Ivt8SBZB25pLPpGg1u1P@f9941", "46IdxunHlHYYawWmS60uVs@f9941", "14UZquuXhLeq/vm/+qgvE/@f9941", "2cCJ/GlQhGmIKYLdsa4Emm@f9941"], ["node", "_font", "_spriteFrame", "root", "lbMonthTask", "lbWeekTask", "lbDayTask", "lbDesc", "btnClose", "sprFrame", "ndRoot", "data"], [["cc.Node", ["_name", "_layer", "_components", "_prefab", "_parent", "_children", "_lpos", "_lscale"], 1, 9, 4, 1, 2, 5, 5], ["cc.Node", ["_name", "_layer", "_parent", "_components", "_prefab", "_lpos", "_lscale"], 1, 1, 12, 4, 5, 5], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.Sprite", ["_type", "node", "__prefab", "_spriteFrame"], 2, 1, 4, 6], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_isSystemFontUsed", "_lineHeight", "_enableOutline", "node", "__prefab", "_color", "_font", "_outlineColor"], -3, 1, 4, 5, 6, 5], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.<PERSON><PERSON>", ["_transition", "node", "__prefab", "_normalColor", "_target"], 2, 1, 4, 5, 1], ["cc.Widget", ["_alignFlags", "_left", "_top", "node", "__prefab"], 0, 1, 4], ["ac1bey2sgxPB7QUcRVGV3Xn", ["node", "__prefab", "ndRoot", "sprFrame", "btnClose", "lbDesc", "lbDayTask", "lbWeekTask", "lbMonthTask"], 3, 1, 4, 1, 1, 1, 1, 1, 1, 1]], [[6, 0, 2], [7, 0, 1, 2, 3, 4, 5, 5], [2, 0, 1, 2, 1], [3, 1, 2, 3, 1], [0, 0, 1, 4, 5, 2, 3, 6, 3], [4, 0, 1, 2, 4, 3, 6, 7, 8, 6], [0, 0, 1, 4, 2, 3, 6, 7, 3], [1, 0, 1, 2, 3, 4, 5, 6, 3], [2, 0, 1, 2, 3, 1], [4, 0, 1, 2, 4, 3, 6, 7, 8, 9, 6], [0, 0, 1, 4, 5, 2, 3, 3], [1, 0, 1, 2, 3, 4, 5, 3], [2, 0, 1, 1], [5, 0, 2], [0, 0, 1, 5, 2, 3, 3], [0, 0, 1, 4, 2, 3, 6, 3], [1, 0, 1, 2, 3, 4, 3], [3, 1, 2, 1], [3, 0, 1, 2, 3, 2], [4, 0, 1, 2, 3, 5, 6, 7, 8, 10, 9, 6], [8, 0, 1, 2, 3, 4, 2], [9, 0, 1, 2, 3, 4, 4], [10, 0, 1, 2, 3, 4, 5, 6, 7, 8, 1]], [[13, "ViewTaskRewardDesc"], [14, "ViewTaskRewardDesc", 33554432, [-11], [[12, -2, [0, "a2kanFPWtMWJNt3TNZMIm3"]], [22, -10, [0, "00XHIGzTxKyp+D0Up+pnfO"], -9, -8, -7, -6, -5, -4, -3]], [1, "a8/clxSKNGM4WaaPgq+0NU", null, null, null, -1, 0]], [10, "ndRoot", 33554432, 1, [-13, -14, -15, -16, -17, -18, -19], [[12, -12, [0, "67cf6zi0JPcKqCfTNhBapW"]]], [1, "03faNzyrtD8bewswjxufiU", null, null, null, 1, 0]], [11, "btnClose", 33554432, 2, [[[2, -20, [0, "62Lma5VmNLGqwZv8K17ztW"], [5, 112, 113]], [18, 1, -21, [0, "aaM4vSh/5EiIQG+wWdQOLM"], 2], -22, [21, 9, 361.706, -360.5909999999999, -23, [0, "f0YhYgZLpAtZxnEolloSMW"]]], 4, 4, 1, 4], [1, "8cmnPQDSdPPbRPq8zq43zW", null, null, null, 1, 0], [1, 367.706, 354.0909999999999, 0]], [10, "common_frame_16", 33554432, 2, [-26, -27], [[2, -24, [0, "33C7W3RKhJd4AeviVtuZnl"], [5, 700, 111]], [3, -25, [0, "8fMMNH2IJHNadvYUZXedOj"], 5]], [1, "0en9Islx5NJoo06Gcjv/Ji", null, null, null, 1, 0]], [4, "common_frame_17", 33554432, 2, [-30, -31], [[2, -28, [0, "e7bt7QFEVK85fSaIVP/b6A"], [5, 700, 111]], [3, -29, [0, "c2EKPTTk1GVo5U4E8o9Zue"], 7]], [1, "b2i0kHPnxILo5PkWIoM0K7", null, null, null, 1, 0], [1, 0, -127.832, 0]], [4, "common_frame_18", 33554432, 2, [-34, -35], [[2, -32, [0, "9eKuk3txJJIYyhnszzbF0A"], [5, 700, 111]], [3, -33, [0, "3aEujmjJFLJaFqhEqYLREW"], 9]], [1, "7bf97A/XlAbIg0hXddZXLN", null, null, null, 1, 0], [1, 0, -255.664, 0]], [4, "game_title_viewuserinfo", 33554432, 2, [-38], [[2, -36, [0, "f1FLgCPyxGrYvofM+30unI"], [5, 294, 77]], [3, -37, [0, "f325AkqupHUbiIyy2qra1b"], 1]], [1, "65losQypRGf6Evbx9pxD1h", null, null, null, 1, 0], [1, 0, 349.42100000000005, 0]], [4, "common_frame_15", 33554432, 2, [-41], [[2, -39, [0, "f9Yx+RDHFECJlxahNylC3n"], [5, 393, 154]], [3, -40, [0, "c7g/j58QJOZZOgB2Bmmfhi"], 3]], [1, "75YZR3a6FBcJ19KCGIwSRD", null, null, null, 1, 0], [1, 64.67899999999997, 212.8409999999999, 0]], [16, "sprFrame", 33554432, 2, [[[2, -42, [0, "1b4EkFQRtHu5kWiM2Y1NXK"], [5, 768, 715]], -43], 4, 1], [1, "2dHdD0bWhKNaYjJyWKoYnC", null, null, null, 1, 0]], [15, "Label", 33554432, 7, [[2, -44, [0, "60f+XbKxNN/pIHZ8zTCvqi"], [5, 148, 54.4]], [19, "任务奖励", 36, 36, false, true, -45, [0, "eewbNVJwJIVKcsCK4oS4Zr"], [4, 4292933887], [4, 4278223283], 0]], [1, "ecHyTJiTtMX7hRwjm/ael3", null, null, null, 1, 0], [1, 0, 4.5470000000000255, 0]], [11, "Label", 33554432, 8, [[[2, -46, [0, "16bmFyms1G4pHcbHJ2z33e"], [5, 272, 112.99999999999999]], -47], 4, 1], [1, "82zrUro3NPpbDVjBl8kAUH", null, null, null, 1, 0], [1, 11.150000000000091, 0, 0]], [6, "lbName", 33554432, 4, [[2, -48, [0, "0eG0hZqaBPtY55X8OUFgW3"], [5, 240, 100.8]], [9, "每日任务", 60, 60, 80, false, -49, [0, "0efzMkrFlK8rYY88GxdLxL"], [4, 4279772212], 4]], [1, "33wsxYDMlKcK1fayi+S/Xq", null, null, null, 1, 0], [1, -263.204, 23.87099999999998, 0], [1, 0.5, 0.5, 1]], [7, "lbName-001", 33554432, 4, [[[8, -50, [0, "d8X7sKpwNIL6k62WzLKsrj"], [5, 885, 100.8], [0, 0, 0.5]], -51], 4, 1], [1, "68SwccHORI1KNw9X7TD918", null, null, null, 1, 0], [1, -323.204, -19.738000000000056, 0], [1, 0.5, 0.5, 1]], [6, "lbName", 33554432, 5, [[2, -52, [0, "b5+PIrvWBLhZpwQNip7GNE"], [5, 240, 100.8]], [9, "每周任务", 60, 60, 80, false, -53, [0, "85PmyqsKlA0Ytr5hTF5DEa"], [4, 4279772212], 6]], [1, "36d5n3XVRLZoXpKLQzO2p8", null, null, null, 1, 0], [1, -263.204, 23.87099999999998, 0], [1, 0.5, 0.5, 1]], [7, "lbName-001", 33554432, 5, [[[8, -54, [0, "b3O/u+uuJLLozZ+g583RLd"], [5, 885, 100.8], [0, 0, 0.5]], -55], 4, 1], [1, "90zsc8rihNx6NCtnhnznj/", null, null, null, 1, 0], [1, -323.204, -19.738000000000056, 0], [1, 0.5, 0.5, 1]], [6, "lbName", 33554432, 6, [[2, -56, [0, "86+XT3NHtElbJ/smAVLP91"], [5, 240, 100.8]], [9, "每月任务", 60, 60, 80, false, -57, [0, "58f+020axEVaYEUnULneO1"], [4, 4279772212], 8]], [1, "fbJ3JAN/xFhbqwfz8/5DPv", null, null, null, 1, 0], [1, -263.204, 23.87099999999998, 0], [1, 0.5, 0.5, 1]], [7, "lbName-001", 33554432, 6, [[[8, -58, [0, "150NLW+MVHYa95QKjMgWOt"], [5, 885, 100.8], [0, 0, 0.5]], -59], 4, 1], [1, "c6ZtZWXdNLMbXugbAW5249", null, null, null, 1, 0], [1, -323.204, -19.738000000000056, 0], [1, 0.5, 0.5, 1]], [17, 9, [0, "d6XcaUHyxDSqcCfz5trIDZ"]], [20, 3, 3, [0, "401ym5x7ZIDqpbDlUZw5Bn"], [4, 4292269782], 3], [5, "完成任务获得灵珠\n提升属性增强战力", 34, 34, 50, false, 11, [0, "915r+bN+pF8YprgHTsfmpd"], [4, 4282467400]], [5, "每日首次随机任意礼物，奖励宝珠", 60, 60, 80, false, 13, [0, "80qADRJS9B65pBLfDz8eWq"], [4, 4279772212]], [5, "每日首次随机任意礼物，奖励宝珠", 60, 60, 80, false, 15, [0, "fa1Ce/s4pK7pxcw0UxN5/c"], [4, 4279772212]], [5, "每日首次随机任意礼物，奖励宝珠", 60, 60, 80, false, 17, [0, "a9aubjOpFLb4SmB916wiKT"], [4, 4279772212]]], 0, [0, 3, 1, 0, 0, 1, 0, 4, 23, 0, 5, 22, 0, 6, 21, 0, 7, 20, 0, 8, 19, 0, 9, 18, 0, 10, 2, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, -1, 9, 0, -2, 7, 0, -3, 3, 0, -4, 8, 0, -5, 4, 0, -6, 5, 0, -7, 6, 0, 0, 3, 0, 0, 3, 0, -3, 19, 0, 0, 3, 0, 0, 4, 0, 0, 4, 0, -1, 12, 0, -2, 13, 0, 0, 5, 0, 0, 5, 0, -1, 14, 0, -2, 15, 0, 0, 6, 0, 0, 6, 0, -1, 16, 0, -2, 17, 0, 0, 7, 0, 0, 7, 0, -1, 10, 0, 0, 8, 0, 0, 8, 0, -1, 11, 0, 0, 9, 0, -2, 18, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, -2, 20, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, -2, 21, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, -2, 22, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, -2, 23, 0, 11, 1, 59], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 20, 21, 22, 23], [1, 2, 2, 2, 1, 2, 1, 2, 1, 2, 2, 1, 1, 1, 1], [0, 2, 3, 4, 0, 1, 0, 1, 0, 1, 5, 0, 0, 0, 0]]