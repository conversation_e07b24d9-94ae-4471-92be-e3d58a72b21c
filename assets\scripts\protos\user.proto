syntax = "proto3";
package pb;

// <p>门店信息</p>
message BusinessInfoResPb {
  // 唯一id
  string id = 1;
  // 门店名称
  string busName = 2;
  // 活动id
  string activityId = 3;
  // 活动开始时间戳
  int64 startTime = 4;
  // 活动结束时间戳
  int64 endTime = 5;
}

// 商家登录请求
message BusinessLoginReqPb {
  string account = 1;
  string password = 2;
  string token = 3;
}

// <p>门店登录响应</p>
message BusinessLoginResPb {
  // 免登录token
  string token = 1;
  string userId = 2;
  string busId = 3;
}

// <p>服务器时间请求类</p>
message ServerTimeReqPb {
  // 客户端时间
  string clientTime = 1;
}

// <p>服务器时间响应类</p>
message ServerTimeResPb {
  // 客户端时间
  string clientTime = 1;
  // 服务器时间
  string serverTransmitTime = 2;
  // 服务器发送时间
  string serverSendTime = 3;
}

// <p>类描述</p>
message UserAddAwardRes {
  string awardId = 1;
  string awardUrl = 2;
  string awardName = 3;
  string address = 4;
  string code = 5;
  string createTime = 6;
}

// <p>类描述</p>
message UserAwardHistory {
  repeated UserAddAwardRes items = 1;
}

// <p>类描述</p>
message UserGameHistory {
  repeated UserGameHistoryItem item = 1;
}

// <p>类描述</p>
message UserGameHistoryItem {
  string rank = 1;
  string businessName = 2;
  string createTime = 3;
}

// <p>类描述</p>
message UserLoginReqPb {
  string token = 1;
  string roomId = 2;
}

// <p>类描述</p>
message UserLoginResPb {
  bool result = 1;
}

message RoomStatusPushPb {
  string json = 1;
}