import { _decorator, Component, Label, Node } from 'cc';
import { ViewBase } from '../../scripts/libs/manager/ViewBase';
const { ccclass, property } = _decorator;

@ccclass('ViewRoundToast')
export class ViewRoundToast extends ViewBase {

    @property(Label)
    private lbToast: Label = null;

    public setData(data: any): void {
        this.lbToast.string = data.desc;
        this.scheduleOnce(() => {
            this.closeSelf();
        }, 2)
        this.removeBg()
    }
    
}


