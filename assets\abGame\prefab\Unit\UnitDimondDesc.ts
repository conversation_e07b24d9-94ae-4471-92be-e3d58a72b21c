import { _decorator, Component, Label, log, Node, Sprite } from 'cc';
import { ConfigHelper } from 'db://assets/scripts/config/ConfigHelper';
import { xcore } from 'db://assets/scripts/libs/xcore';
const { ccclass, property } = _decorator;

@ccclass('UnitDimondDesc')
export class UnitDimondDesc extends Component {

    @property(Sprite)
    private sprIcon: Sprite = null;

    @property(Label)
    private lbName: Label = null;

    @property(Label)
    private lbNum: Label = null;

    @property(Label)
    private lbProp: Label = null;

    async setData(data: any, dimonddata) {
        this.lbName.string = data.name;
        this.refreshImg(`./res/image/${data.path}/${data.picture}`);
        this.lbNum.string = '持有数量：' + dimonddata.num.toString();
        this.lbProp.string = '属性：' + dimonddata.txt
        log('dimonddata', dimonddata)
    }
    async refreshImg(path: string) {
        let sf = await xcore.res.bundleLoadSprite('resources', path)
        this.sprIcon.spriteFrame = sf;
    }
}


