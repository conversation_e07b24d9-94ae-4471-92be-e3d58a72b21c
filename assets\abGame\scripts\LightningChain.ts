import { _decorator, Component, Node, Prefab, math, instantiate, v3, Vec3 } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('LightningChain')
export class LightningChain extends Component {
    @property(Node)
    pointsNode: Node;
    @property(Prefab)
    lightPrefab: Prefab;

    lightNodes: Node[] = []

    start() {
        this.createLightningByPoints();
    }

    getRandom(min: number, max: number) {
        return Math.random() * (max - min) + min;
    }

    onClick_updatePointPos(){
        //测试， 随机更新坐标点位置
        this.pointsNode.children.forEach(point => {
            let pos = point.position.clone();
            pos.x = this.getRandom(-500, 500);
            pos.y = this.getRandom(-500, 500);
            point.setPosition(pos);
        });

        this.createLightningByPoints();
    }

    createLightningByPoints() {
        this.lightNodes.forEach(n => {
            n.destroy();
        })
        this.lightNodes = [];

        this.pointsNode.children.forEach((point1, index1) => {
            let pos1 = point1.position.clone();
            this.pointsNode.children.forEach((point2, index2) => {
                let pos2 = point2.position.clone();
                if (index1 + 1 == index2) {
                    this.lightning(pos1, pos2);
                }
            })
        })
    }

    lightning(startPos: Vec3, endPos: Vec3) {
        let distance = Vec3.distance(startPos, endPos)
        let direction = endPos.subtract(startPos);
        let newFacingAngle = Math.atan2(direction.y, direction.x) * 180 / Math.PI;

        let lightNode = instantiate(this.lightPrefab);
        lightNode.setParent(this.node.parent);
        lightNode.setPosition(startPos)
        lightNode.eulerAngles = v3(0, 0, newFacingAngle)
        lightNode.getChildByPath('effect').scale = v3(1, distance / 100, 1)

        this.lightNodes.push(lightNode);
    }
}


