import { Button, Camera, Canvas, EventTouch, Graphics, JsonAsset, Label, Node, RichText, Sprite, SpriteFrame, TweenSystem, UITransform, Vec2, Vec3, color, director, game, log, macro, tween, v2, v3, view, warn } from "cc"
 
import Tool from "./Tool"
import { xcore } from "../xcore"
import { E_UILAYER } from "../../ConstGlobal";
interface ICheckAbleGuide {
    checkIfAbleGuide(stepName: string): boolean;
}

//ban引导策略1 （是否已引导过）  
export class CheckGuidedStrategy implements ICheckAbleGuide {
    constructor(func: (stepName: string) => boolean) {
        this.checkIfAbleGuide = func;
    }
    checkIfAbleGuide(stepName: string): boolean {
        return false
    }
}

//ban引导策略2 （测试）  
export class CheckTestStrategy implements ICheckAbleGuide {
    constructor(func: (stepName: string) => boolean) {
        this.checkIfAbleGuide = func;
    }
    checkIfAbleGuide(stepName: string): boolean {
        return false
    }
}

/**使用案例 */
// Guide.Instance.initGuide(guideConfig, (StepName) => {
//     let isAbleGuide = false;
//     /**引导记录本地存 储  */
//     if (!Data.stroageData) {
//         error('stroageData null')


//     } else {
//         if (!Data.stroageData.guideSteps) {
//             Data.stroageData.guideSteps = [];
//             isAbleGuide = true;
//         }
//         if (Data.stroageData.guideSteps.includes(StepName)) {
//             isAbleGuide = false;
//         }
//         else {
//             isAbleGuide = true;
//         }

//         if (DEBUG) {
//             isAbleGuide = true;
//         }
//     }



//     if (isAbleGuide) {
//         !Data.stroageData.guideSteps.includes(StepName) && Data.stroageData.guideSteps.push(StepName)
//         App.getInstance().updateOperation();
//         switch (StepName) {
//             case C_GuideStep.TargetBtnQuestion:
//                 this.ndTongzi.getComponent(Button).enabled = false;
//                 break;

//             default:
//                 break;
//         }
//     }

//     log('isAbleGuide', isAbleGuide)
//     return isAbleGuide;
// });
// xcore.event.addEventListener(E_EVENT.ShowGuide, async (eventName: string, stepData: IGuidStepData) => {




//     Guide.Instance.checkIfGuide(stepData, Data.stroageData.guideSteps);





// }, this)
// xcore.event.addEventListener(E_EVENT.HideGuide, (eventName: string, hideStepName: string) => {
//     log('hidestep:', hideStepName)
//     switch (hideStepName) {
//         case C_GuideStep.DescCaishen:
//             xcore.event.raiseEvent(E_EVENT.ShowGuide, {
//                 StepName: C_GuideStep.TargetCaishenBi,
//                 TargetNd: this.lbGold.node.parent,
//                 TouchCb: () => {
//                     Guide.Instance.hideGuide();
//                 },
//             } as IGuidStepData)
//             break;
//         case C_GuideStep.TargetCaishenBi:
//             xcore.event.raiseEvent(E_EVENT.ShowGuide, {
//                 StepName: C_GuideStep.TargetYuanqi,
//                 TargetNd: this.lbYuanqi.node.parent,
//                 TouchCb: () => {
//                     Guide.Instance.hideGuide();
//                 },
//             } as IGuidStepData)
//             break;
//         case C_GuideStep.TargetYuanqi:
//             xcore.event.raiseEvent(E_EVENT.ShowGuide, {
//                 StepName: C_GuideStep.TargetBtnToBrun,
//                 TargetNd: this.btnPlay.node,
//                 TouchCb: () => {
//                     this.btnPlay.node.emit('click');
//                     // Guide.Instance.hideGuide();

//                 },
//             } as IGuidStepData)
//             break;
//         /* case C_GuideStep.DescTask:
//             xcore.event.raiseEvent(E_EVENT.ShowGuide, {
//                 StepName: C_GuideStep.TargetBtnExchange,
//                 TargetNd: this.btnExchange.node,
//                 TouchCb: () => {
//                     this.btnExchange.node.emit('click');
//                     Guide.Instance.hideGuide();

//                 },
//             } as IGuidStepData)
//             break; */
//         case C_GuideStep.TargetBtnQuestion:
//             this.ndTongzi.getComponent(Button).enabled = true;
//             break
//         default:
//             break;
//     }
// })

/**触发 */
// xcore.event.raiseEvent('ShowGuide', {
//     StepName: 'DescCaishen',
//     TouchCb: () => {
//         Guide.Instance.hideGuide();
//     },
//     AutoCb: () => {
//         Guide.Instance.hideGuide();
//     },
// } as IGuidStepData)

export interface IGuid {
    /**引导步骤名称 */
    StepName: string
    /**引导显示前提条件 指需要在ShowAfter列表引导完成显示后才能显示 */
    ShowAfter?: string[]
    /**蒙蔽透明度 0~255 */
    MaskOpacity?: number
    /**是否全屏触控 注：如没有设置TargetNode，则默认可全屏点击*/
    IsFullTouch?: boolean
    /**是否设置长时间不触发回调时自动关闭引导时间   */
    AutoHideGuideTime?: number


    /**延迟显示引导时间 延迟时间内阻断了全局点击 */
    DelayShowGuide?: number
    /**是否延迟触控生效时间 可用于阻断点击 不影响引导显示 */
    DelayTouchGuide?: number


    /**目标节点 */
    TargetNode?: Node
    /**目标节点延迟显示时间 */
    DelayShowTargetNd?: number


    /**展示手指 图片名称 key */
    FingerSpriteFrame?: string
    /**是否延迟显示手指时间 /ms */
    DelayShowFinger?: number
    /**手指位置设置偏移 */
    FingerOffsetPos?: Vec3
    /**手指层级 */
    FingerIndex?: number




    /**富文本文案内容  */
    LbRichTxt?: string
    /**富文本层级 */
    RichTxtIndex?: number
    /**富文本增加属性 */
    PropertyRichTxt?: any
    /**富文本是否要字体打字效果 */
    IsRichTxtShowEff?: boolean
    /**是否点击可取消打字效果 */
    IsClickOffRichTxtShowEff?: boolean



    /**简单文字文案内容 */
    LbTxt?: string
    /**文本层级 */
    TxtIndex?: number
    /**文本增加属性 */
    PropertyTxt?: any
    /**label是否要字体打字效果 */
    IsTxtShowEff?: boolean
    /**是否点击可取消打字效果 */
    IsClickOffTxtShowEff?: boolean


    /**是否有跳过按钮 */
    JumpBtnSpriteFrame?: string
    JumpBtnOffsetPos?: Vec3
    JumpBtnIndex?: number



    /**背景图 建议不超过三张 */
    Frame1SpriteFrame?: string
    Frame2SpriteFrame?: string
    Frame3SpriteFrame?: string
    /**背景图 属性 */
    PropertyFrame1?: any
    PropertyFrame2?: any
    PropertyFrame3?: any
    /**位置设置偏移 */
    Frame1OffsetPos?: Vec3
    Frame2OffsetPos?: Vec3
    Frame3OffsetPos?: Vec3
    /**背景图层级 */
    Frame1Index?: number
    Frame2Index?: number
    Frame3Index?: number



    /**置入节点 */
    InjectNd?: Node
    /**点击触发事件 */
    TouchCb?: Function
    /**跳过触发 */
    JumpCb?: Function
    /**自动关闭引导回调 */
    AutoCb?: Function

}
export interface IGuidStepData {
    /**引导名称 */
    StepName: string,
    /**目标节点 */
    TargetNd?: Node,
    /**引入其他节点作为引导画面 */
    InjectNd?: Node,

    /**引导前回调 返回是否可引导bool */
    GuideReadyCb?: Function,
    /**点击回调 */
    TouchCb?: Function,
    /**跳过回调 */
    JumpCb?: Function
    /**自动关闭引导回调 */
    AutoCb?: Function
}
export default class Guide {
    private static _instance: Guide = null;
    public static get Instance(): Guide {
        if (!this._instance) {
            this._instance = new Guide();
        }
        return this._instance
    }



    private _txtEffTimer: any = null;
    private _richtxtEffTimer: any = null;

    /**打字机效果单个字显示耗时 */
    private _txtEffSpeedTime: number = 100
    private _richtxtEffSpeedTime: number = 100

    /**引导层级groupIndex */
    private _camGroupIndex: number = 1 << 1;
    private _orgTargetNdGroupIndex: number = 0;
    private _orgTargetNdParent: Node = null;
    private _orgTargetNdPos: Vec3 = null;

    private _layerIndex: number = E_UILAYER.GUIDE_LAYER;



    //缓存图片组
    private _spriteFrameGroups: any;

    //缓存节点
    private _ndRoot: Node
    private _ndInject: Node
    private _ndFinger: Node
    private _ndMask: Node
    private _ndFrame1: Node
    private _ndFrame2: Node
    private _ndFrame3: Node
    private _ndJump: Node
    private _lbTxt: Label
    private _lbRichTxt: RichText

    //父节点
    private _canvas: Node = null;
    //引导层相机
    private _guideCamera: Camera

    //计时器
    private _autoHideGuideTimer: any
    private _delayShowGuideTimer: any
    private _delayTouchGuideTimer: any
    private _delayShowFingerTimer: any
    private _delayShowTargetNdTimer: any


    //所有引导配置
    private _guideConfig: { [key: string]: IGuid };

    //当前引导配置
    private _guideInfo: IGuid = null;


    private checkGuidedStrategy: ICheckAbleGuide;
    private tempCheckGuidedStrategy: ICheckAbleGuide;


    constructor() {
        log("引导层摄像机layer 掩码：", this._camGroupIndex.toString(2));
        this._canvas = director.getScene().getChildByName('Canvas');
        this.checkGuidedStrategy = new CheckGuidedStrategy(() => false);
    }

    //新手引导初始化
    public initGuide(config: { [key: string]: IGuid }, checkHadGuidedStrategy: ICheckAbleGuide) {
        this._guideConfig = config;
        this.setCheckAbleGuideFunc(checkHadGuidedStrategy);
    }
    /**
     * 动态设置判断是否可引导策略
     * @param checkHadGuidedStrategy 
     * @param isOnce 是否一次性判断
     */
    public setCheckAbleGuideFunc(checkHadGuidedStrategy: ICheckAbleGuide, isOnce: boolean = false) {
        if (isOnce) {
            this.tempCheckGuidedStrategy = this.checkGuidedStrategy;
        }
        this.checkGuidedStrategy = checkHadGuidedStrategy;
    }
    private backTempCheckGuideFunc() {
        if (this.tempCheckGuidedStrategy) {
            let temp = this.tempCheckGuidedStrategy;
            this.tempCheckGuidedStrategy = null;
            this.checkGuidedStrategy = temp;

        }
    }
    /** 
        * 缓存新手引导需要用到的图片 主要包括： 引导弹窗背景图 跳过按钮图 finger图
        * @param sprName 图片名称
        * @param sf 精灵图
        */
    public initGuideSfriteFrame(sprName: string, sf: SpriteFrame) {
        if (sf && sf instanceof SpriteFrame && sprName) {
            if (!this._spriteFrameGroups) {
                this._spriteFrameGroups = {};
            }
            this._spriteFrameGroups[sprName] = sf;
        } else {
            warn('initGuideSfriteFrame err')
        }
    }


    /**触发引导流程 */
    public checkIfGuide(stepData: IGuidStepData, finishedSteps?: string[]) {

        //已完成过的引导避免重复出现
        if (!this.checkGuidedStrategy.checkIfAbleGuide(stepData.StepName)) {
            this.backTempCheckGuideFunc();
            warn('已完成过引导', stepData.StepName)
            this.hideGuide();
            stepData.GuideReadyCb && stepData.GuideReadyCb(false);
            return
        }
        this.backTempCheckGuideFunc();
        //配置文件错误
        if (!this._guideConfig || !this._guideConfig[stepData.StepName]) {
            warn('引导配置文件错误', stepData.StepName)
            this.hideGuide();
            stepData.GuideReadyCb && stepData.GuideReadyCb(false);
            return
        }

        //引导需要在这些步骤完成后才能显示
        let guideInfo = this._guideConfig[stepData.StepName];
        if (finishedSteps && guideInfo.ShowAfter) {
            for (let i = 0; i < guideInfo.ShowAfter.length; i++) {
                if (!finishedSteps.includes(guideInfo.ShowAfter[i])) {
                    warn(`${stepData.StepName} should show after ${guideInfo.ShowAfter} finished`);
                    this.hideGuide();
                    stepData.GuideReadyCb && stepData.GuideReadyCb(false);
                    return
                }
            }
        }

        /************************************************开始引导**************************************************/
        stepData.GuideReadyCb && stepData.GuideReadyCb(true);
        stepData.TargetNd && (guideInfo.TargetNode = stepData.TargetNd);
        stepData.TouchCb && (guideInfo.TouchCb = stepData.TouchCb);
        stepData.JumpCb && (guideInfo.JumpCb = stepData.JumpCb);
        stepData.AutoCb && (guideInfo.AutoCb = stepData.AutoCb);
        this.showGuide(guideInfo);


    }


    /**引导显示 */
    showGuide(guideInfo: IGuid) {

        this.clearTimer();
        if (!this._guideCamera) {
            this._guideCamera = new Node().addComponent(Camera);
            this._guideCamera.node.name = 'guideCamera';
            this._guideCamera.visibility = this._camGroupIndex;
            this._guideCamera.priority = 1;
            this._guideCamera.projection = Camera.ProjectionType.ORTHO;
            let orthoHeight = this._canvas.getChildByName('Camera')?.getComponent(Camera).orthoHeight;
            if (!orthoHeight) {
                warn('Camera no find');
                return
            }
            this._guideCamera.orthoHeight = orthoHeight;
            this._guideCamera.near = 0;
            this._guideCamera.far = 1000;
            this._guideCamera.clearColor = color(0, 0, 0, 0);
            this._guideCamera.clearFlags = Camera.ClearFlag.DONT_CLEAR;
            this._guideCamera.node.parent = this._canvas;
        }


        if (this._ndRoot && this._ndRoot.isValid && this._ndRoot.active) {
            this.hideGuide();
        }

        //延迟引导时间
        if (guideInfo.DelayShowGuide) {
            this.setMask(0);
            this._delayShowGuideTimer = setTimeout(() => {
                guideInfo.DelayShowGuide = null;
                clearTimeout(this._delayShowGuideTimer);
                this._delayShowGuideTimer = null;
                this.showGuide(guideInfo);
            }, guideInfo.DelayShowGuide);
            return
        }

        //引导需要有引导步骤名称
        if (!guideInfo.StepName) {
            warn('stepname undefined');
            return
        }
        this._delayShowGuideTimer && clearTimeout(this._delayShowGuideTimer);

        //蒙版
        this.setMask(guideInfo.MaskOpacity);
        if (!this._ndRoot || !this._ndRoot.isValid) {
            this._ndRoot = new Node().addComponent(UITransform).node;
            this._ndRoot.getComponent(UITransform).width = view.getVisibleSize().width;
            this._ndRoot.getComponent(UITransform).height = view.getVisibleSize().height;
            this._ndRoot.parent = this._canvas;
            this._ndRoot.layer = this._camGroupIndex;
            this._ndRoot.name = 'ndRoot'
            Tool.setChildrenNodeSortByPriority(this._ndRoot, this._layerIndex + 1);
        }
        this._ndRoot.active = true;




        //窗体
        if (guideInfo.Frame1SpriteFrame) {
            let offsetPos = new Vec3();
            if (guideInfo.PropertyFrame1.x) {
                offsetPos.x = guideInfo.PropertyFrame1.x;
            }
            if (guideInfo.PropertyFrame1.y) {
                offsetPos.y = guideInfo.PropertyFrame1.y;
            }
            this.setFrame(1, guideInfo.Frame1SpriteFrame, guideInfo.PropertyFrame1, offsetPos, guideInfo.Frame1Index);
        }
        if (guideInfo.Frame2SpriteFrame) {
            let offsetPos = new Vec3();
            if (guideInfo.PropertyFrame2.x) {
                offsetPos.x = guideInfo.PropertyFrame2.x;
            }
            if (guideInfo.PropertyFrame2.y) {
                offsetPos.y = guideInfo.PropertyFrame2.y;
            }
            this.setFrame(2, guideInfo.Frame2SpriteFrame, guideInfo.PropertyFrame2, offsetPos, guideInfo.Frame2Index);
        }
        if (guideInfo.Frame3SpriteFrame) {
            let offsetPos = new Vec3();
            if (guideInfo.PropertyFrame3.x) {
                offsetPos.x = guideInfo.PropertyFrame3.x;
            }
            if (guideInfo.PropertyFrame3.y) {
                offsetPos.y = guideInfo.PropertyFrame3.y;
            }
            this.setFrame(3, guideInfo.Frame3SpriteFrame, guideInfo.PropertyFrame3, offsetPos, guideInfo.Frame3Index);
        }
        //文案
        this.setTxt(guideInfo.LbTxt, guideInfo.PropertyTxt, guideInfo.TxtIndex, guideInfo.IsTxtShowEff);
        this.setRichTxt(guideInfo.LbRichTxt, guideInfo.PropertyRichTxt, guideInfo.RichTxtIndex, guideInfo.IsRichTxtShowEff);
        //跳过
        this.setJump(!!guideInfo.JumpBtnSpriteFrame, guideInfo.JumpBtnSpriteFrame, guideInfo.JumpBtnOffsetPos, guideInfo.JumpBtnIndex);
        //目标节点
        this.setTargetNd(guideInfo.TargetNode, guideInfo.DelayShowTargetNd);
        //手指
        this.setFinger(!!guideInfo.FingerSpriteFrame, guideInfo.FingerSpriteFrame, guideInfo.TargetNode, guideInfo.FingerOffsetPos, guideInfo.FingerIndex, guideInfo.DelayShowFinger);
        //延迟回调
        this._delayTouchGuideTimer = setTimeout(() => {
            this._guideInfo = guideInfo;
            guideInfo.DelayTouchGuide = 0;
            clearTimeout(this._delayTouchGuideTimer);

            //自动关闭引导，当长时间不触发点击，可设置自动关闭时间
            if (guideInfo.AutoHideGuideTime) {
                this._autoHideGuideTimer = setTimeout(() => {
                    clearTimeout(this._autoHideGuideTimer);
                    if (this._ndRoot && this._ndRoot.active) {
                        this._guideInfo.AutoCb && this._guideInfo.AutoCb();
                        this.hideGuide();

                    }
                }, guideInfo.AutoHideGuideTime);
            }

        }, guideInfo.DelayTouchGuide || 0);


    }


    /**
    * 设置蒙版
    * @param opacity 透明度 默认210
    */
    setMask(opacity: number = 210) {
        log('setMask', opacity)
        if (!this._ndMask || !this._ndMask.isValid) {
            this._ndMask = new Node().addComponent(UITransform).node;
            this._ndMask.parent = this._canvas;
            this._ndMask.getComponent(UITransform).width = view.getVisibleSize().width;
            this._ndMask.getComponent(UITransform).height = view.getVisibleSize().height;
            this._ndMask.addComponent(Graphics);
            this._ndMask.layer = this._camGroupIndex;
            this._ndMask.name = 'ndMask';
            Tool.setChildrenNodeSortByPriority(this._ndMask, this._layerIndex);

        }
        this._ndMask.off(Node.EventType.TOUCH_END, this.onTouch, this);
        this._ndMask.on(Node.EventType.TOUCH_END, this.onTouch, this);
        let gp = this._ndMask.getComponent(Graphics);
        gp.clear();
        gp.fillColor = color(0, 0, 0, opacity);
        gp.rect(-view.getVisibleSize().width / 2, -view.getVisibleSize().height / 2, view.getVisibleSize().width, view.getVisibleSize().height);
        gp.fill();
        this._ndMask.active = true;
    }

    /**
     * 
     * @param index     背景板节点序号 1~3
     * @param sf        背景板图片名称
     * @param property  设置 sprite属性||UITransform属性 
     * @param offsetPos 位置偏移
     * @param zIndex    层级
     * @returns 
     */
    setFrame(index: number, sf?: string, property?: any, offsetPos?: Vec3, zIndex?: number) {
        if (!sf) {
            return
        }
        let ndFrame = this[`_ndFrame${index}`] as Node;
        if (!ndFrame || !ndFrame.isValid) {
            ndFrame = new Node();
            ndFrame.addComponent(UITransform);
            ndFrame.addComponent(Sprite);
            ndFrame.parent = this._ndRoot;
            ndFrame.name = `ndFrame${index}`;
            this[`_ndFrame${index}`] = ndFrame;
            this[`_ndFrame${index}`].layer = this._camGroupIndex;

        }
        Tool.setChildrenNodeSortByPriority(ndFrame, zIndex || 3);
        if (!this._spriteFrameGroups[sf]) {
            warn('ndFrame spriteframe null', sf)
        } else {
            ndFrame.getComponent(Sprite).spriteFrame = this._spriteFrameGroups[sf];
        }
        ndFrame.setPosition(offsetPos || v3());
        for (const key in property) {
            if (Object.prototype.hasOwnProperty.call(property, key)) {
                const element = property[key];
                if (typeof ndFrame.getComponent(UITransform)[key] != "undefined") {
                    ndFrame.getComponent(UITransform)[key] = element;
                }
                else if (typeof ndFrame.getComponent(Sprite)[key] != 'undefined') {
                    ndFrame.getComponent(Sprite)[key] = element;
                }
                else {
                    ndFrame.getComponent(Sprite).spriteFrame[key] = element;
                }
            }
        }
        ndFrame.active = true;
    }

    setTxt(txt: string, property?: any, zIndex?: number, isShowEff?: boolean) {
        if (!txt) return
        if (!this._lbTxt || !this._lbTxt.node?.isValid) {
            this._lbTxt = new Node().addComponent(Label);
            this._lbTxt.node.parent = this._ndRoot;


            this._lbTxt.node.layer = this._camGroupIndex;
            this._lbTxt.node.name = 'txt';
            Tool.setChildrenNodeSortByPriority(this._lbTxt.node, zIndex || 20)
        }
        for (const key in property) {
            if (Object.prototype.hasOwnProperty.call(property, key)) {
                const element = property[key];

                if (typeof this._lbTxt[key] != "undefined") {
                    this._lbTxt[key] = element

                } else {

                    if (key == 'x') {
                        this._lbTxt.node.setPosition(v3(element, this._lbTxt.node.getPosition().y))
                    }
                    else if (key == 'y') {
                        this._lbTxt.node.setPosition(v3(this._lbTxt.node.getPosition().x, element))
                    }
                    else {
                        this._lbTxt.node.getComponent(UITransform)[key] = element;
                    }


                }


            }
        }
        if (isShowEff) {
            this.doTxtEff(txt);
        } else {
            this._lbTxt.string = txt;
        }
        this._lbTxt.node.active = true;
    }
    doTxtEff(str: string) {
        if (!str) {
            return
        }
        let charArr = str.split('');
        let charIdx = 0;

        this._txtEffTimer && clearInterval(this._txtEffTimer);
        this._txtEffTimer = setInterval(() => {
            if (charIdx >= charArr.length) {
                this._txtEffTimer && clearInterval(this._txtEffTimer);
                this._txtEffTimer = null;
            } else {
                charIdx += 1;
                this._lbTxt.string = charArr.slice(0, charIdx).join('');
            }
        }, this._txtEffSpeedTime);

    }

    setRichTxt(txt: string, property?: any, zIndex?: number, isShowEff?: boolean) {
        if (!txt) return
        if (!this._lbRichTxt || !this._lbRichTxt?.node.isValid) {
            this._lbRichTxt = new Node().addComponent(UITransform).addComponent(RichText);
            this._lbRichTxt.node.parent = this._ndRoot;
            this._lbRichTxt.string = ''
            this._lbRichTxt.node.name = 'rtx'
            this._lbRichTxt.node.layer = this._camGroupIndex;
            Tool.setChildrenNodeSortByPriority(this._lbRichTxt.node, zIndex || 20);
        }

        for (const key in property) {
            if (Object.prototype.hasOwnProperty.call(property, key)) {
                const element = property[key];
                if (typeof this._lbRichTxt[key] != "undefined") {
                    this._lbRichTxt[key] = element

                } else {
                    if (key == 'x') {
                        this._lbRichTxt.node.setPosition(v3(element, this._lbRichTxt.node.getPosition().y))
                    }
                    else if (key == 'y') {
                        this._lbRichTxt.node.setPosition(v3(this._lbRichTxt.node.getPosition().x, element))
                    }
                    else {
                        this._lbRichTxt.node.getComponent(UITransform)[key] = element;

                    }
                }

            }
        }
        if (isShowEff) {
            this.doRichTxtEff(txt);
        } else {
            this._lbRichTxt.string = txt;
        }

        this._lbRichTxt.node.active = true;
    }
    doRichTxtEff(str: string = "") {
        const regex = /<.+?\/?>/g; // 匹配尖括号标签
        const matchArr = str.match(regex);
        const specialChar = "│";
        const replaceStr = str.replace(regex, specialChar); // 标签数组
        const textArr: string[] = replaceStr.split(specialChar); // 文字数组
        const strArr: string[] = []; // 存放处理过的文字数组
        let paraNum = 0; // 待替换参数个数
        for (let text of textArr) {
            // 非空字符替换成类似 $[0-n] 参数
            if (text !== "") {
                text = `$[${paraNum}]`;
                paraNum += 1;
            }
            strArr.push(text);
        }
        let templetStr: string = strArr.join(specialChar); // 数组转成待替换字符串
        for (let index = 0; index < textArr.length; index++) {
            // 转换代替换字符串之后, 删除文字数组多余空字符
            if (textArr[index] === "") {
                textArr.splice(index, 1);
                index = index - 1;
            }
        }
        while (templetStr.search(specialChar) !== -1) {
            // 数组转成的字符串原本 '特殊字符' 位置都是富文本标签的位置, 替换回标签
            if (matchArr[0]) {
                templetStr = templetStr.replace(specialChar, matchArr[0].toString());
                matchArr.splice(0, 1);
            } else {
                templetStr = templetStr.replace(specialChar, "");// 空字符串替换,防止死循环
                warn("matchArr not enough");
            }
        }
        const lastStrArr: string[] = []; // 转换后富文本数组
        const arrayParm: string[] = new Array(paraNum).fill(""); // 替换参数数组
        for (let i = 0; i < textArr.length; i++) {
            for (const text of textArr[i]) {
                arrayParm[i] = arrayParm[i] + text;
                let replaceStr1 = templetStr;
                for (let index = 0; index < paraNum; index++) {
                    replaceStr1 = replaceStr1.replace(`$[${index}]`, arrayParm[index]);
                }
                lastStrArr.push(replaceStr1);
            }
        }
        let lastStrIndex = 0;
        this._richtxtEffTimer && clearInterval(this._richtxtEffTimer);
        this._richtxtEffTimer = setInterval(() => {
            if (lastStrIndex >= lastStrArr.length) {
                this._richtxtEffTimer && clearInterval(this._richtxtEffTimer);
                this._richtxtEffTimer = null;
                return;
            }
            this._lbRichTxt.string = lastStrArr[lastStrIndex];
            lastStrIndex += 1;
        }, this._richtxtEffSpeedTime);
    }
    /**
     * 设置跳过按钮
     * @param isShow 是否显示跳过
     * @param sf 跳过精灵图
     * @param offsetPos 位置
     */
    setJump(isShow: boolean, sf: string, offsetPos?: Vec3, zIndex?: number) {
        if (!isShow) {
            this._ndJump && (this._ndJump.active = false);
            return
        }
        if (!this._ndJump || !this._ndJump.isValid) {
            this._ndJump = new Node().addComponent(UITransform).node;
            this._ndJump.addComponent(Sprite);
            this._ndJump.layer = this._camGroupIndex;
            this._ndJump.parent = this._ndRoot;
            this._ndJump.name = 'jump'
        }
        if (!this._spriteFrameGroups[sf]) {
            warn('jump spriteframe null', sf)
        } else {
            this._ndJump.getComponent(Sprite).spriteFrame = this._spriteFrameGroups[sf]
        }

        this._ndJump.setPosition(offsetPos || v3());

        Tool.setChildrenNodeSortByPriority(this._ndJump, zIndex || 30)
        this._ndJump.active = true;

    }

    /**
     * 手指
     * @param isShow 
     */
    setFinger(isShow: boolean, sf: string, targetNd: Node, offsetPos?: Vec3, zIndex?: number, DelayShowFinger?: number) {
        if (!isShow || !targetNd || !this._spriteFrameGroups[sf]) {
            this._ndFinger && (this._ndFinger.active = false);
            return
        }
        if (!this._ndFinger) {
            this._ndFinger = new Node().addComponent(UITransform).node;
            this._ndFinger.addComponent(Sprite);
            this._ndFinger.parent = this._ndRoot;
            this._ndFinger.layer = this._camGroupIndex;
            this._ndFinger.name = 'finger';
        }
        if (DelayShowFinger) {
            this._ndFinger.active = false;
            this._delayShowFingerTimer = setTimeout(() => {
                this._ndFinger.active = true;
                clearTimeout(this._delayShowFingerTimer)
            }, DelayShowFinger);
        } else {
            this._ndFinger.active = true;
        }
        let targetPos = this._ndRoot.getComponent(UITransform).convertToNodeSpaceAR(targetNd.getWorldPosition());
        offsetPos && (targetPos.x += offsetPos.x);
        offsetPos && (targetPos.y += offsetPos.y);
        this._ndFinger.setPosition(targetPos || v3(1, 1));
        Tool.setChildrenNodeSortByPriority(this._ndFinger, zIndex || 40);
        if (!this._spriteFrameGroups[sf]) {
            warn('Finger spriteframe null', sf);
        } else {
            this._ndFinger.getComponent(Sprite).spriteFrame = this._spriteFrameGroups[sf];
        }

        TweenSystem.instance.ActionManager.removeAllActionsFromTarget(this._ndFinger);

        tween(this._ndFinger)
            .repeatForever(
                tween(this._ndFinger)
                    .to(1, { position: v3(targetPos.x - 60, targetPos.y + 60), scale: v3(1, 1) }, { easing: 'cubicInOut' })
                    .to(1.2, { position: v3(targetPos.x, targetPos.y - 20), scale: v3(1.2, 1.2) }, { easing: 'sineOut' })
            ).start();
    }
    /**
     * 
     * @param TargetNode 目标节点
     * @param DelayShowFinger 延迟显示时间
     * @returns 
     */
    setTargetNd(TargetNode?: Node, DelayShowTaregetNdTime?: number) {
        if (!TargetNode || !TargetNode.isValid || !TargetNode.active) {
            return
        }
        if (DelayShowTaregetNdTime) {

            this._delayShowTargetNdTimer = setTimeout(() => {
                this.showTargetNd(TargetNode);
            }, DelayShowTaregetNdTime);
        }
        else {
            this.showTargetNd(TargetNode);
        }

    }

    showTargetNd(TargetNode: Node) {
        if (TargetNode && TargetNode.isValid) {
            !this._orgTargetNdGroupIndex && (this._orgTargetNdGroupIndex = TargetNode.layer);
            !this._orgTargetNdParent && (this._orgTargetNdParent = TargetNode.parent);
            !this._orgTargetNdPos && (this._orgTargetNdPos = TargetNode.position.clone());

            let targetPos = this._ndRoot.getComponent(UITransform).convertToNodeSpaceAR(TargetNode.getWorldPosition());
            TargetNode.parent = this._ndRoot;
            TargetNode.setPosition(targetPos);
            Tool.setChildrenNodeSortByPriority(TargetNode, 2);
            this.resetAbleButtonTouch(TargetNode, false)
            this._camGroupIndex && this.resetTargetNdLayer(TargetNode, this._camGroupIndex);

        }

        this._delayShowTargetNdTimer && clearTimeout(this._delayShowTargetNdTimer);
    }
    //设置节点及其下子节点层级
    resetTargetNdLayer(TargetNode: Node, layer: number) {

        for (let i = 0; i < TargetNode.children.length; i++) {
            let childNd = TargetNode.children[i];
            childNd.layer = layer;
            if (childNd.children.length > 0) {
                this.resetTargetNdLayer(childNd, layer);
            }
        }
        TargetNode.layer = layer;
    }
    //设置节点及其下子节点所有button组件enable属性
    resetAbleButtonTouch(TargetNode: Node, enabled: boolean) {
        if (!TargetNode || !TargetNode.isValid) return
        for (let i = 0; i < TargetNode.children.length; i++) {
            let childNd = TargetNode.children[i];
            childNd.getComponent(Button) && (childNd.getComponent(Button).enabled = enabled);
            if (childNd.children.length > 0) {
                this.resetAbleButtonTouch(childNd, enabled);
            }
        }
        TargetNode.getComponent(Button) && (TargetNode.getComponent(Button).enabled = enabled);
    }

    /**
     * 外部置入其他节点 如需其他额外效果可通过此方式置入
     * @param nd 节点
     * @param offsetPos 位置 
     */
    injectNode(nd: Node, offsetPos?: Vec3, zIndex?: number) {
        if (this._ndInject && this._ndInject.isValid) {
            this._ndInject.destroy();
            this._ndInject = null;
        }
        this._ndInject = nd;
        this._ndInject.parent = this._ndRoot;
        this._ndInject.position = offsetPos;
        Tool.setChildrenNodeSortByPriority(this._ndInject, zIndex || 0);
    }

    /**引导隐藏 */
    hideGuide() {
        this.targetNodeBackLayer();

        /**节点隐藏 */
        this._ndRoot && (this._ndRoot.active = false);
        this._ndMask && this._ndMask.off(Node.EventType.TOUCH_END, this.onTouch, this);
        if (this._ndFinger) {
            this._ndFinger.active = false;
            TweenSystem.instance.ActionManager.removeAllActionsFromTarget(this._ndFinger)
        };
        this._ndMask && (this._ndMask.active = false);
        this._ndFrame1 && (this._ndFrame1.active = false);
        this._ndFrame2 && (this._ndFrame2.active = false);
        this._ndFrame3 && (this._ndFrame3.active = false);
        this._ndJump && (this._ndJump.active = false);
        this._lbTxt && (this._lbTxt.node.active = false, this._lbTxt.string = '');
        this._lbRichTxt && (this._lbRichTxt.node.active = false, this._lbRichTxt.string = '');
        if (this._ndInject) {
            this._ndInject.destroy();
            this._ndInject = null;
        }

        //清空当前步骤引导数据引用
        if (this._guideInfo) {
            //清空计时器
            this.clearTimer();
            let stepName = this._guideInfo.StepName;
            this._guideInfo = null;
            this._txtEffTimer = null;
            this._delayShowGuideTimer = null;
            this._delayShowFingerTimer = null;
            this._delayShowTargetNdTimer = null;
            this._delayTouchGuideTimer = null;
            this._autoHideGuideTimer = null;
            this._orgTargetNdPos = null;
            xcore.event.raiseEvent('HideGuide', stepName);
        }


    }



    //触发引导点击
    onTouch(event: EventTouch) {
        log("ontouch")
        if (!this._guideInfo) {
            log('延迟点击')
            return
        }

        if (this._txtEffTimer) {
            if (this._guideInfo.IsClickOffTxtShowEff) {
                clearInterval(this._txtEffTimer);
                this._txtEffTimer = null;
                this._lbTxt.string = this._guideInfo.LbTxt;
                if (this._guideInfo.FingerSpriteFrame && this._guideInfo.TargetNode && this._ndFinger) {
                    this._delayShowFingerTimer && clearTimeout(this._delayShowFingerTimer);
                    this._ndFinger.active = true;
                }
                this.showTargetNd(this._guideInfo.TargetNode);
            }
            return
        }

        if (this._richtxtEffTimer) {
            if (this._guideInfo.IsClickOffRichTxtShowEff) {
                clearInterval(this._richtxtEffTimer);
                this._richtxtEffTimer = null;
                this._lbRichTxt.string = this._guideInfo.LbRichTxt;
                if (this._guideInfo.FingerSpriteFrame && this._guideInfo.TargetNode && this._ndFinger) {
                    this._delayShowFingerTimer && clearTimeout(this._delayShowFingerTimer);
                    this._ndFinger.active = true;
                }
                this.showTargetNd(this._guideInfo.TargetNode);
            }
            return
        }

        //跳过触发
        if (this._ndJump && this._guideInfo.JumpCb) {
            let pos = event.getUILocation();
            let targetPos = this._ndJump.getComponent(UITransform).convertToWorldSpaceAR(Vec3.ZERO);
            let checkWidth = Math.max(this._ndJump.getComponent(UITransform).width / 2, 50);
            let mag = pos.subtract(v2(targetPos.x, targetPos.y)).length();
            if (mag <= checkWidth) {
                this.onJumpCb();

                return
            }
        }

        //全屏点击 全屏任意位置可触发
        if (this._guideInfo.IsFullTouch || !this._guideInfo.TargetNode) {
            this.doTaregetNdTouch();
        }
        //目标点击 
        else {
            let pos = event.getUILocation();
            let targetPos = this._guideInfo.TargetNode.getWorldPosition();
            let checkWidth = Math.max(this._guideInfo.TargetNode.getComponent(UITransform).width / 2, 50);
            let mag = pos.subtract(v2(targetPos.x, targetPos.y)).length();

            if (mag <= checkWidth) {
                this.doTaregetNdTouch();
                return
            }
        }


    }
    //触发跳过操作
    onJumpCb() {
        this.targetNodeBackLayer();
        this._guideInfo.JumpCb && this._guideInfo.JumpCb();
        log('doJumpTouch')
    }
    doTaregetNdTouch() {
        this.targetNodeBackLayer();
        this._guideInfo.TouchCb && this._guideInfo.TouchCb()
        log('doTaregetNdTouch or fullTouch')
    }

    //targetNd返回原来layer 层级
    targetNodeBackLayer() {
        if (this._guideInfo && this._guideInfo.TargetNode) {
            this._orgTargetNdGroupIndex && this.resetTargetNdLayer(this._guideInfo.TargetNode, this._orgTargetNdGroupIndex);
            this._orgTargetNdParent && (this._guideInfo.TargetNode.parent = this._orgTargetNdParent);
            this._orgTargetNdPos && this._guideInfo.TargetNode.setPosition(this._orgTargetNdPos);
            this._guideInfo.TargetNode && this.resetAbleButtonTouch(this._guideInfo.TargetNode, true);
        }
        this._orgTargetNdParent = null;
        this._orgTargetNdPos = null
        this._orgTargetNdGroupIndex = null;
    }
    protected clearTimer() {

        this._richtxtEffTimer && clearInterval(this._richtxtEffTimer);
        this._txtEffTimer && clearInterval(this._txtEffTimer);
        this._delayShowGuideTimer && clearTimeout(this._delayShowGuideTimer);
        this._delayShowFingerTimer && clearTimeout(this._delayShowFingerTimer);
        this._delayShowTargetNdTimer && clearTimeout(this._delayShowTargetNdTimer);
        this._delayTouchGuideTimer && clearTimeout(this._delayTouchGuideTimer);
        this._autoHideGuideTimer && clearTimeout(this._autoHideGuideTimer);
    }

}

