{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "446c23cd-c63d-48ee-b69d-45a47efb1bb0", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "446c23cd-c63d-48ee-b69d-45a47efb1bb0@6c48a", "displayName": "huxian-attack_05", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "446c23cd-c63d-48ee-b69d-45a47efb1bb0", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "446c23cd-c63d-48ee-b69d-45a47efb1bb0@f9941", "displayName": "huxian-attack_05", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": -39.5, "offsetY": -11, "trimX": 17, "trimY": 36, "width": 137, "height": 150, "rawWidth": 250, "rawHeight": 200, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-68.5, -75, 0, 68.5, -75, 0, -68.5, 75, 0, 68.5, 75, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [17, 164, 154, 164, 17, 14, 154, 14], "nuv": [0.068, 0.07, 0.616, 0.07, 0.068, 0.82, 0.616, 0.82], "minPos": [-68.5, -75, 0], "maxPos": [68.5, 75, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "446c23cd-c63d-48ee-b69d-45a47efb1bb0@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "446c23cd-c63d-48ee-b69d-45a47efb1bb0@6c48a"}}