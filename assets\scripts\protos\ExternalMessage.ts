/* eslint-disable */
import Long from "long";
import _m0 from "protobufjs/minimal.js";

export const protobufPackage = "com.iohao.message";

/** 对外服数据协议 */
export interface ExternalMessage {
  /** 请求命令类型: 0 心跳，1 业务 */
  cmdCode: number;
  /** 协议开关，用于一些协议级别的开关控制，比如 安全加密校验等。 : 0 不校验 */
  protocolSwitch: number;
  /** 业务路由（高16为主, 低16为子） */
  cmdMerge: number;
  /** 响应码: 0:成功, 其他为有错误 */
  responseStatus: number;
  /** 验证信息（错误消息、异常消息），通常情况下 responseStatus == -1001 时， 会有值 */
  validMsg: string;
  /** 业务请求数据 */
  data: Uint8Array;
}

/** int 包装类 */
export interface IntValue {
  /** int 值 */
  value: number;
}

/** int list 包装类 */
export interface IntValueList {
  /** intList、intArray */
  values: number[];
}

/** long 包装类 */
export interface LongValue {
  /** long 值 */
  value: number;
}

/** long list 包装类 */
export interface LongValueList {
  /** longList、longArray */
  values: number[];
}

/** string 包装类 */
export interface StringValue {
  /** string 值 */
  value: string;
}

/** string list 包装类 */
export interface StringValueList {
  /** stringList、stringArray */
  values: string[];
}

/** bool 包装类 */
export interface BoolValue {
  /** bool 值 */
  value: boolean;
}

/** bool list 包装类 */
export interface BoolValueList {
  /** boolList、boolArray */
  values: boolean[];
}

/** int 包装类 -- 已经废弃的，由 IntValue 代替 */
export interface IntPb {
  /** int 值 */
  intValue: number;
}

/** int list 包装类 -- 已经废弃的，由 IntValueList 代替 */
export interface IntListPb {
  /** intList */
  intValues: number[];
}

/** long 包装类 -- 已经废弃的，由 LongValue 代替 */
export interface LongPb {
  /** long 值 */
  longValue: number;
}

/** long list 包装类 -- 已经废弃的，由 LongValueList 代替 */
export interface LongListPb {
  /** longList */
  longValues: number[];
}

function createBaseExternalMessage(): ExternalMessage {
  return { cmdCode: 0, protocolSwitch: 0, cmdMerge: 0, responseStatus: 0, validMsg: "", data: new Uint8Array(0) };
}

export const ExternalMessage = {
  encode(message: ExternalMessage, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.cmdCode !== 0) {
      writer.uint32(8).int32(message.cmdCode);
    }
    if (message.protocolSwitch !== 0) {
      writer.uint32(16).int32(message.protocolSwitch);
    }
    if (message.cmdMerge !== 0) {
      writer.uint32(24).int32(message.cmdMerge);
    }
    if (message.responseStatus !== 0) {
      writer.uint32(32).sint32(message.responseStatus);
    }
    if (message.validMsg !== "") {
      writer.uint32(42).string(message.validMsg);
    }
    if (message.data.length !== 0) {
      writer.uint32(50).bytes(message.data);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): ExternalMessage {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseExternalMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.cmdCode = reader.int32();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.protocolSwitch = reader.int32();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.cmdMerge = reader.int32();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.responseStatus = reader.sint32();
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.validMsg = reader.string();
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.data = reader.bytes();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ExternalMessage {
    return {
      cmdCode: isSet(object.cmdCode) ? Number(object.cmdCode) : 0,
      protocolSwitch: isSet(object.protocolSwitch) ? Number(object.protocolSwitch) : 0,
      cmdMerge: isSet(object.cmdMerge) ? Number(object.cmdMerge) : 0,
      responseStatus: isSet(object.responseStatus) ? Number(object.responseStatus) : 0,
      validMsg: isSet(object.validMsg) ? String(object.validMsg) : "",
      data: isSet(object.data) ? bytesFromBase64(object.data) : new Uint8Array(0),
    };
  },

  toJSON(message: ExternalMessage): unknown {
    const obj: any = {};
    message.cmdCode !== undefined && (obj.cmdCode = Math.round(message.cmdCode));
    message.protocolSwitch !== undefined && (obj.protocolSwitch = Math.round(message.protocolSwitch));
    message.cmdMerge !== undefined && (obj.cmdMerge = Math.round(message.cmdMerge));
    message.responseStatus !== undefined && (obj.responseStatus = Math.round(message.responseStatus));
    message.validMsg !== undefined && (obj.validMsg = message.validMsg);
    message.data !== undefined &&
      (obj.data = base64FromBytes(message.data !== undefined ? message.data : new Uint8Array(0)));
    return obj;
  },

  create<I extends Exact<DeepPartial<ExternalMessage>, I>>(base?: I): ExternalMessage {
    return ExternalMessage.fromPartial(base ?? {});
  },

  fromPartial<I extends Exact<DeepPartial<ExternalMessage>, I>>(object: I): ExternalMessage {
    const message = createBaseExternalMessage();
    message.cmdCode = object.cmdCode ?? 0;
    message.protocolSwitch = object.protocolSwitch ?? 0;
    message.cmdMerge = object.cmdMerge ?? 0;
    message.responseStatus = object.responseStatus ?? 0;
    message.validMsg = object.validMsg ?? "";
    message.data = object.data ?? new Uint8Array(0);
    return message;
  },
};

function createBaseIntValue(): IntValue {
  return { value: 0 };
}

export const IntValue = {
  encode(message: IntValue, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.value !== 0) {
      writer.uint32(8).sint32(message.value);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): IntValue {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseIntValue();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.value = reader.sint32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): IntValue {
    return { value: isSet(object.value) ? Number(object.value) : 0 };
  },

  toJSON(message: IntValue): unknown {
    const obj: any = {};
    message.value !== undefined && (obj.value = Math.round(message.value));
    return obj;
  },

  create<I extends Exact<DeepPartial<IntValue>, I>>(base?: I): IntValue {
    return IntValue.fromPartial(base ?? {});
  },

  fromPartial<I extends Exact<DeepPartial<IntValue>, I>>(object: I): IntValue {
    const message = createBaseIntValue();
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseIntValueList(): IntValueList {
  return { values: [] };
}

export const IntValueList = {
  encode(message: IntValueList, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    writer.uint32(10).fork();
    for (const v of message.values) {
      writer.sint32(v);
    }
    writer.ldelim();
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): IntValueList {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseIntValueList();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag === 8) {
            message.values.push(reader.sint32());

            continue;
          }

          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.values.push(reader.sint32());
            }

            continue;
          }

          break;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): IntValueList {
    return { values: Array.isArray(object?.values) ? object.values.map((e: any) => Number(e)) : [] };
  },

  toJSON(message: IntValueList): unknown {
    const obj: any = {};
    if (message.values) {
      obj.values = message.values.map((e) => Math.round(e));
    } else {
      obj.values = [];
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<IntValueList>, I>>(base?: I): IntValueList {
    return IntValueList.fromPartial(base ?? {});
  },

  fromPartial<I extends Exact<DeepPartial<IntValueList>, I>>(object: I): IntValueList {
    const message = createBaseIntValueList();
    message.values = object.values?.map((e) => e) || [];
    return message;
  },
};

function createBaseLongValue(): LongValue {
  return { value: 0 };
}

export const LongValue = {
  encode(message: LongValue, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.value !== 0) {
      writer.uint32(8).sint64(message.value);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): LongValue {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLongValue();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.value = longToNumber(reader.sint64() as Long);
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): LongValue {
    return { value: isSet(object.value) ? Number(object.value) : 0 };
  },

  toJSON(message: LongValue): unknown {
    const obj: any = {};
    message.value !== undefined && (obj.value = Math.round(message.value));
    return obj;
  },

  create<I extends Exact<DeepPartial<LongValue>, I>>(base?: I): LongValue {
    return LongValue.fromPartial(base ?? {});
  },

  fromPartial<I extends Exact<DeepPartial<LongValue>, I>>(object: I): LongValue {
    const message = createBaseLongValue();
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseLongValueList(): LongValueList {
  return { values: [] };
}

export const LongValueList = {
  encode(message: LongValueList, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    writer.uint32(10).fork();
    for (const v of message.values) {
      writer.sint64(v);
    }
    writer.ldelim();
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): LongValueList {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLongValueList();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag === 8) {
            message.values.push(longToNumber(reader.sint64() as Long));

            continue;
          }

          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.values.push(longToNumber(reader.sint64() as Long));
            }

            continue;
          }

          break;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): LongValueList {
    return { values: Array.isArray(object?.values) ? object.values.map((e: any) => Number(e)) : [] };
  },

  toJSON(message: LongValueList): unknown {
    const obj: any = {};
    if (message.values) {
      obj.values = message.values.map((e) => Math.round(e));
    } else {
      obj.values = [];
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<LongValueList>, I>>(base?: I): LongValueList {
    return LongValueList.fromPartial(base ?? {});
  },

  fromPartial<I extends Exact<DeepPartial<LongValueList>, I>>(object: I): LongValueList {
    const message = createBaseLongValueList();
    message.values = object.values?.map((e) => e) || [];
    return message;
  },
};

function createBaseStringValue(): StringValue {
  return { value: "" };
}

export const StringValue = {
  encode(message: StringValue, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.value !== "") {
      writer.uint32(10).string(message.value);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): StringValue {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseStringValue();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.value = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): StringValue {
    return { value: isSet(object.value) ? String(object.value) : "" };
  },

  toJSON(message: StringValue): unknown {
    const obj: any = {};
    message.value !== undefined && (obj.value = message.value);
    return obj;
  },

  create<I extends Exact<DeepPartial<StringValue>, I>>(base?: I): StringValue {
    return StringValue.fromPartial(base ?? {});
  },

  fromPartial<I extends Exact<DeepPartial<StringValue>, I>>(object: I): StringValue {
    const message = createBaseStringValue();
    message.value = object.value ?? "";
    return message;
  },
};

function createBaseStringValueList(): StringValueList {
  return { values: [] };
}

export const StringValueList = {
  encode(message: StringValueList, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    for (const v of message.values) {
      writer.uint32(10).string(v!);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): StringValueList {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseStringValueList();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.values.push(reader.string());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): StringValueList {
    return { values: Array.isArray(object?.values) ? object.values.map((e: any) => String(e)) : [] };
  },

  toJSON(message: StringValueList): unknown {
    const obj: any = {};
    if (message.values) {
      obj.values = message.values.map((e) => e);
    } else {
      obj.values = [];
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<StringValueList>, I>>(base?: I): StringValueList {
    return StringValueList.fromPartial(base ?? {});
  },

  fromPartial<I extends Exact<DeepPartial<StringValueList>, I>>(object: I): StringValueList {
    const message = createBaseStringValueList();
    message.values = object.values?.map((e) => e) || [];
    return message;
  },
};

function createBaseBoolValue(): BoolValue {
  return { value: false };
}

export const BoolValue = {
  encode(message: BoolValue, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.value === true) {
      writer.uint32(8).bool(message.value);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): BoolValue {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBoolValue();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.value = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BoolValue {
    return { value: isSet(object.value) ? Boolean(object.value) : false };
  },

  toJSON(message: BoolValue): unknown {
    const obj: any = {};
    message.value !== undefined && (obj.value = message.value);
    return obj;
  },

  create<I extends Exact<DeepPartial<BoolValue>, I>>(base?: I): BoolValue {
    return BoolValue.fromPartial(base ?? {});
  },

  fromPartial<I extends Exact<DeepPartial<BoolValue>, I>>(object: I): BoolValue {
    const message = createBaseBoolValue();
    message.value = object.value ?? false;
    return message;
  },
};

function createBaseBoolValueList(): BoolValueList {
  return { values: [] };
}

export const BoolValueList = {
  encode(message: BoolValueList, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    writer.uint32(10).fork();
    for (const v of message.values) {
      writer.bool(v);
    }
    writer.ldelim();
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): BoolValueList {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBoolValueList();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag === 8) {
            message.values.push(reader.bool());

            continue;
          }

          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.values.push(reader.bool());
            }

            continue;
          }

          break;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BoolValueList {
    return { values: Array.isArray(object?.values) ? object.values.map((e: any) => Boolean(e)) : [] };
  },

  toJSON(message: BoolValueList): unknown {
    const obj: any = {};
    if (message.values) {
      obj.values = message.values.map((e) => e);
    } else {
      obj.values = [];
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BoolValueList>, I>>(base?: I): BoolValueList {
    return BoolValueList.fromPartial(base ?? {});
  },

  fromPartial<I extends Exact<DeepPartial<BoolValueList>, I>>(object: I): BoolValueList {
    const message = createBaseBoolValueList();
    message.values = object.values?.map((e) => e) || [];
    return message;
  },
};

function createBaseIntPb(): IntPb {
  return { intValue: 0 };
}

export const IntPb = {
  encode(message: IntPb, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.intValue !== 0) {
      writer.uint32(8).sint32(message.intValue);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): IntPb {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseIntPb();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.intValue = reader.sint32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): IntPb {
    return { intValue: isSet(object.intValue) ? Number(object.intValue) : 0 };
  },

  toJSON(message: IntPb): unknown {
    const obj: any = {};
    message.intValue !== undefined && (obj.intValue = Math.round(message.intValue));
    return obj;
  },

  create<I extends Exact<DeepPartial<IntPb>, I>>(base?: I): IntPb {
    return IntPb.fromPartial(base ?? {});
  },

  fromPartial<I extends Exact<DeepPartial<IntPb>, I>>(object: I): IntPb {
    const message = createBaseIntPb();
    message.intValue = object.intValue ?? 0;
    return message;
  },
};

function createBaseIntListPb(): IntListPb {
  return { intValues: [] };
}

export const IntListPb = {
  encode(message: IntListPb, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    writer.uint32(10).fork();
    for (const v of message.intValues) {
      writer.sint32(v);
    }
    writer.ldelim();
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): IntListPb {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseIntListPb();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag === 8) {
            message.intValues.push(reader.sint32());

            continue;
          }

          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.intValues.push(reader.sint32());
            }

            continue;
          }

          break;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): IntListPb {
    return { intValues: Array.isArray(object?.intValues) ? object.intValues.map((e: any) => Number(e)) : [] };
  },

  toJSON(message: IntListPb): unknown {
    const obj: any = {};
    if (message.intValues) {
      obj.intValues = message.intValues.map((e) => Math.round(e));
    } else {
      obj.intValues = [];
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<IntListPb>, I>>(base?: I): IntListPb {
    return IntListPb.fromPartial(base ?? {});
  },

  fromPartial<I extends Exact<DeepPartial<IntListPb>, I>>(object: I): IntListPb {
    const message = createBaseIntListPb();
    message.intValues = object.intValues?.map((e) => e) || [];
    return message;
  },
};

function createBaseLongPb(): LongPb {
  return { longValue: 0 };
}

export const LongPb = {
  encode(message: LongPb, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.longValue !== 0) {
      writer.uint32(8).sint64(message.longValue);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): LongPb {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLongPb();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.longValue = longToNumber(reader.sint64() as Long);
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): LongPb {
    return { longValue: isSet(object.longValue) ? Number(object.longValue) : 0 };
  },

  toJSON(message: LongPb): unknown {
    const obj: any = {};
    message.longValue !== undefined && (obj.longValue = Math.round(message.longValue));
    return obj;
  },

  create<I extends Exact<DeepPartial<LongPb>, I>>(base?: I): LongPb {
    return LongPb.fromPartial(base ?? {});
  },

  fromPartial<I extends Exact<DeepPartial<LongPb>, I>>(object: I): LongPb {
    const message = createBaseLongPb();
    message.longValue = object.longValue ?? 0;
    return message;
  },
};

function createBaseLongListPb(): LongListPb {
  return { longValues: [] };
}

export const LongListPb = {
  encode(message: LongListPb, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    writer.uint32(10).fork();
    for (const v of message.longValues) {
      writer.sint64(v);
    }
    writer.ldelim();
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): LongListPb {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLongListPb();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag === 8) {
            message.longValues.push(longToNumber(reader.sint64() as Long));

            continue;
          }

          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.longValues.push(longToNumber(reader.sint64() as Long));
            }

            continue;
          }

          break;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): LongListPb {
    return { longValues: Array.isArray(object?.longValues) ? object.longValues.map((e: any) => Number(e)) : [] };
  },

  toJSON(message: LongListPb): unknown {
    const obj: any = {};
    if (message.longValues) {
      obj.longValues = message.longValues.map((e) => Math.round(e));
    } else {
      obj.longValues = [];
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<LongListPb>, I>>(base?: I): LongListPb {
    return LongListPb.fromPartial(base ?? {});
  },

  fromPartial<I extends Exact<DeepPartial<LongListPb>, I>>(object: I): LongListPb {
    const message = createBaseLongListPb();
    message.longValues = object.longValues?.map((e) => e) || [];
    return message;
  },
};

declare var self: any | undefined;
declare var window: any | undefined;
declare var global: any | undefined;
var tsProtoGlobalThis: any = (() => {
  if (typeof globalThis !== "undefined") {
    return globalThis;
  }
  if (typeof self !== "undefined") {
    return self;
  }
  if (typeof window !== "undefined") {
    return window;
  }
  if (typeof global !== "undefined") {
    return global;
  }
  throw "Unable to locate global object";
})();

function bytesFromBase64(b64: string): Uint8Array {
  if (tsProtoGlobalThis.Buffer) {
    return Uint8Array.from(tsProtoGlobalThis.Buffer.from(b64, "base64"));
  } else {
    const bin = tsProtoGlobalThis.atob(b64);
    const arr = new Uint8Array(bin.length);
    for (let i = 0; i < bin.length; ++i) {
      arr[i] = bin.charCodeAt(i);
    }
    return arr;
  }
}

function base64FromBytes(arr: Uint8Array): string {
  if (tsProtoGlobalThis.Buffer) {
    return tsProtoGlobalThis.Buffer.from(arr).toString("base64");
  } else {
    const bin: string[] = [];
    arr.forEach((byte) => {
      bin.push(String.fromCharCode(byte));
    });
    return tsProtoGlobalThis.btoa(bin.join(""));
  }
}

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(long: Long): number {
  if (long.gt(Number.MAX_SAFE_INTEGER)) {
    throw new tsProtoGlobalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  return long.toNumber();
}

if (_m0.util.Long !== Long) {
  _m0.util.Long = Long as any;
  _m0.configure();
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}
