{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "be65a19c-de55-4e5e-adef-4f97e317a107", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "be65a19c-de55-4e5e-adef-4f97e317a107@6c48a", "displayName": "tianpeng-move_04", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "be65a19c-de55-4e5e-adef-4f97e317a107", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "be65a19c-de55-4e5e-adef-4f97e317a107@f9941", "displayName": "tianpeng-move_04", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": -4.5, "offsetY": -13, "trimX": 57, "trimY": 47, "width": 127, "height": 132, "rawWidth": 250, "rawHeight": 200, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-63.5, -66, 0, 63.5, -66, 0, -63.5, 66, 0, 63.5, 66, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [57, 153, 184, 153, 57, 21, 184, 21], "nuv": [0.228, 0.105, 0.736, 0.105, 0.228, 0.765, 0.736, 0.765], "minPos": [-63.5, -66, 0], "maxPos": [63.5, 66, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "be65a19c-de55-4e5e-adef-4f97e317a107@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "be65a19c-de55-4e5e-adef-4f97e317a107@6c48a"}}