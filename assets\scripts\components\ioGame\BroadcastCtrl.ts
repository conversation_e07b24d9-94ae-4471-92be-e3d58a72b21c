import MessageCtrl from './MessageCtrl'

/**
 * 广播监听
 */
export default class BroadcastCtrl extends MessageCtrl {
	/**
	 * 监听回调
	 * @param type
	 * @param caller
	 * @param listener
	 */
	listen(caller: any, listener: (data: any) => void) {
		this.node.on('recode', listener, caller)
	}

	unListen(caller: any, listener: (data: any) => void) {
		this.node.off('recode', listener, caller)
	}

	send(msg?: any) {
		console.warn('广播监听无法发送')
	}

	async sendSync(msg?: any) {
		this.send()
		return this.succFalse
	}

	receive(code: number, data: any) {
		this.node.emit('recode', data)
	}
}
