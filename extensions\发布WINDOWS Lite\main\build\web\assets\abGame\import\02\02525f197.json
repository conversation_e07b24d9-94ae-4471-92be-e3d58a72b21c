[1, ["52fHu7D8hGm5vLaoALoXCl", "69qH8Ge/pO67EE1Ap1T+cT@f9941", "41Xr0ILK1KeZhrAveJ6ptp@f9941", "26hA86zmVLKYZ30Cd+lOot@f9941", "3bvJBntRxNdLTqkOiS7Nx7@f9941", "26hA86zmVLKYZ30Cd+lOot@6c48a", "3bvJBntRxNdLTqkOiS7Nx7@6c48a", "41Xr0ILK1KeZhrAveJ6ptp@6c48a", "69qH8Ge/pO67EE1Ap1T+cT@6c48a"], ["node", "_spriteFrame", "_textureSource", "_font", "root", "lbRewardNum", "lbName", "spr<PERSON><PERSON><PERSON>", "data"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_layer", "_components", "_prefab", "_parent", "_lpos", "_children", "_lscale"], 1, 9, 4, 1, 5, 2, 5], ["cc.UITransform", ["node", "__prefab", "_anchorPoint", "_contentSize"], 3, 1, 4, 5, 5], ["cc.Sprite", ["_sizeMode", "node", "__prefab", "_spriteFrame"], 2, 1, 4, 6], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_lineHeight", "_isSystemFontUsed", "node", "__prefab", "_font", "_color"], -2, 1, 4, 6, 5], ["cc.Node", ["_name", "_layer", "_parent", "_components", "_prefab", "_lpos", "_lscale"], 1, 1, 12, 4, 5, 5], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["61247uGtkFDq7pAx1it2i8o", ["node", "__prefab", "spr<PERSON><PERSON><PERSON>", "lbName", "lbRewardNum"], 3, 1, 4, 1, 1, 1], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.Mask", ["_type", "node", "__prefab"], 2, 1, 4], ["cc.Graphics", ["node", "__prefab", "_fillColor"], 3, 1, 4, 5], ["cc.Layout", ["_resizeMode", "_layoutType", "_spacingX", "_affectedByScale", "node", "__prefab"], -1, 1, 4]], [[7, 0, 2], [9, 0, 1, 2, 3, 4, 5, 5], [2, 0, 1, 3, 2, 1], [2, 0, 1, 3, 1], [1, 0, 1, 4, 6, 2, 3, 5, 3], [1, 0, 1, 4, 2, 3, 5, 3], [5, 0, 1, 2, 3, 4, 5, 6, 3], [2, 0, 1, 1], [3, 1, 2, 3, 1], [6, 0, 2], [1, 0, 1, 6, 2, 3, 3], [1, 0, 1, 4, 2, 3, 5, 7, 3], [5, 0, 1, 2, 3, 4, 3], [2, 0, 1, 2, 1], [8, 0, 1, 2, 3, 4, 1], [10, 0, 1, 2, 2], [11, 0, 1, 2, 1], [12, 0, 1, 2, 3, 4, 5, 5], [3, 0, 1, 2, 3, 2], [3, 0, 1, 2, 2], [4, 0, 1, 2, 3, 4, 5, 6, 7, 6], [4, 0, 1, 2, 3, 4, 5, 6, 6], [4, 0, 1, 2, 3, 4, 5, 6, 8, 6]], [[[[9, "UnitTaskRewardMessage"], [10, "UnitTaskRewardMessage", 33554432, [-7, -8, -9, -10], [[13, -2, [0, "79xG6B17tCcbjtnJHhPwFQ"], [0, 0, 0]], [14, -6, [0, "f4vndtOfpBjL6XnbL+vGuv"], -5, -4, -3]], [1, "2aEggzOGZNy4+EBPCQBhyT", null, null, null, -1, 0]], [4, "ndAvatar", 33554432, 1, [-12, -13, -14], [[7, -11, [0, "b9x+KwnM1Mvb+k5RJyAZOw"]]], [1, "3frZbu41hIxYjUNu1JyUBb", null, null, null, 1, 0], [1, 353.14099999999996, 79.63799999999992, 0]], [4, "Node", 33554432, 2, [-18], [[7, -15, [0, "d4M3aD0RhKWIvVpN6xeWND"]], [15, 1, -16, [0, "6aO00X0B5Mv5jngcu162ER"]], [16, -17, [0, "62odFlOChKJIS7bBrrftUG"], [4, 16777215]]], [1, "7bsKELRElFZpgkBSxhUj7z", null, null, null, 1, 0], [1, -259.885, 2.2737367544323206e-13, 0]], [4, "Node", 33554432, 1, [-21, -22], [[2, -19, [0, "7bEaP0tp5Dp5tnQ2P5du1O"], [5, 202.5, 100], [0, 0, 0.5]], [17, 1, 1, 6, true, -20, [0, "cc/VtrE/ZCgJI1Xr3KoawL"]]], [1, "a3I+odKxRPGpnahbqA3VuV", null, null, null, 1, 0], [1, 178.28499999999997, 49.80200000000002, 0]], [5, "game_giftmessage_frame02", 33554432, 1, [[3, -23, [0, "a2PExg1BVLspATm6PceVLe"], [5, 799, 190]], [8, -24, [0, "c6x165gPhKRKdccfY79xD1"], 0]], [1, "26DI9wGPBLOavil+vl/Rru", null, null, null, 1, 0], [1, 408.669, 93.81600000000003, 0]], [5, "sprAvatarBg", 33554432, 2, [[3, -25, [0, "12oxiarN9JhqwPluCZDHBl"], [5, 113, 113]], [8, -26, [0, "0feiggzsFBIa37Jb4codnl"], 1]], [1, "de/tYOKQ9FMagdTYbcokOd", null, null, null, 1, 0], [1, -257.885, 0, 0]], [12, "spr<PERSON><PERSON><PERSON>", 33554432, 3, [[[3, -27, [0, "9e8vq3g+hN0YXrYnKLBRO8"], [5, 104, 104]], -28], 4, 1], [1, "d0CFSPTz5LDZVho/pK3Dbd", null, null, null, 1, 0]], [5, "sprAvatarMask", 33554432, 2, [[3, -29, [0, "e73kk4xSNBb5UpQPaRD5Ux"], [5, 104, 104]], [18, 0, -30, [0, "9a8vpnNxxBvqiuJQfL93C6"], 2]], [1, "dfHJAfckpAcbeTOkDtTyEc", null, null, null, 1, 0], [1, -257.885, 0, 0]], [6, "lbName", 33554432, 1, [[[2, -31, [0, "5aV4ufw5tJpaVnTQfKkSw6"], [5, 240, 100.8], [0, 0, 0.5]], -32], 4, 1], [1, "a5t226YnJJOKeNFiVBrulh", null, null, null, 1, 0], [1, 178.28499999999997, 103.92900000000009, 0], [1, 0.5, 0.5, 1]], [11, "lbReward", 33554432, 4, [[2, -33, [0, "2azOr3odFF3qS5YPwU+cJe"], [5, 360, 100.8], [0, 0, 0.5]], [20, "完成任务奖励", 60, 60, 80, false, -34, [0, "44xj9j/H1JfpwowZwLKK4c"], 3]], [1, "2bGnpDkydIUqUV9K3Q05e5", null, null, null, 1, 0], [1, 0, 1.8690000000000282, 0], [1, 0.5, 0.5, 1]], [6, "lbNum", 33554432, 4, [[[2, -35, [0, "d5Lx9f2jlCgqd9PudeJG+0"], [5, 33, 100.8], [0, 0, 0.5]], -36], 4, 1], [1, "c9t3fJyi1Bk4TcZm4rDmnm", null, null, null, 1, 0], [1, 186, 1.8690000000000282, 0], [1, 0.5, 0.5, 1]], [19, 0, 7, [0, "cchNy8WqNCj7w5e9eDSwwu"]], [21, "匿名用户", 60, 60, 80, false, 9, [0, "96HU3qekJIT4H1OyUj1jJg"]], [22, "x", 60, 60, 80, false, 11, [0, "d2BETJGLxE9KzQO2tX1HyR"], [4, 4279347455]]], 0, [0, 4, 1, 0, 0, 1, 0, 5, 14, 0, 6, 13, 0, 7, 12, 0, 0, 1, 0, -1, 5, 0, -2, 2, 0, -3, 9, 0, -4, 4, 0, 0, 2, 0, -1, 6, 0, -2, 3, 0, -3, 8, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -1, 7, 0, 0, 4, 0, 0, 4, 0, -1, 10, 0, -2, 11, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, -2, 12, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, -2, 13, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, -2, 14, 0, 8, 1, 36], [0, 0, 0, 0, 12, 13, 14], [1, 1, 1, 3, 1, 3, 3], [1, 2, 3, 0, 4, 0, 0]], [[{"name": "game_giftmessage_avatarmask", "rect": {"x": 0, "y": 0, "width": 113, "height": 113}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 113, "height": 113}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-56.5, -56.5, 0, 56.5, -56.5, 0, -56.5, 56.5, 0, 56.5, 56.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 113, 113, 113, 0, 0, 113, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -56.5, "y": -56.5, "z": 0}, "maxPos": {"x": 56.5, "y": 56.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [5]], [[{"name": "default_avatar", "rect": {"x": 0, "y": 0, "width": 180, "height": 180}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 180, "height": 180}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-90, -90, 0, 90, -90, 0, -90, 90, 0, 90, 90, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 180, 180, 180, 0, 0, 180, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -90, "y": -90, "z": 0}, "maxPos": {"x": 90, "y": 90, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [6]], [[{"name": "game_giftmessage_avatarbg", "rect": {"x": 5, "y": 3, "width": 113, "height": 113}, "offset": {"x": 1, "y": 1}, "originalSize": {"width": 121, "height": 121}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-56.5, -56.5, 0, 56.5, -56.5, 0, -56.5, 56.5, 0, 56.5, 56.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [5, 118, 118, 118, 5, 5, 118, 5], "nuv": [0.04132231404958678, 0.04132231404958678, 0.9752066115702479, 0.04132231404958678, 0.04132231404958678, 0.9752066115702479, 0.9752066115702479, 0.9752066115702479], "minPos": {"x": -56.5, "y": -56.5, "z": 0}, "maxPos": {"x": 56.5, "y": 56.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [7]], [[{"name": "game_giftmessage_frame02", "rect": {"x": 0, "y": 0, "width": 799, "height": 190}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 799, "height": 190}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-399.5, -95, 0, 399.5, -95, 0, -399.5, 95, 0, 399.5, 95, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 190, 799, 190, 0, 0, 799, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -399.5, "y": -95, "z": 0}, "maxPos": {"x": 399.5, "y": 95, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [8]]]]