

import { AnimationClip, Animation, Component, instantiate, log, Node, Prefab, Size, Sprite, tween, UITransform, v2, v3, Vec2, Vec3, warn, <PERSON>prite<PERSON><PERSON><PERSON> } from "cc";


import { xcore } from "../xcore";
import { C_Bundle, C_Runtime, E_JumpType, E_TaskType, IAnimaData, listenDataType, T_Dict } from "../../ConstGlobal";
import { Collection } from "./Collection";
import ElectronAPI from "../../../ElectronAPI";




export default class Tool {



    static simpleV2(value, out?: Vec2) {
        if (out) {
            out.set(value, value)
            return out
        }
        return v2(value, value)
    }

    static simpleV3(value, out?: Vec3) {
        if (out) {
            out.set(value, value, value)
            return out
        }
        return v3(value, value, value)
    }

    static v2t3(v2Data: Vec2, out?: Vec3) {
        if (!out) {
            return v3(v2Data.x, v2Data.y, 1)
        } else {
            out.x = v2Data.x
            out.y = v2Data.y
            out.z = 1
            return out
        }
    }

    static v3t2(v3Data: Vec3, out?: Vec2) {
        if (!out) {
            return v2(v3Data.x, v3Data.y)
        } else {
            out.x = v3Data.x
            out.y = v3Data.y
            return out
        }
    }

    /**
     * return min  - max  之间的数，包括min和max
     * @param min 
     * @param max 
     */
    /* static randomNumber(min: number, max: number) {
        if (min == max) {
            return min
        }
        return Math.ceil(Math.random() * (max - min + 1) + min)
    } */
    static randomNumber(a: number, b: number): number | null {
        // 1. 确定实际的最小值和最大值边界
        const lowerBound = Math.min(a, b);
        const upperBound = Math.max(a, b);

        // 2. 确定整数范围的起点和终点
        const startInteger = Math.ceil(lowerBound);
        const endInteger = Math.floor(upperBound);

        // 3. 检查是否存在至少一个整数
        if (startInteger > endInteger) {
            // 没有整数在这个范围内 (例如 getIntegersBetween(4.1, 4.9))
            return null;
        }

        // 4. 计算范围内整数的数量
        //    例如，范围 [2, 5] 包含 5 - 2 + 1 = 4 个整数 (2, 3, 4, 5)
        const count = endInteger - startInteger + 1;

        // 5. 生成一个随机索引（偏移量）
        //    Math.random() 返回 [0, 1) 范围的浮点数
        //    Math.random() * count 返回 [0, count) 范围的浮点数
        //    Math.floor(...) 得到 [0, count - 1] 范围的整数索引
        const randomIndexOffset = Math.floor(Math.random() * count);

        // 6. 计算并返回最终的随机整数
        //    起始整数加上随机偏移量
        const randomInteger = startInteger + randomIndexOffset;

        return randomInteger;
    }

    static findArrNullIdx(arr: any[]) {
        if (arr.length == 0) return 0;
        for (let i = 0; i < arr.length; i++) {
            if (!arr[i]) {
                return i;
            }
        }
        return -1;
    }

    //分割number字符串 返回number 数组
    public static splitNumber(str: number | string, Separator: string = ',') {
        if (!str) return [];
        if (typeof str == 'number') {
            return [str];
        }

        return str.split(Separator).map((s_num, idx) => {
            return Number(s_num);
        })
    }

    /**
     * @param weights 权重数组 随机一个
     * @returns 
     */
    static weightRandomIdx(weights: number[]) {
        if (weights.length <= 1) {
            return 0;
        }

        let tw: number = 0;
        for (let i = 0; i < weights.length; i++) {
            tw += weights[i];
        }
        let rw = Math.random() * tw;
        let sw: number = 0, ew: number = 0;

        for (let i = 0; i < weights.length; i++) {
            ew = sw + weights[i];
            if (sw < rw && rw <= ew) {
                return i;
            }
            sw = ew;
        }
        return 0;
    }

    /**小数去0 */
    public static numMoveZoro(num: string) {
        if (num.indexOf('.') < 0) {
            return num;
        }
        num = num.replace(/0+?$/g, '')
        if (num[num.length - 1] == '.') {
            num = num.replace(/[.$]/, '');
        }
        return num;
    }

    /**
     * 
     * @param num 秒 格式化  h:f:s
     */
    public static secondFormat(num: number) {
        let str = '';
        let h: number = Math.floor(num / 3600)
        num -= h * 3600;
        let f: number = Math.floor(num / 60)
        num -= f * 60;
        num = Math.floor(num)

        // str += (h<10?'0' + h:h);

        // str += ':';
        str += (f < 10 ? '0' + f : f);

        str += ':';
        str += (num < 10 ? '0' + num : num);

        return str;
    }
    /**
     * 日期展示
     * @param time 时间戳
     * @param format 格式
     * @param isNeedHms 是否显示时分秒
     * @returns 
     */
    public static getDateStr(time: number, format: number = 1, isNeedHms: boolean = true) {

        let date = new Date(time);
        let y = date.getFullYear(), m = date.getMonth() + 1,
            d = date.getDate(), h = date.getHours(), mn = date.getMinutes();

        let mnStr = '' + mn;
        if (mn < 10) mnStr = '0' + mn;
        let day = ''
        if (format == 1) {
            day = `${y}/${m}/${d}`;

        } else {
            day = `${y}年${m}月${d}日`;

        }
        return day + (isNeedHms ? `${h}:${mnStr}` : '');
    }



    static generatorCallBack(len: number, callBack: Function, ...params: any) {
        function* gen() {
            for (let i = 0; i < len; i++) {
                yield callBack(i, ...params)
            }
        }
        return this.exeGenerator(gen(), 10);
    }

    static exeGenerator(generator: Generator, duration: number) {
        return new Promise((resolve, reject) => {
            let gen = generator
            let execute = () => {
                let startTime = new Date().getTime()
                for (let iter = gen.next(); ; iter = gen.next()) {
                    if (iter == null || iter.done) {
                        resolve(null)
                        return
                    }
                    if (new Date().getTime() - startTime > duration) {
                        setTimeout(() => {
                            execute()
                        }, duration)
                        return
                    }
                }
            }
            execute()
        })
    }

    /**
     * 拷贝对象
     * @param src_obj 源对象
     * @param dst_obj 目标对象
     */
    static copyObj(src_obj: any, dst_obj: any) {
        if (typeof dst_obj === "object") {
            for (var key in dst_obj) {
                if (typeof dst_obj[key] === "object") {
                    src_obj[key] != null && Tool.copyObj(src_obj[key], dst_obj[key]);
                } else if (typeof dst_obj[key] != "function") {
                    src_obj[key] != null && (dst_obj[key] = src_obj[key]);
                }
            }
        } else {
            console.log("can not copy the value type");
        }
    }

    /**
     * 克隆对象
     * @param obj 源对象
     * @returns 新的对象
     */
    static cloneObj(obj: any) {
        var new_obj;
        if (obj != null && (obj.constructor === Object || obj.constructor === Array)) {
            new_obj = new obj.constructor();
            for (var key in obj) {
                new_obj[key] = Tool.cloneObj(obj[key]);
            }
        } else {
            new_obj = obj;
        }
        return new_obj;
    }


    static getAngleByV3(startPos, endPos) {

        let angle = Math.atan2((endPos.y - startPos.y), (endPos.x - startPos.x)) //弧度 
        let theta = angle * (180 / Math.PI); //角度  
        return theta;

    }

    /**
 *  二阶贝塞尔曲线 运动
 * @param target
 * @param {number} duration
 * @param {} c1 起点坐标
 * @param {} c2 控制点
 * @param {Vec3} to 终点坐标
 * @param opts
 * @returns {any}
 */
    public static bezierTo(target: any, duration: number, c1: Vec2, c2: Vec2, to: Vec3, opts: any) {
        opts = opts || Object.create(null);
        /**
         * @desc 二阶贝塞尔
         * @param {number} t 当前百分比
         * @param {} p1 起点坐标
         * @param {} cp 控制点
         * @param {} p2 终点坐标
         * @returns {any}
         */
        let twoBezier = (t: number, p1: Vec2, cp: Vec2, p2: Vec3) => {
            let x = (1 - t) * (1 - t) * p1.x + 2 * t * (1 - t) * cp.x + t * t * p2.x;
            let y = (1 - t) * (1 - t) * p1.y + 2 * t * (1 - t) * cp.y + t * t * p2.y;
            return v3(x, y, 0);
        };
        opts.onUpdate = (arg: Vec3, ratio: number) => {
            target.position = twoBezier(ratio, c1, c2, to);
            target.angle += 10;
            if (ratio == 1) {
                opts.cb && opts.cb()
            }
        };
        return tween(target).to(duration, {}, opts);
    }



    public static getComponentPropertys(target: Object, comp?: Component | [Component]) {
        let components = [] as Component[];
        if (comp && !Array.isArray(comp)) {
            comp = [comp];
        }

        for (const key in target) {
            if (Object.prototype.hasOwnProperty.call(target, key)) {
                const element = target[key];
                if (element instanceof Component && key != 'node') {
                    if (comp) {
                        //@ts-ignore
                        if (comp.find(e => element instanceof e)) {
                            components.push(element);

                        }
                    } else {
                        components.push(element);
                    }

                }
            }
        }
        return components;
    }

    /** 执行代码 远程 */
    public static loadSDK(url) {
        return new Promise(function (resolve) {
            var script = document.createElement("script");
            script.type = "text/javascript";
            script.src = url;
            script.onload = resolve;
            document.body.appendChild(script);
        });
    }

    /** 复制字符 */
    public static copyContentH5(content) {
        var copyDom = document.createElement('input');
        document.body.appendChild(copyDom);
        copyDom.setAttribute('value', content);
        copyDom.setAttribute('readonly', 'readonly');
        copyDom.select();
        copyDom.setSelectionRange(0, 999);
        var successful = document.execCommand('copy');
        document.body.removeChild(copyDom);
        return successful;
    }


    /**
     * 任务跳转 h5 or 小程序中间页
     * @param jumpConfig 
     * @param target  监听节点 
     * @param jumpFunc 点击回调
     */
    public static listenThirdJump(jumpConfig: T_Dict, target: Node, jumpFunc?: Function) {

        target && target.off('click');
        let taskType = jumpConfig.taskType;


        this.formatJumpConfig(jumpConfig, taskType)

        /**环境判断 */
        const isH5ToH5 = xcore.runtime == C_Runtime.other && jumpConfig.type == E_JumpType.h5;
        const isWebviewToH5 = xcore.runtime == C_Runtime.program_wx && jumpConfig.type == E_JumpType.h5;
        const isOfficialToH5 = xcore.runtime == C_Runtime.browser_wx && jumpConfig.type == E_JumpType.h5;
        const isWebviewToPage = xcore.runtime == C_Runtime.program_wx && jumpConfig.type == E_JumpType.MiniProgram;

        //h5跳转 如果有webview中间页承载h5页面，可实现返回不重新加载游戏页面
        if (isH5ToH5 || isWebviewToH5 || isOfficialToH5) {
            //xysz.sound.voiceClick();
            target.on('click', (async () => {
                location.href = jumpConfig.address;
                jumpFunc && jumpFunc()
                console.log('jumpConfig', jumpConfig.address)
            }))
        }
        //中间页跳转
        else if (isWebviewToPage) {
            // xysz.sound.voiceClick();
            target.on('click', (async () => {

                this.miniProgramOpt('navigate', jumpConfig.address);
                console.log('jumpConfig', jumpConfig.address)
                jumpFunc && jumpFunc()
            }), this)
        }
        //环境限制
        else {
            target.on('click', (async () => {
                //xysz.sound.voiceClick()
                console.warn('平台限制：', decodeURIComponent(JSON.stringify(jumpConfig.address)))
                jumpFunc && jumpFunc()
            }))
        }

    }

    public static formatJumpConfig(jumpConfig: T_Dict, taskType?: number) {
        if (!taskType) {
            taskType = jumpConfig.taskType;
        }

        //内部跳转 直接跳转h5 or 内部小程序页面
        if (!jumpConfig.type && jumpConfig.address && taskType == E_TaskType.jumpInside) {
            jumpConfig.type = (jumpConfig.address.startsWith('http') || jumpConfig.address.startsWith('www')) ? E_JumpType.h5 : E_JumpType.MiniProgram;
        }

        //跳转中间页midpagePath 中间页--->转跳外部or分享卡片
        else if (jumpConfig.address && (taskType == E_TaskType.invite || taskType == E_TaskType.share || taskType == E_TaskType.jumpOutside)) {
            jumpConfig.type = E_JumpType.MiniProgram;
            let config = xcore.config[`midpageData.json`]?.find(e => e.jsonId == jumpConfig.address);
            if (config) {
                let midpageParams = {
                    apiParams: jumpConfig.apiParams || {},
                    apiSign: jumpConfig.apiSign || {},

                } as any;

                for (const key in config) {
                    if (Object.prototype.hasOwnProperty.call(config, key)) {
                        midpageParams[key] = config[key];
                    }
                }


                //分享卡片 parame 的 link字段为卡片入口路径 ，如需要携带入口参数，则直接拼在link上，如携带邀请id 任务id等
                if (taskType == E_TaskType.invite || taskType == E_TaskType.share) {
                    let Params = {}
                    for (const key in jumpConfig) {
                        if (Object.prototype.hasOwnProperty.call(jumpConfig, key)) {
                            Params[key] = jumpConfig[key];
                        }
                    }

                    let link = `${location.origin}${location.pathname}${Tool.getSortedQuery(Params) ? `?${Tool.getSortedQuery(Params)}` : ''}`;

                    midpageParams.path = link;
                    log('卡片入口路径', link);
                }
                let parame = encodeURIComponent(JSON.stringify(midpageParams));
                let midpagePath = ''
                jumpConfig.address = `${midpagePath}?config=${parame}`;
                return jumpConfig;
            }

        } else {
            warn('jumpConfig err', jumpConfig)
            return null
        }
        return jumpConfig;

    }
    /** 小程序操作，navigateTo/switchTab */
    public static miniProgramOpt(opt: string, baseUrl: string) {

        if (xcore.runtime == C_Runtime.program_wx) {
            if (opt === 'navigate') {

                //@ts-ignore
                wx.miniProgram.navigateTo({
                    url: baseUrl,
                    success: function (e) {
                        console.log(e);
                        if (e.errMsg === 'invokeMiniProgramAPI:ok') {
                            opt = 'switch';
                            Tool.miniProgramOpt(opt, baseUrl)
                        }
                    }
                });
            } else if (opt === 'switch') {
                //@ts-ignore
                wx.miniProgram.switchTab({ url: baseUrl })
            }
        } else {
            console.log('请在小程序内进行尝试')
        }




    }

    /** 函数节流 */
    public static throttle = function (func, wait) {
        if (wait === void 0) {
            wait = 1000;
        }

        var prev = 0;
        return function () {
            var context = this;
            var args = arguments;
            var now = Date.now(); // 1s内只能点击一次

            if (now - prev >= wait) {
                func.apply(context, args);
                prev = Date.now();
            }
        };
    };

    /** 函数防抖 */
    public static debounce = function (func, wait, immediate) {
        if (wait === void 0) {
            wait = 1000;
        }

        if (immediate === void 0) {
            immediate = false;
        }

        var timeout, result;

        var debounced = function debounced() {
            var context = this;
            var args = arguments;

            if (timeout) {
                clearTimeout(timeout);
            }

            if (immediate) {
                // 如果已经执行过，不再执行
                var callNow = !timeout;
                timeout = setTimeout(function () {
                    timeout = null;
                }, wait);

                if (callNow) {
                    result = func.apply(context, args);
                }
            } else {
                timeout = setTimeout(function () {
                    func.apply(context, args);
                }, wait);
            }

            return result;
        };

        return debounced;
    };

    public static getSortedQuery = function (obj) {
        var keys = Object.keys(obj).sort();
        var str = '';
        keys.forEach(function (key) {
            if (!obj[key] && obj[key] !== 0) {
                return;
            }

            var temp = '';

            if (obj[key] instanceof Object) {
                temp = key + "=" + JSON.stringify(obj[key]);
            } else {
                temp = key + "=" + obj[key];
            }

            if (str && temp) {
                str += '&';
            }

            str += temp;
        });
        return str;
    };
    static getRandomPosInCircle(center: Vec2, radius: number) {

        //生成随机角度（0到2π之间）
        let theta = Math.random() * 2 * Math.PI;
        //生成随机半径，使用平方根确保均匀分布
        let r = radius * Math.sqrt(Math.random());
        // 计算偏移量
        let dx = r * Math.cos(theta)
        let dy = r * Math.sin(theta)
        // 返回中心坐标加上偏移量
        return { x: center.x + dx, y: center.y + dy }
    }
    static parseQuery(url: string) {

        var queryObj = {} as any;
        var reg = /[?&]([^=&#]+)=([^&#]*)/g;
        var querys = url.match(reg);
        if (querys) {
            for (var i in querys) {
                var query = querys[i].split('=');
                var key = query[0].substring(1),
                    value = decodeURIComponent(query[1]);
                queryObj[key] ? queryObj[key] = [].concat(queryObj[key], value) : queryObj[key] = value;
            }
        }
        return queryObj;
    }

    /**async num毫秒 */
    static asyncDelay(num: number) {
        return new Promise((resolve, reject) => {
            setTimeout(() => {
                resolve('')
            }, num);
        })
    }

    /** 显示调试器 */
    static showConsole() {
        return new Promise(function (resolve, reject) {
            Tool.loadSDK('https://gz-cdn-1258783731.cos.ap-guangzhou.myqcloud.com/cocos_sdk/vconsole.min.js').then(function () {
                // @ts-ignore
                new VConsole();
                resolve('');
            });
        });
    }

    static resizeSprite(Sp: Sprite, lenOrSize: Size | number) {
        if (Sp && lenOrSize) {
            var sliceRect = Sp.spriteFrame.rect;
            if (lenOrSize instanceof Size) {
                var size = lenOrSize; // 不能直接设置scale，会导致layout布局占用空间

                var scale = Math.min(size.width / sliceRect.width, size.height / sliceRect.height);
                Sp.node.getComponent(UITransform).width = sliceRect.width * scale;
                Sp.node.getComponent(UITransform).height = sliceRect.height * scale;
            } else if (typeof lenOrSize === 'number') {
                var len = lenOrSize; // 不能直接设置scale，会导致layout布局占用空间

                var scale = Math.min(len / sliceRect.width, len / sliceRect.height);
                Sp.node.getComponent(UITransform).width = sliceRect.width * scale;
                Sp.node.getComponent(UITransform).height = sliceRect.height * scale;
            }
        }
    }
    /**
   * 异步初始化节点
   * @param comp 定时器component
   * @param content 元素父节点
   * @param copy 复制的节点
   * @param count 复制的数量
   * @param func 回调函数
   */
    static asyncModifyChildren(comp: Component | any, content: Node, copy: Node | Prefab, count: number, func: (nd: Node, i: number) => void) {
        const sameCount = content.children.filter(nd => nd.name === copy.name).length

        // 删除节点
        const removeCount = sameCount - count
        if (removeCount > 0) {
            for (let i = 0; i < removeCount; i++) {
                const node = content.children.find(nd => nd.name === copy.name)
                if (node) {
                    content.removeChild(node)
                }
            }
        }

        // 现有节点
        const existNodes = content.children.filter(nd => nd.name === copy.name)
        existNodes.forEach(func)

        // 新增节点
        const addCount = count - sameCount
        if (addCount > 0) {
            let idx = 0
            comp.schedule(() => {
                const node = instantiate(copy) as Node
                node.name = copy.name
                content.addChild(node)
                func(node, existNodes.length + idx)
                idx++
            }, 0, addCount - 1)
        }
    }


    /** 获取一个唯一标识的字符串 */
    static guid() {
        let guid: string = "";
        for (let i = 1; i <= 32; i++) {
            let n = Math.floor(Math.random() * 16.0).toString(16);
            guid += n;
            if ((i == 8) || (i == 12) || (i == 16) || (i == 20))
                guid += "-";
        }
        return guid;
    }

    /**
     * 转美式计数字符串
     * @param value 数字
     * @example
     * 123456789 = 123,456,789
     */
    static numberTotPermil(value: number): string {
        return value.toLocaleString();
    }

    /** 
     * 转英文单位计数
     * @param value 数字
     * @param fixed 保留小数位数
     * @example
     * 12345 = 12.35K
     */
    static numberToThousand(value: number, fixed: number = 2): string {
        var k = 1000;
        var sizes = ['', 'K', 'M', 'G'];
        if (value < k) {
            return value.toString();
        }
        else {
            var i = Math.floor(Math.log(value) / Math.log(k));
            var r = ((value / Math.pow(k, i)));
            return r.toFixed(fixed) + sizes[i];
        }
    }

    /** 
     * 转中文单位计数
     * @param value 数字
     * @param fixed 保留小数位数
     * @example
     * 12345 = 1.23万
     */
    static numberToTenThousand(value: number, fixed: number = 2): string {
        var k = 10000;
        var sizes = ['', '万', '亿', '万亿'];
        if (value < k) {
            return value.toString();
        }
        else {
            var i = Math.floor(Math.log(value) / Math.log(k));
            return ((value / Math.pow(k, i))).toFixed(fixed) + sizes[i];
        }
    }

    /**
     * 时间格式化
     * @param date  时间对象
     * @param fmt   格式化字符(yyyy-MM-dd hh:mm:ss S)
     */
    static format(date: Date, fmt: string) {
        var o: any = {
            "M+": date.getMonth() + 1,                   // 月份 
            "d+": date.getDate(),                        // 日 
            "h+": date.getHours(),                       // 小时 
            "m+": date.getMinutes(),                     // 分 
            "s+": date.getSeconds(),                     // 秒 
            "q+": Math.floor((date.getMonth() + 3) / 3), // 季度 
            "S": date.getMilliseconds()                  // 毫秒 
        };
        if (/(y+)/.test(fmt)) {
            fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
        }
        for (var k in o) {
            if (new RegExp("(" + k + ")").test(fmt)) {
                fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
            }
        }
        return fmt;
    }

    /**
     * "," 分割字符串成数组
     * @param str 字符串
     */
    static stringToArray1(str: string) {
        if (str == "") {
            return [];
        }
        return str.split(",");
    }

    /** 
     * "|" 分割字符串成数组 
     * @param str 字符串
     */
    static stringToArray2(str: string) {
        if (str == "") {
            return [];
        }
        return str.split("|");
    }

    /** 
     * ":" 分割字符串成数组
     * @param str 字符串
     */
    static stringToArray3(str: string) {
        if (str == "") {
            return [];
        }
        return str.split(":");
    }

    /** 
     * ";" 分割字符串成数组 
     * @param str 字符串
     */
    static stringToArray4(str: string) {
        if (str == "") {
            return [];
        }
        return str.split(";");
    }
    static randomSortArray(arr) {
        arr.sort(function () {
            return Math.random() - 0.5;
        });
        return arr;
    }
    static selectArrayIdByWeight(arr) {
        if (arr.length <= 0) return null
        // 计算权重总和
        const totalWeight = arr.reduce((sum, item) => sum + item.weight, 0);
        if (totalWeight <= 0) {
            throw new Error("Total weight must be a positive number");
        }

        // 生成随机数
        let random = Math.random() * totalWeight;
        if (random <= 0) {
            random = 0.1;
        }
        let currentWeight = 0;

        for (const item of arr) {
            currentWeight += item.weight;
            if (random < currentWeight) {
                return item.id;
            }
        }


        return null;
    }
    /**
     * 字符串截取
     * @param str     字符串
     * @param n       截取长度
     * @param showdot 是否把截取的部分用省略号代替
     */
    static sub(str: string, n: number, showdot: boolean = false) {
        if (!str) {
            return ''
        }
        var r = /[^\x00-\xff]/g;
        if (str.replace(r, "mm").length <= n) { return str; }
        var m = Math.floor(n / 2);
        for (var i = m; i < str.length; i++) {
            if (str.substr(0, i).replace(r, "mm").length >= n) {
                if (showdot) {
                    return str.substr(0, i) + "...";
                } else {
                    return str.substr(0, i);
                }
            }
        }
        return str;
    }

    /**
     * 计算字符串长度，中文算两个字节
     * @param str 字符串
     */
    static stringLen(str: string) {
        var realLength = 0, len = str.length, charCode = -1;
        for (var i = 0; i < len; i++) {
            charCode = str.charCodeAt(i);
            if (charCode >= 0 && charCode <= 128)
                realLength += 1;
            else
                realLength += 2;
        }
        return realLength;
    }

    /**
     * 是否为空
     * @param str 
     */
    public static IsEmpty(str: string): boolean {
        if (str == null || str == undefined || str.length == 0) {
            return true;
        }
        return false;
    }

    /**
     * 参数替换
     * @param  str
     * @param  rest
     *  
     * @example
     *
     * var str:string = "here is some info '{0}' and {1}";
     * StringUtil.substitute(str, 15.4, true);
     *
     * "here is some info '15.4' and true"
     */
    public static substitute(str: string, ...rest: any[]): string {
        if (str == null) return '';

        var len: number = rest.length;
        var args: any[];
        if (len == 1 && rest[0] instanceof Array) {
            args = rest[0];
            len = args.length;
        }
        else {
            args = rest;
        }

        for (var i: number = 0; i < len; i++) {
            str = str.replace(new RegExp("\\{" + i + "\\}", "g"), args[i]);
        }

        return str;
    }

    /**
     * 设置节点顺序
     * @param targetNd 目标节点
     * @param priority 目标层级 越小 越在底部
     * @returns 
     */
    static setChildrenNodeSortByPriority(targetNd: Node, priority: number): void {
        if (!targetNd.parent) {
            return;
        }
        let children = targetNd.parent.children.concat();
        targetNd[`priority`] = priority;
        children.sort((a: any, b: any): number => {
            if (a.priority == null) {
                a.priority = 0;
            }
            if (b.priority == null) {
                b.priority = 0;
            }
            return a.priority - b.priority;
        });
        let maxIndex = children.length;
        for (const node of children) {
            node.setSiblingIndex(maxIndex);
        }

    }

    /** 生成随机字符串(默认32) */
    static generateNoncestr(num: number = 32) {
        let str = ""
        let arr = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];

        // 随机产生,i为指定长度

        for (let i = 0; i < num; i++) {
            let pos = Math.round(Math.random() * (arr.length - 1));
            str += arr[pos];
        }
        return str;
    }

    static async createAnim(anim: Animation, animaData: IAnimaData, atlas: SpriteAtlas, isPlay: boolean = true) {
        if (!anim) return
        if (!xcore.gameData.animationClips) {
            xcore.gameData.animationClips = new Collection();
        }

        if (anim.clips[0]?.name == animaData.name) {
            anim.enabled = true;
            anim.play(animaData.name);
            return
        }
        let animationClip = xcore.gameData.animationClips.get(animaData.name);
        if (!animationClip) {
            let spriteFrameList = [] //= spratlas.getSpriteFrames().filter(e => e[`_name`].includes(animaData.name));
            for (let i = 0; i < animaData.sample; i++) {
                let name = `${animaData.name}_${i < 10 ? `0${i}` : i}`;
                let sprPath = `res/anim/${animaData.path}/${name}`;
                const spr = atlas ? atlas.getSpriteFrame(name) : await xcore.res.bundleLoadSprite(C_Bundle.abGame, sprPath);
                spriteFrameList.push(spr);
            }
            animationClip = AnimationClip.createWithSpriteFrames(spriteFrameList, animaData.sample);
            xcore.gameData.animationClips.set(animaData.name, animationClip);
            animationClip.addRef();

        }
        anim.enabled = true;
        anim.stop();
        animationClip.duration = animaData.duration;
        animationClip.speed = animaData.speed;
        animationClip.wrapMode = animaData.wrapMode;
        animationClip.name = animaData.name;
        anim.addClip(animationClip, animaData.name);
        isPlay && anim.play(animaData.name);


    }

    /**启动命令行参数 */
    static getCommandLineArgs() {
        let querys = ElectronAPI.getQuery();
        let args = {} as any;
        for (let i = 0; i < querys.length; i++) {
            let arg = querys[i];
            if (arg.startsWith("-")) {
                var index = arg.indexOf("=");
                if (index > -1) {
                    var key = arg.substring(1, index);
                    var val = arg.substring(index + 1, arg.length);
                    args[`${key}`] = val;
                    console.log("命令行参数：" + arg + '  key:', key + '  val:', val);
                }
            }
        }
        return args
    }
    static setWindow() {
        ElectronAPI.window();
    }
    // 关闭开发者工具
    static closeDevTools() {
        ElectronAPI.closeDevTools()

    }
    // 设置分辨率，这才是最终调用的接口，如果全屏设置分辨率，不全屏设置窗口大小
    static setScreenResolution(width: number, height: number) {
        ElectronAPI.setScreenResolution(width, height)
    }
    // 设置窗口大小
    static setSize(width: number, height: number) {
        ElectronAPI.setSize(width, height)
        // ipcRenderer.send("e_setSize", width.toString(), height.toString());
    }
    static openDevTool() {
        ElectronAPI.openDevTools();
    }
    // 设置分辨率，修改的电脑的分辨率
    static setResolution(width: number, height: number) {
        ElectronAPI.setResolution(width, height);
        //ipcRenderer.send("e_setResolution", width.toString(), height.toString());
    }
    static log(type: string, msg: string) {
        ElectronAPI.log(type, msg);
    }
    // 监听数据变化
    static listenDataChange(cls: any, list: Array<listenDataType>) {
        list.forEach(e => {
            Object.defineProperty(cls, e.key, {
                get() {
                    cls._store = cls._store || {}
                    return cls._store[e.key]
                },
                set(data) {
                    cls._store = cls._store || {}
                    if (e.modifyFunc) {
                        const result = e.modifyFunc(data, cls._store[e.key])
                        if (result) {
                            data = result
                        }
                    }
                    cls._store[e.key] = data
                    if (e.boardcast) {
                        xcore.event.raiseEvent(e.boardcast, data);
                    }
                }
            })
        })
    }



}