import { ExternalMessage } from '../../protos/ExternalMessage'
import { EventManager } from '../EventManager'
import { GameEvent } from './GameEvent'
import MessageCtrl from './MessageCtrl'


import { <PERSON><PERSON>, director } from 'cc'

export interface sendInfo {
	/** 协议开关，用于一些协议级别的开关控制，比如 安全加密校验等。 : 0 不校验 */
	protocolSwitch: number
	/** 消息ID */
	cmd: number
	/** 二级消息ID */
	subCmd: number
	/** 数据 */
	data: Uint8Array
}

export enum SocketLogLevel {
	All,
	Log,
	Warn,
	Error,
	/** 关闭日志 */
	None
}

interface GameSocketConfig {
	/**
	 * 日志级别
	 */
	logLevel?: SocketLogLevel
	/**
	 * 乒乓包超时时间(秒)
	 */
	pingpongMax?: number
}

export class GameSocket extends EventManager {
	/**
	 * 回调消息处理
	 * @private
	 */
	private static messageCtrls = new Map<number, MessageCtrl>()

	/**
	 * 主链接,一般为最后一个
	 */
	public static get mainSocket() {
		if (this.sockets != null && this.sockets.length > 0)
			return this.sockets[this.sockets.length - 1]
		return null
	}

	static sockets: Array<GameSocket> = []

	/** 日志等级 */
	public logLevel: SocketLogLevel
	public pingpongEnable = true

	/** 链接服务器 */
	public set url(v: string) {
		this._url = v
	}

	private socket: WebSocket
	private pingpongMax = -1
	private pingpongIndex = 0
	private pingpong: any
	private _url: string

	static createCls(url?: string, exp: GameSocketConfig = {}) {
		if (exp.logLevel === undefined) exp.logLevel = SocketLogLevel.Error
		if (!exp.pingpongMax) exp.pingpongMax = 10

		let cls = new Node().addComponent(GameSocket) as GameSocket
		director.addPersistRootNode(cls.node)

		if (cls._url != url) cls._url = url
		if (cls.logLevel != exp.logLevel) cls.logLevel = exp.logLevel
		if (cls.pingpongMax != exp.pingpongMax) cls.pingpongMax = exp.pingpongMax
		return cls
	}

	/**
	 * 连接服务器
	 */
	public connect = () => {
		this.socket = new WebSocket(this._url)
		this.socket.binaryType = 'arraybuffer'

		this.socket.onopen = this.onSocketOpen
		this.socket.onclose = this.onSocketClose
		this.socket.onmessage = this.onMessageReveived
		this.socket.onerror = this.onConnectError
		GameSocket.sockets.push(this)
	}

	/**
	 * 发送ioGame数据
	 * @param info
	 */
	public send(info: sendInfo) {
		this.log(info)
		if (this.socket.readyState !== 1) {
			this.error('socket未准备好,无法发送：' + `发送ID! cmd:${info.cmd} sub:${info.subCmd}`)
			return
		}
		let s = {
			cmdCode: 1,
			protocolSwitch: info.protocolSwitch,
			cmdMerge: (info.cmd << 16) + info.subCmd,
			responseStatus: 100,
			validMsg: '',
			data: info.data
		}
		this.log('sendMsg', JSON.stringify(s))
		this.log(`发送ID! cmd:${info.cmd} sub:${info.subCmd}`)
		this.socket.send(ExternalMessage.encode(s).finish())
	}

	/**
	 * 关闭服务
	 */
	close = (code = 1000) => {
		this.log('执行断开链接')
		this.socket?.close(code, '客户端执行断开')
	}

	public static onListenMessage(code: number, ctrl: MessageCtrl) {
		if (GameSocket.messageCtrls.has(code)) {
			let cmd = code >> 16
			this.mainSocket.error(`重复ID cmd:${cmd} sub:${code - (cmd << 16)}`)
			return
		}
		GameSocket.messageCtrls.set(code, ctrl)
	}

	private onSocketOpen = (ev: Event) => {
		this.log('Connected', ev)
		this.startPingPong()
		this.emit(GameEvent.OPEN, ev)

	}

	// https://developer.mozilla.org/zh-CN/docs/Web/API/CloseEvent
	private onSocketClose = (ev: CloseEvent) => {
		this.log('Socket closed', ev)
		this.stopPingPong()
		//销毁
		this.emit(GameEvent.CLOSE, ev)
		//移除断开socket
		GameSocket.sockets.splice(GameSocket.sockets.indexOf(this), 1)
		this.node.destroy()
	}

	private onMessageReveived = (message: MessageEvent) => {
		let data = message.data
		 
		if (typeof data == 'string') {
			this.log(`Message from server string: ${data}`)
		} else if (data instanceof ArrayBuffer) {
			let info = ExternalMessage.decode(new Uint8Array(data))
			if (info.cmdCode == 1) {
				//心跳不打印
				this.log(`Message from server info: ${JSON.stringify(info)}`)
			}
			try {
				if (info.cmdCode == 0) {
					//心跳包
					this.pingpongIndex = 0
				} else if (GameSocket.messageCtrls.has(info.cmdMerge)) {
					let action = GameSocket.messageCtrls.get(info.cmdMerge)
					action && action.receive(info.responseStatus, info.data)

					if (this.logLevel <= SocketLogLevel.Log) {
						let cmd = info.cmdMerge >> 16
						this.log(`收到ID! cmd:${cmd} sub:${info.cmdMerge - (cmd << 16)}`)
					}
				} else {
					let cmd = info.cmdMerge >> 16
					this.error(`获取ID!失败,未注册ID cmd:${cmd} sub:${info.cmdMerge - (cmd << 16)}`)
				}
				this.log("info:::",info, GameSocket.messageCtrls.get(info.cmdMerge))
				 
			} catch (e) {
				this.error(e)
			}
		}
		//console.log('onmessage', data,)
		this.emit(GameEvent.MESSAGE, data)
	}

	private onConnectError = (ev: Event) => {
		this.log('connect error', ev)
		this.emit(GameEvent.ERROR, ev)
	}

	private startPingPong() {
		this.log('开启心跳包')
		if (!this.pingpongEnable) return

		this.stopPingPong()
		let pingPongInfo = ExternalMessage.encode({
			cmdCode: 0,
			protocolSwitch: 1,
			cmdMerge: (1 << 16) + 2,
			responseStatus: 100,
			validMsg: '',
			data: new Uint8Array(0)
		}).finish()
		this.pingpong = setInterval(() => {
			this.socket.send(pingPongInfo)
			// 如何后端长时间不响应手动断开
			if (++this.pingpongIndex > this.pingpongMax) {
				this.close(3001)
			}
			this.log(`当前心跳包index : ${this.pingpongIndex} max: ${this.pingpongMax}`)
		}, 1000)
	}

	private stopPingPong() {
		clearInterval(this.pingpong)
	}

	log = (...data: any[]) => {
		if (this.logLevel > SocketLogLevel.Log) return
		console.log('gameSocket!!', ...data)
	}
	warn = (...data: any[]) => {
		if (this.logLevel > SocketLogLevel.Warn) return
		console.warn('gameSocket!!', ...data)
	}
	error = (...data: any[]) => {
		if (this.logLevel > SocketLogLevel.Error) return
		console.error('gameSocket!!', ...data)
	}
	get state() {
		return this.socket?.readyState
	}
}
