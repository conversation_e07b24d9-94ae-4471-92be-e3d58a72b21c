import { _decorator, Component, Label, Node, size, Sprite } from 'cc';
import { ViewBase } from '../../scripts/libs/manager/ViewBase';
import { xcore } from '../../scripts/libs/xcore';
const { ccclass, property } = _decorator;

@ccclass('ViewWingShow')
export class ViewWingShow extends ViewBase {
    @property(Sprite)
    private sprWing: Sprite = null;

    @property(Label)
    private lbName: Label = null;

    @property(Label)
    private lbProp: Label = null;
 

    public setData(data: any): void {
        //   img: this._wingImg, name: this._wingName, prop: this._wingProp
        xcore.res.remoteLoadSprite(data.img, this.sprWing, size(300, 300));
        this.lbName.string = data.name;
        this.lbProp.string = data.prop;

    }
}


