[1, ["52fHu7D8hGm5vLaoALoXCl", "5f/2JI47xJfahIXaf8QG77@f9941", "aedBSY9blB5IZeO3T3kxHe", "19F57FMVRJOb7NBNCTGNac@f9941", "4bbyTqTcNPnrmVLDfyjIrt@f9941", "ff3reVIcZLdo1ocsK9Lga3@f9941", "9bBYHzhH5F25cHFMhUL3oq@f9941", "67E/Vobn1A7ImerLBhca6R@f9941", "46IdxunHlHYYawWmS60uVs@f9941", "7e26QKT+BBu4iMDq4Ttplf@f9941", "67v5/ycF9OdKujLVANrIzt", "c4QFjyHkxBQoGsOs+ecoqi@f9941"], ["node", "_spriteFrame", "_font", "root", "data", "target", "tmpPrefab", "lbOptionTxt", "btnOption3", "btnOption2", "btnOption", "lbScore", "lbName", "spr<PERSON><PERSON><PERSON>", "ndList", "ndUserData", "pfbUnitSkinShow", "ndUserSkinOn", "lbBtnUserSkinTxt", "btnUserSkin", "ndShowOn", "lbBtnShowTxt", "btnShow", "ndEmpty", "listPlayer", "listShow", "btnClose", "sprFrame", "ndRoot"], [["cc.Node", ["_name", "_layer", "_active", "_components", "_prefab", "_parent", "_children", "_lpos"], 0, 9, 4, 1, 2, 5], ["cc.Label", ["_string", "_actualFontSize", "_isSystemFontUsed", "_fontSize", "_lineHeight", "_outlineWidth", "node", "__prefab", "_color", "_font", "_outlineColor"], -3, 1, 4, 5, 6, 5], ["cc.Layout", ["_resizeMode", "_layoutType", "_spacingY", "_affectedByScale", "_spacingX", "_paddingTop", "node", "__prefab"], -3, 1, 4], ["cc.Sprite", ["_sizeMode", "_type", "node", "__prefab", "_spriteFrame"], 1, 1, 4, 6], ["cc.Node", ["_name", "_layer", "_parent", "_components", "_prefab", "_children", "_lpos"], 1, 1, 12, 4, 2, 5], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.Mask", ["_type", "node", "__prefab"], 2, 1, 4], ["cc.<PERSON><PERSON>", ["_transition", "_zoomScale", "node", "__prefab", "_normalColor", "_target"], 1, 1, 4, 5, 1], ["1465aK3BeFERYvLMRafKnXR", ["templateType", "_virtual", "_updateRate", "frameByFrameRenderNum", "node", "__prefab", "pageChangeEvent", "renderEvent", "selectedEvent", "tmpPrefab"], -1, 1, 4, 4, 4, 4, 6], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["71292Ab6ztNzq8BbT/g7S2d", ["node", "__prefab", "ndUserData", "ndList", "spr<PERSON><PERSON><PERSON>", "lbName", "lbScore", "btnOption", "btnOption2", "btnOption3", "lbOptionTxt", "pfbUnitSkinShow"], 3, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 6], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.Graphics", ["node", "__prefab", "_fillColor"], 3, 1, 4, 5], ["3d6dczHIV5GTrJmaqMJGaFs", ["node", "__prefab", "ndRoot", "sprFrame", "btnClose", "listShow", "listPlayer", "ndEmpty", "btnShow", "lbBtnShowTxt", "ndShowOn", "btnUserSkin", "lbBtnUserSkinTxt", "ndUserSkinOn"], 3, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["cc.Widget", ["_alignFlags", "_left", "_top", "node", "__prefab"], 0, 1, 4], ["<PERSON><PERSON>", ["horizontal", "node", "__prefab", "_content"], 2, 1, 4, 1], ["d1a26AN+WBOrL2OAdolqlDt", ["node", "__prefab", "sv"], 3, 1, 4, 1]], [[11, 0, 2], [13, 0, 1, 2, 3, 4, 5, 5], [5, 0, 1, 2, 1], [5, 0, 1, 2, 3, 1], [3, 2, 3, 4, 1], [0, 0, 1, 5, 3, 4, 7, 3], [0, 0, 1, 5, 6, 3, 4, 7, 3], [4, 0, 1, 2, 5, 3, 4, 6, 3], [4, 0, 1, 2, 3, 4, 6, 3], [7, 0, 1, 2, 3, 3], [0, 0, 1, 5, 3, 4, 3], [9, 1], [4, 0, 1, 2, 3, 4, 3], [14, 0, 1, 2, 1], [10, 0, 2], [4, 0, 1, 2, 5, 3, 4, 3], [5, 0, 1, 1], [3, 0, 2, 3, 4, 2], [6, 1, 2, 1], [1, 0, 1, 3, 2, 6, 7, 8, 9, 5], [1, 0, 1, 3, 4, 2, 5, 6, 7, 8, 10, 7], [1, 0, 1, 2, 6, 7, 8, 4], [9, 0, 1, 2, 3], [17, 0, 1, 2, 3, 2], [18, 0, 1, 2, 1], [0, 0, 1, 6, 3, 4, 7, 3], [0, 0, 1, 6, 3, 4, 3], [0, 0, 1, 5, 6, 3, 4, 3], [0, 0, 2, 1, 5, 3, 4, 4], [2, 0, 1, 2, 3, 6, 7, 5], [2, 0, 1, 6, 7, 3], [2, 0, 1, 4, 2, 3, 6, 7, 6], [2, 0, 1, 5, 4, 2, 6, 7, 6], [2, 0, 1, 5, 4, 2, 3, 6, 7, 7], [12, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 1], [3, 2, 3, 1], [3, 1, 2, 3, 4, 2], [3, 1, 0, 2, 3, 3], [6, 0, 1, 2, 2], [1, 0, 1, 3, 4, 2, 5, 6, 7, 8, 10, 9, 7], [1, 0, 1, 3, 2, 6, 7, 8, 5], [1, 0, 1, 2, 6, 7, 8, 9, 4], [7, 0, 2, 3, 4, 5, 2], [15, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 1], [8, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 5], [8, 0, 4, 5, 6, 7, 8, 9, 2], [16, 0, 1, 2, 3, 4, 4]], [[[[14, "UnitSkinUser"], [25, "UnitSkinUser", 33554432, [-14, -15], [[3, -2, [0, "7bHo2K2phCm50LWhsDHazw"], [5, 776, 154], [0, 0.5, 1]], [29, 1, 2, 10, true, -3, [0, "e4lmymSeNOV6pS2X+k6/lL"]], [34, -13, [0, "faSHqRNpVK94A/q7o+de0/"], -12, -11, -10, -9, -8, -7, -6, -5, -4, 9]], [1, "adIMZgSAlKqq8sfGX/jeyQ", null, null, null, -1, 0], [1, 0, -72, 0]], [6, "ndUserData", 33554432, 1, [-18, -19, -20, -21, -22, -23, -24, -25], [[2, -16, [0, "e0Vngu4OlPxauYIIKzKmpq"], [5, 776, 144]], [4, -17, [0, "0brlIyXZNMpqSxnTWrtzlR"], 8]], [1, "93BwTTCgpB0L+0G6uXHQ4D", null, null, null, 1, 0], [1, 0, -72, 0]], [7, "btnOption1", 33554432, 2, [-29], [[[2, -26, [0, "4aMq8nnYhOS4CLrV3Lfkb6"], [5, 137, 66]], [4, -27, [0, "77SXIwjaBFTJhGls2vaTcW"], 0], -28], 4, 4, 1], [1, "c8cua6auNEm7XpykoWJjVe", null, null, null, 1, 0], [1, 3.3460000000000036, 0, 0]], [7, "btnOption2", 33554432, 2, [-33], [[[2, -30, [0, "57p0IjZBpIGappH8qIczxX"], [5, 137, 66]], [4, -31, [0, "db3PQe/iJMCr4c1ViHuy0y"], 2], -32], 4, 4, 1], [1, "5flUKUXZNME7yZs/T06AKf", null, null, null, 1, 0], [1, 151.93150000000003, 0, 0]], [7, "btnOption-3", 33554432, 2, [-37], [[[2, -34, [0, "84MypXrPtBvKJQdGq80des"], [5, 137, 66]], [4, -35, [0, "ebkY5kTyRPgaLmNfsidcFE"], 4], -36], 4, 4, 1], [1, "aa5ZkMif1JkKiUIWl6zrnA", null, null, null, 1, 0], [1, 300.51700000000005, 0, 0]], [6, "Node", 33554432, 2, [-41], [[2, -38, [0, "6fFe6IVOBONZdAFSnjNZ12"], [5, 90, 90]], [38, 1, -39, [0, "33NA93adpCwI01c2zUgO9d"]], [13, -40, [0, "ad1jWq9L5C+pXIEbH4bQsi"], [4, 16777215]]], [1, "06GF/cQLtGpbT791w50Myw", null, null, null, 1, 0], [1, -318.03999999999996, 0, 0]], [6, "ndScore", 33554432, 2, [-44, -45], [[3, -42, [0, "f7jsSmSwlIMooYyMIQ3VwJ"], [5, 121.41998291015625, 100], [0, 0, 0.5]], [30, 1, 1, -43, [0, "25O6XuLNhI6qHniGBFBKje"]]], [1, "fa6qr77QtOwrjhqrTCQgU1", null, null, null, 1, 0], [1, -244.85899999999998, 10.05600000000004, 0]], [5, "ndSelectList", 33554432, 1, [[2, -46, [0, "4bzFnoc+lG8LnzwXcZB25L"], [5, 700, 0]], [31, 1, 3, 20, 20, true, -47, [0, "ccSs9J8GxOBogSc9UA9Rdz"]]], [1, "95/UOIkvBLbbTj32mH8mlW", null, null, null, 1, 0], [1, 0, -154, 0]], [8, "Label", 33554432, 3, [[[2, -48, [0, "c4U7s4I0FKw6fpT/QYnvpv"], [5, 60, 50.4]], -49], 4, 1], [1, "1eyirD0n1Kz5Fp+n4eVjQy", null, null, null, 1, 0], [1, 0, 1.502999999999929, 0]], [5, "Label", 33554432, 4, [[2, -50, [0, "9bvVgAjpxPTKaw4dfjFf3q"], [5, 60, 50.4]], [19, "灵珠", 30, 30, false, -51, [0, "94s1uCxaNOI7Xbk+vsZV6P"], [4, 4281023312], 1]], [1, "40c6DaNtxG9ot+ntmAoBF0", null, null, null, 1, 0], [1, 0, 1.502999999999929, 0]], [5, "Label", 33554432, 5, [[2, -52, [0, "a6SY5zrmZFBou6ij+O6IMK"], [5, 60, 50.4]], [19, "属性", 30, 30, false, -53, [0, "beQ3IFiDNKx6cLJrmZqoho"], [4, 4281023312], 3]], [1, "8fdpIYGIdPdq3vvWG1QTx2", null, null, null, 1, 0], [1, 0, 1.502999999999929, 0]], [5, "sprAFrame", 33554432, 2, [[2, -54, [0, "d8jXfhjSNEVaJ1V0fCy4T0"], [5, 90, 90]], [17, 0, -55, [0, "5cMfq0UalJoZMhBKFFrtnq"], 5]], [1, "d8/HUF6r1HzbyGhqmVe1D+", null, null, null, 1, 0], [1, -318.03499999999997, 0.8240000000000691, 0]], [8, "spr<PERSON><PERSON><PERSON>", 33554432, 6, [[[2, -56, [0, "1buaha8GdEo42xZe5mbHI6"], [5, 90, 90]], -57], 4, 1], [1, "1cYHmDyehGnL6xG5igr0+t", null, null, null, 1, 0], [1, 0, 1.6889999999999645, 0]], [5, "sprMask", 33554432, 2, [[2, -58, [0, "c4K5sQMChPh6RXxEtFvzQ6"], [5, 90, 90]], [17, 0, -59, [0, "6c4I/aYxNPnaKJqKfd0yz5"], 6]], [1, "7bQey2kFNFDZQnYOXo1Ktn", null, null, null, 1, 0], [1, -318.03999999999996, 1.6890000000000782, 0]], [8, "lbName", 33554432, 2, [[[3, -60, [0, "2b3A8FaGBI0YV02pzskiSF"], [5, 78, 75.6], [0, 0, 0.5]], -61], 4, 1], [1, "df9uNenA5HXLgOmG5nMvpd", null, null, null, 1, 0], [1, -244.85899999999998, 23.883000000000038, 0]], [5, "lbName-001", 33554432, 7, [[3, -62, [0, "bf+Qz2y7xJ+YFA80LGgjdG"], [5, 97.5, 75.6], [0, 0, 0.5]], [39, "月得分：", 26, 26, 60, false, 3, -63, [0, "71LG70buRFNqiAZ8ZYX+7K"], [4, 4279772212], [4, 4278987924], 7]], [1, "28+P8ZqLxNjqAWUBUK1nfG", null, null, null, 1, 0], [1, 0, -32.07400000000007, 0]], [8, "lbScore", 33554432, 7, [[[3, -64, [0, "3f+ut0nVhBhYHDwoeiwtwX"], [5, 23.91998291015625, 75.6], [0, 0, 0.5]], -65], 4, 1], [1, "5fwNafyAJBPJ+h31XDhb1r", null, null, null, 1, 0], [1, 97.5, -33.86699999999996, 0]], [40, "皮肤", 30, 30, false, 9, [0, "a5Oudwr1VHGpghaI/vyQ/j"], [4, 4281023312]], [9, 3, 1.02, 3, [0, "86O+btNrlEX7415Q877bTO"]], [9, 3, 1.02, 4, [0, "44oAcLCjRA/IW12YYxaS7/"]], [9, 3, 1.02, 5, [0, "8bqFCgUkdPBZQFEgfyvLQc"]], [35, 13, [0, "2fAjPP0NlOl7Cf3ysMVCt2"]], [20, "用户名", 26, 26, 60, false, 3, 15, [0, "90un6v+xFPNKFDoecJxtia"], [4, 4279772212], [4, 4278987924]], [20, "--", 26, 26, 60, false, 3, 17, [0, "a1poLmr55LgI+KMpp11Wdh"], [4, 4279345926], [4, 4278987924]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, 7, 18, 0, 8, 21, 0, 9, 20, 0, 10, 19, 0, 11, 24, 0, 12, 23, 0, 13, 22, 0, 14, 8, 0, 15, 2, 0, 0, 1, 0, -1, 2, 0, -2, 8, 0, 0, 2, 0, 0, 2, 0, -1, 3, 0, -2, 4, 0, -3, 5, 0, -4, 12, 0, -5, 6, 0, -6, 14, 0, -7, 15, 0, -8, 7, 0, 0, 3, 0, 0, 3, 0, -3, 19, 0, -1, 9, 0, 0, 4, 0, 0, 4, 0, -3, 20, 0, -1, 10, 0, 0, 5, 0, 0, 5, 0, -3, 21, 0, -1, 11, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, -1, 13, 0, 0, 7, 0, 0, 7, 0, -1, 16, 0, -2, 17, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, -2, 18, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, -2, 22, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, -2, 23, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, -2, 24, 0, 4, 1, 65], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 23, 24], [1, 2, 1, 2, 1, 1, 1, 2, 1, 16, 2, 2, 2], [1, 0, 1, 0, 1, 5, 6, 0, 7, 2, 0, 0, 0]], [[[14, "ViewSkinShow"], [26, "ViewSkinShow", 33554432, [-16], [[16, -2, [0, "beEa8pG1lCXr1N2jEF0MjR"]], [43, -15, [0, "5a7AIq7kBOFpHNXy24XWoC"], -14, -13, -12, -11, -10, -9, -8, -7, -6, -5, -4, -3]], [1, "7fYHW+QyxN74yH4eQLngzl", null, null, null, -1, 0]], [27, "ndRoot", 33554432, 1, [-18, -19, -20, -21, -22, -23, -24], [[16, -17, [0, "c3Kal7wMJN7bZMneDvXZ8f"]]], [1, "2bMheHmXlECZDYbrl9fxf4", null, null, null, 1, 0]], [15, "svPlayer", 33554432, 2, [-30, -31], [[[2, -25, [0, "5amyvjmFNEJp+vKUPesNvr"], [5, 800, 1000]], -26, [44, 2, false, 3, 6, -28, [0, "3ac9f0k4VEzLbjm6T0+Z+i"], [11], [22, "d1a26AN+WBOrL2OAdolqlDt", "onList<PERSON><PERSON>", -27], [11], 4], -29], 4, 1, 4, 1], [1, "6cpSYji7VCb4jxDdjC66su", null, null, null, 1, 0]], [15, "svShow", 33554432, 2, [-37], [[[2, -32, [0, "8fMYnj0uVLpZRAqT7GOGO/"], [5, 800, 1000]], -33, [45, 2, -35, [0, "4bdDpA82JIuImcyC8tV+3d"], [11], [22, "d1a26AN+WBOrL2OAdolqlDt", "onList<PERSON><PERSON>", -34], [11], 2], -36], 4, 1, 4, 1], [1, "36bL8+6x9PIYITqg/rEWx8", null, null, null, 1, 0]], [8, "btnClose", 33554432, 2, [[[2, -38, [0, "30Kht1ye9JKaHE/Hw26BYF"], [5, 112, 113]], [36, 1, -39, [0, "e26qUsKyVKvJxE3Mxd5AUZ"], 0], -40, [46, 9, 361.706, -572.5899999999999, -41, [0, "1e4h6x0jxJrJ7Pwc8zkJCb"]]], 4, 4, 1, 4], [1, "04c+9FE5RJDYnD0mdeDhgF", null, null, null, 1, 0], [1, 367.706, 566.0899999999999, 0]], [7, "btnUserSkin", 33554432, 2, [-45, -46], [[[2, -42, [0, "6f9HxGekVFIrRVOzDE1ImI"], [5, 301, 92]], [4, -43, [0, "4cAjKS6lpNa7Adv4giPCwh"], 6], -44], 4, 4, 1], [1, "79b357CZtOyLOCLZ6HtTSp", null, null, null, 1, 0], [1, 200, -694.6610000000001, 0]], [7, "btnShow", 33554432, 2, [-50, -51], [[[2, -47, [0, "16ukIyMaxKvYFFf6TSmsII"], [5, 301, 92]], [4, -48, [0, "6cNb2856RIN6XuYnou9FIU"], 8], -49], 4, 4, 1], [1, "c43uKS82lDYYbrAfwLvfvG", null, null, null, 1, 0], [1, -200, -694.6610000000001, 0]], [6, "view", 33554432, 4, [-55], [[3, -52, [0, "2fp7D4fu9Oc44KNxfUlPQ9"], [5, 800, 1000], [0, 0.5, 1]], [18, -53, [0, "62bujwsh1O2aAIRNagYG5L"]], [13, -54, [0, "7dqahAqt9IxIxriE0a5tBm"], [4, 16777215]]], [1, "24eeMUFvRLaIyPIWXXcs9q", null, null, null, 1, 0], [1, 0, 500, 0]], [6, "view", 33554432, 3, [-59], [[3, -56, [0, "edrNL5aZxInaSVZxYDvPCQ"], [5, 800, 1000], [0, 0.5, 1]], [18, -57, [0, "c0yFQ+p8JPxKXSZmP6DisC"]], [13, -58, [0, "33SKUKREFJ4oxZl5xwCKc9"], [4, 16777215]]], [1, "f6iaottbBBxKxaXOCuaH/E", null, null, null, 1, 0], [1, 0, 500, 0]], [10, "content", 33554432, 8, [[3, -60, [0, "90h+ToCH1E3aRyLn32nHbR"], [5, 700, 20], [0, 0.5, 1]], [32, 1, 3, 20, 20, 20, -61, [0, "29ANPoY0ZOkJwMPzy1wKaw"]]], [1, "b5qIUa3rlMWbsAzgWx4uul", null, null, null, 1, 0]], [10, "content", 33554432, 9, [[3, -62, [0, "c53LVYB1BCarSQdIefWrRv"], [5, 800, 0], [0, 0.5, 1]], [33, 1, 2, 20, 20, 20, true, -63, [0, "bfoJR0nHlDXrXXqSe8WhIs"]]], [1, "f7oJSOz5dO9Lm84WYUbwXK", null, null, null, 1, 0]], [10, "ndEmpty", 33554432, 3, [[2, -64, [0, "a2SjV5AwVKeoMIdBBkN50F"], [5, 160, 50.4]], [41, "暂无数据", 40, false, -65, [0, "b5AQOOqC1JFYtx0DFYhPLs"], [4, 4282074287], 3]], [1, "bcM4zvHeBEU6U6nS90K2T9", null, null, null, 1, 0]], [28, "ndOn", false, 33554432, 6, [[2, -66, [0, "c7/3M9dgxApbTkH1dHXdDX"], [5, 301, 92]], [4, -67, [0, "a82nYmOxRAHowG4fXGz93c"], 5]], [1, "d4TZyje61AWZovQfhjVRY+", null, null, null, 1, 0]], [10, "ndOn", 33554432, 7, [[2, -68, [0, "f5XYAfKl5FQIoAeUE5Vcbn"], [5, 301, 92]], [4, -69, [0, "93EHDLTHNOx6ziQSWe/63J"], 7]], [1, "d2a4Z9y8FBQqAdRXAzM2Ev", null, null, null, 1, 0]], [12, "sprFrame", 33554432, 2, [[[2, -70, [0, "a28zRpuZhPFIVcExuMVloo"], [5, 800, 1200]], -71], 4, 1], [1, "71CB2XMKdC5oEzix/+jy8K", null, null, null, 1, 0]], [5, "game_title_viewskinshow", 33554432, 2, [[2, -72, [0, "48d+X5RB9Kxo4xSmitT59z"], [5, 254, 46]], [4, -73, [0, "5281DQaFdGkYyZtff7gaCc"], 1]], [1, "1d/+I1265Miqbkt5WjHP2Q", null, null, null, 1, 0], [1, 0, 562.731, 0]], [12, "Label", 33554432, 6, [[[2, -74, [0, "507cZhJ8JAP6O8tzlu7iCC"], [5, 200, 50.4]], -75], 4, 1], [1, "68LqaPRgVLUqm1xsfBGorS", null, null, null, 1, 0]], [12, "Label", 33554432, 7, [[[2, -76, [0, "175YiV6s9LzKLFoluvZCT9"], [5, 160, 50.4]], -77], 4, 1], [1, "592kUJZ5VJu7+9Cto3Js6/", null, null, null, 1, 0]], [37, 1, 0, 15, [0, "a31vyYG7JAMpIc9PplAjF3"]], [42, 3, 5, [0, "e6UzAXGxdL3rg1wW/uA0PX"], [4, 4292269782], 5], [23, false, 4, [0, "a6lDOHsC1Epqv/5vU1A9Wb"], 10], [24, 4, [0, "6dzY1+EjhKHZT/6zM2rkqB"], 21], [23, false, 3, [0, "41oncBE95InphQiYL1b54g"], 11], [24, 3, [0, "f6dE897ExMCLb08V7/gDws"], 23], [21, "直播间皮肤", 40, false, 17, [0, "11EZviBnRB4JmMhZti0GRM"], [4, 4282074287]], [9, 3, 1.02, 6, [0, "28TevEE5FG5YWdI3niIdPp"]], [21, "皮肤展示", 40, false, 18, [0, "68xCLZKSRD65bRPF5MTK+J"], [4, 4291482321]], [9, 3, 1.02, 7, [0, "c0HCYGU+FNmaNCpCgCkjde"]]], 0, [0, 3, 1, 0, 0, 1, 0, 17, 13, 0, 18, 25, 0, 19, 26, 0, 20, 14, 0, 21, 27, 0, 22, 28, 0, 23, 12, 0, 24, 24, 0, 25, 22, 0, 26, 20, 0, 27, 19, 0, 28, 2, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, -1, 15, 0, -2, 5, 0, -3, 16, 0, -4, 4, 0, -5, 3, 0, -6, 6, 0, -7, 7, 0, 0, 3, 0, -2, 23, 0, 5, 3, 0, 0, 3, 0, -4, 24, 0, -1, 9, 0, -2, 12, 0, 0, 4, 0, -2, 21, 0, 5, 4, 0, 0, 4, 0, -4, 22, 0, -1, 8, 0, 0, 5, 0, 0, 5, 0, -3, 20, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, -3, 26, 0, -1, 13, 0, -2, 17, 0, 0, 7, 0, 0, 7, 0, -3, 28, 0, -1, 14, 0, -2, 18, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, -1, 10, 0, 0, 9, 0, 0, 9, 0, 0, 9, 0, -1, 11, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, -2, 19, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, -2, 25, 0, 0, 18, 0, -2, 27, 0, 4, 1, 77], [0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 25, 27], [1, 1, 6, 2, 6, 1, 1, 1, 1, 1, 2, 2], [8, 9, 2, 0, 10, 3, 4, 3, 4, 11, 0, 0]]]]