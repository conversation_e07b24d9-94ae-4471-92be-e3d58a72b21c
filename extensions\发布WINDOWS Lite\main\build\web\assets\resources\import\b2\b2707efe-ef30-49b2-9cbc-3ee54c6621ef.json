[1, 0, 0, [["cc.Json<PERSON>set", ["_name", "json"], 1]], [[0, 0, 1, 3]], [[0, "viewconfig", {"ViewMatch": {"bundleName": "a<PERSON><PERSON><PERSON>", "viewName": "ViewMatch", "path": "/prefab/ViewMatch"}, "ViewGameOver": {"bundleName": "abGame", "viewName": "ViewGameOver", "path": "/prefab/ViewGameOver"}, "ViewTestGift": {"bundleName": "abGame", "viewName": "ViewTestGift", "path": "/prefab/ViewTestGift"}, "ViewToast": {"bundleName": "resources", "viewName": "ViewToast", "path": "/prefab/ViewToast"}, "ViewSelectGameLevel": {"bundleName": "abGame", "viewName": "ViewSelectGameLevel", "path": "/prefab/ViewSelectGameLevel"}, "ViewSelectGameMode": {"bundleName": "abGame", "viewName": "ViewSelectGameMode", "path": "/prefab/ViewSelectGameMode"}, "ViewFightRank": {"bundleName": "abGame", "viewName": "ViewFightRank", "path": "/prefab/ViewFightRank"}, "ViewRank": {"bundleName": "abGame", "viewName": "ViewRank", "path": "/prefab/ViewRank"}, "ViewLogin": {"bundleName": "resources", "viewName": "ViewLogin", "path": "/prefab/ViewLogin"}, "ViewSkinSelect": {"bundleName": "abGame", "viewName": "ViewSkinSelect", "path": "/prefab/ViewSkinSelect"}, "ViewRoundToast": {"bundleName": "abGame", "viewName": "ViewRoundToast", "path": "/prefab/ViewRoundToast"}, "ViewSetting": {"bundleName": "abGame", "viewName": "ViewSetting", "path": "/prefab/ViewSetting"}, "ViewCommonVideo": {"bundleName": "resources", "viewName": "ViewCommonVideo", "path": "/prefab/ViewCommonVideo"}, "ViewBossMsg": {"bundleName": "abGame", "viewName": "ViewBossMsg", "path": "/prefab/ViewBossMsg"}, "ViewCommonTips": {"bundleName": "abGame", "viewName": "ViewCommonTips", "path": "/prefab/ViewCommonTips"}, "ViewSkinShow": {"bundleName": "abGame", "viewName": "ViewSkinShow", "path": "/prefab/ViewSkinShow"}, "ViewOpenBox": {"bundleName": "abGame", "viewName": "ViewOpenBox", "path": "/prefab/ViewOpenBox"}, "ViewSkinDebrisReward": {"bundleName": "abGame", "viewName": "ViewSkinDebrisReward", "path": "/prefab/ViewSkinDebrisReward"}, "ViewSkinReward": {"bundleName": "abGame", "viewName": "ViewSkinReward", "path": "/prefab/ViewSkinReward"}, "ViewSkinDetail": {"bundleName": "abGame", "viewName": "ViewSkinDetail", "path": "/prefab/ViewSkinDetail"}, "ViewPowerRank": {"bundleName": "abGame", "viewName": "ViewPowerRank", "path": "/prefab/ViewPowerRank"}, "ViewTowerLevUp": {"bundleName": "abGame", "viewName": "ViewTowerLevUp", "path": "/prefab/ViewTowerLevUp"}, "ViewCrossRewardDesc": {"bundleName": "abGame", "viewName": "ViewCrossRewardDesc", "path": "/prefab/ViewCrossRewardDesc"}, "ViewUserInfo": {"bundleName": "abGame", "viewName": "ViewUserInfo", "path": "/prefab/ViewUserInfo"}, "ViewDimondReward": {"bundleName": "abGame", "viewName": "ViewDimondReward", "path": "/prefab/ViewDimondReward"}, "ViewWingShow": {"bundleName": "abGame", "viewName": "ViewWingShow", "path": "/prefab/ViewWingShow"}, "ViewTaskRewardDesc": {"bundleName": "abGame", "viewName": "ViewTaskRewardDesc", "path": "/prefab/ViewTaskRewardDesc"}, "ViewSelectGameType": {"bundleName": "abGame", "viewName": "ViewSelectGameType", "path": "/prefab/ViewSelectGameType"}, "ViewExchange": {"bundleName": "abGame", "viewName": "ViewExchange", "path": "/prefab/ViewExchange"}, "ViewExchangeShow": {"bundleName": "abGame", "viewName": "ViewExchangeShow", "path": "/prefab/ViewExchangeShow"}}]], 0, 0, [], [], []]