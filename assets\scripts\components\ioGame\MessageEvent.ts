/**
 * 主ID
 */
export const MessageCmdEvent = {
	/**
	 * 用户模块
	 */
	userCmd: 1,
	/**
	 * 房间模块
	 */
	roomCmd: 2,
	// 大屏端
	screenUserCmd: 9
}

/**
 * 手机端登录
 */
export const MessageUserSubCmd = {
	//  登录
	login: 1,

	// 加载进入房间
	joinRoom: 8,
	// 获取服务器时间戳
	serverTime: 4,
	// 显示游戏记录
	userGameHistory: 7,
	// 奖品列表
	UserAwardHistory: 10
}

/**
 * 大屏端端登录
 */
export const MessageScreenUserSubCmd = {
	//  门店登录接口
	login: 1,

	// 登录加载门店房间信息
	joinRoom: 2
}

// 房间业务
export const MessageRoomSubCmd = {
	// 更新分数，建议每秒调用一次当前分数的增量
	updateScore: 10,
	// (房间状态变更推送(大小),小端只会推送GAME_READY,GAME_START,GAME_END这三种状态,大端推送所有状态)
	roomStatusPush: 6,
	// 玩家进入或退出推送（大）)
	roomUserPush: 7,
	//(显示房间信息推送(大))
	roomMainInfoPush: 4,
	// (显示房间信息推送（小）)
	roomUserMainPush: 11,
	// (游戏数据推送,排名分数等信息(大))
	roomGameRankPush: 8,
	// (房间结算推送(小),如果奖励字段为-1，表示无奖励)
	rewardPush: 9,
	// 活动规则
	rule: 12,
	// 房间配置
	roomConfig: 13,
	// 游戏排名
	rank: 14,
	kickout: 15
}
