[1, ["52fHu7D8hGm5vLaoALoXCl", "c4Ivt8SBZB25pLPpGg1u1P@f9941", "46IdxunHlHYYawWmS60uVs@f9941", "c0iAobRJ1HhJAWLc9syNlo@f9941"], ["node", "_font", "_spriteFrame", "root", "lbProp", "lbName", "sprWing", "btnClose", "sprFrame", "ndRoot", "data"], [["cc.Node", ["_name", "_layer", "_components", "_prefab", "_children", "_parent", "_lpos"], 1, 9, 4, 2, 1, 5], ["cc.Node", ["_name", "_layer", "_parent", "_components", "_prefab", "_lpos", "_lscale"], 1, 1, 12, 4, 5, 5], ["cc.Sprite", ["_type", "node", "__prefab", "_spriteFrame"], 2, 1, 4, 6], ["cc.UITransform", ["node", "__prefab", "_contentSize"], 3, 1, 4, 5], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_isSystemFontUsed", "_enableOutline", "_lineHeight", "node", "__prefab", "_color", "_outlineColor", "_font"], -3, 1, 4, 5, 5, 6], ["cc.Layout", ["_resizeMode", "_layoutType", "_affectedByScale", "node", "__prefab"], 0, 1, 4], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.<PERSON><PERSON>", ["_transition", "node", "__prefab", "_normalColor", "_target"], 2, 1, 4, 5, 1], ["cc.Widget", ["_alignFlags", "_left", "_top", "node", "__prefab"], 0, 1, 4], ["83dc3dn+apC5ppAXX4S4ewb", ["node", "__prefab", "ndRoot", "sprFrame", "btnClose", "sprWing", "lbName", "lbProp"], 3, 1, 4, 1, 1, 1, 1, 1, 1]], [[7, 0, 2], [8, 0, 1, 2, 3, 4, 5, 5], [3, 0, 1, 2, 1], [0, 0, 1, 5, 4, 2, 3, 6, 3], [0, 0, 1, 5, 2, 3, 6, 3], [1, 0, 1, 2, 3, 4, 5, 3], [1, 0, 1, 2, 3, 4, 5, 6, 3], [3, 0, 1, 1], [2, 1, 2, 1], [4, 0, 1, 2, 5, 3, 6, 7, 8, 6], [6, 0, 2], [0, 0, 1, 4, 2, 3, 3], [0, 0, 1, 5, 4, 2, 3, 3], [1, 0, 1, 2, 3, 4, 3], [2, 1, 2, 3, 1], [2, 0, 1, 2, 3, 2], [4, 0, 1, 2, 3, 4, 6, 7, 8, 9, 10, 6], [9, 0, 1, 2, 3, 4, 2], [10, 0, 1, 2, 3, 4, 4], [5, 0, 1, 3, 4, 3], [5, 0, 1, 2, 3, 4, 4], [11, 0, 1, 2, 3, 4, 5, 6, 7, 1]], [[10, "ViewWingShow"], [11, "ViewWingShow", 33554432, [-10], [[7, -2, [0, "c3dZ33MkBG8px6vkf3TxSl"]], [21, -9, [0, "88QTrxs9lIbboM5rBnqxeP"], -8, -7, -6, -5, -4, -3]], [1, "26JVWPKLdP5ZEWy9Rjrtjb", null, null, null, -1, 0]], [12, "ndRoot", 33554432, 1, [-12, -13, -14, -15, -16, -17, -18], [[7, -11, [0, "91PhMtBTNOQq+XY7G0r4fy"]]], [1, "b3LYP28CpIG42eHlYEX4Wu", null, null, null, 1, 0]], [5, "btnClose", 33554432, 2, [[[2, -19, [0, "0fVcVw9EJF9b6Jug11azbx"], [5, 112, 113]], [15, 1, -20, [0, "bfeoJzhaFLf7CCVCflK4eR"], 2], -21, [18, 9, 361.706, -310.18100000000004, -22, [0, "22h64u3vFAv4tqpC5y6ko2"]]], 4, 4, 1, 4], [1, "eff2a1QCVNLrsIruOGLVhd", null, null, null, 1, 0], [1, 367.706, 303.68100000000004, 0]], [3, "game_title_viewuserinfo", 33554432, 2, [-25], [[2, -23, [0, "55CUxlD6NAL4o7RxDp2ePe"], [5, 294, 77]], [14, -24, [0, "bfIfTUSWlPIYTNm84OuN1A"], 1]], [1, "b1wZ0Ku5RLo7tfac6shCTV", null, null, null, 1, 0], [1, 0, 302.73800000000006, 0]], [3, "Node", 33554432, 2, [-28], [[2, -26, [0, "d7uXjwttRG9aOQXyAVs66o"], [5, 190, 100]], [20, 1, 1, true, -27, [0, "b0LRNdxmNJXodei6acairH"]]], [1, "1bFi1SGi1K+KHuj15Whq//", null, null, null, 1, 0], [1, 0, -218.57100000000003, 0]], [13, "sprFrame", 33554432, 2, [[[2, -29, [0, "3dZDffMQZFRJrVZJI8wekR"], [5, 775, 599]], -30], 4, 1], [1, "d6wFyGNR9PH4g4HgKWRo7g", null, null, null, 1, 0]], [4, "Label", 33554432, 4, [[2, -31, [0, "36fn0VzzpE6Zzx/C4NtrQG"], [5, 184, 54.4]], [16, "排行榜奖励", 36, 36, false, true, -32, [0, "d54owi4thK1IGnXCOeQ7Fl"], [4, 4292933887], [4, 4278223283], 0]], [1, "edE6RKeDlFn6CBDVOJZ/eB", null, null, null, 1, 0], [1, 0, 4.5470000000000255, 0]], [4, "ndDimond", 33554432, 2, [[2, -33, [0, "f7l3JWJ0VNY4WvVPfcydYL"], [5, 100, 0]], [19, 1, 2, -34, [0, "c9qp9B9YJJIYMmpGdL36wr"]]], [1, "19/ZMo48RAo5Ev1hzNJEiy", null, null, null, 1, 0], [1, 0, -132.72299999999996, 0]], [6, "lbName", 33554432, 2, [[[2, -35, [0, "47AEjiS4BBI6/gzX50OPE3"], [5, 320, 100.8]], -36], 4, 1], [1, "9bokw3I5lOqpTyahSbDOyK", null, null, null, 1, 0], [1, 0, -163.49400000000003, 0], [1, 0.5, 0.5, 1]], [6, "lbProp", 33554432, 5, [[[2, -37, [0, "acozVGZBpO84w3eukPWvSR"], [5, 380, 100.8]], -38], 4, 1], [1, "7cLxJwneVJlbe4qzCd1UZ5", null, null, null, 1, 0], [1, 0, -11.331999999999994, 0], [1, 0.5, 0.5, 1]], [5, "sprWing", 33554432, 2, [[[2, -39, [0, "e52nnjdZJGSbINp3nlCvZ5"], [5, 40, 36]], -40], 4, 1], [1, "39kFauzHNCG6+BvysTdL9g", null, null, null, 1, 0], [1, 0, 62.35799999999995, 0]], [8, 6, [0, "7beNx3X8xOEpEb1/epO3aI"]], [17, 3, 3, [0, "678XAR0KdGXZBdyTW+Clrh"], [4, 4292269782], 3], [9, "翅膀名称", 80, 80, 80, false, 9, [0, "74Sc70vHxDY7McwDhM02Yc"], [4, 4279772212]], [9, "装备效果：", 80, 80, 80, false, 10, [0, "cf65TR831InJp0wV3UUrLq"], [4, 4279772212]], [8, 11, [0, "8fAsa2xGFFq7LkltWu2reQ"]]], 0, [0, 3, 1, 0, 0, 1, 0, 4, 15, 0, 5, 14, 0, 6, 16, 0, 7, 13, 0, 8, 12, 0, 9, 2, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, -1, 6, 0, -2, 4, 0, -3, 3, 0, -4, 8, 0, -5, 9, 0, -6, 5, 0, -7, 11, 0, 0, 3, 0, 0, 3, 0, -3, 13, 0, 0, 3, 0, 0, 4, 0, 0, 4, 0, -1, 7, 0, 0, 5, 0, 0, 5, 0, -1, 10, 0, 0, 6, 0, -2, 12, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, -2, 14, 0, 0, 10, 0, -2, 15, 0, 0, 11, 0, -2, 16, 0, 10, 1, 40], [0, 0, 0, 12, 14, 15], [1, 2, 2, 2, 1, 1], [0, 1, 2, 3, 0, 0]]