import { _decorator, Component, Label, Node, Tween, tween, v3, Vec3 } from 'cc';
import { EffectMgr } from '../../scripts/EffectMgr';
const { ccclass, property } = _decorator;

@ccclass('UnitSkillTips')
export class UnitSkillTips extends Component {
    @property(Label)
    private lbTips: Label = null;

    private _tempPos: Vec3 = new Vec3();
    private _tw: Tween

    setData(data) {
        this.lbTips.string = data.txt;

        this._tempPos.set(data.pos.x, data.pos.y + 200)

        this.node.setPosition(this._tempPos);
        this._tempPos.set(data.pos.x, data.pos.y + 300);
        if (!this._tw) {
            this._tw = new Tween(this.node)
                .to(0.2, { position: this._tempPos })
                .delay(0.8)
                .call(() => {
                    EffectMgr.getInstance().killSkillTips(this);
                })
        }
        this._tw.start();
    }
}


