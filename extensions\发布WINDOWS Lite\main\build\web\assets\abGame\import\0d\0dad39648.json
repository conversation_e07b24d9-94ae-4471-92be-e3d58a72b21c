[1, ["52fHu7D8hGm5vLaoALoXCl", "023hs6Z9hMN7n00LscdOzW@f9941", "e8YGnkLQpCN5bnM/tgb9x6@f9941", "a6x/jQxmxNq5PPqwyNw1y+@6c48a", "a6x/jQxmxNq5PPqwyNw1y+@f9941", "9bX03K6LJHe4W/p2H25qsI@f9941", "43uJP40mxBTZ8IgIuKY1Zb@f9941", "3eXnGr7TxPg65txioMwdYg"], ["node", "_font", "_spriteFrame", "_parent", "_textureSource", "root", "svRankContent", "lbGiftTitle", "lbTopTxtGiftScore", "lbTxtRankReward", "lbTxtRankLev", "lbTxtRankScore", "btnClose", "sprFrame", "ndRoot", "data", "itemPrefab"], [["cc.Node", ["_name", "_layer", "_active", "_components", "_prefab", "_parent", "_children", "_lpos"], 0, 9, 4, 1, 2, 5], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_isSystemFontUsed", "_enableOutline", "node", "__prefab", "_font", "_outlineColor", "_color"], -2, 1, 4, 6, 5, 5], ["cc.Node", ["_name", "_layer", "_parent", "_components", "_prefab", "_lpos", "_children"], 1, 1, 12, 4, 5, 2], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.Sprite", ["_sizeMode", "node", "__prefab", "_spriteFrame"], 2, 1, 4, 6], ["cc.Layout", ["_resizeMode", "_layoutType", "_spacingX", "_spacingY", "_constraintNum", "node", "__prefab"], -2, 1, 4], ["cc.<PERSON><PERSON>", ["_transition", "_zoomScale", "node", "__prefab", "_normalColor", "_target"], 1, 1, 4, 5, 1], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cb465fcQypH7qXYK7lbAXIf", ["node", "__prefab", "ndRoot", "sprFrame", "btnClose", "tags", "lbTxtRankScore", "lbTxtRankLev", "lbTxtRankReward", "lbTopTxtGiftScore", "lbGiftTitle", "svRankContent"], 3, 1, 4, 1, 1, 1, 2, 1, 1, 1, 1, 1, 1], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.Mask", ["node", "__prefab"], 3, 1, 4], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node", "__prefab"], 0, 1, 4], ["cc.Graphics", ["node", "__prefab", "_fillColor"], 3, 1, 4, 5], ["d2bd8Ybyq5JP6Lhg2nKR4ii", ["horizontal", "type", "node", "__prefab", "_content"], 1, 1, 4, 1]], [[9, 0, 2], [11, 0, 1, 2, 3, 4, 5, 5], [3, 0, 1, 2, 1], [0, 0, 1, 5, 3, 4, 7, 3], [2, 0, 1, 2, 6, 3, 4, 5, 3], [4, 0, 1, 2, 3, 2], [2, 0, 1, 2, 3, 4, 5, 3], [1, 0, 1, 2, 3, 5, 6, 9, 5], [4, 1, 2, 3, 1], [6, 0, 1, 2, 3, 4, 5, 3], [0, 0, 2, 1, 5, 3, 4, 4], [3, 0, 1, 1], [3, 0, 1, 2, 3, 1], [1, 0, 1, 2, 3, 5, 6, 9, 7, 5], [1, 0, 1, 2, 3, 5, 6, 9, 8, 7, 5], [0, 0, 1, 6, 3, 4, 3], [0, 0, 1, 5, 3, 4, 3], [8, 0, 2], [0, 0, 1, 6, 3, 4, 7, 3], [0, 0, 1, 5, 6, 3, 4, 3], [0, 0, 1, 5, 6, 3, 4, 7, 3], [2, 0, 1, 2, 3, 4, 3], [10, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 1], [5, 0, 1, 2, 5, 6, 4], [5, 0, 1, 2, 3, 4, 5, 6, 6], [4, 1, 2, 1], [12, 0, 1, 1], [13, 0, 1, 2, 3, 4, 4], [14, 0, 1, 2, 1], [1, 0, 1, 2, 3, 4, 5, 6, 8, 7, 6], [1, 0, 1, 2, 3, 5, 6, 8, 7, 5], [6, 0, 2, 3, 4, 5, 2], [15, 0, 1, 2, 3, 4, 3]], [[[{"name": "game_title_rank", "rect": {"x": 0, "y": 0, "width": 465, "height": 176}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 465, "height": 176}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-232.5, -88, 0, 232.5, -88, 0, -232.5, 88, 0, 232.5, 88, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 176, 465, 176, 0, 0, 465, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -232.5, "y": -88, "z": 0}, "maxPos": {"x": 232.5, "y": 88, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [7], 0, [0], [4], [3]], [[[17, "ViewRank"], [15, "ViewRank", 33554432, [-17], [[11, -2, [0, "37+e0A9qdKNZtAhANh4OPE"]], [22, -16, [0, "53epGW6SpA9rI5S7l+bCgV"], -15, -14, -13, [-9, -10, -11, -12], -8, -7, -6, -5, -4, -3]], [1, "20FEXY0U5JTJuqLAKf6Hq+", null, null, null, -1, 0]], [18, "Node-002", 33554432, [-19, -20, -21, -22, -23, -24, -25, -26], [[11, -18, [0, "c2L/u+tKBLjJtMTT+k4klJ"]]], [1, "39ORLsPVFCQrw2xVLlSkY6", null, null, null, 1, 0], [1, 0, 250.1110000000001, 0]], [19, "ndRoot", 33554432, 1, [-28, -29, -30, 2, -31, -32], [[11, -27, [0, "424uJG4/JLq6RHE1FI7jlt"]]], [1, "e3sQmBgG5Iboc1P/TCCT8H", null, null, null, 1, 0]], [20, "ndBtns", 33554432, 3, [-35, -36, -37, -38], [[2, -33, [0, "e8284quPhEMoakVcHLsrsY"], [5, 710, 100]], [23, 1, 1, 10, -34, [0, "f7h3t5qBxD8Jna44fLKCsq"]]], [1, "4eb0jO6F5F64XIBkLdtMCL", null, null, null, 1, 0], [1, 0, 496.193, 0]], [4, "btnScoreRank", 33554432, 4, [-42, -43], [[[2, -39, [0, "84ObIiw35Nh75bKlwIquDq"], [5, 170, 66]], [5, 0, -40, [0, "3fNCj+h9RN64s0bq3oOUP5"], 8], -41], 4, 4, 1], [1, "144kLJs9tFtaAhbBqEmD2H", null, null, null, 1, 0], [1, -270, 4.242999999999938, 0]], [4, "btnLevRank", 33554432, 4, [-47, -48], [[[2, -44, [0, "42IcVtqUBBJ7UQd/wswhlo"], [5, 170, 66]], [5, 0, -45, [0, "40JPYulzFL95eQ0qcKTjNc"], 11], -46], 4, 4, 1], [1, "feTZquvLhEQKjJx8z+Slur", null, null, null, 1, 0], [1, -90, 4.242999999999938, 0]], [4, "btnLiveRank", 33554432, 4, [-52, -53], [[[2, -49, [0, "776RLq2spPdoUcd4A4ck2t"], [5, 170, 66]], [5, 0, -50, [0, "2e9F6r3g1NuYYs7u3243cj"], 14], -51], 4, 4, 1], [1, "ddMFwwkfRPvZ2iKt5e1FJ6", null, null, null, 1, 0], [1, 90, 4.242999999999938, 0]], [4, "btnGiftRank", 33554432, 4, [-57, -58], [[[2, -54, [0, "bcbcFhmjVEYrXEl5aSIua0"], [5, 170, 66]], [5, 0, -55, [0, "39SNhzSBNEa4gJrruRB5iM"], 17], -56], 4, 4, 1], [1, "a0EnO9A6JI9I2lrbKh5LUe", null, null, null, 1, 0], [1, 270, 4.242999999999938, 0]], [4, "btnClose", 33554432, 3, [-62], [[[2, -59, [0, "1cc9Eox2BKF6VYziAooxfY"], [5, 294, 87]], [8, -60, [0, "448CTfnTpGBJbWqHmO7uBL"], 2], -61], 4, 4, 1], [1, "99W/4E/b9IY5/i0+xFK2HS", null, null, null, 1, 0], [1, 0, -738.823, 0]], [15, "view", 33554432, [-67], [[12, -63, [0, "b978bF3tBCVo97THVIFB3P"], [5, 750, 940], [0, 0.5, 1]], [26, -64, [0, "09xx9k1RZJlYBe7R+zuT4r"]], [27, 45, 240, 250, -65, [0, "adWv94z3JMI4K5mdSZBdL/"]], [28, -66, [0, "c1bvMzgVFAAqntcW7WEp64"], [4, 16777215]]], [1, "24Tfl5l2ZMxYDesGoH1yOM", null, null, null, 1, 0]], [4, "svV", 33554432, 3, [10], [[[12, -68, [0, "82vqnXnPhKXbyf4ohlf3Ml"], [5, 750, 940], [0, 0.5, 1]], -69], 4, 1], [1, "a7r05zTGNBYbyLQxny4hpK", null, null, null, 1, 0], [1, 0, 338.6400000000001, 0]], [16, "content", 33554432, 10, [[12, -70, [0, "ffaVTwYbJLtbOQHJOibTaz"], [5, 750, -20], [0, 0.5, 1]], [24, 1, 2, 50, 20, -1.8, -71, [0, "35Qg0Aj5xHhoZq7r60CRF7"]]], [1, "16X5tvn/tGM56zoO/1kbCI", null, null, null, 1, 0]], [21, "sprFrame", 33554432, 3, [[[2, -72, [0, "27A/SFmfZDL6Wgf/cUmjJ6"], [5, 769, 1345]], -73], 4, 1], [1, "4dWwrG4HFIRqfQ7C7T7nsZ", null, null, null, 1, 0]], [3, "game_title_01", 33554432, 3, [[2, -74, [0, "9fufRWI/lBL7qZ7ejunZr+"], [5, 465, 176]], [8, -75, [0, "6420/qEttM87QuRA8XJIAr"], 0]], [1, "a2EPHM12ZEwLzL/8aj/qoL", null, null, null, 1, 0], [1, 177.84, 699.246, 0]], [3, "Label", 33554432, 9, [[2, -76, [0, "95q0UZrl9Cb4QYQ828EfWb"], [5, 72, 54.4]], [29, "返回", 34, 34, false, true, -77, [0, "ac8u7Ki/RCrIT8LYTv+xjJ"], [4, 4280507528], 1]], [1, "31GgORb3RL9b8aRZE1U7oN", null, null, null, 1, 0], [1, 0, 4.331999999999965, 0]], [3, "Label-002", 33554432, 2, [[2, -78, [0, "8eUNg6zklIYrjkfFujywrf"], [5, 190.2419891357422, 50.4]], [13, " 每月1日0点更新", 26, 26, false, -79, [0, "1b6zJvycFKGrnznNwma6Wg"], [4, 4279772212], 3]], [1, "14bmcwPFxFVKsm9a8Kd82D", null, null, null, 1, 0], [1, -259.962, 328.66899999999987, 0]], [3, "Label", 33554432, 2, [[2, -80, [0, "10cckO2JhMxrT+RAVU5/0E"], [5, 78, 50.4]], [13, "月排名", 26, 26, false, -81, [0, "107RA3+cVDG5IXG30hhvz2"], [4, 4279772212], 4]], [1, "5doVVpQfFJlo7sio0kQlsz", null, null, null, 1, 0], [1, -321.964, 142, 0]], [3, "Label-001", 33554432, 2, [[2, -82, [0, "6arjjsOvJPyqtTkDMMFNKK"], [5, 52, 50.4]], [13, "昵称", 26, 26, false, -83, [0, "1dkEsIv4pFBr4LqWjWIRbO"], [4, 4279772212], 5]], [1, "3312m97GBNqJ/hUvenTeYg", null, null, null, 1, 0], [1, -90.44900000000001, 142, 0]], [6, "lbTopTxtScore", 33554432, 2, [[[2, -84, [0, "04uy0qaZ1MKZ6ScjXSm3b8"], [5, 104, 50.4]], -85], 4, 1], [1, "a1tUoyOUdIU5esCHfuaTTf", null, null, null, 1, 0], [1, 289.557, 142, 0]], [6, "lbTopTxtGiftScore", 33554432, 2, [[[2, -86, [0, "706h8b0lBKYb5doTDoXwK4"], [5, 104, 50.4]], -87], 4, 1], [1, "ddDe0D5CNDFbXOldmbJcn1", null, null, null, 1, 0], [1, 289.557, 142, 0]], [6, "lbTopTxtLev", 33554432, 2, [[[2, -88, [0, "65teyzKN1DbpPXmJ7skyYR"], [5, 104, 50.4]], -89], 4, 1], [1, "9fLygToFlJm4AOSMQpuvUP", null, null, null, 1, 0], [1, 289.557, 142, 0]], [6, "lbTopTxtRankReward", 33554432, 2, [[[2, -90, [0, "1d3FkwCW1KIJtaUTa5LqiM"], [5, 130, 50.4]], -91], 4, 1], [1, "c1Siqt4khLWKcj4Ab9k88S", null, null, null, 1, 0], [1, 290, 142, 0]], [6, "lbGiftTitle", 33554432, 2, [[[2, -92, [0, "1ft/uZhnlMepo/s0aNOJV5"], [5, 104, 50.4]], -93], 4, 1], [1, "66fYcQMCJFwISijRt8IAz4", null, null, null, 1, 0], [1, 111.63699999999994, 142, 0]], [16, "ndOn", 33554432, 5, [[2, -94, [0, "e4IbFarp9AwqdY3INQitb7"], [5, 170, 66]], [5, 0, -95, [0, "e7oT+jNNZExIyfzvur9ftD"], 6]], [1, "c7Cc8vuCRO2ZrXE5CaIXhO", null, null, null, 1, 0]], [3, "Label", 33554432, 5, [[2, -96, [0, "3fFCD7PtVJ9pskHLs1cHdq"], [5, 120, 50.4]], [30, "积分排行", 30, 30, false, -97, [0, "27SMC9oQ9KzZ5jITFVpovi"], [4, 4280507528], 7]], [1, "78ve9ApAZPybWrYWRCXqSj", null, null, null, 1, 0], [1, 0, 2.129000000000133, 0]], [10, "ndOn", false, 33554432, 6, [[2, -98, [0, "8aeHt7vcBKjqTipDBMQ8v4"], [5, 170, 66]], [8, -99, [0, "6aTuA6sUhOU75I0tJaKQyd"], 9]], [1, "12mXGhVANNRZcphkPAJKpB", null, null, null, 1, 0]], [3, "Label", 33554432, 6, [[2, -100, [0, "e9YZ1rfx5CyZyeKca2s5vf"], [5, 120, 50.4]], [14, "通关排名", 30, 30, false, -101, [0, "51/tyYqiJCArthnJBuUq9e"], [4, 4279772212], [4, 4280507528], 10]], [1, "21KZqv9ttBuruoNP87AhAp", null, null, null, 1, 0], [1, 0, 2.129000000000133, 0]], [10, "ndOn", false, 33554432, 7, [[2, -102, [0, "8cpKBBEmJDhaGPLv/eUShA"], [5, 170, 66]], [8, -103, [0, "d5y2D5QiVJ4r15h9TqAwGq"], 12]], [1, "1cnZ8Hgm9J95LZfwT/mwIs", null, null, null, 1, 0]], [3, "Label", 33554432, 7, [[2, -104, [0, "ffdAjVDa1JYZUhmTsjxRCS"], [5, 120, 50.4]], [14, "主播排名", 30, 30, false, -105, [0, "53LGr59NtJzqPGcGlVmQJ6"], [4, 4279772212], [4, 4280507528], 13]], [1, "89wMM9tndOhLovWqgUf+p0", null, null, null, 1, 0], [1, 0, 2.129000000000133, 0]], [10, "ndOn", false, 33554432, 8, [[2, -106, [0, "af5kff2TVCA7ZpyIf6Kipx"], [5, 170, 66]], [5, 0, -107, [0, "e7EJugyB9Huah2hyMz1Kh8"], 15]], [1, "46ghe/h99CMorE6DAbo7+5", null, null, null, 1, 0]], [3, "Label", 33554432, 8, [[2, -108, [0, "f5hCXUicFA8bYBbgjkFqxB"], [5, 120, 50.4]], [14, "等级排名", 30, 30, false, -109, [0, "41k3lZW69OP6Aaz2uuw1Oi"], [4, 4279772212], [4, 4280507528], 16]], [1, "68IWivVNhOS5gQAM7jHpot", null, null, null, 1, 0], [1, 0, 2.129000000000133, 0]], [25, 13, [0, "4abTUAryhDeaSi2LdamqyR"]], [31, 3, 9, [0, "83+kxKqvpH8Js/qEld+2Bh"], [4, 4292269782], 9], [7, "本月积分", 26, 26, false, 19, [0, "9aPMYTxIZNbILiC5s+6FxL"], [4, 4279772212]], [7, "礼物积分", 26, 26, false, 20, [0, "68fdCO7HRNQY6VlcyCQww0"], [4, 4279772212]], [7, "本月通关", 26, 26, false, 21, [0, "d0osHQX5xEFYc/xb3T46/F"], [4, 4279772212]], [7, "排行榜奖励", 26, 26, false, 22, [0, "83py+7SHRHFKDpCYvcOWRK"], [4, 4279772212]], [7, "等级称号", 26, 26, false, 23, [0, "c3CBztWeZOILdeS8QDyn+F"], [4, 4279772212]], [32, false, 1, 11, [0, "ab7SC0vOZCRZEHtalMzNhk"], 12], [9, 3, 1.05, 5, [0, "68t/Uuo7lG9bcD4+9wd5qk"], [4, 4292269782], 5], [9, 3, 1.05, 6, [0, "aaTWJ/SNFPOLizCcIIAw7g"], [4, 4292269782], 6], [9, 3, 1.05, 7, [0, "96rY5ylm5F2JxXJ2NUa2MM"], [4, 4292269782], 7], [9, 3, 1.05, 8, [0, "eb12FGAQxEr5ZfaSiKs80g"], [4, 4292269782], 8]], 0, [0, 5, 1, 0, 0, 1, 0, 6, 39, 0, 7, 38, 0, 8, 35, 0, 9, 37, 0, 10, 36, 0, 11, 34, 0, -1, 40, 0, -2, 41, 0, -3, 42, 0, -4, 43, 0, 12, 33, 0, 13, 32, 0, 14, 3, 0, 0, 1, 0, -1, 3, 0, 0, 2, 0, -1, 16, 0, -2, 17, 0, -3, 18, 0, -4, 19, 0, -5, 20, 0, -6, 21, 0, -7, 22, 0, -8, 23, 0, 0, 3, 0, -1, 13, 0, -2, 14, 0, -3, 9, 0, -5, 11, 0, -6, 4, 0, 0, 4, 0, 0, 4, 0, -1, 5, 0, -2, 6, 0, -3, 7, 0, -4, 8, 0, 0, 5, 0, 0, 5, 0, -3, 40, 0, -1, 24, 0, -2, 25, 0, 0, 6, 0, 0, 6, 0, -3, 41, 0, -1, 26, 0, -2, 27, 0, 0, 7, 0, 0, 7, 0, -3, 42, 0, -1, 28, 0, -2, 29, 0, 0, 8, 0, 0, 8, 0, -3, 43, 0, -1, 30, 0, -2, 31, 0, 0, 9, 0, 0, 9, 0, -3, 33, 0, -1, 15, 0, 0, 10, 0, 0, 10, 0, 0, 10, 0, 0, 10, 0, -1, 12, 0, 0, 11, 0, -2, 39, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, -2, 32, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, -2, 34, 0, 0, 20, 0, -2, 35, 0, 0, 21, 0, -2, 36, 0, 0, 22, 0, -2, 37, 0, 0, 23, 0, -2, 38, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, 0, 26, 0, 0, 27, 0, 0, 27, 0, 0, 28, 0, 0, 28, 0, 0, 29, 0, 0, 29, 0, 0, 30, 0, 0, 30, 0, 0, 31, 0, 0, 31, 0, 15, 1, 2, 3, 3, 10, 3, 11, 109], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 32, 34, 35, 36, 37, 38, 39], [2, 1, 2, 1, 1, 1, 2, 1, 2, 2, 1, 2, 2, 1, 2, 2, 1, 2, 2, 1, 1, 1, 1, 1, 16], [4, 0, 5, 0, 0, 0, 1, 0, 2, 1, 0, 2, 1, 0, 2, 1, 0, 2, 6, 0, 0, 0, 0, 0, 7]]]]