[1, ["20g1ukYUVPvKWKBRznAKo+@f9941", "52fHu7D8hGm5vLaoALoXCl", "4b1ocMsJlA45ARE0cj0KmA@6c48a", "4b1ocMsJlA45ARE0cj0KmA@f9941", "41Xr0ILK1KeZhrAveJ6ptp@f9941", "26hA86zmVLKYZ30Cd+lOot@f9941", "54TknWPwVPqJqeCR+Y/Czo@f9941", "95EkngnxZFbYuFpsqVTaFr@f9941", "3bvJBntRxNdLTqkOiS7Nx7@f9941"], ["node", "_spriteFrame", "_font", "_textureSource", "root", "lbDesc", "lbName", "spr<PERSON><PERSON><PERSON>", "anim", "btnClose", "sprFrame", "ndRoot", "data", "_normalSprite", "_hoverSprite", "_pressedSprite", "_disabledSprite"], [["cc.Sprite", ["_sizeMode", "_type", "node", "__prefab", "_spriteFrame"], 1, 1, 4, 6], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.Node", ["_name", "_layer", "_components", "_prefab", "_children", "_parent", "_lpos"], 1, 9, 4, 2, 1, 5], ["cc.Node", ["_name", "_layer", "_active", "_parent", "_components", "_prefab", "_lpos"], 0, 1, 12, 4, 5], ["cc.Label", ["_string", "_actualFontSize", "_isSystemFontUsed", "_fontSize", "_lineHeight", "_horizontalAlign", "_enableOutline", "_outlineWidth", "node", "__prefab", "_color", "_outlineColor"], -5, 1, 4, 5, 5], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["5e2d6pITJNNpZs80Kf/c2mt", ["animStyle", "node", "__prefab", "ndRoot", "sprFrame", "btnClose", "anim", "spr<PERSON><PERSON><PERSON>", "lbName", "lbDesc"], 2, 1, 4, 1, 1, 1, 1, 1, 1, 1], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.Mask", ["_type", "node", "__prefab"], 2, 1, 4], ["cc.Graphics", ["node", "__prefab", "_fillColor"], 3, 1, 4, 5], ["cc.Layout", ["_resizeMode", "_layoutType", "node", "__prefab"], 1, 1, 4], ["cc.<PERSON><PERSON>", ["_transition", "node", "__prefab", "_normalColor", "_target"], 2, 1, 4, 5, 1], ["sp.Skeleton", ["_preCacheMode", "node", "__prefab"], 2, 1, 4]], [[7, 0, 2], [9, 0, 1, 2, 3, 4, 5, 5], [1, 0, 1, 2, 1], [2, 0, 1, 5, 4, 2, 3, 6, 3], [1, 0, 1, 1], [2, 0, 1, 5, 2, 3, 6, 3], [3, 0, 1, 3, 4, 5, 6, 3], [3, 0, 1, 3, 4, 5, 3], [1, 0, 1, 2, 3, 1], [0, 2, 3, 4, 1], [6, 0, 2], [2, 0, 1, 4, 2, 3, 3], [3, 0, 2, 1, 3, 4, 5, 6, 4], [1, 0, 1, 3, 1], [8, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 2], [0, 1, 0, 2, 3, 4, 3], [0, 0, 2, 3, 4, 2], [0, 2, 3, 1], [0, 0, 2, 3, 2], [10, 0, 1, 2, 2], [11, 0, 1, 2, 1], [12, 0, 1, 2, 3, 3], [13, 0, 1, 2, 3, 4, 2], [14, 0, 1, 2, 2], [4, 0, 1, 3, 4, 2, 8, 9, 10, 6], [4, 0, 5, 1, 2, 6, 7, 8, 9, 10, 11, 7]], [[[{"name": "game_giftmessage_frame01", "rect": {"x": 0, "y": 0, "width": 674, "height": 178}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 674, "height": 178}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-337, -89, 0, 337, -89, 0, -337, 89, 0, 337, 89, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 178, 674, 178, 0, 0, 674, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -337, "y": -89, "z": 0}, "maxPos": {"x": 337, "y": 89, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [5], 0, [0], [3], [2]], [[[10, "ViewSkillUpShow"], [11, "ViewSkillUpShow", 33554432, [-11], [[4, -2, [0, "0fbtueGT1HYIL5+ZLHZNXN"]], [14, 0, -10, [0, "e59hhX+ttIK4Z+TDUy2NR7"], -9, -8, -7, -6, -5, -4, -3]], [1, "98yBVO1z5MubWqNYFWLGRt", null, null, null, -1, 0]], [3, "ndRoot", 33554432, 1, [-13, -14, -15, -16], [[4, -12, [0, "bcYQjgElhJQbAxBZz/vj7M"]]], [1, "c4ZxEvzXtAtKanWCiLmDmP", null, null, null, 1, 0], [1, 0, 511.4119999999999, 0]], [12, "btnClose", false, 33554432, 2, [[[2, -17, [0, "03mhwOLXtAtKArkDauc9Sz"], [5, 60, 60]], [15, 1, 0, -18, [0, "d8VdGOEktKYZOudm0S8gWc"], 0], -19], 4, 4, 1], [1, "772uawL2FNX5wF2n2xJuvY", null, null, null, 1, 0], [1, 351.401, -580.639, 0]], [3, "Node", 33554432, 2, [-21, -22, -23], [[4, -20, [0, "00mH2R6K5DYI4Pl4tQaCgw"]]], [1, "9bN6QUoeNNI6tBE3F6CgrY", null, null, null, 1, 0], [1, 0, -996.1469999999998, 0]], [3, "ndAvatar", 33554432, 4, [-25, -26, -27], [[4, -24, [0, "83X7xSkEFPgJuJMIBhmXJ/"]]], [1, "6fGFRsFapKJI5TDobL5P8d", null, null, null, 1, 0], [1, 0, 84.55200000000002, 0]], [3, "Node", 33554432, 5, [-31], [[4, -28, [0, "9ayiqDaftI55kEwDjHngln"]], [19, 1, -29, [0, "7fOn3cm4ZFEp6KHdpR8dRE"]], [20, -30, [0, "3fmyX5uSNPCaoyw7cYpaFr"], [4, 16777215]]], [1, "4e3+ctm0RGsKP1Z9bn/RR6", null, null, null, 1, 0], [1, -259.885, 2.2737367544323206e-13, 0]], [3, "ndDetail", 33554432, 4, [-34, -35], [[2, -32, [0, "c4uP0RTZRPHr0b2l9sjdBi"], [5, 100, 96.19999999999999]], [21, 1, 2, -33, [0, "38QcFzEB9I1LnTci6I4cHF"]]], [1, "0foPcCP/NJ8rVYmRw+shv9", null, null, null, 1, 0], [1, -168.85199999999998, 76.55200000000002, 0]], [7, "sprFrame", 33554432, 2, [[[2, -36, [0, "a3iPdNPgNOtbplYabBLuv6"], [5, 806, 319]], -37], 4, 1], [1, "a1XpVJb35J4IbVrLqReHiA", null, null, null, 1, 0]], [6, "anim", 33554432, 2, [[[13, -38, [0, "1dWIVYpy9K2K7iQ4Dp6cLe"], [0, 0.5, 0]], -39], 4, 1], [1, "42YDzX1y1EY6jsq2Qe5jYE", null, null, null, 1, 0], [1, 0, -541.3619999999999, 0]], [5, "sprFrame01", 33554432, 4, [[2, -40, [0, "d6YrlaKdpAkbX9WIeKeDH3"], [5, 674, 178]], [9, -41, [0, "7bjsCIF1pAMIqeuaFoU+CT"], 1]], [1, "c9NDce0xtPspDkybT1Rx4M", null, null, null, 1, 0], [1, 0, 91.84500000000003, 0]], [5, "sprAvatarBg", 33554432, 5, [[2, -42, [0, "e6OP5Cug5NLobAZvVgPWz4"], [5, 113, 113]], [9, -43, [0, "08enNTf3BIvZJR6BIJQE/p"], 2]], [1, "5b1vdUUhhAUZyEI1Du9iv/", null, null, null, 1, 0], [1, -257.885, 0, 0]], [7, "spr<PERSON><PERSON><PERSON>", 33554432, 6, [[[2, -44, [0, "d93CRZOuVOM70NdGigpt54"], [5, 104, 104]], -45], 4, 1], [1, "49dhw8UXtGjYip+KvT5xiA", null, null, null, 1, 0]], [5, "sprAvatarMask", 33554432, 5, [[2, -46, [0, "c1aI2UWZ9JzI4eNDI+DzKR"], [5, 104, 104]], [16, 0, -47, [0, "30LGStBSVKfaOTwkkTinyg"], 3]], [1, "966MZGx85LEIWks9222Qdk", null, null, null, 1, 0], [1, -257.885, 0, 0]], [6, "lbName", 33554432, 7, [[[8, -48, [0, "ccJyVfjcROD5GV4AzB7f5H"], [5, 154, 37.8], [0, 0, 0.5]], -49], 4, 1], [1, "cehDsmyAtCpI0PaCfKUqXo", null, null, null, 1, 0], [1, 0, 29.199999999999996, 0]], [6, "lbDesc", 33554432, 7, [[[8, -50, [0, "61LVor27JDyKgMjGfW1Spc"], [5, 238.63998413085938, 58.4], [0, 0, 0.5]], -51], 4, 1], [1, "56v8nVm7JJFYKJbvUNGAGB", null, null, null, 1, 0], [1, 0, -18.900000000000002, 0]], [17, 8, [0, "43e+b6FUVMxJtucYrksPka"]], [22, 2, 3, [0, "9dPi4dTnFEOaQ+dfsWqm3g"], [4, 4292269782], 3], [23, 0, 9, [0, "70bURkV+5Ky78bVGH+kb/r"]], [18, 0, 12, [0, "86e2ggJQlIwKU+INk9DR4p"]], [24, "玩家一般七个字", 22, 22, 30, false, 14, [0, "416HQwbcZLZLyaKSTyekYk"], [4, 4294965492]], [25, "技能（Lv.1）", 0, 40, false, true, 4, 15, [0, "dc54Telc5NiKCSMPpbGoGP"], [4, 4282967039], [4, 4280098273]]], 0, [0, 4, 1, 0, 0, 1, 0, 5, 21, 0, 6, 20, 0, 7, 19, 0, 8, 18, 0, 9, 17, 0, 10, 16, 0, 11, 2, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, -1, 8, 0, -2, 3, 0, -3, 9, 0, -4, 4, 0, 0, 3, 0, 0, 3, 0, -3, 17, 0, 0, 4, 0, -1, 10, 0, -2, 5, 0, -3, 7, 0, 0, 5, 0, -1, 11, 0, -2, 6, 0, -3, 13, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, -1, 12, 0, 0, 7, 0, 0, 7, 0, -1, 14, 0, -2, 15, 0, 0, 8, 0, -2, 16, 0, 0, 9, 0, -2, 18, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, -2, 19, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, -2, 20, 0, 0, 15, 0, -2, 21, 0, 12, 1, 51], [0, 0, 0, 0, 17, 17, 17, 17, 19, 20, 21], [1, 1, 1, 1, 13, 14, 15, 16, 1, 2, 2], [0, 3, 4, 5, 0, 0, 6, 7, 8, 1, 1]]]]