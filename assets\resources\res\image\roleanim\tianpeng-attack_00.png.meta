{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "d0b472a9-e53e-4eec-afd9-217fc14e9e52", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "d0b472a9-e53e-4eec-afd9-217fc14e9e52@6c48a", "displayName": "tianpeng-attack_00", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "d0b472a9-e53e-4eec-afd9-217fc14e9e52", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "d0b472a9-e53e-4eec-afd9-217fc14e9e52@f9941", "displayName": "tianpeng-attack_00", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": -16.5, "offsetY": -13, "trimX": 42, "trimY": 43, "width": 133, "height": 140, "rawWidth": 250, "rawHeight": 200, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-66.5, -70, 0, 66.5, -70, 0, -66.5, 70, 0, 66.5, 70, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [42, 157, 175, 157, 42, 17, 175, 17], "nuv": [0.168, 0.085, 0.7, 0.085, 0.168, 0.785, 0.7, 0.785], "minPos": [-66.5, -70, 0], "maxPos": [66.5, 70, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "d0b472a9-e53e-4eec-afd9-217fc14e9e52@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "d0b472a9-e53e-4eec-afd9-217fc14e9e52@6c48a"}}