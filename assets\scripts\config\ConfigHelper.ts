
import { C_GreatSkill, E_BuffType, E_MonsterType, E_SkillType } from "../../scripts/ConstGlobal";
import { Singleton } from "../../scripts/libs/utils/Singleton";
import { xcore } from "../../scripts/libs/xcore";
import TimeUtil from "../../scripts/libs/utils/TimeUtil";

export class ConfigHelper extends Singleton {


    /**
     * 获取妖族配置信息
     */
    getMonsterConfigByType(type: string, lev: number = 1) {
        let config = xcore.config[`monster.json`].find(e => e.monsterType == type && e.monsterLevel == lev);
        return config;
    }
    getMonsterConfigByJsonId(jsonId: string) {
        let config = xcore.config[`monster.json`].find(e => e.jsonId == jsonId);
        return config;
    }

    getHeroConfigByJsonId(jsonId: string) {
        let config = xcore.config[`skin.json`].find(e => e.jsonId == jsonId);
        return config;
    }
    /**塔防炮台配置 */
    getTowerPointByJsonId(jsonId: string) {

        let config = xcore.config[`fort.json`].find(e => e.jsonId == jsonId);
        return config;
    }
    /**塔防炮台配置 */
    getTowerPointByType(type: string, lev: number = 1) {

        let config = xcore.config[`fort.json`].find(e => {
            let levRag = e.monsterLevel.split('|');

            return e.monsterType == type && lev >= levRag[0] && lev <= levRag[1]
        });
        return config;
    }
    getTowerPointMaxLev() {
        let lev = 0;
        for (let i = 0; i < xcore.config[`fort.json`].length; i++) {
            let towerpoint = xcore.config[`fort.json`][i]
            let levRag = parseInt(towerpoint.monsterLevel.split('|')[1]);

            if (lev >= levRag) {
                return lev
            }
            lev = levRag
        }
        return lev
    }
    getSkinConfgs() {
        let configs = xcore.config[`skin.json`]
        if (!configs) {
            configs = []
        }
        return configs
    }
    getSkinConfigByJsonId(jsonId: string) {
        let config = xcore.config[`skin.json`].find(e => e.jsonId == jsonId);
        return config;
    }
    getSkinConfigByDebrisId(debrisId: string) {
        let config = xcore.config[`skin.json`].find(e => e.skinFragment == debrisId);
        return config;
    }
    getSkinJsonIdByContent(content: string) {
        let config = xcore.config[`skin.json`].find(e => e.barrage == content);
        if (config) {
            return config.jsonId;
        }
        return null;
    }

    getSkinLevelConfigBySkinId(skinId: string, lev: number = 1) {
        let config = xcore.config[`skinLevel.json`].find(e => e.skinId == skinId && e.skin == lev);
        return config;
    }
    getSkinMaxLevelBySkinId(skinId: string) {
        let configs = xcore.config[`skinLevel.json`].filter(e => e.skinId == skinId);
        console.log("getSkinMaxLevelBySkinId", skinId)
        let lev = 0;
        for (let i = 0; i < configs.length; i++) {
            let config = configs[i]
            let levRag = config.skin;
            if (lev >= levRag) {
                return lev
            }
            lev = levRag
        }
        return lev
    }
    getLotteryDebrisConfigsByJsonId(jsonId: string) {
        let configs = xcore.config[`lottery.json`].filter(e => e.lotteryId == jsonId);
        return configs
    }
    getLotteryDebrisConfigByJsonId(jsonId: string, targetId?: string) {
        let config
        if (!targetId) {
            config = xcore.config[`lottery.json`].find(e => e.skinFragmentId == jsonId);
        } else {
            config = xcore.config[`lottery.json`].find(e => e.skinFragmentId == jsonId && e.lotteryId == targetId);
        }

        return config
    }
    getLotteryConfigByJsonId(jsonId: string) {
        let config = xcore.config[`lotteryId.json`].find(e => e.jsonId == jsonId);
        return config;
    }
    /**获取法宝配置信息 */
    getWeaponConfigByType(type: E_SkillType, lev: number = 1) {
        let config = xcore.config[`weapon.json`].find(e => e.weaponType == type && e.weaponLevel == lev);
        return config;
    }
    getWeaponConfigBySkillJsonId(jsonId: string) {
        let config = xcore.config[`weapon.json`].find(e => e.skilId == jsonId);
        return config;
    }
    /**
     * 获取技能配置信息（所有法宝和部分怪物有技能）
     */
    getSkillConfigByType(type: E_SkillType, lev: number = 1) {
        let config = xcore.config[`skill.json`].find(e => e.type == type && e.skillLevel == lev);
        return config;
    }
    getSkillConfigByJsonId(jsonId: string) {
        let config = xcore.config[`skill.json`].find(e => e.jsonId == jsonId);
        return config;
    }
    /**
         * 获取技能配置信息（所有法宝和部分怪物有技能）
         */
    getSkillConfigsByType(type: E_SkillType) {
        let configs = xcore.config[`skill.json`].filter(e => e.type == type);
        return configs;
    }

    getGreateSkillAbleLevelByNum(greatSkillType: E_SkillType, totalGiftNum: number) {
        if (!greatSkillType || !totalGiftNum) {
            return 0
        }
        let bigNum = 0;
        let skillTotalLev = this.getSkillConfigsByType(greatSkillType).length;
        if (!skillTotalLev || skillTotalLev.length <= 0) return 0
        for (let i = 0; i < skillTotalLev; i++) {
            let skillconfig = this.getSkillConfigByType(greatSkillType, i + 1);
            let config = this.getWeaponConfigBySkillJsonId(skillconfig.jsonId);

            if (config && totalGiftNum >= config.douyinGiftNum) {
                bigNum += 1;
            }
        }
        return bigNum;
    }
    getGreateSkillAbleLevelNeedGiftNum(greatSkillType: E_SkillType, totalGiftNum: number) {
        let skillTotalLev = this.getSkillConfigsByType(greatSkillType).length;
        let minNum, maxNum, skillConfig;
        for (let i = 0; i < skillTotalLev; i++) {

            skillConfig = this.getSkillConfigByType(greatSkillType, i + 1);
            let config = this.getWeaponConfigBySkillJsonId(skillConfig.jsonId);
            maxNum = config.douyinGiftNum
            if (i == 0) {
                minNum = config.douyinGiftNum;
            }
            if (totalGiftNum < maxNum) {
                return [maxNum, skillConfig, minNum];
            }
        }
        return [maxNum, skillConfig];
    }

    /**获取buff配置信息 */
    getBuffConfigByType(type: E_BuffType, lev: number = 1) {
        let config = xcore.config[`state.json`].find(e => e.type == type && e.stateLevel == lev);
        return config;
    }

    /**获取buff配置信息 */
    getBuffConfigByJsonId(jsonId: string) {
        let config = xcore.config[`state.json`].find(e => e.jsonId == jsonId);
        return config;
    }

    /**礼物配置 */
    getGiftConfigs() {
        let config = xcore.config[`gift.json`];
        return config;
    }
    /**礼物配置 */
    getGiftConfigByJsonId(jsonId: string) {
        let config = xcore.config[`gift.json`].find(e => e.jsonId == jsonId);
        return config;
    }
    getGiftJsonIdByGiftId(giftId: string) {
        let config = xcore.config[`gift.json`].find(e => e.douyinGiftId == giftId);
        if (config) {
            return config.jsonId;
        }
        return null;
    }
    getGiftTypeByJsonId(jsonId: string) {
        let config = xcore.config[`gift.json`].find(e => e.jsonId == jsonId);
        if (config && config.douyinGiftId) {
            return config.giftType;
        }
        return null
    }
    getGiftJsonIdByType(type: string) {
        let config = xcore.config[`gift.json`].find(e => e.giftType == type);
        if (config) {
            return config.jsonId;
        }
        return null
    }
    /**常量表配置 */
    getConstantConfigByKey(key: string) {
        let config = xcore.config[`constant.json`][key];
        return config;
    }
    /**难度配置 */
    getDifficultyConfigByJsonId(jsonId: string) {
        let config = xcore.config[`selectDifficulty.json`].find(e => e.jsonId == jsonId);
        return config;
    }
    getLevelConfigs() {
        let config = xcore.config[`gameLevel.json`];
        return config;
    }
    /**关卡配置 */
    getLevelConfigByLevel(lev: number) {
        let config = this.getLevelConfigs().find(e => e.gameLevel == lev);

        return config
    }
    getLevelConfigByJsonId(jsonId: string) {
        let config = this.getLevelConfigs().find(e => e.jsonId == jsonId);

        return config
    }
    /**怪物轮次配置表 */
    getMonsterRefreshConfigByLevelJsonId(jsonId: string) {
        let config = xcore.config[`monsterRefresh.json`].filter(e => e.gameLevel >= jsonId);

        return config
    }
    /**怪物轮次配置表 */
    getDungeonRefreshConfigByLevelJsonId(jsonId: string) {
        let config = xcore.config[`dungeonRefresh.json`].filter(e => e.dungeonId == jsonId);

        return config
    }

    getBossConfigStartBy(refresh: number) {
        let configs = xcore.config[`monsterRefresh.json`];

        for (let i = 0; i < configs.length; i++) {
            let config = configs[i];


            if (refresh < config.refresh && config.type != 1) {
                return config;
            }
        }
        return null
    }
    /**获取动画配置 */
    getAnimConfigByJsonId(jsonId: string) {
        let config = xcore.config[`animation.json`].find(e => e.jsonId == jsonId);
        return config;
    }
    /**攻击特效配置 */
    getEffectConfigByJsonId(jsonId: string) {
        let config = xcore.config[`attacKeffect.json`].find(e => e.jsonId == jsonId);
        return config;
    }
    getLinyunKeyByForever() {
        let key = "rank:" + xcore.gameData.appId + ":" + xcore.gameData.channelId + ":gold:forever";
        return key
    }
    getGiftRankKeyByDay() {
        let time = TimeUtil.getLastDayByIndex(TimeUtil.formatTimestampToDate(TimeUtil.getServerTime(), '-'), 6);
        let key = "rank:" + xcore.gameData.appId + ":" + xcore.gameData.channelId + ":gift:price:day:" + time;
        return key
    }
    getGiftRankKeyByWeek() {
        let time = TimeUtil.getLastDayByIndex(TimeUtil.formatTimestampToDate(TimeUtil.getServerTime(), '-'), 6);
        let key = "rank:" + xcore.gameData.appId + ":" + xcore.gameData.channelId + ":gift:price:week:" + time;
        return key
    }
    getGiftRankKeyByMonth() {
        let time = TimeUtil.getLastDayByIndex(TimeUtil.formatTimestampToDate(TimeUtil.getServerTime(), '-'), 6);
        let key = "rank:" + xcore.gameData.appId + ":" + xcore.gameData.channelId + ":gift:price:month:" + time;
        return key
    }

    getGiftRankKeyByForever() {
        let key = "rank:" + xcore.gameData.appId + ":" + xcore.gameData.channelId + ":gift:price:forever";
        return key
    }


    getRankKeyByWeek() {
        let time = TimeUtil.getLastDayByIndex(TimeUtil.formatTimestampToDate(TimeUtil.getServerTime(), '-'), 6);
        let key = "rank:" + xcore.gameData.appId + ":" + xcore.gameData.channelId + ":week:" + time;
        return key

    }
    getRankKeyByMonth() {

        let time = TimeUtil.getFirstDayOfTheMonth()
        let key = "rank:" + xcore.gameData.appId + ":" + xcore.gameData.channelId + ":month:" + time;
        return key
    }
    getRankKeyByLev() {

        let time = TimeUtil.getFirstDayOfTheMonth()

        let key = "rank:" + xcore.gameData.appId + ":" + xcore.gameData.channelId + ":level:month:" + time;
        return key
    }
    getLiverKeyByLev() {

        let time = TimeUtil.getFirstDayOfTheMonth()

        // let time = TimeUtil.getLastDayByIndex(TimeUtil.formatTimestampToDate(TimeUtil.getServerTime(), '-'), 6);
        let key = "rank:" + xcore.gameData.appId + ":" + xcore.gameData.channelId + ":anchor:level:month:" + time;
        return key
    }
    /**获取排行榜入场配置 */
    getBossJoinConfig(rank: number) {

        if (rank <= 0) {
            return null;
        }
        console.log('getBossJoinConfig', rank)
        for (let i = 0; i < xcore.config[`bossAnimation.json`].length; i++) {
            let config = xcore.config[`bossAnimation.json`][i];
            let ranks = config.sample.split('|');
            if (rank >= ranks[0] && rank <= ranks[1]) {
                return config;
            }
        }
        return null;
    }

    getDebriConfigByJsonId(jsonId: string) {
        let config = xcore.config[`skinFragment.json`].find(e => e.jsonId == jsonId);
        return config;
    }

    getDoorConfigByLev(lev: number) {
        let configs = xcore.config[`door.json`];
        for (let i = 0; i < configs.length; i++) {
            let config = configs[i];
            let levs = config.doorLevel.split('|');
            if (lev >= levs[0] && lev <= levs[1]) {
                return config;
            }
        }
        return null;
    }
    getBeadConfigs() {
        let configs = xcore.config[`bead.json`];
        return configs;
    }
    getBeadConfigById(jsonId: string) {
        let config = xcore.config[`bead.json`].find(e => e.jsonId == jsonId);
        return config;
    }
    getBeadConfigByNum(jsonId: string, num: number) {

        let config = xcore.config[`beadLevel.json`].find(e => {
            let levs = `${e.beadNum}`.split('|');
            let lev1 = Number(levs[0]);
            let lev2 = Number(levs[1] || levs[0]);

            return e.beadId == jsonId && num >= lev1 && num <= lev2;
        });
        return config;
    }
    getBeadConfigByLev(jsonId: string, lev: number) {
        let config = xcore.config[`beadLevel.json`].find(e => e.beadId == jsonId && e.beadLevel == lev);
        return config;
    }

    getRankUpScore(type: number, rank: number) {
        let config = xcore.config[`scorePool.json`].find(e => e.poolType == type && e.ranking == rank);
        if (!config) return 0
        return Number(config.proportion);
    }
    getAbleSelectLev(lev: number) {
        let config = this.getConstantConfigByKey('gameLevelCoordinate');
        if (!config) return true;
        let list = config.split('|');
        return !!list.find(e => e == lev);
    }
    getRankRewardByRankIndex(rank: number) {
        let config = xcore.config[`rankReward.json`].find(e => e.rankMax >= rank && e.rankMin <= rank);
        return config;
    }

    getWingConfigByJsonId(jsonId: string) {
        let config = xcore.config[`wing.json`].find(e => e.jsonId == jsonId);
        return config;
    }
    getWingConfigByRankIndex(rank: number) {
        let rankConfig = this.getRankRewardByRankIndex(rank);
        if (rankConfig && rankConfig.rewardType == 1 && rankConfig.rewardId) {
            let wingconfig = this.getWingConfigByJsonId(rankConfig.rewardId);
            return wingconfig;
        }

        return null
    }
    getWingSkinConfigByJsonId(jsonId: string) {
        let config = xcore.config[`wingSkill.json`].find(e => e.jsonId == jsonId);
        return config;
    }

    getWingSkinConfigByRankIndex(rank: number) {
        let rankConfig = this.getRankRewardByRankIndex(rank);
        if (rankConfig && rankConfig.rewardType == 1 && rankConfig.rewardId) {
            let wingconfig = this.getWingConfigByJsonId(rankConfig.rewardId);
            if (wingconfig && wingconfig.wingSkillId) {
                let config = this.getWingSkinConfigByJsonId(wingconfig.wingSkillId);
                return config;
            }

        }
        return null;
    }

    getTaskConfigByType(type: number) {
        let config = xcore.config[`task.json`].find(e => e.type == type);
        return config;
    }

    getGiftRankTitleByScore(score: number) {

        let config = xcore.config[`giftLevel.json`].find(e => e.giftScoreMin <= score && e.giftScoreMax >= score);
        return config;
    }
    getDungeonConfigs() {
        let config = xcore.config[`dungeon.json`]
        return config;

    }
    getDungeonConfigByJsonId(jsonId: string) {
        let config = xcore.config[`dungeon.json`].find(e => e.jsonId == jsonId);
        return config;
    }
    getDungeonExchangeConfigByJsonId(jsonId: string) {
        let config = xcore.config[`dungeonExchange.json`].find(e => e.dungeonId == jsonId);
        return config;
    }
    getExchaengConfigByNameContent(content: string) {
        let config = xcore.config[`exchange.json`].find(e => e.name == content);
        return config;
    }
    getExchangeConfigs() {
        let configs = xcore.config[`exchange.json`]
        return configs;
    }
    getExchangeTypeConfigs() {
        let configs = xcore.config[`exchangeType.json`]
        return configs;

    }
    //兑换配置信息
    // 兑换规则说明：
    // 1.玩家消耗 [灵韵] 可兑换游戏道具
    // 2.兑换通过弹幕触发，兑换+商品名字
    // 3.兑换成功立即装备兑换的道具
    getExchangeConfigsByJsonId(jsonId: string) {
        let configs = xcore.config[`exchange.json`].filter(e => e.exchangeTypeId == jsonId)
        return configs;
    }
}