[1, ["52fHu7D8hGm5vLaoALoXCl", "20g1ukYUVPvKWKBRznAKo+@f9941", "84P+DO0TRGwahBpHX9cuna@6c48a", "84P+DO0TRGwahBpHX9cuna@f9941", "9bX03K6LJHe4W/p2H25qsI@f9941", "f3Q5Ex7DpN+5tlKAq1TJmt@f9941", "362kKZ4/dAjYk6TLeCigUz", "6aG0LPbIBL65Clek6Bmaup@f9941", "54TknWPwVPqJqeCR+Y/Czo@f9941", "95EkngnxZFbYuFpsqVTaFr@f9941", "b8+SOpP4hKVa+o3D4z1aL2", "ff3reVIcZLdo1ocsK9Lga3@f9941", "9bBYHzhH5F25cHFMhUL3oq@f9941", "5dOtsKrqlK65WzLYlDlVcf@f9941", "e6thSVya5LJ788DxHC8Z45@f9941", "2c8hCyiEhL3KBAMa5+iwT7@f9941"], ["node", "_font", "_spriteFrame", "root", "data", "_textureSource", "svRankContent", "ndTopContent", "lbTime", "btNextRound", "btnWorldRank", "btnClose", "sprFrame", "ndRoot", "_parent", "pfbUnitTopRank", "_normalSprite", "_hoverSprite", "_pressedSprite", "_disabledSprite", "itemPrefab", "lbRankChange", "lbRoundChange", "lbRoundNum", "lbScoreDesc", "lbScore", "lbName", "lbRankNum", "ndIconUp", "sprRankNum", "spr<PERSON><PERSON><PERSON>"], [["cc.Node", ["_name", "_layer", "_active", "_components", "_prefab", "_parent", "_children", "_lpos"], 0, 9, 4, 1, 2, 5], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_isSystemFontUsed", "_enableOutline", "_lineHeight", "node", "__prefab", "_color", "_outlineColor", "_font"], -3, 1, 4, 5, 5, 6], ["cc.Node", ["_name", "_layer", "_active", "_parent", "_components", "_prefab", "_lpos", "_children"], 0, 1, 12, 4, 5, 2], ["cc.Sprite", ["_type", "_sizeMode", "node", "__prefab", "_spriteFrame", "_color"], 1, 1, 4, 6, 5], ["cc.Layout", ["_layoutType", "_resizeMode", "_spacingY", "_spacingX", "_constraintNum", "node", "__prefab"], -2, 1, 4], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.Mask", ["_type", "node", "__prefab"], 2, 1, 4], ["cc.<PERSON><PERSON>", ["_transition", "node", "__prefab", "_normalColor", "_target"], 2, 1, 4, 5, 1], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["d34f8bHnjBAjqK8v7VM7khS", ["node", "__prefab", "ndRoot", "sprFrame", "btnClose", "btnWorldRank", "btNextRound", "lbTime", "ndTopContent", "svRankContent", "pfbUnitTopRank"], 3, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 6], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node", "__prefab"], 0, 1, 4], ["cc.Graphics", ["node", "__prefab", "_fillColor"], 3, 1, 4, 5], ["d2bd8Ybyq5JP6Lhg2nKR4ii", ["horizontal", "type", "node", "__prefab", "_content"], 1, 1, 4, 1], ["e3369BgzmxFw72HdBQ7L2F3", ["node", "__prefab", "spr<PERSON><PERSON><PERSON>", "sprRankNum", "ndIconUp", "lbRankNum", "lbName", "lbScore", "lbScoreDesc", "lbRoundNum", "lbRoundChange", "lbRankChange"], 3, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]], [[10, 0, 2], [12, 0, 1, 2, 3, 4, 5, 5], [5, 0, 1, 2, 1], [0, 0, 1, 5, 3, 4, 7, 3], [2, 0, 1, 3, 4, 5, 6, 3], [3, 2, 3, 4, 1], [1, 0, 1, 2, 3, 6, 7, 8, 10, 5], [5, 0, 1, 2, 3, 1], [5, 0, 1, 1], [1, 0, 1, 2, 3, 6, 7, 8, 5], [0, 0, 1, 5, 6, 3, 4, 7, 3], [2, 0, 1, 3, 7, 4, 5, 6, 3], [3, 2, 3, 1], [14, 0, 1, 2, 1], [9, 0, 2], [0, 0, 1, 6, 3, 4, 3], [0, 0, 1, 5, 6, 3, 4, 3], [0, 0, 1, 5, 3, 4, 3], [0, 0, 2, 1, 5, 3, 4, 7, 4], [2, 0, 1, 3, 4, 5, 3], [6, 1, 2, 1], [1, 0, 1, 2, 3, 4, 6, 7, 9, 10, 6], [1, 0, 1, 2, 5, 3, 6, 7, 8, 6], [7, 0, 1, 2, 3, 4, 2], [0, 0, 1, 6, 3, 4, 7, 3], [2, 0, 2, 1, 3, 4, 5, 6, 4], [11, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 1], [3, 2, 3, 5, 4, 1], [3, 0, 1, 2, 3, 4, 3], [6, 0, 1, 2, 2], [13, 0, 1, 2, 3, 4, 4], [4, 0, 2, 5, 6, 3], [4, 1, 0, 3, 2, 4, 5, 6, 6], [4, 1, 0, 3, 5, 6, 4], [4, 1, 0, 5, 6, 3], [1, 0, 1, 2, 3, 6, 7, 9, 5], [1, 0, 1, 2, 3, 6, 7, 5], [7, 0, 1, 2, 3, 2], [15, 0, 1, 2, 3, 4, 3], [16, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 1]], [[[{"name": "game_title_01", "rect": {"x": 0, "y": 0, "width": 465, "height": 176}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 465, "height": 176}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-232.5, -88, 0, 232.5, -88, 0, -232.5, 88, 0, 232.5, 88, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 176, 465, 176, 0, 0, 465, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -232.5, "y": -88, "z": 0}, "maxPos": {"x": 232.5, "y": 88, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [8], 0, [0], [5], [2]], [[[14, "ViewFightRank"], [15, "ViewFightRank", 33554432, [-12], [[8, -2, [0, "fdJsCylwpH8K0qutihq//O"]], [26, -11, [0, "d3HvJ8lW9D/6OWiTUSav2J"], -10, -9, -8, -7, -6, -5, -4, -3, 11]], [1, "74dEK9KcVFTZQikQ4ZJezW", null, null, null, -1, 0]], [16, "ndRoot", 33554432, 1, [-14, -15, -16, -17, -18, -19, -20, -21, -22, -23], [[8, -13, [0, "46PhPwnytKLqe95bVdh65G"]]], [1, "8fV3kWwXxMjZALfATkPKHf", null, null, null, 1, 0]], [16, "Node-002", 33554432, 2, [-25, -26, -27, -28, -29], [[8, -24, [0, "1eKHXG4ONFXYzGP6u+vMpW"]]], [1, "33Q+Go3flFVIq5oMB9Wrom", null, null, null, 1, 0]], [11, "btnWorldRank", 33554432, 2, [-33], [[[2, -30, [0, "c1wiL7kWVH3batNd1qoJFh"], [5, 294, 87]], [5, -31, [0, "30RLxAasVCv41C9JTtBlgA"], 3], -32], 4, 4, 1], [1, "ebLZZdqHpJ/5NkH5ViQBs7", null, null, null, 1, 0], [1, -180, -738.823, 0]], [11, "btnNextRound", 33554432, 2, [-37, -38], [[[2, -34, [0, "2as/Vim8BLNZWHv3AgccJP"], [5, 294, 87]], [27, -35, [0, "d4r0rHb4dAvrpZz8dnUInt"], [4, 4292269782], 5], -36], 4, 4, 1], [1, "b2TTpkf6RGeLcXYwEJUFd+", null, null, null, 1, 0], [1, 180, -738.823, 0]], [15, "view", 33554432, [-43], [[7, -39, [0, "4bPUIgsbxEy4oGH8ptBGmF"], [5, 750, 690], [0, 0.5, 1]], [20, -40, [0, "cc/zbr/ddLZqe/ZQ4P3eIu"]], [30, 45, 240, 250, -41, [0, "5bIyVSeMVJD7qkm7Qs+lnW"]], [13, -42, [0, "96sxSd1cZMDZysu2HSnFzG"], [4, 16777215]]], [1, "d7OjS1W7xOvI6kFnGOw4Y9", null, null, null, 1, 0]], [25, "btnClose", false, 33554432, 2, [[[2, -44, [0, "559xgIMg9G8YnIkpinrQ9u"], [5, 60, 60]], [28, 1, 0, -45, [0, "4bpwSpPKhMjZ7FcXQmpuj8"], 1], -46], 4, 4, 1], [1, "1aEwRAXxxJdrHRbafqlfcV", null, null, null, 1, 0], [1, 351.401, -580.639, 0]], [3, "ndRankContent", 33554432, 2, [[7, -47, [0, "83jJme0nhBV6xykz/3kljk"], [5, 800, 680], [0, 0.5, 1]], [31, 2, 10, -48, [0, "93fv3ONIhLJKNLlcisnN2k"]], [20, -49, [0, "b5yoO1zaJG7742Ei1PC36F"]], [13, -50, [0, "e7cWIeZCZMY64opZTV9ml2"], [4, 16777215]]], [1, "8e7BNzDMhFq7JtcOutPxpa", null, null, null, 1, 0], [1, 0, 81.611, 0]], [11, "svV", 33554432, 2, [6], [[[7, -51, [0, "00bIdYf2FO96OLMNSjEhtw"], [5, 750, 690], [0, 0.5, 1]], -52], 4, 1], [1, "47ZZm1zVdJiLNbVKwZd5E9", null, null, null, 1, 0], [1, 0, 88.529, 0]], [17, "content", 33554432, 6, [[7, -53, [0, "43BvyfEb5EaoFnzN0ArQQ0"], [5, 750, -20], [0, 0.5, 1]], [32, 1, 2, 50, 20, -1.8, -54, [0, "4bEMuy7vNBGYeHf7fZ7LWK"]]], [1, "b02hDBZd9F55UZ4+isWAcF", null, null, null, 1, 0]], [19, "sprFrame", 33554432, 2, [[[2, -55, [0, "3aq5hgxJVAXo4mgE6TfJMy"], [5, 768, 1344]], -56], 4, 1], [1, "a4x3vAK6VPGKkZuvp14YXt", null, null, null, 1, 0]], [3, "game_title_01", 33554432, 2, [[2, -57, [0, "7bDnNaE4tLYKwhzP7xeX7Q"], [5, 465, 176]], [5, -58, [0, "a8ixM9A8dPGLr+Z/fE78JG"], 0]], [1, "e4gg9JlthGwryt4/K7jLWP", null, null, null, 1, 0], [1, 177.84, 699.246, 0]], [3, "Label", 33554432, 4, [[2, -59, [0, "bcWfoeGnRAEbj/T/3xhjCn"], [5, 140, 54.4]], [21, "世界排行", 34, 34, false, true, -60, [0, "543pXbnwdHurN9VjfKGZn2"], [4, 4280507528], 2]], [1, "0bMnmOI6BJs7/GaavS23xu", null, null, null, 1, 0], [1, 0, 4.331999999999965, 0]], [3, "Label", 33554432, 5, [[2, -61, [0, "0bhZmuz8JJ26u0+Y8c6b5i"], [5, 106, 54.4]], [21, "下一局", 34, 34, false, true, -62, [0, "1cUe/3sUtADYcqnac9Bm2F"], [4, 4287136803], 4]], [1, "d0LrcpCMNBIKAhWGxgcjYu", null, null, null, 1, 0], [1, 0, 4.331999999999965, 0]], [4, "lbTime", 33554432, 5, [[[2, -63, [0, "70Vx7KtOxIe6gxeY5DswEK"], [5, 83.81997680664062, 50.4]], -64], 4, 1], [1, "1868/Kw45FdII38rKiNZYv", null, null, null, 1, 0], [1, 0, -72.96400000000003, 0]], [3, "Label", 33554432, 3, [[2, -65, [0, "fd0WKHRwhKH7Yfxa7AewUF"], [5, 104, 50.4]], [6, "本局排名", 26, 26, false, -66, [0, "37S+gjdqpABrLDNqucoqof"], [4, 4279772212], 6]], [1, "60qklCVRJATaDEaDgyG51b", null, null, null, 1, 0], [1, -321.964, 141.52600000000007, 0]], [3, "Label-001", 33554432, 3, [[2, -67, [0, "831dii9rJHZ5WSdK1eV+7d"], [5, 52, 50.4]], [6, "昵称", 26, 26, false, -68, [0, "49ijF2AwJDWZwIXcECU92K"], [4, 4279772212], 7]], [1, "6bTZcYqOFLfo/kssq2ouBn", null, null, null, 1, 0], [1, -144.697, 141.526, 0]], [3, "Label-002", 33554432, 3, [[2, -69, [0, "cfQFvUBdNHl4A+hAFZUAxg"], [5, 52, 50.4]], [6, "积分", 26, 26, false, -70, [0, "7bMbB3rTJDhojZiocABU9w"], [4, 4279772212], 8]], [1, "2d1bY3Lq1E7JbvE8NFE5cz", null, null, null, 1, 0], [1, 83.811, 141.526, 0]], [18, "Label-003", false, 33554432, 3, [[2, -71, [0, "687dsD4F9FSpVZIEpNq99C"], [5, 104, 50.4]], [6, "本局关卡", 26, 26, false, -72, [0, "bcm0L9prtIX4PWYWGFx3Hw"], [4, 4279772212], 9]], [1, "22xs/Bi+dI1YM2eBL87ZDw", null, null, null, 1, 0], [1, 216.873, 141.526, 0]], [3, "Label-004", 33554432, 3, [[2, -73, [0, "92laAhQZxK7oBPKBcC/cLX"], [5, 52, 50.4]], [6, "排名", 26, 26, false, -74, [0, "56oSeOw7RHCpZi2FmGvGAM"], [4, 4279772212], 10]], [1, "d8DKh/prBKZoN59zHpu744", null, null, null, 1, 0], [1, 320.365, 141.526, 0]], [3, "ndTop", 33554432, 2, [[2, -75, [0, "0d7HsrIMpFiq7KmCg/u+rK"], [5, -60, 100]]], [1, "34dHoUXFJFGKH1AWst/ckU", null, null, null, 1, 0], [1, 0, 376.5899999999999, 0]], [12, 11, [0, "a5sndbeGJNnpyjzvYPyapu"]], [23, 2, 7, [0, "b3OFZB1QlLTJGv5EceiJME"], [4, 4292269782], 7], [23, 3, 4, [0, "17cjwJP4BJwJ1b+OvVuTAa"], [4, 4292269782], 4], [35, "00:00", 30, 30, false, 15, [0, "0cmxtoFdFLULut+QC43lLU"], [4, 4287136803]], [37, 3, 5, [0, "e8LBTPh5dHdKDoLUyHezSn"], [4, 4292269782]], [17, "Node", 33554432, 2, [[8, -76, [0, "5e0VvArQJB5aZDYnw45nwL"]]], [1, "20nHz5Qm1OQYzzBqceFYWY", null, null, null, 1, 0]], [38, false, 1, 9, [0, "07luMBNqhNH7AxwIiTiZ3m"], 10]], 0, [0, 3, 1, 0, 0, 1, 0, 6, 28, 0, 7, 21, 0, 8, 25, 0, 9, 26, 0, 10, 24, 0, 11, 23, 0, 12, 22, 0, 13, 2, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, -1, 11, 0, -2, 12, 0, -3, 7, 0, -4, 4, 0, -5, 5, 0, -6, 27, 0, -7, 8, 0, -8, 3, 0, -9, 21, 0, -10, 9, 0, 0, 3, 0, -1, 16, 0, -2, 17, 0, -3, 18, 0, -4, 19, 0, -5, 20, 0, 0, 4, 0, 0, 4, 0, -3, 24, 0, -1, 13, 0, 0, 5, 0, 0, 5, 0, -3, 26, 0, -1, 14, 0, -2, 15, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, -1, 10, 0, 0, 7, 0, 0, 7, 0, -3, 23, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, -2, 28, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, -2, 22, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, -2, 25, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, 0, 27, 0, 4, 1, 6, 14, 9, 76], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 23, 23, 23, 23, 25, 28], [2, 2, 1, 2, 1, 2, 1, 1, 1, 1, 1, 15, 2, 16, 17, 18, 19, 1, 20], [3, 1, 0, 4, 0, 5, 0, 0, 0, 0, 0, 6, 7, 1, 1, 8, 9, 0, 10]], [[[14, "UnitRoundRank"], [24, "UnitRoundRank", 33554432, [-15, -16, -17, -18, -19, -20, -21, -22, -23, -24, -25], [[2, -2, [0, "26+LEFWf1HnYfHXapv5ELj"], [5, 736, 96]], [5, -3, [0, "2cknrXPqVCgbAWOPRfdS/s"], 4], [39, -14, [0, "4bX9RxEH9Lu7fU2CwpVuj1"], -13, -12, -11, -10, -9, -8, -7, -6, -5, -4]], [1, "f0M2LyMDhEALuI7yPwe841", null, null, null, -1, 0], [1, 0, -48, 0]], [10, "Node-002", 33554432, 1, [-29], [[2, -26, [0, "a1zpgWVFVBLbqttzmUlSyu"], [5, 70, 70]], [29, 1, -27, [0, "93ZaDlR7BIaYqzrjXdCDjV"]], [13, -28, [0, "75zImfDy5PdZj5D0JBMgZ3"], [4, 16777215]]], [1, "31RpopHv9IAY0Ecx2G3dNh", null, null, null, 1, 0], [1, -225.599, 1.6889999999999645, 0]], [10, "Node", 33554432, 1, [-32, -33], [[7, -30, [0, "97ZMKCwLxHPKNIbLcXXpBw"], [5, 59.95997619628906, 100], [0, 1, 0.5]], [33, 1, 1, 10, -31, [0, "cfX4+pimdBwrn5HSAIvag3"]]], [1, "9bI/MeiB5DZ4S/0YcfKKlK", null, null, null, 1, 0], [1, 337.22, -1.72199999999998, 0]], [10, "Node-001", 33554432, 1, [-36, -37], [[2, -34, [0, "20ToQjcR5Ltaw7rrCsuSRq"], [5, 100, 92.03999999999999]], [34, 1, 2, -35, [0, "eeSVBaNB5NoIjQEFTynfQW"]]], [1, "d2dXDH98BFYInEzedXyElM", null, null, null, 1, 0], [1, 76.75699999999995, 0.9840000000000373, 0]], [3, "ndIconUp", 33554432, 3, [[2, -38, [0, "54w17h34xJUY1v8Afho6pV"], [5, 13, 23]], [5, -39, [0, "60Gz47P5pBG6d76+41FVQH"], 3]], [1, "58OQS/7H1OJIQ9+UmcaTOj", null, null, null, 1, 0], [1, -6.5, 2.495999999999981, 0]], [3, "sprAFrame", 33554432, 1, [[2, -40, [0, "aaLkYex81LurRMjLG6STZ0"], [5, 70, 70]], [5, -41, [0, "08Wxa1DiBCbqwjgXlva/Sd"], 0]], [1, "cbNKKt3hFMRqc6XKtiv9xk", null, null, null, 1, 0], [1, -225.594, 0.8239999999998417, 0]], [19, "spr<PERSON><PERSON><PERSON>", 33554432, 2, [[[2, -42, [0, "5aUwI7f5REzqPp+3ljUqtr"], [5, 99, 100]], -43], 4, 1], [1, "16Gcu7kHRFzLAYVpSnT4/Y", null, null, null, 1, 0]], [3, "sprMask", 33554432, 1, [[2, -44, [0, "4f3KnOiBtE5ZDQh+8hWP2Z"], [5, 70, 70]], [5, -45, [0, "0440ULHuFPw5rrbD/QWNTh"], 1]], [1, "592rr3kSxDPqWlQXeHlYPB", null, null, null, 1, 0], [1, -225.599, 1.689, 0]], [4, "sprRankNum", 33554432, 1, [[[2, -46, [0, "1dW2iu42RHkac+Ik0ADFi7"], [5, 61, 61]], -47], 4, 1], [1, "8dlIk72RBCUIEr6a8rmkcP", null, null, null, 1, 0], [1, -325.744, 0, 0]], [4, "lbRankNum", 33554432, 1, [[[2, -48, [0, "20NwSbPTlEO5VulWEoCOrb"], [5, 8.419998168945312, 50.4]], -49], 4, 1], [1, "72t2CoZTBDj7rrSNBcgJZ9", null, null, null, 1, 0], [1, -325.803, 0.8, 0]], [4, "lbName", 33554432, 1, [[[2, -50, [0, "2bClEtSwNKMraeJ9kR/bX5"], [5, 40, 50.4]], -51], 4, 1], [1, "01YKYYI8ZKjrTnhAHzlGpw", null, null, null, 1, 0], [1, -89.116, 0.8, 0]], [4, "lbRoundNum", 33554432, 1, [[[2, -52, [0, "dfTU70AxdLnoLYLJ4+e5bo"], [5, 0, 50.4]], -53], 4, 1], [1, "c6+KJVgT1LUrHV2VkVNCSC", null, null, null, 1, 0], [1, 210.466, 16.924, 0]], [4, "lbRoundChange", 33554432, 1, [[[2, -54, [0, "41r7nOyZFHLqnFSae7p3KY"], [5, 0, 50.4]], -55], 4, 1], [1, "71tx0pgYlOirZBefRi4gNC", null, null, null, 1, 0], [1, 210.466, -14.276, 0]], [18, "Label-007", false, 33554432, 1, [[2, -56, [0, "9dcRyYaYZNUJDhjAM3vU3Q"], [5, 80, 50.4]], [6, "上局排名", 20, 20, false, -57, [0, "bf4NWM7glGS6iHRKlWLEEp"], [4, 4279772212], 2]], [1, "78tXBDlEVIA6ddcvYjYHZ3", null, null, null, 1, 0], [1, 311.484, -12.722, 0]], [4, "lbRankChange", 33554432, 3, [[[2, -58, [0, "adui0YwRtCFLv/N1htino1"], [5, 36.95997619628906, 50.4]], -59], 4, 1], [1, "14aDBmn9tA0rr4DRnd8T+I", null, null, null, 1, 0], [1, -41.47998809814453, 2.7479999999999336, 0]], [4, "lbScore", 33554432, 4, [[[2, -60, [0, "32D9h+tc9A8ocBiXGXwCJQ"], [5, 44.639984130859375, 37.8]], -61], 4, 1], [1, "8dKOTDyW9NzLfsSagNpv+g", null, null, null, 1, 0], [1, 0.3490000000000464, 27.119999999999994, 0]], [4, "lbScoreDesc", 33554432, 4, [[[2, -62, [0, "53E6hGwD9PdaVQATHXIPHZ"], [5, 143.77996826171875, 54.239999999999995]], -63], 4, 1], [1, "0b2SvxeLNL8b8WgCXU4txv", null, null, null, 1, 0], [1, 0.3490000000000464, -18.9, 0]], [12, 7, [0, "57h6ZqTelH+5dlJ5EC2UAg"]], [12, 9, [0, "ecJLLbvLNJD7ntDHLWBIrw"]], [36, "1", 20, 20, false, 10, [0, "b46nsFmblMK66BXn5i2R47"]], [9, "名称", 20, 20, false, 11, [0, "1fK4TGcfVPXI5DfV5QcIP/"], [4, 4279772212]], [9, "", 20, 20, false, 12, [0, "13zI2NiwhNI62ggGJRO1ta"], [4, 4279772212]], [9, "", 20, 20, false, 13, [0, "d0b+wP1zNIKodIBhktCr6R"], [4, 4279668742]], [9, "999", 20, 20, false, 15, [0, "125bhY8h5CIqEy4ZDwMRQk"], [4, 4278859720]], [22, "99万", 20, 20, 30, false, 16, [0, "f9qXO5KNpMxKs2yM+LQDv1"], [4, 4279772212]], [22, "积分池 +999\n关卡加成 ****万", 20, 20, 24, false, 17, [0, "eei2Ykp5NMAJrMmNKUJqfU"], [4, 4289488687]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, 21, 24, 0, 22, 23, 0, 23, 22, 0, 24, 26, 0, 25, 25, 0, 26, 21, 0, 27, 20, 0, 28, 5, 0, 29, 19, 0, 30, 18, 0, 0, 1, 0, -1, 6, 0, -2, 2, 0, -3, 8, 0, -4, 9, 0, -5, 10, 0, -6, 11, 0, -7, 12, 0, -8, 13, 0, -9, 14, 0, -10, 3, 0, -11, 4, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 7, 0, 0, 3, 0, 0, 3, 0, -1, 15, 0, -2, 5, 0, 0, 4, 0, 0, 4, 0, -1, 16, 0, -2, 17, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, -2, 18, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, -2, 19, 0, 0, 10, 0, -2, 20, 0, 0, 11, 0, -2, 21, 0, 0, 12, 0, -2, 22, 0, 0, 13, 0, -2, 23, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, -2, 24, 0, 0, 16, 0, -2, 25, 0, 0, 17, 0, -2, 26, 0, 4, 1, 63], [0, 0, 0, 0, 0, 19, 20, 21, 22, 23, 24, 25, 26], [2, 2, 1, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1], [11, 12, 0, 13, 14, 15, 0, 0, 0, 0, 0, 0, 0]]]]