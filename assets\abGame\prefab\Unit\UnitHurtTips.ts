import { _decorator, Color, Component, Label, log, Node, tween, Tween, TweenSystem, UIOpacity, v3, Vec2, Vec3 } from 'cc';
import Tool from 'db://assets/scripts/libs/utils/Tool';
const { ccclass, property } = _decorator;

@ccclass('UnitHurtTips')
export class UnitHurtTips extends Component {

    @property(Label)
    private lbTips: Label = null;

    private _tempPos: Vec3 = new Vec3();
    private _tempPos2: Vec3 = new Vec3();
    private _tempScale: Vec3 = new Vec3();
    private _tempColor: Color = new Color();
    private _tempColor2: Color = new Color();
    private _tempSize: number
    private _tempDelay: number

    private _tempTween: Tween
    private _cb: Function



    protected onLoad(): void {

    }
    setData(data, cb) {
        this._tempPos.set(data.posx, data.posy + 30)
        this.node.setPosition(this._tempPos);
        this._tempDelay = Math.random() * 0.2;
        this.formatNum(data.num,data.isBao);
        this._tempPos2.set(data.posx, data.posy + 120);
        this._tempScale.set(1.4, 1.4, 1.4);
        this._cb = cb;

        if (!this._tempTween) {
            this._tempTween = new Tween(this.node)
                .delay(this._tempDelay)
                .to(0.2, { position: this._tempPos2 }, { easing: 'cubicInOut' })
                .to(0.2, { scale: this._tempScale })

                .call(() => {
                    this._tempScale.set(1, 1, 1);
                    this.node.scale = this._tempScale
                    this._cb && this._cb(this);
                    this._tempTween.stop();
                })
        }


        if (this._tempTween) {
            this._tempTween.start()

        }
    }

    // 灰色，1~599
    // 橙色，500~999
    // 红色：1000以上
    formatNum(num: number, isBao: boolean = false) {
        let txt = num > 0 ? num.toString() : 'miss';
        if (num < 100) {
            this._tempSize = 34;
            this._tempColor.set(255, 245, 226, 255);
            this._tempColor2.set(64, 41, 0, 255);
        } else if (num < 300) {
            this._tempSize = 34;
            this._tempColor.set(255, 245, 140, 255);
            this._tempColor2.set(200, 128, 0, 255);
        } else {
            this._tempSize = 40;
            this._tempColor.set(255, 245, 140, 255);
            this._tempColor2.set(255, 0, 0, 255);
            /* if (num > 10000) {
                txt = Tool.numberToTenThousand(num)
            } */
        }
        this.lbTips.color = this._tempColor;
        this.lbTips.outlineColor = this._tempColor2;
        this.lbTips.fontSize = this._tempSize;
        this.lbTips.string = isBao ? '暴击' + txt : txt;
    }
}


