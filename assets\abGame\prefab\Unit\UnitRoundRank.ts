import { _decorator, Component, Label, Node, size, Sprite } from 'cc';
import { xcore } from '../../../scripts/libs/xcore';
import { StringUtil } from 'db://assets/scripts/libs/utils/StringUtil';
const { ccclass, property } = _decorator;

@ccclass('UnitRoundRank')
export class UnitRoundRank extends Component {

    @property(Sprite)
    private sprAvatar: Sprite = null;

    @property(Sprite)
    private sprRankNum: Sprite = null;

    @property(Node)
    private ndIconUp: Node = null;

    @property(Label)
    private lbRankNum: Label = null;

    @property(Label)
    private lbName: Label = null;

    @property(Label)
    private lbScore: Label = null;

    @property(Label)
    private lbScoreDesc: Label = null;

    @property(Label)
    private lbRoundNum: Label = null;

    @property(Label)
    private lbRoundChange: Label = null;

    @property(Label)
    private lbRankChange: Label = null;



    setData(data: any, nickName: string, iconUrl: string, rank: number, level: number, oldWeekRank: number, weekRank: number, upscore?: number) {
        this.lbName.string = StringUtil.sub(nickName || '匿名用户', 9, true);
        xcore.res.remoteLoadSprite(iconUrl, this.sprAvatar, size(70, 70));
        this.lbRankNum.string = `${rank + 1}`;
        this.lbScore.string = data.score;
        this.lbRankChange.string = weekRank < 0 ? '' : (weekRank + 1).toString();
        this.ndIconUp.active = oldWeekRank > 0 && weekRank < oldWeekRank;
        this.lbScoreDesc.node.active = (data.poolScore);
        this.lbScoreDesc.string = `积分池 +${data.poolScore}\n${upscore > 0 ? `关卡加成 +${upscore}` : ''}`;
    }

}


