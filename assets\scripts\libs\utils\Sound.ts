//Sound.ts
import { Node, AudioSource, AudioClip, resources, director, assetManager, Component, log } from 'cc';
import { Singleton } from './Singleton';

import { ResManager } from '../manager/ResManager';

/**
 * @en
 * this is a sington class for audio play, can be easily called from anywhere in you project.
 * @zh
 * 这是一个用于播放音频的单件类，可以很方便地在项目的任何地方调用。
 */
export class Sound extends Component {

    //音效开关
    private _soundAble: boolean = true;
    //背景音乐开关
    private _musicAble: boolean = true

    private _soundNum: number = 1;
    private _musicNum: number = 1;

    private _audioSource: AudioSource;

    protected onLoad(): void {
        this._audioSource = this.node.addComponent(AudioSource);
    }
    initSoundState() {

    }
    public get audioSource() {
        return this._audioSource;
    }

    /**
     * @en
     * play short audio, such as strikes,explosions
     * @zh
     * 播放短音频,比如 打击音效，爆炸音效等
     * @param sound clip or url for the audio
     * @param volume 
     */
    playOneShot(sound: AudioClip | string, volume: number = 1.0, bundleName: string = 'resources') {
        if (!this._soundAble) return
        if (sound instanceof AudioClip) {
            this._audioSource.playOneShot(sound, volume);
        }
        else {
            let bundle = assetManager.getBundle(bundleName);
            bundle.load(sound, (err, clip: AudioClip) => {
                if (err) {
                    console.log(err);
                }
                else {
                    this._audioSource.playOneShot(clip, volume);
                }
            });
        }
    }


    /**
     * @en
     * play long audio, such as the bg music
     * @zh
     * 播放长音频，比如 背景音乐
     * @param sound clip or url for the sound
     * @param volume 
     */
    play(sound: AudioClip | string, volume: number = 1.0, bundleName: string = 'resources', isLoop: boolean = true) {
        if (!this._musicAble) return
        if (sound instanceof AudioClip) {
            this._audioSource.clip = sound;
            this._audioSource.play();
            this.audioSource.volume = volume;
            this._audioSource.loop = isLoop;
        }
        else {
            let bundle = assetManager.getBundle(bundleName);
            bundle.load(sound, (err, clip: AudioClip) => {
                if (err) {
                    console.log(err);
                }
                else {
                    this._audioSource.clip = clip;
                    this._audioSource.play();
                    this.audioSource.volume = volume;
                    this._audioSource.loop = isLoop;
                }
            });
        }
    }
    //远程加载播放背景音乐
    remotePlay(url: string, volume: number = 1.0, isLoop: boolean = true) {
        if (!this._musicAble) return
        ResManager.getInstance().remoteLoadAny(url, (err, clip) => {

            if (err) {
                console.log(err);
            }
            else {
                this.stop();
                this._audioSource.clip = clip;
                this._audioSource.play();
                this.audioSource.volume = volume;
                this._audioSource.loop = isLoop;
            }
        })
    }
    //远程加载播放音效
    remotePlayOneShot(url: string) {
        if (!this._musicAble) return
        ResManager.getInstance().remoteLoadAny(url, (err, clip) => {

            if (err) {
                console.log(err);
            }
            else {
                this._audioSource.playOneShot(clip, this._soundNum);
            }
        })
    }
    switchSound(isable: boolean) {
        this._soundAble = isable;
    }
    switchMusic(isable: boolean) {
        this._musicAble = isable;
        if (!this._musicAble) {
            this.pause();
        } else {
            this.resume();
        }
    }
    setSoundNum(num: number = 1) {
        this._soundNum = num;
    }
    setMusicNum(num: number = 1) {
        this._musicNum = num;

        if (this._audioSource) {
            this.audioSource.volume = this._musicNum;

        }

    }
    /**
     * stop the audio play
     */
    stop() {
        this._audioSource && this._audioSource.stop();
    }

    /**
     * pause the audio play
     */
    pause() {
        this._audioSource && this._audioSource.pause();
    }

    /**
     * resume the audio play
     */
    resume() {
        (this._audioSource && this._audioSource.clip) && this._audioSource.play();
        /* (this._audioSource && !this._audioSource.clip) && this.playBgMusic(); */
    }
}