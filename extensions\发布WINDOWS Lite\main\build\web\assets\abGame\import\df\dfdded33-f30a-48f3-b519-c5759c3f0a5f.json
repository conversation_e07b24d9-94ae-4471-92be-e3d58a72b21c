[1, ["52fHu7D8hGm5vLaoALoXCl", "46IdxunHlHYYawWmS60uVs@f9941", "20g1ukYUVPvKWKBRznAKo+@f9941", "54TknWPwVPqJqeCR+Y/Czo@f9941", "95EkngnxZFbYuFpsqVTaFr@f9941", "46amII9lpO2bPbVg4yf9HH@f9941", "a6HGGCGI1LX5mSMegT6BXH@f9941", "37CDiVfFxLwKS5C6el7uJO@f9941", "c4QFjyHkxBQoGsOs+ecoqi@f9941"], ["node", "_font", "_spriteFrame", "_normalSprite", "_hoverSprite", "_pressedSprite", "_disabledSprite", "root", "btnBack", "btnModeD", "btnModeM", "btnModeE", "btnClose", "sprFrame", "ndRoot", "data"], [["cc.Node", ["_name", "_layer", "_active", "_parent", "_components", "_prefab", "_lpos", "_children"], 0, 1, 12, 4, 5, 2], ["cc.Node", ["_name", "_layer", "_components", "_prefab", "_children", "_parent", "_lpos"], 1, 9, 4, 2, 1, 5], ["cc.Sprite", ["_type", "_sizeMode", "node", "__prefab", "_spriteFrame"], 1, 1, 4, 6], ["cc.<PERSON><PERSON>", ["_transition", "_zoomScale", "node", "__prefab", "_target", "_normalColor"], 1, 1, 4, 1, 5], ["cc.Label", ["_string", "_actualFontSize", "_isSystemFontUsed", "_outlineWidth", "_enableOutline", "_fontSize", "node", "__prefab", "_outlineColor", "_font", "_color"], -3, 1, 4, 5, 6, 5], ["cc.UITransform", ["node", "__prefab", "_contentSize"], 3, 1, 4, 5], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["f1b89gDe8FFAYMI8QfI/pUT", ["node", "__prefab", "ndRoot", "sprFrame", "btnClose", "btnModeE", "btnModeM", "btnModeD", "btnBack"], 3, 1, 4, 1, 1, 1, 1, 1, 1, 1]], [[7, 0, 2], [8, 0, 1, 2, 3, 4, 5, 5], [5, 0, 1, 2, 1], [1, 0, 1, 5, 2, 3, 6, 3], [0, 0, 1, 3, 7, 4, 5, 6, 3], [2, 2, 3, 4, 1], [4, 0, 1, 2, 4, 3, 6, 7, 8, 9, 6], [4, 0, 1, 5, 2, 3, 6, 7, 10, 8, 9, 6], [5, 0, 1, 1], [2, 0, 2, 3, 4, 2], [3, 0, 2, 3, 5, 4, 2], [3, 0, 1, 2, 3, 3], [6, 0, 2], [1, 0, 1, 4, 2, 3, 3], [1, 0, 1, 5, 4, 2, 3, 3], [0, 0, 1, 3, 4, 5, 3], [0, 0, 2, 1, 3, 4, 5, 6, 4], [0, 0, 1, 3, 4, 5, 6, 3], [2, 0, 1, 2, 3, 3], [3, 0, 1, 2, 3, 4, 3], [4, 0, 1, 2, 4, 3, 6, 7, 10, 8, 9, 6], [9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 1]], [[12, "ViewSelectGameMode"], [13, "ViewSelectGameMode", 33554432, [-11], [[8, -2, [0, "f2NW8LwRpHU7EZ48d2Qg21"]], [21, -10, [0, "3bdk76JvdElrKf7HqMACx4"], -9, -8, -7, -6, -5, -4, -3]], [1, "7fdACQN+NN9bdl414qEukQ", null, null, null, -1, 0]], [14, "ndCore", 33554432, 1, [-13, -14, -15, -16, -17, -18, -19], [[8, -12, [0, "ceDknsZwJDWKQDQTwLIbsH"]]], [1, "738BskzZxG9ZKKeo1Kr3xe", null, null, null, 1, 0]], [4, "common_btn_modeeasy", 33554432, 2, [-23, -24], [[[2, -20, [0, "40RwNQp5pMYoDlrQOcA1D+"], [5, 643, 197]], [5, -21, [0, "99y4FalEBMPKghhASZQE/4"], 3], -22], 4, 4, 1], [1, "05YQayUhpLvYRlWHwcm0Oe", null, null, null, 1, 0], [1, 0, 274.3800000000001, 0]], [4, "common_btn_modem", 33554432, 2, [-28, -29], [[[2, -25, [0, "f03VPg20pKxIjEx8FOnnmX"], [5, 643, 196]], [5, -26, [0, "85HABZCJVMtoGXnjgPct3L"], 6], -27], 4, 4, 1], [1, "34vfrg4+FJG5jUHXK0flnF", null, null, null, 1, 0], [1, 0, -20.22849999999994, 0]], [4, "common_btn_modeeasyd", 33554432, 2, [-33, -34], [[[2, -30, [0, "7cB3/zLi9LBam3+ZRY5ZeC"], [5, 643, 196]], [5, -31, [0, "2dhGAUXMZJ5JMmtWBp3Oqu"], 9], -32], 4, 4, 1], [1, "94y3ceFs9OZqj27CQv74Kj", null, null, null, 1, 0], [1, 0, -314.837, 0]], [16, "btnClose", false, 33554432, 2, [[[2, -35, [0, "d2cYWvCiNKKL2XyV5ygOg3"], [5, 112, 113]], [9, 1, -36, [0, "a4l59h04NNkbFkIKZpYoDv"], 0], -37], 4, 4, 1], [1, "abN/7PKktJdL6yEwO+zpba", null, null, null, 1, 0], [1, 336.976, 534.9490000000001, 0]], [17, "btnBack", 33554432, 2, [[[2, -38, [0, "c3C3zw6m5KJa+DAhnixDQI"], [5, 112, 113]], [9, 1, -39, [0, "c4Nk6PJK5F/qFIiOpxz1KG"], 11], -40], 4, 4, 1], [1, "a8mi361mtH7og+0kA1ZzwO", null, null, null, 1, 0], [1, 336.976, 534.9490000000001, 0]], [15, "Sprite", 33554432, 2, [[[2, -41, [0, "27Op11DVdBVbODiOKBqDMy"], [5, 706, 1100]], -42], 4, 1], [1, "ed2JzOp9FGaIimUgIdV+L0", null, null, null, 1, 0]], [3, "Label", 33554432, 3, [[2, -43, [0, "b6nAK5u/JIiI+vZhe4VHZ4"], [5, 86, 56.4]], [6, "简单", 40, false, true, 3, -44, [0, "9dItCpZh9OFrKufNI22C5m"], [4, 4289534740], 1]], [1, "d5TqqVZAFN8Yc6+JZCYR5H", null, null, null, 1, 0], [1, -213.99400000000003, 44.27099999999996, 0]], [3, "Label-002", 33554432, 3, [[2, -45, [0, "5dvgVGMsRDKLbzD8pde2+s"], [5, 322.5, 50.4]], [7, "掉落绿色、蓝色品质碎片", 30, 30, false, 3, -46, [0, "c5ZcXLYqlI4rtGdg/dAm7b"], [4, 4287776798], [4, 4280236409], 2]], [1, "dfS5otHFROn59lZ8EkQF5+", null, null, null, 1, 0], [1, -124.67399267578128, -34.226, 0]], [3, "Label-001", 33554432, 4, [[2, -47, [0, "eehR1o/wVE+JxIQvNZQSYw"], [5, 86, 56.4]], [6, "中等", 40, false, true, 3, -48, [0, "b3+mae91NPd6+I0c9tXSNc"], [4, 4280236409], 4]], [1, "2b8GEqR49F+INx0rBJwx09", null, null, null, 1, 0], [1, -213.994, 44.271, 0]], [3, "Label-002", 33554432, 4, [[2, -49, [0, "9cX0SopxxO9ImchwvM4ctM"], [5, 322.5, 50.4]], [7, "掉落蓝色、紫色品质碎片", 30, 30, false, 3, -50, [0, "be0IAlCFBBUIn+HSc56jtj"], [4, 4280170386], [4, 4280236409], 5]], [1, "b1Nu0frqpAFZvkjtJr2yVO", null, null, null, 1, 0], [1, -124.67399267578128, -34.226, 0]], [3, "Label-003", 33554432, 5, [[2, -51, [0, "0csKbxU71HqYT9pi/0IKGX"], [5, 86, 56.4]], [6, "困难", 40, false, true, 3, -52, [0, "10RWaICy9NOoBxPUzxEnqX"], [4, 4289466750], 7]], [1, "7bR9QUM45CZIHP9FWebkhn", null, null, null, 1, 0], [1, -213.99400000000003, 44.27099999999996, 0]], [3, "Label-004", 33554432, 5, [[2, -53, [0, "0ajTnqUjJAdLRl+sB8yN/1"], [5, 322.5, 50.4]], [7, "掉落紫色、橙色品质碎片", 30, 30, false, 3, -54, [0, "dddTCS75hDVrDOElgHeJcV"], [4, 4291432047], [4, 4280236409], 8]], [1, "658VQMyQpNHK8vR9BCL8Xy", null, null, null, 1, 0], [1, -124.67399267578128, -34.226, 0]], [3, "Label", 33554432, 2, [[2, -55, [0, "25c6ziwAtMDryST6kwAKAv"], [5, 166, 56.4]], [20, "难度选择", 40, false, true, 3, -56, [0, "5f0SkLxsdPLL4e6ilg/oNR"], [4, 4292605695], [4, 4278929278], 10]], [1, "ecN4XDpKhKab9gX9P9OlwN", null, null, null, 1, 0], [1, -15.085000000000036, 515.924, 0]], [18, 1, 0, 8, [0, "c3hc5dACpMpraJ/06oQave"]], [10, 3, 6, [0, "2dgw31MlFAmrPp1JKmzEZR"], [4, 4292269782], 6], [19, 3, 1.02, 3, [0, "62eLocXuxLEKJRqL1kso4o"], 3], [11, 3, 1.02, 4, [0, "53qKlt4SVC7JoaiJMFffdp"]], [11, 3, 1.02, 5, [0, "4di0EJcmtEQLNygh26DSoH"]], [10, 3, 7, [0, "96NcFf6ApF4J2HlrTm5zuo"], [4, 4292269782], 7]], 0, [0, 7, 1, 0, 0, 1, 0, 8, 21, 0, 9, 20, 0, 10, 19, 0, 11, 18, 0, 12, 17, 0, 13, 16, 0, 14, 2, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, -1, 8, 0, -2, 6, 0, -3, 3, 0, -4, 4, 0, -5, 5, 0, -6, 15, 0, -7, 7, 0, 0, 3, 0, 0, 3, 0, -3, 18, 0, -1, 9, 0, -2, 10, 0, 0, 4, 0, 0, 4, 0, -3, 19, 0, -1, 11, 0, -2, 12, 0, 0, 5, 0, 0, 5, 0, -3, 20, 0, -1, 13, 0, -2, 14, 0, 0, 6, 0, 0, 6, 0, -3, 17, 0, 0, 7, 0, 0, 7, 0, -3, 21, 0, 0, 8, 0, -2, 16, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, 15, 1, 56], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16, 17, 17, 17, 17, 21, 21, 21, 21], [2, 1, 1, 2, 1, 1, 2, 1, 1, 2, 1, 2, 2, 3, 4, 5, 6, 3, 4, 5, 6], [1, 0, 0, 5, 0, 0, 6, 0, 0, 7, 0, 1, 8, 1, 2, 3, 4, 1, 2, 3, 4]]