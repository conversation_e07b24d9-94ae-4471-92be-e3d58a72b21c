[1, ["52fHu7D8hGm5vLaoALoXCl", "b5YLPadOZMx60zj6fdCuKY@f9941"], ["node", "_font", "_spriteFrame", "root", "lbDesc", "lbRoleName", "bgAnim", "role", "data"], [["cc.Node", ["_name", "_layer", "_components", "_prefab", "_children", "_parent", "_lpos"], 1, 9, 4, 2, 1, 5], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.Sprite", ["_sizeMode", "node", "__prefab", "_color", "_spriteFrame"], 2, 1, 4, 5, 6], ["cc.Label", ["_string", "_horizontalAlign", "_actualFontSize", "_isSystemFontUsed", "_enableOutline", "_outlineWidth", "_fontSize", "_lineHeight", "node", "__prefab", "_color", "_outlineColor"], -5, 1, 4, 5, 5], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_layer", "_parent", "_components", "_prefab", "_lpos"], 1, 1, 12, 4, 5], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["sp.Skeleton", ["_preCacheMode", "node", "__prefab"], 2, 1, 4], ["cc.Animation", ["node", "__prefab"], 3, 1, 4], ["51c10fRLy9Gu6a1mV378KpA", ["node", "__prefab", "role", "bgAnim", "lbRoleName", "lbDesc"], 3, 1, 4, 1, 1, 1, 1]], [[6, 0, 2], [7, 0, 1, 2, 3, 4, 5, 5], [5, 0, 1, 2, 3, 4, 5, 3], [1, 0, 1, 2, 1], [1, 0, 1, 3, 1], [4, 0, 2], [0, 0, 1, 4, 2, 3, 3], [0, 0, 1, 5, 2, 3, 3], [0, 0, 1, 5, 4, 2, 3, 6, 3], [1, 0, 1, 1], [2, 0, 1, 2, 3, 4, 2], [2, 1, 2, 1], [8, 0, 1, 2, 2], [9, 0, 1, 1], [3, 0, 1, 2, 3, 4, 5, 8, 9, 10, 11, 7], [3, 0, 1, 2, 6, 7, 3, 4, 5, 8, 9, 10, 11, 9], [10, 0, 1, 2, 3, 4, 5, 1]], [[5, "UnitTowerPointShowMessage"], [6, "UnitTowerPointShowMessage", 33554432, [-8, -9], [[3, -2, [0, "46xvI+QlFIBZmDDW2PDsUs"], [5, 1000, 2000]], [16, -7, [0, "32asMVp5pOEqO8TAcjirZc"], -6, -5, -4, -3]], [1, "60U5wUJ8xA5I16y1URVmNO", null, null, null, -1, 0]], [8, "ndRoot", 33554432, 1, [-11, -12, -13, -14], [[9, -10, [0, "93s63kbE5OP4B48G4qi85e"]]], [1, "b8IbX/MANNXKZ2lu5RQLOk", null, null, null, 1, 0], [1, 0, 511.4119999999999, 0]], [2, "role", 33554432, 2, [[[4, -15, [0, "788Jgy8DxK5Iye7ZxeZsfq"], [0, 0.5, 0]], [11, -16, [0, "e3vsiDEo5P/IIXW0OUZwc0"]], -17], 4, 4, 1], [1, "cb3U1f1bNBYoHnlvZPoUl6", null, null, null, 1, 0], [1, 0, -886.8129999999998, 0]], [7, "Sprite", 33554432, 1, [[3, -18, [0, "39yomjXtFL5rdmNT3OoZDl"], [5, 900, 1160]], [10, 0, -19, [0, "52tseq2CBA8JsoOI9EGtFQ"], [4, 1426063360], 0]], [1, "42hJ7nFvxFeKfbKXSyQ2S3", null, null, null, 1, 0]], [2, "anim", 33554432, 2, [[[4, -20, [0, "56kENr0Q9M+7m0qIYFVMVD"], [0, 0.5, 0.4405008765048007]], -21], 4, 1], [1, "25lPsWpKBJTqyjkJXTRIpO", null, null, null, 1, 0], [1, 0, -649.1869999999998, 0]], [2, "lbDesc", 33554432, 2, [[[3, -22, [0, "9fRmXwHJVNhYXjb9rVpm2D"], [5, 44.79998779296875, 58.4]], -23], 4, 1], [1, "73mdceo6tFQ6dZr41xnax9", null, null, null, 1, 0], [1, 0, -165.36299999999983, 0]], [2, "lbRoleName", 33554432, 2, [[[3, -24, [0, "5aNBunzddHL6jq8EfecPqD"], [5, 81.5999755859375, 108.8]], -25], 4, 1], [1, "c4ZMIzkFVJNb1gJButcN4T", null, null, null, 1, 0], [1, 0, -77.57299999999987, 0]], [12, 0, 5, [0, "1eqVQRX3RPU7VzrsdslMe1"]], [13, 3, [0, "caCBXWCUNLN4IhrmPetGDD"]], [14, "--", 0, 40, false, true, 4, 6, [0, "0cXKrAXV5DSIChPU3zIEPd"], [4, 4282967039], [4, 4280098273]], [15, "--", 0, 80, 80, 80, false, true, 4, 7, [0, "3cPV4ttExJbL1x/YLS94ou"], [4, 4282967039], [4, 4280098273]]], 0, [0, 3, 1, 0, 0, 1, 0, 4, 10, 0, 5, 11, 0, 6, 8, 0, 7, 9, 0, 0, 1, 0, -1, 4, 0, -2, 2, 0, 0, 2, 0, -1, 5, 0, -2, 3, 0, -3, 6, 0, -4, 7, 0, 0, 3, 0, 0, 3, 0, -3, 9, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, -2, 8, 0, 0, 6, 0, -2, 10, 0, 0, 7, 0, -2, 11, 0, 8, 1, 25], [0, 10, 11], [2, 1, 1], [1, 0, 0]]