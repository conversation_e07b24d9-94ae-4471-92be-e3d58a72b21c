import { _decorator, Button, Component, instantiate, Label, log, Node, size, Sprite, SpriteFrame } from 'cc';
import { ViewBase } from '../../scripts/libs/manager/ViewBase';
import { ConfigHelper } from '../../scripts/config/ConfigHelper';
import { xcore } from '../../scripts/libs/xcore';
const { ccclass, property } = _decorator;

@ccclass('ViewSkinDetail')
export class ViewSkinDetail extends ViewBase {
    @property([SpriteFrame])
    private sfFrames: SpriteFrame[] = [];
    @property([SpriteFrame])
    private sfOns: SpriteFrame[] = [];


    @property(Sprite)
    private sprBg: Sprite = null;

    @property(Sprite)
    private sprOn: Sprite = null;

    @property(Sprite)
    private sprSkin: Sprite = null;

    @property(Label)
    private lbSkinName: Label = null;

    @property(Label)
    private lbAtk: Label = null;

    @property(Label)
    private lbSpeed: Label = null;

    @property(Label)
    private lbSkill: Label = null;

    @property(Label)
    private lbProp: Label = null;

    @property(Node)
    private pfbBtn: Node = null;

    @property(Node)
    private ndBtnContent: Node = null;

    private _data: any = null;

    protected onLoadCompleted(): void {


    }

    public setData(data: any): void {
        this._data = data;
        this.refreshData(1);
        let maxLev = ConfigHelper.getInstance().getSkinMaxLevelBySkinId(this._data.skinId);
        for (let i = 0; i < maxLev; i++) {
            let btn = instantiate(this.pfbBtn);
            btn.getChildByName('Label').getComponent(Label).string = `等级${i + 1}`;
            btn.parent = this.ndBtnContent
            btn.on('click', () => {
                this.refreshData(i + 1)
            }, this)
        }
    }
    refreshData(lev: number) {
        let skinConfig = ConfigHelper.getInstance().getSkinLevelConfigBySkinId(this._data.skinId, lev);
        if (skinConfig) {
            let config = ConfigHelper.getInstance().getSkinConfigByJsonId(this._data.skinId);
            this.lbSkinName.string = `Lv.${lev} ${this._data.skinName}`;
            let atks = skinConfig.attack.split('|');
            this.lbAtk.string = '攻击力：' + (atks[0] == atks[1] ? atks[0] : `${atks[0]}~${atks[1]}`);
            this.lbSpeed.string = '攻速：' + skinConfig.attackCooldown;
            this.sprBg.spriteFrame = this.sfFrames[config.quality - 1];
            this.sprOn.spriteFrame = this.sfOns[config.quality - 1];
            xcore.res.bundleLoadSprite('resources', `./res/image/${config.path}/${config.skinRole}`, this.sprSkin, size(200, 290));
            this.lbSkill.node.active = !!skinConfig.skill;
            this.lbProp.node.active = !!skinConfig.attacKeffect;



            let effect = ConfigHelper.getInstance().getEffectConfigByJsonId(skinConfig.attacKeffect);
            let skill = ConfigHelper.getInstance().getSkillConfigByJsonId(skinConfig.skill);
            if (skill) {
                this.lbSkill.string = skill.name + ':' + skill.skillDescribe;
            }
            if (effect) {
                this.lbProp.string = effect.name + ':' + effect.describe;
            }
        }

        log('skinConfig:', skinConfig)
    }
}


