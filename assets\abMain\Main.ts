import { _decorator, Button, Component, EditBox, instantiate, Label, log, Node, warn } from 'cc';

import { WECHAT } from 'cc/env';
import { gameVersion, xcore } from '../scripts/libs/xcore';
import { C_Bundle, C_Scene, C_View, E_GameMode } from '../scripts/ConstGlobal';
import ResLoader from '../scripts/libs/manager/ResLoader';
import Tool from '../scripts/libs/utils/Tool';
;






const { ccclass, property } = _decorator;

@ccclass('Main')
export class Main extends Component {

    @property(Button)
    private btnJoin: Button = null;

    @property(Button)
    private btnSetting: Button = null;

    @property(Button)
    private btnRank: Button = null;

    @property(Button)
    private btnModeEasy: Button = null;

    @property(Button)
    private btnModeDifficult: Button = null;

    @property(Button)
    private btnModeHell: Button = null;

    @property(Button)
    private btnModeBoss: Button = null;

    @property(Button)
    private btnModePass: Button = null;

    @property(Label)
    private lbVersion: Label = null;


    @property(Node)
    private ndUITop: Node = null;

    @property(Node)
    private ndUIBottom: Node = null;

    @property([Node])
    private ndIconSelects: Node[] = []

    private doFinishLoadingPgsCmd: Function = null;

    private _gameMode: number = 0;

    private _time: number = 0;

    async onLoad() {
        this.ndUITop.active = false;
        this.ndUIBottom.active = false;

        let loadingResConfig = {
            resLoading: {
                sprBg: {
                    bundleName: 'resources',
                    path: 'img_unpack/ready_loading_bg'
                },
                sprLogo: {
                    bundleName: 'resources',
                    path: 'img_unpack/ready_loading_logo'
                }
            },
            resLoadingPgs: {
                sprBg: {
                    bundleName: 'resources',
                    path: 'img_unpack/ready_loadingpgs_bg'
                },
                sprBar: {
                    bundleName: 'resources',
                    path: 'img_unpack/ready_loadingpgs_bar'
                },
            }
        }
        this.doFinishLoadingPgsCmd = await xcore.ui.showLoadingUI(loadingResConfig, this.onLoginFinish.bind(this));
        await this.preloadGameRes();
        this.scheduleOnce(() => {
            this.doFinishLoadingPgsCmd && this.doFinishLoadingPgsCmd();
            this.ndUITop.active = true;
            this.ndUIBottom.active = true;
            this.addButtonListener();
        }, 2)

        xcore.sound.play('./res/sound/bgm', xcore.gameData.soundData.music, 'resources', true)
        this.lbVersion.string = `v${gameVersion}`
    }
    addButtonListener() {
        this.btnJoin.node.on('click', this.gameStart, this);
        this.btnModeEasy.node.on('click', this.switchMode.bind(this, 0), this);
        this.btnModeDifficult.node.on('click', this.switchMode.bind(this, 1), this);
        this.btnModeHell.node.on('click', this.switchMode.bind(this, 2), this);
        this.btnRank.node.on('click', () => {
            xcore.ui.addView(C_View.ViewRank)
        }, this);
        this.btnSetting.node.on('click', () => {
            xcore.ui.addView(C_View.ViewSetting)
        }, this)
        this.btnModePass.node.on('click', () => {
            xcore.ui.addView(C_View.ViewSelectGameMode)
        }, this);
        this.btnModeBoss.node.on('click', () => {
            xcore.ui.addView(C_View.ViewSelectGameType)
        }, this);
        if (this.node.getChildByName('btnTest')) {
            this.node.getChildByName('btnTest').on('click', this.openDebug, this)
        }
    }
    removeListener() {
        this.btnJoin.node.off('click');
        this.btnModeEasy.node.off('click');
        this.btnModeDifficult.node.off('click');
        this.btnModeHell.node.off('click');
        this.btnRank.node.off('click');
        this.btnSetting.node.off('click');

        if (this.node.getChildByName('btnTest')) {
            this.node.getChildByName('btnTest').off('click')
        }
    }
    openDebug() {

        this._time += 1;
        if (this._time > 10) {
            if (this.node.getChildByName('btnTest')) {
                this.node.getChildByName('btnTest').active = false;
            }
            Tool.openDevTool()
            log('openDebug')
        }

    }
    /**开始游戏 */
    gameStart() {
        this.removeListener();
        log('游戏难度', this._gameMode);
        xcore.ui.switchScene(C_Bundle.abGame, C_Scene.Game);
    }
    /**切换难度 */
    switchMode(mode: E_GameMode) {

        xcore.gameData.gameMode = mode;
        this.gameStart();
    }

    /**预加载游戏内资源 配置表 图片 动画*/
    async preloadGameRes() {

    }

    onLoginFinish() {

    }


}


