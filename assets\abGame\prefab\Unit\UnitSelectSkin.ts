import { _decorator, Animation, Component, log, Node, UITransform, v3, Vec3 } from 'cc';
import { ConfigHelper } from 'db://assets/scripts/config/ConfigHelper';
import Tool from 'db://assets/scripts/libs/utils/Tool';
import { xcore } from 'db://assets/scripts/libs/xcore';
const { ccclass, property } = _decorator;

@ccclass('UnitSelectSkin')
export class UnitSelectSkin extends Component {
    @property(Animation)
    private anim: Animation = null;


    async setData(data: any) {
        let config = ConfigHelper.getInstance().getAnimConfigByJsonId(data.moveAnimation);
        let speed = 1;
        let animaData = {
            'sample': config.sample,
            'duration': config.duration,
            'speed': speed,
            'wrapMode': config.wrapMode,
            'path': config.path,
            'name': config.name
        }
        let atlasName = animaData.name.split('-')[0] || 'default';
        let atlas = xcore.res.getAtlas(atlasName);

        this.node.scale = v3(data.animationScale * 1.5 || 3, data.animationScale * 1.5 || 3, data.animationScale * 1.5 || 3)

        for (let i = 0; i < animaData.sample; i++) {
            let name = `${animaData.name}_${i < 10 ? `0${i}` : i}`;
            let sf = atlas?.getSpriteFrame(name)
            if (!sf) {
                // let sf = await xcore.res.remoteLoadSprite(xcore.gameData.cospath + `image/${animaData.path}/${name}.png`) //await xcore.res.bundleLoadSprite(C_Bundle.abGame, `./res/anim/${animaData.path}/${name}`)
                let sf = await xcore.res.bundleLoadSprite('resources', `./res/image/${animaData.path}/${name}`) //await xcore.res.bundleLoadSprite(C_Bundle.abGame, `./res/anim/${animaData.path}/${name}`)
                xcore.res.addAtlasSprite(atlasName, name, sf);

            }
        }

        await Tool.createAnim(this.anim, animaData, atlas, false);
        this.scheduleOnce(() => {
            this.anim.play(animaData.name);
        })
    }
}


