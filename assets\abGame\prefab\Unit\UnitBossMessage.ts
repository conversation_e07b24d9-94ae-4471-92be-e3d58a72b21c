import { _decorator, Component, Label, log, Node, size, Sprite, VideoClip } from 'cc';
import { ConfigHelper } from 'db://assets/scripts/config/ConfigHelper';
import { xcore } from 'db://assets/scripts/libs/xcore';
import { MediaVideo } from 'db://assets/scripts/mediaVideo';
import { GiftMessageMgr } from '../../scripts/GiftMessageMgr';
import { E_GiftMessageType } from 'db://assets/scripts/ConstGlobal';
import { FightMgr } from '../../scripts/FightMgr';
import Tool from 'db://assets/scripts/libs/utils/Tool';
const { ccclass, property } = _decorator;

@ccclass('UnitBossMessage')
export class UnitBossMessage extends Component {

    @property(Node)
    private ndVideo: Node = null;

    @property(Node)
    private ndNormal: Node = null;

    @property(Node)
    private ndTop: Node = null;

    @property(Label)
    private lbName: Label = null;

    @property(Label)
    private lbName2: Label = null;

    @property(Label)
    private lbRank: Label = null;

    @property(Label)
    private lbRank2: Label = null;

    @property(Sprite)
    private sprAvatar: Sprite = null;

    @property(Sprite)
    private sprAvatar2: Sprite = null;


    @property(MediaVideo)
    private video: MediaVideo = null;


    async setData(data) {
        Tool.setChildrenNodeSortByPriority(this.node, 10);
        let config = ConfigHelper.getInstance().getBossJoinConfig(data.lev + 1);
        let user = FightMgr.getInstance().findUser(data.userId) || { nickName: '匿名用户', iconUrl: null, lev: data.lev };
        this.ndTop.active = false;
        this.ndNormal.active = data.lev >= 50;
        this.ndVideo.active = data.lev < 50;
        //前三大哥 有视频
        if (data.lev < 50) {
            this.video.videoPlayer.node.active = true;
            this.lbRank.string = `周榜第${data.lev + 1}`
            this.lbName.string = user.nickName;
            xcore.res.remoteLoadSprite(user.iconUrl, this.sprAvatar, size(100, 100))
            if (config) {
                try {
                    let clip = await xcore.res.bundleLoadClip('resources', `./res/video/${config.name.split('.')[0]}`)

                    this.video.setData(clip, () => {
                        this.ndTop.active = true;
                    }, () => {

                        this.kill();
                    })
                } catch (err) {

                    this.kill();
                }
            } else {

                this.kill();
            }
        }
        //前100
        else {
            this.lbRank2.string = data.lev + 1;
            this.lbName2.string = user.nickName;
            xcore.res.remoteLoadSprite(user.iconUrl, this.sprAvatar2, size(100, 100))

            this.scheduleOnce(() => {
                this.kill();
            }, 1.3)
        }



    }


    kill() {
        this.node.removeFromParent();
        GiftMessageMgr.getInstance().killBossMessage(this);
        this.destroy();
    }
}


