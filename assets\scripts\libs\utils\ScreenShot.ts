
import { _decorator, Component, Node, Camera, RenderTexture, director, gfx, ImageAsset, renderer, view, Size, Texture2D, SpriteFrame, Sprite, UITransform, spriteAssembler, sys, Vec2, Canvas, warnID, log, error, Button, assetManager, instantiate, Vec3, Label, Layers, color } from 'cc';
import { JSB, PREVIEW } from 'cc/env';
import { Canvas2Image } from "./Canvas2Image";
const { ccclass, property } = _decorator;

@ccclass('Screenshot2D')
export class Screenshot2D extends Component {

    @property(Camera)
    copyCamera: Camera = null!;

    @property(Node)
    targetNode: Node = null!;

    private _rt: RenderTexture = null;
    private _canvas: HTMLCanvasElement = null!;
    private _buffer: ArrayBufferView = null!;



    start() {

        this._rt = new RenderTexture();
        this._rt.reset({
            width: view.getVisibleSize().width,
            height: view.getVisibleSize().height,
        })
        this.copyCamera.targetTexture = this._rt;
        this.scheduleOnce(() => {
            this.capture();
        }, 2)

    }


    //位运算操作 检查state的第pos位是否为1 
    bitCheck(state: number, pos: number) {
        return state & 1 << pos - 1;
    }

    //将state的第pos位的值设为1  
    bitAdd(state: number, pos: number) {
        return state | (1 << (pos - 1));
    }

    //将state的第pos位的值设为0  
    bitDel(state: number, pos: number) {
        return state & (~(1 << (pos - 1)));
    }
    capture() {
        this.copyRenderTex();
    }

    copyRenderTex() {
        var width = this.targetNode.getComponent(UITransform).width;
        var height = this.targetNode.getComponent(UITransform).height;
        var worldPos = this.targetNode.getWorldPosition();
        this._buffer = this._rt.readPixels(Math.round(worldPos.x), Math.round(worldPos.y), width, height);
        this.showImage(width, height);
    }

    showImage(width, height) {
        let img = new ImageAsset();
        img.reset({
            _data: this._buffer,
            width: width,
            height: height,
            format: Texture2D.PixelFormat.RGBA8888,
            _compressed: false
        });
        let texture = new Texture2D();
        texture.image = img;
        let sf = new SpriteFrame();
        sf.texture = texture;
        sf.packable = false;

        setTimeout(() => {
            this.savaAsImage(width, height, this._buffer)
        }, 2);
    }
    
    savaAsImage(width, height, arrayBuffer) {
        if (sys.isBrowser) {
            if (!this._canvas) {
                this._canvas = document.createElement('canvas');
                this._canvas.width = width;
                this._canvas.height = height;
            } else {
                this.clearCanvas();
            }
            let ctx = this._canvas.getContext('2d')!;
            let rowBytes = width * 4;
            for (let row = 0; row < height; row++) {
                let sRow = height - 1 - row;
                let imageData = ctx.createImageData(width, 1);
                let start = sRow * width * 4;
                for (let i = 0; i < rowBytes; i++) {
                    imageData.data[i] = arrayBuffer[start + i];
                }
                ctx.putImageData(imageData, 0, row);
            }
            //@ts-ignore
            this.canvas2image.saveAsPNG(this._canvas, width, height);

        } else if (sys.isNative) {
            //@ts-ignore
            let filePath = jsb.fileUtils.getWritablePath() + 'render_to_sprite_image.png';
            //@ts-ignore
            if (jsb.saveImageData) {
                //@ts-ignore
                jsb.saveImageData(this._buffer, width, height, filePath).then(() => {
                    assetManager.loadRemote<ImageAsset>(filePath, (err, imageAsset) => {
                        if (err) {
                            console.log("show image error")
                        } else {
                            var newNode = instantiate(this.targetNode);
                            newNode.setPosition(new Vec3(-newNode.position.x, newNode.position.y, newNode.position.z));
                            this.targetNode.parent.addChild(newNode);

                            const spriteFrame = new SpriteFrame();
                            const texture = new Texture2D();
                            texture.image = imageAsset;
                            spriteFrame.texture = texture;
                            newNode.getComponent(Sprite).spriteFrame = spriteFrame;
                            spriteFrame.packable = false;
                            spriteFrame.flipUVY = true;
                            if (sys.isNative && (sys.os === sys.OS.IOS || sys.os === sys.OS.OSX)) {
                                spriteFrame.flipUVY = false;
                            }


                        }
                    });
                    log("save image data success, file: " + filePath);

                }).catch(() => {
                    error("save image data failed!");

                });
            }
        } else if (sys.platform === sys.Platform.WECHAT_GAME) {
            if (!this._canvas) {
                //@ts-ignore
                this._canvas = wx.createCanvas();
                this._canvas.width = width;
                this._canvas.height = height;
            } else {
                this.clearCanvas();
            }
            var ctx = this._canvas.getContext('2d');

            var rowBytes = width * 4;

            for (var row = 0; row < height; row++) {
                var sRow = height - 1 - row;
                var imageData = ctx.createImageData(width, 1);
                var start = sRow * width * 4;

                for (var i = 0; i < rowBytes; i++) {
                    imageData.data[i] = arrayBuffer[start + i];
                }

                ctx.putImageData(imageData, 0, row);
            }
            //@ts-ignore
            this._canvas.toTempFilePath({
                x: 0,
                y: 0,
                width: this._canvas.width,
                height: this._canvas.height,
                destWidth: this._canvas.width,
                destHeight: this._canvas.height,
                fileType: "png",
                success: (res) => {
                    //@ts-ignore
                    wx.showToast({
                        title: "截图成功"
                    });

                    //@ts-ignore
                    wx.saveImageToPhotosAlbum({
                        filePath: res.tempFilePath,
                        success: (res) => {
                            //@ts-ignore              
                            wx.showToast({
                                title: "成功保存到设备相册",
                            });

                        },
                        fail: () => {

                        }
                    })
                },
                fail: () => {
                    //@ts-ignore
                    wx.showToast({
                        title: "截图失败"
                    });

                }
            })
        }
    }

    clearCanvas() {
        let ctx = this._canvas.getContext('2d');
        ctx.clearRect(0, 0, this._canvas.width, this._canvas.height);
    }

    onSaveImageBtnClicked() {
        var width = this.targetNode.getComponent(UITransform).width;
        var height = this.targetNode.getComponent(UITransform).height;
        this.savaAsImage(width, height, this._buffer)
    }
}
