import { _decorator, Component, EventTouch, Node, PageView, size, Sprite, UITransform, Vec3, view } from 'cc';
import { xcore } from '../../scripts/libs/xcore';
import { ConfigHelper } from '../../scripts/config/ConfigHelper';
const { ccclass, property } = _decorator;

@ccclass('UIGiftDesc')
export class UIGiftDesc extends Component {

    @property(PageView)
    private pv: PageView = null;


    private _giftTempPos: Vec3 = new Vec3();
    private _viewLimitLeft: number
    private _viewLimitRight: number
    private _viewLimitBottom: number
    private _viewLimitTop: number


    onLoad() {
        let viewSize = view.getVisibleSize();
        this._viewLimitLeft = -viewSize.width / 2 + 80 + this.node.getComponent(UITransform).width / 2
        this._viewLimitRight = viewSize.width / 2 - 80 - this.node.getComponent(UITransform).width / 2
        this._viewLimitBottom = -viewSize.height / 2 + 20 + this.node.getComponent(UITransform).height / 2
        this._viewLimitTop = viewSize.height / 2 - 20 - this.node.getComponent(UITransform).height / 2
        this.node.on(Node.EventType.TOUCH_START, this.onTouchStart, this, true);
        this.node.on(Node.EventType.TOUCH_MOVE, this.onTouchMove, this, true);
        this.node.on(Node.EventType.TOUCH_END, this.onTouchEnd, this, true);
        this.node.on(Node.EventType.TOUCH_CANCEL, this.onTouchEnd, this, true);
        this._giftTempPos.set(this._viewLimitRight, this._viewLimitBottom)
        this.node.setPosition(this._giftTempPos)
        this.initBanner()
    }

    initBanner() {
        let giftPicUrl = ConfigHelper.getInstance().getConstantConfigByKey('giftPicture');
        let giftPicUrl2 = ConfigHelper.getInstance().getConstantConfigByKey('giftPicture2');
        let configs = [];
        if (giftPicUrl) {
            configs.push(giftPicUrl)

        }
        if (giftPicUrl2) {
            configs.push(giftPicUrl2)
        }
        for (let i = 0; i < configs.length; i++) {
            let url = configs[i];
            if (url) {
                let spr = new Node().addComponent(Sprite);
                xcore.res.remoteLoadSprite(url, spr, size(430, 370));
                this.pv.addPage(spr.node);
            }
        }
        this.randomMoveBanner(configs.length);

    }
    randomMoveBanner(num: number) {
        if (num <= 1) return
        if (this && this.node && this.node.isValid) {
            this.scheduleOnce(() => {
                let pageNum = this.pv.getCurrentPageIndex();
                this.pv.scrollToPage(pageNum >= num - 1 ? 0 : pageNum + 1, 0.6)
                this.randomMoveBanner(num)
            }, 6);
        }
    }


    onTouchStart(event: EventTouch) {
        this._giftTempPos = this.node.getPosition();
    }
    onTouchMove(event: EventTouch) {
        let moveX = event.getDeltaX() * 2;
        let moveY = event.getDeltaY() * 2;
        //移动范围限制
        if (this._giftTempPos.x + moveX > this._viewLimitLeft && this._giftTempPos.x + moveX < this._viewLimitRight) {
            this._giftTempPos.x += moveX;
        }
        if (this._giftTempPos.y + moveY > this._viewLimitBottom && this._giftTempPos.y + moveY < this._viewLimitTop) {
            this._giftTempPos.y += moveY;
        }
        this.node.setPosition(this._giftTempPos);

    }
    onTouchEnd(event: EventTouch) {

    }
}


