import { _decorator, Component, Animation, Label, Node, sp, Sprite, SpriteFrame, size, log, UITransform, v3, tween, TweenSystem } from 'cc';
import { ConfigHelper } from 'db://assets/scripts/config/ConfigHelper';
import { C_Bundle } from 'db://assets/scripts/ConstGlobal';
import Tool from 'db://assets/scripts/libs/utils/Tool';
import { xcore } from 'db://assets/scripts/libs/xcore';
import { GiftMessageMgr } from '../../scripts/GiftMessageMgr';

const { ccclass, property } = _decorator;


@ccclass('UnitSkinRewardMessage')
export class UnitSkinRewardMessage extends Component {

    @property(Node)
    private ndCore: Node = null;
    @property(sp.Skeleton)
    private anim: sp.Skeleton = null;

    @property(Label)
    private lbTitle: Label = null;

    @property(Sprite)
    private sprBg: Sprite = null;

    @property(Animation)
    private role: Animation = null;

    @property(Sprite)
    private sprAvatar: Sprite = null;

    @property(Label)
    private lbUserName: Label = null;

    @property(Label)
    private lbName: Label = null;

    protected onLoad(): void {
        this.node.on(Node.EventType.TOUCH_END, () => {
            this.node.removeFromParent();
            GiftMessageMgr.getInstance().killSkinRewardMessage(this)
        }, this)
    }
    async setData(data: any) {
        /*  TweenSystem.instance.ActionManager.removeAllActionsFromTarget(this.ndCore);
         this.node.setScale(v3(0, 0, 0));
         tween(this.ndCore)
             .to(0.15, { scale: v3(1, 1, 1) }, { easing: "backOut" })
             .start() */
        log("debris reward:", data)
        let config = ConfigHelper.getInstance().getSkinConfigByJsonId(data.skinId);
        let animConfig = ConfigHelper.getInstance().getAnimConfigByJsonId(config.moveAnimation);
        let quality = config.quality || 1;

        let spinePath = `./res/anim/skinshow/${quality}/tianhu`;
        xcore.res.bundleLoadSpine(C_Bundle.abGame, spinePath, this.anim).then(() => {
            this.anim.setAnimation(0, 'attack_1', true)
        });

        xcore.res.bundleLoadSprite(C_Bundle.abGame, `./res/img_unpack/game_skinreward_bg0${quality}`, this.sprBg)
        this.lbUserName.string = Tool.sub(data.user.nickName || '匿名用户', 10, true);
        xcore.res.remoteLoadSprite(data.user.iconUrl, this.sprAvatar, size(80, 80));
        this.lbName.string = config.name;
        if (data.level > 1) {
            this.lbTitle.string = `升级到Lv.${data.level}`;
        } else {
            this.lbTitle.string = '解锁'
        }
        let animaData = {
            'sample': animConfig.sample,
            'duration': animConfig.duration,
            'speed': 1,
            'wrapMode': animConfig.wrapMode,
            'path': animConfig.path,
            'name': animConfig.name
        }
        let atlasName = animaData.name.split('-')[0] || 'default';
        let atlas = xcore.res.getAtlas(atlasName);

        for (let i = 0; i < animaData.sample; i++) {
            let name = `${animaData.name}_${i < 10 ? `0${i}` : i}`;
            let sf = atlas.getSpriteFrame(name)
            if (!sf) {
                // let sf = await xcore.res.remoteLoadSprite(xcore.gameData.cospath + `image/${animaData.path}/${name}.png`) //await xcore.res.bundleLoadSprite(C_Bundle.abGame, `./res/anim/${animaData.path}/${name}`)
                let sf = await xcore.res.bundleLoadSprite('resources', `./res/image/${animaData.path}/${name}`) //await xcore.res.bundleLoadSprite(C_Bundle.abGame, `./res/anim/${animaData.path}/${name}`)
                xcore.res.addAtlasSprite(atlasName, name, sf);

            }
        }
        //缩放配置
        if (animConfig.XCenterPoint == undefined || animConfig.XCenterPoint == null) {
            animConfig.XCenterPoint = 0.5;
        } else if (animConfig.XCenterPoint == "") {
            animConfig.XCenterPoint = 0;
        }
        if (animConfig.YCenterPoint == undefined || animConfig.YCenterPoint == null) {
            animConfig.YCenterPoint = 0.5;
        } else if (animConfig.YCenterPoint == "") {
            animConfig.YCenterPoint = 0;
        }



        await Tool.createAnim(this.role, animaData, atlas);

        //配置动画中心位置
        let animUITransform = this.role?.node?.getComponent(UITransform);
        if (!animUITransform) return
        this.scheduleOnce(() => {
            if (!animUITransform) return
            let h = animUITransform.height;
            let scale = 600 / h;
            animUITransform.node.scale = v3(scale, scale)
        })

        animUITransform?.setAnchorPoint(animConfig.XCenterPoint, animConfig.YCenterPoint);
        this.scheduleOnce(() => {
            if (this.node && this.node.isValid) {
                this.node.removeFromParent();
                GiftMessageMgr.getInstance().killSkinRewardMessage(this)
            }

        }, 3)
    }
}


