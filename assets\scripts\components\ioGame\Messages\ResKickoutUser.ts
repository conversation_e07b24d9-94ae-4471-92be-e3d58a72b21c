import { StringValue } from '../../../protos/ExternalMessage'
import BroadcastCtrl from '../BroadcastCtrl'
import { MessageCmdEvent, MessageRoomSubCmd } from '../MessageEvent'
import { _decorator } from 'cc'
const { ccclass } = _decorator

/**
 * 分数违规踢出用户
 */
@ccclass()
export default class ResKickoutUser extends BroadcastCtrl {
	////////////////////消息单例////////////////////
	public static get inst() {
		return this.getInst(ResKickoutUser)
	}

	constructor() {
		super()
		this.cmd = MessageCmdEvent.roomCmd
		this.subCmd = MessageRoomSubCmd.kickout
		this.addListen()
	}

	receive(code: number, data: any) {
		super.receive(code, StringValue.decode(data))
	}
}
