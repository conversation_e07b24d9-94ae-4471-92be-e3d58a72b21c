import { _decorator, Button, Component, instantiate, Label, log, Node, Prefab } from 'cc';
import { ViewBase } from '../../scripts/libs/manager/ViewBase';

import ScrollViewProCom from '../../scripts/libs/utils/ScrollViewProCom';
import { UnitSelectGameLevel } from './Unit/UnitSelectGameLevel';

import { ConfigHelper } from '../../scripts/config/ConfigHelper';
import { xcore } from '../../scripts/libs/xcore';
import { E_EVENT } from '../../scripts/ConstGlobal';

const { ccclass, property } = _decorator;

@ccclass('ViewSelectGameLevel')
export class ViewSelectGameLevel extends ViewBase {


    @property(ScrollViewProCom)
    private svLev: ScrollViewProCom = null;






    private _configs: any
    private _selectLevIndex: number


    start() {
        this._configs = ConfigHelper.getInstance().getLevelConfigs();
        this.svLev.setView(this._configs, (n: Node, data, index: number) => {
            n.getComponent(UnitSelectGameLevel).setData(data, index, (selectIndex) => {
                log("selectIndex", selectIndex)
                xcore.gameData.gameSelectLev = selectIndex;
                this.closeSelf();
                xcore.event.raiseEvent(E_EVENT.GameConfig)
            });
        });
    }



}


