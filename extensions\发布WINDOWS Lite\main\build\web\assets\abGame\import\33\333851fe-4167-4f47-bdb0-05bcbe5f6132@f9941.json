[1, ["33OFH+QWdPR72wBby+X2Ey@6c48a"], ["_textureSource"], ["cc.SpriteFrame"], 0, [{"name": "tian<PERSON>", "rect": {"x": 2, "y": 2, "width": 2037, "height": 2037}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 2041, "height": 2041}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-1018.5, -1018.5, 0, 1018.5, -1018.5, 0, -1018.5, 1018.5, 0, 1018.5, 1018.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [2, 2039, 2039, 2039, 2, 2, 2039, 2], "nuv": [0.0009799118079372856, 0.0009799118079372856, 0.9990200881920627, 0.0009799118079372856, 0.0009799118079372856, 0.9990200881920627, 0.9990200881920627, 0.9990200881920627], "minPos": {"x": -1018.5, "y": -1018.5, "z": 0}, "maxPos": {"x": 1018.5, "y": 1018.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]]