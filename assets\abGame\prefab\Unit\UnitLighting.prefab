[{"__type__": "cc.Prefab", "_name": "UnitLighting", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "UnitLighting", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [{"__id__": 67}, {"__id__": 69}], "_prefab": {"__id__": 71}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0.7071067811865475, "w": 0.7071067811865476}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 90}, "_id": ""}, {"__type__": "cc.Node", "_name": "effect", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}, {"__id__": 62}, {"__id__": 64}], "_prefab": {"__id__": 66}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0.5, "y": 0.4999999999999999, "z": 0.5, "w": 0.5000000000000001}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 3, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 90, "y": 0, "z": 90}, "_id": ""}, {"__type__": "cc.ParticleSystem", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 4}, "_materials": [{"__uuid__": "65b8477b-44d7-471e-b159-28d6cbb8432a", "__expectedType__": "cc.Material"}, null], "_visFlags": 0, "startColor": {"__id__": 5}, "scaleSpace": 1, "startSize3D": false, "startSizeX": {"__id__": 6}, "startSize": {"__id__": 6}, "startSizeY": {"__id__": 7}, "startSizeZ": {"__id__": 8}, "startSpeed": {"__id__": 9}, "startRotation3D": false, "startRotationX": {"__id__": 10}, "startRotationY": {"__id__": 11}, "startRotationZ": {"__id__": 12}, "startRotation": {"__id__": 12}, "startDelay": {"__id__": 13}, "startLifetime": {"__id__": 14}, "duration": 1, "loop": true, "simulationSpeed": 3, "playOnAwake": true, "gravityModifier": {"__id__": 15}, "rateOverTime": {"__id__": 16}, "rateOverDistance": {"__id__": 17}, "bursts": [], "_renderCulling": false, "_cullingMode": 0, "_aabbHalfX": 100.00000000000003, "_aabbHalfY": 300.0698950617284, "_aabbHalfZ": 1, "_dataCulling": false, "enableCulling": false, "_colorOverLifetimeModule": {"__id__": 18}, "_shapeModule": {"__id__": 25}, "_sizeOvertimeModule": {"__id__": 27}, "_velocityOvertimeModule": {"__id__": 32}, "_forceOvertimeModule": {"__id__": 37}, "_limitVelocityOvertimeModule": {"__id__": 41}, "_rotationOvertimeModule": {"__id__": 46}, "_textureAnimationModule": {"__id__": 50}, "_noiseModule": {"__id__": 55}, "_trailModule": {"__id__": 56}, "renderer": {"__id__": 61}, "_prewarm": false, "_capacity": 50, "_simulationSpace": 1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d707t9sn1PzZ9fLGXXny76"}, {"__type__": "cc.GradientRange", "_mode": 0, "color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}}, {"__type__": "cc.CurveRange", "mode": 3, "constantMin": 100, "constantMax": 100, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 3, "constantMin": 0.1, "constantMax": 0.3, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 3, "constantMin": 0.5, "constantMax": 0.5, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 1.5, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.ColorOvertimeModule", "_enable": false, "color": {"__id__": 19}}, {"__type__": "cc.GradientRange", "_mode": 1, "gradient": {"__id__": 20}}, {"__type__": "cc.Gradient", "colorKeys": [{"__id__": 21}, {"__id__": 22}], "alphaKeys": [{"__id__": 23}, {"__id__": 24}], "mode": 0}, {"__type__": "cc.<PERSON><PERSON>", "color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "time": 0}, {"__type__": "cc.<PERSON><PERSON>", "color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "time": 1}, {"__type__": "cc.<PERSON><PERSON><PERSON>", "alpha": 255, "time": 0}, {"__type__": "cc.<PERSON><PERSON><PERSON>", "alpha": 255, "time": 0.5591428571428572}, {"__type__": "cc.ShapeModule", "_enable": false, "_shapeType": 0, "shapeType": 0, "emitFrom": 3, "alignToDirection": false, "randomDirectionAmount": 0, "sphericalDirectionAmount": 0, "randomPositionAmount": 0, "radius": 1, "radiusThickness": 1, "arcMode": 0, "arcSpread": 0, "arcSpeed": {"__id__": 26}, "length": 5, "boxThickness": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_position": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_rotation": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_scale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_arc": 6.283185307179586, "_angle": 0.4363323129985824}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 1, "multiplier": 1}, {"__type__": "cc.SizeOvertimeModule", "_enable": false, "separateAxes": false, "size": {"__id__": 28}, "x": {"__id__": 29}, "y": {"__id__": 30}, "z": {"__id__": 31}}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.VelocityOvertimeModule", "_enable": false, "x": {"__id__": 33}, "y": {"__id__": 34}, "z": {"__id__": 35}, "speedModifier": {"__id__": 36}, "space": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 1, "multiplier": 1}, {"__type__": "cc.ForceOvertimeModule", "_enable": false, "x": {"__id__": 38}, "y": {"__id__": 39}, "z": {"__id__": 40}, "space": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.LimitVelocityOvertimeModule", "_enable": true, "limitX": {"__id__": 42}, "limitY": {"__id__": 43}, "limitZ": {"__id__": 44}, "limit": {"__id__": 45}, "dampen": 0.1, "separateAxes": false, "space": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0.1, "multiplier": 1}, {"__type__": "cc.RotationOvertimeModule", "_enable": false, "_separateAxes": false, "x": {"__id__": 47}, "y": {"__id__": 48}, "z": {"__id__": 49}}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.TextureAnimationModule", "_enable": true, "_numTilesX": 1, "numTilesX": 1, "_numTilesY": 4, "numTilesY": 4, "_mode": 0, "animation": 0, "frameOverTime": {"__id__": 51}, "startFrame": {"__id__": 54}, "cycleCount": 1, "_flipU": 0, "_flipV": 0, "_uvChannelMask": -1, "randomRow": false, "rowIndex": 0}, {"__type__": "cc.CurveRange", "mode": 2, "splineMin": {"__id__": 52}, "splineMax": {"__id__": 53}, "multiplier": 1}, {"__type__": "cc.RealCurve", "_times": [0, 1], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 1, "rightTangentWeight": 1, "leftTangent": 1, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 1, "rightTangentWeight": 1, "leftTangent": 1, "leftTangentWeight": 1, "easingMethod": 0}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.RealCurve", "_times": [0, 1], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0.18518518518518517, "rightTangent": 1, "rightTangentWeight": 1, "leftTangent": 1, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0.8148148148148149, "rightTangentWeight": 1, "leftTangent": 0.8148148148148149, "leftTangentWeight": 1, "easingMethod": 0}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.NoiseModule", "_enable": false, "_strengthX": 10, "_strengthY": 10, "_strengthZ": 10, "_noiseSpeedX": 0, "_noiseSpeedY": 0, "_noiseSpeedZ": 0, "_noiseFrequency": 1, "_remapX": 0, "_remapY": 0, "_remapZ": 0, "_octaves": 1, "_octaveMultiplier": 0.5, "_octaveScale": 2}, {"__type__": "cc.TrailModule", "_enable": false, "mode": 0, "lifeTime": {"__id__": 57}, "_minParticleDistance": 0.1, "existWithParticles": true, "textureMode": 0, "widthFromParticle": true, "widthRatio": {"__id__": 58}, "colorFromParticle": false, "colorOverTrail": {"__id__": 59}, "colorOvertime": {"__id__": 60}, "_space": 0, "_particleSystem": {"__id__": 3}}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 1, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.GradientRange", "_mode": 0, "color": {"__type__": "cc.Color", "r": 255, "g": 0, "b": 0, "a": 255}}, {"__type__": "cc.GradientRange", "_mode": 0, "color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}}, {"__type__": "cc.ParticleSystemRenderer", "_renderMode": 1, "_velocityScale": 1, "_lengthScale": 1, "_mesh": null, "_cpuMaterial": {"__uuid__": "65b8477b-44d7-471e-b159-28d6cbb8432a", "__expectedType__": "cc.Material"}, "_gpuMaterial": null, "_mainTexture": {"__uuid__": "b6c511ed-799b-42a4-aa67-062ac9c37802@6c48a", "__expectedType__": "cc.Texture2D"}, "_useGPU": false, "_alignSpace": 1}, {"__type__": "cc.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 63}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c8X72XbEVGZL5aYJZy9+ZS"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 65}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4btZxw4w5C9KgfKx+zJyN5"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a08+rnwyVPJ7lM78P5+Vn1", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 68}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "32dRDF3w5IPbCx0RXipxrV"}, {"__type__": "dabfdDKmgNLiYZKZt77GOh7", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 70}, "ndLight": {"__id__": 2}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9ePrSKdRVNVbzcqaagwgfJ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "54ja70r4tFaKnQKeeQl6ak", "instance": null, "targetOverrides": []}]