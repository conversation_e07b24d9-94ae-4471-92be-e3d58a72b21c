/* eslint-disable */
import Long from 'long'
import _m0 from 'protobufjs/minimal.js'

export const protobufPackage = 'pb'

/** <p>閹村潡妫块悩鑸碉拷锟�-閺嬫矮濡囩猾锟�</p> */
export enum RoomStatusEnum {
	/** WAITING - 缁涘绶熷ú璇插З瀵拷閸氼垶妯佸▓锟� */
	WAITING = 0,
	/** GAME_READY - 濞撳憡鍨欓崙鍡楊槵闂冭埖顔� */
	GAME_READY = 1,
	/** GAME_BIG_COUNTDOWN - 濞撳憡鍨欏锟芥慨瀣亣鐏炲繐锟芥帟顓搁弮锟� */
	GAME_BIG_COUNTDOWN = 2,
	/** GAME_COUNTDOWN - 濞撳憡鍨欏锟芥慨瀣拷鎺曨吀閺冿拷 */
	GAME_COUNTDOWN = 3,
	/** GAME_START - 濞撳憡鍨欏锟介崥顖炴▉濞堬拷 */
	GAME_START = 4,
	/** GAME_END - 濞撳憡鍨欑紒鎾存将闂冭埖顔� */
	GAME_END = 5,
	/** CLOSE - 濞茶濮╅崗鎶芥４ */
	CLOSE = 6,
	/** UN_OPEN - 濞茶濮╅張顏勭磻閸氾拷 */
	UN_OPEN = 7,
	UNRECOGNIZED = -1
}

export function roomStatusEnumFromJSON(object: any): RoomStatusEnum {
	switch (object) {
		case 0:
		case 'WAITING':
			return RoomStatusEnum.WAITING
		case 1:
		case 'GAME_READY':
			return RoomStatusEnum.GAME_READY
		case 2:
		case 'GAME_BIG_COUNTDOWN':
			return RoomStatusEnum.GAME_BIG_COUNTDOWN
		case 3:
		case 'GAME_COUNTDOWN':
			return RoomStatusEnum.GAME_COUNTDOWN
		case 4:
		case 'GAME_START':
			return RoomStatusEnum.GAME_START
		case 5:
		case 'GAME_END':
			return RoomStatusEnum.GAME_END
		case 6:
		case 'CLOSE':
			return RoomStatusEnum.CLOSE
		case 7:
		case 'UN_OPEN':
			return RoomStatusEnum.UN_OPEN
		case -1:
		case 'UNRECOGNIZED':
		default:
			return RoomStatusEnum.UNRECOGNIZED
	}
}

export function roomStatusEnumToJSON(object: RoomStatusEnum): string {
	switch (object) {
		case RoomStatusEnum.WAITING:
			return 'WAITING'
		case RoomStatusEnum.GAME_READY:
			return 'GAME_READY'
		case RoomStatusEnum.GAME_BIG_COUNTDOWN:
			return 'GAME_BIG_COUNTDOWN'
		case RoomStatusEnum.GAME_COUNTDOWN:
			return 'GAME_COUNTDOWN'
		case RoomStatusEnum.GAME_START:
			return 'GAME_START'
		case RoomStatusEnum.GAME_END:
			return 'GAME_END'
		case RoomStatusEnum.CLOSE:
			return 'CLOSE'
		case RoomStatusEnum.UN_OPEN:
			return 'UN_OPEN'
		case RoomStatusEnum.UNRECOGNIZED:
		default:
			return 'UNRECOGNIZED'
	}
}

/**  */
export interface GameScoreReqPb {
	score: number
}

/** <p>缁粯寮挎潻锟�</p> */
export interface RoomBusinessConfigPb {
	/** 閼冲本娅欓崶鎯ф勾閸э拷 */
	bgImg: string
	/** 楠炴寧鎸遍棅鎶筋暥閸︽澘娼� */
	broadcastAudioUrl: string
	/** 瀵拷婵顕㈤棅锟� */
	startAudioUrl: string
}

/**  */
export interface RoomGameAwardPush {
	ranks: RoomUserInfoPb[]
	/**  */
	my: RoomUserInfoPb | undefined
	total: number
	awardId: string
	awardUrl: string
	awardName: string
	address: string
	maxScore: number
	awardPeopleCount: number
	emptyAward: boolean
	result: boolean
}

/**  */
export interface RoomGameEndPush {
	users: RoomUserInfoPb[]
}

/**  */
export interface RoomGamePush {
	users: RoomUserInfoPb[]
}

/** <p>缁粯寮挎潻锟�</p> */
export interface RoomMainInfoPb {
	/** 閹村潡妫縤d */
	busId: string
	/** 閹村潡妫块悩鑸碉拷锟� */
	status: RoomStatusEnum
	/** 閺勫墽銇氶惃鍕负鐎规湹淇婇幁锟� */
	userList: RoomUserInfoPb[]
	/** 娑撳绔存稉顏堟▉濞堝灚妞傞梻瀛樺煈 */
	nextStepTime: number
	/** 濞撳憡鍨欏锟介崥顖涙闂傦拷 */
	gameStartTime: number
	/** 閹姹夐弫锟� */
	totalPeople: number
	/** 閺勵垰鎯佹＃鏍ф簚 */
	firstRound: boolean
	/** 瑜版挸澧犲〒鍛婂灆閸撯晙缍戦弮鍫曟？(缁夛拷) */
	gameTime: number
	busName: string
	/** 濞茶濮╅崥宥囆� */
	activityName: string
	/** 濞茶濮㊣D */
	activityId: number
}

/**  */
export interface RoomStatusPushPb {
	/** 閹村潡妫块悩鑸碉拷锟� */
	status: RoomStatusEnum
	/** 娑撳绔存稉顏堟▉濞堝灚妞傞梻瀛樺煈 */
	nextStepTime: number
	/** 娑撳绔撮崷鍝勭磻婵妞傞梻锟� */
	nextGameStartTime: number
	/** 濞撳憡鍨欓弮鍫曟毐 */
	gameTime: number
	/** 濞茶濮╅崥宥囆� */
	activityName: string
	/** 濞茶濮㊣D */
	activityId: number
	/** 閹村潡妫块崷鐑橆偧閸烆垯绔磇d */
	roundId: string
}

/**  */
export interface RoomUserInfoPb {
	userId: string
	nickName: string
	avatarUrl: string
	score: number
	rank: number
	awardUrl: string
}

/**  */
export interface RoomUserMainPb {
	/** 閹村潡妫块悩鑸碉拷锟� */
	status: RoomStatusEnum
	/** 娑撳绔存稉顏堟▉濞堝灚妞傞梻瀛樺煈 */
	nextStepTime: number
	/** 瑜版挸澧犲〒鍛婂灆閸撯晙缍戦弮鍫曟？(缁夛拷) */
	gameTime: number
	/** 娑撳绔撮崷鐑樼埗閹村繐绱戦崥顖涙闂傚瓨妞傞梻瀛樺煈 */
	nextGameStartTime: number
	busName: string
	/** 瑜版挸澧犳禍鐑樻殶 */
	totalPeople: number
	/** 濞茶濮╅崥宥囆� */
	activityName: string
	/** 濞茶濮㊣D */
	activityId: number
}

/**  */
export interface RoomUserPushPb {
	/**  */
	userInfo: RoomUserInfoPb | undefined
	add: boolean
	total: number
}

function createBaseGameScoreReqPb(): GameScoreReqPb {
	return { score: 0 }
}

export const GameScoreReqPb = {
	encode(message: GameScoreReqPb, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
		if (message.score !== 0) {
			writer.uint32(8).int32(message.score)
		}
		return writer
	},

	decode(input: _m0.Reader | Uint8Array, length?: number): GameScoreReqPb {
		const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseGameScoreReqPb()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1:
					if (tag !== 8) {
						break
					}

					message.score = reader.int32()
					continue
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skipType(tag & 7)
		}
		return message
	},

	fromJSON(object: any): GameScoreReqPb {
		return { score: isSet(object.score) ? Number(object.score) : 0 }
	},

	toJSON(message: GameScoreReqPb): unknown {
		const obj: any = {}
		message.score !== undefined && (obj.score = Math.round(message.score))
		return obj
	},

	create<I extends Exact<DeepPartial<GameScoreReqPb>, I>>(base?: I): GameScoreReqPb {
		return GameScoreReqPb.fromPartial(base ?? {})
	},

	fromPartial<I extends Exact<DeepPartial<GameScoreReqPb>, I>>(object: I): GameScoreReqPb {
		const message = createBaseGameScoreReqPb()
		message.score = object.score ?? 0
		return message
	}
}

function createBaseRoomBusinessConfigPb(): RoomBusinessConfigPb {
	return { bgImg: '', broadcastAudioUrl: '', startAudioUrl: '' }
}

export const RoomBusinessConfigPb = {
	encode(message: RoomBusinessConfigPb, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
		if (message.bgImg !== '') {
			writer.uint32(10).string(message.bgImg)
		}
		if (message.broadcastAudioUrl !== '') {
			writer.uint32(18).string(message.broadcastAudioUrl)
		}
		if (message.startAudioUrl !== '') {
			writer.uint32(26).string(message.startAudioUrl)
		}
		return writer
	},

	decode(input: _m0.Reader | Uint8Array, length?: number): RoomBusinessConfigPb {
		const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseRoomBusinessConfigPb()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1:
					if (tag !== 10) {
						break
					}

					message.bgImg = reader.string()
					continue
				case 2:
					if (tag !== 18) {
						break
					}

					message.broadcastAudioUrl = reader.string()
					continue
				case 3:
					if (tag !== 26) {
						break
					}

					message.startAudioUrl = reader.string()
					continue
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skipType(tag & 7)
		}
		return message
	},

	fromJSON(object: any): RoomBusinessConfigPb {
		return {
			bgImg: isSet(object.bgImg) ? String(object.bgImg) : '',
			broadcastAudioUrl: isSet(object.broadcastAudioUrl)
				? String(object.broadcastAudioUrl)
				: '',
			startAudioUrl: isSet(object.startAudioUrl) ? String(object.startAudioUrl) : ''
		}
	},

	toJSON(message: RoomBusinessConfigPb): unknown {
		const obj: any = {}
		message.bgImg !== undefined && (obj.bgImg = message.bgImg)
		message.broadcastAudioUrl !== undefined &&
			(obj.broadcastAudioUrl = message.broadcastAudioUrl)
		message.startAudioUrl !== undefined && (obj.startAudioUrl = message.startAudioUrl)
		return obj
	},

	create<I extends Exact<DeepPartial<RoomBusinessConfigPb>, I>>(base?: I): RoomBusinessConfigPb {
		return RoomBusinessConfigPb.fromPartial(base ?? {})
	},

	fromPartial<I extends Exact<DeepPartial<RoomBusinessConfigPb>, I>>(
		object: I
	): RoomBusinessConfigPb {
		const message = createBaseRoomBusinessConfigPb()
		message.bgImg = object.bgImg ?? ''
		message.broadcastAudioUrl = object.broadcastAudioUrl ?? ''
		message.startAudioUrl = object.startAudioUrl ?? ''
		return message
	}
}

function createBaseRoomGameAwardPush(): RoomGameAwardPush {
	return {
		ranks: [],
		my: undefined,
		total: 0,
		awardId: '',
		awardUrl: '',
		awardName: '',
		address: '',
		maxScore: 0,
		awardPeopleCount: 0,
		emptyAward: false,
		result: false
	}
}

export const RoomGameAwardPush = {
	encode(message: RoomGameAwardPush, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
		for (const v of message.ranks) {
			RoomUserInfoPb.encode(v!, writer.uint32(10).fork()).ldelim()
		}
		if (message.my !== undefined) {
			RoomUserInfoPb.encode(message.my, writer.uint32(18).fork()).ldelim()
		}
		if (message.total !== 0) {
			writer.uint32(24).int32(message.total)
		}
		if (message.awardId !== '') {
			writer.uint32(34).string(message.awardId)
		}
		if (message.awardUrl !== '') {
			writer.uint32(42).string(message.awardUrl)
		}
		if (message.awardName !== '') {
			writer.uint32(50).string(message.awardName)
		}
		if (message.address !== '') {
			writer.uint32(58).string(message.address)
		}
		if (message.maxScore !== 0) {
			writer.uint32(64).int32(message.maxScore)
		}
		if (message.awardPeopleCount !== 0) {
			writer.uint32(72).int32(message.awardPeopleCount)
		}
		if (message.emptyAward === true) {
			writer.uint32(80).bool(message.emptyAward)
		}
		if (message.result === true) {
			writer.uint32(88).bool(message.result)
		}
		return writer
	},

	decode(input: _m0.Reader | Uint8Array, length?: number): RoomGameAwardPush {
		const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseRoomGameAwardPush()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1:
					if (tag !== 10) {
						break
					}

					message.ranks.push(RoomUserInfoPb.decode(reader, reader.uint32()))
					continue
				case 2:
					if (tag !== 18) {
						break
					}

					message.my = RoomUserInfoPb.decode(reader, reader.uint32())
					continue
				case 3:
					if (tag !== 24) {
						break
					}

					message.total = reader.int32()
					continue
				case 4:
					if (tag !== 34) {
						break
					}

					message.awardId = reader.string()
					continue
				case 5:
					if (tag !== 42) {
						break
					}

					message.awardUrl = reader.string()
					continue
				case 6:
					if (tag !== 50) {
						break
					}

					message.awardName = reader.string()
					continue
				case 7:
					if (tag !== 58) {
						break
					}

					message.address = reader.string()
					continue
				case 8:
					if (tag !== 64) {
						break
					}

					message.maxScore = reader.int32()
					continue
				case 9:
					if (tag !== 72) {
						break
					}

					message.awardPeopleCount = reader.int32()
					continue
				case 10:
					if (tag !== 80) {
						break
					}

					message.emptyAward = reader.bool()
					continue
				case 11:
					if (tag !== 88) {
						break
					}

					message.result = reader.bool()
					continue
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skipType(tag & 7)
		}
		return message
	},

	fromJSON(object: any): RoomGameAwardPush {
		return {
			ranks: Array.isArray(object?.ranks)
				? object.ranks.map((e: any) => RoomUserInfoPb.fromJSON(e))
				: [],
			my: isSet(object.my) ? RoomUserInfoPb.fromJSON(object.my) : undefined,
			total: isSet(object.total) ? Number(object.total) : 0,
			awardId: isSet(object.awardId) ? String(object.awardId) : '',
			awardUrl: isSet(object.awardUrl) ? String(object.awardUrl) : '',
			awardName: isSet(object.awardName) ? String(object.awardName) : '',
			address: isSet(object.address) ? String(object.address) : '',
			maxScore: isSet(object.maxScore) ? Number(object.maxScore) : 0,
			awardPeopleCount: isSet(object.awardPeopleCount) ? Number(object.awardPeopleCount) : 0,
			emptyAward: isSet(object.emptyAward) ? Boolean(object.emptyAward) : false,
			result: isSet(object.result) ? Boolean(object.result) : false
		}
	},

	toJSON(message: RoomGameAwardPush): unknown {
		const obj: any = {}
		if (message.ranks) {
			obj.ranks = message.ranks.map(e => (e ? RoomUserInfoPb.toJSON(e) : undefined))
		} else {
			obj.ranks = []
		}
		message.my !== undefined &&
			(obj.my = message.my ? RoomUserInfoPb.toJSON(message.my) : undefined)
		message.total !== undefined && (obj.total = Math.round(message.total))
		message.awardId !== undefined && (obj.awardId = message.awardId)
		message.awardUrl !== undefined && (obj.awardUrl = message.awardUrl)
		message.awardName !== undefined && (obj.awardName = message.awardName)
		message.address !== undefined && (obj.address = message.address)
		message.maxScore !== undefined && (obj.maxScore = Math.round(message.maxScore))
		message.awardPeopleCount !== undefined &&
			(obj.awardPeopleCount = Math.round(message.awardPeopleCount))
		message.emptyAward !== undefined && (obj.emptyAward = message.emptyAward)
		message.result !== undefined && (obj.result = message.result)
		return obj
	},

	create<I extends Exact<DeepPartial<RoomGameAwardPush>, I>>(base?: I): RoomGameAwardPush {
		return RoomGameAwardPush.fromPartial(base ?? {})
	},

	fromPartial<I extends Exact<DeepPartial<RoomGameAwardPush>, I>>(object: I): RoomGameAwardPush {
		const message = createBaseRoomGameAwardPush()
		message.ranks = object.ranks?.map(e => RoomUserInfoPb.fromPartial(e)) || []
		message.my =
			object.my !== undefined && object.my !== null
				? RoomUserInfoPb.fromPartial(object.my)
				: undefined
		message.total = object.total ?? 0
		message.awardId = object.awardId ?? ''
		message.awardUrl = object.awardUrl ?? ''
		message.awardName = object.awardName ?? ''
		message.address = object.address ?? ''
		message.maxScore = object.maxScore ?? 0
		message.awardPeopleCount = object.awardPeopleCount ?? 0
		message.emptyAward = object.emptyAward ?? false
		message.result = object.result ?? false
		return message
	}
}

function createBaseRoomGameEndPush(): RoomGameEndPush {
	return { users: [] }
}

export const RoomGameEndPush = {
	encode(message: RoomGameEndPush, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
		for (const v of message.users) {
			RoomUserInfoPb.encode(v!, writer.uint32(10).fork()).ldelim()
		}
		return writer
	},

	decode(input: _m0.Reader | Uint8Array, length?: number): RoomGameEndPush {
		const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseRoomGameEndPush()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1:
					if (tag !== 10) {
						break
					}

					message.users.push(RoomUserInfoPb.decode(reader, reader.uint32()))
					continue
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skipType(tag & 7)
		}
		return message
	},

	fromJSON(object: any): RoomGameEndPush {
		return {
			users: Array.isArray(object?.users)
				? object.users.map((e: any) => RoomUserInfoPb.fromJSON(e))
				: []
		}
	},

	toJSON(message: RoomGameEndPush): unknown {
		const obj: any = {}
		if (message.users) {
			obj.users = message.users.map(e => (e ? RoomUserInfoPb.toJSON(e) : undefined))
		} else {
			obj.users = []
		}
		return obj
	},

	create<I extends Exact<DeepPartial<RoomGameEndPush>, I>>(base?: I): RoomGameEndPush {
		return RoomGameEndPush.fromPartial(base ?? {})
	},

	fromPartial<I extends Exact<DeepPartial<RoomGameEndPush>, I>>(object: I): RoomGameEndPush {
		const message = createBaseRoomGameEndPush()
		message.users = object.users?.map(e => RoomUserInfoPb.fromPartial(e)) || []
		return message
	}
}

function createBaseRoomGamePush(): RoomGamePush {
	return { users: [] }
}

export const RoomGamePush = {
	encode(message: RoomGamePush, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
		for (const v of message.users) {
			RoomUserInfoPb.encode(v!, writer.uint32(10).fork()).ldelim()
		}
		return writer
	},

	decode(input: _m0.Reader | Uint8Array, length?: number): RoomGamePush {
		const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseRoomGamePush()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1:
					if (tag !== 10) {
						break
					}

					message.users.push(RoomUserInfoPb.decode(reader, reader.uint32()))
					continue
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skipType(tag & 7)
		}
		return message
	},

	fromJSON(object: any): RoomGamePush {
		return {
			users: Array.isArray(object?.users)
				? object.users.map((e: any) => RoomUserInfoPb.fromJSON(e))
				: []
		}
	},

	toJSON(message: RoomGamePush): unknown {
		const obj: any = {}
		if (message.users) {
			obj.users = message.users.map(e => (e ? RoomUserInfoPb.toJSON(e) : undefined))
		} else {
			obj.users = []
		}
		return obj
	},

	create<I extends Exact<DeepPartial<RoomGamePush>, I>>(base?: I): RoomGamePush {
		return RoomGamePush.fromPartial(base ?? {})
	},

	fromPartial<I extends Exact<DeepPartial<RoomGamePush>, I>>(object: I): RoomGamePush {
		const message = createBaseRoomGamePush()
		message.users = object.users?.map(e => RoomUserInfoPb.fromPartial(e)) || []
		return message
	}
}

function createBaseRoomMainInfoPb(): RoomMainInfoPb {
	return {
		busId: '',
		status: 0,
		userList: [],
		nextStepTime: 0,
		gameStartTime: 0,
		totalPeople: 0,
		firstRound: false,
		gameTime: 0,
		busName: '',
		activityName: '',
		activityId: 0
	}
}

export const RoomMainInfoPb = {
	encode(message: RoomMainInfoPb, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
		if (message.busId !== '') {
			writer.uint32(10).string(message.busId)
		}
		if (message.status !== 0) {
			writer.uint32(16).int32(message.status)
		}
		for (const v of message.userList) {
			RoomUserInfoPb.encode(v!, writer.uint32(26).fork()).ldelim()
		}
		if (message.nextStepTime !== 0) {
			writer.uint32(32).int64(message.nextStepTime)
		}
		if (message.gameStartTime !== 0) {
			writer.uint32(40).int64(message.gameStartTime)
		}
		if (message.totalPeople !== 0) {
			writer.uint32(48).int32(message.totalPeople)
		}
		if (message.firstRound === true) {
			writer.uint32(56).bool(message.firstRound)
		}
		if (message.gameTime !== 0) {
			writer.uint32(64).int64(message.gameTime)
		}
		if (message.busName !== '') {
			writer.uint32(74).string(message.busName)
		}
		if (message.activityName !== '') {
			writer.uint32(82).string(message.activityName)
		}
		if (message.activityId !== 0) {
			writer.uint32(88).int64(message.activityId)
		}
		return writer
	},

	decode(input: _m0.Reader | Uint8Array, length?: number): RoomMainInfoPb {
		const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseRoomMainInfoPb()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1:
					if (tag !== 10) {
						break
					}

					message.busId = reader.string()
					continue
				case 2:
					if (tag !== 16) {
						break
					}

					message.status = reader.int32() as any
					continue
				case 3:
					if (tag !== 26) {
						break
					}

					message.userList.push(RoomUserInfoPb.decode(reader, reader.uint32()))
					continue
				case 4:
					if (tag !== 32) {
						break
					}

					message.nextStepTime = longToNumber(reader.int64() as Long)
					continue
				case 5:
					if (tag !== 40) {
						break
					}

					message.gameStartTime = longToNumber(reader.int64() as Long)
					continue
				case 6:
					if (tag !== 48) {
						break
					}

					message.totalPeople = reader.int32()
					continue
				case 7:
					if (tag !== 56) {
						break
					}

					message.firstRound = reader.bool()
					continue
				case 8:
					if (tag !== 64) {
						break
					}

					message.gameTime = longToNumber(reader.int64() as Long)
					continue
				case 9:
					if (tag !== 74) {
						break
					}

					message.busName = reader.string()
					continue
				case 10:
					if (tag !== 82) {
						break
					}

					message.activityName = reader.string()
					continue
				case 11:
					if (tag !== 88) {
						break
					}

					message.activityId = longToNumber(reader.int64() as Long)
					continue
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skipType(tag & 7)
		}
		return message
	},

	fromJSON(object: any): RoomMainInfoPb {
		return {
			busId: isSet(object.busId) ? String(object.busId) : '',
			status: isSet(object.status) ? roomStatusEnumFromJSON(object.status) : 0,
			userList: Array.isArray(object?.userList)
				? object.userList.map((e: any) => RoomUserInfoPb.fromJSON(e))
				: [],
			nextStepTime: isSet(object.nextStepTime) ? Number(object.nextStepTime) : 0,
			gameStartTime: isSet(object.gameStartTime) ? Number(object.gameStartTime) : 0,
			totalPeople: isSet(object.totalPeople) ? Number(object.totalPeople) : 0,
			firstRound: isSet(object.firstRound) ? Boolean(object.firstRound) : false,
			gameTime: isSet(object.gameTime) ? Number(object.gameTime) : 0,
			busName: isSet(object.busName) ? String(object.busName) : '',
			activityName: isSet(object.activityName) ? String(object.activityName) : '',
			activityId: isSet(object.activityId) ? Number(object.activityId) : 0
		}
	},

	toJSON(message: RoomMainInfoPb): unknown {
		const obj: any = {}
		message.busId !== undefined && (obj.busId = message.busId)
		message.status !== undefined && (obj.status = roomStatusEnumToJSON(message.status))
		if (message.userList) {
			obj.userList = message.userList.map(e => (e ? RoomUserInfoPb.toJSON(e) : undefined))
		} else {
			obj.userList = []
		}
		message.nextStepTime !== undefined && (obj.nextStepTime = Math.round(message.nextStepTime))
		message.gameStartTime !== undefined &&
			(obj.gameStartTime = Math.round(message.gameStartTime))
		message.totalPeople !== undefined && (obj.totalPeople = Math.round(message.totalPeople))
		message.firstRound !== undefined && (obj.firstRound = message.firstRound)
		message.gameTime !== undefined && (obj.gameTime = Math.round(message.gameTime))
		message.busName !== undefined && (obj.busName = message.busName)
		message.activityName !== undefined && (obj.activityName = message.activityName)
		message.activityId !== undefined && (obj.activityId = Math.round(message.activityId))
		return obj
	},

	create<I extends Exact<DeepPartial<RoomMainInfoPb>, I>>(base?: I): RoomMainInfoPb {
		return RoomMainInfoPb.fromPartial(base ?? {})
	},

	fromPartial<I extends Exact<DeepPartial<RoomMainInfoPb>, I>>(object: I): RoomMainInfoPb {
		const message = createBaseRoomMainInfoPb()
		message.busId = object.busId ?? ''
		message.status = object.status ?? 0
		message.userList = object.userList?.map(e => RoomUserInfoPb.fromPartial(e)) || []
		message.nextStepTime = object.nextStepTime ?? 0
		message.gameStartTime = object.gameStartTime ?? 0
		message.totalPeople = object.totalPeople ?? 0
		message.firstRound = object.firstRound ?? false
		message.gameTime = object.gameTime ?? 0
		message.busName = object.busName ?? ''
		message.activityName = object.activityName ?? ''
		message.activityId = object.activityId ?? 0
		return message
	}
}

function createBaseRoomStatusPushPb(): RoomStatusPushPb {
	return {
		status: 0,
		nextStepTime: 0,
		nextGameStartTime: 0,
		gameTime: 0,
		activityName: '',
		activityId: 0,
		roundId: ''
	}
}

export const RoomStatusPushPb = {
	encode(message: RoomStatusPushPb, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
		if (message.status !== 0) {
			writer.uint32(8).int32(message.status)
		}
		if (message.nextStepTime !== 0) {
			writer.uint32(16).int64(message.nextStepTime)
		}
		if (message.nextGameStartTime !== 0) {
			writer.uint32(24).int64(message.nextGameStartTime)
		}
		if (message.gameTime !== 0) {
			writer.uint32(32).int64(message.gameTime)
		}
		if (message.activityName !== '') {
			writer.uint32(42).string(message.activityName)
		}
		if (message.activityId !== 0) {
			writer.uint32(48).int64(message.activityId)
		}
		if (message.roundId !== '') {
			writer.uint32(58).string(message.roundId)
		}
		return writer
	},

	decode(input: _m0.Reader | Uint8Array, length?: number): RoomStatusPushPb {
		const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseRoomStatusPushPb()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1:
					if (tag !== 8) {
						break
					}

					message.status = reader.int32() as any
					continue
				case 2:
					if (tag !== 16) {
						break
					}

					message.nextStepTime = longToNumber(reader.int64() as Long)
					continue
				case 3:
					if (tag !== 24) {
						break
					}

					message.nextGameStartTime = longToNumber(reader.int64() as Long)
					continue
				case 4:
					if (tag !== 32) {
						break
					}

					message.gameTime = longToNumber(reader.int64() as Long)
					continue
				case 5:
					if (tag !== 42) {
						break
					}

					message.activityName = reader.string()
					continue
				case 6:
					if (tag !== 48) {
						break
					}

					message.activityId = longToNumber(reader.int64() as Long)
					continue
				case 7:
					if (tag !== 58) {
						break
					}

					message.roundId = reader.string()
					continue
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skipType(tag & 7)
		}
		return message
	},

	fromJSON(object: any): RoomStatusPushPb {
		return {
			status: isSet(object.status) ? roomStatusEnumFromJSON(object.status) : 0,
			nextStepTime: isSet(object.nextStepTime) ? Number(object.nextStepTime) : 0,
			nextGameStartTime: isSet(object.nextGameStartTime)
				? Number(object.nextGameStartTime)
				: 0,
			gameTime: isSet(object.gameTime) ? Number(object.gameTime) : 0,
			activityName: isSet(object.activityName) ? String(object.activityName) : '',
			activityId: isSet(object.activityId) ? Number(object.activityId) : 0,
			roundId: isSet(object.roundId) ? String(object.roundId) : ''
		}
	},

	toJSON(message: RoomStatusPushPb): unknown {
		const obj: any = {}
		message.status !== undefined && (obj.status = roomStatusEnumToJSON(message.status))
		message.nextStepTime !== undefined && (obj.nextStepTime = Math.round(message.nextStepTime))
		message.nextGameStartTime !== undefined &&
			(obj.nextGameStartTime = Math.round(message.nextGameStartTime))
		message.gameTime !== undefined && (obj.gameTime = Math.round(message.gameTime))
		message.activityName !== undefined && (obj.activityName = message.activityName)
		message.activityId !== undefined && (obj.activityId = Math.round(message.activityId))
		message.roundId !== undefined && (obj.roundId = message.roundId)
		return obj
	},

	create<I extends Exact<DeepPartial<RoomStatusPushPb>, I>>(base?: I): RoomStatusPushPb {
		return RoomStatusPushPb.fromPartial(base ?? {})
	},

	fromPartial<I extends Exact<DeepPartial<RoomStatusPushPb>, I>>(object: I): RoomStatusPushPb {
		const message = createBaseRoomStatusPushPb()
		message.status = object.status ?? 0
		message.nextStepTime = object.nextStepTime ?? 0
		message.nextGameStartTime = object.nextGameStartTime ?? 0
		message.gameTime = object.gameTime ?? 0
		message.activityName = object.activityName ?? ''
		message.activityId = object.activityId ?? 0
		message.roundId = object.roundId ?? ''
		return message
	}
}

function createBaseRoomUserInfoPb(): RoomUserInfoPb {
	return { userId: '', nickName: '', avatarUrl: '', score: 0, rank: 0, awardUrl: '' }
}

export const RoomUserInfoPb = {
	encode(message: RoomUserInfoPb, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
		if (message.userId !== '') {
			writer.uint32(10).string(message.userId)
		}
		if (message.nickName !== '') {
			writer.uint32(18).string(message.nickName)
		}
		if (message.avatarUrl !== '') {
			writer.uint32(26).string(message.avatarUrl)
		}
		if (message.score !== 0) {
			writer.uint32(32).int64(message.score)
		}
		if (message.rank !== 0) {
			writer.uint32(40).int32(message.rank)
		}
		if (message.awardUrl !== '') {
			writer.uint32(50).string(message.awardUrl)
		}
		return writer
	},

	decode(input: _m0.Reader | Uint8Array, length?: number): RoomUserInfoPb {
		const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseRoomUserInfoPb()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1:
					if (tag !== 10) {
						break
					}

					message.userId = reader.string()
					continue
				case 2:
					if (tag !== 18) {
						break
					}

					message.nickName = reader.string()
					continue
				case 3:
					if (tag !== 26) {
						break
					}

					message.avatarUrl = reader.string()
					continue
				case 4:
					if (tag !== 32) {
						break
					}

					message.score = longToNumber(reader.int64() as Long)
					continue
				case 5:
					if (tag !== 40) {
						break
					}

					message.rank = reader.int32()
					continue
				case 6:
					if (tag !== 50) {
						break
					}

					message.awardUrl = reader.string()
					continue
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skipType(tag & 7)
		}
		return message
	},

	fromJSON(object: any): RoomUserInfoPb {
		return {
			userId: isSet(object.userId) ? String(object.userId) : '',
			nickName: isSet(object.nickName) ? String(object.nickName) : '',
			avatarUrl: isSet(object.avatarUrl) ? String(object.avatarUrl) : '',
			score: isSet(object.score) ? Number(object.score) : 0,
			rank: isSet(object.rank) ? Number(object.rank) : 0,
			awardUrl: isSet(object.awardUrl) ? String(object.awardUrl) : ''
		}
	},

	toJSON(message: RoomUserInfoPb): unknown {
		const obj: any = {}
		message.userId !== undefined && (obj.userId = message.userId)
		message.nickName !== undefined && (obj.nickName = message.nickName)
		message.avatarUrl !== undefined && (obj.avatarUrl = message.avatarUrl)
		message.score !== undefined && (obj.score = Math.round(message.score))
		message.rank !== undefined && (obj.rank = Math.round(message.rank))
		message.awardUrl !== undefined && (obj.awardUrl = message.awardUrl)
		return obj
	},

	create<I extends Exact<DeepPartial<RoomUserInfoPb>, I>>(base?: I): RoomUserInfoPb {
		return RoomUserInfoPb.fromPartial(base ?? {})
	},

	fromPartial<I extends Exact<DeepPartial<RoomUserInfoPb>, I>>(object: I): RoomUserInfoPb {
		const message = createBaseRoomUserInfoPb()
		message.userId = object.userId ?? ''
		message.nickName = object.nickName ?? ''
		message.avatarUrl = object.avatarUrl ?? ''
		message.score = object.score ?? 0
		message.rank = object.rank ?? 0
		message.awardUrl = object.awardUrl ?? ''
		return message
	}
}

function createBaseRoomUserMainPb(): RoomUserMainPb {
	return {
		status: 0,
		nextStepTime: 0,
		gameTime: 0,
		nextGameStartTime: 0,
		busName: '',
		totalPeople: 0,
		activityName: '',
		activityId: 0
	}
}

export const RoomUserMainPb = {
	encode(message: RoomUserMainPb, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
		if (message.status !== 0) {
			writer.uint32(8).int32(message.status)
		}
		if (message.nextStepTime !== 0) {
			writer.uint32(16).int64(message.nextStepTime)
		}
		if (message.gameTime !== 0) {
			writer.uint32(24).int64(message.gameTime)
		}
		if (message.nextGameStartTime !== 0) {
			writer.uint32(32).int64(message.nextGameStartTime)
		}
		if (message.busName !== '') {
			writer.uint32(42).string(message.busName)
		}
		if (message.totalPeople !== 0) {
			writer.uint32(48).int32(message.totalPeople)
		}
		if (message.activityName !== '') {
			writer.uint32(58).string(message.activityName)
		}
		if (message.activityId !== 0) {
			writer.uint32(64).int64(message.activityId)
		}
		return writer
	},

	decode(input: _m0.Reader | Uint8Array, length?: number): RoomUserMainPb {
		const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseRoomUserMainPb()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1:
					if (tag !== 8) {
						break
					}

					message.status = reader.int32() as any
					continue
				case 2:
					if (tag !== 16) {
						break
					}

					message.nextStepTime = longToNumber(reader.int64() as Long)
					continue
				case 3:
					if (tag !== 24) {
						break
					}

					message.gameTime = longToNumber(reader.int64() as Long)
					continue
				case 4:
					if (tag !== 32) {
						break
					}

					message.nextGameStartTime = longToNumber(reader.int64() as Long)
					continue
				case 5:
					if (tag !== 42) {
						break
					}

					message.busName = reader.string()
					continue
				case 6:
					if (tag !== 48) {
						break
					}

					message.totalPeople = reader.int32()
					continue
				case 7:
					if (tag !== 58) {
						break
					}

					message.activityName = reader.string()
					continue
				case 8:
					if (tag !== 64) {
						break
					}

					message.activityId = longToNumber(reader.int64() as Long)
					continue
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skipType(tag & 7)
		}
		return message
	},

	fromJSON(object: any): RoomUserMainPb {
		return {
			status: isSet(object.status) ? roomStatusEnumFromJSON(object.status) : 0,
			nextStepTime: isSet(object.nextStepTime) ? Number(object.nextStepTime) : 0,
			gameTime: isSet(object.gameTime) ? Number(object.gameTime) : 0,
			nextGameStartTime: isSet(object.nextGameStartTime)
				? Number(object.nextGameStartTime)
				: 0,
			busName: isSet(object.busName) ? String(object.busName) : '',
			totalPeople: isSet(object.totalPeople) ? Number(object.totalPeople) : 0,
			activityName: isSet(object.activityName) ? String(object.activityName) : '',
			activityId: isSet(object.activityId) ? Number(object.activityId) : 0
		}
	},

	toJSON(message: RoomUserMainPb): unknown {
		const obj: any = {}
		message.status !== undefined && (obj.status = roomStatusEnumToJSON(message.status))
		message.nextStepTime !== undefined && (obj.nextStepTime = Math.round(message.nextStepTime))
		message.gameTime !== undefined && (obj.gameTime = Math.round(message.gameTime))
		message.nextGameStartTime !== undefined &&
			(obj.nextGameStartTime = Math.round(message.nextGameStartTime))
		message.busName !== undefined && (obj.busName = message.busName)
		message.totalPeople !== undefined && (obj.totalPeople = Math.round(message.totalPeople))
		message.activityName !== undefined && (obj.activityName = message.activityName)
		message.activityId !== undefined && (obj.activityId = Math.round(message.activityId))
		return obj
	},

	create<I extends Exact<DeepPartial<RoomUserMainPb>, I>>(base?: I): RoomUserMainPb {
		return RoomUserMainPb.fromPartial(base ?? {})
	},

	fromPartial<I extends Exact<DeepPartial<RoomUserMainPb>, I>>(object: I): RoomUserMainPb {
		const message = createBaseRoomUserMainPb()
		message.status = object.status ?? 0
		message.nextStepTime = object.nextStepTime ?? 0
		message.gameTime = object.gameTime ?? 0
		message.nextGameStartTime = object.nextGameStartTime ?? 0
		message.busName = object.busName ?? ''
		message.totalPeople = object.totalPeople ?? 0
		message.activityName = object.activityName ?? ''
		message.activityId = object.activityId ?? 0
		return message
	}
}

function createBaseRoomUserPushPb(): RoomUserPushPb {
	return { userInfo: undefined, add: false, total: 0 }
}

export const RoomUserPushPb = {
	encode(message: RoomUserPushPb, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
		if (message.userInfo !== undefined) {
			RoomUserInfoPb.encode(message.userInfo, writer.uint32(10).fork()).ldelim()
		}
		if (message.add === true) {
			writer.uint32(16).bool(message.add)
		}
		if (message.total !== 0) {
			writer.uint32(24).int32(message.total)
		}
		return writer
	},

	decode(input: _m0.Reader | Uint8Array, length?: number): RoomUserPushPb {
		const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseRoomUserPushPb()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1:
					if (tag !== 10) {
						break
					}

					message.userInfo = RoomUserInfoPb.decode(reader, reader.uint32())
					continue
				case 2:
					if (tag !== 16) {
						break
					}

					message.add = reader.bool()
					continue
				case 3:
					if (tag !== 24) {
						break
					}

					message.total = reader.int32()
					continue
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skipType(tag & 7)
		}
		return message
	},

	fromJSON(object: any): RoomUserPushPb {
		return {
			userInfo: isSet(object.userInfo) ? RoomUserInfoPb.fromJSON(object.userInfo) : undefined,
			add: isSet(object.add) ? Boolean(object.add) : false,
			total: isSet(object.total) ? Number(object.total) : 0
		}
	},

	toJSON(message: RoomUserPushPb): unknown {
		const obj: any = {}
		message.userInfo !== undefined &&
			(obj.userInfo = message.userInfo ? RoomUserInfoPb.toJSON(message.userInfo) : undefined)
		message.add !== undefined && (obj.add = message.add)
		message.total !== undefined && (obj.total = Math.round(message.total))
		return obj
	},

	create<I extends Exact<DeepPartial<RoomUserPushPb>, I>>(base?: I): RoomUserPushPb {
		return RoomUserPushPb.fromPartial(base ?? {})
	},

	fromPartial<I extends Exact<DeepPartial<RoomUserPushPb>, I>>(object: I): RoomUserPushPb {
		const message = createBaseRoomUserPushPb()
		message.userInfo =
			object.userInfo !== undefined && object.userInfo !== null
				? RoomUserInfoPb.fromPartial(object.userInfo)
				: undefined
		message.add = object.add ?? false
		message.total = object.total ?? 0
		return message
	}
}

declare var self: any | undefined
declare var window: any | undefined
declare var global: any | undefined
var tsProtoGlobalThis: any = (() => {
	if (typeof globalThis !== 'undefined') {
		return globalThis
	}
	if (typeof self !== 'undefined') {
		return self
	}
	if (typeof window !== 'undefined') {
		return window
	}
	if (typeof global !== 'undefined') {
		return global
	}
	throw 'Unable to locate global object'
})()

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined

export type DeepPartial<T> = T extends Builtin
	? T
	: T extends Array<infer U>
	? Array<DeepPartial<U>>
	: T extends ReadonlyArray<infer U>
	? ReadonlyArray<DeepPartial<U>>
	: T extends {}
	? { [K in keyof T]?: DeepPartial<T[K]> }
	: Partial<T>

type KeysOfUnion<T> = T extends T ? keyof T : never
export type Exact<P, I extends P> = P extends Builtin
	? P
	: P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never }

function longToNumber(long: Long): number {
	if (long.gt(Number.MAX_SAFE_INTEGER)) {
		throw new tsProtoGlobalThis.Error('Value is larger than Number.MAX_SAFE_INTEGER')
	}
	return long.toNumber()
}

if (_m0.util.Long !== Long) {
	_m0.util.Long = Long as any
	_m0.configure()
}

function isSet(value: any): boolean {
	return value !== null && value !== undefined
}
