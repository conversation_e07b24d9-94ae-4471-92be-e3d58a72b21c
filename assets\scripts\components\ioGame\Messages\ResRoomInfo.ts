import { RoomUserMainPb } from '../../../protos/room'
import BroadcastCtrl from '../BroadcastCtrl'
import { MessageCmdEvent, MessageRoomSubCmd } from '../MessageEvent'
import { _decorator } from 'cc'
const { ccclass } = _decorator

/**
 * 房间信息
 */
@ccclass()
export default class ResRoomInfo extends BroadcastCtrl {
	////////////////////消息单例////////////////////
	public static get inst() {
		return this.getInst(ResRoomInfo)
	}

	constructor() {
		super()
		this.cmd = MessageCmdEvent.roomCmd
		this.subCmd = MessageRoomSubCmd.roomUserMainPush
		this.addListen()
	}

	receive(code: number, data: any) {
		super.receive(code, RoomUserMainPb.decode(data))
	}
}
