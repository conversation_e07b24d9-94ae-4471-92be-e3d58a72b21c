import { _decorator, Button, Component, instantiate, Label, Node, Prefab } from 'cc';
import { ViewBase } from '../../scripts/libs/manager/ViewBase';
import { xcore } from '../../scripts/libs/xcore';
import { C_View, E_EVENT } from '../../scripts/ConstGlobal';
import ScrollViewProCom from '../../scripts/libs/utils/ScrollViewProCom';
import { UnitRoundRank } from './Unit/UnitRoundRank';
import { UnitTopRank } from './Unit/UnitTopRank';
import TimeUtil from '../../scripts/libs/utils/TimeUtil';
const { ccclass, property } = _decorator;

@ccclass('ViewFightRank')
export class ViewFightRank extends ViewBase {


    @property(Button)
    private btnWorldRank: Button = null;

    @property(Button)
    private btNextRound: Button = null

    @property(Label)
    private lbTime: Label = null;

    @property(Node)
    private ndTopContent: Node = null;

    @property(ScrollViewProCom)
    private svRankContent: ScrollViewProCom = null;

    @property(Prefab)
    private pfbUnitTopRank: Prefab = null;

    private _time: number = 60;

    start() {

        this.addButtonEvent(this.btNextRound, async () => {
            await xcore.ui.closeAllView();
            //xcore.event.raiseEvent(E_EVENT.GameConfig);
            xcore.event.raiseEvent(E_EVENT.GameReplay);

        }, this)

        this.addButtonEvent(this.btnWorldRank, () => {
            xcore.ui.addView(C_View.ViewRank);
        }, this)

    }

    public setData(data: any): void {
        /*  userList,
         giftList,
         scoreList,
  
         oldWeekData
         weekRankDatas
         */


        //顶部前三名
        for (let i = 0; i < 3; i++) {
            let nd = instantiate(this.pfbUnitTopRank);
            let scoredata = data.scoreList[i];
            if (scoredata) {
                let user = data.userList.find(a => a.userId == scoredata.userId);
                nd.parent = this.ndTopContent;
                let oldWeekRank = data.oldWeekDatas ? data.oldWeekDatas[user.userId] : -1;
                let weekRank = data.weekRankDatas ? data.weekRankDatas[user.userId] : -1;
                nd.getComponent(UnitTopRank).setData(data.scoreList[i], user.nickName, user.iconUrl, i, user.upscore, data.maxLevel, oldWeekRank, weekRank);
            }
        }

        this.svRankContent.setView(data.scoreList, (n: Node, scoredata: any, index: number) => {
            let user = data.userList.find(a => a.userId == scoredata.userId);
            let oldWeekRank = data.oldWeekDatas ? data.oldWeekDatas[user.userId] : -1;
            let weekRank = data.weekRankDatas ? data.weekRankDatas[user.userId] : -1;
            n.getComponent(UnitRoundRank).setData(scoredata, user.nickName, user.iconUrl, index, data.maxLevel, oldWeekRank, weekRank, user.upscore);
        });

        //console.log('data:', data)

    }

    update(deltaTime: number) {
        if (this._time > 0) {
            this._time -= deltaTime;
            this.lbTime.string = TimeUtil.formatTime(Math.floor(this._time) * 1000, 2);
            if (this._time <= 0) {
                xcore.event.raiseEvent(E_EVENT.GameReplay);
                this.closeSelf();
            }
        }
    }
}


