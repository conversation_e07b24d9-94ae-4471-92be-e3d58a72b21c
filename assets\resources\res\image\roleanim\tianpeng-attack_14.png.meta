{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "0bc8df51-fc65-4909-8948-531240bfc0ab", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "0bc8df51-fc65-4909-8948-531240bfc0ab@6c48a", "displayName": "tianpeng-attack_14", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "0bc8df51-fc65-4909-8948-531240bfc0ab", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "0bc8df51-fc65-4909-8948-531240bfc0ab@f9941", "displayName": "tianpeng-attack_14", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": -11.5, "offsetY": -24, "trimX": 48, "trimY": 59, "width": 131, "height": 130, "rawWidth": 250, "rawHeight": 200, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-65.5, -65, 0, 65.5, -65, 0, -65.5, 65, 0, 65.5, 65, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [48, 141, 179, 141, 48, 11, 179, 11], "nuv": [0.192, 0.055, 0.716, 0.055, 0.192, 0.705, 0.716, 0.705], "minPos": [-65.5, -65, 0], "maxPos": [65.5, 65, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "0bc8df51-fc65-4909-8948-531240bfc0ab@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "0bc8df51-fc65-4909-8948-531240bfc0ab@6c48a"}}