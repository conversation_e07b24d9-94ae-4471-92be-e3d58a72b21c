{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "15f9f0e3-0d87-40ac-96c3-d0dd32915585", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "15f9f0e3-0d87-40ac-96c3-d0dd32915585@6c48a", "displayName": "tianpeng-attack_07", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "15f9f0e3-0d87-40ac-96c3-d0dd32915585", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "15f9f0e3-0d87-40ac-96c3-d0dd32915585@f9941", "displayName": "tianpeng-attack_07", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": -34.5, "offsetY": 0.5, "trimX": 19, "trimY": 23, "width": 143, "height": 153, "rawWidth": 250, "rawHeight": 200, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-71.5, -76.5, 0, 71.5, -76.5, 0, -71.5, 76.5, 0, 71.5, 76.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [19, 177, 162, 177, 19, 24, 162, 24], "nuv": [0.076, 0.12, 0.648, 0.12, 0.076, 0.885, 0.648, 0.885], "minPos": [-71.5, -76.5, 0], "maxPos": [71.5, 76.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "15f9f0e3-0d87-40ac-96c3-d0dd32915585@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "15f9f0e3-0d87-40ac-96c3-d0dd32915585@6c48a"}}