import { _decorator, Component, easing, Label, Node, size, sp, Sprite, Tween, Vec3 } from 'cc';
import { GiftMessageMgr, IGiftMessage } from '../../scripts/GiftMessageMgr';
import { C_Bundle, E_SkillType } from 'db://assets/scripts/ConstGlobal';
import { xcore } from 'db://assets/scripts/libs/xcore';
const { ccclass, property } = _decorator;

@ccclass('UnitSkillUpMessage')
export class UnitSkillUpMessage extends Component {

    @property(sp.Skeleton)
    private anim: sp.Skeleton = null;

    @property(Sprite)
    private sprAvatar: Sprite = null;

    @property(Label)
    private lbName: Label = null;

    @property(Label)
    private lbDesc: Label = null;

    private _scaleTw: Tween
    private _tempScale: Vec3 = new Vec3(0, 0, 0)

    protected onLoad(): void {
        this.node.on(Node.EventType.TOUCH_END, () => {
            this.kill();
        }, this)
    }

    protected onEnable(): void {
        if (!this._scaleTw) {
            this._scaleTw = new Tween(this.node)

                .to(0.2, { scale: new Vec3(1, 1, 1) }, { easing: "backOut" })
        }
        this.node.scale = this._tempScale;
        this._scaleTw.start()
    }
    async setData(data) {

        let message = data.message as IGiftMessage;

        let txt
        let animPath
        switch (message.skillType) {
            case E_SkillType.GreatFire:
                txt = '朱雀扇'
                animPath = './res/anim/skilleffect/zhuque';
                break;
            case E_SkillType.GreatLightning:
                txt = '五雷钉'
                animPath = './res/anim/skilleffect/leilingzhu';
                break;
            case E_SkillType.GreatRock:
                txt = '混元伞'
                animPath = './res/anim/skilleffect/hunyuansan';
                break;
            case E_SkillType.GreatSkyRock:
                txt = '女娲石'
                animPath = './res/anim/skilleffect/lvwashi';
                break;
            case E_SkillType.GreatFires:
                txt = '神火炉';
                animPath = './res/anim/skilleffect/bagualu';
                break;

            default:
                break;
        }
        if (message.lev == 1) {
            txt = `获得法宝` + txt;
        } else {
            txt = txt + `升级到等级${message.lev}`;
        }

        this.lbDesc.string = txt;
        this.lbName.string = message.name || '匿名用户';
        if (message.avatar) {
            xcore.res.remoteLoadSprite(message.avatar, this.sprAvatar, size(100, 100))
        }
        this.anim.skeletonData = null;
        await xcore.res.bundleLoadSpine(C_Bundle.abGame, animPath, this.anim);
        this.anim.setAnimation(0, 'animation');
        this.scheduleOnce(() => {
            this.kill();
        }, 1.8)
    }



    kill() {
        this.node.removeFromParent();
        GiftMessageMgr.getInstance().killSkillMessage(this);
    }
}


