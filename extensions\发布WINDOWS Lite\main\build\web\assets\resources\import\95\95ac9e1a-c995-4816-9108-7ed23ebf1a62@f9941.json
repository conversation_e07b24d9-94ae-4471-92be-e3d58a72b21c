[1, ["95rJ4ayZVIFpEIftI+vxpi@6c48a"], ["_textureSource"], ["cc.SpriteFrame"], 0, [{"name": "huaxian_suipian", "rect": {"x": 0, "y": 0, "width": 96, "height": 96}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 96, "height": 96}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-48, -48, 0, 48, -48, 0, -48, 48, 0, 48, 48, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 96, 96, 96, 0, 0, 96, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -48, "y": -48, "z": 0}, "maxPos": {"x": 48, "y": 48, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]]