{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "2ad02ea8-b937-49c1-abc5-819f725f471f", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "2ad02ea8-b937-49c1-abc5-819f725f471f@6c48a", "displayName": "xiaolonglv-attack_04", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "2ad02ea8-b937-49c1-abc5-819f725f471f", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "2ad02ea8-b937-49c1-abc5-819f725f471f@f9941", "displayName": "xiaolonglv-attack_04", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": -26.5, "offsetY": -9, "trimX": 18, "trimY": 35, "width": 111, "height": 148, "rawWidth": 200, "rawHeight": 200, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-55.5, -74, 0, 55.5, -74, 0, -55.5, 74, 0, 55.5, 74, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [18, 165, 129, 165, 18, 17, 129, 17], "nuv": [0.09, 0.085, 0.645, 0.085, 0.09, 0.825, 0.645, 0.825], "minPos": [-55.5, -74, 0], "maxPos": [55.5, 74, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "2ad02ea8-b937-49c1-abc5-819f725f471f@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "2ad02ea8-b937-49c1-abc5-819f725f471f@6c48a"}}