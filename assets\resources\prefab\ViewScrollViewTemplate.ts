import { _decorator, Button, Component, Label,  math, Node } from 'cc';
 
import { ViewBase } from '../../scripts/libs/manager/ViewBase';
import ScrollViewProCom from '../../scripts/libs/utils/ScrollViewProCom';
 

const { ccclass, property } = _decorator;

@ccclass('ViewScrollViewTemplate')
export class ViewScrollViewTemplate extends ViewBase {





    @property({ type: ScrollViewProCom, displayName: "垂直布局列表" })
    public scrollviewV: ScrollViewProCom = null;

    @property({ type: ScrollViewProCom, displayName: "横向布局列表" })
    public scrollviewH: ScrollViewProCom = null;

    @property({ type: ScrollViewProCom, displayName: "网格布局列表" })
    public scrollviewG: ScrollViewProCom = null;

    @property({ type: ScrollViewProCom, displayName: "复合布局列表" })
    public scrollviewM: ScrollViewProCom = null;

    start() {
        this.refreshSv();
    }

    async refreshSv() {
        let datas = [];
        for (let i: number = 0; i < 1000; i++) {
            datas.push([1, 2, 3, 4, 5, 6]);
        }
        this.scrollviewV.setView(datas, (n: Node, data: number[], index: number) => {

        });


    }

}


