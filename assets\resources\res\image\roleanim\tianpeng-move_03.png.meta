{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "475f085e-59c3-49f5-bc00-09cf711476c5", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "475f085e-59c3-49f5-bc00-09cf711476c5@6c48a", "displayName": "tianpeng-move_03", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "475f085e-59c3-49f5-bc00-09cf711476c5", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "475f085e-59c3-49f5-bc00-09cf711476c5@f9941", "displayName": "tianpeng-move_03", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": -4, "offsetY": -11, "trimX": 61, "trimY": 42, "width": 120, "height": 138, "rawWidth": 250, "rawHeight": 200, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-60, -69, 0, 60, -69, 0, -60, 69, 0, 60, 69, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [61, 158, 181, 158, 61, 20, 181, 20], "nuv": [0.244, 0.1, 0.724, 0.1, 0.244, 0.79, 0.724, 0.79], "minPos": [-60, -69, 0], "maxPos": [60, 69, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "475f085e-59c3-49f5-bc00-09cf711476c5@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "475f085e-59c3-49f5-bc00-09cf711476c5@6c48a"}}