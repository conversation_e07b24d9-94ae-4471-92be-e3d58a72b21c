{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "aa4a2c0a-4f11-47cd-a210-0dba9bd0f8fd", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "aa4a2c0a-4f11-47cd-a210-0dba9bd0f8fd@6c48a", "displayName": "tianpeng-attack_02", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "aa4a2c0a-4f11-47cd-a210-0dba9bd0f8fd", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "aa4a2c0a-4f11-47cd-a210-0dba9bd0f8fd@f9941", "displayName": "tianpeng-attack_02", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": -19, "offsetY": -27.5, "trimX": 24, "trimY": 64, "width": 164, "height": 127, "rawWidth": 250, "rawHeight": 200, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-82, -63.5, 0, 82, -63.5, 0, -82, 63.5, 0, 82, 63.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [24, 136, 188, 136, 24, 9, 188, 9], "nuv": [0.096, 0.045, 0.752, 0.045, 0.096, 0.68, 0.752, 0.68], "minPos": [-82, -63.5, 0], "maxPos": [82, 63.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "aa4a2c0a-4f11-47cd-a210-0dba9bd0f8fd@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "aa4a2c0a-4f11-47cd-a210-0dba9bd0f8fd@6c48a"}}