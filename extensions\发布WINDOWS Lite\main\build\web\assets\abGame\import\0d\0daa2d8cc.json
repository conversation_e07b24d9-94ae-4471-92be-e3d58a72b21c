[1, ["52fHu7D8hGm5vLaoALoXCl", "f9EM8XfwZHmqPrjqhH3Hda@f9941", "6e4e2/Z5BMYr7bCWpDsEDn@f9941", "4bbyTqTcNPnrmVLDfyjIrt@f9941", "46IdxunHlHYYawWmS60uVs@f9941", "c5ZxzU1upA9Zzx9f9fmaDg", "2c8hCyiEhL3KBAMa5+iwT7@f9941", "ff3reVIcZLdo1ocsK9Lga3@f9941", "9bBYHzhH5F25cHFMhUL3oq@f9941", "67E/Vobn1A7ImerLBhca6R@f9941", "47IDFzJNxCyJxUQYoBTRK9", "f9EM8XfwZHmqPrjqhH3Hda@6c48a"], ["node", "_spriteFrame", "_font", "root", "ndContent", "data", "btnClose2", "lbBtnTxt", "lbDesc", "btnClose", "sprFrame", "ndRoot", "_parent", "pfbUnitSkinDebrisReward", "spr<PERSON><PERSON><PERSON>", "lbName", "lbRank", "ndDetail", "pfbUnitSkinDebris", "_textureSource"], [["cc.Node", ["_name", "_layer", "_components", "_prefab", "_children", "_parent", "_lpos"], 1, 9, 4, 2, 1, 5], ["cc.Node", ["_name", "_layer", "_parent", "_components", "_prefab", "_lpos", "_children", "_lscale"], 1, 1, 12, 4, 5, 2, 5], ["cc.Sprite", ["_type", "_sizeMode", "node", "__prefab", "_spriteFrame"], 1, 1, 4, 6], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_lineHeight", "_isSystemFontUsed", "_horizontalAlign", "_overflow", "_enableOutline", "_outlineWidth", "node", "__prefab", "_color", "_outlineColor"], -6, 1, 4, 5, 5], ["cc.Mask", ["_type", "node", "__prefab"], 2, 1, 4], ["cc.Layout", ["_resizeMode", "_layoutType", "_spacingX", "_spacingY", "_constraintNum", "node", "__prefab"], -2, 1, 4], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["4e8c8c0h/NFoa50ZgeyZjaZ", ["node", "__prefab", "ndRoot", "sprFrame", "btnClose", "lbDesc", "lbBtnTxt", "ndContent", "btnClose2", "pfbUnitSkinDebrisReward"], 3, 1, 4, 1, 1, 1, 1, 1, 1, 1, 6], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node", "__prefab"], 0, 1, 4], ["cc.Graphics", ["node", "__prefab", "_fillColor"], 3, 1, 4, 5], ["<PERSON><PERSON>", ["horizontal", "node", "__prefab", "_content"], 2, 1, 4, 1], ["cc.<PERSON><PERSON>", ["_transition", "_zoomScale", "node", "__prefab", "_normalColor", "_target"], 1, 1, 4, 5, 1], ["361a5OqKkFC6ZE8qgkCWBtp", ["node", "__prefab", "ndDetail", "lbRank", "lbName", "spr<PERSON><PERSON><PERSON>", "ndContent", "pfbUnitSkinDebris"], 3, 1, 4, 1, 1, 1, 1, 1, 6]], [[9, 0, 2], [11, 0, 1, 2, 3, 4, 5, 5], [3, 0, 1, 2, 1], [0, 0, 1, 5, 2, 3, 6, 3], [1, 0, 1, 2, 3, 4, 5, 7, 3], [3, 0, 1, 2, 3, 1], [2, 2, 3, 4, 1], [3, 0, 1, 1], [8, 0, 2], [0, 0, 1, 4, 2, 3, 3], [0, 0, 1, 5, 4, 2, 3, 3], [0, 0, 1, 5, 4, 2, 3, 6, 3], [1, 0, 1, 2, 3, 4, 5, 3], [2, 0, 2, 3, 4, 2], [2, 1, 2, 3, 4, 2], [2, 2, 3, 1], [13, 0, 1, 2, 1], [4, 0, 1, 2, 3, 4, 9, 10, 11, 6], [15, 0, 1, 2, 3, 4, 5, 3], [0, 0, 1, 5, 2, 3, 3], [0, 0, 1, 4, 2, 3, 6, 3], [1, 0, 1, 2, 6, 3, 4, 5, 3], [1, 0, 1, 2, 3, 4, 3], [10, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 1], [5, 1, 2, 1], [5, 0, 1, 2, 2], [12, 0, 1, 2, 3, 4, 4], [6, 0, 1, 2, 3, 4, 5, 6, 6], [6, 0, 1, 2, 5, 6, 4], [14, 0, 1, 2, 3, 2], [4, 0, 5, 1, 2, 3, 6, 9, 10, 11, 7], [4, 0, 1, 2, 3, 4, 7, 8, 9, 10, 12, 8], [16, 0, 1, 2, 3, 4, 5, 6, 7, 1]], [[[[8, "ViewSkinDebrisReward"], [9, "ViewSkinDebrisReward", 33554432, [-11], [[7, -2, [0, "edauGuXFxJQKFhHYNz/2YE"]], [23, -10, [0, "b9Dp7vRotN/pjmsFFdGgA/"], -9, -8, -7, -6, -5, -4, -3, 4]], [1, "99JjVKTfxM+6BwyDOQJXdm", null, null, null, -1, 0]], [10, "ndCore", 33554432, 1, [-13, -14, -15, -16, -17, -18, -19], [[7, -12, [0, "a7vhTylXVFeYHm/n4xJVD9"]]], [1, "e5R2eO6YlPs6Uw8LCK3SDj", null, null, null, 1, 0]], [21, "btnClose", 33554432, 2, [-23], [[[2, -20, [0, "3cXQRv1D9JNYHschGuLbSJ"], [5, 301, 92]], [13, 1, -21, [0, "2d63qxk/VPwIo74+vrd/k/"], 2], -22], 4, 4, 1], [1, "c11YS52hhBQ5FX+qfgTdk5", null, null, null, 1, 0], [1, 0, -707.828, 0]], [9, "view", 33554432, [-28], [[5, -24, [0, "216SSCbUZFX4zsx2FpKKxV"], [5, 750, 760], [0, 0.5, 1]], [24, -25, [0, "73qAhyd3VCCq8AYVXLBV/7"]], [26, 45, 240, 250, -26, [0, "3a/l01VxVA3bsLgHvLGing"]], [16, -27, [0, "31Jl2eUz1N+ZV4A6NwpHfl"], [4, 16777215]]], [1, "0aoBX+cGpIeJx8jHiUybwX", null, null, null, 1, 0]], [19, "content", 33554432, 4, [[5, -29, [0, "beiWsieCNMk4MxW4oRO8Cy"], [5, 750, -17], [0, 0.5, 1]], [27, 1, 2, 50, 17, -1.8, -30, [0, "ae3Re/MktFHZsgmsFsr5y9"]]], [1, "1dn4atCfBMoJxdONhhWWnU", null, null, null, 1, 0]], [12, "btnClose-001", 33554432, 2, [[[2, -31, [0, "ecGL43XuVBKZiw6VnLU6Jo"], [5, 112, 113]], [13, 1, -32, [0, "40QduAB1hKtpZ4ovxHlLal"], 3], -33], 4, 4, 1], [1, "d3Oesh2HZJ7KCSvC9cY6jk", null, null, null, 1, 0], [1, 374.19899999999996, 613.4749999999999, 0]], [11, "svV", 33554432, 2, [4], [[5, -34, [0, "fd5EgkSkJEd4uWNtFaRioH"], [5, 750, 760], [0, 0.5, 1]], [29, false, -35, [0, "15Rvo/yk5HEJ7hVfBTQ1x5"], 5]], [1, "f3UShx76VM347G3u+QQCVL", null, null, null, 1, 0], [1, 0, 162.64499999999998, 0]], [12, "Sprite", 33554432, 2, [[[2, -36, [0, "ddwBTJiEBP5KE9WlAuGoOe"], [5, 40, 36]], -37], 4, 1], [1, "45TJWnCHpMFIdhfhd8n26u", null, null, null, 1, 0], [1, 0, 111.62699999999995, 0]], [3, "game_title_viewskindebrisreward", 33554432, 2, [[2, -38, [0, "b06uQbY/1Br5V8AA2Lm7I2"], [5, 843, 315]], [6, -39, [0, "93YUe0mWxD/o8l2C46ewBG"], 0]], [1, "67Boc4W0FPbav9pfwop2Yh", null, null, null, 1, 0], [1, 0, 625.7370000000001, 0]], [3, "common_frame_01", 33554432, 2, [[2, -40, [0, "41HwhbvXpBCZgJ/PysR5mG"], [5, 800, 323]], [14, 0, -41, [0, "6b4buw/bdAl4PeQapEokj9"], 1]], [1, "b5dBABpD1JKbcPFPjJCLKF", null, null, null, 1, 0], [1, 0, 339.6310000000001, 0]], [4, "lbDesc", 33554432, 2, [[[5, -42, [0, "48tSKw381LcYtK8ivHGGnt"], [5, 1400, 375.59999999999997], [0, 0.5, 1]], -43], 4, 1], [1, "efx+HdnEdGALwqaLMAD1ZG", null, null, null, 1, 0], [1, 0, 470.028, 0], [1, 0.5, 0.5, 1]], [4, "Label", 33554432, 3, [[[2, -44, [0, "d9K2XYin9JtYz4FRIJQKAq"], [5, 434.219970703125, 163.2]], -45], 4, 1], [1, "93Xk4+b55Ct7u584O/vprV", null, null, null, 1, 0], [1, 0, 2.9990000000000236, 0], [1, 0.5, 0.5, 1]], [15, 8, [0, "70qvp5o9hAD47z04kr3uHh"]], [30, "抽取规则：\n 1.XXXXXXXXXXXXXXXXXXXXXXXXXXX\n2.XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX\n3.XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX\n蓝色碎片：1111\n绿色碎片：222", 0, 48, 48, 60, 3, 11, [0, "ba+2lOKGVPxbpqVFSqPfes"], [4, 4281023312]], [31, "继续关卡（10）", 60, 60, 120, false, true, 6, 12, [0, "46oVerfetIPoCDzQrObVUX"], [4, 4280505736]], [18, 3, 1.02, 3, [0, "e06ziBcPFDGZKOd3JkUpQK"], [4, 4292269782], 3], [18, 3, 1.02, 6, [0, "c6INbRJK1KOIgOmpL7LV7s"], [4, 4292269782], 6]], 0, [0, 3, 1, 0, 0, 1, 0, 6, 17, 0, 4, 5, 0, 7, 15, 0, 8, 14, 0, 9, 16, 0, 10, 13, 0, 11, 2, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, -1, 8, 0, -2, 9, 0, -3, 10, 0, -4, 11, 0, -5, 3, 0, -6, 7, 0, -7, 6, 0, 0, 3, 0, 0, 3, 0, -3, 16, 0, -1, 12, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, -1, 5, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, -3, 17, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, -2, 13, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, -2, 14, 0, 0, 12, 0, -2, 15, 0, 5, 1, 4, 12, 7, 45], [0, 0, 0, 0, 0, 15], [1, 1, 1, 1, 13, 2], [1, 2, 3, 4, 5, 0]], [[[8, "UnitSkinDebrisReward"], [20, "UnitSkinDebrisReward", 33554432, [-10], [[2, -2, [0, "59Heci9sFBP6tDvkf10yKV"], [5, 800, 144]], [14, 0, -3, [0, "23xd+Qw9tFA5lc8VsMJH0R"], 3], [32, -9, [0, "2cxgLr7qdOIL8yO5zRG8QH"], -8, -7, -6, -5, -4, 4]], [1, "depTEA+vxAAKnkbUyTGF2Y", null, null, null, -1, 0], [1, 0, -13.780999999999949, 0]], [10, "ndDetail", 33554432, 1, [-12, -13, -14, -15, -16, -17, -18], [[7, -11, [0, "d2absONANHbZ7vxfiSWu3x"]]], [1, "15pKEj0hdKZ6yH80yY2WfK", null, null, null, 1, 0]], [11, "Node-002", 33554432, 2, [-22], [[2, -19, [0, "fePiDCWYBCyqy9MtDyHIsH"], [5, 70, 70]], [25, 1, -20, [0, "70buHVohFHGI/501fS7vDp"]], [16, -21, [0, "af3MfQ6KBN27ipG4ILUfSU"], [4, 16777215]]], [1, "4fSiw1Ry5IUY08y9PkSQSa", null, null, null, 1, 0], [1, -225.596, 20.533999999999992, 0]], [3, "Node", 33554432, 2, [[2, -23, [0, "8f6yqR5YJBT7p7Gn7YquII"], [5, -40, 100]], [28, 1, 1, 40, -24, [0, "50UsTVlHhORZSmwEhkubhg"]]], [1, "13whA/VydFxrzFhxSsUOKW", null, null, null, 1, 0], [1, 184.64499999999998, 4.433999999999969, 0]], [3, "game_rankindex_03", 33554432, 2, [[2, -25, [0, "cb3/Ys4MRJBILepl/+529t"], [5, 61, 61]], [6, -26, [0, "7eNTZj4mpC9qtkifHtCpYi"], 0]], [1, "68EPFrpp1BQrDe+8Ih2hSI", null, null, null, 1, 0], [1, -339.349, 18.844999999999914, 0]], [3, "sprAFrame", 33554432, 2, [[2, -27, [0, "33SHX3/GRDg6u5VKGfK9PZ"], [5, 70, 70]], [6, -28, [0, "ecyoLeckJHJYFVrklUImbz"], 1]], [1, "e1Sq2Qko1JIaoSHg1Eir3o", null, null, null, 1, 0], [1, -225.596, 19.668999999999983, 0]], [22, "spr<PERSON><PERSON><PERSON>", 33554432, 3, [[[2, -29, [0, "84FZZoxF1IQ7b699JvQcYh"], [5, 99, 100]], -30], 4, 1], [1, "33Fu2DFapH4p6DSxf4RKHD", null, null, null, 1, 0]], [3, "sprMask", 33554432, 2, [[2, -31, [0, "c2wHv2IpdAjpjE4XkNST3D"], [5, 70, 70]], [6, -32, [0, "7dOzQfsoBJ6qFAOcEOkVeS"], 2]], [1, "386X1CRddL+a46YXk/NDRN", null, null, null, 1, 0], [1, -225.596, 20.533999999999992, 0]], [4, "lbRank", 33554432, 2, [[[2, -33, [0, "c9NV9K/qVDkbvEvKpjxByt"], [5, 66.239990234375, 100.8]], -34], 4, 1], [1, "6dee2fKcRK5b10DteLiNEU", null, null, null, 1, 0], [1, -339.624, 21.420999999999935, 0], [1, 0.5, 0.5, 1]], [4, "lbName", 33554432, 2, [[[2, -35, [0, "f0kNatQuRH0L4eh+jZXPCp"], [5, 240, 100.8]], -36], 4, 1], [1, "24e5hfQ7RAdY8GKqf/9IfX", null, null, null, 1, 0], [1, -225.596, -38.22000000000003, 0], [1, 0.5, 0.5, 1]], [15, 7, [0, "54SzxDKU1KDbS4WCyH4uNS"]], [17, "--", 72, 72, 80, false, 9, [0, "04UvqdHnpCYKjW/kLN84D4"], [4, 4292538105]], [17, "用户名名称", 48, 48, 80, false, 10, [0, "1fo41A+btHIZUCNPaPonE1"], [4, 4279772212]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, 4, 4, 0, 14, 11, 0, 15, 13, 0, 16, 12, 0, 17, 2, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, -1, 5, 0, -2, 6, 0, -3, 3, 0, -4, 8, 0, -5, 9, 0, -6, 10, 0, -7, 4, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -1, 7, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, -2, 11, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, -2, 12, 0, 0, 10, 0, -2, 13, 0, 5, 1, 36], [0, 0, 0, 0, 0, 12, 13], [1, 1, 1, 1, 18, 2, 2], [6, 7, 8, 9, 10, 0, 0]], [[{"name": "game_title_viewskindebrisreward", "rect": {"x": 2, "y": 0, "width": 843, "height": 315}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 847, "height": 315}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-421.5, -157.5, 0, 421.5, -157.5, 0, -421.5, 157.5, 0, 421.5, 157.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [2, 315, 845, 315, 2, 0, 845, 0], "nuv": [0.0023612750885478157, 0, 0.9976387249114522, 0, 0.0023612750885478157, 1, 0.9976387249114522, 1], "minPos": {"x": -421.5, "y": -157.5, "z": 0}, "maxPos": {"x": 421.5, "y": 157.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [7], 0, [0], [19], [11]]]]