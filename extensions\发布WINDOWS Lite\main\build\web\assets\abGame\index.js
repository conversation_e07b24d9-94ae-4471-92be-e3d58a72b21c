System.register("chunks:///_virtual/abGame",["./Game.ts","./UIGiftDesc.ts","./UnitBossMessage.ts","./UnitBuffCoin.ts","./UnitCloud.ts","./UnitDimond.ts","./UnitDimondDesc.ts","./UnitDimondReward.ts","./UnitExchange.ts","./UnitGiftDetail.ts","./UnitGiftMessage.ts","./UnitHurtTips.ts","./UnitItemScore.ts","./UnitLighting.ts","./UnitLighting2.ts","./UnitNewsEffect.ts","./UnitPowerRank.ts","./UnitRank.ts","./UnitRoleComp.ts","./UnitRoundRank.ts","./UnitSelectGameLevel.ts","./UnitSelectGameTime.ts","./UnitSelectGameType.ts","./UnitSelectSkin.ts","./UnitSkillComp.ts","./UnitSkillEffect.ts","./UnitSkillTips.ts","./UnitSkillUpMessage.ts","./UnitSkinDebris.ts","./UnitSkinDebrisReward.ts","./UnitSkinRewardMessage.ts","./UnitSkinShow.ts","./UnitSkinUser.ts","./UnitTaskRewardMessage.ts","./UnitTopRank.ts","./UnitTowerPoint.ts","./UnitTowerPointShowMessage.ts","./UnitUserInfoMessage.ts","./ViewCommonTips.ts","./ViewCrossRewardDesc.ts","./ViewDimondReward.ts","./ViewExchange.ts","./ViewExchangeShow.ts","./ViewFightRank.ts","./ViewGameOver.ts","./ViewOpenBox.ts","./ViewPowerRank.ts","./ViewRank.ts","./ViewRoundToast.ts","./ViewSelectGameLevel.ts","./ViewSelectGameMode.ts","./ViewSelectGameType.ts","./ViewSetting.ts","./ViewSkillUpShow.ts","./ViewSkinDebrisReward.ts","./ViewSkinDetail.ts","./ViewSkinReward.ts","./ViewSkinSelect.ts","./ViewSkinShow.ts","./ViewTaskRewardDesc.ts","./ViewTestGift.ts","./ViewTowerLevUp.ts","./ViewUserInfo.ts","./ViewWingShow.ts","./Buff.ts","./EffectMgr.ts","./FightMgr.ts","./GiftMessageMgr.ts","./GiftMgr.ts","./LightningChain.ts","./Role.ts","./RoleData.ts","./Skill.ts"],(function(){return{setters:[null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null],execute:function(){}}}));

System.register("chunks:///_virtual/Buff.ts",["cc","./ConstGlobal.ts","./FightMgr.ts","./ConfigHelper.ts"],(function(e){var t,i,s,c;return{setters:[function(e){t=e.cclegacy},function(e){i=e.E_BuffType},function(e){s=e.FightMgr},function(e){c=e.ConfigHelper}],execute:function(){t._RF.push({},"4ba3b+sPcdJBYXXKaYDgeLW","Buff",void 0);e("Buff",function(){function e(){this.type=void 0,this.effectTime=null,this.effectEachTime=null,this.effectNum=null,this.effectIds=[],this.hurtNum=null,this.targets=[],this.icon=void 0,this.color=void 0,this.tickTime=0,this.isDestroy=!1}var t=e.prototype;return t.init=function(e){var t=c.getInstance().getBuffConfigByJsonId(e);switch(this.type=t.type,this.icon=t.icon,this.color=t.colour,this.tickTime=0,this.targets=[],this.isDestroy=!1,this.type){case i.Fire:this.hurtNum=t.hurt,this.effectEachTime=t.hurtInterval,this.effectTime=t.stateDescribe;break;case i.MoveSpeed:this.effectEachTime=.05,this.effectTime=t.stateDescribe,this.effectNum=t.percentage;break;case i.Dizziness:this.effectEachTime=.05,this.effectTime=.09,this.effectNum=t.stateDescribe;break;case i.AttackNum:this.effectNum=t.percentage;break;case i.AttackSpeed:case i.SkillSpeed:this.effectEachTime=.05,this.effectTime=t.stateDescribe,this.effectNum=t.percentage;break;case i.HurtWeak:this.effectNum=t.percentage,this.effectIds=t.weakness?t.weakness.split("|"):[]}},t.setTargets=function(e,t){void 0===t&&(t=!1),this.targets=e,t&&this.releaseBuff()},t.destroy=function(){this.isDestroy||(this.isDestroy=!0,s.getInstance().killBuff(this),this.targets=null)},t.releaseBuff=function(){var e=this;if(this.targets&&!(this.targets.length<=0))switch(this.type){case i.Fire:this.targets.forEach((function(t){t&&t.isRoleAlive()&&t.setBuffAutoHurt(e.hurtNum,e.effectEachTime,e.effectTime,e.icon,e.color)})),this.destroy();break;case i.MoveSpeed:this.targets.forEach((function(t){t&&t.isRoleAlive()&&t.setBuffSpeed(e.effectNum,e.effectTime,e.icon,e.color)})),this.destroy();break;case i.Dizziness:this.targets.forEach((function(t){t&&t.isRoleAlive()&&t.setDizzTime(e.effectNum,e.icon,e.color)})),this.destroy();break;case i.AttackNum:this.targets.forEach((function(t){t&&t.isRoleAlive()&&t.setBuffAtk(e.effectNum,3,e.icon,e.color)})),this.destroy();break;case i.AttackSpeed:this.targets.forEach((function(t){t&&t.isRoleAlive()&&t.setBuffAtkSpeed(e.effectNum,e.effectTime,e.icon,e.color)})),this.destroy();break;case i.SkillSpeed:this.targets.forEach((function(t){t&&t.isRoleAlive()&&t.setBuffSkillSpeed(e.effectNum,e.effectTime,e.icon,e.color)})),this.destroy();break;case i.HurtWeak:this.targets.forEach((function(t){t&&t.isRoleAlive()&&t.setWeakState(e.effectIds,e.effectNum)})),this.destroy()}},t.tick=function(e){this.tickTime+=e,this.effectTime&&this.tickTime>=this.effectTime?this.destroy():this.effectEachTime&&this.tickTime>this.effectEachTime&&this.tickTime%this.effectEachTime<=e&&this.releaseBuff()},e}());t._RF.pop()}}}));

System.register("chunks:///_virtual/EffectMgr.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ConstGlobal.ts","./Singleton.ts","./xcore.ts","./Collection.ts","./UnitSkillEffect.ts","./UnitHurtTips.ts","./Tool.ts","./UnitNewsEffect.ts","./UnitLighting2.ts","./UnitSkillTips.ts"],(function(t){var e,n,r,i,s,a,o,l,c,f,u,h,p,d,g,v,k,y,P,x,w,E;return{setters:[function(t){e=t.inheritsLoose,n=t.createForOfIteratorHelperLoose,r=t.asyncToGenerator,i=t.regeneratorRuntime},function(t){s=t.cclegacy,a=t.v2,o=t.Vec2,l=t.instantiate,c=t.v3},function(t){f=t.E_EVENT,u=t.E_AtkStyle,h=t.E_RoleType,p=t.C_Bundle},function(t){d=t.Singleton},function(t){g=t.xcore},function(t){v=t.Collection},function(t){k=t.UnitSkillEffect},function(t){y=t.UnitHurtTips},function(t){P=t.default},function(t){x=t.UnitNewsEffect},function(t){w=t.UnitLighting2},function(t){E=t.UnitSkillTips}],execute:function(){s._RF.push({},"b19ddYyiqZOQZoo1cZSfHSu","EffectMgr",void 0);t("EffectMgr",function(t){function s(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).targetParentNd=null,e.targetParentNd2=null,e.targetParentNd3=null,e.targetParentNd4=null,e.effectPools=new v,e.lightingPool=[],e.hurtTipsPool=[],e.newsPool=[],e.skillTipsPool=[],e._G=-2e3,e._V=1e3,e.linearVelocity=void 0,e}e(s,t);var d=s.prototype;return d.setEffectParentNd=function(t,e,n,r){this.targetParentNd=t,this.targetParentNd2=e,this.targetParentNd3=n,this.targetParentNd4=r,g.event.addEventListener(f.SkillEffect,this.ShowSkillEffect,this),g.event.addEventListener(f.HeroAtkEffect,this.ShowHeroAtkEffect,this),g.event.addEventListener(f.MonsterAtkEffect,this.ShowMonsterAtkEffect,this),g.event.addEventListener(f.HurtEffect,this.ShowMonsterHurtEffect,this),g.event.addEventListener(f.HurtTips,this.ShowHurtTips,this),g.event.addEventListener(f.MonsterDead,this.ShowMonsterDead,this),g.event.addEventListener(f.NewsEffect,this.ShowNewsEffect,this),g.event.addEventListener(f.SkillTips,this.ShowSkillTips,this)},d.destroy=function(){g.event.removeEventListener(f.SkillEffect,this.ShowSkillEffect,this),g.event.removeEventListener(f.HeroAtkEffect,this.ShowHeroAtkEffect,this),g.event.removeEventListener(f.MonsterAtkEffect,this.ShowMonsterAtkEffect,this),g.event.removeEventListener(f.HurtEffect,this.ShowMonsterHurtEffect,this),g.event.removeEventListener(f.HurtTips,this.ShowHurtTips,this),g.event.removeEventListener(f.MonsterDead,this.ShowMonsterDead,this),g.event.removeEventListener(f.NewsEffect,this.ShowNewsEffect,this),g.event.removeEventListener(f.SkillTips,this.ShowSkillTips,this),this.effectPools.forEach((function(t){t.length=0})),this.effectPools.clear(),this.newsPool.length=0,this.hurtTipsPool.length=0,this.lightingPool.length=0,this.skillTipsPool.length=0},d.refreshZindex=function(){var t=this.targetParentNd.children.concat();t.sort((function(t,e){return e.position.y-t.position.y}));for(var e,r=t.length,i=n(t);!(e=i()).done;){e.value.setSiblingIndex(r)}},d.ShowSkillEffect=function(){var t=r(i().mark((function t(e,n){var r;return i().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(this.effectPools||(this.effectPools=new v),!n.delay){t.next=4;break}return t.next=4,P.asyncDelay(n.delay);case 4:if(!(n.lightningTargets&&n.lightningTargets.length>0)){t.next=10;break}return t.next=7,this.ShowLightings(n.lightningTargets,n.animId01);case 7:return n.cb&&n.cb(1),n.sound&&g.sound.remotePlayOneShot(n.sound),t.abrupt("return");case 10:if(n.animId01||n.animId02||n.normalatk){t.next=13;break}return n.cb&&n.cb(),t.abrupt("return");case 13:if(!n.skill){t.next=19;break}return t.next=16,this.getSkillEffectByType(n.skill,null,n.animId01,n.animId02,null,n.sound);case 16:(r=t.sent).node.parent=this.targetParentNd,r.play(n);case 19:this.refreshZindex();case 20:case"end":return t.stop()}}),t,this)})));return function(e,n){return t.apply(this,arguments)}}(),d.ShowHeroAtkEffect=function(){var t=r(i().mark((function t(e,n){var r;return i().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!n.delay){t.next=3;break}return t.next=3,P.asyncDelay(n.delay);case 3:return t.next=5,this.getSkillEffectByType(null,n.normalatk,n.animId01,n.animId02);case 5:(r=t.sent).node.parent=this.targetParentNd,r.play(n);case 8:case"end":return t.stop()}}),t,this)})));return function(e,n){return t.apply(this,arguments)}}(),d.ShowMonsterAtkEffect=function(){var t=r(i().mark((function t(e,n){var r;return i().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!n.delay){t.next=3;break}return t.next=3,P.asyncDelay(n.delay);case 3:if(!n.checkFunc){t.next=8;break}if(n.checkFunc()){t.next=7;break}return t.abrupt("return");case 7:n.checkFunc=null;case 8:return t.next=10,this.getSkillEffectByType(null,null,n.animId01,n.animId02);case 10:r=t.sent,n.atkStyle==u.Near?r.node.parent=this.targetParentNd3:r.node.parent=this.targetParentNd2,r.play(n);case 13:case"end":return t.stop()}}),t,this)})));return function(e,n){return t.apply(this,arguments)}}(),d.ShowMonsterHurtEffect=function(){var t=r(i().mark((function t(e,n){var r;return i().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!n.delay){t.next=3;break}return t.next=3,P.asyncDelay(n.delay);case 3:return t.next=5,this.getSkillEffectByType(null,null,null,n.animId02);case 5:r=t.sent,n.formRoleType==h.Monster?r.node.parent=this.targetParentNd3:r.node.parent=this.targetParentNd2,r.play(n);case 8:case"end":return t.stop()}}),t,this)})));return function(e,n){return t.apply(this,arguments)}}(),d.ShowHurtTips=function(){var t=r(i().mark((function t(e,n){var r,s,a=this;return i().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(this.hurtTipsPool||(this.hurtTipsPool=[]),r=null,!(this.hurtTipsPool.length<=0)){t.next=9;break}return t.next=5,g.res.bundleLoadPrefab(p.abGame,"./prefab/Unit/UnitHurtTips");case 5:s=t.sent,r=l(s).getComponent(y),t.next=10;break;case 9:r=this.hurtTipsPool.shift();case 10:r.node.parent=this.targetParentNd4,r.setData(n,(function(t){a.killHurtTips(t)}));case 12:case"end":return t.stop()}}),t,this)})));return function(e,n){return t.apply(this,arguments)}}(),d.ShowMonsterDead=function(){var t=r(i().mark((function t(e,n){var r,s;return i().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r="deadeffect",t.next=3,this.getSkillEffectByType(null,null,null,null,r);case 3:(s=t.sent).node.parent=this.targetParentNd,n.deadanim=r,s.play(n);case 7:case"end":return t.stop()}}),t,this)})));return function(e,n){return t.apply(this,arguments)}}(),d.getSkillEffectByType=function(){var t=r(i().mark((function t(e,n,r,s,a,o){var c,f,u,h;return i().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(this.effectPools||(this.effectPools=new v),c=null,f=a||(e||(n||r||s)),(u=this.effectPools.get(f))||(this.effectPools.set(f,[]),u=this.effectPools.get(f)),u&&!(u.length<=0)){t.next=12;break}return t.next=8,g.res.bundleLoadPrefab(p.abGame,"./prefab/Unit/UnitSkillEffect");case 8:h=t.sent,c=l(h).getComponent(k),t.next=13;break;case 12:c=u.shift();case 13:return c.getComponent(k).setData(e,n,r,s,o),t.abrupt("return",c);case 15:case"end":return t.stop()}}),t,this)})));return function(e,n,r,i,s,a){return t.apply(this,arguments)}}(),d.ShowSkillTips=function(){var t=r(i().mark((function t(e,n){var r,s;return i().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(this.skillTipsPool||(this.skillTipsPool=[]),r=null,!(this.skillTipsPool.length<=0)){t.next=9;break}return t.next=5,g.res.bundleLoadPrefab(p.abGame,"./prefab/Unit/UnitSkillTips");case 5:s=t.sent,r=l(s).getComponent(E),t.next=10;break;case 9:r=this.skillTipsPool.shift();case 10:return r.node.parent=this.targetParentNd4,r.setData(n),t.abrupt("return",r);case 13:case"end":return t.stop()}}),t,this)})));return function(e,n){return t.apply(this,arguments)}}(),d.ShowNewsEffect=function(){var t=r(i().mark((function t(e,n){var r,s;return i().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(this.newsPool||(this.newsPool=[]),r=null,!(this.newsPool.length<=0)){t.next=9;break}return t.next=5,g.res.bundleLoadPrefab(p.abGame,"./prefab/Unit/UnitNewsEffect");case 5:s=t.sent,r=l(s).getComponent(x),t.next=10;break;case 9:r=this.newsPool.shift();case 10:return r.node.parent=this.targetParentNd4,r.setData(n),t.abrupt("return",r);case 13:case"end":return t.stop()}}),t,this)})));return function(e,n){return t.apply(this,arguments)}}(),d.ShowLightings=function(){var t=r(i().mark((function t(e,n){var r,s;return i().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:r=0;case 1:if(!(r<e.length)){t.next=8;break}return s=c(e[r].x,e[r].y),t.next=5,this.createLighting2(n,s);case 5:r++,t.next=1;break;case 8:case"end":return t.stop()}}),t,this)})));return function(e,n){return t.apply(this,arguments)}}(),d.createLighting=function(){var t=r(i().mark((function t(e,n){return i().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:case"end":return t.stop()}}),t)})));return function(e,n){return t.apply(this,arguments)}}(),d.createLighting2=function(){var t=r(i().mark((function t(e,n){var r,s;return i().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(this.lightingPool||(this.lightingPool=[]),r=null,this.lightingPool&&!(this.lightingPool.length<=0)){t.next=9;break}return t.next=5,g.res.bundleLoadPrefab(p.abGame,"./prefab/Unit/UnitLighting2");case 5:s=t.sent,r=l(s).getComponent(w),t.next=10;break;case 9:r=this.lightingPool.shift();case 10:r.setData(e,n),r.node.parent=this.targetParentNd;case 12:case"end":return t.stop()}}),t,this)})));return function(e,n){return t.apply(this,arguments)}}(),d.killSkillEffectByType=function(t,e){var n;null==e||null==(n=e.node)||n.removeFromParent();var r=this.effectPools.get(t);r&&r.push(e)},d.killHurtTips=function(t){t.node.removeFromParent(),this.hurtTipsPool.push(t)},d.killNewsEffect=function(t){t.node.removeFromParent(),this.newsPool.push(t)},d.killLighting=function(t){t.node.removeFromParent(),this.lightingPool.push(t)},d.killSkillTips=function(t){t.node.removeFromParent(),this.skillTipsPool.push(t)},d.arrowByTarget=function(t,e,n){if(t&&e){var r=[],i=e.x-t.x,s=e.y-t.y;this.linearVelocity=a(0,0);var l=t.x>e.x?-1:1;if(i*i+s*s<4e3)n(r=[{x:t.x,y:t.y},{x:e.x,y:e.y}],l,!0);else{var c=this._G*i/(2*this._V*this._V),f=1-4*c*(c-s/i);if(f>=0){var u=(-1-Math.sqrt(f))/(2*c),h=Math.atan(u)+(i<0?Math.PI:0),p=Math.cos(h)*this._V,d=Math.sin(h)*this._V;this.linearVelocity.x=p,this.linearVelocity.y=d}else this.linearVelocity=o.ZERO;if(this.linearVelocity.x)for(var g=0;g<100;g++){var v=.2*g,k=this.linearVelocity.x*v,y=this.linearVelocity.y*v+.5*this._G*v*v,P=t.x+k,x=t.y+y;if(x<e.y&&(1==l&&P>e.x||-1==l&&P<e.x)){r.push({x:e.x,y:e.y});break}r.push({x:P,y:x})}n(r,l,!1)}}},s}(d));s._RF.pop()}}}));

System.register("chunks:///_virtual/FightMgr.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ConstGlobal.ts","./Singleton.ts","./Role.ts","./Skill.ts","./QuadTree.ts","./EventManager2.ts","./List2.ts","./Tool.ts","./xcore.ts","./UnitRoleComp.ts","./Buff.ts","./Net.ts","./ConfigHelper.ts","./TimeUtil.ts"],(function(e){var t,n,r,o,i,s,a,l,c,u,h,d,f,m,g,p,v,k,w,y,T,I,S,N,R,b,x,L,P,G,D,E,U,M,C,B,O,K,F;return{setters:[function(e){t=e.inheritsLoose,n=e.asyncToGenerator,r=e.regeneratorRuntime,o=e.createForOfIteratorHelperLoose},function(e){i=e.cclegacy,s=e.v2,a=e.UITransform,l=e.Rect,c=e.log,u=e.error,h=e.tween,d=e.v3,f=e.Vec3,m=e.view,g=e.warn},function(e){p=e.E_RoleType,v=e.E_TowerPointType,k=e.C_GiftKey,w=e.E_EVENT,y=e.E_GameState,T=e.E_GiftMessageType,I=e.C_GreatSkill,S=e.E_SkillType,N=e.C_View,R=e.E_MonsterTag,b=e.E_Channel},function(e){x=e.Singleton},function(e){L=e.Role},function(e){P=e.Skill},function(e){G=e.Quadtree},function(e){D=e.EventManager},function(e){E=e.List},function(e){U=e.default},function(e){M=e.xcore},function(e){C=e.UnitRoleComp},function(e){B=e.Buff},function(e){O=e.default},function(e){K=e.ConfigHelper},function(e){F=e.default}],execute:function(){i._RF.push({},"2bf90LFhKRHdrgBCuANc4SK","FightMgr",void 0);e("FightMgr",function(e){function i(){var t;return(t=e.call(this)||this).gameState=y.None,t.heroUser=new Map,t.heroRoles=[],t.waitUser=new Map,t.monsterRoles=new E(!0),t.monsterNum=0,t.attackTargets=new Map,t.buffList=new E(!0),t.tower=void 0,t.towerPoints=[],t.towerGiftNums=[0,0,0,0],t.tickTime=0,t.heroParentNd=void 0,t.monsterParentNd=void 0,t.towerParentNd=void 0,t.skillParentNd=void 0,t.monsterSpace={left:0,right:0,top:0,bottom:0,width:0,height:0},t.heroSpace={left:0,right:0,top:0,bottom:0,width:0,height:0},t.rolePool=new E,t.skillPool=new E,t.buffPool=[],t.gameoverTime=void 0,t.gameRoundConfigs=void 0,t.gameRound=0,t.gameLevel=0,t.gameLevelJsonId=void 0,t.scoreAddedValue=void 0,t.newGameLevel=0,t.autoNextGameLevel=!1,t.nextRoundTime=3,t.eachCreateMonsterTime=1,t.tempeachCreateMonsterTime=1,t.eachCreateMonsterNum=3,t.leftRoundMonsterNum=0,t.roundMonsterJsonId=void 0,t.towerPointUpGiftNum=100,t.gameTotalScore=0,t.gameTotalKillScore=0,t.gameTotalGiftScore=0,t.firstJoinGame=!0,t.isOldTargetPriority=!0,t.quadTree=void 0,t.checkRect=void 0,t.roundEmitData=void 0,t.towerTw=void 0,t.towerTwFunc=void 0,t.dimondId=null,t.freeDimondId=null,t.lotteryId=null,t.freeLotteryId=null,t.firstPassLotteryId=null,t.needUpdateUser=[],t.onRefreshUserRank=null,t.lastUser=new E,t.isOnline=!1,t.giftPowerNum=0,t}t(i,e);var x=i.prototype;return x.initTower=function(){this.tower||(this.tower=new L),this.tower.add(null,p.Tower),this.createTowerPoints()},x.createTowerPoints=function(){this.towerGiftNums=[0,0,0,0];for(var e=[v.Point1,v.Point2,v.Point3,v.Point4],t=0;t<4;t++){var n=this.towerPoints[t];n||(n=new L,this.towerPoints.push(n));var r=s(-this.monsterSpace.width/2+this.monsterSpace.width/4*(t+.5),this.towerParentNd.position.y+30);n.add("towerPoint",p.TowerPoint,e[t],1,r)}},x.setRoleParentNd=function(e,t,n,r){this.heroParentNd=e,this.monsterParentNd=t,this.towerParentNd=n,this.skillParentNd=r},x.setRoleMoveSpace=function(e,t){var n=e.getComponent(a).width,r=e.getComponent(a).height,o=t.getComponent(a).width,i=t.getComponent(a).height;this.monsterSpace.left=e.position.x-n/2,this.monsterSpace.right=e.position.x+n/2,this.monsterSpace.top=e.position.y+r/2,this.monsterSpace.bottom=e.position.y-r/2,this.monsterSpace.x=e.position.x,this.monsterSpace.y=e.position.y,this.monsterSpace.width=e.getComponent(a).width,this.monsterSpace.height=e.getComponent(a).height,this.heroSpace.left=t.position.x-o/2,this.heroSpace.right=t.position.x+o/2,this.heroSpace.top=t.position.y+i/2,this.heroSpace.bottom=t.position.y-i/2},x.getTreeColliderList=function(e){return this.quadTree?this.quadTree.retrieve(e):[]},x.rebuildTree=function(){this.checkRect||(this.checkRect=new l(this.monsterSpace.x,this.monsterSpace.y,this.monsterSpace.width,this.monsterSpace.height)),this.quadTree=new G(this.checkRect,0);for(var e=0;e<this.monsterRoles.count;e++){var t=this.monsterRoles.get(e);if(t&&t.isRoleAlive()){var n,r=null==t||null==(n=t.comp)?void 0:n.node;r&&this.quadTree.insert(r)}}},x.checkNearMonster=function(e,t,n){void 0===n&&(n=1),t<=0&&(t=10);var r=[],o=this.getTreeColliderList(e);if(o.length<=0)return r;for(var i=0,s=e.getComponent(C).getFromRole().data.pos,a=0;a<o.length;a++){var l=o[a].getComponent(C).getFromRole(),c=l.data.pos;if(i>=n)break;if(l&&l.isRoleAlive())this.isCollision(s,c,t)&&(i+=1,r.push(l))}return r},x.checkLineMonster=function(e,t,n){void 0===n&&(n=1);var r=[],o=this.getTreeColliderList(e);if(o.length<=0)return r;for(var i=0,s=0;s<o.length;s++){var a=o[s].getComponent(C).getFromRole();a.data.pos;if(i>=n)break;a&&a.isRoleAlive()&&(i+=1,r.push(a))}return r},x.isCollision=function(e,t,n){if(!e||!t)return!1;var r=n*n;return Math.abs((e.y-t.y)*(e.y-t.y)+(e.x-t.x)*(e.x-t.x))<=r},x.findUser=function(e,t){return this.heroUser.get(e)},x.getUserRank=function(e){var t=this.findUser(e);return t?t.rank:-1},x.getIUsers=function(){return this.heroUser},x.getUsers=function(){return this.heroRoles},x.addUserScore=function(e,t,n,r){void 0===n&&(n=!1);var o=this.findUser(e);if(o){o.score||(o.score=0),o.killscore||(o.killscore=0),o.giftscore||(o.giftscore=0),o.gold||(o.gold=0),o.giftTitleScore||(o.giftTitleScore=0);var i=Math.floor(t);o.score+=i,this.addTotalScore(2*i),n?(o.killscore+=i,this.addtotalKillScore(2*i)):(o.giftscore+=i,r!=k.Like&&r!=k.SixSix&&(o.giftTitleScore+=i,o.gold+=i),this.addTotalGiftScore(2*i),this.checkTask(o))}M.event.raiseEvent(w.GameScore)},x.addUserAtkPower=function(e,t){var n=this.findUser(e);n&&(n.atkPower||(n.atkPower=0),n.atkPower+=Math.floor(t))},x.addTotalScore=function(e){this.gameTotalScore||(this.gameTotalScore=0),this.gameTotalScore+=e,M.event.raiseEvent(w.GameScore)},x.addTotalGiftScore=function(e){this.gameTotalGiftScore||(this.gameTotalGiftScore=0),this.gameTotalGiftScore+=e},x.addtotalKillScore=function(e){this.gameTotalKillScore||(this.gameTotalKillScore=0),this.gameTotalKillScore+=e},x.getTotalScore=function(){return this.gameTotalScore||(this.gameTotalScore=0),this.gameTotalScore},x.getTotalGiftScore=function(){return this.gameTotalGiftScore||(this.gameTotalGiftScore=0),this.gameTotalGiftScore},x.getTotalKillScore=function(){return this.gameTotalKillScore||(this.gameTotalKillScore=0),this.gameTotalKillScore},x.checkIfAbleGift=function(){return this.gameState!=y.None},x.checkExchangeWing=function(){var e=n(r().mark((function e(t,o){var i,s,a=this;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(i=this.findUser(t)){e.next=4;break}return console.log("user not exist",t),e.abrupt("return");case 4:if(!(s=K.getInstance().getExchaengConfigByNameContent(o))){e.next=11;break}if(!(i.score<s.yingYunConsume)){e.next=9;break}return M.ui.showToast("灵韵不足"),e.abrupt("return");case 9:console.log("wing exchange",t,o,s),O.costGold(s.yingYunConsume,t).then(n(r().mark((function e(n){var o;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(1!=n.data){e.next=7;break}return e.next=3,O.addDebris(t,s.rewardNum,s.skinFragmentId);case 3:(o=K.getInstance().getSkinConfigByDebrisId(s.skinFragmentId))&&a.checkIfExchangeSkin(i,s.skinFragmentId,o.skinFragmentNum,o.jsonId,o.period,s.skinFragmentId),e.next=8;break;case 7:M.ui.showToast("灵韵不足");case 8:case"end":return e.stop()}}),e)}))));case 11:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}(),x.checkIfExchangeSkin=function(e,t,n,r,o,i){var s=e.skins.find((function(e){return e.prop==r})),a=K.getInstance().getSkinMaxLevelBySkinId(r);if(c("maxLev:",a,null==s?void 0:s.level,s),!s||(null==s?void 0:s.level)<a){var l=e.debris.find((function(e){return e.prop==t}));if((null==l?void 0:l.num)>=n){O.exchangeSkin(e.userId,r,n,o,i);var u=r;l.num-=n;var h={prop:r,time:F.getServerTime()+1e3*o,level:s?s.level+=1:1};c("up",h),e.skins.push(h),M.event.raiseEvent(w.GiftMessage,{type:T.SkinReward,skinId:u,user:e,lev:h.level})}}else c("已有皮肤"+r)},x.checkLingyunExchange=function(){var e=n(r().mark((function e(t,n,o,i,s){var a,l,c=this;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.findUser(t)){e.next=3;break}return e.abrupt("return");case 3:if(1!=M.gameData.gameType){e.next=10;break}if(a=K.getInstance().getDungeonExchangeConfigByJsonId(M.gameData.gameTypeJsonId),console.log("content",s,a.name),s==a.name){e.next=9;break}return M.ui.showToast("兑换副本错误 \n错误的副本"+s+" 当前副本"+a.name),e.abrupt("return");case 9:a&&(l=K.getInstance().getLinyunKeyByForever(),O.getRoleRankInfo(l,[t],(function(e){console.log("灵韵",e[0].score,s),e[0].score>=a.yingYunConsume?O.costGold(a.yingYunConsume,t).then((function(e){1==e.data?c.addUser(t,n,o,i,!0):M.ui.showToast("灵韵不足")})):M.ui.showToast("灵韵不足")}),!1));case 10:case"end":return e.stop()}}),e,this)})));return function(t,n,r,o,i){return e.apply(this,arguments)}}(),x.addUser=function(){var e=n(r().mark((function e(t,o,i,s,a){var l,u,h,d,f,m,g,v,y,T,I=this;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(void 0===a&&(a=!1),c("adduser",o),!(l=this.findUser(t))){e.next=6;break}return c("已是"+(l.type==p.Hero?"仙族":"妖族")+"阵营玩家，无需再加入"+(o==p.Hero?"仙族":"妖族")),e.abrupt("return");case 6:if(1!=M.gameData.gameType||a){e.next=31;break}if(!(h=K.getInstance().getDungeonConfigByJsonId(M.gameData.gameTypeJsonId))){e.next=31;break}return console.log("",h.type,h.limitation),d=1==h.type?F.formatTimestampToDate(F.getServerTime(),"-"):2==h.type?F.getFirstDayOfTheWeek(F.formatTimestampToDate(F.getServerTime(),"-")):F.getFirstDayOfTheMonth(),f="bossFight_"+M.gameData.gameTypeJsonId,e.next=14,O.getPlayerOperate(t,f);case 14:if(null==M.gameData.playersOperation[t][f]){e.next=29;break}if(m=M.gameData.playersOperation[t][f].split("_"),g=m[0],v=Number(m[1]),g!=d&&(v=0),!(v>=h.limitation)){e.next=25;break}return c("已超过挑战次数"),M.ui.showToast((i||"匿名用户")+"已超过挑战次数\n限制"+h.limitation+" 已玩"+v),e.abrupt("return");case 25:u=d+"_"+(v+=1);case 27:e.next=31;break;case 29:u=d+"_1";case 31:if(this.lastUser.push({userId:t,type:o,nickName:i,iconUrl:s}),o!=p.Hero){e.next=46;break}return y=this.createHero(t,o,i,s),T={userId:t,type:o,role:y,nickName:i,iconUrl:s,giftKey:[{key:k.JoinHero,num:1}],gold:0,score:0,poolScore:0,upscore:0,killboss:0,killmonster:0,helptower:0,skins:[],debris:[],bossChangeTimeTxt:u},this.heroUser.set(t,T),this.heroRoles.push(y),this.refreshHeroZindex(),e.next=40,O.roleJoin(i,s,t);case 40:return e.next=42,this.updateSkinInfo(t);case 42:this.updateDimondInfo(t),this.needUpdateUser.push(t),this.onRefreshUserRank||(this.onRefreshUserRank=U.debounce(n(r().mark((function e(){var t,n,o,i,s;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=K.getInstance().getRankKeyByMonth(),n=I.needUpdateUser,o="",n.forEach((function(e,t){o+=0==t?e:","+e})),I.needUpdateUser=[],O.getDebrisInfo(o,(function(e){for(var t=function(){var t=n[r],o=I.findUser(t),i=e.filter((function(e){return e.playerId==t}));o.debris=i},r=0;r<n.length;r++)t()})),e.next=8,O.getRoleRankInfo(t,n,(function(e){e.forEach((function(e){var t=I.findUser(e.playerId);t&&(t.rank=e.rank,t.role&&(t.role.data.weekScore=e.score,t.role.checkSwitchSkin()))}))}));case 8:return i=K.getInstance().getGiftRankKeyByForever(),e.next=11,O.getRoleRankInfo(i,n,(function(e){e.forEach((function(e){var t,n=I.findUser(e.playerId);n&&(n.giftTitleScore=e.score,null==(t=n.role)||t.checkGiftTitle(n.giftTitleScore))}))}),!1);case 11:s=K.getInstance().getLinyunKeyByForever(),O.getRoleRankInfo(s,n,(function(e){e.forEach((function(e){var t=I.findUser(e.playerId);t&&(t.lingyun=e.score)}))}),!1);case 13:case"end":return e.stop()}}),e)}))),1e3,!1)),this.onRefreshUserRank();case 46:D.getInstance().raiseEvent(w.BaseInfo,{heroUserInfo:this.heroUser,monsterRoles:this.monsterRoles});case 47:case"end":return e.stop()}}),e,this)})));return function(t,n,r,o,i){return e.apply(this,arguments)}}(),x.updateSkinInfo=function(e){var t=this;O.getSKinInfo(e,n(r().mark((function n(o){var i,s,a,l,u,h,d;return r().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if((a=t.findUser(e)).skins=o,l="170001",a.skins&&!(a.skins.length<=0)){n.next=8;break}return u=2592e3,n.next=7,O.rewardSkin(e,4,[{num:1,prop:l,time:u}]);case 7:a.skins.push({prop:l,time:u,useStatus:null});case 8:(h=a.skins.find((function(e){return 1==e.useStatus})))||(d=a.skins.find((function(e){return e.prop==l})),O.setSelectSkin(e,l),d&&(d.useStatus=1),h=d),a.skins.forEach((function(e){var t=e.time,n=F.getServerTime()+1e3*t;e.time=n})),a.role.switchSkin(null==(i=h)?void 0:i.prop,null==(s=h)?void 0:s.level),c("皮肤：",e,a.skins);case 13:case"end":return n.stop()}}),n)}))))},x.updateDimondInfo=function(){var e=n(r().mark((function e(t){var n=this;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:O.getDimondInfo(t).then((function(e){var r=n.findUser(t);r&&e.data.length>0&&(c("dimondInfo:",e.data),r.role.updateDimondInfo(e.data))}));case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),x.updateGiftTitle=function(){},x.updateTowerPointLev=function(e){void 0===e&&(e=1);for(var t=0,n=this.towerPoints[t],r=this.towerPoints[t].data.lev,o=K.getInstance().getTowerPointMaxLev(),i=0;i<this.towerPoints.length;i++){var s=this.towerPoints[i];if(r>s.data.lev){t=i,n=s;break}}var a=e>this.towerPointUpGiftNum?this.towerPointUpGiftNum:e;this.towerGiftNums[t]+=a;var l=1+Math.floor(this.towerGiftNums[t]/this.towerPointUpGiftNum);n.data.lev<o&&n.data.lev<l&&(n.towerPointupLev(l),e>this.towerPointUpGiftNum&&this.updateTowerPointLev(e-a))},x.updateTowerPoint=function(e){void 0===e&&(e=1);for(var t=[],n=0;n<this.towerPoints.length;n++)t.push(this.towerPoints[n].data.lev);this.updateTowerPointLev(e);for(var r=0;r<this.towerPoints.length;r++){var o=this.towerPoints[r],i=t[r],s=o.data.lev;i<s&&M.event.raiseEvent(w.GiftMessage,{type:T.SkillUp,fromLev:i,anim:o.data.moveAnimation,lev:s,towerPointType:o.data.monsterType})}},x.doSwitchSkin=function(e,t,n){var r=n||K.getInstance().getSkinJsonIdByContent(t);if(c("doSwitchSkin:",e,t,r),e.type==p.Hero){var o=e.role;null==o||o.switchSkin(r)}e.giftKey.push({key:r,num:1})},x.addGift=function(e,t,n,r){if(c("addgift",e,t,n),"string"==typeof e&&(e=this.findUser(e)),e)if(n){var o=K.getInstance().getGiftConfigByJsonId(t),i=this.getUserGiftByKey(e,t,r,o.integra);if(e.type==p.Hero){var s=e.role,a=I[n],l=K.getInstance().getGreateSkillAbleLevelByNum(a,i);s.updateSkill(n,a,l,r),n!=S.Attack&&(M.event.raiseEvent(w.GiftMessage,{type:T.Gift,userId:e.userId,name:e.nickName,avatar:e.iconUrl,giftType:t,num:r,totalNum:i}),M.event.raiseEvent(w.GiftMessage,{type:T.GetSmallSkill,userId:s.data.userId,name:s.data.nickName,avatar:s.data.iconUrl,skillType:n,num:r}));var h=o.integra*r;this.addUserScore(s.data.userId,h,!1,t)}D.getInstance().raiseEvent(w.BaseInfo,{heroUserInfo:this.heroUser,monsterRoles:this.monsterRoles})}else u("addGift key err")},x.getUserGiftByKey=function(e,t,n,r){var o=e.giftKey.find((function(e){return e.key==t})),i=K.getInstance().getGiftTypeByJsonId(t);return o?(o.num+=n,o.num):(e.giftKey.push({key:t,num:n,giftType:i,price:r}),n)},x.createRoundMonsters=function(){if(this.tempeachCreateMonsterTime=this.eachCreateMonsterTime,this.leftRoundMonsterNum<=0)return this.gameRound+=1,void this.refreshLevelMonsterConfig();for(var e=this.leftRoundMonsterNum<this.eachCreateMonsterNum?this.leftRoundMonsterNum:this.eachCreateMonsterNum,t=0;t<e;t++)this.createMonster(null,null,this.roundMonsterJsonId);this.leftRoundMonsterNum-=e,c("leftRoundMonsterNum",this.leftRoundMonsterNum,e,this.newGameLevel),this.emitRoundData()},x.goNextGameLevel=function(){this.newGameLevel=0},x.setAbleAutoNextGame=function(e){this.autoNextGameLevel=e,this.autoNextGameLevel&&(this.checkOpenBox(),this.newGameLevel=0),this.gameState==y.Pause&&(this.gameState=y.Resume)},x.setGameOnlineMode=function(e){this.isOnline=e},x.refreshLevelMonsterConfig=function(){var e=this.gameRoundConfigs[this.gameRound];if(console.log("refreshLevelMonsterConfig",this.gameRound),!e)return console.log("游戏结束"),this.nextRoundTime=100,void(this.monsterNum<=0&&this.checkOpenBox(!0));if(e.lotteryId&&(this.lotteryId=e.lotteryId,this.freeLotteryId=e.freeLotteryId),e.firstPassLotteryId&&(this.firstPassLotteryId=e.firstPassLotteryId),e.beadlotteryId&&(this.dimondId=e.beadlotteryId,this.freeDimondId=e.freeBeadlotteryId,c(" this.dimondId",this.dimondId)),this.gameLevelJsonId=e.gameLevel,this.gameLevelJsonId){var t=K.getInstance().getLevelConfigByJsonId(this.gameLevelJsonId);this.scoreAddedValue=t.scoreAddedValue,this.gameLevel!=t.gameLevel?(this.firstJoinGame||1==t.gameLevel||(this.newGameLevel=t.gameLevel),c("关卡更新"+this.gameLevel+"--\x3e"+t.gameLevel)):this.newGameLevel=0,this.gameLevel=t.gameLevel}this.firstJoinGame=!1,this.nextRoundTime=e.refreshInterval,this.leftRoundMonsterNum=e.monsterTotal,this.roundMonsterJsonId=e.monsterId,this.eachCreateMonsterTime=e.monsterInterval||1,this.tempeachCreateMonsterTime=this.eachCreateMonsterTime,this.eachCreateMonsterNum=e.monsterNum||3,0==this.newGameLevel&&M.ui.addView(N.ViewRoundToast,{desc:e.name}),e.preview&&M.event.raiseEvent(w.NewsEffect,{anim:e.preview,anim02:e.admission})},x.emitRoundData=function(){var e=this.gameRoundConfigs[this.gameRound];if(e){this.roundEmitData={roundDesc:e.name},e.type==R.normal&&(this.roundEmitData.monsterId=e.monsterId,this.roundEmitData.leftNum=this.leftRoundMonsterNum,this.roundEmitData.monsterSub=e.prompt);var t=K.getInstance().getBossConfigStartBy(e.refresh);t&&(this.roundEmitData.bossId=t.monsterId,this.roundEmitData.bossRoundDesc=t.name,this.roundEmitData.bossSub=t.prompt),M.event.raiseEvent(w.Round,this.roundEmitData)}},x.createMonster=function(){var e=n(r().mark((function e(t,n,o,i){var s;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return s=null,s=this.rolePool.count>0?this.rolePool.shift():new L,this.monsterNum+=1,this.monsterRoles.push(s),e.next=6,s.add("monster",p.Monster,t,n,i,null,null,o);case 6:return e.abrupt("return",s);case 7:case"end":return e.stop()}}),e,this)})));return function(t,n,r,o){return e.apply(this,arguments)}}(),x.createHero=function(e,t,n,r){var o=new L;return o.add(e,t,null,1,null,n,r),o},x.getRandomPosByType=function(e){return e==p.Hero?s(U.randomNumber(this.heroSpace.left,this.heroSpace.right),U.randomNumber(this.heroSpace.bottom,this.heroSpace.top)):e==p.Monster?s(U.randomNumber(this.monsterSpace.left,this.monsterSpace.right),U.randomNumber(this.monsterSpace.bottom,this.monsterSpace.top)):s(0,0)},x.getDistanceFromTower=function(e){var t;if(null!=e&&null!=(t=e.comp)&&t.node)return Math.abs(e.comp.node.position.y-this.towerParentNd.position.y)},x.attackTower=function(e){var t=this;if(this.tower&&e.isRoleAlive()){if(!this.towerTw){var n=this.tower.getAnimNode();if(!n)return;this.towerTw=h(n).to(.1,{scale:d(1.02,1.02,1.02)}).by(.1,{position:d(0,10)}).by(.1,{position:d(0,-10)}).to(.05,{scale:d(1,1,1)}),this.towerTwFunc=U.throttle((function(){t.towerTw.stop(),n.setPosition(f.ZERO),n.scale=d(1,1,1),t.towerTw.start()}),800)}this.towerTwFunc();var r=this.tower.hurt(e.data.atkNum);(!r||r<=0)&&(c("游戏结束 塔防失守"),this.fightOver(!1))}},x.addMaxHpTower=function(e){this.tower&&this.tower.towerUpHp(e)},x.saveTower=function(e,t,n){if(this.tower){this.tower.saveTower(e,5);var r=this.findUser(n);r&&(r.helptower||(r.helptower=0),r.helptower+=e,c("help:",r.helptower))}},x.upLevTower=function(e){if(this.tower){this.giftPowerNum+=e;var t=Math.floor(this.giftPowerNum/10)+1;this.tower.towerUpLev(t,this.gameState==y.Gameing)}},x.buffTower=function(e){this.tower&&this.tower.setInvincible(e)},x.getAttackNearEnemys=function(e,t,n){if(void 0===t&&(t=1),void 0===n&&(n=!1),e.data.type==p.Hero||e.data.type==p.TowerPoint){if(this.isOldTargetPriority&&!n){var r=this.attackTargets.get(e.data.userId);if(r&&r.isRoleAlive())return[r]}var o=this.filterNearEnemy(e,t);return o[0]&&this.attackTargets.set(e.data.userId,o[0]),o}if(e.data.type==p.Monster)return this.getHeroRoles()},x.getTower=function(){return[this.tower]},x.getHeroRoles=function(){return this.heroRoles},x.getMonsters=function(){return this.monsterRoles},x.filterNearEnemy=function(e,t,n){var r;void 0===n&&(n=500);var o=null==(r=e.comp)?void 0:r.node;if(!o)return[];if(e.data.type==p.Hero||e.data.type==p.TowerPoint){var i=this.monsterRoles.filter((function(e){return Math.abs(e.data.pos.x-o.position.x)<=n&&e.isRoleAlive()}));return i.length<=0?n+30>=m.getVisibleSize().width?[]:this.filterNearEnemy(e,t,n+30):(i.sort((function(e,t){var n,r;return(null==t||null==(n=t.comp)||null==(n=n.node)?void 0:n.position.y)-(null==e||null==(r=e.comp)||null==(r=r.node)?void 0:r.position.y)})),i.length>t&&(i=i.slice(0,t)),i)}},x.filterEnemyByDistance=function(e,t){var n=t*t;return this.monsterRoles.filter((function(t){return Math.abs((e.y-t.data.pos.y)*(e.y-t.data.pos.y)-(e.x-t.data.pos.x)*(e.x-t.data.pos.x))<=n&&t.isRoleAlive()}))},x.killMonster=function(e,t){var n=this;if(t&&this.attackTargets.get(t)&&this.attackTargets.delete(t),this.monsterRoles.has(e)){var r=this.monsterRoles.remove(e);setTimeout((function(){n.rolePool.push(r)}),200)}else e.destroy(!0),g("monster killed no find");if(this.monsterNum-=1,c("monsterRoles:",this.newGameLevel,this.monsterRoles.count,this.monsterNum,this.monsterParentNd.children.length),this.monsterNum<=0||this.monsterParentNd.children.length<=0){if(this.monsterRoles.count>0){for(var o=0;o<this.monsterRoles.count;o++){var i=this.monsterRoles.get(o);i&&i.destroy(!0)}g("monsterRoles clear"),this.monsterRoles.clear()}if(this.gameRoundConfigs[this.gameRound])return void((this.lotteryId||this.dimondId)&&this.newGameLevel?this.checkOpenBox():this.newGameLevel?this.autoNextGameLevel?this.createRoundMonsters():M.event.raiseEvent(w.NextGameLevel,this.newGameLevel):this.createRoundMonsters());this.checkOpenBox(!0)}},x.checkIfHaveBox=function(){(this.lotteryId||this.dimondId)&&this.newGameLevel&&(this.monsterNum<=0||this.monsterParentNd.children.length<=0||this.monsterRoles.count<=0)&&this.checkOpenBox()},x.checkOpenBox=function(e){var t=this;if(void 0===e&&(e=!1),this.lotteryId||this.dimondId){var n=120|K.getInstance().getConstantConfigByKey("maxLevel"),r=e&&this.firstPassLotteryId&&M.gameData.gameLev!=n,o=r?this.firstPassLotteryId:this.lotteryId,i=this.freeLotteryId,s=this.dimondId,a=this.freeDimondId;this.lotteryId=null,this.freeLotteryId=null,this.dimondId=null,this.firstPassLotteryId=null,this.gameState=y.Pause,console.log("最高关卡：",n,"是否通关",e,"是否有通关皮肤：",r,"结算关卡：",this.gameLevel,"历史最高关卡：",M.gameData.gameLev);var l=K.getInstance().getConstantConfigByKey("lotteryNum")||10,u=Array.from(this.heroUser);u.sort((function(e,t){return t[1].score-e[1].score}));var h=u.filter((function(e,t){return t<=l-1}));c("targetscore",l,u),M.ui.addView(N.ViewOpenBox,{dimondId:s,lotteryId:o,freeid:i,freeDimondId:a,isPassLottery:r,users:h,cb:function(){e?t.fightOver(!0):(t.gameState=y.Resume,t.newGameLevel?t.autoNextGameLevel?t.createRoundMonsters():M.event.raiseEvent(w.NextGameLevel,t.newGameLevel):t.createRoundMonsters()),c("resume",t.newGameLevel,t.autoNextGameLevel)}})}else e&&this.fightOver(!0)},x.killSkill=function(e){var t=this.skillPool.push(e);c("poolNum:",this.skillPool.count,t)},x.getSkill=function(){var e=this.skillPool.shift();return e||(e=new P,c("get new skill")),c("pool1",this.skillPool),e},x.killBuff=function(e){this.buffList.remove(e),this.buffPool.push(e)},x.getBuff=function(){var e=null;return e=this.buffPool.length>0?this.buffPool.shift():new B,this.buffList.push(e),e},x.setGameOverTime=function(e){this.gameoverTime=e},x.checkLastUser=function(e){if(this.isOnline){for(var t=0;t<this.lastUser.count;t++){var n=this.lastUser.get(t);n&&this.addUser(n.userId,n.type,n.nickName,n.iconUrl)}e&&e()}},x.fightStart=function(){var e=n(r().mark((function e(t,n){var o,i=this;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(void 0===t&&(t=0xe8d4a51001),0==M.gameData.gameType?(o=K.getInstance().getLevelConfigByLevel(M.gameData.gameSelectLev),this.gameRoundConfigs=K.getInstance().getMonsterRefreshConfigByLevelJsonId(null==o?void 0:o.jsonId)):this.gameRoundConfigs=K.getInstance().getDungeonRefreshConfigByLevelJsonId(M.gameData.gameTypeJsonId),this.gameRoundConfigs&&!(this.gameRoundConfigs.length<=0)){e.next=8;break}return M.ui.showToast("配置错误"),M.gameData.gameSelectLev=1,M.event.raiseEvent(w.GameConfig),setTimeout((function(){M.event.raiseEvent(w.GameReplay)}),50),e.abrupt("return");case 8:if(console.log("配置：",t,this.gameRoundConfigs),console.log("开始游戏","开始关卡：",M.gameData.gameSelectLev,"已完成最高关卡：",M.gameData.gameLev),e.prev=10,M.channel!=b.TIKTOK){e.next=14;break}return e.next=14,O.startFight(M.gameData.combatId,M.gameData.gameMode);case 14:if(this.monsterNum=0,this.firstJoinGame=!0,M.gameData.oldRankInfo={},this.gameoverTime=t,this.newGameLevel=0,this.tower){e.next=25;break}return e.next=22,this.initTower();case 22:setTimeout((function(){i.tower.setTowerHp(n)}),2e3),e.next=26;break;case 25:this.tower.setTowerHp(n);case 26:this.gameRound=0,this.refreshLevelMonsterConfig(),this.gameState=y.Gameing,M.event.raiseEvent(w.GameScore),e.next=37;break;case 32:e.prev=32,e.t0=e.catch(10),M.ui.showToast("开始游戏失败"+e.t0),this.gameState=y.Stop,setTimeout((function(){M.event.raiseEvent(w.GameReplay)}),50);case 37:case"end":return e.stop()}}),e,this,[[10,32]])})));return function(t,n){return e.apply(this,arguments)}}(),x.fightOver=function(){var e=n(r().mark((function e(t){var n,o,i,s,a,l,u,h,d,f,m,g,p,v,k,T,I,S,N,R,b,x,L,P,G,D,E,C,B,F,H,J,A,_,V,q,W=this;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.gameState!=y.None){e.next=2;break}return e.abrupt("return");case 2:if(this.gameState=y.None,this.tower&&!t){for(this.tower.destroy(!0),this.tower=null,n=0;n<this.towerPoints.length;n++)this.towerPoints[n].destroy(),null;this.towerPoints=[]}this.getTotalScore(),o=this.getTotalGiftScore(),i=this.getTotalKillScore(),s=[],a=[],l=[],u=[],h={},d=[],v=t&&this.leftRoundMonsterNum<=0&&!!this.gameRoundConfigs[this.gameRound],(k=Array.from(this.heroUser)).sort((function(e,t){var n,r;return(null==(n=t[1])?void 0:n.giftscore)-(null==(r=e[1])?void 0:r.giftscore)})),T=0;case 17:if(!(T<3)){e.next=29;break}if(I=k[T]){e.next=21;break}return e.abrupt("continue",26);case 21:if(S=I[0],N=I[1],S&&N){e.next=25;break}return e.abrupt("continue",26);case 25:I&&(R=this.heroUser.get(S))&&(b=K.getInstance().getRankUpScore(1,T+1),R.poolScore+=Math.floor(o*b));case 26:T++,e.next=17;break;case 29:k.sort((function(e,t){var n,r;return(null==(n=t[1])?void 0:n.killscore)-(null==(r=e[1])?void 0:r.killscore)})),x=0;case 31:if(!(x<3)){e.next=43;break}if(L=k[x]){e.next=35;break}return e.abrupt("continue",40);case 35:if(P=L[0],G=L[1],P&&G){e.next=39;break}return e.abrupt("continue",40);case 39:L&&(D=this.heroUser.get(P))&&(E=K.getInstance().getRankUpScore(2,x+1),D.poolScore+=Math.floor(i*E));case 40:x++,e.next=31;break;case 43:e.prev=43,C=0;case 45:if(!(C<k.length)){e.next=65;break}if(B=k[C]){e.next=49;break}return e.abrupt("continue",62);case 49:if(F=B[0],H=B[1],F&&H){e.next=53;break}return e.abrupt("continue",62);case 53:B&&(J=this.heroUser.get(F))&&J.poolScore&&(A=J.score+J.poolScore,J.score=Math.floor(A*this.scoreAddedValue),J.upscore=J.score-A),!f&&H.score>0&&(f=H),!m&&H.killboss>0&&(m=H),!g&&H.killmonster>0&&(g=H),!p&&H.helptower>0&&(p=H),f&&f.score<H.score&&(f=H),m&&m.killboss<H.killboss&&(m=H),g&&g.killmonster<H.killmonster&&(g=H),p&&p.helptower<H.helptower&&(p=H);case 62:C++,e.next=45;break;case 65:return this.heroUser.forEach((function(e){e&&e.userId&&(u.push(e.userId),h[e.userId]=e.rank,s.push({userId:e.userId,nickName:e.nickName,iconUrl:e.iconUrl}),e.giftKey.forEach((function(t){t.giftType&&a.push({userId:e.userId,giftType:t.giftType,num:t.num,price:t.price})})),l.push({score1:null,score2:null,time:Math.floor(W.tickTime),result:null,gold:e.gold,groupId:null,userId:e.userId,score:e.score||0,poolScore:e.poolScore}),1==M.gameData.gameType&&d.push({playerId:e.userId,key:"bossFight_"+M.gameData.gameTypeJsonId,value:e.bossChangeTimeTxt}))})),l=l.sort((function(e,t){return t.score-e.score})),console.log("bossFightTypeDatas",d),e.next=70,O.settleFight(M.gameData.combatId,s,a,l,0,0,this.gameLevel);case 70:return _=K.getInstance().getRankKeyByMonth(),V={},e.next=74,O.getRoleRankInfo(_,u,(function(e){e.forEach((function(e){var t=W.findUser(e.playerId);t&&(t.rank=e.rank,V[e.playerId]=e.rank,t.role&&(t.role.data.weekScore=e.score,t.role.checkSwitchSkin()))}))}),!1);case 74:q=K.getInstance().getLiverKeyByLev(),O.updateAnchorRankGameLev(q,M.gameData.baseInfo.id),1==M.gameData.gameType&&d.length>0&&O.setPlayersOperate(d),M.event.raiseEvent(w.GameOver,{isWin:t,isCross:v,userList:s,giftList:a,scoreList:l,maxLevel:this.gameLevel,firstFightUser:f,firstKillBossUser:m,firstKillMonsterUser:g,firstHelpTowerUser:p,corssTime:this.tickTime,totalUserNum:this.heroUser.size,oldWeekDatas:h,weekRankDatas:V}),e.next=85;break;case 80:e.prev=80,e.t0=e.catch(43),c("end err:::",e.t0),M.event.raiseEvent(w.GameOver,{isWin:t,isCross:v,userList:s,giftList:a,scoreList:l,maxLevel:this.gameLevel,firstFightUser:f,firstKillBossUser:m,firstKillMonsterUser:g,firstHelpTowerUser:p,corssTime:this.tickTime,totalUserNum:this.heroUser.size,oldWeekDatas:h}),U.log("error","fightover combatId:"+M.gameData.combatId+" err message:"+e.t0);case 85:case"end":return e.stop()}}),e,this,[[43,80]])})));return function(t){return e.apply(this,arguments)}}(),x.fightToReplay=function(){this.gameTotalScore=0,this.gameTotalGiftScore=0,this.gameTotalKillScore=0,this.giftPowerNum=0,this.towerTw=null,M.event.raiseEvent(w.GameScore),this.destroy()},x.destroy=function(){this.gameState=y.Pause,this.tickTime=0,this.attackTargets.clear();for(var e=0;e<this.heroRoles.length;e++)this.heroRoles[e].destroy(!0);for(var t=0;t<this.monsterRoles.count;t++){this.monsterRoles.get(t).destroy(!0)}for(var n=0;n<this.buffList.count;n++){this.buffList.get(n).destroy()}this.heroRoles=[],this.heroUser.clear(),this.monsterRoles.clear(),this.buffList.clear(),this.quadTree&&this.quadTree.clear(),this.isOnline||this.lastUser.clear(),this.tower&&this.tower.clearAllBuff()},x.getTime=function(){return Math.floor(this.tickTime)},x.refreshZindex=function(){var e=this.monsterParentNd.children.concat();e.sort((function(e,t){return t.position.y-e.position.y}));for(var t,n=e.length,r=o(e);!(t=r()).done;){t.value.setSiblingIndex(n)}},x.refreshHeroZindex=function(){var e=this.heroParentNd.children.concat();e.sort((function(e,t){return t.position.y-e.position.y}));for(var t,n=e.length,r=o(e);!(t=r()).done;){t.value.setSiblingIndex(n)}},x.checkTask=function(){var e=n(r().mark((function e(t){var n,o,i,s,a,l,c,u,h,d,f,m,g,p,v,k,y,I,S=this;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=K.getInstance().getTaskConfigByType(1),o=K.getInstance().getTaskConfigByType(2),i=K.getInstance().getTaskConfigByType(3),!(t.giftTitleScore>=n.giftScore)){e.next=9;break}return s=F.formatTimestampToDate(F.getServerTime(),"-"),e.next=7,O.getPlayerOperate(t.userId,"daytask");case 7:a=M.gameData.playersOperation[t.userId].daytask,s!=a&&(M.gameData.playersOperation[t.userId].daytask=s,O.setPlayerOperate(t.userId,"daytask",s),l=n.rewardBeadId.split("|"),c=n.rewardNum.split("|"),u=0,l.forEach((function(e,n){var r=Number(c[n])||1;u+=r,O.addDimond(t.userId,e,r),S.updateDimond(t.role,e,r)})),M.event.raiseEvent(w.GiftMessage,{type:T.TaskReward,name:t.nickName,avatar:t.iconUrl,totalNum:u,desc:i.name}));case 9:if(!(t.giftTitleScore>=o.giftScore)){e.next=15;break}return h=F.getFirstDayOfTheWeek(F.formatTimestampToDate(F.getServerTime(),"-")),e.next=13,O.getPlayerOperate(t.userId,"weektask");case 13:d=M.gameData.playersOperation[t.userId].weektask,h!=d&&(M.gameData.playersOperation[t.userId].weektask=h,O.setPlayerOperate(t.userId,"weektask",h),f=o.rewardBeadId.split("|"),m=o.rewardNum.split("|"),g=0,f.forEach((function(e,n){var r=Number(m[n])||1;g+=r,O.addDimond(t.userId,e,r),S.updateDimond(t.role,e,r)})),M.event.raiseEvent(w.GiftMessage,{type:T.TaskReward,name:t.nickName,avatar:t.iconUrl,totalNum:g,desc:i.name}));case 15:if(!(t.giftTitleScore>=i.giftScore)){e.next=22;break}return p=F.getFirstDayOfTheMonth(),console.log("monthT",p),e.next=20,O.getPlayerOperate(t.userId,"monthtask");case 20:v=M.gameData.playersOperation[t.userId].monthtask,p!=v&&(M.gameData.playersOperation[t.userId].monthtask=p,O.setPlayerOperate(t.userId,"monthtask",p),k=i.rewardBeadId.split("|"),y=i.rewardNum.split("|"),I=0,k.forEach((function(e,n){var r=Number(y[n])||1;I+=r,O.addDimond(t.userId,e,r),S.updateDimond(t.role,e,r)})),M.event.raiseEvent(w.GiftMessage,{type:T.TaskReward,name:t.nickName,avatar:t.iconUrl,totalNum:I,desc:i.name}));case 22:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),x.updateDimond=function(e,t,n){var r;if(e)switch(t){case"260001":e.data.dimonEffects.attackDmNum||(e.data.dimonEffects.attackDmNum=0),e.data.dimonEffects.attackDmNum+=n,r=K.getInstance().getBeadConfigByNum(t,e.data.dimonEffects.attackDmNum),e.data.dimonEffects.attackLev=r.beadLevel,e.data.dimonEffects.attack=Number(r.attack);break;case"260002":e.data.dimonEffects.skillcdDmNum||(e.data.dimonEffects.skillcdDmNum=0),e.data.dimonEffects.skillcdDmNum+=n,r=K.getInstance().getBeadConfigByNum(t,e.data.dimonEffects.skillcdDmNum),e.data.dimonEffects.skillcdLev=r.beadLevel,e.data.dimonEffects.skillcd=Number(r.weaponInterval);break;case"260003":e.data.dimonEffects.atkspeedDmNum||(e.data.dimonEffects.atkspeedDmNum=0),e.data.dimonEffects.atkspeedDmNum+=n,r=K.getInstance().getBeadConfigByNum(t,e.data.dimonEffects.atkspeedDmNum),e.data.dimonEffects.atkspeedLev=r.beadLevel,e.data.dimonEffects.atkspeed=Number(r.attackCooldown);break;case"260004":e.data.dimonEffects.atkmDmNum||(e.data.dimonEffects.atkmDmNum=0),e.data.dimonEffects.atkmDmNum+=n,r=K.getInstance().getBeadConfigByNum(t,e.data.dimonEffects.atkmDmNum),e.data.dimonEffects.atkmLev=r.beadLevel,e.data.dimonEffects.atkm=Number(r.criticalDamage);break;case"260005":e.data.dimonEffects.hpDmNum||(e.data.dimonEffects.hpDmNum=0),e.data.dimonEffects.hpDmNum+=n,r=K.getInstance().getBeadConfigByNum(t,e.data.dimonEffects.hpDmNum),e.data.dimonEffects.hpLev=r.beadLevel}},x.tick=function(e){switch(this.gameState){case y.None:break;case y.Gameing:if(this.rebuildTree(),this.tickTime+=e,this.nextRoundTime-=e,this.tickTime>=this.gameoverTime)return void this.fightOver(!1);this.tickTime%.5<=e&&(this.refreshZindex(),this.refreshHeroZindex(),this.monsterRoles.sort((function(e,t){var n,r;return(null==e||null==(n=e.comp)||null==(n=n.node)?void 0:n.position.y)-(null==t||null==(r=t.comp)||null==(r=r.node)?void 0:r.position.y)}))),this.heroUser.forEach((function(t){var n;return null==(n=t.role)?void 0:n.tick(e)}));for(var t=0;t<this.monsterRoles.count;t++){var n=this.monsterRoles.get(t);n&&n.tick(e)}for(var r=0;r<this.buffList.count;r++){var o=this.buffList.get(r);o&&o.tick(e)}this.nextRoundTime<=0&&(!this.newGameLevel||this.autoNextGameLevel&&!this.lotteryId&&!this.dimondId)?(this.tempeachCreateMonsterTime-=e,this.tempeachCreateMonsterTime<=0&&this.monsterNum<300&&this.createRoundMonsters()):(this.checkIfHaveBox(),!this.autoNextGameLevel&&this.newGameLevel&&(this.monsterNum<=0||this.monsterParentNd.children.length<=0)&&M.event.raiseEvent(w.NextGameLevel,this.newGameLevel)),this.tower&&this.tower.tick(e),this.towerPoints.length>0&&this.towerPoints.forEach((function(t){return t.tick(e)}));break;case y.Pause:break;case y.Resume:this.gameState=y.Gameing}},i}(x));i._RF.pop()}}}));

System.register("chunks:///_virtual/Game.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ConstGlobal.ts","./xcore.ts","./EffectMgr.ts","./FightMgr.ts","./GiftMgr.ts","./GiftMessageMgr.ts","./Tool.ts","./Net.ts","./GameSocketCtrl.ts","./ConfigHelper.ts","./Collection.ts"],(function(e){var t,n,i,r,a,o,s,l,c,u,h,f,d,b,g,m,p,v,y,w,_,M,S,G,T,k,I,C,D,R,L,E,z,B,U,K,x,N,P,O,A,V;return{setters:[function(e){t=e.applyDecoratedDescriptor,n=e.inheritsLoose,i=e.initializerDefineProperty,r=e.assertThisInitialized,a=e.asyncToGenerator,o=e.regeneratorRuntime},function(e){s=e.cclegacy,l=e._decorator,c=e.Node,u=e.Button,h=e.Label,f=e.Sprite,d=e.Graphics,b=e.Vec3,g=e.input,m=e.Input,p=e.KeyCode,v=e.log,y=e.size,w=e.TweenSystem,_=e.Tween,M=e.Component,S=e.tween,G=e.v3,T=e.instantiate},function(e){k=e.C_View,I=e.E_EVENT,C=e.E_GameState,D=e.E_Channel,R=e.C_Scene,L=e.C_GiftKey,E=e.C_Bundle},function(e){z=e.xcore},function(e){B=e.EffectMgr},function(e){U=e.FightMgr},function(e){K=e.GiftMgr},function(e){x=e.GiftMessageMgr},function(e){N=e.default},function(e){P=e.default},function(e){O=e.default},function(e){A=e.ConfigHelper},function(e){V=e.Collection}],execute:function(){var H,Y,J,F,W,j,Q,q,X,Z,$,ee,te,ne,ie,re,ae,oe,se,le,ce,ue,he,fe,de,be,ge,me,pe,ve,ye,we,_e,Me,Se,Ge,Te,ke,Ie,Ce,De,Re,Le,Ee,ze,Be,Ue,Ke,xe,Ne,Pe,Oe,Ae,Ve,He,Ye,Je,Fe,We,je,Qe,qe,Xe,Ze,$e,et,tt,nt,it,rt,at,ot,st,lt,ct,ut,ht,ft,dt,bt,gt,mt,pt,vt,yt,wt,_t,Mt,St,Gt,Tt,kt,It,Ct,Dt,Rt,Lt,Et,zt,Bt,Ut,Kt,xt,Nt,Pt,Ot,At,Vt,Ht,Yt,Jt,Ft,Wt,jt,Qt,qt,Xt,Zt,$t,en,tn,nn,rn,an,on,sn,ln,cn,un,hn,fn,dn,bn,gn,mn;s._RF.push({},"690a860s+hAeKYpJUeqXIhy","Game",void 0);var pn=l.ccclass,vn=l.property;e("Game",(H=pn("Game"),Y=vn(c),J=vn(c),F=vn(c),W=vn(c),j=vn(c),Q=vn(c),q=vn(c),X=vn(c),Z=vn(c),$=vn(c),ee=vn(c),te=vn(c),ne=vn(c),ie=vn(c),re=vn(c),ae=vn(u),oe=vn(u),se=vn(u),le=vn(u),ce=vn(u),ue=vn(u),he=vn(u),fe=vn(u),de=vn(u),be=vn(u),ge=vn(u),me=vn(u),pe=vn(u),ve=vn(u),ye=vn(u),we=vn(h),_e=vn(c),Me=vn(c),Se=vn(f),Ge=vn(f),Te=vn(u),ke=vn(h),Ie=vn(h),Ce=vn(h),De=vn(h),Re=vn(h),Le=vn(c),Ee=vn(c),ze=vn(h),Be=vn(h),Ue=vn(h),Ke=vn(h),xe=vn(f),Ne=vn(h),Pe=vn(f),Oe=vn(h),Ae=vn(u),Ve=vn(u),He=vn(u),Ye=vn(c),Je=vn(c),Fe=vn(c),We=vn(c),je=vn(c),Qe=vn(h),qe=vn(h),Xe=vn(u),Ze=vn(u),$e=vn(c),et=vn(h),tt=vn(d),H((rt=t((it=function(e){function t(){for(var t,n=arguments.length,a=new Array(n),o=0;o<n;o++)a[o]=arguments[o];return t=e.call.apply(e,[this].concat(a))||this,i(t,"ndUITop",rt,r(t)),i(t,"ndUIContent",at,r(t)),i(t,"ndUISelectMode",ot,r(t)),i(t,"ndUISelectGameType",st,r(t)),i(t,"ndContentTower",lt,r(t)),i(t,"ndCam",ct,r(t)),i(t,"ndContentHero",ut,r(t)),i(t,"ndContentMonster",ht,r(t)),i(t,"ndContentGiftTips",ft,r(t)),i(t,"ndContentBossOrSkillTips",dt,r(t)),i(t,"ndContentSkill",bt,r(t)),i(t,"ndContentHeroEffect",gt,r(t)),i(t,"ndContentMonsterEffect",mt,r(t)),i(t,"ndContentHurtEffect",pt,r(t)),i(t,"ndContentHurtTips",vt,r(t)),i(t,"btnSetting",yt,r(t)),i(t,"btnPlay",wt,r(t)),i(t,"btnPlay2",_t,r(t)),i(t,"btnMode",Mt,r(t)),i(t,"btnMode2",St,r(t)),i(t,"btnSelectLev",Gt,r(t)),i(t,"btnSkin",Tt,r(t)),i(t,"btnBack",kt,r(t)),i(t,"btnBack2",It,r(t)),i(t,"btnRank",Ct,r(t)),i(t,"btnPowerRank",Dt,r(t)),i(t,"btnAutoGameLevel",Rt,r(t)),i(t,"btnOnlineMode",Lt,r(t)),i(t,"btnTask",Et,r(t)),i(t,"btnGameSpeed",zt,r(t)),i(t,"lbGameSpeed",Bt,r(t)),i(t,"ndGameSpeedLeft",Ut,r(t)),i(t,"ndGameSpeedRight",Kt,r(t)),i(t,"sprTagAuto",xt,r(t)),i(t,"sprTagOnlineMode",Nt,r(t)),i(t,"btnTest",Pt,r(t)),i(t,"lbLev",Ot,r(t)),i(t,"lbMode",At,r(t)),i(t,"lbBossMode",Vt,r(t)),i(t,"lbTime",Ht,r(t)),i(t,"lbJoinNum",Yt,r(t)),i(t,"ndSpaceMonster",Jt,r(t)),i(t,"ndSpaceHero",Ft,r(t)),i(t,"lbGameTime",Wt,r(t)),i(t,"lbDesc",jt,r(t)),i(t,"lbRound",Qt,r(t)),i(t,"lbBossDesc",qt,r(t)),i(t,"sprBoss",Xt,r(t)),i(t,"lbMonsterDesc",Zt,r(t)),i(t,"sprMonster",$t,r(t)),i(t,"lbScore",en,r(t)),i(t,"btnMoveLeft",tn,r(t)),i(t,"btnMoveRight",nn,r(t)),i(t,"btnExchange",rn,r(t)),i(t,"ndRoundDetail",an,r(t)),i(t,"ndMonsterDetail",on,r(t)),i(t,"ndBossDetail",sn,r(t)),i(t,"ndMonsterSub",ln,r(t)),i(t,"ndBossSub",cn,r(t)),i(t,"lbMonsterSub",un,r(t)),i(t,"lbBossSub",hn,r(t)),i(t,"btnGameNextRoundStart",fn,r(t)),i(t,"btnCrossReward",dn,r(t)),i(t,"ndRoundStart",bn,r(t)),i(t,"lbGameNextRoundStart",gn,r(t)),i(t,"gp",mn,r(t)),t.gpT=.5,t._giftMgr=void 0,t._fightMgr=void 0,t._effectMgr=void 0,t._giftMessageMgr=void 0,t._gameoverTime=1e13,t._towerHp=void 0,t._isConnect=!1,t._shankTw=void 0,t._camTempPos=new b,t._downKeys=new V,t._upKeys=new V,t._createDebugNum=0,t._isCreateDebug=!1,t._gameSpeed=1,t._onlineMode=!1,t._reloadWsNum=0,t}n(t,e);var s=t.prototype;return s.onLoad=function(){this._giftMgr=new K,this._fightMgr=U.getInstance(),this._effectMgr=B.getInstance(),this._giftMessageMgr=x.getInstance(),this._effectMgr.setEffectParentNd(this.ndContentHeroEffect,this.ndContentMonsterEffect,this.ndContentHurtEffect,this.ndContentHurtTips),this._fightMgr.setRoleParentNd(this.ndContentHero,this.ndContentMonster,this.ndContentTower,this.ndContentSkill),this._fightMgr.setRoleMoveSpace(this.ndSpaceMonster,this.ndSpaceHero),this._giftMessageMgr.init(this.ndContentGiftTips,this.ndContentBossOrSkillTips),this.onRefreshGameConfig(),this.initUI(),this.gameSocketInit()},s.initUI=function(){var e=a(o().mark((function e(){var t;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.ndUITop.active=!1,this.ndUISelectMode.active=!1,this.ndUISelectGameType.active=!1,this.lbGameTime.string="未开始",this._fightMgr.initTower(),this.ndUITop.active=!0,0==z.gameData.gameType?this.ndUISelectMode.active=!0:this.ndUISelectGameType.active=!0,this.addListener(),this._fightMgr.gameState=C.Stop,this.sprTagAuto.node.active=!1,this._fightMgr.setAbleAutoNextGame(!1),this.sprTagOnlineMode.node.active=!1,this._fightMgr.setGameOnlineMode(!1),this._onlineMode=!1,!A.getInstance().getConstantConfigByKey("giftPicture")){e.next=21;break}return e.next=18,z.res.bundleLoadPrefab(E.abGame,"./prefab/UIGiftDesc");case 18:t=e.sent,T(t).parent=this.ndUIContent;case 21:S(this.ndRoundStart).repeatForever(S(this.ndRoundStart).to(.8,{scale:G(1.2,1.2)}).to(.8,{scale:G(1,1)})).start(),N.setChildrenNodeSortByPriority(this.ndContentBossOrSkillTips,2e3),z.channel==D.UHO&&(z.gameData.gameLev=120),0!=z.gameData.gameType&&(this.btnAutoGameLevel.node.active=!1,this.btnOnlineMode.node.active=!1);case 25:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}(),s.addListener=function(){var e=this;this.btnMode.node.on("click",(function(){z.ui.addView(k.ViewSelectGameMode)}),this),this.btnMode2.node.on("click",(function(){z.ui.addView(k.ViewSelectGameType)}),this),this.btnPlay.node.on("click",this.gameStart,this),this.btnPlay2.node.on("click",this.gameStart,this),this.btnBack.node.on("click",this.gameBackMain,this),this.btnBack2.node.on("click",this.gameBackMain,this),this.btnSkin.node.on("click",this.selectSkin,this),this.btnRank.node.on("click",(function(){z.ui.addView(k.ViewRank)}),this),this.btnPowerRank.node.on("click",(function(){z.ui.addView(k.ViewPowerRank)})),this.btnSelectLev.node.on("click",(function(){z.ui.addView(k.ViewSelectGameLevel)}),this),this.btnSetting.node.on("click",(function(){z.ui.addView(k.ViewSetting)}),this),this.btnExchange.node.on("click",(function(){z.ui.addView(k.ViewExchange)}),this),this.btnTest.node.on("click",(function(){z.ui.addView(k.ViewTestGift,{heroUser:e._fightMgr.heroUser,giftMgr:e._giftMgr})}),this),this.btnGameNextRoundStart.node.on("click",(function(){e._fightMgr.goNextGameLevel(),e.btnGameNextRoundStart.node.active=!1}),this),this.btnAutoGameLevel.node.on("click",(function(){var t=!e.sprTagAuto.node.active;e.sprTagAuto.node.active=t,e._fightMgr.setAbleAutoNextGame(t),t&&(e.btnGameNextRoundStart.node.active=!1)}),this),this.btnOnlineMode.node.on("click",(function(){var t=!e.sprTagOnlineMode.node.active;e.sprTagOnlineMode.node.active=t,e._onlineMode=t,e._fightMgr.setGameOnlineMode(t),t&&(e.sprTagAuto.node.active=t,e._fightMgr.setAbleAutoNextGame(t),t&&(e.btnGameNextRoundStart.node.active=!1))}),this),this.btnCrossReward.node.on("click",(function(){z.ui.addView(k.ViewCrossRewardDesc)}),this),this.btnTask.node.on("click",(function(){z.ui.addView(k.ViewTaskRewardDesc)}),this),this.btnGameSpeed.node.on("click",this.onClickGameSpeed,this),this.btnMoveLeft.node.on("click",this.checkRoleMove.bind(this,"left"),this),this.btnMoveRight.node.on("click",this.checkRoleMove.bind(this,"right"),this),z.event.addEventListener(I.BaseInfo,this.onRefreshUserInfo,this),z.event.addEventListener(I.GameStart,this.gameStart,this),z.event.addEventListener(I.GameOver,this.gameOver,this),z.event.addEventListener(I.GameReplay,this.gameToReplay,this),z.event.addEventListener(I.GameBack,this.gameBack,this),z.event.addEventListener(I.GameConfig,this.onRefreshGameConfig,this),z.event.addEventListener(I.Round,this.onRefreshRoundData,this),z.event.addEventListener(I.GameScore,this.onRefreshGameScore,this),z.event.addEventListener(I.GameLogin,this.onGameLogin,this),z.event.addEventListener(I.CrateMoreMonster,this.onCreateMonster,this),z.event.addEventListener(I.NextGameLevel,this.onShowNextGameLevelUI,this),z.event.addEventListener(I.GameOut,this.onGameOut,this),z.event.addEventListener(I.ShakeCam,this.Shake,this),g.on(m.EventType.KEY_DOWN,this.onKeyDown,this),g.on(m.EventType.KEY_UP,this.onKeyUp,this),this.onClickGameSpeed(!1)},s.onKeyDown=function(e){switch(this._downKeys.set(""+e.keyCode,e.keyCode),e.keyCode){case p.KEY_G:if(this._downKeys.has(""+p.ALT_LEFT)){if(this._isCreateDebug)return;this._createDebugNum+=1,this._createDebugNum>=5&&(this._isCreateDebug=!0,v("开启调试模式"))}break;case p.KEY_H:this._isCreateDebug&&this._downKeys.has(""+p.CTRL_LEFT)&&(v("随机刷机器人与礼物，用于测试性能"),this.testRandomCreateUser());break;case p.DIGIT_1:case p.DIGIT_2:case p.DIGIT_3:case p.DIGIT_4:case p.DIGIT_5:case p.DIGIT_6:case p.DIGIT_7:case p.DIGIT_8:case p.DIGIT_9:this.testAddUser(e.keyCode);break;case p.KEY_Q:case p.KEY_W:case p.KEY_E:case p.KEY_R:case p.KEY_T:case p.KEY_Y:case p.KEY_U:this.testAddGift(e.keyCode)}},s.onKeyUp=function(e){this._downKeys.delete(""+e.keyCode)},s.testAddUser=function(e){if(this._isCreateDebug){var t;if(e==p.DIGIT_1)t="a01";else if(e==p.DIGIT_2)t="a02";else if(e==p.DIGIT_3)t="a03";else if(e==p.DIGIT_4)t="a04";else if(e==p.DIGIT_5)t="a05";else if(e==p.DIGIT_6)t="a06";else if(e==p.DIGIT_7)t="a07";else if(e==p.DIGIT_8)t="a08";else{if(e!=p.DIGIT_9)return;t="a09"}this._giftMgr.addGifts([{userId:t,key:"1",num:1}])}},s.testAddGift=function(e){if(this._isCreateDebug){var t,n;if(this._downKeys.has(""+p.DIGIT_1))t="a01";else if(this._downKeys.has(""+p.DIGIT_2))t="a02";else if(this._downKeys.has(""+p.DIGIT_3))t="a03";else if(this._downKeys.has(""+p.DIGIT_4))t="a04";else if(this._downKeys.has(""+p.DIGIT_5))t="a05";else if(this._downKeys.has(""+p.DIGIT_6))t="a06";else if(this._downKeys.has(""+p.DIGIT_7))t="a07";else if(this._downKeys.has(""+p.DIGIT_8))t="a08";else{if(!this._downKeys.has(""+p.DIGIT_9))return;t="a09"}if(e==p.KEY_Q)n=L.Gift01;else if(e==p.KEY_W)n=L.Gift02;else if(e==p.KEY_E)n=L.Gift03;else if(e==p.KEY_R)n=L.Gift04;else if(e==p.KEY_T)n=L.Gift05;else if(e==p.KEY_Y)n=L.Gift06;else{if(e!=p.KEY_U)return;n=L.Like}this._giftMgr.addGifts([{userId:t,key:n,num:this._downKeys.has(""+p.CTRL_LEFT)?10:1}])}},s.testRandomCreateUser=function(){for(var e=N.randomNumber(4,10),t=0;t<e;t++){var n=N.guid(),i=["1","1","1","1","1","1","1","1","1","1","1","1","1","1","1","1","300001","300001","300001","300001","300001","300001",,"300001","300001","300001","300001","300001","300002","300002","300002","300002","300002","300002","300002","300002","300003","300003","300003","300003","300003","300004","300004","300004","300005","300005","300006","300006","300006","300006","300006","300007","300008"];i=N.randomSortArray(i);for(var r=0;r<3;r++){var a=i[r];this._giftMgr.addGifts([{userId:n,key:a,num:N.randomNumber(1,20)}])}}},s.removeListener=function(){this.btnMode.node.off("click"),this.btnMode2.node.off("click"),this.btnPlay.node.off("click"),this.btnPlay2.node.off("click"),this.btnBack.node.off("click"),this.btnBack2.node.off("click"),this.btnSkin.node.off("click"),this.btnSetting.node.off("click"),this.btnExchange.node.off("click"),this.btnRank.node.off("click"),this.btnPowerRank.node.off("click"),this.btnGameNextRoundStart.node.off("click"),this.btnAutoGameLevel.node.off("click"),this.btnGameSpeed.node.off("click"),this.btnMoveLeft.node.off("click"),this.btnMoveRight.node.off("click"),this.btnOnlineMode.node.off("click"),this.btnCrossReward.node.off("click"),z.event.removeEventListener(I.BaseInfo,this.onRefreshUserInfo,this),z.event.removeEventListener(I.GameStart,this.gameStart,this),z.event.removeEventListener(I.GameOver,this.gameOver,this),z.event.removeEventListener(I.GameReplay,this.gameToReplay,this),z.event.removeEventListener(I.GameBack,this.gameBack,this),z.event.removeEventListener(I.GameConfig,this.onRefreshGameConfig,this),z.event.removeEventListener(I.Round,this.onRefreshRoundData,this),z.event.removeEventListener(I.GameScore,this.onRefreshGameScore,this),z.event.removeEventListener(I.GameLogin,this.onGameLogin,this),z.event.removeEventListener(I.CrateMoreMonster,this.onCreateMonster,this),z.event.removeEventListener(I.NextGameLevel,this.onShowNextGameLevelUI,this),z.event.removeEventListener(I.GameOut,this.onGameOut,this),z.event.removeEventListener(I.ShakeCam,this.Shake,this),g.off(m.EventType.KEY_DOWN,this.onKeyDown,this),g.off(m.EventType.KEY_UP,this.onKeyUp,this)},s.GraphicsEffectRange=function(e,t){},s.onGameOut=function(){this._isConnect=!1,this._reloadWsNum<3?(this._reloadWsNum++,z.ui.showToast("正在重连中..."),this.gameSocketInit()):(this._reloadWsNum=0,this._fightMgr.setGameOverTime(0),z.ui.addView(k.ViewCommonTips,{desc:"您与服务器断开链接，请重启玩法"}))},s.onCreateMonster=function(e,t){this._fightMgr.createMonster(null,null,t.jsonId,t.pos)},s.onShowNextGameLevelUI=function(e,t){(!this.btnGameNextRoundStart.node.active&&this._fightMgr.gameState==C.Gameing||this._fightMgr.gameState==C.Resume)&&(this.btnGameNextRoundStart.node.active=!0,this.lbGameNextRoundStart.string="开始挑战第"+t+"关卡")},s.onRefreshGameConfig=function(){var e=["310001","310002","310003"][z.gameData.gameMode],t=A.getInstance().getDifficultyConfigByJsonId(e);if(this._towerHp=parseInt(t.Hp),0==z.gameData.gameType){if(!t)return z.ui.showToast("配置错误，无法找到"+e+" 难度配置"),void this.gameBack();this._gameoverTime=1e13,this.lbTime.string="通关时间："+this._gameoverTime+"秒",z.gameData.gameSelectLev||(z.gameData.gameSelectLev=1),this.lbLev.string="选择关卡："+z.gameData.gameSelectLev,this.lbMode.string="难度："+["容易","中等","困难"][z.gameData.gameMode],v("fightStartconfig",z.gameData.gameSelectLev,t,this._gameoverTime,this._towerHp)}else{var n=A.getInstance().getDungeonConfigByJsonId(z.gameData.gameTypeJsonId);console.log("bossmode:",z.gameData.gameMode,z.gameData.gameTypeJsonId,n);z.gameData.gameMode;this.lbBossMode.string="副本Boss:"+n.name}},s.onRefreshUserInfo=function(e,t){t.heroUserInfo&&(this.lbDesc.string="",this.lbJoinNum.string="参与人数："+(t.heroUserInfo.size||0))},s.onRefreshGameScore=function(){var e=this._fightMgr.getTotalScore();this.lbScore.string="积分池："+N.numberToTenThousand(e)},s.onRefreshRoundData=function(e,t){if(!t)return this.ndRoundDetail.active=!1,this.ndBossDetail.active=!1,void(this.ndMonsterDetail.active=!1);if(t.roundDesc&&(this.lbRound.string=["简单","中等","困难"][z.gameData.gameMode]+" "+t.roundDesc),t.monsterId){var n=A.getInstance().getMonsterConfigByJsonId(t.monsterId);if(!n)return;z.res.remoteLoadSprite(n.icon,this.sprMonster,y(90,90)),this.lbMonsterDesc.string=n.name+"\n剩余数量:"+t.leftNum,this.ndMonsterSub.active=!!t.monsterSub,this.lbMonsterSub.string=t.monsterSub||""}if(t.bossId){var i=A.getInstance().getMonsterConfigByJsonId(t.bossId);if(!i)return;A.getInstance().getAnimConfigByJsonId(i.moveAnimation).name;this.lbBossDesc.string=i.name+"\n"+t.bossRoundDesc,z.res.remoteLoadSprite(i.icon,this.sprBoss,y(90,90)),this.ndBossSub.active=!!t.bossSub,this.lbBossSub.string=t.bossSub||""}this.ndRoundDetail.active=!!t.roundDesc,this.ndBossDetail.active=!0,this.ndMonsterDetail.active=!0},s.gameSocketInit=function(){var e=z.channel==D.TIKTOK?"ws://barrage-game.xiaoyisz.com:10088/websocket":"ws://42.194.174.223:10088/websocket";v("gameSocketInit",e,z.channel),O.inst.connect(e)},s.gameStart=function(){var e=a(o().mark((function e(){return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this._isConnect||z.channel!=D.TIKTOK){e.next=4;break}return z.ui.showToast("链接失败"),this.gameBack(),e.abrupt("return");case 4:if(e.prev=4,this.btnPlay.interactable=!1,this.btnPlay2.interactable=!1,z.gameData.combatId){e.next=13;break}return e.next=10,P.getCombatId();case 10:return e.next=12,P.relogin(z.channel,z.gameData.channelId,z.gameData.token,null,null,z.gameData.appId,z.gameData.combatId);case 12:this._fightMgr.checkLastUser();case 13:return e.next=15,this._fightMgr.fightStart(this._gameoverTime,this._towerHp);case 15:this.ndUISelectMode.active=!1,this.ndUISelectGameType.active=!1,this.lbDesc.string="",this.btnPlay.interactable=!0,this.btnPlay2.interactable=!0,e.next=27;break;case 22:e.prev=22,e.t0=e.catch(4),console.log("error",e.t0),N.log("error",e.t0),this.gameBack();case 27:case"end":return e.stop()}}),e,this,[[4,22]])})));return function(){return e.apply(this,arguments)}}(),s.onGameLogin=function(e,t){z.channel==D.TIKTOK||z.channel==D.GAME560?this._isConnect=t:this._isConnect=!1},s.gameOver=function(){var e=a(o().mark((function e(t,n){return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this.onRefreshUserInfo(null,{heroNum:0,monsterNum:0,heroUserInfo:new Map}),this._giftMessageMgr.clear(),e.next=4,z.ui.closeAllView();case 4:this.scheduleOnce((function(){z.ui.addView(k.ViewGameOver,n)}),.1);case 5:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}(),s.gameToReplay=function(){var e=a(o().mark((function e(){var t=this;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this.btnPlay.node.active=!1,this.btnPlay2.node.active=!1,this.ndRoundDetail.active=!1,this.ndBossDetail.active=!1,this.ndMonsterDetail.active=!1,0==z.gameData.gameType?this.ndUISelectMode.active=!0:this.ndUISelectGameType.active=!0,this.btnGameNextRoundStart.node.active=!1,z.gameData.gameSelectLev||(z.gameData.gameSelectLev=1),this._onlineMode&&(z.gameData.gameSelectLev=1,this.ndUISelectMode.active=!1,this.ndUISelectGameType.active=!1),this.scheduleOnce((function(){t.btnPlay.node.active=!0,t.btnPlay2.node.active=!0}),1.2),this._fightMgr.fightToReplay(),this.onRefreshGameConfig(),this._fightMgr.initTower(),z.gameData.combatId=null,e.prev=14,e.next=17,P.getCombatId();case 17:return e.next=19,P.relogin(z.channel,z.gameData.channelId,z.gameData.token,null,null,z.gameData.appId,z.gameData.combatId);case 19:this.btnPlay.node.active=!0,this.btnPlay2.node.active=!0,this.ndContentSkill.children.length>0&&this.ndContentSkill.destroyAllChildren(),this.ndContentMonster.children.length>0&&this.ndContentMonster.destroyAllChildren(),this._fightMgr.checkLastUser((function(){t.gameStart()})),e.next=30;break;case 26:e.prev=26,e.t0=e.catch(14),this.btnOnlineMode.node.emit("click"),0==z.gameData.gameType?this.ndUISelectMode.active=!0:this.ndUISelectGameType.active=!0;case 30:case"end":return e.stop()}}),e,this,[[14,26]])})));return function(){return e.apply(this,arguments)}}(),s.Shake=function(){var e=this;w.instance.ActionManager.removeAllActionsFromTarget(this.ndCam),this._shankTw||(this._camTempPos.set(1,1,1),this._shankTw=new _(this.ndCam).call((function(t){t.setPosition(e._camTempPos)})).by(.05,{worldPosition:new b(10,10)}).by(.05,{worldPosition:new b(-20,-20)}).by(.05,{worldPosition:new b(10,10)})),this._shankTw.start()},s.onClickGameSpeed=function(e){void 0===e&&(e=!0),e&&(this._gameSpeed=1==this._gameSpeed?2:1),this.lbGameSpeed.string=this._gameSpeed.toString(),this.ndGameSpeedLeft.active=2==this._gameSpeed,this.ndGameSpeedRight.active=1==this._gameSpeed},s.gameBack=function(){O.inst.closeSocket(),this._giftMgr.destroy(),this._fightMgr.destroy(),this._effectMgr.destroy(),this._giftMessageMgr.destroy(),this.removeListener(),z.res.clearCache(),z.ui.switchScene(null,R.Ready)},s.gameBackMain=function(){O.inst.closeSocket(),this._giftMgr.destroy(),this._fightMgr.destroy(),this._effectMgr.destroy(),this._giftMessageMgr.destroy(),this.removeListener(),z.res.clearCache(),z.ui.switchScene(null,R.Main)},s.checkRoleMove=function(e){(v(e),"left"==e)?this._fightMgr.getHeroRoles().filter((function(e){return e.data.pos.x>=0})).forEach((function(e,t){(0==t||Math.random()<.5)&&e.moveLeft()})):this._fightMgr.getHeroRoles().filter((function(e){return e.data.pos.x<0})).forEach((function(e,t){(0==t||Math.random()<.5)&&e.moveRight()}))},s.selectSkin=function(){z.ui.addView(k.ViewSkinShow)},s.start=function(){var e=a(o().mark((function e(){return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),s.update=function(e){this._fightMgr&&(this._fightMgr.tick(e),2==this._gameSpeed&&this._fightMgr.tick(e),this.lbGameTime.string=this._fightMgr.getTime()+"/"+this._gameoverTime,this.gpT>0&&(this.gpT-=e,this.gpT<=0&&this.gp.clear()))},s.onDestroy=function(){this._giftMgr.destroy(),this._fightMgr.destroy()},t}(M)).prototype,"ndUITop",[Y],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),at=t(it.prototype,"ndUIContent",[J],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),ot=t(it.prototype,"ndUISelectMode",[F],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),st=t(it.prototype,"ndUISelectGameType",[W],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),lt=t(it.prototype,"ndContentTower",[j],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),ct=t(it.prototype,"ndCam",[Q],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),ut=t(it.prototype,"ndContentHero",[q],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),ht=t(it.prototype,"ndContentMonster",[X],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),ft=t(it.prototype,"ndContentGiftTips",[Z],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),dt=t(it.prototype,"ndContentBossOrSkillTips",[$],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),bt=t(it.prototype,"ndContentSkill",[ee],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),gt=t(it.prototype,"ndContentHeroEffect",[te],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),mt=t(it.prototype,"ndContentMonsterEffect",[ne],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),pt=t(it.prototype,"ndContentHurtEffect",[ie],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),vt=t(it.prototype,"ndContentHurtTips",[re],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),yt=t(it.prototype,"btnSetting",[ae],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),wt=t(it.prototype,"btnPlay",[oe],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),_t=t(it.prototype,"btnPlay2",[se],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Mt=t(it.prototype,"btnMode",[le],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),St=t(it.prototype,"btnMode2",[ce],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Gt=t(it.prototype,"btnSelectLev",[ue],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Tt=t(it.prototype,"btnSkin",[he],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),kt=t(it.prototype,"btnBack",[fe],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),It=t(it.prototype,"btnBack2",[de],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Ct=t(it.prototype,"btnRank",[be],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Dt=t(it.prototype,"btnPowerRank",[ge],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Rt=t(it.prototype,"btnAutoGameLevel",[me],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Lt=t(it.prototype,"btnOnlineMode",[pe],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Et=t(it.prototype,"btnTask",[ve],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),zt=t(it.prototype,"btnGameSpeed",[ye],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Bt=t(it.prototype,"lbGameSpeed",[we],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Ut=t(it.prototype,"ndGameSpeedLeft",[_e],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Kt=t(it.prototype,"ndGameSpeedRight",[Me],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),xt=t(it.prototype,"sprTagAuto",[Se],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Nt=t(it.prototype,"sprTagOnlineMode",[Ge],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Pt=t(it.prototype,"btnTest",[Te],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Ot=t(it.prototype,"lbLev",[ke],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),At=t(it.prototype,"lbMode",[Ie],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Vt=t(it.prototype,"lbBossMode",[Ce],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Ht=t(it.prototype,"lbTime",[De],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Yt=t(it.prototype,"lbJoinNum",[Re],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Jt=t(it.prototype,"ndSpaceMonster",[Le],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Ft=t(it.prototype,"ndSpaceHero",[Ee],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Wt=t(it.prototype,"lbGameTime",[ze],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),jt=t(it.prototype,"lbDesc",[Be],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Qt=t(it.prototype,"lbRound",[Ue],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),qt=t(it.prototype,"lbBossDesc",[Ke],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Xt=t(it.prototype,"sprBoss",[xe],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Zt=t(it.prototype,"lbMonsterDesc",[Ne],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),$t=t(it.prototype,"sprMonster",[Pe],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),en=t(it.prototype,"lbScore",[Oe],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),tn=t(it.prototype,"btnMoveLeft",[Ae],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),nn=t(it.prototype,"btnMoveRight",[Ve],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),rn=t(it.prototype,"btnExchange",[He],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),an=t(it.prototype,"ndRoundDetail",[Ye],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),on=t(it.prototype,"ndMonsterDetail",[Je],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),sn=t(it.prototype,"ndBossDetail",[Fe],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),ln=t(it.prototype,"ndMonsterSub",[We],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),cn=t(it.prototype,"ndBossSub",[je],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),un=t(it.prototype,"lbMonsterSub",[Qe],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),hn=t(it.prototype,"lbBossSub",[qe],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),fn=t(it.prototype,"btnGameNextRoundStart",[Xe],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),dn=t(it.prototype,"btnCrossReward",[Ze],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),bn=t(it.prototype,"ndRoundStart",[$e],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),gn=t(it.prototype,"lbGameNextRoundStart",[et],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),mn=t(it.prototype,"gp",[tt],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),nt=it))||nt));s._RF.pop()}}}));

System.register("chunks:///_virtual/GiftMessageMgr.ts",["./rollupPluginModLoBabelHelpers.js","cc","./Singleton.ts","./xcore.ts","./ConstGlobal.ts","./UnitGiftMessage.ts","./UnitBossMessage.ts","./UnitSkillUpMessage.ts","./UnitTowerPointShowMessage.ts","./UnitUserInfoMessage.ts","./UnitSkinRewardMessage.ts","./UnitTaskRewardMessage.ts"],(function(s){var e,t,i,n,a,r,o,g,h,l,f,M,u,d,c,k,w;return{setters:[function(s){e=s.inheritsLoose,t=s.asyncToGenerator,i=s.regeneratorRuntime},function(s){n=s.cclegacy,a=s.instantiate},function(s){r=s.Singleton},function(s){o=s.xcore},function(s){g=s.E_EVENT,h=s.E_GiftMessageType,l=s.C_Bundle},function(s){f=s.UnitGiftMessage},function(s){M=s.UnitBossMessage},function(s){u=s.UnitSkillUpMessage},function(s){d=s.UnitTowerPointShowMessage},function(s){c=s.UnitUserInfoMessage},function(s){k=s.UnitSkinRewardMessage},function(s){w=s.UnitTaskRewardMessage}],execute:function(){n._RF.push({},"a8c73YdO99Bk4CXc+aCOddv","GiftMessageMgr",void 0);s("GiftMessageMgr",function(s){function n(){for(var e,t=arguments.length,i=new Array(t),n=0;n<t;n++)i[n]=arguments[n];return(e=s.call.apply(s,[this].concat(i))||this).ndContent=null,e.ndContent2=null,e.giftUnitMessagePool=[],e.towerpointUnitMessage=null,e.skillUnitMessage=null,e.bossUnitMessage=null,e.userinfoUnitMessage=null,e.skinrewardUnitMessage=null,e.taskrewardUnitMessage=null,e.giftMessages00=[],e.giftMessages01=[],e.giftMessages02=[],e.giftMessages03=[],e.giftMessages04=[],e.bossOrSkillMessage=[],e.skinrewardMessage=[],e.taskrewardMessage=[],e.isBossShowMsg=!1,e.isSkillUpShowMsg=!1,e.isUserinfoShowMsg=!1,e.isSkinRewardShowMsg=!1,e.isTaskRewardShowMsg=!1,e._interval=void 0,e._syncRate=1.8,e._addIndex=0,e}e(n,s);var r=n.prototype;return r.init=function(s,e){var t=this;this.giftUnitMessagePool=[],this.ndContent=s,this.ndContent2=e,o.event.addEventListener(g.GiftMessage,this.addMessage,this),this._interval=setInterval((function(){t.showMessage()}),1e3*this._syncRate)},r.clear=function(){this.giftMessages00.length=0,this.giftMessages01.length=0,this.giftMessages02.length=0,this.giftMessages03.length=0,this.giftMessages04.length=0,this.bossOrSkillMessage.length=0,this.skinrewardMessage.length=0,this.taskrewardMessage.length=0,this.isBossShowMsg=!1,this.isSkillUpShowMsg=!1,this.isUserinfoShowMsg=!1,this.isSkinRewardShowMsg=!1,this.isTaskRewardShowMsg=!1,this.ndContent.destroyAllChildren()},r.destroy=function(){this.clear(),this._interval&&clearInterval(this._interval),o.event.removeEventListener(g.GiftMessage,this.addMessage,this)},r.addMessage=function(){var s=t(i().mark((function s(e,t){var n,a,r=this;return i().wrap((function(s){for(;;)switch(s.prev=s.next){case 0:t.type==h.GetBigSkill?n=this.giftMessages00:t.type==h.RankBoss||t.type==h.SkillUp?n=this.bossOrSkillMessage:t.type==h.GetSmallSkill?n=this.giftMessages01:t.type==h.Gift?n=this.giftMessages02:t.type==h.UseProp?n=this.giftMessages03:t.type==h.UserInfo?n=this.giftMessages04:t.type==h.SkinReward?n=this.skinrewardMessage:t.type==h.TaskReward?n=this.taskrewardMessage:(n=0==this._addIndex?this.giftMessages01:1==this._addIndex?this.giftMessages02:this.giftMessages03,this._addIndex+=1,this._addIndex>=3&&(this._addIndex=0)),(a=n.find((function(s){return r.checkSame(s,t)})))?(a.num+=t.num,a.totalNum=t.totalNum):n.push(t);case 3:case"end":return s.stop()}}),s,this)})));return function(e,t){return s.apply(this,arguments)}}(),r.showMessage=function(){var s=t(i().mark((function s(){var e,t,n,a;return i().wrap((function(s){for(;;)switch(s.prev=s.next){case 0:this.giftMessages01.length>0&&(e=this.giftMessages01.shift(),this.getUnitMessage(e,2)),this.giftMessages02.length>0&&(t=this.giftMessages02.shift(),this.getUnitMessage(t,1)),this.giftMessages03.length>0&&(n=this.giftMessages03.shift(),this.getUnitMessage(n,0)),this.giftMessages00.length>0&&(a=this.giftMessages00.shift(),this.getUnitMessage(a,0)),this.showBossOrSkillMessage(),this.showUserInfoMessage(),this.showSkinRewardMessage(),this.showTaskRewardMessage();case 8:case"end":return s.stop()}}),s,this)})));return function(){return s.apply(this,arguments)}}(),r.showBossOrSkillMessage=function(){if(this.bossOrSkillMessage.length>0){if(this.isBossShowMsg||this.isSkillUpShowMsg)return;var s=this.bossOrSkillMessage.shift();s&&this.getUnitBossOrSkillMessage(s)}},r.showUserInfoMessage=function(){if(this.giftMessages04.length>0){if(this.isUserinfoShowMsg)return;var s=this.giftMessages04.shift();this.getUserInfoMessage(s)}},r.showSkinRewardMessage=function(){if(this.skinrewardMessage.length>0){if(this.isSkinRewardShowMsg)return;var s=this.skinrewardMessage.shift();s&&this.getSkinRewardMessage(s)}},r.showTaskRewardMessage=function(){if(this.taskrewardMessage.length>0){if(this.isTaskRewardShowMsg)return;var s=this.taskrewardMessage.shift();s&&this.getTaskRewardMessage(s)}},r.checkSame=function(s,e){switch(e.type){case h.Gift:return s.giftType==e.giftType;case h.GetSmallSkill:return s.skillType==e.skillType;case h.GetBigSkill:return!1;case h.UseProp:return s.skillType==e.skillType;case h.Kill:return s.userId==e.userId;default:return!1}},r.killMessage=function(s){this.giftUnitMessagePool.push(s)},r.killBossMessage=function(s){this.isBossShowMsg=!1,this.showBossOrSkillMessage()},r.killTowerPointMessage=function(s){this.isSkillUpShowMsg=!1,this.showBossOrSkillMessage()},r.killSkillMessage=function(s){this.isSkillUpShowMsg=!1,this.showBossOrSkillMessage()},r.killUserInfoMessage=function(s){this.isUserinfoShowMsg=!1,this.showUserInfoMessage()},r.killSkinRewardMessage=function(s){this.isSkinRewardShowMsg=!1,this.showSkinRewardMessage()},r.killTaskRewardMessage=function(s){this.isTaskRewardShowMsg=!1,this.showTaskRewardMessage()},r.getUnitMessage=function(){var s=t(i().mark((function s(e,t){var n,r;return i().wrap((function(s){for(;;)switch(s.prev=s.next){case 0:if(this.giftUnitMessagePool||(this.giftUnitMessagePool=[]),n=null,!(this.giftUnitMessagePool.length<=0)){s.next=9;break}return s.next=5,o.res.bundleLoadPrefab(l.abGame,"./prefab/Unit/UnitGiftMessage");case 5:r=s.sent,n=a(r).getComponent(f),s.next=10;break;case 9:n=this.giftUnitMessagePool.shift();case 10:return n.node.parent=this.ndContent,n.setData(e,t),s.abrupt("return",n);case 13:case"end":return s.stop()}}),s,this)})));return function(e,t){return s.apply(this,arguments)}}(),r.getUserInfoMessage=function(){var s=t(i().mark((function s(e){var t;return i().wrap((function(s){for(;;)switch(s.prev=s.next){case 0:if(this.userinfoUnitMessage){s.next=5;break}return s.next=3,o.res.bundleLoadPrefab(l.abGame,"./prefab/Unit/UnitUserInfoMessage");case 3:t=s.sent,this.userinfoUnitMessage=a(t).getComponent(c);case 5:this.isUserinfoShowMsg=!0,this.userinfoUnitMessage.node.parent=this.ndContent2,this.userinfoUnitMessage.setData({type:h.UserInfo,userId:e.userId,name:e.name,avatar:e.avatar,skinId:e.skinId,minAtkNum:e.minAtkNum,maxAtkNum:e.maxAtkNum,dimonEffects:e.dimonEffects,speed:e.speed});case 8:case"end":return s.stop()}}),s,this)})));return function(e){return s.apply(this,arguments)}}(),r.getSkinRewardMessage=function(){var s=t(i().mark((function s(e){var t;return i().wrap((function(s){for(;;)switch(s.prev=s.next){case 0:if(this.skinrewardUnitMessage){s.next=5;break}return s.next=3,o.res.bundleLoadPrefab(l.abGame,"./prefab/Unit/UnitSkinRewardMessage");case 3:t=s.sent,this.skinrewardUnitMessage=a(t).getComponent(k);case 5:this.isSkinRewardShowMsg=!0,this.skinrewardUnitMessage.node.parent=this.ndContent2,this.skinrewardUnitMessage.setData({type:h.SkinReward,skinId:e.skinId,user:e.user,level:e.lev});case 8:case"end":return s.stop()}}),s,this)})));return function(e){return s.apply(this,arguments)}}(),r.getTaskRewardMessage=function(){var s=t(i().mark((function s(e){var t;return i().wrap((function(s){for(;;)switch(s.prev=s.next){case 0:if(this.taskrewardUnitMessage){s.next=5;break}return s.next=3,o.res.bundleLoadPrefab(l.abGame,"./prefab/Unit/UnitTaskRewardMessage");case 3:t=s.sent,this.taskrewardUnitMessage=a(t).getComponent(w);case 5:this.isTaskRewardShowMsg=!0,this.taskrewardUnitMessage.node.parent=this.ndContent2,this.taskrewardUnitMessage.setData({type:h.TaskReward,avatar:e.avatar,nickname:e.name,totalNum:e.totalNum,desc:e.desc});case 8:case"end":return s.stop()}}),s,this)})));return function(e){return s.apply(this,arguments)}}(),r.getUnitBossOrSkillMessage=function(){var s=t(i().mark((function s(e){var t,n,r;return i().wrap((function(s){for(;;)switch(s.prev=s.next){case 0:if(e.type!=h.RankBoss){s.next=10;break}return this.isBossShowMsg=!0,s.next=4,o.res.bundleLoadPrefab(l.abGame,"./prefab/Unit/UnitBossMessage");case 4:t=s.sent,this.bossUnitMessage=a(t).getComponent(M),this.bossUnitMessage.node.parent=this.ndContent2,this.bossUnitMessage.setData(e),s.next=29;break;case 10:if(e.type!=h.SkillUp){s.next=29;break}if(this.isSkillUpShowMsg=!0,!e.towerPointType){s.next=22;break}if(this.towerpointUnitMessage){s.next=18;break}return s.next=16,o.res.bundleLoadPrefab(l.abGame,"./prefab/Unit/UnitTowerPointShowMessage");case 16:n=s.sent,this.towerpointUnitMessage=a(n).getComponent(d);case 18:this.towerpointUnitMessage.node.parent=this.ndContent2,this.towerpointUnitMessage.setData({fromLev:e.fromLev,anim:e.anim,lev:e.lev,type:e.towerPointType}),s.next=29;break;case 22:if(this.skillUnitMessage){s.next=27;break}return s.next=25,o.res.bundleLoadPrefab(l.abGame,"./prefab/Unit/UnitSkillUpMessage");case 25:r=s.sent,this.skillUnitMessage=a(r).getComponent(u);case 27:this.skillUnitMessage.node.parent=this.ndContent2,this.skillUnitMessage.setData({message:e});case 29:case"end":return s.stop()}}),s,this)})));return function(e){return s.apply(this,arguments)}}(),n}(r));n._RF.pop()}}}));

System.register("chunks:///_virtual/GiftMgr.ts",["cc","./ConstGlobal.ts","./FightMgr.ts","./xcore.ts","./ConfigHelper.ts","./App.ts"],(function(e){var t,i,n,a,s,r,c,o,f,g,h;return{setters:[function(e){t=e.cclegacy,i=e.log},function(e){n=e.E_EVENT,a=e.C_HeroGiftKeyToSkillType,s=e.C_GiftKey,r=e.E_RoleType,c=e.E_GiftMessageType},function(e){o=e.FightMgr},function(e){f=e.xcore},function(e){g=e.ConfigHelper},function(e){h=e.default}],execute:function(){t._RF.push({},"9ae90knsW1JVLJ5uD6o4bsn","GiftMgr",void 0);e("GiftMgr",function(){function e(){this._fightMgr=void 0,this._syncRate=10,this._interval=void 0,this._gifts=[],this._waitGifts=[],this._gifts=[],this._fightMgr=o.getInstance(),this.addListener()}var t=e.prototype;return t.addListener=function(){this._interval=setInterval(this.syncGift.bind(this),1e3/this._syncRate),f.event.addEventListener(n.Gift,this.onRefreshGifts,this)},t.removeListener=function(){this._interval&&clearInterval(this._interval),f.event.removeEventListener(n.Gift,this.onRefreshGifts,this)},t.onRefreshGifts=function(e,t){i("onRefreshGifts:",t);for(var n=[],a=function(){var e=t[r],i={};switch(e.msg_type){case"live_comment":if("1"==e.content)i={key:s.JoinHero,userId:e.sec_openid,avatar:e.avatar_url,nickName:e.nickname,num:1};else if("666"==e.content){var a=g.getInstance().getGiftJsonIdByType("2");a&&(i={key:a,userId:e.sec_openid,avatar:e.avatar_url,nickName:e.nickname,num:1})}else if(-1!==e.content.indexOf("换")){i={key:g.getInstance().getSkinJsonIdByContent(e.content)||s.Skin1,userId:e.sec_openid,avatar:e.avatar_url,nickName:e.nickname,num:1}}else if("左"==e.content||"右"==e.content)i={key:"左"==e.content?s.MoveLeft:s.MoveRight,userId:e.sec_openid,avatar:e.avatar_url,nickName:e.nickname,num:1};else if("宝珠"==e.content||"属性"==e.content)i={key:s.Baozhu,userId:e.sec_openid,avatar:e.avatar_url,nickName:e.nickname,num:1};else if(-1!==e.content.indexOf("兑换")){console.log("兑换",e.content);for(var c,o=[],f=[],h=g.getInstance().getDungeonConfigs(),u=g.getInstance().getExchangeConfigs(),k=0;k<h.length;k++){var d=h[k];o.push(d.name)}for(var v=0;v<u.length;v++){var m=u[v];"470001"==m.exchangeTypeId&&f.push(m.name)}o.forEach((function(t){-1!==e.content.indexOf(t)&&(c=t)})),f.forEach((function(t){-1!==e.content.indexOf(t)&&(c=t)})),c&&(i={key:s.ExchangeBossFight,content:c,userId:e.sec_openid,avatar:e.avatar_url,nickName:e.nickname,num:1},console.log("兑换副本次数 "))}break;case"live_like":var l=g.getInstance().getGiftJsonIdByType("1");l&&(i={key:l,userId:e.sec_openid,avatar:e.avatar_url,nickName:e.nickname,num:1});break;case"live_gift":var _=g.getInstance().getGiftJsonIdByGiftId(e.sec_gift_id);_&&(i={key:_,userId:e.sec_openid,avatar:e.avatar_url,nickName:e.nickname,num:e.gift_num})}i.key&&n.push(i),i=null},r=0;r<t.length;r++)a();this.addGifts(n)},t.syncGift=function(){if(this._fightMgr.checkIfAbleGift()){var e=this._gifts;this._gifts=[],this.emitGifts(e),h.getInstance().updateOperation()}},t.addGifts=function(e){this._gifts=this._gifts.concat(e),i("addGifts:",e),f.gameData.giftData||(f.gameData.giftData={}),f.gameData.giftData[f.gameData.combatId]||(f.gameData.giftData[f.gameData.combatId]=[]),f.gameData.giftData[f.gameData.combatId]=f.gameData.giftData[f.gameData.combatId].concat(e)},t.destroy=function(){this.removeListener()},t.getSkilTypeByGiftKey=function(e){return a[e]},t.emitGifts=function(e){for(var t=0;t<e.length;t++){var a=e[t];switch(a.key){case s.JoinMonster:case s.JoinHero:this._fightMgr.addUser(a.userId,r.Hero,a.nickName,a.avatar);break;case s.Like:case s.SixSix:case s.Gift01:case s.Gift02:case s.Gift03:case s.Gift04:case s.Gift05:case s.Gift06:var o=this.checkIfJoinGroup(a);if(!o)return;var h=void 0;o.type==r.Hero&&(h=this.getSkilTypeByGiftKey(a.key),this._fightMgr.addGift(o,a.key,h,a.num)),a.key==s.Gift01&&this._fightMgr.updateTowerPoint(a.num),a.key==s.Gift04&&this._fightMgr.upLevTower(a.num);break;case s.Skin1:case s.Skin2:case s.Skin3:case s.Skin4:case s.Skin5:case s.Skin6:case s.Skin7:case s.Skin8:case s.Skin9:case s.Skin10:case s.Skin11:case s.Skin12:case s.Skin13:case s.Skin14:case s.Skin15:case s.Skin16:case s.Skin17:case s.Skin18:case s.Skin19:case s.Skin20:case s.Skin21:case s.Skin22:case s.Skin23:case s.Skin24:case s.Skin25:var u=this.checkIfJoinGroup(a,!0);if(!u)return void i("换肤失败,请先加入游戏");var k=g.getInstance().getSkinJsonIdByContent(a.key);this._fightMgr.doSwitchSkin(u,k,a.key);break;case s.MoveLeft:case s.MoveRight:var d,v=this.checkIfJoinGroup(a,!0);if(!v)return;if(a.key==s.MoveRight)null==(d=v.role)||d.moveRight();else if(a.key==s.MoveLeft){var m;null==(m=v.role)||m.moveLeft()}break;case s.Baozhu:var l=this.checkIfJoinGroup(a,!0);if(!l)return void i("换肤失败,请先加入游戏");f.event.raiseEvent(n.GiftMessage,{type:c.UserInfo,userId:l.userId,name:l.nickName,avatar:l.iconUrl,minAtkNum:l.role.data.minAtkNum,maxAtkNum:l.role.data.maxAtkNum,skinId:l.role.data.skinId,dimonEffects:l.role.data.dimonEffects,speed:l.role.getAtkSpeed()});break;case s.ExchangeBossFight:console.log("gift.Exchange:",a),this._fightMgr.checkLingyunExchange(a.userId,r.Hero,a.nickName,a.avatar,a.content);break;case s.ExchangeWing:console.log("gift.Exchange2:",a),this._fightMgr.checkExchangeWing(a.userId,a.content);break;default:if(-1!==a.key.indexOf("换")){var _=this.checkIfJoinGroup(a,!0);if(!_)return;this._fightMgr.doSwitchSkin(_,a.key)}}}},t.checkIfJoinGroup=function(e,t){if(void 0===t&&(t=!1),!this._fightMgr.checkIfAbleGift())return i("游戏暂未开始 "+e.key+"指令无法生效"),this.backToWaitGiftGroup({userId:e.userId,key:e.key,num:e.num}),null;var n=this._fightMgr.findUser(e.userId);if(!n){if(t)return null;this._fightMgr.addUser(e.userId,r.Hero,e.nickName,e.avatar),n=this._fightMgr.findUser(e.userId)}return n},t.backToWaitGiftGroup=function(e){this._waitGifts.push(e)},e}());t._RF.pop()}}}));

System.register("chunks:///_virtual/LightningChain.ts",["./rollupPluginModLoBabelHelpers.js","cc"],(function(t){var i,n,e,o,r,a,s,c,h,l,g,u;return{setters:[function(t){i=t.applyDecoratedDescriptor,n=t.inheritsLoose,e=t.initializerDefineProperty,o=t.assertThisInitialized},function(t){r=t.cclegacy,a=t._decorator,s=t.Node,c=t.Prefab,h=t.Vec3,l=t.instantiate,g=t.v3,u=t.Component}],execute:function(){var p,f,d,y,P,v,b;r._RF.push({},"f92d699nhdOi4pmR+EEn7/Z","LightningChain",void 0);var N=a.ccclass,m=a.property;t("LightningChain",(p=N("LightningChain"),f=m(s),d=m(c),p((v=i((P=function(t){function i(){for(var i,n=arguments.length,r=new Array(n),a=0;a<n;a++)r[a]=arguments[a];return i=t.call.apply(t,[this].concat(r))||this,e(i,"pointsNode",v,o(i)),e(i,"lightPrefab",b,o(i)),i.lightNodes=[],i}n(i,t);var r=i.prototype;return r.start=function(){this.createLightningByPoints()},r.getRandom=function(t,i){return Math.random()*(i-t)+t},r.onClick_updatePointPos=function(){var t=this;this.pointsNode.children.forEach((function(i){var n=i.position.clone();n.x=t.getRandom(-500,500),n.y=t.getRandom(-500,500),i.setPosition(n)})),this.createLightningByPoints()},r.createLightningByPoints=function(){var t=this;this.lightNodes.forEach((function(t){t.destroy()})),this.lightNodes=[],this.pointsNode.children.forEach((function(i,n){var e=i.position.clone();t.pointsNode.children.forEach((function(i,o){var r=i.position.clone();n+1==o&&t.lightning(e,r)}))}))},r.lightning=function(t,i){var n=h.distance(t,i),e=i.subtract(t),o=180*Math.atan2(e.y,e.x)/Math.PI,r=l(this.lightPrefab);r.setParent(this.node.parent),r.setPosition(t),r.eulerAngles=g(0,0,o),r.getChildByPath("effect").scale=g(1,n/100,1),this.lightNodes.push(r)},i}(u)).prototype,"pointsNode",[f],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),b=i(P.prototype,"lightPrefab",[d],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),y=P))||y));r._RF.pop()}}}));

System.register("chunks:///_virtual/Role.ts",["./rollupPluginModLoBabelHelpers.js","cc","./RoleData.ts","./ConstGlobal.ts","./UnitRoleComp.ts","./xcore.ts","./FightMgr.ts","./UnitBuffCoin.ts","./UnitTowerPoint.ts","./ConfigHelper.ts","./Net.ts"],(function(t){var a,e,i,s,n,o,d,h,r,c,m,u,f,l,p,k,v,S,T,g,I,b,A,y,N,B,w,H,x,E,C;return{setters:[function(t){a=t.createForOfIteratorHelperLoose,e=t.asyncToGenerator,i=t.regeneratorRuntime},function(t){s=t.cclegacy,n=t.log,o=t.v3,d=t.Vec3,h=t.v2,r=t.warn,c=t.Vec2,m=t.view,u=t.instantiate},function(t){f=t.RoleData},function(t){l=t.C_View,p=t.E_RoleState,k=t.E_SkinType,v=t.E_RoleType,S=t.E_EVENT,T=t.E_MonsterTag,g=t.E_SkillType,I=t.E_GiftMessageType,b=t.C_GiftSkill,A=t.E_MonsterType,y=t.C_Bundle},function(t){N=t.UnitRoleComp},function(t){B=t.xcore},function(t){w=t.FightMgr},function(t){H=t.UnitBuffCoin},function(t){x=t.UnitTowerPoint},function(t){E=t.ConfigHelper},function(t){C=t.default}],execute:function(){s._RF.push({},"6b053NVnblAUZWgWBV0uv/u","Role",void 0);t("Role",function(){function t(){this.data=void 0,this.comp=void 0,this.skills=new Map,this.buffCons=new Map,this.towerPosY=void 0,this.timeoutBackState=void 0,this.isInAttackDistance=!1,this.atkTime=0,this.skillAnimCb=null,this.needSwitchSkinId=null,this.tempTime=1,this.tempAddTowerHp=0}var s=t.prototype;return s.add=function(){var t=e(i().mark((function t(a,e,s,d,u,l,k,S){var T,g,I,b,y,N,H,x,C,M,R,P,D,L,U,z,G,J,j,W,K,_,V,Y;return i().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(void 0===d&&(d=1),T=m.getVisibleSize().width/2-60,g=m.getVisibleSize().height/2-100,!this.data&&(this.data=new f),this.towerPosY=w.getInstance().towerParentNd.position.y-160,this.data.tickTime=0,this.data.userId=a,this.data.nickName=l,this.data.iconUrl=k,this.data.type=e,this.setState(p.Idle),this.data.lev=d,this.data.skillWeakIds=[],this.data.isInvincible=!1,this.isInAttackDistance=!1,this.data.isLongMonster=!1,this.data.moveLeft=w.getInstance().monsterSpace.left,this.data.moveRight=w.getInstance().monsterSpace.right,this.tempTime=0,this.data.defenseNum=0,this.data.atkOffsetPos?this.data.atkOffsetPos.set(0,0):this.data.atkOffsetPos=h(0,0),this.data.dimonEffects||(this.data.dimonEffects={attack:0,attackLev:0,attackDmNum:0,atkspeed:0,atkspeedLev:0,atkspeedDmNum:0,skillcd:0,skillcdLev:0,skillcdDmNum:0,atkr:0,atkrLev:0,atkm:0,atkmLev:0,atkmDmNum:0,hp:0,hpLev:0,hpDmNum:0,baseAtkM:1}),e!=v.MonsterPoint){t.next=32;break}return I=-T+2*T*Math.random(),this.data.pos=h(I,80-g),t.next=27,this.createRoleComp(e);case 27:this.comp=t.sent,this.comp.init(this),this.comp.setPosAtonce(new c(this.data.pos.x,this.data.pos.y-200)),t.next=179;break;case 32:if(e!=v.Tower){t.next=41;break}return t.next=35,this.createRoleComp(e);case 35:this.comp=t.sent,this.comp.init(this),this.tempAddTowerHp=0,n("init tower"),t.next=179;break;case 41:if(e!=v.TowerPoint){t.next=95;break}if(!d){t.next=46;break}b=E.getInstance().getTowerPointByType(s,d),t.next=52;break;case 46:if(!S){t.next=50;break}b=E.getInstance().getTowerPointByJsonId(S),t.next=52;break;case 50:return r("炮台配置错误"),t.abrupt("return");case 52:if(b){t.next=55;break}return r("TowerPoint config null",s,d,S),t.abrupt("return");case 55:return y=E.getInstance().getEffectConfigByJsonId(b.attacKeffect),N=E.getInstance().getAnimConfigByJsonId(y.Animation),w.getInstance().towerPointUpGiftNum=b.douyinGiftNum,this.data.jsonId=b.jsonId,this.data.monsterType=s,H=b.attack.split("|"),this.data.minAtkNum=parseInt(H[0]||0),this.data.maxAtkNum=parseInt(H[1]||0),this.data.atkSpeed=b.attackCooldown,this.data.maxHp=b.monsterHp,this.data.hpNum=b.monsterHp,this.data.moveAnimation=b.moveAnimation,this.data.attackAnimation=b.attackAnimation,this.data.attackEffect=b.attacKeffect,this.data.moveSpeed=b.movementSpeed,this.data.attacKeffectInterval=1e3*b.attacKeffectInterval,this.data.monsterTag=b.monsterRefreshType,this.data.atkAnimJsonId=y.Animation,this.data.atkAnimTargetType=N.target,this.data.atkStyle=y.type,this.data.monsterName=b.name,this.data.hurtEffect=y.impactAnimation,x=.8+.4*Math.random(),this.data.atkRag=y.attacDistance*x,this.data.atkPropNum=y.doubleHit,this.data.pos=u,this.data.skillAnimation=b.skillAnimation,this.data.monsterScore=b.monsterScore,C=Number(b.animationScale)||1,this.data.scale=o(C,C,C),this.data.moveLeft=u.x-70,this.data.moveRight=u.x+70,t.next=89,this.createRoleComp(e);case 89:this.comp=t.sent,this.comp.setPosAtonce(this.data.pos),this.comp.init(this),b.skill&&((M=E.getInstance().getSkillConfigByJsonId(b.skill))?(this.createSkill(M.type,1),n("炮台 skillId:",M,b.skill)):r("skill null",b.skill)),t.next=179;break;case 95:if(e!=v.Hero){t.next=134;break}if(R=E.getInstance().getHeroConfigByJsonId(this.data.skinId)){t.next=100;break}return r("Hero skin config null",this.data.skinId),t.abrupt("return");case 100:return P=E.getInstance().getEffectConfigByJsonId(R.attacKeffect),D=E.getInstance().getAnimConfigByJsonId(P.Animation),n("hero config:",R),this.data.jsonId=R.jsonId,L=R.attack.split("|"),this.data.minAtkNum=parseInt(L[0]||0),this.data.maxAtkNum=parseInt(L[1]||0),this.data.atkSpeed=R.attackCooldown,this.data.moveAnimation=R.moveAnimation,this.data.attackAnimation=R.attackAnimation,this.data.attackEffect=R.attacKeffect,this.data.moveSpeed=R.movementSpeed,this.data.attacKeffectInterval=1e3*R.attacKeffectInterval,this.data.atkOffsetPos.set(Number(R.trajectoryX)||0,Number(R.trajectoryY)||0),this.data.atkAnimJsonId=P.Animation,this.data.atkAnimTargetType=D.target,this.data.atkStyle=P.type,this.data.hurtEffect=P.impactAnimation,U=Number(R.animationScale)||1,this.data.scale=o(U,U,U),this.data.dimonEffects.atkr=Number(R.critical||0),this.data.dimonEffects.baseAtkM=Number(R.criticalDamage||1),u?this.data.pos=u:(z=w.getInstance().getRandomPosByType(e),this.data.pos=z),this.data.skillAnimation=R.skillAnimation,this.data.atkRag=P.attacDistance,this.data.atkPropNum=P.doubleHit,t.next=128,this.createRoleComp(e);case 128:this.comp=t.sent,this.comp.setPosAtonce(new c(this.data.pos.x,this.data.pos.y+200)),this.comp.init(this),R.skill&&this.createSkill(null,1,!0,R.skill,!0),t.next=178;break;case 134:if(e!=v.Monster){t.next=178;break}if(G=S?E.getInstance().getMonsterConfigByJsonId(S):E.getInstance().getMonsterConfigByType(s,d)){t.next=139;break}return r("monster config null",s,d,S),t.abrupt("return");case 139:return J=E.getInstance().getEffectConfigByJsonId(G.attacKeffect),j=J?E.getInstance().getAnimConfigByJsonId(J.Animation):null,J||n("effectConfig null",G.attacKeffect,s,S),W=[1,2,4][B.gameData.gameMode]||1,this.data.jsonId=G.jsonId,this.data.monsterType=s||G.monsterType,K=G.attack.split("|"),this.data.minAtkNum=parseInt(K[0]||0)*W,this.data.maxAtkNum=parseInt(K[1]||0)*W,this.data.atkSpeed=G.attackCooldown,this.data.maxHp=G.monsterHp*W,this.data.hpNum=G.monsterHp*W,this.data.moveAnimation=G.moveAnimation,this.data.attackAnimation=G.attackAnimation,this.data.attackEffect=G.attacKeffect,this.data.moveSpeed=G.movementSpeed,this.data.attacKeffectInterval=1e3*G.attacKeffectInterval,this.data.monsterTag=G.monsterRefreshType,this.data.monsterName=G.name,this.data.skillAnimation=G.skillAnimation,this.data.monsterScore=G.monsterScore,this.data.atkOffsetPos.set(Number(G.trajectoryX)||0,Number(G.trajectoryY)||0),this.data.atkAnimJsonId=null==J?void 0:J.Animation,this.data.atkAnimTargetType=null==j?void 0:j.target,this.data.atkStyle=null==J?void 0:J.type,this.data.hurtEffect=null==J?void 0:J.impactAnimation,_=.8+.4*Math.random(),this.data.atkRag=(null==J?void 0:J.attacDistance)*_,this.data.atkPropNum=null==J?void 0:J.doubleHit,V=-T+2*T*Math.random(),this.data.pos=u||h(V,200-g),Y=Number(G.animationScale)||1,this.data.scale=o(Y,Y,Y),t.next=174,this.createRoleComp(e);case 174:this.comp=t.sent,this.comp.init(this),G.skill&&this.createSkill(null,1,null,G.skill),this.data.monsterType==A.Baize||this.data.monsterType==A.Huodou||this.data.monsterType==A.Jiuwei||this.data.monsterType==A.Qiongqi||this.data.monsterType==A.Suanni||this.data.monsterType==A.Taotie?(this.data.pos.x=0,this.data.pos.y=-160,this.comp.setPosAtonce(new c(this.data.pos.x,this.data.pos.y))):this.comp.setPosAtonce(new c(this.data.pos.x,u?u.y:this.data.pos.y-400));case 178:this.data.atkSpeed<=0&&(this.data.atkSpeed=1);case 179:case"end":return t.stop()}}),t,this)})));return function(a,e,i,s,n,o,d,h){return t.apply(this,arguments)}}(),s.getAnimNode=function(){var t;return null==(t=this.comp)?void 0:t.getCompAnimNode()},s.towerUpLev=function(t,a){void 0===a&&(a=!1);var e=E.getInstance().getDoorConfigByLev(t);if(e){var i=e.doorHp||0;this.data.lev<t&&(B.ui.addView(l.ViewTowerLevUp,{level:t,hp:i},1),this.data.lev=t,this.towerUpHp(i,!0),this.comp&&a&&this.comp.setHp()),this.comp&&this.comp.setLev(t)}},s.towerUpHp=function(t,a){if(void 0===a&&(a=!1),!this.data.hpNum)return this.tempAddTowerHp+=t,void n("this.tempAddTowerHp",this.tempAddTowerHp);this.data.maxHp+=t,this.data.hpNum+=t,this.tempAddTowerHp&&(this.data.hpNum+=this.tempAddTowerHp,this.data.maxHp+=this.tempAddTowerHp,this.tempAddTowerHp=0),n("塔防增加血量",this.data.hpNum,t),a&&(this.data.hpNum=this.data.maxHp),this.comp&&this.comp.setHp()},s.towerPointupLev=function(t){this.add(this.data.userId,this.data.type,this.data.monsterType,t,this.data.pos)},s.createRoleComp=function(){var t=e(i().mark((function t(a){var e,s,n,o,d,h;return i().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!this.comp||!this.comp.node){t.next=3;break}return a==v.Monster?this.comp.node.parent=w.getInstance().monsterParentNd:a==v.Tower&&(this.comp.node.parent=w.getInstance().towerParentNd),t.abrupt("return",this.comp);case 3:if(a!=v.Tower){t.next=13;break}return t.next=7,B.res.bundleLoadPrefab(y.abGame,"./prefab/Unit/UnitTower");case 7:return e=t.sent,(s=u(e).getComponent(N)).node.parent=w.getInstance().towerParentNd,t.abrupt("return",s);case 13:if(a!=v.TowerPoint){t.next=22;break}return t.next=16,B.res.bundleLoadPrefab(y.abGame,"./prefab/Unit/UnitTowerPoint");case 16:return n=t.sent,(o=u(n).getComponent(x)).node.parent=w.getInstance().heroParentNd,t.abrupt("return",o);case 22:return t.next=24,B.res.bundleLoadPrefab(y.abGame,"./prefab/Unit/UnitRole");case 24:return d=t.sent,(h=u(d).getComponent(N)).node.parent=a==v.Hero?w.getInstance().heroParentNd:w.getInstance().monsterParentNd,t.abrupt("return",h);case 28:case"end":return t.stop()}}),t,this)})));return function(a){return t.apply(this,arguments)}}(),s.setTowerHp=function(){var t=e(i().mark((function t(a){return i().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(this.data.maxHp=a,this.data.hpNum=this.data.maxHp,this.tempAddTowerHp&&(this.data.hpNum+=this.tempAddTowerHp,this.data.maxHp+=this.tempAddTowerHp,this.tempAddTowerHp=0),n("setTowerHp",!!this.comp),this.comp){t.next=8;break}return t.next=7,this.createRoleComp(this.data.type);case 7:this.comp.init(this);case 8:this.comp.setHp();case 9:case"end":return t.stop()}}),t,this)})));return function(a){return t.apply(this,arguments)}}(),s.getUserRank=function(){return w.getInstance().getUserRank(this.data.userId)},s.getUserWeekScore=function(){return this.data.weekScore},s.destroy=function(t){void 0===t&&(t=!1),this.setState(p.Dead),this.skills&&(this.skills.forEach((function(t){t.destroy()})),this.skills.clear()),this.comp&&this.comp.clear(t),this.data.isLongMonster=!1,this.data.reliveTime=0,this.data.skinId=k.Skin1,this.data.skinLev=1,this.clearAllBuff()},s.getNearEnemys=function(t,a){return void 0===t&&(t=1),void 0===a&&(a=!1),w.getInstance().getAttackNearEnemys(this,t,a)},s.isRoleAlive=function(){return this.data.state!=p.Dead&&this.data.state!=p.None&&this.data.state!=p.WaitRelive},s.moveLeft=function(){this.data.pos.x-=270},s.moveRight=function(){this.data.pos.x+=270},s.checkIfAbleAttackTower=function(){var t;if(null==(t=this.comp)||!t.node)return null;var a=Math.abs(this.towerPosY-this.comp.node.position.y)<=this.data.atkRag;return this.isInAttackDistance=a,a},s.checkSkillAnim=function(t){return this.data.skillAnimation?E.getInstance().getAnimConfigByJsonId(this.data.skillAnimation)?(this.setState(p.Skill),this.skillAnimCb=t,!0):void n("skillAnimation config no find",this.data.skillAnimation):(t&&t(),!1)},s.releaseAttack=function(t,a){var e=this;void 0===t&&(t=1),void 0===a&&(a=!0);var i=this.getAtkSpeed();if(this.data.type==v.Monster){if(this.data.state==p.Hurt||this.data.state==p.Skill)return;var s;if(this.checkIfAbleAttackTower())this.setState(p.Attack),this.atkTime=0,null==(s=this.comp)||s.doAttackAnim((function(i){var s={pos0:null,pos1:null,offsetpos:e.data.atkOffsetPos,animId01:e.data.atkAnimJsonId,animId02:e.data.hurtEffect,roledir:e.data.moveDir,atkStyle:e.data.atkStyle,delay:e.data.attacKeffectInterval,formRoleType:e.data.type,checkFunc:null};2==e.data.atkAnimTargetType?s.pos1=e.data.pos:3==e.data.atkAnimTargetType&&(s.pos0=e.data.pos,s.pos1=o(e.data.pos.x,e.towerPosY+100)),s.checkFunc=e.isRoleAlive.bind(e),a&&B.event.raiseEvent(S.MonsterAtkEffect,s),e.backTempState(p.Idle,1e3*i,(function(){w.getInstance().attackTower(e);var a=t-1;a>0?e.releaseAttack(a,!1):e.checkIfGreateBoss()&&B.event.raiseEvent(S.ShakeCam)}))}),i)}else{var n,d=this.getNearEnemys(t,!0);if(!d||d.length<=0)return;this.setState(p.Attack),d[0].data.pos.x>this.data.pos.x?this.data.moveDir=1:this.data.moveDir=-1,null==(n=this.comp)||n.doAttackAnim((function(t){e.backTempState(p.Idle,1e3*t,(function(){}))}),i);var h=!1,r=this.data.atkNum*(1+this.data.atkNumBuff)+this.data.dimonEffects.attack;Math.random()<this.data.dimonEffects.atkr&&(r*=this.data.dimonEffects.atkm+this.data.dimonEffects.baseAtkM,h=!0);for(var c=function(){var t=d[m]||d[0],a=t.data.pos;B.event.raiseEvent(S.HeroAtkEffect,{animId01:e.data.atkAnimJsonId,pos0:e.data.pos,pos1:a,offsetpos:e.data.atkOffsetPos,normalatk:e.data.skinId,animId02:e.data.hurtEffect,formRoleType:e.data.type,delay:e.data.attacKeffectInterval,speed:i,cb:function(){t&&t.isRoleAlive()&&t.hurt(r,e.data.userId,null,h)}})},m=0;m<t;m++)c()}},s.getAtkSpeed=function(){return this.data.atkSpeed*this.data.atkSpeedBuff*(this.data.atkSpeed/(this.data.atkSpeed*(1+this.data.dimonEffects.atkspeed)))},s.buffTower=function(t){w.getInstance().buffTower(t)},s.setState=function(t){this.timeoutBackState&&clearTimeout(this.timeoutBackState),this.data.state==p.Skill&&this.skillAnimCb&&this.skillAnimCb(),this.data.state=t,t==p.Dead&&this.buffCons.forEach((function(t){return t.hide()})),this.skillAnimCb=null},s.backTempState=function(t,a,e){var i=this;void 0===a&&(a=1e3),this.data.tempState||(this.data.tempState=t),this.timeoutBackState&&clearTimeout(this.timeoutBackState),this.timeoutBackState=setTimeout((function(){i.data&&(i.data.state!=p.Dead&&(i.setState(i.data.tempState),i.data.tempState=null),e&&e())}),a)},s.hurt=function(t,a,e,i){var s=this;if(this.data&&!(this.data.invincibleTime>0)&&this.isRoleAlive()){if(this.data.skillWeakIds.length>0&&this.data.skillWeakIds.includes(e)&&this.data.skillWeakNum>0&&(t*=this.data.skillWeakNum),t){var o=Math.floor(t);if((t=o)>this.data.hpNum&&(t=this.data.hpNum),this.data.hpNum-=t,w.getInstance().addUserAtkPower(a,t),this.data.type==v.Monster&&(B.event.raiseEvent(S.HurtTips,{posx:this.data.pos.x,posy:this.data.pos.y,num:o,isBao:i}),this.skills.forEach((function(t){return t.checkHpReduceSkill(s.data.hpNum/s.data.maxHp)}))),!this.data.hpNum||this.data.hpNum<=0){if(this.data.hpNum=0,this.setState(p.Dead),this.data.type==v.Monster){var d;null==(d=this.comp)||d.onDead(),B.event.raiseEvent(S.MonsterDead,{pos0:this.data.pos}),w.getInstance().addUserScore(a,this.data.monsterScore,!0),this.data.isLongMonster?(this.setState(p.WaitRelive),this.data.reliveTime=10,this.skills.forEach((function(t){return t.hide()})),n("妖族等待复活")):(w.getInstance().killMonster(this,a),this.destroy());var h=w.getInstance().findUser(a);if(!h)return;this.checkIfBossMonster()&&(h.killboss+=1),h.killmonster+=1}return 0}return this.comp&&(this.comp.playHit(),this.comp.setHp()),this.data.hpNum}n("扣血值有问题",t,a,e)}},s.checkIfBossMonster=function(){return this.data.type==v.Monster&&(this.data.monsterTag==T.smallboss||this.data.monsterTag==T.bigboss)},s.checkIfSmallBoss=function(){return this.data.type==v.Monster&&this.data.monsterTag==T.smallboss},s.checkIfBigBoss=function(){return this.data.type==v.Monster&&this.data.monsterTag==T.bigboss},s.checkIfGreateBoss=function(){return this.data.type==v.Monster&&this.data.monsterTag==T.bigboss&&3==this.data.atkAnimTargetType},s.saveTower=function(t,a){return this.data.hpNum>=this.data.maxHp?0:(this.data.auotSaveHpTime<0&&(this.data.auotSaveHpTime=0),this.data.auotSaveHpTime+=a,this.data.autoSaveHpNum||(t=Number(t),this.data.autoSaveHpNum=Math.ceil(this.data.maxHp*(t/100)),this.data.autoSaveHpEachNum=Math.ceil(this.data.autoSaveHpNum/5)),this.data.autoSaveHpNum)},s.addHp=function(t){(t=Math.floor(t))<0||(this.data.hpNum+=t,this.data.hpNum>this.data.maxHp&&(this.data.hpNum=this.data.maxHp),this.comp.setHp())},s.setBuffIcon=function(){var t=e(i().mark((function t(a,e){var s,n,o;return i().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(a){t.next=2;break}return t.abrupt("return");case 2:if(this.isRoleAlive()){t.next=4;break}return t.abrupt("return");case 4:if(s=this.buffCons.get(a)){t.next=14;break}return t.next=8,B.res.bundleLoadPrefab(y.abGame,"./prefab/Unit/UnitBuffCoin");case 8:if(n=t.sent,s=u(n).getComponent(H),this.isRoleAlive()){t.next=13;break}return s.destroy(),t.abrupt("return");case 13:this.buffCons.set(a,s);case 14:s.setData(this,a,e),s.node.parent=w.getInstance().skillParentNd,o=0,this.buffCons.forEach((function(t){t.node.active&&(t.setPos(o),o+=1)}));case 18:case"end":return t.stop()}}),t,this)})));return function(a,e){return t.apply(this,arguments)}}(),s.setBuffColor=function(){var t=e(i().mark((function t(a,e){return i().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:this.comp&&this.comp.setColor(a,e);case 1:case"end":return t.stop()}}),t,this)})));return function(a,e){return t.apply(this,arguments)}}(),s.setBuffAutoHurt=function(t,a,e,i,s){this.isRoleAlive()&&(this.data.autoHurtNum=Number(t),this.data.autoHurtEachTime=a,this.data.autoHurtTime=e,this.data.autoHurtEffectTime=0,this.setBuffIcon(i,e),this.setBuffColor(s,e))},s.setBuffAtkSpeed=function(t,a,e,i){void 0===a&&(a=1),this.isRoleAlive()&&(this.data.atkSpeedBuff=Number(t),this.data.atkSpeedBuffTime=a,this.setBuffIcon(e,a),this.setBuffColor(i,a),n("攻速buff",this.data.atkSpeedBuff,this.data.atkSpeedBuffTime))},s.setBuffSkillSpeed=function(t,a,e,i){void 0===a&&(a=1),this.isRoleAlive()&&(this.data.skillSpeedBuffTime=Number(a),this.data.skillSpeedBuff=t,this.skills.forEach((function(t){return t.refreshSpeed()})),this.setBuffIcon(e,a),this.setBuffColor(i,a))},s.setBuffAtk=function(t,a,e,i){void 0===a&&(a=1),this.isRoleAlive()&&(this.data.atkNumBuff=Number(t),this.data.atkNumBuffTime=a,this.data.atkNumBuff<0&&(this.data.atkNumBuff=0),this.setBuffIcon(e,a),this.setBuffColor(i,a))},s.setBuffSpeed=function(t,a,e,i){void 0===a&&(a=1),this.isRoleAlive()&&(this.data.moveSpeedBuff=Number(t),this.data.moveSpeedBuffTime=a,this.setBuffIcon(e,a),this.setBuffColor(i,a))},s.setDizzTime=function(t,a,e){n("setDizzTime"),this.isRoleAlive()&&(this.data.dizzinessTime=Number(t),this.setState(p.Dizziness),this.setBuffIcon(a,t),this.setBuffColor(e,t))},s.setInvincible=function(t){this.data.invincibleTime=t},s.setWeakState=function(t,a){this.data.skillWeakIds=t,this.data.skillWeakNum=Number(a),n("设置弱点",this.data.skillWeakIds,this.data.skillWeakNum,this.data.monsterType)},s.clearAllBuff=function(){this.data.atkNumBuff=0,this.data.atkNumBuffTime=0,this.data.moveSpeedBuff=1,this.data.moveSpeedBuffTime=0,this.data.dizzinessTime=0,this.data.invincibleTime=0,this.data.autoHurtTime=0,this.data.atkSpeedBuff=1,this.data.skillSpeedBuff=1,this.data.autoHurtNum=0,this.data.auotSaveHpTime=0,this.buffCons.forEach((function(t){return t.kill()})),this.buffCons.clear()},s.moveTo=function(t){this.data.pos=t},s.updateSkill=function(t,a,e,i){var s,o;this.isRoleAlive()&&(i>0&&(s=this.createSkill(t,i)),e>0&&a&&(n("大技能"),o=this.createSkill(a,e,!0)),n("skill:",s,o,s==o))},s.createSkill=function(t,a,e,i,s){if(void 0===e&&(e=!1),void 0===s&&(s=!1),this.isRoleAlive()){this.skills||(this.skills=new Map);var o=t,d=o||i,h=this.skills.get(d);if(n("createskill:",d,h,this.skills,e),h)if(e){if(!h.checkIfAbleUpLev(a))return!1;var r=h.getLev();h.levelUp(a,o),o!=g.GreatAttack&&B.event.raiseEvent(S.GiftMessage,{type:I.SkillUp,skillType:o,fromLev:r,lev:a,avatar:this.data.iconUrl,name:this.data.nickName})}else h.addNum(a);else(h=w.getInstance().getSkill()).init(this,o,e?a:1,i,a,s),this.skills.set(o||i,h),e&&o!=g.GreatAttack&&o&&B.event.raiseEvent(S.GiftMessage,{type:I.SkillUp,skillType:o,lev:a,avatar:this.data.iconUrl,name:this.data.nickName});return h}},s.delectSkill=function(t){this.skills.delete(t.skillType),w.getInstance().killSkill(t)},s.switchSkin=function(t,e){if(void 0===e&&(e=1),t){var i=w.getInstance().findUser(this.data.userId),s=i.skins.find((function(a){return a.prop==t}));if(s)if(1!=s.useStatus&&(C.setSelectSkin(this.data.userId,t),i.skins.forEach((function(t){t.useStatus=0})),s.useStatus=1),null!=this.data.weekScore&&null!=this.data.weekScore){if(this.isRoleAlive()){var o;this.data.skinId=t,this.data.skinLev=e,this.needSwitchSkinId=null;for(var h,r=b,c=a(this.skills);!(h=c()).done;){var m=h.value,u=m[0];m[1];r.includes(u)||(this.skills.delete(u),n("切换皮肤-》删除技能",u))}this.add(this.data.userId,this.data.type,this.data.monsterType,this.data.lev,this.data.pos,this.data.nickName,this.data.iconUrl),this.setState(p.Idle);var f=new d(0,0);switch(this.data.skinId){case k.Skin1:break;case k.Skin2:f.set(0,20);break;case k.Skin3:case k.Skin4:case k.Skin5:f.set(0,60);break;case k.Skin6:case k.Skin7:default:f.set(0,80)}null==(o=this.comp)||o.setRankInfo(f),this.skills.forEach((function(t){return t.resetSkillCompPos(null,f)}))}}else this.needSwitchSkinId=t;else n("没有这个皮肤",i.skins,t)}},s.updateDimondInfo=function(t){for(var a=this,e=E.getInstance().getBeadConfigs(),i=function(){var i=e[s].jsonId,n=t.find((function(t){return t.type==i}));if(n&&n.num){var o=E.getInstance().getBeadConfigByNum(i,n.num);if(!o)return r("获取 beads 配置失败",i,n.num),1;switch(i){case"260001":a.data.dimonEffects.attackLev=o.beadLevel,a.data.dimonEffects.attack=Number(o.attack),a.data.dimonEffects.attackDmNum=n.num;break;case"260002":a.data.dimonEffects.skillcdLev=o.beadLevel,a.data.dimonEffects.skillcd=Number(o.weaponInterval),a.data.dimonEffects.skillcdDmNum=n.num;break;case"260003":a.data.dimonEffects.atkspeedLev=o.beadLevel,a.data.dimonEffects.atkspeed=Number(o.attackCooldown),a.data.dimonEffects.atkspeedDmNum=n.num;break;case"260004":a.data.dimonEffects.atkmLev=o.beadLevel,a.data.dimonEffects.atkm=Number(o.criticalDamage),a.data.dimonEffects.atkmDmNum=n.num;break;case"260005":a.data.dimonEffects.hpLev=o.beadLevel,a.data.dimonEffects.hpDmNum=n.num,a.data.dimonEffects.hp!=Number(o.gateHp)&&(a.data.dimonEffects.hp=Number(o.gateHp),w.getInstance().addMaxHpTower(a.data.dimonEffects.hp))}}},s=0;s<e.length&&!i();s++);n("updateDimondInfo",this.data.dimonEffects)},s.checkSwitchSkin=function(){var t=this;this.needSwitchSkinId&&this.switchSkin(this.needSwitchSkinId);for(var a=w.getInstance().findUser(this.data.userId),e=E.getInstance().getSkinConfgs().filter((function(t){return 1==t.skin})),i=2592e3,s=function(){var s=e[o];if(!a.skins.find((function(t){return t.prop==s.jsonId}))&&t.data.weekScore>=s.openCondition){n("needUnlock",s.jsonId),C.rewardSkin(a.userId,4,[{num:1,prop:s.jsonId,time:i}]);var d={prop:s.jsonId,time:i+1e3*s.period};a.skins.push(d)}},o=0;o<e.length;o++)s();var d=a.skins.find((function(t){return 1==t.useStatus}));if(!d){var h="170001",r=a.skins.find((function(t){return t.prop==h}));C.setSelectSkin(this.data.userId,h),r&&(r.useStatus=1),d=r}},s.checkGiftTitle=function(t){var a=E.getInstance().getGiftRankTitleByScore(t);n("礼物积分名头",t,a)},s.checkIfHaveBuff=function(t){return t?this.isRoleAlive()?this.createBuff(t):void 0:null},s.createBuff=function(t){var a=w.getInstance().getBuff();return a.init(t),a},s.tick=function(t){var a=this;if(this.data.state!=p.Dead){if(this.data.tickTime+=t,this.data.moveSpeedBuffTime>0&&(this.data.moveSpeedBuffTime-=t,this.data.moveSpeedBuffTime<=0&&(this.data.moveSpeedBuffTime=0,this.data.moveSpeedBuff=1)),this.data.atkNumBuffTime>0&&(this.data.atkNumBuffTime-=t,this.data.atkNumBuffTime<=0&&(this.data.atkNumBuffTime=0,this.data.atkNumBuff=0)),this.data.atkSpeedBuffTime&&(this.data.atkSpeedBuffTime-=t,this.data.atkSpeedBuffTime<=0&&(this.data.atkSpeedBuffTime=0,this.data.atkSpeedBuff=1)),this.data.skillSpeedBuffTime>0&&(this.data.skillSpeedBuffTime-=t,this.data.skillSpeedBuffTime<=0&&(this.data.skillSpeedBuffTime=0,this.data.skillSpeedBuff=1,this.skills.forEach((function(t){return t.refreshSpeed()})))),this.data.dizzinessTime>0)return this.data.dizzinessTime-=t,void(this.data.dizzinessTime<=0&&(this.data.dizzinessTime=0,this.setState(p.Idle)));if(this.data.autoHurtTime>0&&(this.data.autoHurtTime-=t,this.data.autoHurtEffectTime+=t,this.data.autoHurtEffectTime>=this.data.autoHurtEachTime&&(this.data.autoHurtEffectTime=0,this.hurt(this.data.autoHurtNum)),this.data.autoHurtTime<=0&&(this.data.autoHurtTime=0,this.data.autoHurtNum=0)),this.data.state!=p.WaitRelive)if(this.data.type==v.Tower)this.data.invincibleTime>0&&(this.data.invincibleTime-=t,this.data.invincibleTime<0&&(this.data.invincibleTime=0)),this.data.auotSaveHpTime>0&&(this.data.auotSaveHpTime-=t,this.tempTime+=t,this.tempTime>=1&&(this.tempTime=0,this.addHp(this.data.autoSaveHpEachNum)),this.data.auotSaveHpTime<=0&&(this.data.auotSaveHpTime=0));else{var e=this.getAtkSpeed();this.data.type==v.Monster?(this.data.state!=p.Attack&&this.data.tickTime%e<=t&&this.releaseAttack(this.data.atkPropNum),this.data.state==p.Skill||(this.data.state==p.Attack?(this.data.pos.x+=2*Math.random()-1,this.atkTime+=t,this.atkTime>3&&(this.atkTime=0,this.setState(p.Idle))):this.data.state==p.Hurt?this.comp&&(this.data.pos.x=this.comp.node.position.x,this.data.pos.y=this.comp.node.position.y):this.isInAttackDistance?this.data.state==p.Idle?(this.setState(p.Move),this.data.moveDir=Math.random()<.5?1:-1,this.data.pos.y>this.towerPosY&&(this.data.pos.y=this.towerPosY-5)):this.data.state!=p.Move||this.checkIfBossMonster()||(this.data.pos.x+=this.data.moveDir):(this.data.pos.x+=6*Math.random()-3,this.data.pos.y+=this.data.moveSpeed*this.data.moveSpeedBuff*t,this.data.pos.y>this.towerPosY&&(this.data.pos.y=this.towerPosY-5,this.releaseAttack(this.data.atkPropNum))))):this.data.type==v.Hero?(this.data.state!=p.Attack&&this.data.tickTime%e<=t&&this.releaseAttack(this.data.atkPropNum),this.data.state==p.Attack&&(this.atkTime+=t,this.atkTime>1.5&&(this.atkTime=0,this.setState(p.Idle))),this.data.state==p.Idle?(this.setState(p.Move),this.data.moveDir=Math.random()<.5?1:-1):this.data.state==p.Move&&(this.data.pos.x+=this.data.moveDir)):this.data.type==v.TowerPoint&&(this.data.tickTime%e<=t&&this.releaseAttack(this.data.atkPropNum),this.data.state==p.Attack?this.data.pos.x+=2*Math.random()-1:this.data.state==p.Idle?(this.setState(p.Move),this.data.moveDir=Math.random()<.5?1:-1):this.data.state==p.Move&&(this.data.pos.x+=this.data.moveDir)),this.data.pos.x<this.data.moveLeft&&(this.data.pos.x=this.data.moveLeft+6,this.data.moveDir*=-1),this.data.pos.x>this.data.moveRight&&(this.data.pos.x=this.data.moveRight-6,this.data.moveDir*=-1),this.skills.forEach((function(a){return a.tick(t)}))}else if(this.comp.node.active&&(this.comp.node.active=!1),this.data.reliveTime-=t,this.data.reliveTime<=0){this.data.reliveTime=0,this.setState(p.Idle),this.comp.node.active=!0,this.skills.forEach((function(t){return t.show()}));var i=h(this.comp.node.position.x,this.comp.node.position.y);this.add(this.data.userId,this.data.type,this.data.monsterType,this.data.lev).then((function(){a.data.pos=i,a.comp.setPosAtonce(a.data.pos)})),n("怪物原地复活")}}},t}());s._RF.pop()}}}));

System.register("chunks:///_virtual/RoleData.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ConstGlobal.ts","./Tool.ts"],(function(i){var t,s,e,o,h;return{setters:[function(i){t=i.createClass},function(i){s=i.cclegacy,e=i.v3},function(i){o=i.E_SkinType},function(i){h=i.default}],execute:function(){s._RF.push({},"3bbadh9mV1AbYrouYT7Slkt","RoleData",void 0);i("RoleData",function(){function i(){this.userId=void 0,this.nickName=void 0,this.iconUrl=void 0,this.weekScore=void 0,this.type=void 0,this.monsterType=void 0,this.monsterTag=void 0,this.monsterName=void 0,this.monsterScore=void 0,this.lev=void 0,this.maxLev=5,this.jsonId="001",this.pos=void 0,this.hpNum=void 0,this.maxHp=void 0,this.atkOffsetPos=void 0,this.minAtkNum=void 0,this.maxAtkNum=void 0,this.normalAtkNum=10,this.scale=e(1,1,1),this.defenseNum=0,this.dimonEffects=void 0,this.skinId=o.Skin1,this.skinLev=1,this.moveLeft=void 0,this.moveRight=void 0,this.moveDir=1,this.moveAnimation=void 0,this.attackAnimation=void 0,this.skillAnimation=void 0,this.attackEffect=void 0,this.attacKeffectInterval=0,this.hurtEffect=void 0,this.atkAnimJsonId=void 0,this.atkAnimTargetType=void 0,this.atkSpeed=void 0,this.atkType=void 0,this.atkStyle=void 0,this.atkPropNum=void 0,this.atkRag=void 0,this.moveSpeed=void 0,this.tickTime=void 0,this.state=void 0,this.tempState=void 0,this.isInvincible=!1,this.invincibleTime=0,this.isLongMonster=!1,this.reliveTime=0,this.dizzinessTime=0,this.moveSpeedBuff=1,this.moveSpeedBuffTime=0,this.atkNumBuff=0,this.atkNumBuffTime=0,this.atkSpeedBuff=1,this.atkSpeedBuffTime=0,this.skillSpeedBuff=1,this.skillSpeedBuffTime=0,this.autoHurtNum=0,this.autoHurtEachTime=0,this.autoHurtTime=0,this.autoHurtEffectTime=void 0,this.auotSaveHpTime=0,this.autoSaveHpNum=0,this.autoSaveHpEachNum=0,this.skillWeakNum=0,this.skillWeakIds=void 0}return t(i,[{key:"atkNum",get:function(){return this.minAtkNum&&this.maxAtkNum?h.randomNumber(this.minAtkNum,this.maxAtkNum):this.normalAtkNum}}]),i}());s._RF.pop()}}}));

System.register("chunks:///_virtual/Skill.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ConstGlobal.ts","./UnitSkillComp.ts","./xcore.ts","./FightMgr.ts","./Tool.ts","./ConfigHelper.ts"],(function(t){var i,e,s,l,o,n,h,a,r,k,c,u,f,p,m,d,g,y,v,S,T;return{setters:[function(t){i=t.createClass,e=t.asyncToGenerator,s=t.regeneratorRuntime},function(t){l=t.cclegacy,o=t.v3,n=t.log,h=t.warn,a=t.v2,r=t.instantiate,k=t.Node,c=t.UITransform},function(t){u=t.E_SkillType,f=t.E_EVENT,p=t.E_RoleType,m=t.C_GiftSkill,d=t.C_Bundle},function(t){g=t.UnitSkillComp},function(t){y=t.xcore},function(t){v=t.FightMgr},function(t){S=t.default},function(t){T=t.ConfigHelper}],execute:function(){l._RF.push({},"2428ckMiFZP36Z1uGJRXmeG","Skill",void 0);t("Skill",function(){function t(){this.skillid=void 0,this.skillType=void 0,this.useNum=1,this.speed=3,this.skillatkMax=void 0,this.skillatkMix=void 0,this.normalskillAtk=void 0,this.healNum=void 0,this.skillLastTime=void 0,this.distance=0,this.level=0,this.animId01=void 0,this.animId02=void 0,this.fromRole=void 0,this.skillTarget=1,this.tickTime=0,this.isLongSkill=!1,this.comp=null,this.nowSpeed=void 0,this.lightningNum=0,this.offsetPos=o(),this.tempPos=o(),this.buffJsonId=void 0,this.hpDeclinePercentage=0,this.cloneNum=0,this.cloneJsonId=void 0,this.sound=void 0,this.skillname=void 0,this.createComp=!1,this.isSkinSkill=null,this.isDestroy=!1}var l=t.prototype;return l.init=function(){var t=e(s().mark((function t(i,e,l,o,h,a){var r,k,c;return s().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(void 0===l&&(l=1),void 0===h&&(h=1),this.fromRole=i,r=o?T.getInstance().getSkillConfigByJsonId(o):T.getInstance().getSkillConfigByType(e,l),n("skill config",e,l,r,h,"buff:",r.state),r){t.next=7;break}return t.abrupt("return");case 7:this.isDestroy=!1,this.useNum=1,this.isLongSkill=null,this.skillname=r.name,this.skillType=e||r.type,this.skillTarget=r.skillTarget,this.distance=r.skillRange,this.lightningNum=r.lightningNum,this.level=l,this.animId01=r.skillAnimation,this.animId02=r.impactAnimation,this.tickTime=0,this.buffJsonId=r.state,this.skillid=r.jsonId,this.sound=r.soundEffects,this.checkIfSkinSkill(),this.skillType==u.GreatAttack&&(this.useNum=l,l=1),this.skillType==u.GreatFire||this.skillType==u.GreatLightning||this.skillType==u.GreatRock||this.skillType==u.GreatSkyRock||this.skillType==u.GreatFires||this.skillType==u.LineAttack||this.fromRole.data.type==p.Monster?this.isLongSkill=!0:(k=T.getInstance().getWeaponConfigByType(this.skillType),this.useNum=k?k.skillNum*h:h,this.useNum<=0&&(this.useNum=1),n("userNum init:",this.useNum,k)),e==u.Invincible?(n("塔防无敌状态",this.useNum),this.speed=.01,this.normalskillAtk=r.guardTime||10):e==u.AddHp?(this.speed=r.skillCooldown,this.skillLastTime=r.healingTime,this.healNum=r.healing):e==u.Weak?(this.speed=.01,this.isLongSkill=null,this.useNum=1):(this.speed=r.skillCooldown,r.skillDamage&&(c=r.skillDamage.toString().split("|"),this.skillatkMix=parseInt(c[0]||0),this.skillatkMax=parseInt(c[1]||0),this.normalskillAtk=parseInt(c[0]||0)),this.hpDeclinePercentage=Number(r.hpDeclinePercentage)||0,this.cloneNum=r.humanClone||0,this.cloneJsonId=r.cloneMonsterId||null,this.isLongSkill&&this.fromRole.data.type==p.Hero&&this.createSkillComp(e)),this.refreshSpeed(),this.fromRole.data.type==p.TowerPoint&&n("炮塔技能：",this.isLongSkill,e,r,this.nowSpeed),a&&(this.isLongSkill=a);case 29:case"end":return t.stop()}}),t,this)})));return function(i,e,s,l,o,n){return t.apply(this,arguments)}}(),l.refreshSpeed=function(){this.speed?this.nowSpeed=this.speed*this.fromRole.data.skillSpeedBuff*(this.speed/(this.speed*(1+this.fromRole.data.dimonEffects.skillcd))):this.nowSpeed=null},l.getSkillSpeed=function(){return this.nowSpeed},l.createSkillComp=function(){var t=e(s().mark((function t(i,e){var l,h;return s().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(void 0===e&&(e=o(0,0)),this.isLongSkill&&!this.isSkinSkill&&!this.createComp){t.next=3;break}return t.abrupt("return");case 3:if(this.createComp=!0,this.comp&&this.comp.node&&this.comp.node.isValid&&(!this.comp||this.comp.getType()==i)){t.next=14;break}return t.next=7,y.res.bundleLoadPrefab(d.abGame,"./prefab/Unit/UnitSkillComp");case 7:l=t.sent,(h=r(l)).parent=v.getInstance().skillParentNd,this.comp=h.getComponent(g),n("newCreate",i),t.next=15;break;case 14:n("oldCreate",i,this.comp.getType());case 15:return n("createSkillComp",this.comp,this.isLongSkill,i,this),this.resetSkillCompPos(i,e),this.comp.node.active=!0,this.createComp=!1,t.abrupt("return",this.comp);case 20:case"end":return t.stop()}}),t,this)})));return function(i,e){return t.apply(this,arguments)}}(),l.resetSkillCompPos=function(t,i){void 0===i&&(i=o(0,0)),this.comp&&(this.skillType==u.GreatFire?this.offsetPos=o(-60,20):this.skillType==u.GreatFires?this.offsetPos=o(-80,90):this.skillType==u.GreatLightning?this.offsetPos=o(80,90):this.skillType==u.GreatRock?this.offsetPos=o(60,20):this.skillType==u.GreatSkyRock?this.offsetPos=o(0,160+i.y):this.offsetPos=o(),this.comp.setData({jsonId:this.skillid,roleData:this.fromRole.data,targetSkill:this,type:t||this.skillType,lev:this.level,offsetPos:this.offsetPos}))},l.checkIfHaveBuff=function(t,i,e){if(void 0===e&&(e=!1),t){var s=this.fromRole.checkIfHaveBuff(t);s&&s.setTargets(i,e)}},l.releaseSkill=function(){var t=e(s().mark((function t(){var i,e,l,n,h,a,r,m,d,g,S,T,R,I,N=this;return s().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(this.isLongSkill){t.next=3;break}if(!(this.useNum<=0)){t.next=3;break}return t.abrupt("return");case 3:t.t0=this.skillType,t.next=t.t0===u.Invincible?6:t.t0===u.AddHp?9:t.t0===u.CreateMore?13:14;break;case 6:return this.fromRole.buffTower(this.skillatkNum),this.isLongSkill||(this.useNum>0&&(this.useNum-=1),this.useNum<=0&&this.destroy()),t.abrupt("break",33);case 9:return v.getInstance().saveTower(this.healNum,this.skillLastTime,this.fromRole.data.userId),this.useNum>0&&(this.useNum-=1),this.useNum<=0&&this.destroy(),t.abrupt("break",33);case 13:return t.abrupt("break",33);case 14:if(this.fromRole.data.type!=p.Hero&&this.fromRole.data.type!=p.TowerPoint){t.next=31;break}if(i=this.fromRole.getNearEnemys(1),e=i[0]){t.next=19;break}return t.abrupt("return");case 19:if(l=e.data.pos,n=[],1==this.skillTarget?(h=300,0==this.distance?h=1:this.distance>=1e4&&(h=1e3),this.lightningNum&&(h=this.lightningNum,this.distance=1e3),this.skillType==u.LineAttack?(a=(new k).addComponent(c),this.tempPos.set((this.fromRole.data.pos.x+l.x)/2,(this.fromRole.data.pos.y+l.y)/2+100),a.node.setPosition(this.tempPos),r=this.fromRole.data.pos.x-l.x,m=this.fromRole.data.pos.y-l.y,d=180*Math.atan2(m,r)/Math.PI,a.node.angle=d,a.width=100,a.height=Math.sqrt((this.fromRole.data.pos.y-l.y)*(this.fromRole.data.pos.y-l.y)+(this.fromRole.data.pos.x-l.x)*(this.fromRole.data.pos.x-l.x)),n=v.getInstance().checkLineMonster(a.node,this.distance,h)):n=v.getInstance().checkNearMonster(null==(g=e.comp)?void 0:g.node,this.distance,h)):2==this.skillTarget?n=[this.fromRole]:3==this.skillTarget&&(n=v.getInstance().getHeroRoles()),this.isLongSkill||n.length>0&&(this.useNum-=1),this.skillType!=u.MoveSpeed&&this.skillType!=u.SkillSpeed&&this.skillType!=u.Dizziness&&this.skillType!=u.Crazy&&this.skillType!=u.Weak&&this.skillType!=u.MoveSpeed2&&this.skillType!=u.SkillSpeed2&&this.skillType!=u.Dizziness2&&this.skillType!=u.Crazy2&&this.skillType!=u.Weak2){t.next=26;break}return this.checkHeroBuff(n),t.abrupt("return");case 26:if(S={pos0:this.fromRole.data.pos,pos1:l,skill:this.skillType,offsetpos:this.offsetPos||o(),animId01:this.animId01,animId02:this.animId02,formRoleType:this.fromRole.data.type,range:this.distance,sound:this.sound,cb:function(){for(var t=0;t<n.length;t++){var i=n[t];i&&i.isRoleAlive()&&i.hurt(N.skillatkNum,N.fromRole.data.userId,N.skillType)}n.length>0&&N.checkIfHaveBuff(N.buffJsonId,n),N.skillType==u.SkyRock&&y.event.raiseEvent(f.ShakeCam),!N.isLongSkill&&N.useNum<=0&&N.destroy()}},this.lightningNum>0&&n.length>0){for(T=[],R=0;R<n.length;R++)I={x:n[R].data.pos.x,y:n[R].data.pos.y},T.push(I);S.lightningTargets=T}y.event.raiseEvent(f.SkillEffect,S),t.next=32;break;case 31:this.fromRole.data.type==p.Monster&&this.checkMonsterBuff();case 32:return t.abrupt("break",33);case 33:case"end":return t.stop()}}),t,this)})));return function(){return t.apply(this,arguments)}}(),l.checkIfSkinSkill=function(){var t=this,i=m;this.isSkinSkill=!0,i.forEach((function(i){i==t.skillType&&(t.isSkinSkill=!1)})),n("this.isSkinSkill",this.isSkinSkill,this.skillType)},l.createSound=function(){this.sound&&y.sound.remotePlayOneShot(this.sound)},l.addNum=function(t){var i=T.getInstance().getWeaponConfigByType(this.skillType);this.useNum+=i?(i.skillNum||1)*t:1,n("userNum addNum:+",this.useNum),this.skillType==u.AddHp&&h("回血add")},l.checkHeroBuff=function(t){t.length>0&&(this.checkIfHaveBuff(this.buffJsonId,t,!0),this.fromRole.checkSkillAnim()&&y.event.raiseEvent(f.SkillEffect,{pos0:this.fromRole.data.pos,skill:this.skillType,offsetpos:this.offsetPos||o(),animId01:this.animId01}),this.fromRole.data.type==p.Monster&&y.event.raiseEvent(f.SkillTips,{txt:this.skillname,pos:this.fromRole.data.pos}),this.useNum>0&&(this.useNum-=1))},l.checkMonsterBuff=function(){var t=[];if(1==this.skillTarget)t=v.getInstance().getHeroRoles();else if(2==this.skillTarget)t=[this.fromRole];else if(3==this.skillTarget){var i=50;0==this.distance?i=1:this.distance>=1e4&&(i=1e3),this.lightningNum&&(i=this.lightningNum),t=v.getInstance().checkNearMonster(this.fromRole.comp.node,this.distance,i)}t.length>0&&(this.checkIfHaveBuff(this.buffJsonId,t,!0),this.fromRole.checkSkillAnim()&&y.event.raiseEvent(f.SkillEffect,{pos0:this.fromRole.data.pos,skill:this.skillType,offsetpos:this.offsetPos||o(),animId01:this.animId01}),this.fromRole.data.type==p.Monster&&y.event.raiseEvent(f.SkillTips,{txt:this.skillname,pos:this.fromRole.data.pos}),this.useNum>0&&(this.useNum-=1))},l.checkHpReduceSkill=function(t){var i=this;switch(this.skillType){case u.Crazy:t<=this.hpDeclinePercentage&&(this.checkMonsterBuff(),this.skillType=null);break;case u.CreateMore:if(t<=this.hpDeclinePercentage){this.skillType=null;var e=function(){for(var t=0;t<i.cloneNum;t++){var e=S.getRandomPosInCircle(i.fromRole.data.pos,100);y.event.raiseEvent(f.CrateMoreMonster,{jsonId:i.cloneJsonId,pos:a(e.x,e.y)})}},s=this.fromRole.checkSkillAnim((function(){e&&e()}));this.hpDeclinePercentage<=0&&(e(),e=null),s&&y.event.raiseEvent(f.SkillEffect,{pos0:this.fromRole.data.pos,skill:this.skillType,offsetpos:this.offsetPos||o(),animId01:this.animId01}),this.fromRole.data.type==p.Monster&&y.event.raiseEvent(f.SkillTips,{txt:this.skillname,pos:this.fromRole.data.pos})}}},l.levelUp=function(t,i){void 0===t&&(t=1),this.init(this.fromRole,i,t),n("技能升级")},l.checkIfAbleUpLev=function(t){return this.level<t},l.getLev=function(){return this.level},l.hide=function(){this.comp.node.active=!1},l.show=function(){this.comp.node.active=!0},l.onDestroy=function(){this.comp&&this.comp.node&&this.comp.node.isValid&&(this.comp.node.destroy(),this.comp=null,n("skill onDestroy")),this.isLongSkill=!1},l.destroy=function(){this.isDestroy||(this.isDestroy=!0,this.onDestroy(),this.fromRole.delectSkill(this))},l.tick=function(t){this.nowSpeed&&(this.tickTime+=t,this.tickTime>=this.nowSpeed&&this.tickTime%this.nowSpeed<=t&&(this.isLongSkill||this.useNum>0)&&this.releaseSkill())},i(t,[{key:"skillatkNum",get:function(){return this.skillatkMax&&this.skillatkMix?S.randomNumber(this.skillatkMix,this.skillatkMax):this.normalskillAtk}}]),t}());l._RF.pop()}}}));

System.register("chunks:///_virtual/UIGiftDesc.ts",["./rollupPluginModLoBabelHelpers.js","cc","./xcore.ts","./ConfigHelper.ts"],(function(t){var i,e,o,n,s,h,r,a,c,p,g,f,v,d,m,u;return{setters:[function(t){i=t.applyDecoratedDescriptor,e=t.inheritsLoose,o=t.initializerDefineProperty,n=t.assertThisInitialized},function(t){s=t.cclegacy,h=t._decorator,r=t.PageView,a=t.Vec3,c=t.view,p=t.UITransform,g=t.Node,f=t.Sprite,v=t.size,d=t.Component},function(t){m=t.xcore},function(t){u=t.ConfigHelper}],execute:function(){var T,_,l,w,L;s._RF.push({},"31fa0n58rBNrKcDJfyBAZZ9","UIGiftDesc",void 0);var P=h.ccclass,y=h.property;t("UIGiftDesc",(T=P("UIGiftDesc"),_=y(r),T((L=i((w=function(t){function i(){for(var i,e=arguments.length,s=new Array(e),h=0;h<e;h++)s[h]=arguments[h];return i=t.call.apply(t,[this].concat(s))||this,o(i,"pv",L,n(i)),i._giftTempPos=new a,i._viewLimitLeft=void 0,i._viewLimitRight=void 0,i._viewLimitBottom=void 0,i._viewLimitTop=void 0,i}e(i,t);var s=i.prototype;return s.onLoad=function(){var t=c.getVisibleSize();this._viewLimitLeft=-t.width/2+80+this.node.getComponent(p).width/2,this._viewLimitRight=t.width/2-80-this.node.getComponent(p).width/2,this._viewLimitBottom=-t.height/2+20+this.node.getComponent(p).height/2,this._viewLimitTop=t.height/2-20-this.node.getComponent(p).height/2,this.node.on(g.EventType.TOUCH_START,this.onTouchStart,this,!0),this.node.on(g.EventType.TOUCH_MOVE,this.onTouchMove,this,!0),this.node.on(g.EventType.TOUCH_END,this.onTouchEnd,this,!0),this.node.on(g.EventType.TOUCH_CANCEL,this.onTouchEnd,this,!0),this._giftTempPos.set(this._viewLimitRight,this._viewLimitBottom),this.node.setPosition(this._giftTempPos),this.initBanner()},s.initBanner=function(){var t=u.getInstance().getConstantConfigByKey("giftPicture"),i=u.getInstance().getConstantConfigByKey("giftPicture2"),e=[];t&&e.push(t),i&&e.push(i);for(var o=0;o<e.length;o++){var n=e[o];if(n){var s=(new g).addComponent(f);m.res.remoteLoadSprite(n,s,v(430,370)),this.pv.addPage(s.node)}}this.randomMoveBanner(e.length)},s.randomMoveBanner=function(t){var i=this;t<=1||this&&this.node&&this.node.isValid&&this.scheduleOnce((function(){var e=i.pv.getCurrentPageIndex();i.pv.scrollToPage(e>=t-1?0:e+1,.6),i.randomMoveBanner(t)}),6)},s.onTouchStart=function(t){this._giftTempPos=this.node.getPosition()},s.onTouchMove=function(t){var i=2*t.getDeltaX(),e=2*t.getDeltaY();this._giftTempPos.x+i>this._viewLimitLeft&&this._giftTempPos.x+i<this._viewLimitRight&&(this._giftTempPos.x+=i),this._giftTempPos.y+e>this._viewLimitBottom&&this._giftTempPos.y+e<this._viewLimitTop&&(this._giftTempPos.y+=e),this.node.setPosition(this._giftTempPos)},s.onTouchEnd=function(t){},i}(d)).prototype,"pv",[_],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),l=w))||l));s._RF.pop()}}}));

System.register("chunks:///_virtual/UnitBossMessage.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ConfigHelper.ts","./xcore.ts","./mediaVideo.ts","./GiftMessageMgr.ts","./FightMgr.ts","./Tool.ts"],(function(e){var t,n,i,r,a,o,l,s,u,c,p,f,b,d,g,v,h,m,y;return{setters:[function(e){t=e.applyDecoratedDescriptor,n=e.inheritsLoose,i=e.initializerDefineProperty,r=e.assertThisInitialized,a=e.asyncToGenerator,o=e.regeneratorRuntime},function(e){l=e.cclegacy,s=e._decorator,u=e.Node,c=e.Label,p=e.Sprite,f=e.Component,b=e.size},function(e){d=e.ConfigHelper},function(e){g=e.xcore},function(e){v=e.MediaVideo},function(e){h=e.GiftMessageMgr},function(e){m=e.FightMgr},function(e){y=e.default}],execute:function(){var k,N,w,z,M,x,R,B,U,A,T,C,I,L,P,V,D,F,S,H,_,G,j;l._RF.push({},"55baa684rVPepx5juHccnIa","UnitBossMessage",void 0);var J=s.ccclass,O=s.property;e("UnitBossMessage",(k=J("UnitBossMessage"),N=O(u),w=O(u),z=O(u),M=O(c),x=O(c),R=O(c),B=O(c),U=O(p),A=O(p),T=O(v),k((L=t((I=function(e){function t(){for(var t,n=arguments.length,a=new Array(n),o=0;o<n;o++)a[o]=arguments[o];return t=e.call.apply(e,[this].concat(a))||this,i(t,"ndVideo",L,r(t)),i(t,"ndNormal",P,r(t)),i(t,"ndTop",V,r(t)),i(t,"lbName",D,r(t)),i(t,"lbName2",F,r(t)),i(t,"lbRank",S,r(t)),i(t,"lbRank2",H,r(t)),i(t,"sprAvatar",_,r(t)),i(t,"sprAvatar2",G,r(t)),i(t,"video",j,r(t)),t}n(t,e);var l=t.prototype;return l.setData=function(){var e=a(o().mark((function e(t){var n,i,r,a=this;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(y.setChildrenNodeSortByPriority(this.node,10),n=d.getInstance().getBossJoinConfig(t.lev+1),i=m.getInstance().findUser(t.userId)||{nickName:"匿名用户",iconUrl:null,lev:t.lev},this.ndTop.active=!1,this.ndNormal.active=t.lev>=50,this.ndVideo.active=t.lev<50,!(t.lev<50)){e.next=27;break}if(this.video.videoPlayer.node.active=!0,this.lbRank.string="周榜第"+(t.lev+1),this.lbName.string=i.nickName,g.res.remoteLoadSprite(i.iconUrl,this.sprAvatar,b(100,100)),!n){e.next=24;break}return e.prev=12,e.next=15,g.res.bundleLoadClip("resources","./res/video/"+n.name.split(".")[0]);case 15:r=e.sent,this.video.setData(r,(function(){a.ndTop.active=!0}),(function(){a.kill()})),e.next=22;break;case 19:e.prev=19,e.t0=e.catch(12),this.kill();case 22:e.next=25;break;case 24:this.kill();case 25:e.next=31;break;case 27:this.lbRank2.string=t.lev+1,this.lbName2.string=i.nickName,g.res.remoteLoadSprite(i.iconUrl,this.sprAvatar2,b(100,100)),this.scheduleOnce((function(){a.kill()}),1.3);case 31:case"end":return e.stop()}}),e,this,[[12,19]])})));return function(t){return e.apply(this,arguments)}}(),l.kill=function(){this.node.removeFromParent(),h.getInstance().killBossMessage(this),this.destroy()},t}(f)).prototype,"ndVideo",[N],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),P=t(I.prototype,"ndNormal",[w],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),V=t(I.prototype,"ndTop",[z],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),D=t(I.prototype,"lbName",[M],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),F=t(I.prototype,"lbName2",[x],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),S=t(I.prototype,"lbRank",[R],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),H=t(I.prototype,"lbRank2",[B],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),_=t(I.prototype,"sprAvatar",[U],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),G=t(I.prototype,"sprAvatar2",[A],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),j=t(I.prototype,"video",[T],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),C=I))||C));l._RF.pop()}}}));

System.register("chunks:///_virtual/UnitBuffCoin.ts",["./rollupPluginModLoBabelHelpers.js","cc","./xcore.ts"],(function(t){var e,i,o,s,n,r,a,h,u,c;return{setters:[function(t){e=t.applyDecoratedDescriptor,i=t.inheritsLoose,o=t.initializerDefineProperty,s=t.assertThisInitialized},function(t){n=t.cclegacy,r=t._decorator,a=t.Sprite,h=t.v3,u=t.Component},function(t){c=t.xcore}],execute:function(){var f,l,p,d,m;n._RF.push({},"4bbb2ZUP7lDdIReaBOP1S0e","UnitBuffCoin",void 0);var P=r.ccclass,y=r.property;t("UnitBuffCoin",(f=P("UnitBuffCoin"),l=y(a),f((m=e((d=function(t){function e(){for(var e,i=arguments.length,n=new Array(i),r=0;r<i;r++)n[r]=arguments[r];return e=t.call.apply(t,[this].concat(n))||this,o(e,"sprIcon",m,s(e)),e.targetRoleData=void 0,e.offsetPos=h(0,-16),e.tempPos=h(),e.hideTimeout=void 0,e}i(e,t);var n=e.prototype;return n.setData=function(t,e,i,o){var s=this;this.targetRoleData=t.data,c.res.bundleLoadSprite("resources","./res/image/common/"+e,this.sprIcon),this.node.active=!0,this.hideTimeout&&clearTimeout(this.hideTimeout),this.hideTimeout=setTimeout((function(){s.node&&(s.node.active=!1)}),1e3*i)},n.setPos=function(t){this.offsetPos.set(160*t/5-50,-16),this.node.setPosition(h(this.targetRoleData.pos.x+this.offsetPos.x,this.targetRoleData.pos.y+this.offsetPos.y))},n.hide=function(){this.hideTimeout&&clearTimeout(this.hideTimeout),this.node.active=!1},n.kill=function(){this.hideTimeout&&clearTimeout(this.hideTimeout),this.node.destroy()},n.update=function(t){this.node.position.x==this.targetRoleData.pos.x+this.offsetPos.x&&this.node.position.y==this.targetRoleData.pos.y+this.offsetPos.y||(this.tempPos.set(this.targetRoleData.pos.x+this.offsetPos.x,this.targetRoleData.pos.y+this.offsetPos.y),this.node.setPosition(this.tempPos))},e}(u)).prototype,"sprIcon",[l],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),p=d))||p));n._RF.pop()}}}));

System.register("chunks:///_virtual/UnitCloud.ts",["./rollupPluginModLoBabelHelpers.js","cc","./xcore.ts","./ConstGlobal.ts"],(function(t){var o,n,i,e,r,u,a,d,s,l,c,p,h;return{setters:[function(t){o=t.applyDecoratedDescriptor,n=t.inheritsLoose,i=t.initializerDefineProperty,e=t.assertThisInitialized},function(t){r=t.cclegacy,u=t._decorator,a=t.Node,d=t.UITransform,s=t.Sprite,l=t.v3,c=t.Component},function(t){p=t.xcore},function(t){h=t.C_Bundle}],execute:function(){var C,f,_,v,g;r._RF.push({},"bdc7cVyChZMfpIyxK37KXQ9","UnitCloud",void 0);var y=u.ccclass,m=u.property;t("UnitCloud",(C=y("UnitCloud"),f=m(a),C((g=o((v=function(t){function o(){for(var o,n=arguments.length,r=new Array(n),u=0;u<n;u++)r[u]=arguments[u];return o=t.call.apply(t,[this].concat(r))||this,i(o,"ndMapCloud",g,e(o)),o._maxY=30,o._width=void 0,o}n(o,t);var r=o.prototype;return r.onLoad=function(){this._width=this.node.getComponent(d).width},r.start=function(){this.initCloud()},r.initCloud=function(){for(var t=["game_cloud_01","game_cloud_01"],o=0;o<this.ndMapCloud.length;o++){var n=this.ndMapCloud[o].getComponent(s);p.res.bundleLoadSprite(h.abGame,"./res/img_unpack/"+t[o],n)}},r.updateCloud=function(){for(var t=0;t<this.ndMapCloud.length;t++){var o=this.ndMapCloud[t].position.x+.5,n=this.ndMapCloud[t].position.y;this.ndMapCloud[t].setPosition(l(o,n)),o>=1*this._width&&(o=-this._width,this.ndMapCloud[t].setPosition(l(o,0)))}},r.update=function(t){this.updateCloud()},o}(c)).prototype,"ndMapCloud",[f],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return[]}}),_=v))||_));r._RF.pop()}}}));

System.register("chunks:///_virtual/UnitDimond.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ConfigHelper.ts","./xcore.ts"],(function(e){var t,n,i,r,o,a,l,c,u,s,p,f;return{setters:[function(e){t=e.applyDecoratedDescriptor,n=e.inheritsLoose,i=e.initializerDefineProperty,r=e.assertThisInitialized},function(e){o=e.cclegacy,a=e._decorator,l=e.Label,c=e.Sprite,u=e.size,s=e.Component},function(e){p=e.ConfigHelper},function(e){f=e.xcore}],execute:function(){var m,b,g,d,y,h,I,v,D;o._RF.push({},"566a0ive+dIKaYBImHuwFSA","UnitDimond",void 0);var z=a.ccclass,N=a.property;e("UnitDimond",(m=z("UnitDimond"),b=N(l),g=N(l),d=N(c),m((I=t((h=function(e){function t(){for(var t,n=arguments.length,o=new Array(n),a=0;a<n;a++)o[a]=arguments[a];return t=e.call.apply(e,[this].concat(o))||this,i(t,"lbName",I,r(t)),i(t,"lbNum",v,r(t)),i(t,"sprIcon",D,r(t)),t}return n(t,e),t.prototype.setData=function(e,t){this.lbName.string=e.tips,this.lbNum.string="+"+t.toString();var n=p.getInstance().getBeadConfigById(e.skinFragmentId),i="./res/image/"+n.path+"/"+n.picture;f.res.remoteLoadSprite(i,this.sprIcon,u(90,90))},t}(s)).prototype,"lbName",[b],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),v=t(h.prototype,"lbNum",[g],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),D=t(h.prototype,"sprIcon",[d],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),y=h))||y));o._RF.pop()}}}));

System.register("chunks:///_virtual/UnitDimondDesc.ts",["./rollupPluginModLoBabelHelpers.js","cc","./xcore.ts"],(function(e){var r,t,n,i,o,a,u,c,s,l,p,f,m;return{setters:[function(e){r=e.applyDecoratedDescriptor,t=e.inheritsLoose,n=e.initializerDefineProperty,i=e.assertThisInitialized,o=e.asyncToGenerator,a=e.regeneratorRuntime},function(e){u=e.cclegacy,c=e._decorator,s=e.Sprite,l=e.Label,p=e.Component,f=e.log},function(e){m=e.xcore}],execute:function(){var b,h,g,d,y,v,D,w,x,S,z;u._RF.push({},"2f33eSmzKxJg6dSxESSAIV7","UnitDimondDesc",void 0);var I=c.ccclass,N=c.property;e("UnitDimondDesc",(b=I("UnitDimondDesc"),h=N(s),g=N(l),d=N(l),y=N(l),b((w=r((D=function(e){function r(){for(var r,t=arguments.length,o=new Array(t),a=0;a<t;a++)o[a]=arguments[a];return r=e.call.apply(e,[this].concat(o))||this,n(r,"sprIcon",w,i(r)),n(r,"lbName",x,i(r)),n(r,"lbNum",S,i(r)),n(r,"lbProp",z,i(r)),r}t(r,e);var u=r.prototype;return u.setData=function(){var e=o(a().mark((function e(r,t){return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:this.lbName.string=r.name,this.refreshImg("./res/image/"+r.path+"/"+r.picture),this.lbNum.string="持有数量："+t.num.toString(),this.lbProp.string="属性："+t.txt,f("dimonddata",t);case 5:case"end":return e.stop()}}),e,this)})));return function(r,t){return e.apply(this,arguments)}}(),u.refreshImg=function(){var e=o(a().mark((function e(r){var t;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,m.res.bundleLoadSprite("resources",r);case 2:t=e.sent,this.sprIcon.spriteFrame=t;case 4:case"end":return e.stop()}}),e,this)})));return function(r){return e.apply(this,arguments)}}(),r}(p)).prototype,"sprIcon",[h],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),x=r(D.prototype,"lbName",[g],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),S=r(D.prototype,"lbNum",[d],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),z=r(D.prototype,"lbProp",[y],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),v=D))||v));u._RF.pop()}}}));

System.register("chunks:///_virtual/UnitDimondReward.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ConfigHelper.ts","./Tool.ts","./xcore.ts","./UnitDimond.ts","./Net.ts","./FightMgr.ts"],(function(e){var n,t,r,i,a,o,u,l,s,c,d,p,f,m,g,b,D,y,h,I,k,w;return{setters:[function(e){n=e.applyDecoratedDescriptor,t=e.inheritsLoose,r=e.initializerDefineProperty,i=e.assertThisInitialized,a=e.asyncToGenerator,o=e.regeneratorRuntime},function(e){u=e.cclegacy,l=e._decorator,s=e.Node,c=e.Label,d=e.Sprite,p=e.Prefab,f=e.Component,m=e.log,g=e.instantiate,b=e.size},function(e){D=e.ConfigHelper},function(e){y=e.default},function(e){h=e.xcore},function(e){I=e.UnitDimond},function(e){k=e.default},function(e){w=e.FightMgr}],execute:function(){var v,R,U,N,x,C,z,L,A,F,M,S,B,_,H;u._RF.push({},"5957eU9UINA64/sKmYekynL","UnitDimondReward",void 0);var P=l.ccclass,T=l.property;e("UnitDimondReward",(v=P("UnitDimondReward"),R=T(s),U=T(c),N=T(c),x=T(d),C=T(s),z=T(p),v((F=n((A=function(e){function n(){for(var n,t=arguments.length,a=new Array(t),o=0;o<t;o++)a[o]=arguments[o];return n=e.call.apply(e,[this].concat(a))||this,r(n,"ndDetail",F,i(n)),r(n,"lbRank",M,i(n)),r(n,"lbName",S,i(n)),r(n,"sprAvatar",B,i(n)),r(n,"ndContent",_,i(n)),r(n,"pfbUnitDimond",H,i(n)),n}return t(n,e),n.prototype.setData=function(){var e=a(o().mark((function e(n,t){var r,i,a,u,l,s,c,d,p,f,v,R;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(m(n,t),n.user){e.next=4;break}return this.ndDetail.active=!1,e.abrupt("return");case 4:r=n.user[1],this.lbName.string=r.nickName||"匿名用户",this.lbRank.string=t+1,h.res.remoteLoadSprite(r.iconUrl,this.sprAvatar,b(70,70)),i=0;case 9:if(!(i<n.ids.length)){e.next=30;break}return a=n.ids[i],u=D.getInstance().getLotteryDebrisConfigByJsonId(a,n.targetId),l=w.getInstance().findUser(r.userId),s=0,l&&((c=D.getInstance().getWingSkinConfigByRankIndex(l.rank))&&(s=Number(c.number)),m("getWingSkinConfigByRankIndex",l,c,s)),d=null,1==h.gameData.gameMode?d=null==(p=u.normalRewardNum)?void 0:p.split("|"):2==h.gameData.gameMode&&(d=null==(f=u.hardRewardNum)?void 0:f.split("|")),d||(d=u.rewardNum.split("|")),m("nums",u,d),v=1,d[0]&&d[1]?v=y.randomNumber(parseInt(d[0]),parseInt(d[1])):d[0]&&(v=parseInt(d[0])),(R=g(this.pfbUnitDimond)).parent=this.ndContent,v+=s,R.getComponent(I).setData(u,v),e.next=27,k.addDimond(r.userId,u.skinFragmentId,v);case 27:i++,e.next=9;break;case 30:w.getInstance().updateDimondInfo(r.userId);case 31:case"end":return e.stop()}}),e,this)})));return function(n,t){return e.apply(this,arguments)}}(),n}(f)).prototype,"ndDetail",[R],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),M=n(A.prototype,"lbRank",[U],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),S=n(A.prototype,"lbName",[N],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),B=n(A.prototype,"sprAvatar",[x],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),_=n(A.prototype,"ndContent",[C],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),H=n(A.prototype,"pfbUnitDimond",[z],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),L=A))||L));u._RF.pop()}}}));

System.register("chunks:///_virtual/UnitExchange.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ConfigHelper.ts","./ConstGlobal.ts","./xcore.ts"],(function(n){var e,i,t,r,o,a,s,l,c,u,p,f,g,b;return{setters:[function(n){e=n.applyDecoratedDescriptor,i=n.inheritsLoose,t=n.initializerDefineProperty,r=n.assertThisInitialized},function(n){o=n.cclegacy,a=n._decorator,s=n.SpriteFrame,l=n.Sprite,c=n.Label,u=n.size,p=n.Component},function(n){f=n.ConfigHelper},function(n){g=n.C_View},function(n){b=n.xcore}],execute:function(){var m,h,d,y,I,C,_,k,v,w,z,F,D,x,B,O,S;o._RF.push({},"9c5c8CMZodDZIBp/dvM8oXX","UnitExchange",void 0);var L=a.ccclass,E=a.property;n("UnitExchange",(m=L("UnitExchange"),h=E([s]),d=E([s]),y=E(l),I=E(l),C=E(l),_=E(c),k=E(c),m((z=e((w=function(n){function e(){for(var e,i=arguments.length,o=new Array(i),a=0;a<i;a++)o[a]=arguments[a];return e=n.call.apply(n,[this].concat(o))||this,t(e,"sfFrames",z,r(e)),t(e,"sfOns",F,r(e)),t(e,"sprBg",D,r(e)),t(e,"sprOn",x,r(e)),t(e,"sprIcon",B,r(e)),t(e,"lbName",O,r(e)),t(e,"lbCost",S,r(e)),e._config=null,e._skinId=null,e}i(e,n);var o=e.prototype;return o.onLoad=function(){var n=this;this.node.on("click",(function(){b.ui.addView(g.ViewSkinDetail,{skinId:n._skinId,skinName:n._config.name})}),this)},o.setData=function(n){this._config=n,this.lbName.string=n.name,this.lbCost.string=n.yingYunConsume+"灵韵兑换";var e=f.getInstance().getSkinConfigByDebrisId(this._config.skinFragmentId),i=f.getInstance().getDebriConfigByJsonId(n.skinFragmentId);this._skinId=e.jsonId;var t="./res/image/"+i.path+"/"+i.icon;b.res.remoteLoadSprite(t,this.sprIcon,u(230,300)),this.sprBg.spriteFrame=this.sfFrames[e.quality-1],this.sprOn.spriteFrame=this.sfOns[e.quality-1]},e}(p)).prototype,"sfFrames",[h],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return[]}}),F=e(w.prototype,"sfOns",[d],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return[]}}),D=e(w.prototype,"sprBg",[y],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),x=e(w.prototype,"sprOn",[I],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),B=e(w.prototype,"sprIcon",[C],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),O=e(w.prototype,"lbName",[_],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),S=e(w.prototype,"lbCost",[k],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),v=w))||v));o._RF.pop()}}}));

System.register("chunks:///_virtual/UnitGiftDetail.ts",["./rollupPluginModLoBabelHelpers.js","cc"],(function(t){var e,i,r,n,l,o,a,c,u,p;return{setters:[function(t){e=t.applyDecoratedDescriptor,i=t.inheritsLoose,r=t.initializerDefineProperty,n=t.assertThisInitialized},function(t){l=t.cclegacy,o=t._decorator,a=t.Sprite,c=t.RichText,u=t.log,p=t.Component}],execute:function(){var s,f,b,y,D,h,g,m,d;l._RF.push({},"69554hHkIxDzZljHrHclduJ","UnitGiftDetail",void 0);var v=o.ccclass,z=o.property;t("UnitGiftDetail",(s=v("UnitGiftDetail"),f=z(a),b=z(c),y=z(c),s((g=e((h=function(t){function e(){for(var e,i=arguments.length,l=new Array(i),o=0;o<i;o++)l[o]=arguments[o];return e=t.call.apply(t,[this].concat(l))||this,r(e,"sprGift",g,n(e)),r(e,"lbName",m,n(e)),r(e,"lbDesc",d,n(e)),e}return i(e,t),e.prototype.setData=function(t){u(t)},e}(p)).prototype,"sprGift",[f],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),m=e(h.prototype,"lbName",[b],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),d=e(h.prototype,"lbDesc",[y],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),D=h))||D));l._RF.pop()}}}));

System.register("chunks:///_virtual/UnitGiftMessage.ts",["./rollupPluginModLoBabelHelpers.js","cc","./GiftMessageMgr.ts","./xcore.ts","./ConstGlobal.ts","./ConfigHelper.ts","./StringUtil.ts"],(function(e){var i,t,l,n,r,s,a,o,c,u,g,b,p,f,m,d,h,v,S,y,k,z,F,w,G,D;return{setters:[function(e){i=e.applyDecoratedDescriptor,t=e.inheritsLoose,l=e.initializerDefineProperty,n=e.assertThisInitialized},function(e){r=e.cclegacy,s=e._decorator,a=e.Node,o=e.Label,c=e.Sprite,u=e.RichText,g=e.v3,b=e.size,p=e.view,f=e.UITransform,m=e.tween,d=e.easing,h=e.log,v=e.Component},function(e){S=e.GiftMessageMgr},function(e){y=e.xcore},function(e){k=e.E_GiftMessageType,z=e.C_HeroGiftKeyToSkillType,F=e.C_GreatSkill,w=e.E_SkillType},function(e){G=e.ConfigHelper},function(e){D=e.StringUtil}],execute:function(){var P,B,I,N,A,C,T,_,M,R,K,x,L,U,E,H,j,J,V,W,q,O,Q,X,Y,Z,$,ee,ie,te,le,ne,re,se,ae,oe,ce,ue,ge,be,pe,fe,me,de,he,ve,Se;r._RF.push({},"25fb0bdkxdKKKktxQuqB5M3","UnitGiftMessage",void 0);var ye=s.ccclass,ke=s.property;e("UnitGiftMessage",(P=ye("UnitGiftMessage"),B=ke(a),I=ke(a),N=ke(a),A=ke(a),C=ke(o),T=ke(c),_=ke(o),M=ke(u),R=ke(a),K=ke(c),x=ke(a),L=ke(o),U=ke(o),E=ke(a),H=ke(c),j=ke(a),J=ke(c),V=ke(o),W=ke(a),q=ke(c),O=ke(a),Q=ke(u),P((Z=i((Y=function(e){function i(){for(var i,t=arguments.length,r=new Array(t),s=0;s<t;s++)r[s]=arguments[s];return i=e.call.apply(e,[this].concat(r))||this,l(i,"ndFrame01",Z,n(i)),l(i,"ndFrame02",$,n(i)),l(i,"ndDetail",ee,n(i)),l(i,"ndPgs",ie,n(i)),l(i,"lbPgs",te,n(i)),l(i,"sprPgsBar",le,n(i)),l(i,"lbName",ne,n(i)),l(i,"lbDesc",re,n(i)),l(i,"ndAvatar",se,n(i)),l(i,"sprAvatar",ae,n(i)),l(i,"ndSmallSkill",oe,n(i)),l(i,"lbSmallSkillNum",ce,n(i)),l(i,"lbSmallSkillDesc",ue,n(i)),l(i,"ndBigSkill",ge,n(i)),l(i,"sprBigSkill",be,n(i)),l(i,"ndGift",pe,n(i)),l(i,"sprGift",fe,n(i)),l(i,"lbGiftNum",me,n(i)),l(i,"ndReleaseSkill",de,n(i)),l(i,"sprReleaseSkill",he,n(i)),l(i,"ndKill",ve,n(i)),l(i,"lbKillDesc",Se,n(i)),i._defaultAvatar=null,i.tw=void 0,i.toPos=g(0,0),i.fromPos=g(0,0),i}t(i,e);var r=i.prototype;return r.onLoad=function(){var e=this;this.node.on(a.EventType.TOUCH_END,(function(){e.kill()}),this)},r.setData=function(e,i){var t=this;switch(this._defaultAvatar||(this._defaultAvatar=this.sprAvatar.spriteFrame),this.ndFrame01.active=!1,this.ndFrame02.active=!1,this.ndDetail.active=!1,this.ndSmallSkill.active=!1,this.ndBigSkill.active=!1,this.ndGift.active=!1,this.ndReleaseSkill.active=!1,this.ndKill.active=!1,this.sprAvatar.spriteFrame=null,this.sprGift.spriteFrame=null,this.sprBigSkill.spriteFrame=null,this.sprGift.spriteFrame=null,this.sprReleaseSkill.spriteFrame=null,this.lbDesc.string="",this.lbGiftNum.string="",this.lbKillDesc.string="",this.lbName.string="",this.lbPgs.string="",this.lbSmallSkillDesc.string="",this.lbSmallSkillNum.string="",this.lbName.string=D.sub(e.name||"匿名用户"+e.userId,10,!0),e.avatar?y.res.remoteLoadSprite(e.avatar,this.sprAvatar,b(110,110)):this._defaultAvatar&&(this.sprAvatar.spriteFrame=this._defaultAvatar),e.type==k.GetBigSkill?(this.toPos.set(-p.getVisibleSize().width/2+90,-550),this.fromPos.set(-p.getVisibleSize().width/2-this.node.getComponent(f).width,-550)):(this.toPos.set(-p.getVisibleSize().width/2+90,200*i),this.fromPos.set(-p.getVisibleSize().width/2-this.node.getComponent(f).width,200*i)),this.node.setPosition(this.fromPos),this.tw=m(this.node).delay(.1*i).to(.4,{position:this.toPos},{easing:d.cubicIn}).delay(1.2).call((function(){t.kill()})),this.tw.start(),e.type){case k.Gift:this.ndGift.active=!0,this.ndDetail.active=!0,this.ndFrame01.active=!0;var l=G.getInstance().getGiftConfigByJsonId(e.giftType);l.douyinGiftPicture?(y.res.bundleLoadSprite("resources","./res/image/common/"+l.douyinGiftPicture,this.sprGift,b(150,150)),this.lbGiftNum.string="x"+e.num):this.lbGiftNum.string=l.name+"x"+e.num;var n=z[e.giftType],r=F[n];if(n==w.AddHp&&(this.ndPgs.active=!1),r){var s=G.getInstance().getGreateSkillAbleLevelNeedGiftNum(r,e.totalNum),a=s[0],o=s[1],c=s[2],u=a-e.totalNum,g=G.getInstance().getWeaponConfigBySkillJsonId(o.jsonId);if(u<=0){if(this.ndPgs.active=!1,o.name){var v=o.name.split("级");v[v.length-1]}this.lbDesc.string="<size=24><color=#F86024>"+l.name+"</color><color=#F4F8FF>已升至满级</color></size>",h("最大等级")}else this.ndPgs.active=!0,this.lbPgs.string=e.totalNum+"/"+a,this.sprPgsBar.fillRange=e.totalNum/a,e.totalNum<c?this.lbDesc.string="<size=24><color=#F4F8FF>还差"+u+"个召唤</color><color=#F86024>"+g.name+"</color></size>":this.lbDesc.string="<size=24><color=#F4F8FF>还差"+u+"个升级</color><color=#F86024>"+g.name+"</color></size>"}else{var S=G.getInstance().getSkillConfigByType(n);this.lbDesc.string="<size=24><color=#F4F8FF>召唤"+S.name+"</color></size>"}break;case k.GetSmallSkill:this.ndSmallSkill.active=!0,this.ndFrame01.active=!0,this.ndDetail.active=!0,this.ndPgs.active=!1,this.lbDesc.string="<size=44><color=#5AE040><size>召唤</color></size>";var P=G.getInstance().getSkillConfigByType(e.skillType),B=G.getInstance().getWeaponConfigBySkillJsonId(P.jsonId);this.lbSmallSkillNum.string=P.name+"x"+e.num*((null==B?void 0:B.skillNum)||1),this.lbSmallSkillDesc.string=B.describe;break;case k.GetBigSkill:this.ndBigSkill.active=!0,this.ndFrame02.active=!0,this.ndDetail.active=!0,this.ndPgs.active=!1;var I=G.getInstance().getSkillConfigByType(e.skillType,e.lev),N=G.getInstance().getWeaponConfigBySkillJsonId(I.jsonId);this.lbDesc.string="<size=38><color=#FB7906><size>召唤"+I.name+"</color></size>",N.icon&&y.res.bundleLoadSprite("resources","./res/image/common/"+N.icon,this.sprBigSkill,b(300,300));break;case k.UseProp:this.ndReleaseSkill.active=!0,this.ndDetail.active=!0,this.ndPgs.active=!1,this.ndFrame01.active=!0;var A=G.getInstance().getSkillConfigByType(e.skillType,e.lev),C=G.getInstance().getWeaponConfigBySkillJsonId(A.jsonId);this.lbDesc.string="<size=28><color=#FFE348>释放"+A.name+"</color></size>",C&&C.icon&&y.res.bundleLoadSprite("resources","./res/image/common/"+C.icon,this.sprReleaseSkill,b(200,200));break;case k.Kill:this.ndFrame01.active=!0,this.ndDetail.active=!0,this.ndPgs.active=!1,this.lbDesc.string="<size=24><color=#5AE040>击杀boss "+e.monsterName+"</color></size>"}},r.kill=function(){this.node.removeFromParent(),S.getInstance().killMessage(this)},i}(v)).prototype,"ndFrame01",[B],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),$=i(Y.prototype,"ndFrame02",[I],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),ee=i(Y.prototype,"ndDetail",[N],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),ie=i(Y.prototype,"ndPgs",[A],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),te=i(Y.prototype,"lbPgs",[C],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),le=i(Y.prototype,"sprPgsBar",[T],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),ne=i(Y.prototype,"lbName",[_],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),re=i(Y.prototype,"lbDesc",[M],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),se=i(Y.prototype,"ndAvatar",[R],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),ae=i(Y.prototype,"sprAvatar",[K],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),oe=i(Y.prototype,"ndSmallSkill",[x],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),ce=i(Y.prototype,"lbSmallSkillNum",[L],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),ue=i(Y.prototype,"lbSmallSkillDesc",[U],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),ge=i(Y.prototype,"ndBigSkill",[E],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),be=i(Y.prototype,"sprBigSkill",[H],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),pe=i(Y.prototype,"ndGift",[j],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),fe=i(Y.prototype,"sprGift",[J],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),me=i(Y.prototype,"lbGiftNum",[V],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),de=i(Y.prototype,"ndReleaseSkill",[W],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),he=i(Y.prototype,"sprReleaseSkill",[q],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),ve=i(Y.prototype,"ndKill",[O],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Se=i(Y.prototype,"lbKillDesc",[Q],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),X=Y))||X));r._RF.pop()}}}));

System.register("chunks:///_virtual/UnitHurtTips.ts",["./rollupPluginModLoBabelHelpers.js","cc"],(function(t){var e,i,s,o,n,p,r,l,a,m,c;return{setters:[function(t){e=t.applyDecoratedDescriptor,i=t.inheritsLoose,s=t.initializerDefineProperty,o=t.assertThisInitialized},function(t){n=t.cclegacy,p=t._decorator,r=t.Label,l=t.Vec3,a=t.Color,m=t.Tween,c=t.Component}],execute:function(){var _,h,u,b,f;n._RF.push({},"a7f7aLEl85OZJwubzYEsMbM","UnitHurtTips",void 0);var T=p.ccclass,d=p.property;t("UnitHurtTips",(_=T("UnitHurtTips"),h=d(r),_((f=e((b=function(t){function e(){for(var e,i=arguments.length,n=new Array(i),p=0;p<i;p++)n[p]=arguments[p];return e=t.call.apply(t,[this].concat(n))||this,s(e,"lbTips",f,o(e)),e._tempPos=new l,e._tempPos2=new l,e._tempScale=new l,e._tempColor=new a,e._tempColor2=new a,e._tempSize=void 0,e._tempDelay=void 0,e._tempTween=void 0,e._cb=void 0,e}i(e,t);var n=e.prototype;return n.onLoad=function(){},n.setData=function(t,e){var i=this;this._tempPos.set(t.posx,t.posy+30),this.node.setPosition(this._tempPos),this._tempDelay=.2*Math.random(),this.formatNum(t.num,t.isBao),this._tempPos2.set(t.posx,t.posy+120),this._tempScale.set(1.4,1.4,1.4),this._cb=e,this._tempTween||(this._tempTween=new m(this.node).delay(this._tempDelay).to(.2,{position:this._tempPos2},{easing:"cubicInOut"}).to(.2,{scale:this._tempScale}).call((function(){i._tempScale.set(1,1,1),i.node.scale=i._tempScale,i._cb&&i._cb(i),i._tempTween.stop()}))),this._tempTween&&this._tempTween.start()},n.formatNum=function(t,e){void 0===e&&(e=!1);var i=t>0?t.toString():"miss";t<100?(this._tempSize=34,this._tempColor.set(255,245,226,255),this._tempColor2.set(64,41,0,255)):t<300?(this._tempSize=34,this._tempColor.set(255,245,140,255),this._tempColor2.set(200,128,0,255)):(this._tempSize=40,this._tempColor.set(255,245,140,255),this._tempColor2.set(255,0,0,255)),this.lbTips.color=this._tempColor,this.lbTips.outlineColor=this._tempColor2,this.lbTips.fontSize=this._tempSize,this.lbTips.string=e?"暴击"+i:i},e}(c)).prototype,"lbTips",[h],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),u=b))||u));n._RF.pop()}}}));

System.register("chunks:///_virtual/UnitItemScore.ts",["./rollupPluginModLoBabelHelpers.js","cc"],(function(e){var r,t,n,i,l,o,a,c,u;return{setters:[function(e){r=e.applyDecoratedDescriptor,t=e.inheritsLoose,n=e.initializerDefineProperty,i=e.assertThisInitialized},function(e){l=e.cclegacy,o=e._decorator,a=e.Label,c=e.Sprite,u=e.Component}],execute:function(){var s,p,b,f,m,y,g,h,S,v,d;l._RF.push({},"39d08HIBTlPIZQzcsYE/eDd","UnitItemScore",void 0);var z=o.ccclass,I=o.property;e("UnitItemScore",(s=z("UnitItemScore"),p=I(a),b=I(a),f=I(a),m=I(c),s((h=r((g=function(e){function r(){for(var r,t=arguments.length,l=new Array(t),o=0;o<t;o++)l[o]=arguments[o];return r=e.call.apply(e,[this].concat(l))||this,n(r,"lbName",h,i(r)),n(r,"lbRank",S,i(r)),n(r,"lbScore",v,i(r)),n(r,"sprAvatar",d,i(r)),r}return t(r,e),r.prototype.setData=function(e){this.lbName.string=e.nickName||"匿名用户",this.lbRank.string=e.rank,this.lbScore.string=e.score},r}(u)).prototype,"lbName",[p],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),S=r(g.prototype,"lbRank",[b],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),v=r(g.prototype,"lbScore",[f],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),d=r(g.prototype,"sprAvatar",[m],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),y=g))||y));l._RF.pop()}}}));

System.register("chunks:///_virtual/UnitLighting.ts",["./rollupPluginModLoBabelHelpers.js","cc"],(function(t){var i,n,e,r,o,a,c,s,l,u;return{setters:[function(t){i=t.applyDecoratedDescriptor,n=t.inheritsLoose,e=t.initializerDefineProperty,r=t.assertThisInitialized},function(t){o=t.cclegacy,a=t._decorator,c=t.Node,s=t.Vec3,l=t.v3,u=t.Component}],execute:function(){var h,g,p,d,f;o._RF.push({},"dabfdDKmgNLiYZKZt77GOh7","UnitLighting",void 0);var y=a.ccclass,L=a.property;t("UnitLighting",(h=y("UnitLighting"),g=L(c),h((f=i((d=function(t){function i(){for(var i,n=arguments.length,o=new Array(n),a=0;a<n;a++)o[a]=arguments[a];return i=t.call.apply(t,[this].concat(o))||this,e(i,"ndLight",f,r(i)),i}return n(i,t),i.prototype.setData=function(t,i){var n=s.distance(t,i),e=i.subtract(t),r=180*Math.atan2(e.y,e.x)/Math.PI;this.node.setPosition(t),this.node.eulerAngles=l(0,0,r),this.ndLight.scale=l(1,n/100,1),this.scheduleOnce((function(){}),.5)},i}(u)).prototype,"ndLight",[g],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),p=d))||p));o._RF.pop()}}}));

System.register("chunks:///_virtual/UnitLighting2.ts",["./rollupPluginModLoBabelHelpers.js","cc","./EffectMgr.ts","./xcore.ts","./Tool.ts","./ConfigHelper.ts"],(function(e){var t,n,i,r,o,s,a,c,f,l,u,p,g,h,m,d;return{setters:[function(e){t=e.applyDecoratedDescriptor,n=e.inheritsLoose,i=e.initializerDefineProperty,r=e.assertThisInitialized,o=e.asyncToGenerator,s=e.regeneratorRuntime},function(e){a=e.cclegacy,c=e._decorator,f=e.Animation,l=e.Vec3,u=e.Component,p=e.UITransform},function(e){g=e.EffectMgr},function(e){h=e.xcore},function(e){m=e.default},function(e){d=e.ConfigHelper}],execute:function(){var P,C,y,x,X;a._RF.push({},"a8ad8sw2OpFmYSpqrM67Xqr","UnitLighting2",void 0);var Y=c.ccclass,v=c.property;e("UnitLighting2",(P=Y("UnitLighting2"),C=v(f),P((X=t((x=function(e){function t(){for(var t,n=arguments.length,o=new Array(n),s=0;s<n;s++)o[s]=arguments[s];return t=e.call.apply(e,[this].concat(o))||this,i(t,"anim",X,r(t)),t.pos=new l,t.offsetPos=new l,t}return n(t,e),t.prototype.setData=function(){var e=o(s().mark((function e(t,n){var i,r,o,a,c,f,l,u=this;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:i=d.getInstance().getAnimConfigByJsonId(t),this.pos.set(n.x,n.y),this.node.setPosition(this.pos),r={sample:i.sample,duration:i.duration,speed:1,wrapMode:i.wrapMode,path:i.path,name:i.name,offsetX:i.XAxisOffset||0,offsetY:i.YAxisOffset||0},o=h.res.getAtlas("lighting"),a=0;case 6:if(!(a<r.sample)){e.next=17;break}if(c=r.name+"_"+(a<10?"0"+a:a),f=o.getSpriteFrame(c)){e.next=14;break}return e.next=12,h.res.bundleLoadSprite("resources","./res/image/"+r.path+"/"+c);case 12:f=e.sent,h.res.addAtlasSprite("lighting",c,f);case 14:a++,e.next=6;break;case 17:return null==i.XCenterPoint||null==i.XCenterPoint?i.XCenterPoint=.5:""==i.XCenterPoint&&(i.XCenterPoint=0),null==i.YCenterPoint||null==i.YCenterPoint?i.YCenterPoint=.5:""==i.YCenterPoint&&(i.YCenterPoint=0),e.next=21,m.createAnim(this.anim,r,o);case 21:this.offsetPos.set(r.offsetX,r.offsetY),this.anim.node.setPosition(this.offsetPos),null==(l=this.anim.node.getComponent(p))||l.setAnchorPoint(i.XCenterPoint,i.YCenterPoint),this.scheduleOnce((function(){g.getInstance().killLighting(u)}),r.duration);case 26:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}(),t}(u)).prototype,"anim",[C],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),y=x))||y));a._RF.pop()}}}));

System.register("chunks:///_virtual/UnitNewsEffect.ts",["./rollupPluginModLoBabelHelpers.js","cc","./EffectMgr.ts","./xcore.ts","./ConstGlobal.ts"],(function(e){var n,t,i,a,r,s,o,c,u,f,l,m,p,h;return{setters:[function(e){n=e.applyDecoratedDescriptor,t=e.inheritsLoose,i=e.initializerDefineProperty,a=e.assertThisInitialized,r=e.asyncToGenerator,s=e.regeneratorRuntime},function(e){o=e.cclegacy,c=e._decorator,u=e.sp,f=e.Component,l=e.v3},function(e){m=e.EffectMgr},function(e){p=e.xcore},function(e){h=e.E_EVENT}],execute:function(){var v,w,g,E,y;o._RF.push({},"aceee43VshLUp2KYlMSYNNO","UnitNewsEffect",void 0);var N=c.ccclass,d=c.property;e("UnitNewsEffect",(v=N("UnitNewsEffect"),w=d(u.Skeleton),v((y=n((E=function(e){function n(){for(var n,t=arguments.length,r=new Array(t),s=0;s<t;s++)r[s]=arguments[s];return n=e.call.apply(e,[this].concat(r))||this,i(n,"anim",y,a(n)),n}t(n,e);var o=n.prototype;return o.setData=function(){var e=r(s().mark((function e(n){var t=this;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!n.anim){e.next=5;break}return e.next=3,p.res.bundleLoadSpine("resources","./res/image/newseffect/"+n.anim,this.anim);case 3:"baize"==n.anim||"huodou"==n.anim||"tianhu"==n.anim||"qiongqi"==n.anim||"suanni"==n.anim||"taotie"==n.anim?(this.anim.setAnimation(0,"chuchang",!1),this.anim.node.setPosition(l(0,-200))):this.anim.setAnimation(0,"animation",!1),this.anim.setCompleteListener((function(){t.killNews(),n.anim02&&p.event.raiseEvent(h.NewsEffect,{anim:n.anim02})}));case 5:case"end":return e.stop()}}),e,this)})));return function(n){return e.apply(this,arguments)}}(),o.killNews=function(){this.anim.skeletonData=null,m.getInstance().killNewsEffect(this)},n}(f)).prototype,"anim",[w],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),g=E))||g));o._RF.pop()}}}));

System.register("chunks:///_virtual/UnitPowerRank.ts",["./rollupPluginModLoBabelHelpers.js","cc","./StringUtil.ts","./xcore.ts","./ConstGlobal.ts"],(function(e){var r,n,t,i,l,o,a,s,u,c,b,p,f,g,k,m;return{setters:[function(e){r=e.applyDecoratedDescriptor,n=e.inheritsLoose,t=e.initializerDefineProperty,i=e.assertThisInitialized},function(e){l=e.cclegacy,o=e._decorator,a=e.Label,s=e.Sprite,u=e.SpriteFrame,c=e.Node,b=e.log,p=e.size,f=e.Component},function(e){g=e.StringUtil},function(e){k=e.xcore},function(e){m=e.E_SkillType}],execute:function(){var h,y,R,S,w,d,v,z,P,N,T,C,G,U,F,L,_,x,A,D,B;l._RF.push({},"be5f5GnY21C/LoOdSTYtvfx","UnitPowerRank",void 0);var E=o.ccclass,Y=o.property;e("UnitPowerRank",(h=E("UnitPowerRank"),y=Y(a),R=Y(s),S=Y(a),w=Y(a),d=Y(s),v=Y(a),z=Y([u]),P=Y([c]),N=Y([a]),h((G=r((C=function(e){function r(){for(var r,n=arguments.length,l=new Array(n),o=0;o<n;o++)l[o]=arguments[o];return r=e.call.apply(e,[this].concat(l))||this,t(r,"lbRank",G,i(r)),t(r,"sprRank",U,i(r)),t(r,"lbScore",F,i(r)),t(r,"lbPower",L,i(r)),t(r,"sprAvatar",_,i(r)),t(r,"lbName",x,i(r)),t(r,"sfRanks",A,i(r)),t(r,"ndSkills",D,i(r)),t(r,"lbSkillNums",B,i(r)),r}n(r,e);var l=r.prototype;return l.setData=function(e,r,n){this.lbRank.string=""+(r+1),b(e);var t=e.score||0;2==n?t=e.killscore||0:3==n&&(t=e.giftscore||0),this.lbScore.string=g.numberToTenThousand(t),this.lbPower.string=g.numberToTenThousand(e.atkPower||0),this.lbName.string=g.sub(e.nickName||"匿名用户",9,!0),k.res.remoteLoadSprite(e.iconUrl,this.sprAvatar,p(70,70)),this.sprRank.spriteFrame=this.sfRanks[r<=2?r:3],this.lbRank.node.active=r>2,this.onRefreshSkills(e.role.skills)},l.onRefreshSkills=function(e){for(var r=this,n=[m.GreatFire,m.GreatLightning,m.GreatRock,m.GreatFires,m.GreatSkyRock],t=function(){var t=r.ndSkills[i],l=e.get(n[i]);t.getComponent(s).grayscale=!l,t.children.forEach((function(e){e.getComponent(s)&&(e.getComponent(s).grayscale=!l)})),t.getChildByName("lbNum").getComponent(a).string=l?l.level.toString():"0"},i=0;i<n.length;i++)t()},r}(f)).prototype,"lbRank",[y],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),U=r(C.prototype,"sprRank",[R],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),F=r(C.prototype,"lbScore",[S],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),L=r(C.prototype,"lbPower",[w],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),_=r(C.prototype,"sprAvatar",[d],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),x=r(C.prototype,"lbName",[v],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),A=r(C.prototype,"sfRanks",[z],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return[]}}),D=r(C.prototype,"ndSkills",[P],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return[]}}),B=r(C.prototype,"lbSkillNums",[N],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return[]}}),T=C))||T));l._RF.pop()}}}));

System.register("chunks:///_virtual/UnitRank.ts",["./rollupPluginModLoBabelHelpers.js","cc","./xcore.ts","./Tool.ts","./StringUtil.ts","./ConfigHelper.ts","./ConstGlobal.ts"],(function(i){var n,e,t,r,o,l,a,s,u,c,g,p,f,b,m,h,d,_;return{setters:[function(i){n=i.applyDecoratedDescriptor,e=i.inheritsLoose,t=i.initializerDefineProperty,r=i.assertThisInitialized},function(i){o=i.cclegacy,l=i._decorator,a=i.Sprite,s=i.Label,u=i.Node,c=i.v3,g=i.log,p=i.size,f=i.Component},function(i){b=i.xcore},function(i){m=i.default},function(i){h=i.StringUtil},function(i){d=i.ConfigHelper},function(i){_=i.C_View}],execute:function(){var w,v,k,y,I,W,N,R,S,z,U,C,L,P,T,V,F,G,x,B,D;o._RF.push({},"48442l44o5I/oK4m3BpY7QE","UnitRank",void 0);var A=l.ccclass,H=l.property;i("UnitRank",(w=A("UnitRank"),v=H(a),k=H(a),y=H(s),I=H(s),W=H(s),N=H(s),R=H(u),S=H(a),z=H(s),w((L=n((C=function(i){function n(){for(var n,e=arguments.length,o=new Array(e),l=0;l<e;l++)o[l]=arguments[l];return n=i.call.apply(i,[this].concat(o))||this,t(n,"sprAvatar",L,r(n)),t(n,"sprRankNum",P,r(n)),t(n,"lbRankNum",T,r(n)),t(n,"lbName",V,r(n)),t(n,"lbScore",F,r(n)),t(n,"lbGiftTitle",G,r(n)),t(n,"ndWing",x,r(n)),t(n,"sprWing",B,r(n)),t(n,"lbWing",D,r(n)),n._imgUrl=void 0,n._imgFunc=void 0,n._wingName=void 0,n._wingImg=void 0,n._wingProp=void 0,n._tempVec1=c(110,0,0),n._tempVec2=c(290,0,0),n}e(n,i);var o=n.prototype;return o.onLoad=function(){var i=this;this.ndWing.on("click",(function(){i._wingImg&&b.ui.addView(_.ViewWingShow,{img:i._wingImg,name:i._wingName,prop:i._wingProp})}),this)},o.setData=function(i,n){if(i){if(this.node.active=!0,this.lbRankNum.string=i.rank,this.lbName.string=h.sub(i.nickName||"匿名用户",10,!0),this.lbScore.string=i.score,this._imgUrl=i.avatarUrl,this._imgFunc||(this._imgFunc=m.debounce(this.refreshImg,60,!1)),this.lbGiftTitle.string="",this._imgFunc(),this._wingImg=null,0==n){var e=d.getInstance().getWingConfigByRankIndex(i.rank);if(e){var t=d.getInstance().getWingSkinConfigByRankIndex(i.rank);this._wingImg="./res/image/"+e.path+"/"+e.picture,this._wingName=e.name,this._wingProp=t.skillDescribe,this.lbWing.string=this._wingName,this.ndWing.active=!0,g("wingConfig",e),b.res.bundleLoadSprite("resources",this._wingImg,this.sprWing,p(68,68))}else this.ndWing.active=!1;this.lbScore.node.setPosition(this._tempVec1)}else if(this.lbScore.node.setPosition(this._tempVec2),this.ndWing.active=!1,3==n){var r=d.getInstance().getGiftRankTitleByScore(i.score);r&&(this.lbGiftTitle.string=r.giftLevel+"\n"+r.jsonId)}}else this.node.active=!1},o.refreshImg=function(){b.res.remoteLoadSprite(this._imgUrl,this.sprAvatar,p(70,70))},n}(f)).prototype,"sprAvatar",[v],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),P=n(C.prototype,"sprRankNum",[k],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),T=n(C.prototype,"lbRankNum",[y],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),V=n(C.prototype,"lbName",[I],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),F=n(C.prototype,"lbScore",[W],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),G=n(C.prototype,"lbGiftTitle",[N],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),x=n(C.prototype,"ndWing",[R],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),B=n(C.prototype,"sprWing",[S],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),D=n(C.prototype,"lbWing",[z],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),U=C))||U));o._RF.pop()}}}));

System.register("chunks:///_virtual/UnitRoleComp.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ConstGlobal.ts","./Tool.ts","./xcore.ts","./ConfigHelper.ts","./StringUtil.ts"],(function(e){var t,i,n,o,s,r,a,l,m,p,h,u,c,d,f,b,g,v,R,P,C,w,y,A,S,T,k,B,x,_,L;return{setters:[function(e){t=e.applyDecoratedDescriptor,i=e.inheritsLoose,n=e.initializerDefineProperty,o=e.assertThisInitialized,s=e.asyncToGenerator,r=e.regeneratorRuntime},function(e){a=e.cclegacy,l=e._decorator,m=e.Animation,p=e.Label,h=e.SpriteFrame,u=e.Sprite,c=e.Node,d=e.sp,f=e.Vec3,b=e.Color,g=e.size,v=e.v3,R=e.log,P=e.v2,C=e.Tween,w=e.Component,y=e.UITransform,A=e.warn},function(e){S=e.E_RoleState,T=e.E_RoleType,k=e.C_Bundle},function(e){B=e.default},function(e){x=e.xcore},function(e){_=e.ConfigHelper},function(e){L=e.StringUtil}],execute:function(){var D,N,F,H,I,z,U,G,O,W,X,Y,M,Z,J,j,E,V,q,K,Q,$,ee,te,ie,ne,oe,se,re,ae,le,me,pe,he,ue,ce,de,fe,be,ge,ve,Re,Pe,Ce,we,ye,Ae,Se,Te;a._RF.push({},"29221bHNehCFYfFcJLvXSB6","UnitRoleComp",void 0);var ke=l.ccclass,Be=l.property;e("UnitRoleComp",(D=ke("UnitRoleComp"),N=Be(m),F=Be(p),H=Be(p),I=Be([h]),z=Be([h]),U=Be([h]),G=Be(u),O=Be(p),W=Be(p),X=Be(p),Y=Be(c),M=Be(c),Z=Be(u),J=Be(u),j=Be(u),E=Be(p),V=Be(p),q=Be(d.Skeleton),K=Be(c),Q=Be(c),$=Be(u),ee=Be(d.Skeleton),te=Be(u),D((oe=t((ne=function(e){function t(){for(var t,i=arguments.length,s=new Array(i),r=0;r<i;r++)s[r]=arguments[r];return t=e.call.apply(e,[this].concat(s))||this,n(t,"anim",oe,o(t)),n(t,"lbHp",se,o(t)),n(t,"lbLev",re,o(t)),n(t,"sfRoleNameBgs",ae,o(t)),n(t,"sfPgsBgs",le,o(t)),n(t,"sfPgsBars",me,o(t)),n(t,"sprRoleName",pe,o(t)),n(t,"lbRoleName",he,o(t)),n(t,"lbName",ue,o(t)),n(t,"lbDesc",ce,o(t)),n(t,"ndUserDetail",de,o(t)),n(t,"ndPgs",fe,o(t)),n(t,"sprBar",be,o(t)),n(t,"sprLevBg",ge,o(t)),n(t,"sprGiftTitle",ve,o(t)),n(t,"lbGiftTitle",Re,o(t)),n(t,"lbInvincibleTime",Pe,o(t)),n(t,"animIconInvincible",Ce,o(t)),n(t,"ndUserData",we,o(t)),n(t,"ndAvatar",ye,o(t)),n(t,"sprAvatar",Ae,o(t)),n(t,"animAddHp",Se,o(t)),n(t,"sprWingAnim",Te,o(t)),t.fromRole=void 0,t.state=S.None,t.tempPos=new f,t.tempScale=new f,t.tempScale2=new f,t.tempPgsScale=new f,t.unpdateTowerShowFunc=void 0,t.sprRole=null,t.colorTime=0,t.tempColorRed=new b(240,0,0,200),t.tempColorGreen=new b(0,240,0,200),t.tempColorDefault=new b(255,255,255,255),t.tempColorHurt=new b(255,255,255,255),t.tempColorOutline=new b(255,255,255,255),t.tempColorZero=new b(255,255,255,255),t.tempLevBgPos=new f,t.tempLevTxtPos=new f,t._tempTween=void 0,t.finishCb=void 0,t}i(t,e);var a=t.prototype;return a.init=function(e){var t;this.unscheduleAllCallbacks(),this.fromRole=e,null==(t=this.tempScale)||t.set(0,0),this.node&&this.node.isValid&&(this.node.active=!0),this.state=S.None,!this.sprRole&&this.anim&&(this.sprRole=this.anim.getComponent(u)),this.clearAnim(),this.lbName&&(this.lbName.string=""+L.sub(e.data.nickName||"匿名用户",9,!0)),this.sprAvatar&&(e.data.iconUrl?(this.ndAvatar.active=!0,x.res.remoteLoadSprite(e.data.iconUrl,this.sprAvatar,g(68,68))):this.ndAvatar.active=!1),this.ndUserDetail&&(this.ndUserDetail.active=e.data.type==T.Hero),this.ndPgs&&(this.ndPgs.active=!1),this.sprRoleName&&(this.sprRoleName.spriteFrame=null,this.lbRoleName.string="",this.sprRoleName.node.active=!1,this.fromRole.data.type==T.Monster&&(this.ndPgs.setPosition(f.ZERO),this.lbRoleName.string=this.fromRole.data.monsterName,this.fromRole.checkIfSmallBoss()?(this.ndPgs.getComponent(u).spriteFrame=this.sfPgsBgs[1],this.sprBar.spriteFrame=this.sfPgsBars[1],this.sprRoleName.spriteFrame=this.sfRoleNameBgs[0],this.sprRoleName.node.active=!0,this.ndPgs.setPosition(v(0,260/this.fromRole.data.scale.x,0))):this.fromRole.checkIfBigBoss()?(this.ndPgs.getComponent(u).spriteFrame=this.sfPgsBgs[2],this.sprBar.spriteFrame=this.sfPgsBars[2],this.sprRoleName.spriteFrame=this.sfRoleNameBgs[1],this.sprRoleName.node.active=!0,this.ndPgs.setPosition(v(0,430,0))):(this.ndPgs.getComponent(u).spriteFrame=this.sfPgsBgs[0],this.sprBar.spriteFrame=this.sfPgsBars[0])))},a.setHp=function(){var e=this;this.ndPgs.active||(this.ndPgs.active=!0),this.lbHp.string=this.fromRole?this.fromRole.data.hpNum+"/"+this.fromRole.data.maxHp:"0";var t=this.fromRole?Math.max(.01,this.fromRole.data.hpNum/this.fromRole.data.maxHp):0;this.sprBar.fillRange=t,this.fromRole.data.type==T.Tower&&(this.unpdateTowerShowFunc||(this.unpdateTowerShowFunc=B.throttle((function(t){var i;if(t<=.3)e.sprRole.spriteFrame&&"tower_03"==(null==(i=e.sprRole)?void 0:i.spriteFrame.name)||x.res.bundleLoadSprite(k.abGame,"./res/img_unpack/tower_03",e.sprRole);else if(t<=.5){var n;e.sprRole.spriteFrame&&"tower_02"==(null==(n=e.sprRole)?void 0:n.spriteFrame.name)||x.res.bundleLoadSprite(k.abGame,"./res/img_unpack/tower_02",e.sprRole)}else{var o;e.sprRole.spriteFrame&&"tower_01"==(null==(o=e.sprRole)?void 0:o.spriteFrame.name)||x.res.bundleLoadSprite(k.abGame,"./res/img_unpack/tower_01",e.sprRole)}}),3e3)),this.unpdateTowerShowFunc(t))},a.getCompAnimNode=function(){var e;return null==(e=this.anim)?void 0:e.node},a.setRankInfo=function(e){if(void 0===e&&(e=v(0,0)),this.fromRole&&this.fromRole.data.type==T.Hero&&this.lbDesc){var t=this.fromRole.getUserRank();this.lbDesc.string=t<0?"":"全服第"+(t+1);var i,n=t+1;this.tempLevTxtPos.set(0,0);var o=_.getInstance().getRankRewardByRankIndex(n);o&&1==o.rewardType&&o.rewardId?this.updateWingAnim(o.rewardId):this.sprWingAnim&&(this.sprWingAnim.spriteFrame=null,this.sprWingAnim.getComponent(m).enabled=!1),n<=0?(this.tempLevBgPos.set(0,0),i=null,this.lbDesc.enableOutline=!1):1==n?(this.tempLevBgPos.set(0,10+e.y),i="game_role_levbg01",this.lbDesc.enableOutline=!0,this.tempColorOutline.set(180,38,38,255),this.tempLevTxtPos.set(0,-9),this.lbDesc.outlineColor=this.tempColorOutline):2==n?(this.tempLevBgPos.set(0,2+e.y),i="game_role_levbg02",this.tempLevTxtPos.set(0,0),this.lbDesc.enableOutline=!0,this.tempColorOutline.set(38,58,180,255),this.lbDesc.outlineColor=this.tempColorOutline):3==n?(this.tempLevBgPos.set(0,2+e.y),i="game_role_levbg03",this.tempLevTxtPos.set(0,-2),this.lbDesc.enableOutline=!0,this.tempColorOutline.set(135,63,35,255),this.lbDesc.outlineColor=this.tempColorOutline):n<=100?(this.tempLevBgPos.set(0,-2+e.y),i="game_role_levbg04",this.lbDesc.enableOutline=!0,this.tempColorOutline.set(71,71,100,255),this.tempLevTxtPos.set(0,4),this.lbDesc.outlineColor=this.tempColorOutline):(this.tempLevBgPos.set(0,0+e.y),i="game_role_levbg00",this.lbDesc.enableOutline=!1);var s=i?"./res/img_unpack/"+i:null;s?(x.res.bundleLoadSprite(k.abGame,s,this.sprLevBg),this.ndUserData.setPosition(v(0,42+e.y)),this.sprGiftTitle.node.setPosition(v(0,150))):(this.sprLevBg.spriteFrame=null,this.ndUserData.setPosition(v(0,20+e.y))),this.sprLevBg.node.setPosition(this.tempLevBgPos),this.lbDesc.node.setPosition(this.tempLevTxtPos)}},a.setGiftTitleInfo=function(e){this.sprGiftTitle.node.active=!0;var t="./res/image/"+e.path+"/"+e.picture;x.res.remoteLoadSprite(t,this.sprGiftTitle,g(90,90)),this.lbGiftTitle.string=e.jsonId},a.setColor=function(e,t){this._tempTween&&this._tempTween.stop(),this.colorTime=t||0,this.sprRole&&(1==e?this.sprRole.color=this.tempColorRed:2==e?this.sprRole.color=this.tempColorGreen:(this.colorTime=0,this.sprRole.color=this.tempColorDefault)),e&&R("color::::::",e,t)},a.setLev=function(e){this.lbLev&&(this.lbLev.string="南天门等级"+e)},a.doAttackAnim=function(){var e=s(r().mark((function e(t,i){return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:this.updateAnim(t,i);case 1:case"end":return e.stop()}}),e,this)})));return function(t,i){return e.apply(this,arguments)}}(),a.clear=function(e){void 0===e&&(e=!1),this.clearAnim(),this.node&&this.node.isValid&&(e?this.node.destroy():(this.node.active=!1,this.setPosAtonce(P(-3e3,-3e3))))},a.clearAnim=function(){var e;(null==(e=this.fromRole)||null==(e=e.data)?void 0:e.type)!=T.Tower&&(this.anim&&(this.sprRole.spriteFrame=null,this.anim.getComponent(m).enabled=!1),this.setColor())},a.setPosAtonce=function(e){var t;null==(t=this.node)||t.setPosition(v(e.x,e.y))},a.getFromRole=function(){return this.fromRole?this.fromRole:null},a.getRoleType=function(){return this.fromRole?this.fromRole.data.type:null},a.switchState=function(){switch(this.state){case S.None:break;case S.Idle:case S.Move:case S.Attack:this.updateAnim();break;case S.Hurt:break;case S.Skill:this.updateAnim();break;case S.WaitRelive:case S.Dizziness:case S.Dead:}},a.onDead=function(){this.setColor(),this.clearAnim(),this.node.removeFromParent()},a.playHit=function(){var e=this;if(this.colorTime>0)this._tempTween&&this._tempTween.stop();else{if(!this._tempTween){var t=this;this.tempColorHurt.set(255,120,120,220),this._tempTween=new C(this.sprRole).to(.2,{color:this.tempColorHurt}).to(.05,{color:this.tempColorZero}).to(.2,{color:this.tempColorHurt}).to(.2,{color:this.tempColorHurt}).to(.05,{color:this.tempColorZero}).to(.2,{color:this.tempColorHurt}).to(.2,{color:this.tempColorHurt}).to(.05,{color:this.tempColorZero}).to(.2,{color:this.tempColorHurt}).call((function(){t.sprRole.color=t.tempColorZero,e._tempTween.stop()}))}this._tempTween.stop(),this._tempTween.start()}},a.update=function(e){this.fromRole&&this.fromRole.data&&(this.fromRole.data.type!=T.Tower?((this.fromRole.data.scale.y!=this.anim.node.scale.y||this.fromRole.data.moveDir>0&&this.anim.node.scale.x<0||this.fromRole.data.moveDir<0&&this.anim.node.scale.x>0)&&this.tempScale.set(this.fromRole.data.moveDir*this.fromRole.data.scale.y,this.fromRole.data.scale.y),this.tempScale!=this.anim.node.scale&&(this.tempPgsScale.set(this.fromRole.data.moveDir/this.anim.node.scale.y,1/this.anim.node.scale.y),this.anim.node.setScale(this.tempScale),this.ndPgs.setScale(this.tempPgsScale),this.tempScale2.set(this.tempScale.x/Math.abs(this.tempScale.x),this.tempScale.y/Math.abs(this.tempScale.y)),this.sprWingAnim.node.setScale(this.tempScale2)),this.node.position.x==this.fromRole.data.pos.x&&this.node.position.y==this.fromRole.data.pos.y||(this.node.getPosition(this.tempPos),this.tempPos=this.tempPos.lerp(v(this.fromRole.data.pos.x,this.fromRole.data.pos.y),.3),this.node.setPosition(this.tempPos)),this.colorTime>0&&(this.colorTime-=e,this.colorTime<=0&&this.setColor()),this.state!=this.fromRole.data.state&&(this.state=this.fromRole.data.state,this.switchState())):this.state!=S.Dead&&(this.fromRole.data.auotSaveHpTime>0?this.animAddHp&&(this.animAddHp.node.active||(this.animAddHp.node.active=!0)):this.animAddHp.node.active&&(this.animAddHp.node.active=!1)))},a.updateAnim=function(){var e=s(r().mark((function e(t,i){var n,o,s,a,l,m,p,h,u,c=this;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(void 0===i&&(i=1),this.state!=S.Move&&this.state!=S.Attack&&this.state!=S.Idle&&this.state!=S.Skill){e.next=34;break}if(n=_.getInstance().getAnimConfigByJsonId(this.state==S.Move||this.state==S.Idle?this.fromRole.data.moveAnimation:this.state==S.Skill?this.fromRole.data.skillAnimation:this.fromRole.data.attackAnimation)){e.next=6;break}return A("config null:",this.fromRole.data.jsonId,this.state,this.fromRole.data.moveAnimation,this.fromRole.data.skillAnimation,this.fromRole.data.attackAnimation),e.abrupt("return");case 6:o=n.duration,i<o&&(o=i),s={sample:n.sample,duration:o,speed:1,wrapMode:n.wrapMode,path:n.path,name:n.name},a=s.name.split("-")[0]||"default",l=x.res.getAtlas(a),(m=this.anim.node.getComponent(y)).width,m.height,p=0;case 15:if(!(p<s.sample)){e.next=28;break}if(h=s.name+"_"+(p<10?"0"+p:p),null==l?void 0:l.getSpriteFrame(h)){e.next=25;break}return e.next=21,x.res.bundleLoadSprite("resources","./res/image/"+s.path+"/"+h);case 21:u=e.sent,x.res.addAtlasSprite(a,h,u),u.width,u.height;case 25:p++,e.next=15;break;case 28:return null==n.XCenterPoint||null==n.XCenterPoint?n.XCenterPoint=.5:""==n.XCenterPoint&&(n.XCenterPoint=0),null==n.YCenterPoint||null==n.YCenterPoint?n.YCenterPoint=.5:""==n.YCenterPoint&&(n.YCenterPoint=0),e.next=32,B.createAnim(this.anim,s,l);case 32:null==m||m.setAnchorPoint(Number(n.XCenterPoint),Number(n.YCenterPoint)),t?t(o):this.state==S.Skill&&this.scheduleOnce((function(){c.fromRole&&c.fromRole.isRoleAlive()&&c.fromRole.setState(S.Idle)}),o/2);case 34:case"end":return e.stop()}}),e,this)})));return function(t,i){return e.apply(this,arguments)}}(),a.updateWingAnim=function(){var e=s(r().mark((function e(t){var i,n,o,s,a,l,p,h,u;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(i=_.getInstance().getWingConfigByJsonId(t),R(i),!i){e.next=35;break}if(!i.animationId){e.next=35;break}if(n=_.getInstance().getAnimConfigByJsonId(i.animationId)){e.next=7;break}return e.abrupt("return");case 7:this.sprWingAnim.getComponent(m).enabled=!0,o={sample:n.sample,duration:n.duration,speed:1,wrapMode:n.wrapMode,path:n.path,name:n.name},s=o.name.split("-")[0]||"default",a=x.res.getAtlas(s),(l=this.sprWingAnim.node.getComponent(y)).width,l.height,p=0;case 15:if(!(p<o.sample)){e.next=28;break}if(h=o.name+"_"+(p<10?"0"+p:p),null==a?void 0:a.getSpriteFrame(h)){e.next=25;break}return e.next=21,x.res.bundleLoadSprite("resources","./res/image/"+o.path+"/"+h);case 21:u=e.sent,x.res.addAtlasSprite(s,h,u),u.width,u.height;case 25:p++,e.next=15;break;case 28:return null==n.XCenterPoint||null==n.XCenterPoint?n.XCenterPoint=.5:""==n.XCenterPoint&&(n.XCenterPoint=0),null==n.YCenterPoint||null==n.YCenterPoint?n.YCenterPoint=.5:""==n.YCenterPoint&&(n.YCenterPoint=0),null==l||l.setAnchorPoint(Number(n.XCenterPoint),Number(n.YCenterPoint)),this.sprWingAnim.node.setPosition(v(10,210,0)),e.next=34,B.createAnim(this.sprWingAnim.getComponent(m),o,a);case 34:R("wingConfig",n,this.anim.node.position,this.anim.getComponent(y).anchorPoint,this.sprWingAnim.node.position,this.sprWingAnim.node.getComponent(y).anchorPoint);case 35:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),t}(w)).prototype,"anim",[N],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),se=t(ne.prototype,"lbHp",[F],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),re=t(ne.prototype,"lbLev",[H],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),ae=t(ne.prototype,"sfRoleNameBgs",[I],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return[]}}),le=t(ne.prototype,"sfPgsBgs",[z],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return[]}}),me=t(ne.prototype,"sfPgsBars",[U],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return[]}}),pe=t(ne.prototype,"sprRoleName",[G],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),he=t(ne.prototype,"lbRoleName",[O],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),ue=t(ne.prototype,"lbName",[W],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),ce=t(ne.prototype,"lbDesc",[X],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),de=t(ne.prototype,"ndUserDetail",[Y],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),fe=t(ne.prototype,"ndPgs",[M],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),be=t(ne.prototype,"sprBar",[Z],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),ge=t(ne.prototype,"sprLevBg",[J],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),ve=t(ne.prototype,"sprGiftTitle",[j],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Re=t(ne.prototype,"lbGiftTitle",[E],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Pe=t(ne.prototype,"lbInvincibleTime",[V],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Ce=t(ne.prototype,"animIconInvincible",[q],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),we=t(ne.prototype,"ndUserData",[K],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),ye=t(ne.prototype,"ndAvatar",[Q],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Ae=t(ne.prototype,"sprAvatar",[$],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Se=t(ne.prototype,"animAddHp",[ee],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Te=t(ne.prototype,"sprWingAnim",[te],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),ie=ne))||ie));a._RF.pop()}}}));

System.register("chunks:///_virtual/UnitRoundRank.ts",["./rollupPluginModLoBabelHelpers.js","cc","./xcore.ts","./StringUtil.ts"],(function(e){var n,r,t,i,l,o,a,u,c,b,p,s,f;return{setters:[function(e){n=e.applyDecoratedDescriptor,r=e.inheritsLoose,t=e.initializerDefineProperty,i=e.assertThisInitialized},function(e){l=e.cclegacy,o=e._decorator,a=e.Sprite,u=e.Node,c=e.Label,b=e.size,p=e.Component},function(e){s=e.xcore},function(e){f=e.StringUtil}],execute:function(){var g,m,R,d,h,y,S,z,k,w,v,N,U,D,C,L,x,A,F,I,_,B,H;l._RF.push({},"e3369BgzmxFw72HdBQ7L2F3","UnitRoundRank",void 0);var P=o.ccclass,j=o.property;e("UnitRoundRank",(g=P("UnitRoundRank"),m=j(a),R=j(a),d=j(u),h=j(c),y=j(c),S=j(c),z=j(c),k=j(c),w=j(c),v=j(c),g((D=n((U=function(e){function n(){for(var n,r=arguments.length,l=new Array(r),o=0;o<r;o++)l[o]=arguments[o];return n=e.call.apply(e,[this].concat(l))||this,t(n,"sprAvatar",D,i(n)),t(n,"sprRankNum",C,i(n)),t(n,"ndIconUp",L,i(n)),t(n,"lbRankNum",x,i(n)),t(n,"lbName",A,i(n)),t(n,"lbScore",F,i(n)),t(n,"lbScoreDesc",I,i(n)),t(n,"lbRoundNum",_,i(n)),t(n,"lbRoundChange",B,i(n)),t(n,"lbRankChange",H,i(n)),n}return r(n,e),n.prototype.setData=function(e,n,r,t,i,l,o,a){this.lbName.string=f.sub(n||"匿名用户",9,!0),s.res.remoteLoadSprite(r,this.sprAvatar,b(70,70)),this.lbRankNum.string=""+(t+1),this.lbScore.string=e.score,this.lbRankChange.string=o<0?"":(o+1).toString(),this.ndIconUp.active=l>0&&o<l,this.lbScoreDesc.node.active=e.poolScore,this.lbScoreDesc.string="积分池 +"+e.poolScore+"\n"+(a>0?"关卡加成 +"+a:"")},n}(p)).prototype,"sprAvatar",[m],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),C=n(U.prototype,"sprRankNum",[R],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),L=n(U.prototype,"ndIconUp",[d],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),x=n(U.prototype,"lbRankNum",[h],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),A=n(U.prototype,"lbName",[y],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),F=n(U.prototype,"lbScore",[S],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),I=n(U.prototype,"lbScoreDesc",[z],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),_=n(U.prototype,"lbRoundNum",[k],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),B=n(U.prototype,"lbRoundChange",[w],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),H=n(U.prototype,"lbRankChange",[v],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),N=U))||N));l._RF.pop()}}}));

System.register("chunks:///_virtual/UnitSelectGameLevel.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ConfigHelper.ts","./xcore.ts"],(function(e){var t,n,i,l,r,a,o,c,s,u,v,p,b;return{setters:[function(e){t=e.applyDecoratedDescriptor,n=e.inheritsLoose,i=e.initializerDefineProperty,l=e.assertThisInitialized},function(e){r=e.cclegacy,a=e._decorator,o=e.Node,c=e.Label,s=e.Color,u=e.Sprite,v=e.Component},function(e){p=e.ConfigHelper},function(e){b=e.xcore}],execute:function(){var f,g,h,d,m,L,S,x,y;r._RF.push({},"075a1poB45OFoI50l9mrFwC","UnitSelectGameLevel",void 0);var I=a.ccclass,D=a.property;e("UnitSelectGameLevel",(f=I("UnitSelectGameLevel"),g=D(o),h=D(o),d=D(c),f((S=t((L=function(e){function t(){for(var t,n=arguments.length,r=new Array(n),a=0;a<n;a++)r[a]=arguments[a];return t=e.call.apply(e,[this].concat(r))||this,i(t,"ndOff",S,l(t)),i(t,"ndOn",x,l(t)),i(t,"lbLev",y,l(t)),t.cb=null,t.levIndex=void 0,t.isAbleSelect=!1,t}n(t,e);var r=t.prototype;return r.start=function(){var e=this;this.node.on("click",(function(){!e.isAbleSelect||b.gameData.gameLev<e.levIndex||e.cb&&e.cb(e.levIndex)}),this)},r.setData=function(e,t,n){this.cb=n,this.levIndex=t+1,this.lbLev.string=this.levIndex.toString(),this.isAbleSelect=p.getInstance().getAbleSelectLev(this.levIndex)||1==this.levIndex,b.gameData.gameLev||(b.gameData.gameLev=1),this.lbLev.color=b.gameData.gameLev<this.levIndex||!this.isAbleSelect?new s(180,180,180,255):new s(52,36,24,255),this.ndOn.getComponent(u).grayscale=b.gameData.gameLev<this.levIndex||!this.isAbleSelect},t}(v)).prototype,"ndOff",[g],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),x=t(L.prototype,"ndOn",[h],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),y=t(L.prototype,"lbLev",[d],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),m=L))||m));r._RF.pop()}}}));

System.register("chunks:///_virtual/UnitSelectGameTime.ts",["./rollupPluginModLoBabelHelpers.js","cc","./xcore.ts"],(function(e){var t,i,n,r,o,c,a,l,u,s,p;return{setters:[function(e){t=e.applyDecoratedDescriptor,i=e.inheritsLoose,n=e.initializerDefineProperty,r=e.assertThisInitialized},function(e){o=e.cclegacy,c=e._decorator,a=e.Sprite,l=e.Label,u=e.Color,s=e.Component},function(e){p=e.xcore}],execute:function(){var m,b,f,y,_,d,h;o._RF.push({},"221c3qmsDVG77Zy4JgDcW8E","UnitSelectGameTime",void 0);var g=c.ccclass,T=c.property;e("UnitSelectGameTime",(m=g("UnitSelectGameTime"),b=T(a),f=T(l),m((d=t((_=function(e){function t(){for(var t,i=arguments.length,o=new Array(i),c=0;c<i;c++)o[c]=arguments[c];return t=e.call.apply(e,[this].concat(o))||this,n(t,"sprFrame",d,r(t)),n(t,"lbTime",h,r(t)),t._index=void 0,t}i(t,e);var o=t.prototype;return o.start=function(){},o.setData=function(e,t,i){this._index=t,this.lbTime.string=e+"秒",p.res.bundleLoadSprite("resources",t!=i?"./img_unpack/common_btn_blue":"./img_unpack/common_btn_yellow02",this.sprFrame),this.lbTime.color=t==i?new u(245,245,245,255):new u(52,36,24,255)},o.update=function(e){},t}(s)).prototype,"sprFrame",[b],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),h=t(_.prototype,"lbTime",[f],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),y=_))||y));o._RF.pop()}}}));

System.register("chunks:///_virtual/UnitSelectGameType.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ConstGlobal.ts","./xcore.ts"],(function(e){var t,n,r,i,a,o,s,c,l,u,p,m,f,g,h,b,y;return{setters:[function(e){t=e.applyDecoratedDescriptor,n=e.inheritsLoose,r=e.initializerDefineProperty,i=e.assertThisInitialized,a=e.asyncToGenerator,o=e.regeneratorRuntime},function(e){s=e.cclegacy,c=e._decorator,l=e.Label,u=e.Sprite,p=e.director,m=e.Component},function(e){f=e.C_Bundle,g=e.C_Scene,h=e.C_View,b=e.E_EVENT},function(e){y=e.xcore}],execute:function(){var d,v,G,S,w,T,C,I,_;s._RF.push({},"43d136/P6xI7ranqG/bVcc1","UnitSelectGameType",void 0);var x=c.ccclass,D=c.property;e("UnitSelectGameType",(d=x("UnitSelectGameType"),v=D(l),G=D(l),S=D(u),d((C=t((T=function(e){function t(){for(var t,n=arguments.length,a=new Array(n),o=0;o<n;o++)a[o]=arguments[o];return t=e.call.apply(e,[this].concat(a))||this,r(t,"lbName",C,i(t)),r(t,"lbCost",I,i(t)),r(t,"sprBg",_,i(t)),t.jsonId=null,t}n(t,e);var s=t.prototype;return s.onLoad=function(){var e=this;this.node.on("click",(function(){y.gameData.gameTypeJsonId=e.jsonId,y.gameData.gameType=1,"Main"===p.getScene().name?y.ui.switchScene(f.abGame,g.Game):(y.ui.closeView(h.ViewSelectGameType),y.event.raiseEvent(b.GameConfig))}),this)},s.setData=function(e){this.lbName.string=e.name;var t="./res/image/"+e.path+"/"+e.picture;this.refreshImg(t),this.jsonId=e.jsonId;var n=["日","周","月"][e.type-1];this.lbCost.string="每"+n+"获得"+e.limitation+"次挑战机会"},s.refreshImg=function(){var e=a(o().mark((function e(t){var n;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,y.res.bundleLoadSprite("resources",t);case 2:n=e.sent,this.sprBg.spriteFrame=n;case 4:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),t}(m)).prototype,"lbName",[v],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),I=t(T.prototype,"lbCost",[G],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),_=t(T.prototype,"sprBg",[S],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),w=T))||w));s._RF.pop()}}}));

System.register("chunks:///_virtual/UnitSelectSkin.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ConfigHelper.ts","./Tool.ts","./xcore.ts"],(function(e){var n,t,a,r,i,o,s,c,l,u,p,m,f,d;return{setters:[function(e){n=e.applyDecoratedDescriptor,t=e.inheritsLoose,a=e.initializerDefineProperty,r=e.assertThisInitialized,i=e.asyncToGenerator,o=e.regeneratorRuntime},function(e){s=e.cclegacy,c=e._decorator,l=e.Animation,u=e.Component,p=e.v3},function(e){m=e.ConfigHelper},function(e){f=e.default},function(e){d=e.xcore}],execute:function(){var h,S,g,y,v;s._RF.push({},"0226e2lN9JFhbr+rZVIa+tz","UnitSelectSkin",void 0);var b=c.ccclass,k=c.property;e("UnitSelectSkin",(h=b("UnitSelectSkin"),S=k(l),h((v=n((y=function(e){function n(){for(var n,t=arguments.length,i=new Array(t),o=0;o<t;o++)i[o]=arguments[o];return n=e.call.apply(e,[this].concat(i))||this,a(n,"anim",v,r(n)),n}return t(n,e),n.prototype.setData=function(){var e=i(o().mark((function e(n){var t,a,r,i,s,c,l,u=this;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t=m.getInstance().getAnimConfigByJsonId(n.moveAnimation),1,a={sample:t.sample,duration:t.duration,speed:1,wrapMode:t.wrapMode,path:t.path,name:t.name},r=a.name.split("-")[0]||"default",i=d.res.getAtlas(r),this.node.scale=p(1.5*n.animationScale||3,1.5*n.animationScale||3,1.5*n.animationScale||3),s=0;case 7:if(!(s<a.sample)){e.next=18;break}if(c=a.name+"_"+(s<10?"0"+s:s),null==i?void 0:i.getSpriteFrame(c)){e.next=15;break}return e.next=13,d.res.bundleLoadSprite("resources","./res/image/"+a.path+"/"+c);case 13:l=e.sent,d.res.addAtlasSprite(r,c,l);case 15:s++,e.next=7;break;case 18:return e.next=20,f.createAnim(this.anim,a,i,!1);case 20:this.scheduleOnce((function(){u.anim.play(a.name)}));case 21:case"end":return e.stop()}}),e,this)})));return function(n){return e.apply(this,arguments)}}(),n}(u)).prototype,"anim",[S],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),g=y))||g));s._RF.pop()}}}));

System.register("chunks:///_virtual/UnitSkillComp.ts",["./rollupPluginModLoBabelHelpers.js","cc","./xcore.ts","./ConfigHelper.ts"],(function(t){var e,i,o,s,n,r,a,l,p,c,h,f,u,g,d;return{setters:[function(t){e=t.applyDecoratedDescriptor,i=t.inheritsLoose,o=t.initializerDefineProperty,s=t.assertThisInitialized,n=t.asyncToGenerator,r=t.regeneratorRuntime},function(t){a=t.cclegacy,l=t._decorator,p=t.Sprite,c=t.v3,h=t.tween,f=t.Component,u=t.size},function(t){g=t.xcore},function(t){d=t.ConfigHelper}],execute:function(){var y,S,k,m,v,P,x;a._RF.push({},"0b97aftP4pKcaEPpBC9ehHH","UnitSkillComp",void 0);var D=l.ccclass,R=l.property;t("UnitSkillComp",(y=D("UnitSkillComp"),S=R(p),k=R(p),y((P=e((v=function(t){function e(){for(var e,i=arguments.length,n=new Array(i),r=0;r<i;r++)n[r]=arguments[r];return e=t.call.apply(t,[this].concat(n))||this,o(e,"sprIcon",P,s(e)),o(e,"sprBar",x,s(e)),e.targetRoleData=void 0,e.targetSkill=void 0,e.type=void 0,e.isLongSkill=!1,e.offsetPos=c(),e.tempPos=c(),e}i(e,t);var a=e.prototype;return a.onLoad=function(){var t=this;this.scheduleOnce((function(){h(t.sprIcon.node).repeatForever(h(t.sprIcon.node).to(2,{position:c(0,40)}).to(2,{position:c(0,-40)})).start()}),1*Math.random())},a.setData=function(){var t=n(r().mark((function t(e){var i;return r().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(this.offsetPos=e.offsetPos||c(),this.targetRoleData=e.roleData,this.targetSkill=e.targetSkill,this.type=e.type,this.node.setPosition(c(this.targetRoleData.pos.x+this.offsetPos.x,this.targetRoleData.pos.y+this.offsetPos.y)),(i=d.getInstance().getWeaponConfigBySkillJsonId(e.jsonId))||(i=d.getInstance().getSkillConfigByType(this.type,e.lev||1)),this.node.active=!0,!i.icon){t.next=16;break}return this.sprIcon.node.active=!0,this.sprBar.node.active=!0,t.next=13,g.res.bundleLoadSprite("resources","./res/image/common/"+i.icon,this.sprIcon,u(68,68));case 13:g.res.bundleLoadSprite("resources","./res/image/common/"+i.icon,this.sprBar,u(68,68)),t.next=18;break;case 16:this.sprIcon.node.active=!1,this.sprBar.node.active=!1;case 18:case"end":return t.stop()}}),t,this)})));return function(e){return t.apply(this,arguments)}}(),a.getType=function(){return this.type},a.update=function(t){if(this.targetRoleData&&this.targetSkill&&this.type&&(this.node.position.x==this.targetRoleData.pos.x+this.offsetPos.x&&this.node.position.y==this.targetRoleData.pos.y+this.offsetPos.y||(this.node.getPosition(this.tempPos),this.tempPos=this.tempPos.lerp(c(this.targetRoleData.pos.x+this.offsetPos.x,this.targetRoleData.pos.y+this.offsetPos.y),.3),this.node.position=this.tempPos),this.targetSkill.isLongSkill!=this.isLongSkill&&(this.isLongSkill=this.targetSkill.isLongSkill),this.targetSkill.tickTime&&this.targetSkill.nowSpeed)){var e=this.targetSkill.tickTime%this.targetSkill.nowSpeed/this.targetSkill.nowSpeed;this.sprBar.fillRange=e}},e}(f)).prototype,"sprIcon",[S],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),x=e(v.prototype,"sprBar",[k],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),m=v))||m));a._RF.pop()}}}));

System.register("chunks:///_virtual/UnitSkillEffect.ts",["./rollupPluginModLoBabelHelpers.js","cc","./EffectMgr.ts","./Tool.ts","./xcore.ts","./ConstGlobal.ts","./Vec3Util.ts","./ConfigHelper.ts"],(function(t){var e,n,i,o,s,a,r,l,d,f,c,h,p,m,u,g,I,y,k,v,C,T,A,P;return{setters:[function(t){e=t.applyDecoratedDescriptor,n=t.inheritsLoose,i=t.initializerDefineProperty,o=t.assertThisInitialized,s=t.asyncToGenerator,a=t.regeneratorRuntime},function(t){r=t.cclegacy,l=t._decorator,d=t.Animation,f=t.v3,c=t.tween,h=t.Component,p=t.view,m=t.TweenSystem,u=t.easing,g=t.UITransform},function(t){I=t.EffectMgr},function(t){y=t.default},function(t){k=t.xcore},function(t){v=t.E_SkillType,C=t.E_EVENT,T=t.E_RoleType},function(t){A=t.Vec3Util},function(t){P=t.ConfigHelper}],execute:function(){var S,w,E,x,b;r._RF.push({},"0e852owtOpBE5VgnfbNRDVC","UnitSkillEffect",void 0);var B=l.ccclass,J=l.property;t("UnitSkillEffect",(S=B("UnitSkillEffect"),w=J(d),S((b=e((x=function(t){function e(){for(var e,n=arguments.length,s=new Array(n),a=0;a<n;a++)s[a]=arguments[a];return e=t.call.apply(t,[this].concat(s))||this,i(e,"anim",b,o(e)),e.speed=700,e.skillType=void 0,e.atkJsonId=void 0,e.atkType=void 0,e.animId01=void 0,e.animId02=void 0,e.deadAnim=void 0,e.sound=void 0,e.animConfig=void 0,e.offPos=f(-5e3,-5e3),e._goastNodes=[],e._tw=void 0,e}n(e,t);var r=e.prototype;return r.start=function(){},r.setData=function(t,e,n,i,o){if(this.skillType=t,this.atkJsonId=e,this.animId01=n,this.animId02=i,this.sound=o,this.animId01?this.animConfig=P.getInstance().getAnimConfigByJsonId(this.animId01):this.animId02&&(this.animConfig=P.getInstance().getAnimConfigByJsonId(this.animId02)),this.atkJsonId){var s=P.getInstance().getHeroConfigByJsonId(this.atkJsonId),a=P.getInstance().getEffectConfigByJsonId(s.attacKeffect);this.animConfig=P.getInstance().getAnimConfigByJsonId(a.Animation),this.atkType=s.trajectory}this.skillType==v.MoveSpeed||this.skillType==v.SkillSpeed||(this.skillType,v.Dizziness)},r.play=function(){var t=s(a().mark((function t(e){var n,i,o,s,r,l=this;return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!e.deadanim){t.next=9;break}return this.deadAnim=e.deadanim,this.node.scale=f(1,1,1),this.node.active=!0,this.node.setPosition(f(e.pos0.x,e.pos0.y)),t.next=7,this.animUpdate();case 7:return this.scheduleOnce((function(){l.killSkill(e.cb)}),e.speed||1),t.abrupt("return");case 9:return n=e.pos0?f(e.pos0.x+(e.offsetpos?e.offsetpos.x:0),e.pos0.y+(e.offsetpos?e.offsetpos.y:0),1):null,i=e.pos1?f(e.pos1.x,e.pos1.y,1):null,this.node.scale=f(n?1:e.roledir||1,1,1),this.node.active=!0,o=this.animConfig.duration,e.speed&&e.speed<o&&(o=e.speed),t.next=17,this.animUpdate();case 17:1==this.animConfig.target?(this.node.setPosition(n),this.scheduleOnce((function(){l.killSkill(e.cb)}),o)):2==this.animConfig.target?(this.node.setPosition(i),this.node.setSiblingIndex(this.node.position.y),this.animId01&&this.animId02&&k.event.raiseEvent(C.HurtEffect,{pos1:i,animId02:this.animId02,roledir:e.roledir,formRoleType:e.formRoleType}),this.scheduleOnce((function(){l.sound&&k.sound.remotePlayOneShot(l.sound),l.killSkill(e.cb)}),o)):3==this.animConfig.target?("340309"==this.animConfig.jsonId&&(s=y.randomNumber(-260,260),n=f(n.x+s,p.getVisibleSize().height/2+100)),this.node.setPosition(n),r=Math.min(A.distance(n,i)/this.speed,o),m.instance.ActionManager.removeAllActionsFromTarget(this.node),this.atkJsonId&&e.formRoleType!=T.TowerPoint&&2==this.atkType?(i.y+=100,I.getInstance().arrowByTarget(n,i,(function(t,n,s){l.doAction(t,n,s,o,e.cb,!0,(function(){l.animId01&&l.animId02&&k.event.raiseEvent(C.HurtEffect,{pos1:i,animId02:l.animId02,roledir:e.roledir,formRoleType:e.formRoleType})}))}))):"340312"==this.animConfig.jsonId?(this.node.angle=0,this.sound&&k.sound.remotePlayOneShot(this.sound),I.getInstance().arrowByTarget(n,i,(function(t,n,s){l.doAction(t,n,s,o,(function(){l.animId01&&l.animId02&&k.event.raiseEvent(C.HurtEffect,{pos1:i,animId02:l.animId02,roledir:e.roledir,formRoleType:e.formRoleType}),e.cb&&e.cb()}),!1)}))):("340309"==this.animConfig.jsonId||e.formRoleType!=T.Hero&&e.formRoleType!=T.TowerPoint||(i.y+=100),this.node.angle=y.getAngleByV3(n,i),c(this.node).to(r,{position:i,scale:f(1.2,1.2)},{easing:u.cubicIn}).call((function(){l.animId01&&l.animId02&&k.event.raiseEvent(C.HurtEffect,{pos1:i,animId02:l.animId02,roledir:e.roledir,formRoleType:e.formRoleType}),l.sound&&k.sound.remotePlayOneShot(l.sound),l.killSkill(e.cb)})).start())):(this.node.setPosition(n),this.killSkill(e.cb));case 18:case"end":return t.stop()}}),t,this)})));return function(e){return t.apply(this,arguments)}}(),r.killSkill=function(t){this.offPos||(this.offPos=f(-5e3,-5e3)),this.node.setPosition(this.offPos),t&&t(),this.deadAnim?I.getInstance().killSkillEffectByType(this.deadAnim,this):this.skillType?I.getInstance().killSkillEffectByType(this.skillType,this):this.atkJsonId?I.getInstance().killSkillEffectByType(this.atkJsonId,this):this.animId01?I.getInstance().killSkillEffectByType(this.animId01,this):this.animId02&&I.getInstance().killSkillEffectByType(this.animId02,this)},r.animUpdate=function(){var t=s(a().mark((function t(){var e,n,i,o,s,r,l,d,c;return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:this.deadAnim?n={sample:9,duration:1,speed:1,wrapMode:2,path:"common",name:"42"}:this.animId01?(e=P.getInstance().getAnimConfigByJsonId(this.animId01),n={sample:e.sample,duration:e.duration,speed:1,wrapMode:e.wrapMode,path:e.path,name:e.name,offsetX:e.XAxisOffset||0,offsetY:e.YAxisOffset||0}):this.animId02?(e=P.getInstance().getAnimConfigByJsonId(this.animId02),n={sample:e.sample,duration:e.duration,speed:1,wrapMode:e.wrapMode,path:e.path,name:e.name,offsetX:e.XAxisOffset||0,offsetY:e.YAxisOffset||0}):this.atkJsonId&&(i=P.getInstance().getHeroConfigByJsonId(this.atkJsonId),o=P.getInstance().getEffectConfigByJsonId(i.attacKeffect),e=P.getInstance().getAnimConfigByJsonId(o.Animation),n={sample:e.sample,duration:e.duration,speed:1,wrapMode:e.wrapMode,path:e.path,name:e.name,offsetX:e.XAxisOffset||0,offsetY:e.YAxisOffset||0}),s=k.res.getAtlas("skilleffect"),r=0;case 3:if(!(r<n.sample)){t.next=14;break}if(l=n.name+"_"+(r<10?"0"+r:r),d=s.getSpriteFrame(l)){t.next=11;break}return t.next=9,k.res.bundleLoadSprite("resources","./res/image/"+n.path+"/"+l);case 9:d=t.sent,k.res.addAtlasSprite("skilleffect",l,d);case 11:r++,t.next=3;break;case 14:return t.next=16,y.createAnim(this.anim,n,s);case 16:this.anim.node.setPosition(f(n.offsetX,n.offsetY)),e&&(null==e.XCenterPoint||null==e.XCenterPoint?e.XCenterPoint=.5:""==e.XCenterPoint&&(e.XCenterPoint=0),null==e.YCenterPoint||null==e.YCenterPoint?e.YCenterPoint=.5:""==e.YCenterPoint&&(e.YCenterPoint=0),null==(c=this.anim.node.getComponent(g))||c.setAnchorPoint(e.XCenterPoint,e.YCenterPoint));case 18:case"end":return t.stop()}}),t,this)})));return function(){return t.apply(this,arguments)}}(),r.followAnim=function(t,e){},r.doAction=function(t,e,n,i,o,s,a){var r=this;if(void 0===n&&(n=!1),void 0===o&&(o=null),void 0===s&&(s=!0),this._tw=c(this.node),this.node.angle=0,this.node.active=!0,n)return this.node.angle=y.getAngleByV3(t[0],t[1]),void this._tw.to(i,{position:t[1]}).call((function(){r._tw.stop(),r.killSkill(o),a&&a()})).start();for(var l=0;l<t.length;l++)if(0!=l){var d=y.getAngleByV3(t[l-1],t[l]);d<0&&-1==e&&(d+=360),this._tw.to(i/t.length,{position:f(t[l].x,t[l].y),angle:s?d:0})}else this.node.angle=s?90-30*e:0;this._tw.call((function(){r._tw.stop(),r.killSkill(o),a&&a()})),this._tw.start()},r.update=function(t){},e}(h)).prototype,"anim",[w],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),E=x))||E));r._RF.pop()}}}));

System.register("chunks:///_virtual/UnitSkillTips.ts",["./rollupPluginModLoBabelHelpers.js","cc","./EffectMgr.ts"],(function(t){var i,e,s,n,o,r,l,p,c,a,u;return{setters:[function(t){i=t.applyDecoratedDescriptor,e=t.inheritsLoose,s=t.initializerDefineProperty,n=t.assertThisInitialized},function(t){o=t.cclegacy,r=t._decorator,l=t.Label,p=t.Vec3,c=t.Tween,a=t.Component},function(t){u=t.EffectMgr}],execute:function(){var h,f,_,y,T;o._RF.push({},"00c61NRQ45IIZ1ol7K6TAl/","UnitSkillTips",void 0);var d=r.ccclass,g=r.property;t("UnitSkillTips",(h=d("UnitSkillTips"),f=g(l),h((T=i((y=function(t){function i(){for(var i,e=arguments.length,o=new Array(e),r=0;r<e;r++)o[r]=arguments[r];return i=t.call.apply(t,[this].concat(o))||this,s(i,"lbTips",T,n(i)),i._tempPos=new p,i._tw=void 0,i}return e(i,t),i.prototype.setData=function(t){var i=this;this.lbTips.string=t.txt,this._tempPos.set(t.pos.x,t.pos.y+200),this.node.setPosition(this._tempPos),this._tempPos.set(t.pos.x,t.pos.y+300),this._tw||(this._tw=new c(this.node).to(.2,{position:this._tempPos}).delay(.8).call((function(){u.getInstance().killSkillTips(i)}))),this._tw.start()},i}(a)).prototype,"lbTips",[f],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),_=y))||_));o._RF.pop()}}}));

System.register("chunks:///_virtual/UnitSkillUpMessage.ts",["./rollupPluginModLoBabelHelpers.js","cc","./GiftMessageMgr.ts","./ConstGlobal.ts","./xcore.ts"],(function(e){var t,n,r,a,i,s,l,o,c,u,p,f,b,h,k,m,g,v,d,y;return{setters:[function(e){t=e.applyDecoratedDescriptor,n=e.inheritsLoose,r=e.initializerDefineProperty,a=e.assertThisInitialized,i=e.asyncToGenerator,s=e.regeneratorRuntime},function(e){l=e.cclegacy,o=e._decorator,c=e.sp,u=e.Sprite,p=e.Label,f=e.Vec3,b=e.Node,h=e.Tween,k=e.Component,m=e.size},function(e){g=e.GiftMessageMgr},function(e){v=e.C_Bundle,d=e.E_SkillType},function(e){y=e.xcore}],execute:function(){var w,S,M,_,T,G,z,D,U,L,x;l._RF.push({},"2146aW4BCJJx6LMZddMM4k0","UnitSkillUpMessage",void 0);var A=o.ccclass,C=o.property;e("UnitSkillUpMessage",(w=A("UnitSkillUpMessage"),S=C(c.Skeleton),M=C(u),_=C(p),T=C(p),w((D=t((z=function(e){function t(){for(var t,n=arguments.length,i=new Array(n),s=0;s<n;s++)i[s]=arguments[s];return t=e.call.apply(e,[this].concat(i))||this,r(t,"anim",D,a(t)),r(t,"sprAvatar",U,a(t)),r(t,"lbName",L,a(t)),r(t,"lbDesc",x,a(t)),t._scaleTw=void 0,t._tempScale=new f(0,0,0),t}n(t,e);var l=t.prototype;return l.onLoad=function(){var e=this;this.node.on(b.EventType.TOUCH_END,(function(){e.kill()}),this)},l.onEnable=function(){this._scaleTw||(this._scaleTw=new h(this.node).to(.2,{scale:new f(1,1,1)},{easing:"backOut"})),this.node.scale=this._tempScale,this._scaleTw.start()},l.setData=function(){var e=i(s().mark((function e(t){var n,r,a,i=this;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n=t.message,e.t0=n.skillType,e.next=e.t0===d.GreatFire?4:e.t0===d.GreatLightning?7:e.t0===d.GreatRock?10:e.t0===d.GreatSkyRock?13:e.t0===d.GreatFires?16:19;break;case 4:return r="朱雀扇",a="./res/anim/skilleffect/zhuque",e.abrupt("break",20);case 7:return r="五雷钉",a="./res/anim/skilleffect/leilingzhu",e.abrupt("break",20);case 10:return r="混元伞",a="./res/anim/skilleffect/hunyuansan",e.abrupt("break",20);case 13:return r="女娲石",a="./res/anim/skilleffect/lvwashi",e.abrupt("break",20);case 16:return r="神火炉",a="./res/anim/skilleffect/bagualu",e.abrupt("break",20);case 19:return e.abrupt("break",20);case 20:return r=1==n.lev?"获得法宝"+r:r+"升级到等级"+n.lev,this.lbDesc.string=r,this.lbName.string=n.name||"匿名用户",n.avatar&&y.res.remoteLoadSprite(n.avatar,this.sprAvatar,m(100,100)),this.anim.skeletonData=null,e.next=27,y.res.bundleLoadSpine(v.abGame,a,this.anim);case 27:this.anim.setAnimation(0,"animation"),this.scheduleOnce((function(){i.kill()}),1.8);case 29:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),l.kill=function(){this.node.removeFromParent(),g.getInstance().killSkillMessage(this)},t}(k)).prototype,"anim",[S],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),U=t(z.prototype,"sprAvatar",[M],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),L=t(z.prototype,"lbName",[_],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),x=t(z.prototype,"lbDesc",[T],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),G=z))||G));l._RF.pop()}}}));

System.register("chunks:///_virtual/UnitSkinDebris.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ConfigHelper.ts","./Tool.ts","./xcore.ts"],(function(e){var r,t,i,n,s,a,o,u,l,c,p,f,b,m,g,h;return{setters:[function(e){r=e.applyDecoratedDescriptor,t=e.inheritsLoose,i=e.initializerDefineProperty,n=e.assertThisInitialized,s=e.asyncToGenerator,a=e.regeneratorRuntime},function(e){o=e.cclegacy,u=e._decorator,l=e.SpriteFrame,c=e.Sprite,p=e.Label,f=e.Component,b=e.size},function(e){m=e.ConfigHelper},function(e){g=e.default},function(e){h=e.xcore}],execute:function(){var y,D,F,k,v,d,z,w,S,M,I,N,x,C,L,H,U;o._RF.push({},"7860aohVe1J9oF7PLMCHD0V","UnitSkinDebris",void 0);var _=u.ccclass,P=u.property;e("UnitSkinDebris",(y=_("UnitSkinDebris"),D=P([l]),F=P([l]),k=P(c),v=P(c),d=P(c),z=P(p),w=P(p),y((I=r((M=function(e){function r(){for(var r,t=arguments.length,s=new Array(t),a=0;a<t;a++)s[a]=arguments[a];return r=e.call.apply(e,[this].concat(s))||this,i(r,"sfFrams",I,n(r)),i(r,"sfMasks",N,n(r)),i(r,"sprFrame",x,n(r)),i(r,"sprMask",C,n(r)),i(r,"sprDebris",L,n(r)),i(r,"lbName",H,n(r)),i(r,"lbNum",U,n(r)),r}t(r,e);var o=r.prototype;return o.setData=function(e,r){if(e){this.lbName.string=e.tips,this.lbNum.string="+"+r.toString();var t=m.getInstance().getDebriConfigByJsonId(e.skinFragmentId);this.sprFrame.spriteFrame=this.sfFrams[t.quality-1],this.sprMask.spriteFrame=this.sfMasks[t.quality-1];var i="./res/image/"+t.path+"/"+t.icon;this.refreshImage(i)}else this.node.active=!1},o.refreshImage=function(){var e=s(a().mark((function e(r){var t;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,h.res.bundleLoadSprite("resources",r);case 2:t=e.sent,this.sprDebris.spriteFrame=t,g.resizeSprite(this.sprDebris,b(80,80));case 5:case"end":return e.stop()}}),e,this)})));return function(r){return e.apply(this,arguments)}}(),r}(f)).prototype,"sfFrams",[D],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return[]}}),N=r(M.prototype,"sfMasks",[F],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return[]}}),x=r(M.prototype,"sprFrame",[k],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),C=r(M.prototype,"sprMask",[v],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),L=r(M.prototype,"sprDebris",[d],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),H=r(M.prototype,"lbName",[z],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),U=r(M.prototype,"lbNum",[w],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),S=M))||S));o._RF.pop()}}}));

System.register("chunks:///_virtual/UnitSkinDebrisReward.ts",["./rollupPluginModLoBabelHelpers.js","cc","./UnitSkinDebris.ts","./xcore.ts","./ConfigHelper.ts","./Net.ts","./ConstGlobal.ts","./TimeUtil.ts","./Tool.ts","./FightMgr.ts"],(function(e){var n,t,r,i,a,o,s,u,l,c,p,f,d,b,g,k,m,v,h,y,I,D,S,w,x;return{setters:[function(e){n=e.applyDecoratedDescriptor,t=e.inheritsLoose,r=e.initializerDefineProperty,i=e.assertThisInitialized,a=e.asyncToGenerator,o=e.regeneratorRuntime},function(e){s=e.cclegacy,u=e._decorator,l=e.Node,c=e.Label,p=e.Sprite,f=e.Prefab,d=e.log,b=e.Component,g=e.size,k=e.instantiate},function(e){m=e.UnitSkinDebris},function(e){v=e.xcore},function(e){h=e.ConfigHelper},function(e){y=e.default},function(e){I=e.E_EVENT,D=e.E_GiftMessageType},function(e){S=e.default},function(e){w=e.default},function(e){x=e.FightMgr}],execute:function(){var R,C,N,U,z,E,M,B,L,T,F,_,A,G,H;s._RF.push({},"361a5OqKkFC6ZE8qgkCWBtp","UnitSkinDebrisReward",void 0);var P=u.ccclass,W=u.property;e("UnitSkinDebrisReward",(R=P("UnitSkinDebrisReward"),C=W(l),N=W(c),U=W(c),z=W(p),E=W(l),M=W(f),R((T=n((L=function(e){function n(){for(var n,t=arguments.length,a=new Array(t),o=0;o<t;o++)a[o]=arguments[o];return n=e.call.apply(e,[this].concat(a))||this,r(n,"ndDetail",T,i(n)),r(n,"lbRank",F,i(n)),r(n,"lbName",_,i(n)),r(n,"sprAvatar",A,i(n)),r(n,"ndContent",G,i(n)),r(n,"pfbUnitSkinDebris",H,i(n)),n}t(n,e);var s=n.prototype;return s.setData=function(){var e=a(o().mark((function e(n,t){var r,i,a,s=this;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(d(n,t),n.user){e.next=4;break}return this.ndDetail.active=!1,e.abrupt("return");case 4:r=n.user[1],this.lbName.string=r.nickName||"匿名用户",this.lbRank.string=t+1,v.res.remoteLoadSprite(r.iconUrl,this.sprAvatar,g(70,70)),i=o().mark((function e(){var t,i,u,l,c,p,f,b,g,I,D,S;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=n.ids[a],!(i=h.getInstance().getLotteryDebrisConfigByJsonId(t,n.targetId))){e.next=24;break}return u=x.getInstance().findUser(r.userId),l=0,u&&((c=h.getInstance().getWingSkinConfigByRankIndex(u.rank))&&(l=Number(c.number)),d("getWingSkinConfigByRankIndex::",u,c,l)),p=null,1==v.gameData.gameMode?p=null==(f=i.normalRewardNum)?void 0:f.split("|"):2==v.gameData.gameMode&&(p=null==(b=i.hardRewardNum)?void 0:b.split("|")),p||(p=i.rewardNum.split("|")),g=1,p[0]&&p[1]?g=w.randomNumber(parseInt(p[0]),parseInt(p[1])):p[0]&&(g=parseInt(p[0])),g+=l,(I=k(s.pfbUnitSkinDebris)).parent=s.ndContent,I.getComponent(m).setData(i,g),D=r.debris.find((function(e){return e.prop==t})),d("nums:",i,p),e.next=19,y.addDebris(r.userId,g,t);case 19:D?D.num+=g:r.debris.push({playerId:r.userId,prop:t,num:g}),(S=h.getInstance().getSkinConfigByDebrisId(t))&&s.checkIfExchangeSkin(r,t,S.skinFragmentNum,S.jsonId,S.period,t),e.next=25;break;case 24:d("lackof config",t);case 25:case"end":return e.stop()}}),e)})),a=0;case 10:if(!(a<n.ids.length)){e.next=15;break}return e.delegateYield(i(),"t0",12);case 12:a++,e.next=10;break;case 15:case"end":return e.stop()}}),e,this)})));return function(n,t){return e.apply(this,arguments)}}(),s.checkIfExchangeSkin=function(e,n,t,r,i,a){var o=e.skins.find((function(e){return e.prop==r})),s=h.getInstance().getSkinMaxLevelBySkinId(r);if(d("maxLev:",s,null==o?void 0:o.level,o),!o||(null==o?void 0:o.level)<s){var u=e.debris.find((function(e){return e.prop==n}));if((null==u?void 0:u.num)>=t){y.exchangeSkin(e.userId,r,t,i,a);var l=r;u.num-=t;var c={prop:r,time:S.getServerTime()+1e3*i,level:o?o.level+=1:1};d("up",c),e.skins.push(c),v.event.raiseEvent(I.GiftMessage,{type:D.SkinReward,skinId:l,user:e,lev:c.level})}}else d("已有皮肤"+r)},n}(b)).prototype,"ndDetail",[C],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),F=n(L.prototype,"lbRank",[N],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),_=n(L.prototype,"lbName",[U],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),A=n(L.prototype,"sprAvatar",[z],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),G=n(L.prototype,"ndContent",[E],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),H=n(L.prototype,"pfbUnitSkinDebris",[M],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),B=L))||B));s._RF.pop()}}}));

System.register("chunks:///_virtual/UnitSkinRewardMessage.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ConfigHelper.ts","./ConstGlobal.ts","./Tool.ts","./xcore.ts","./GiftMessageMgr.ts"],(function(e){var n,t,r,i,a,o,l,s,u,c,p,f,d,m,b,g,h,v,k,y,C,w,P;return{setters:[function(e){n=e.applyDecoratedDescriptor,t=e.inheritsLoose,r=e.initializerDefineProperty,i=e.assertThisInitialized,a=e.asyncToGenerator,o=e.regeneratorRuntime},function(e){l=e.cclegacy,s=e._decorator,u=e.Node,c=e.sp,p=e.Label,f=e.Sprite,d=e.Animation,m=e.Component,b=e.UITransform,g=e.log,h=e.size,v=e.v3},function(e){k=e.ConfigHelper},function(e){y=e.C_Bundle},function(e){C=e.default},function(e){w=e.xcore},function(e){P=e.GiftMessageMgr}],execute:function(){var S,M,A,z,T,_,x,I,R,U,L,N,B,G,F,X,Y,D,H;l._RF.push({},"ad7f8P+7QRInakdgyTGF1E/","UnitSkinRewardMessage",void 0);var E=s.ccclass,O=s.property;e("UnitSkinRewardMessage",(S=E("UnitSkinRewardMessage"),M=O(u),A=O(c.Skeleton),z=O(p),T=O(f),_=O(d),x=O(f),I=O(p),R=O(p),S((N=n((L=function(e){function n(){for(var n,t=arguments.length,a=new Array(t),o=0;o<t;o++)a[o]=arguments[o];return n=e.call.apply(e,[this].concat(a))||this,r(n,"ndCore",N,i(n)),r(n,"anim",B,i(n)),r(n,"lbTitle",G,i(n)),r(n,"sprBg",F,i(n)),r(n,"role",X,i(n)),r(n,"sprAvatar",Y,i(n)),r(n,"lbUserName",D,i(n)),r(n,"lbName",H,i(n)),n}t(n,e);var l=n.prototype;return l.onLoad=function(){var e=this;this.node.on(u.EventType.TOUCH_END,(function(){e.node.removeFromParent(),P.getInstance().killSkinRewardMessage(e)}),this)},l.setData=function(){var e=a(o().mark((function e(n){var t,r,i,a,l,s,u,c,p,f,d,m,S=this;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:g("debris reward:",n),r=k.getInstance().getSkinConfigByJsonId(n.skinId),i=k.getInstance().getAnimConfigByJsonId(r.moveAnimation),a=r.quality||1,l="./res/anim/skinshow/"+a+"/tianhu",w.res.bundleLoadSpine(y.abGame,l,this.anim).then((function(){S.anim.setAnimation(0,"attack_1",!0)})),w.res.bundleLoadSprite(y.abGame,"./res/img_unpack/game_skinreward_bg0"+a,this.sprBg),this.lbUserName.string=C.sub(n.user.nickName||"匿名用户",10,!0),w.res.remoteLoadSprite(n.user.iconUrl,this.sprAvatar,h(80,80)),this.lbName.string=r.name,n.level>1?this.lbTitle.string="升级到Lv."+n.level:this.lbTitle.string="解锁",s={sample:i.sample,duration:i.duration,speed:1,wrapMode:i.wrapMode,path:i.path,name:i.name},u=s.name.split("-")[0]||"default",c=w.res.getAtlas(u),p=0;case 15:if(!(p<s.sample)){e.next=26;break}if(f=s.name+"_"+(p<10?"0"+p:p),c.getSpriteFrame(f)){e.next=23;break}return e.next=21,w.res.bundleLoadSprite("resources","./res/image/"+s.path+"/"+f);case 21:d=e.sent,w.res.addAtlasSprite(u,f,d);case 23:p++,e.next=15;break;case 26:return null==i.XCenterPoint||null==i.XCenterPoint?i.XCenterPoint=.5:""==i.XCenterPoint&&(i.XCenterPoint=0),null==i.YCenterPoint||null==i.YCenterPoint?i.YCenterPoint=.5:""==i.YCenterPoint&&(i.YCenterPoint=0),e.next=30,C.createAnim(this.role,s,c);case 30:if(m=null==(t=this.role)||null==(t=t.node)?void 0:t.getComponent(b)){e.next=33;break}return e.abrupt("return");case 33:this.scheduleOnce((function(){if(m){var e=600/m.height;m.node.scale=v(e,e)}})),null==m||m.setAnchorPoint(i.XCenterPoint,i.YCenterPoint),this.scheduleOnce((function(){S.node&&S.node.isValid&&(S.node.removeFromParent(),P.getInstance().killSkinRewardMessage(S))}),3);case 36:case"end":return e.stop()}}),e,this)})));return function(n){return e.apply(this,arguments)}}(),n}(m)).prototype,"ndCore",[M],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),B=n(L.prototype,"anim",[A],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),G=n(L.prototype,"lbTitle",[z],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),F=n(L.prototype,"sprBg",[T],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),X=n(L.prototype,"role",[_],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Y=n(L.prototype,"sprAvatar",[x],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),D=n(L.prototype,"lbUserName",[I],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),H=n(L.prototype,"lbName",[R],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),U=L))||U));l._RF.pop()}}}));

System.register("chunks:///_virtual/UnitSkinShow.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ListItem.ts","./ConfigHelper.ts","./xcore.ts","./FightMgr.ts","./Net.ts","./TimeUtil.ts","./ConstGlobal.ts"],(function(e){var n,t,i,r,s,l,a,o,u,c,d,p,f,h,k,b,m,g,I,v,S,_;return{setters:[function(e){n=e.applyDecoratedDescriptor,t=e.inheritsLoose,i=e.initializerDefineProperty,r=e.assertThisInitialized,s=e.asyncToGenerator,l=e.regeneratorRuntime},function(e){a=e.cclegacy,o=e._decorator,u=e.SpriteFrame,c=e.Sprite,d=e.Node,p=e.Label,f=e.Button,h=e.size,k=e.log},function(e){b=e.default},function(e){m=e.ConfigHelper},function(e){g=e.xcore},function(e){I=e.FightMgr},function(e){v=e.default},function(e){S=e.default},function(e){_=e.C_View}],execute:function(){var y,w,F,D,U,x,L,z,O,N,C,T,R,A,B,M,G,H,P,V,j,q,J,Y,E,X,Z,K,Q,W,$;a._RF.push({},"85715aLYYRLGZP6ewIXAbd1","UnitSkinShow",void 0);var ee=o.ccclass,ne=o.property;e("UnitSkinShow",(y=ee("UnitSkinShow"),w=ne([u]),F=ne([u]),D=ne(c),U=ne(c),x=ne(c),L=ne(d),z=ne(d),O=ne(p),N=ne(p),C=ne(d),T=ne(p),R=ne(p),A=ne(d),B=ne(f),y((H=n((G=function(e){function n(){for(var n,t=arguments.length,s=new Array(t),l=0;l<t;l++)s[l]=arguments[l];return n=e.call.apply(e,[this].concat(s))||this,i(n,"sfFrames",H,r(n)),i(n,"sfOns",P,r(n)),i(n,"sprFrame",V,r(n)),i(n,"sprOn",j,r(n)),i(n,"sprSkin",q,r(n)),i(n,"ndIconDetail",J,r(n)),i(n,"ndLeftDay",Y,r(n)),i(n,"lbLeftDay",E,r(n)),i(n,"lbName",X,r(n)),i(n,"ndUnlock",Z,r(n)),i(n,"lbUnlockDesc",K,r(n)),i(n,"lbUnlock",Q,r(n)),i(n,"ndIsSelect",W,r(n)),i(n,"btnOption",$,r(n)),n._data=void 0,n._index=void 0,n._fromRole=void 0,n._leftTime=0,n._userId=null,n._skinId=null,n._isAbleSelect=!1,n}t(n,e);var a=n.prototype;return a.setData=function(){var e=s(l().mark((function e(n,t,i,r){var s,a,o,u,c,d,p,f,h,b,y,w,F,D,U,x,L,z=this;return l().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this._data=n,this._index=t,this._fromRole=i,this.lbName.string=n.name,this.lbUnlockDesc.string=1==n.skin?"积分":"",this.sprFrame.grayscale=!1,this.sprSkin.grayscale=!1,this.sprOn.grayscale=!1,this.sprFrame.spriteFrame=this.sfFrames[n.quality-1],this.sprOn.spriteFrame=this.sfOns[n.quality-1],this.ndLeftDay.active=!1,s=null,this._userId=null,this._skinId=n.jsonId,a=null,o=!1,u=!1,this._leftTime=null,this.ndIsSelect.active=!1,this.btnOption.node.off("click"),i?(this.ndIconDetail.active=!1,s=I.getInstance().findUser(i.data.userId),this._userId=null==(c=s)?void 0:c.role.data.userId,a=null==(d=s)?void 0:d.skins.find((function(e){return e.prop==z._skinId})),this.btnOption.node.on("click",this.onSelectItem.bind(this,r),this)):(this.ndIconDetail.active=!0,this.btnOption.node.on("click",(function(){g.ui.addView(_.ViewSkinDetail,{skinId:z._skinId,skinName:n.name})}),this)),1!=n.skin){e.next=34;break}if(this.lbUnlock.string=n.openCondition+"积分",o=!!a||!i||i.data.weekScore>=n.openCondition,!s||a||!o){e.next=31;break}return p=2592e3,e.next=28,v.rewardSkin(this._userId,4,[{num:1,prop:this._skinId,time:p}]);case 28:I.getInstance().updateSkinInfo(this._userId),a={prop:this._skinId,time:S.getServerTime()+1e3*p},s.skins.push(a);case 31:n.openCondition<=0&&(this.ndUnlock.active=!1),e.next=35;break;case 34:2==n.skin&&(f=m.getInstance().getDebriConfigByJsonId(n.skinFragment),this.lbUnlockDesc.string=f.name,this.lbUnlock.string=n.skinFragmentNum+"碎片",s?(F=m.getInstance().getSkinLevelConfigBySkinId(this._skinId,null==(h=a)?void 0:h.level),this.ndUnlock.active=!1,D=m.getInstance().getSkinMaxLevelBySkinId(this._skinId),k("maxLev::",D,null==(b=a)?void 0:b.level,a,F,null==F?void 0:F.skinFragmentNum),o=!!a,U=s.debris.find((function(e){return e.prop==n.skinFragment})),u=(null==U?void 0:U.num)>=((null==F?void 0:F.skinFragmentNum)||1e4),(!o||(null==(y=a)?void 0:y.level)<D)&&u&&(this.autoUnlockSkin(this._userId,this._skinId,F.skinFragmentNum,n.period,n.skinFragment),U.num-=F.skinFragmentNum,a={prop:this._skinId,time:S.getServerTime()+1e3*n.period,level:o?a.level+=1:1},k("up",a),s.skins.push(a),o=!0,this.scheduleOnce((function(){z.setData(z._data,z._index,z._fromRole)}))),!a||(null==(w=a)?void 0:w.level)<D?(L=m.getInstance().getSkinLevelConfigBySkinId(this._skinId,null==(x=a)?void 0:x.level),this.lbName.string=n.name+"("+(U?U.num:0)+"/"+((null==L?void 0:L.skinFragmentNum)||n.skinFragmentNum)+")"):this.lbName.string=""+n.name,o&&(this.ndLeftDay.active=!0,this._leftTime=a.time-S.getServerTime(),this.lbLeftDay.string="Lv."+a.level)):(o=!0,this.lbName.string=n.name));case 35:s&&a?(this.ndIsSelect.active=1==a.useStatus,this._isAbleSelect=1!=a.useStatus):this._isAbleSelect=!1,this.sprFrame.grayscale=!o,this.sprSkin.grayscale=!o,this.sprOn.grayscale=!o,this.refreshSkin(this._skinId);case 40:case"end":return e.stop()}}),e,this)})));return function(n,t,i,r){return e.apply(this,arguments)}}(),a.refreshSkin=function(e){var n=m.getInstance().getSkinConfigByJsonId(e);g.res.bundleLoadSprite("resources","./res/image/"+n.path+"/"+n.skinRole,this.sprSkin,h(200,290)),k("skinid",this._skinId,n)},a.autoUnlockSkin=function(){var e=s(l().mark((function e(n,t,i,r,s){return l().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,v.exchangeSkin(n,t,i,r,s);case 2:case"end":return e.stop()}}),e)})));return function(n,t,i,r,s){return e.apply(this,arguments)}}(),a.onSelectItem=function(){var e=s(l().mark((function e(n){var t,i,r,s=this;return l().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this._isAbleSelect){e.next=2;break}return e.abrupt("return");case 2:return this.btnOption.enabled=!1,e.prev=3,e.next=6,v.setSelectSkin(this._userId,this._skinId);case 6:t=I.getInstance().findUser(this._userId),i=t.skins.find((function(e){return e.prop==s._skinId})),t.skins.forEach((function(e){e.useStatus=0})),i.useStatus=1,this._isAbleSelect=!0,this.btnOption.enabled=!0,r=I.getInstance().findUser(this._userId),I.getInstance().doSwitchSkin(r,null,this._skinId),n&&n(this._userId,this._skinId),e.next=20;break;case 17:e.prev=17,e.t0=e.catch(3),this.btnOption.enabled=!0;case 20:case"end":return e.stop()}}),e,this,[[3,17]])})));return function(n){return e.apply(this,arguments)}}(),a.update=function(e){},n}(b)).prototype,"sfFrames",[w],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return[]}}),P=n(G.prototype,"sfOns",[F],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return[]}}),V=n(G.prototype,"sprFrame",[D],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),j=n(G.prototype,"sprOn",[U],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),q=n(G.prototype,"sprSkin",[x],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),J=n(G.prototype,"ndIconDetail",[L],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Y=n(G.prototype,"ndLeftDay",[z],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),E=n(G.prototype,"lbLeftDay",[O],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),X=n(G.prototype,"lbName",[N],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Z=n(G.prototype,"ndUnlock",[C],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),K=n(G.prototype,"lbUnlockDesc",[T],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Q=n(G.prototype,"lbUnlock",[R],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),W=n(G.prototype,"ndIsSelect",[A],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),$=n(G.prototype,"btnOption",[B],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),M=G))||M));a._RF.pop()}}}));

System.register("chunks:///_virtual/UnitSkinUser.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ListItem.ts","./Tool.ts","./ConfigHelper.ts","./xcore.ts","./ConstGlobal.ts","./UnitSkinShow.ts"],(function(n){var t,i,e,r,o,l,a,u,s,c,p,f,b,h,d,S,g,w;return{setters:[function(n){t=n.applyDecoratedDescriptor,i=n.inheritsLoose,e=n.initializerDefineProperty,r=n.assertThisInitialized},function(n){o=n.cclegacy,l=n._decorator,a=n.Node,u=n.Prefab,s=n.Sprite,c=n.Label,p=n.Button,f=n.size},function(n){b=n.default},function(n){h=n.default},function(n){d=n.ConfigHelper},function(n){S=n.xcore},function(n){g=n.C_View},function(n){w=n.UnitSkinShow}],execute:function(){var y,k,U,m,z,O,_,v,L,x,C,D,V,I,N,A,T,B,H,P,F,M,R;o._RF.push({},"71292Ab6ztNzq8BbT/g7S2d","UnitSkinUser",void 0);var j=l.ccclass,q=l.property;n("UnitSkinUser",(y=j("UnitSkinUser"),k=q(a),U=q(u),m=q(a),z=q(s),O=q(c),_=q(c),v=q(p),L=q(p),x=q(p),C=q(c),y((I=t((V=function(n){function t(){for(var t,i=arguments.length,o=new Array(i),l=0;l<i;l++)o[l]=arguments[l];return t=n.call.apply(n,[this].concat(o))||this,e(t,"ndUserData",I,r(t)),e(t,"pfbUnitSkinShow",N,r(t)),e(t,"ndList",A,r(t)),e(t,"sprAvatar",T,r(t)),e(t,"lbName",B,r(t)),e(t,"lbScore",H,r(t)),e(t,"btnOption",P,r(t)),e(t,"btnOption2",F,r(t)),e(t,"btnOption3",M,r(t)),e(t,"lbOptionTxt",R,r(t)),t._isOpen=!1,t._role=null,t}i(t,n);var o=t.prototype;return o.onLoad=function(){var n=this;this.btnOption.node.on("click",(function(){S.ui.addView(g.ViewUserInfo,{role:n._role,index:0})}),this),this.btnOption2.node.on("click",(function(){S.ui.addView(g.ViewUserInfo,{role:n._role,index:1})}),this),this.btnOption3.node.on("click",(function(){S.ui.addView(g.ViewUserInfo,{role:n._role,index:2})}),this)},o.setData=function(n,t){this._role=n,this.lbName.string=n.data.nickName||"匿名用户",this.lbScore.string=(n.data.weekScore||0).toString(),this._isOpen=!1,this.refreshSkins(),S.res.remoteLoadSprite(n.data.iconUrl,this.sprAvatar,f(90,90))},o.refreshSkins=function(){var n=this;if(this.ndList.active=this._isOpen,this._isOpen){var t=d.getInstance().getSkinConfgs();t.sort((function(n,t){return t.sort-n.sort})),h.asyncModifyChildren(this,this.ndList,this.pfbUnitSkinShow,t.length,(function(i,e){i.getComponent(w).setData(t[e],e,n._role,(function(t){n.refreshSkins()}))}))}},t}(b)).prototype,"ndUserData",[k],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),N=t(V.prototype,"pfbUnitSkinShow",[U],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),A=t(V.prototype,"ndList",[m],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),T=t(V.prototype,"sprAvatar",[z],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),B=t(V.prototype,"lbName",[O],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),H=t(V.prototype,"lbScore",[_],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),P=t(V.prototype,"btnOption",[v],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),F=t(V.prototype,"btnOption2",[L],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),M=t(V.prototype,"btnOption3",[x],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),R=t(V.prototype,"lbOptionTxt",[C],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),D=V))||D));o._RF.pop()}}}));

System.register("chunks:///_virtual/UnitTaskRewardMessage.ts",["./rollupPluginModLoBabelHelpers.js","cc","./GiftMessageMgr.ts","./xcore.ts"],(function(e){var t,i,r,s,n,a,o,l,u,c,p,f,h,g,d,m,b;return{setters:[function(e){t=e.applyDecoratedDescriptor,i=e.inheritsLoose,r=e.initializerDefineProperty,s=e.assertThisInitialized},function(e){n=e.cclegacy,a=e._decorator,o=e.Sprite,l=e.Label,u=e.v3,c=e.view,p=e.UITransform,f=e.tween,h=e.easing,g=e.size,d=e.Component},function(e){m=e.GiftMessageMgr},function(e){b=e.xcore}],execute:function(){var w,v,y,k,M,P,R,z,N;n._RF.push({},"61247uGtkFDq7pAx1it2i8o","UnitTaskRewardMessage",void 0);var T=a.ccclass,x=a.property;e("UnitTaskRewardMessage",(w=T("UnitTaskRewardMessage"),v=x(o),y=x(l),k=x(l),w((R=t((P=function(e){function t(){for(var t,i=arguments.length,n=new Array(i),a=0;a<i;a++)n[a]=arguments[a];return t=e.call.apply(e,[this].concat(n))||this,r(t,"sprAvatar",R,s(t)),r(t,"lbName",z,s(t)),r(t,"lbRewardNum",N,s(t)),t.toPos=u(0,0),t.fromPos=u(0,0),t.tw=void 0,t}i(t,e);var n=t.prototype;return n.setData=function(e){var t=this;this.toPos.set(-c.getVisibleSize().width/2+90,0),this.fromPos.set(-c.getVisibleSize().width/2-this.node.getComponent(p).width,0),this.node.setPosition(this.fromPos),this.tw=f(this.node).to(.4,{position:this.toPos},{easing:h.cubicIn}).delay(1.2).call((function(){t.kill()})),this.tw.start(),this.lbName.string=e.name||"匿名用户",b.res.remoteLoadSprite(e.avatar,this.sprAvatar,g(70,70)),this.lbRewardNum.string="灵珠x"+e.totalNum},n.kill=function(){this.node.removeFromParent(),m.getInstance().killTaskRewardMessage(this)},t}(d)).prototype,"sprAvatar",[v],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),z=t(P.prototype,"lbName",[y],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),N=t(P.prototype,"lbRewardNum",[k],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),M=P))||M));n._RF.pop()}}}));

System.register("chunks:///_virtual/UnitTopRank.ts",["./rollupPluginModLoBabelHelpers.js","cc","./xcore.ts","./StringUtil.ts"],(function(e){var r,t,i,n,l,o,a,u,s,p,c,b,f,m,g;return{setters:[function(e){r=e.applyDecoratedDescriptor,t=e.inheritsLoose,i=e.initializerDefineProperty,n=e.assertThisInitialized},function(e){l=e.cclegacy,o=e._decorator,a=e.Sprite,u=e.SpriteFrame,s=e.Node,p=e.Label,c=e.size,b=e.v3,f=e.Component},function(e){m=e.xcore},function(e){g=e.StringUtil}],execute:function(){var h,y,d,z,R,S,v,w,F,N,T,k,U,D,x,A,C,I,L,_,P,K,M,j,B,H,W;l._RF.push({},"265a4uvZ+5CbYaWa8KxK1M/","UnitTopRank",void 0);var Y=o.ccclass,Z=o.property;e("UnitTopRank",(h=Y("UnitTopRank"),y=Z(a),d=Z([u]),z=Z(a),R=Z([u]),S=Z(a),v=Z(s),w=Z(p),F=Z(p),N=Z(p),T=Z(p),k=Z(p),U=Z(p),h((A=r((x=function(e){function r(){for(var r,t=arguments.length,l=new Array(t),o=0;o<t;o++)l[o]=arguments[o];return r=e.call.apply(e,[this].concat(l))||this,i(r,"sprTitle",A,n(r)),i(r,"sprTitles",C,n(r)),i(r,"sprFrame",I,n(r)),i(r,"sprFrames",L,n(r)),i(r,"sprAvatar",_,n(r)),i(r,"ndIconUp",P,n(r)),i(r,"lbName",K,n(r)),i(r,"lbRankNum",M,n(r)),i(r,"lbRoundNum",j,n(r)),i(r,"lbRoundChange",B,n(r)),i(r,"lbScore",H,n(r)),i(r,"lbScoreDesc",W,n(r)),r}return t(r,e),r.prototype.setData=function(e,r,t,i,n,l,o,a){this.lbName.string=g.sub(r||"匿名用户",9,!0),this.lbRankNum.string=""+(i+1),m.res.remoteLoadSprite(t,this.sprAvatar,c(70,70)),this.lbScore.string=e.score,this.lbScoreDesc.string="积分池 +"+e.poolScore+"\n"+(n>0?"关卡加成 +"+n:""),this.lbRoundNum.string=""+l;var u=[b(0,20),b(-240,0),b(240,0)];this.node.setPosition(u[i]),this.sprFrame.spriteFrame=this.sprFrames[i],this.sprTitle.spriteFrame=this.sprTitles[i],this.lbRankNum.string=a<0?"":(a+1).toString(),this.ndIconUp.active=o>0&&a<o},r}(f)).prototype,"sprTitle",[y],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),C=r(x.prototype,"sprTitles",[d],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return[]}}),I=r(x.prototype,"sprFrame",[z],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),L=r(x.prototype,"sprFrames",[R],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return[]}}),_=r(x.prototype,"sprAvatar",[S],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),P=r(x.prototype,"ndIconUp",[v],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),K=r(x.prototype,"lbName",[w],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),M=r(x.prototype,"lbRankNum",[F],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),j=r(x.prototype,"lbRoundNum",[N],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),B=r(x.prototype,"lbRoundChange",[T],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),H=r(x.prototype,"lbScore",[k],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),W=r(x.prototype,"lbScoreDesc",[U],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),D=x))||D));l._RF.pop()}}}));

System.register("chunks:///_virtual/UnitTowerPoint.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ConstGlobal.ts","./Tool.ts","./xcore.ts","./ConfigHelper.ts"],(function(t){var e,n,i,o,a,s,r,l,c,m,h,u,d,f,p,v,b,R,g,A,y,P,C;return{setters:[function(t){e=t.applyDecoratedDescriptor,n=t.inheritsLoose,i=t.initializerDefineProperty,o=t.assertThisInitialized,a=t.asyncToGenerator,s=t.regeneratorRuntime},function(t){r=t.cclegacy,l=t._decorator,c=t.Animation,m=t.Label,h=t.sp,u=t.Vec3,d=t.v2,f=t.Sprite,p=t.v3,v=t.Component,b=t.UITransform},function(t){R=t.E_RoleState,g=t.C_Bundle,A=t.E_RoleType},function(t){y=t.default},function(t){P=t.xcore},function(t){C=t.ConfigHelper}],execute:function(){var k,w,S,x,T,L,I,z,D;r._RF.push({},"ef3d1iuYJ9BMaOA3vB5peia","UnitTowerPoint",void 0);var _=l.ccclass,M=l.property;t("UnitTowerPoint",(k=_("UnitTowerPoint"),w=M(c),S=M(m),x=M(h.Skeleton),k((I=e((L=function(t){function e(){for(var e,n=arguments.length,a=new Array(n),s=0;s<n;s++)a[s]=arguments[s];return e=t.call.apply(t,[this].concat(a))||this,i(e,"anim",I,o(e)),i(e,"lbLev",z,o(e)),i(e,"bgAnim",D,o(e)),e.fromRole=void 0,e.state=R.None,e.tempPos=new u,e.tempScale=new u,e.finishCb=void 0,e}n(e,t);var r=e.prototype;return r.onLoad=function(){},r.init=function(t){var e=this;this.fromRole=t,this.node.active=!0,this.state=R.None,this.clearAnim();var n=this.fromRole.data.lev;if(this.lbLev.string!="Lv."+n){this.lbLev.string="Lv."+n;var i=C.getInstance().getTowerPointByType(t.data.monsterType,n);i.fazhen?(this.bgAnim.enabled=!0,P.res.bundleLoadSpine(g.abGame,"./res/anim/towerpoint/"+i.fazhen,this.bgAnim).then((function(){e.bgAnim.setAnimation(0,"animation",!0)}))):this.bgAnim.enabled=!1}},r.getCompAnimNode=function(){var t;return null==(t=this.anim)?void 0:t.node},r.doAttackAnim=function(){var t=a(s().mark((function t(e,n){return s().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:this.updateAnim(e,n);case 1:case"end":return t.stop()}}),t,this)})));return function(e,n){return t.apply(this,arguments)}}(),r.clear=function(t){void 0===t&&(t=!1),this.clearAnim(),this.node&&this.node.isValid&&(t?this.node.destroy():(this.node.active=!1,this.setPosAtonce(d(-3e3,-3e3))))},r.onDead=function(){},r.clearAnim=function(){var t;(null==(t=this.fromRole)||null==(t=t.data)?void 0:t.type)!=A.Tower&&this.anim&&(this.anim.getComponent(f).spriteFrame=null,this.anim.getComponent(c).enabled=!1)},r.setPosAtonce=function(t){var e;null==(e=this.node)||e.setPosition(p(t.x,t.y))},r.getFromRole=function(){return this.fromRole?this.fromRole:null},r.getRoleType=function(){return this.fromRole?this.fromRole.data.type:null},r.setRankInfo=function(){},r.setLev=function(t){},r.switchState=function(){switch(this.state){case R.None:break;case R.Idle:case R.Move:case R.Attack:this.updateAnim();break;case R.Hurt:break;case R.Skill:this.updateAnim();break;case R.WaitRelive:case R.Dizziness:break;case R.Dead:this.clearAnim()}},r.update=function(t){this.fromRole&&this.fromRole.data&&((this.fromRole.data.scale.y!=this.anim.node.scale.y||this.fromRole.data.moveDir>0&&this.anim.node.scale.x<0||this.fromRole.data.moveDir<0&&this.anim.node.scale.x>0)&&this.tempScale.set(this.fromRole.data.moveDir*this.fromRole.data.scale.y,this.fromRole.data.scale.y),this.tempScale!=this.anim.node.scale&&this.anim.node.setScale(this.tempScale),this.node.position.x==this.fromRole.data.pos.x&&this.node.position.y==this.fromRole.data.pos.y||(this.node.getPosition(this.tempPos),this.tempPos=this.tempPos.lerp(p(this.fromRole.data.pos.x,this.fromRole.data.pos.y),.3),this.node.setPosition(this.tempPos)),this.state!=this.fromRole.data.state&&(this.state=this.fromRole.data.state,this.switchState()))},r.playHit=function(){},r.setHp=function(){},r.setGiftTitleInfo=function(t){},r.setColor=function(t,e){},r.updateAnim=function(){var t=a(s().mark((function t(e,n){var i,o,a,r,l,c,m,h;return s().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(this.state!=R.Move&&this.state!=R.Attack&&this.state!=R.Idle&&this.state!=R.Skill){t.next=27;break}(i=C.getInstance().getAnimConfigByJsonId(this.state==R.Move||this.state==R.Idle?this.fromRole.data.moveAnimation:this.state==R.Skill?this.fromRole.data.skillAnimation:this.fromRole.data.attackAnimation)).duration,o={sample:i.sample,duration:i.duration,speed:1,wrapMode:i.wrapMode,path:i.path,name:i.name},a=o.name.split("-")[0]||"default",r=P.res.getAtlas(a),l=this.anim.node.getComponent(b),c=0;case 10:if(!(c<o.sample)){t.next=21;break}if(m=o.name+"_"+(c<10?"0"+c:c),r.getSpriteFrame(m)){t.next=18;break}return t.next=16,P.res.bundleLoadSprite("resources","./res/image/"+o.path+"/"+m);case 16:h=t.sent,P.res.addAtlasSprite(a,m,h);case 18:c++,t.next=10;break;case 21:return null==i.XCenterPoint||null==i.XCenterPoint?i.XCenterPoint=.5:""==i.XCenterPoint&&(i.XCenterPoint=0),null==i.YCenterPoint||null==i.YCenterPoint?i.YCenterPoint=.5:""==i.YCenterPoint&&(i.YCenterPoint=0),t.next=25,y.createAnim(this.anim,o,r);case 25:l&&l.node.isValid&&l.setAnchorPoint(i.XCenterPoint,i.YCenterPoint),e&&e(i.duration);case 27:case"end":return t.stop()}}),t,this)})));return function(e,n){return t.apply(this,arguments)}}(),e}(v)).prototype,"anim",[w],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),z=e(L.prototype,"lbLev",[S],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),D=e(L.prototype,"bgAnim",[x],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),T=L))||T));r._RF.pop()}}}));

System.register("chunks:///_virtual/UnitTowerPointShowMessage.ts",["./rollupPluginModLoBabelHelpers.js","cc","./GiftMessageMgr.ts","./ConfigHelper.ts","./xcore.ts","./Tool.ts","./ConstGlobal.ts"],(function(e){var t,n,i,r,o,a,s,l,c,u,f,p,m,h,g,b,d,w,v,y,T,A,_;return{setters:[function(e){t=e.applyDecoratedDescriptor,n=e.inheritsLoose,i=e.initializerDefineProperty,r=e.assertThisInitialized,o=e.asyncToGenerator,a=e.regeneratorRuntime},function(e){s=e.cclegacy,l=e._decorator,c=e.Animation,u=e.sp,f=e.Label,p=e.Vec3,m=e.Node,h=e.Tween,g=e.TweenSystem,b=e.tween,d=e.Component,w=e.v3},function(e){v=e.GiftMessageMgr},function(e){y=e.ConfigHelper},function(e){T=e.xcore},function(e){A=e.default},function(e){_=e.C_Bundle}],execute:function(){var D,L,M,S,k,x,P,C,R,z,E;s._RF.push({},"51c10fRLy9Gu6a1mV378KpA","UnitTowerPointShowMessage",void 0);var G=l.ccclass,F=l.property;e("UnitTowerPointShowMessage",(D=G("UnitTowerPointShowMessage"),L=F(c),M=F(u.Skeleton),S=F(f),k=F(f),D((C=t((P=function(e){function t(){for(var t,n=arguments.length,o=new Array(n),a=0;a<n;a++)o[a]=arguments[a];return(t=e.call.apply(e,[this].concat(o))||this).anim=null,i(t,"role",C,r(t)),i(t,"bgAnim",R,r(t)),i(t,"lbRoleName",z,r(t)),i(t,"lbDesc",E,r(t)),t._cb=void 0,t._scaleTw=void 0,t._tempScale=new p(0,0,0),t}n(t,e);var s=t.prototype;return s.onLoad=function(){var e=this;this.node.on(m.EventType.TOUCH_END,(function(){e.kill()}),this)},s.onEnable=function(){this._scaleTw||(this._scaleTw=new h(this.node).to(.2,{scale:new p(1,1,1)},{easing:"backOut"})),this.node.scale=this._tempScale,this._scaleTw.start()},s.setData=function(){var e=o(a().mark((function e(t){var n,i,r,o,s,l,c,u,f,p,m=this;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n=y.getInstance().getAnimConfigByJsonId(t.anim),this.bgAnim.skeletonData=null,i=y.getInstance().getTowerPointByType(t.type,t.lev),t.fromLev?(this.lbDesc.string="等级提升Lv."+t.fromLev,this.doTxtEff(t.fromLev,t.lev)):this.lbDesc.string="等级提升Lv."+t.lev,this.lbRoleName.string=i.name,this.role.enabled=!1,r={sample:n.sample,duration:n.duration,speed:1,wrapMode:n.wrapMode,path:n.path,name:n.name},o=r.name.split("-")[0]||"default",s=T.res.getAtlas(o),l=i.specialEffects,c="./res/anim/towerpoint/"+l,T.res.bundleLoadSpine(_.abGame,c,this.bgAnim).then((function(){m.bgAnim.setAnimation(0,"animation",!0)})),u=0;case 14:if(!(u<r.sample)){e.next=25;break}if(f=r.name+"_"+(u<10?"0"+u:u),s.getSpriteFrame(f)){e.next=22;break}return e.next=20,T.res.bundleLoadSprite("resources","./res/image/"+r.path+"/"+f);case 20:p=e.sent,T.res.addAtlasSprite(o,f,p);case 22:u++,e.next=14;break;case 25:return this.role.node.scale=w(2,2,2),e.next=28,A.createAnim(this.role,r,s);case 28:this.scheduleOnce((function(){m.kill()}),1.8);case 29:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),s.doTxtEff=function(e,t){var n=this;g.instance.ActionManager.removeAllActionsFromTarget(this.lbDesc);var i=b(this.lbDesc),r=t-e,o=1/r;o>.1&&(o=.1);for(var a=function(t){i.delay(o),i.call((function(){n.lbDesc.string="等级提升Lv."+(e+t+1)}))},s=0;s<r;s++)a(s);i.start()},s.kill=function(){this.node.removeFromParent(),v.getInstance().killTowerPointMessage(this)},t}(d)).prototype,"role",[L],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),R=t(P.prototype,"bgAnim",[M],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),z=t(P.prototype,"lbRoleName",[S],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),E=t(P.prototype,"lbDesc",[k],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),x=P))||x));s._RF.pop()}}}));

System.register("chunks:///_virtual/UnitUserInfoMessage.ts",["./rollupPluginModLoBabelHelpers.js","cc","./GiftMessageMgr.ts","./xcore.ts","./ConfigHelper.ts"],(function(t){var e,i,n,o,r,s,a,l,f,c,u,p,m,d,b,h,g,k;return{setters:[function(t){e=t.applyDecoratedDescriptor,i=t.inheritsLoose,n=t.initializerDefineProperty,o=t.assertThisInitialized},function(t){r=t.cclegacy,s=t._decorator,a=t.Label,l=t.Sprite,f=t.log,c=t.Vec3,u=t.view,p=t.UITransform,m=t.tween,d=t.size,b=t.Component},function(t){h=t.GiftMessageMgr},function(t){g=t.xcore},function(t){k=t.ConfigHelper}],execute:function(){var v,E,w,y,M,S,z,A,I,U,N,C,P,x,L,B,D,H,R,V,_;r._RF.push({},"9bfd5cpQs9LmpVc0au9hR2X","UnitUserInfoMessage",void 0);var F=s.ccclass,G=s.property;t("UnitUserInfoMessage",(v=F("UnitUserInfoMessage"),E=G(a),w=G(a),y=G(l),M=G(a),S=G(a),z=G(a),A=G(a),I=G(a),U=G(a),v((P=e((C=function(t){function e(){for(var e,i=arguments.length,r=new Array(i),s=0;s<i;s++)r[s]=arguments[s];return e=t.call.apply(t,[this].concat(r))||this,n(e,"lbUserName",P,o(e)),n(e,"lbSkinName",x,o(e)),n(e,"sprAvatar",L,o(e)),n(e,"lbAtk",B,o(e)),n(e,"lbAtkSpeed",D,o(e)),n(e,"lbBaoHurtRat",H,o(e)),n(e,"lbBaoAtkRat",R,o(e)),n(e,"lbSkillCd",V,o(e)),n(e,"lbHp",_,o(e)),e.tw=void 0,e.fromPos=null,e.toPos=null,e}i(e,t);var r=e.prototype;return r.setData=function(t){var e=this;f("E_GiftMessageType.UserInfo",t),this.fromPos||(this.fromPos=new c(u.getVisibleSize().width/2+this.node.getComponent(p).width/2,-u.getVisibleSize().height/2+this.node.getComponent(p).height+240,0)),this.toPos||(this.toPos=new c(u.getVisibleSize().width/2-this.node.getComponent(p).width/2-80,-u.getVisibleSize().height/2+this.node.getComponent(p).height+240,0)),this.node.setPosition(this.fromPos),this.tw=m(this.node).to(.4,{position:this.toPos}).delay(4.5).call((function(){e.kill()})),this.tw.start();for(var i=k.getInstance().getBeadConfigs(),n=0;n<i.length;n++){var o=void 0;switch(i[n].jsonId){case"260001":o={lev:t.dimonEffects.attackLev,num:t.dimonEffects.attackDmNum,prop:t.dimonEffects.attack,txt:"攻击+"+t.dimonEffects.attack},this.lbAtk.string=t.minAtkNum+o.prop+"~"+(t.maxAtkNum+o.prop);break;case"260002":o={lev:t.dimonEffects.skillcdLev,num:t.dimonEffects.skillcdDmNum,prop:t.dimonEffects.skillcd,txt:"法宝速度+"+Math.floor(100*t.dimonEffects.skillcd)+"%"},this.lbSkillCd.string="+"+Math.floor(100*t.dimonEffects.skillcd)+"%";break;case"260003":o={lev:t.dimonEffects.atkspeedLev,num:t.dimonEffects.atkspeedDmNum,prop:t.dimonEffects.atkspeed,txt:"攻速+"+Math.floor(100*t.dimonEffects.atkspeed)+"%"},this.lbAtkSpeed.string=t.speed.toFixed(2)+"S";break;case"260004":o={lev:t.dimonEffects.atkmLev,num:t.dimonEffects.atkmDmNum,prop:t.dimonEffects.atkm,txt:"暴击伤害+"+Math.floor(100*t.dimonEffects.atkm)+"%"},this.lbBaoHurtRat.string=Math.floor(100*(t.dimonEffects.baseAtkM+o.prop))+"%";break;case"260005":o={lev:t.dimonEffects.hpLev,num:t.dimonEffects.hpDmNum,prop:t.dimonEffects.hp,txt:"城门血量+"+t.dimonEffects.hp},this.lbHp.string="+"+o.prop}}this.lbUserName.string=t.name||"匿名用户",g.res.remoteLoadSprite(t.avatar,this.sprAvatar,d(90,90)),this.lbBaoAtkRat.string=Math.floor(100*t.dimonEffects.atkr)+"%";var r=k.getInstance().getSkinConfigByJsonId(t.skinId);r&&(this.lbSkinName.string=r.name)},r.kill=function(){this.node.removeFromParent(),h.getInstance().killUserInfoMessage(this)},e}(b)).prototype,"lbUserName",[E],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),x=e(C.prototype,"lbSkinName",[w],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),L=e(C.prototype,"sprAvatar",[y],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),B=e(C.prototype,"lbAtk",[M],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),D=e(C.prototype,"lbAtkSpeed",[S],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),H=e(C.prototype,"lbBaoHurtRat",[z],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),R=e(C.prototype,"lbBaoAtkRat",[A],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),V=e(C.prototype,"lbSkillCd",[I],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),_=e(C.prototype,"lbHp",[U],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),N=C))||N));r._RF.pop()}}}));

System.register("chunks:///_virtual/ViewCommonTips.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ViewBase.ts"],(function(e){var i,t,n,r,o,l,s,a;return{setters:[function(e){i=e.applyDecoratedDescriptor,t=e.inheritsLoose,n=e.initializerDefineProperty,r=e.assertThisInitialized},function(e){o=e.cclegacy,l=e._decorator,s=e.Label},function(e){a=e.ViewBase}],execute:function(){var c,u,p,f,b,m,y;o._RF.push({},"f188dWveQBAapRIKZegZT1A","ViewCommonTips",void 0);var w=l.ccclass,g=l.property;e("ViewCommonTips",(c=w("ViewCommonTips"),u=g(s),p=g(s),c((m=i((b=function(e){function i(){for(var i,t=arguments.length,o=new Array(t),l=0;l<t;l++)o[l]=arguments[l];return i=e.call.apply(e,[this].concat(o))||this,n(i,"lbTitle",m,r(i)),n(i,"lbDesc",y,r(i)),i}return t(i,e),i.prototype.setData=function(e){this.lbDesc.string=e.desc},i}(a)).prototype,"lbTitle",[u],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),y=i(b.prototype,"lbDesc",[p],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),f=b))||f));o._RF.pop()}}}));

System.register("chunks:///_virtual/ViewCrossRewardDesc.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ViewBase.ts","./ConfigHelper.ts"],(function(e){var r,t,i,n,s,o,c,a,l;return{setters:[function(e){r=e.applyDecoratedDescriptor,t=e.inheritsLoose,i=e.initializerDefineProperty,n=e.assertThisInitialized},function(e){s=e.cclegacy,o=e._decorator,c=e.Label},function(e){a=e.ViewBase},function(e){l=e.ConfigHelper}],execute:function(){var u,p,f,d,g;s._RF.push({},"f8d39vUb1ZJHbtDd3rUR5g1","ViewCrossRewardDesc",void 0);var w=o.ccclass,y=o.property;e("ViewCrossRewardDesc",(u=w("ViewCrossRewardDesc"),p=y(c),u((g=r((d=function(e){function r(){for(var r,t=arguments.length,s=new Array(t),o=0;o<t;o++)s[o]=arguments[o];return r=e.call.apply(e,[this].concat(s))||this,i(r,"lbDesc",g,n(r)),r}return t(r,e),r.prototype.onLoadCompleted=function(){var e=l.getInstance().getConstantConfigByKey("firstPassRule");e&&(this.lbDesc.string=e)},r}(a)).prototype,"lbDesc",[p],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),f=d))||f));s._RF.pop()}}}));

System.register("chunks:///_virtual/ViewDimondReward.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ViewBase.ts","./ConfigHelper.ts","./xcore.ts","./Tool.ts","./UnitDimondReward.ts","./ConstGlobal.ts"],(function(e){var t,n,i,r,o,a,s,l,c,u,d,f,g,h,b,y,p,m;return{setters:[function(e){t=e.applyDecoratedDescriptor,n=e.inheritsLoose,i=e.initializerDefineProperty,r=e.assertThisInitialized},function(e){o=e.cclegacy,a=e._decorator,s=e.Label,l=e.Prefab,c=e.Node,u=e.Button,d=e.log,f=e.instantiate},function(e){g=e.ViewBase},function(e){h=e.ConfigHelper},function(e){b=e.xcore},function(e){y=e.default},function(e){p=e.UnitDimondReward},function(e){m=e.C_View}],execute:function(){var w,C,D,v,L,_,B,I,R,x,V,T,z;o._RF.push({},"656442NCsZCi54JsECa5Chd","ViewDimondReward",void 0);var W=a.ccclass,M=a.property;e("ViewDimondReward",(w=W("ViewDimondReward"),C=M(s),D=M(l),v=M(s),L=M(c),_=M(u),w((R=t((I=function(e){function t(){for(var t,n=arguments.length,o=new Array(n),a=0;a<n;a++)o[a]=arguments[a];return t=e.call.apply(e,[this].concat(o))||this,i(t,"lbDesc",R,r(t)),i(t,"pfbUnitDimondReward",x,r(t)),i(t,"lbBtnTxt",V,r(t)),i(t,"ndContent",T,r(t)),i(t,"btnClose2",z,r(t)),t._time=21,t._cb=null,t}n(t,e);var o=t.prototype;return o.onLoadCompleted=function(){var e=this;this.btnClose2.node.on("click",(function(){var t;b.ui.closeView(m.ViewDimondReward,!0),null==(t=e.node)||t.destroy()}),this)},o.setData=function(e){var t=e.id,n=e.freeid;this._cb=e.cb;var i=h.getInstance().getConstantConfigByKey("lotteryScore")||200,r=h.getInstance().getConstantConfigByKey("freeLotteryScore")||100,o=h.getInstance().getLotteryConfigByJsonId(t);this.lbDesc.string=o[["easyText","normaText","hardText"][b.gameData.gameMode]];for(var a=[3,2,1,1],s=0;s<e.users.length;s++){var l=e.users[s],c=a[s]||1,u=l[1].giftscore,g=l[1].score,m=[],w=void 0;if(u>=i){var C=h.getInstance().getLotteryDebrisConfigsByJsonId(t);w=t;for(var D=0;D<C.length;D++){var v=C[D],L=[v.easyLotteryWeight,v.normalLotteryWeight,v.hardLotteryWeight][b.gameData.gameMode];m.push({id:v.skinFragmentId,weight:L})}}else{if(!(g>=r)){d("积分不足",u,g);continue}var _=h.getInstance().getLotteryDebrisConfigsByJsonId(n);w=n;for(var B=0;B<_.length;B++){var I=_[B],R=[I.easyLotteryWeight,I.normalLotteryWeight,I.hardLotteryWeight][b.gameData.gameMode];m.push({id:I.skinFragmentId,weight:R})}}d("宝珠：",u,g,m);for(var x=[],V=0;V<c;V++){var T=y.selectArrayIdByWeight(m);T&&x.push(T)}if(m.length>0){var z=f(this.pfbUnitDimondReward);z.setParent(this.ndContent),z.getComponent(p).setData({targetId:w,user:l,ids:x},s)}}},o.update=function(e){this._time>0&&(this._time-=e,this.lbBtnTxt.string="继续关卡("+Math.floor(this._time)+")",this._time<=0&&this.closeSelf())},o.onDestroyCompleted=function(){this._cb&&this._cb()},t}(g)).prototype,"lbDesc",[C],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),x=t(I.prototype,"pfbUnitDimondReward",[D],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),V=t(I.prototype,"lbBtnTxt",[v],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),T=t(I.prototype,"ndContent",[L],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),z=t(I.prototype,"btnClose2",[_],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),B=I))||B));o._RF.pop()}}}));

System.register("chunks:///_virtual/ViewExchange.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ViewBase.ts","./ScrollViewProCom.ts","./ConfigHelper.ts","./UnitExchange.ts"],(function(e){var n,t,i,o,r,a,c,s,l,u;return{setters:[function(e){n=e.applyDecoratedDescriptor,t=e.inheritsLoose,i=e.initializerDefineProperty,o=e.assertThisInitialized},function(e){r=e.cclegacy,a=e._decorator},function(e){c=e.ViewBase},function(e){s=e.default},function(e){l=e.ConfigHelper},function(e){u=e.UnitExchange}],execute:function(){var p,f,g,h,v;r._RF.push({},"da346jZRGpOOZaVh1wPlKmi","ViewExchange",void 0);var w=a.ccclass,d=a.property;e("ViewExchange",(p=w("ViewExchange"),f=d(s),p((v=n((h=function(e){function n(){for(var n,t=arguments.length,r=new Array(t),a=0;a<t;a++)r[a]=arguments[a];return n=e.call.apply(e,[this].concat(r))||this,i(n,"svContent",v,o(n)),n}return t(n,e),n.prototype.onLoadCompleted=function(){var e=l.getInstance().getExchangeConfigsByJsonId("470001");this.svContent.setView(e,(function(e,n,t){var i=e.getComponent(u);i&&i.setData(n)}))},n}(c)).prototype,"svContent",[f],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),g=h))||g));r._RF.pop()}}}));

System.register("chunks:///_virtual/ViewExchangeShow.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ViewBase.ts","./ConfigHelper.ts","./xcore.ts"],(function(e){var r,n,t,i,o,a,c,l,s,u,p,f;return{setters:[function(e){r=e.applyDecoratedDescriptor,n=e.inheritsLoose,t=e.initializerDefineProperty,i=e.assertThisInitialized},function(e){o=e.cclegacy,a=e._decorator,c=e.Sprite,l=e.Label,s=e.size},function(e){u=e.ViewBase},function(e){p=e.ConfigHelper},function(e){f=e.xcore}],execute:function(){var g,h,b,w,y,m,d,v,x;o._RF.push({},"999d2sZ8NZDhruNZ9Nc/Y4K","ViewExchangeShow",void 0);var I=a.ccclass,S=a.property;e("ViewExchangeShow",(g=I("ViewExchangeShow"),h=S(c),b=S(l),w=S(l),g((d=r((m=function(e){function r(){for(var r,n=arguments.length,o=new Array(n),a=0;a<n;a++)o[a]=arguments[a];return r=e.call.apply(e,[this].concat(o))||this,t(r,"sprIcon",d,i(r)),t(r,"lbName",v,i(r)),t(r,"lbProp",x,i(r)),r}return n(r,e),r.prototype.setData=function(e){this.lbName.string=e.name;var r=p.getInstance().getDebriConfigByJsonId(e.skinFragmentId),n="./res/image/"+r.path+"/"+r.icon;f.res.remoteLoadSprite(n,this.sprIcon,s(424,600))},r}(u)).prototype,"sprIcon",[h],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),v=r(m.prototype,"lbName",[b],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),x=r(m.prototype,"lbProp",[w],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),y=m))||y));o._RF.pop()}}}));

System.register("chunks:///_virtual/ViewFightRank.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ViewBase.ts","./xcore.ts","./ConstGlobal.ts","./ScrollViewProCom.ts","./UnitRoundRank.ts","./UnitTopRank.ts","./TimeUtil.ts"],(function(e){var t,n,i,r,a,o,u,s,l,c,f,p,d,b,k,R,m,h,v,w,g;return{setters:[function(e){t=e.applyDecoratedDescriptor,n=e.inheritsLoose,i=e.initializerDefineProperty,r=e.assertThisInitialized,a=e.asyncToGenerator,o=e.regeneratorRuntime},function(e){u=e.cclegacy,s=e._decorator,l=e.Button,c=e.Label,f=e.Node,p=e.Prefab,d=e.instantiate},function(e){b=e.ViewBase},function(e){k=e.xcore},function(e){R=e.E_EVENT,m=e.C_View},function(e){h=e.default},function(e){v=e.UnitRoundRank},function(e){w=e.UnitTopRank},function(e){g=e.default}],execute:function(){var y,T,D,V,C,_,x,L,U,I,z,B,E,N,W;u._RF.push({},"d34f8bHnjBAjqK8v7VM7khS","ViewFightRank",void 0);var F=s.ccclass,G=s.property;e("ViewFightRank",(y=F("ViewFightRank"),T=G(l),D=G(l),V=G(c),C=G(f),_=G(h),x=G(p),y((I=t((U=function(e){function t(){for(var t,n=arguments.length,a=new Array(n),o=0;o<n;o++)a[o]=arguments[o];return t=e.call.apply(e,[this].concat(a))||this,i(t,"btnWorldRank",I,r(t)),i(t,"btNextRound",z,r(t)),i(t,"lbTime",B,r(t)),i(t,"ndTopContent",E,r(t)),i(t,"svRankContent",N,r(t)),i(t,"pfbUnitTopRank",W,r(t)),t._time=60,t}n(t,e);var u=t.prototype;return u.start=function(){this.addButtonEvent(this.btNextRound,a(o().mark((function e(){return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,k.ui.closeAllView();case 2:k.event.raiseEvent(R.GameReplay);case 3:case"end":return e.stop()}}),e)}))),this),this.addButtonEvent(this.btnWorldRank,(function(){k.ui.addView(m.ViewRank)}),this)},u.setData=function(e){for(var t=this,n=function(){var n=d(t.pfbUnitTopRank),r=e.scoreList[i];if(r){var a=e.userList.find((function(e){return e.userId==r.userId}));n.parent=t.ndTopContent;var o=e.oldWeekDatas?e.oldWeekDatas[a.userId]:-1,u=e.weekRankDatas?e.weekRankDatas[a.userId]:-1;n.getComponent(w).setData(e.scoreList[i],a.nickName,a.iconUrl,i,a.upscore,e.maxLevel,o,u)}},i=0;i<3;i++)n();this.svRankContent.setView(e.scoreList,(function(t,n,i){var r=e.userList.find((function(e){return e.userId==n.userId})),a=e.oldWeekDatas?e.oldWeekDatas[r.userId]:-1,o=e.weekRankDatas?e.weekRankDatas[r.userId]:-1;t.getComponent(v).setData(n,r.nickName,r.iconUrl,i,e.maxLevel,a,o,r.upscore)}))},u.update=function(e){this._time>0&&(this._time-=e,this.lbTime.string=g.formatTime(1e3*Math.floor(this._time),2),this._time<=0&&(k.event.raiseEvent(R.GameReplay),this.closeSelf()))},t}(b)).prototype,"btnWorldRank",[T],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),z=t(U.prototype,"btNextRound",[D],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),B=t(U.prototype,"lbTime",[V],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),E=t(U.prototype,"ndTopContent",[C],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),N=t(U.prototype,"svRankContent",[_],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),W=t(U.prototype,"pfbUnitTopRank",[x],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),L=U))||L));u._RF.pop()}}}));

System.register("chunks:///_virtual/ViewGameOver.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ViewBase.ts","./xcore.ts","./ConstGlobal.ts","./TimeUtil.ts","./Tool.ts"],(function(e){var t,i,r,n,l,s,o,a,u,c,b,f,p,m,h,d,g,M;return{setters:[function(e){t=e.applyDecoratedDescriptor,i=e.inheritsLoose,r=e.initializerDefineProperty,n=e.assertThisInitialized,l=e.asyncToGenerator,s=e.regeneratorRuntime},function(e){o=e.cclegacy,a=e._decorator,u=e.Label,c=e.Button,b=e.Node,f=e.Sprite,p=e.size},function(e){m=e.ViewBase},function(e){h=e.xcore},function(e){d=e.C_View},function(e){g=e.default},function(e){M=e.default}],execute:function(){var w,v,U,y,T,F,K,z,N,B,k,H,_,A,V,C,x,R,S,L,D,G,I,W,O,P,j,J,q,E,Q,X,Y,Z,$,ee,te,ie,re,ne,le,se,oe,ae,ue,ce,be;o._RF.push({},"792d71v7xJMv64a8rs0/smV","ViewGameOver",void 0);var fe=a.ccclass,pe=a.property;e("ViewGameOver",(w=fe("ViewGameOver"),v=pe(u),U=pe(c),y=pe(u),T=pe(b),F=pe(b),K=pe(b),z=pe(b),N=pe(f),B=pe(u),k=pe(b),H=pe(f),_=pe(u),A=pe(b),V=pe(f),C=pe(u),x=pe(b),R=pe(f),S=pe(u),L=pe(b),D=pe(u),G=pe(b),I=pe(u),w((P=t((O=function(e){function t(){for(var t,i=arguments.length,l=new Array(i),s=0;s<i;s++)l[s]=arguments[s];return t=e.call.apply(e,[this].concat(l))||this,r(t,"lbDesc",P,n(t)),r(t,"btnCheckRank",j,n(t)),r(t,"lbBtnTxt",J,n(t)),r(t,"ndTitleCross",q,n(t)),r(t,"ndTitleWin",E,n(t)),r(t,"ndTitleFail",Q,n(t)),r(t,"ndFightFirst",X,n(t)),r(t,"sprAvatarFightFirst",Y,n(t)),r(t,"lbNameFightFirst",Z,n(t)),r(t,"ndMostKillBoss",$,n(t)),r(t,"sprAvatarMostKillBoss",ee,n(t)),r(t,"lbNameMostKillBoss",te,n(t)),r(t,"ndMostKillMonster",ie,n(t)),r(t,"sprAvatarMostKillMonster",re,n(t)),r(t,"lbNameMostKillMonster",ne,n(t)),r(t,"ndMostHelpful",le,n(t)),r(t,"sprAvatarMostHelpful",se,n(t)),r(t,"lbNameMostHelpful",oe,n(t)),r(t,"ndTime",ae,n(t)),r(t,"lbTime",ue,n(t)),r(t,"ndUserNum",ce,n(t)),r(t,"lbUserNum",be,n(t)),t._data=void 0,t._time=11,t}i(t,e);var o=t.prototype;return o.setData=function(){var e=l(s().mark((function e(t){var i=this;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:this._data=t,this.ndTitleCross.active=t.isCross,this.ndTitleWin.active=t.isWin&&!t.isCross,this.ndTitleFail.active=!t.isWin,t.firstFightUser&&(this.lbNameFightFirst.string=M.sub(t.firstFightUser.nickName||t.firstFightUser.userId,10,!0),h.res.remoteLoadSprite(t.firstFightUser.iconUrl,this.sprAvatarFightFirst,p(106,71060))),t.firstKillBossUser&&(this.lbNameMostKillBoss.string=M.sub(t.firstKillBossUser.nickName||t.firstKillBossUser.userId,10,!0),h.res.remoteLoadSprite(t.firstKillBossUser.iconUrl,this.sprAvatarMostKillBoss,p(106,106))),t.firstKillMonsterUser&&(this.lbNameMostKillMonster.string=M.sub(t.firstKillMonsterUser.nickName||t.firstKillMonsterUser.userId,10,!0),h.res.remoteLoadSprite(t.firstKillMonsterUser.iconUrl,this.sprAvatarMostKillMonster,p(106,106))),t.firstHelpTowerUser&&(this.lbNameMostHelpful.string=M.sub(t.firstHelpTowerUser.nickName||t.firstHelpTowerUser.userId,10,!0),h.res.remoteLoadSprite(t.firstHelpTowerUser.iconUrl,this.sprAvatarMostHelpful,p(106,106))),this.ndFightFirst.active=!!t.firstFightUser,this.ndMostKillBoss.active=!!t.firstKillBossUser,this.ndMostKillMonster.active=!!t.firstKillMonsterUser,this.ndMostHelpful.active=!!t.firstHelpTowerUser,this.ndTime.active=!!t.corssTime,this.ndUserNum.active=!!t.totalUserNum,this.lbTime.string=g.formatTime(1e3*Math.floor(t.corssTime),2),this.lbUserNum.string=t.totalUserNum,this.btnCheckRank.node.on("click",(function(){h.ui.addView(d.ViewFightRank,i._data),i.closeSelf()}),this);case 17:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),o.update=function(e){this._time>0&&(this._time-=e,this.lbBtnTxt.string="查看排行榜("+Math.floor(this._time)+")",this._time<=0&&(this.lbBtnTxt.string="查看排行榜",this.btnCheckRank.interactable=!1,h.ui.addView(d.ViewFightRank,this._data),this.closeSelf()))},t}(m)).prototype,"lbDesc",[v],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),j=t(O.prototype,"btnCheckRank",[U],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),J=t(O.prototype,"lbBtnTxt",[y],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),q=t(O.prototype,"ndTitleCross",[T],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),E=t(O.prototype,"ndTitleWin",[F],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Q=t(O.prototype,"ndTitleFail",[K],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),X=t(O.prototype,"ndFightFirst",[z],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Y=t(O.prototype,"sprAvatarFightFirst",[N],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Z=t(O.prototype,"lbNameFightFirst",[B],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),$=t(O.prototype,"ndMostKillBoss",[k],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),ee=t(O.prototype,"sprAvatarMostKillBoss",[H],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),te=t(O.prototype,"lbNameMostKillBoss",[_],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),ie=t(O.prototype,"ndMostKillMonster",[A],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),re=t(O.prototype,"sprAvatarMostKillMonster",[V],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),ne=t(O.prototype,"lbNameMostKillMonster",[C],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),le=t(O.prototype,"ndMostHelpful",[x],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),se=t(O.prototype,"sprAvatarMostHelpful",[R],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),oe=t(O.prototype,"lbNameMostHelpful",[S],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),ae=t(O.prototype,"ndTime",[L],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),ue=t(O.prototype,"lbTime",[D],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),ce=t(O.prototype,"ndUserNum",[G],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),be=t(O.prototype,"lbUserNum",[I],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),W=O))||W));o._RF.pop()}}}));

System.register("chunks:///_virtual/ViewOpenBox.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ViewBase.ts","./xcore.ts","./ConstGlobal.ts"],(function(e){var i,t,n,o,r,s,a,c,d,l,u;return{setters:[function(e){i=e.applyDecoratedDescriptor,t=e.inheritsLoose,n=e.initializerDefineProperty,o=e.assertThisInitialized},function(e){r=e.cclegacy,s=e._decorator,a=e.sp,c=e.Button},function(e){d=e.ViewBase},function(e){l=e.xcore},function(e){u=e.C_View}],execute:function(){var f,p,w,h,b,y,V;r._RF.push({},"3cdc3d8aIBEPIAanAZQjgEk","ViewOpenBox",void 0);var m=s.ccclass,k=s.property;e("ViewOpenBox",(f=m("ViewOpenBox"),p=k(a.Skeleton),w=k(c),f((y=i((b=function(e){function i(){for(var i,t=arguments.length,r=new Array(t),s=0;s<t;s++)r[s]=arguments[s];return i=e.call.apply(e,[this].concat(r))||this,n(i,"anim",y,o(i)),n(i,"btnOption",V,o(i)),i._data=null,i}t(i,e);var r=i.prototype;return r.onLoadCompleted=function(){var e=this;this.btnOption.node.on("click",(function(){e.checkRewards(e._data),e.node.destroy(),l.ui.closeView(u.ViewOpenBox)}),this)},r.setData=function(e){var i=this;this._data=e,this.openBox(e),this.btnClose.node.off("click"),this.btnClose.node.on("click",(function(){i.checkRewards(e),i.closeSelf()}),this)},r.checkRewards=function(e){var i=e.users;e.dimondId?l.ui.addView(u.ViewDimondReward,{id:e.dimondId,freeid:e.freeDimondId,users:i,cb:function(){e.lotteryId?l.ui.addView(u.ViewSkinDebrisReward,{isPassLottery:e.isPassLottery,id:e.lotteryId,freeid:e.freeid,users:i,cb:e.cb}):e.cb&&e.cb()}}):e.lotteryId&&l.ui.addView(u.ViewSkinDebrisReward,{isPassLottery:e.isPassLottery,id:e.lotteryId,freeid:e.freeid,users:i,cb:e.cb})},r.openBox=function(e){var i=this;this.anim.setAnimation(0,"open",!1),this.anim.setCompleteListener((function(){i.node&&i.node.isValid&&(i.checkRewards(e),i.closeSelf())}))},i}(d)).prototype,"anim",[p],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),V=i(b.prototype,"btnOption",[w],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),h=b))||h));r._RF.pop()}}}));

System.register("chunks:///_virtual/ViewPowerRank.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ViewBase.ts","./ScrollViewProCom.ts","./FightMgr.ts","./UnitPowerRank.ts"],(function(n){var e,t,i,r,o,a,l,c,s,u,f,k,b;return{setters:[function(n){e=n.applyDecoratedDescriptor,t=n.inheritsLoose,i=n.initializerDefineProperty,r=n.assertThisInitialized},function(n){o=n.cclegacy,a=n._decorator,l=n.Button,c=n.Node,s=n.Label},function(n){u=n.ViewBase},function(n){f=n.default},function(n){k=n.FightMgr},function(n){b=n.UnitPowerRank}],execute:function(){var p,h,R,d,w,g,y,m,v,P,_,I,z,C,x,S,O,V,G,L,B,D,F;o._RF.push({},"cbff3/Uk3NIgL7y6GktPg2M","ViewPowerRank",void 0);var M=a.ccclass,U=a.property;n("ViewPowerRank",(p=M("ViewPowerRank"),h=U(f),R=U(l),d=U(c),w=U(s),g=U(l),y=U(c),m=U(l),v=U(c),P=U(c),_=U(s),p((C=e((z=function(n){function e(){for(var e,t=arguments.length,o=new Array(t),a=0;a<t;a++)o[a]=arguments[a];return e=n.call.apply(n,[this].concat(o))||this,i(e,"svRankContent",C,r(e)),i(e,"btnScoreRank",x,r(e)),i(e,"ndScoreRankOn",S,r(e)),i(e,"lbScoreRank",O,r(e)),i(e,"btnPowerRank",V,r(e)),i(e,"ndPowerRankOn",G,r(e)),i(e,"btnGiftRank",L,r(e)),i(e,"ndGiftRankOn",B,r(e)),i(e,"ndEmpty",D,r(e)),i(e,"lbPowerRank",F,r(e)),e._users=void 0,e._selectIndex=0,e}t(e,n);var o=e.prototype;return o.onLoadCompleted=function(){this.btnScoreRank.node.on("click",this.onClickRank.bind(this,1),this),this.btnPowerRank.node.on("click",this.onClickRank.bind(this,2),this),this.btnGiftRank.node.on("click",this.onClickRank.bind(this,3),this),this.onClickRank(1)},o.onClickRank=function(n){var e=this;if(this._selectIndex!=n){this._selectIndex=n,this.ndScoreRankOn.active=1==this._selectIndex,this.ndPowerRankOn.active=2==this._selectIndex,this.ndGiftRankOn.active=3==this._selectIndex,this._users=k.getInstance().getIUsers();var t=Array.from(this._users);1==this._selectIndex?t.sort((function(n,e){return e[1].score-n[1].score})):2==this._selectIndex?t.sort((function(n,e){return e[1].killscore-n[1].killscore})):t.sort((function(n,e){return e[1].giftscore-n[1].giftscore})),this.ndEmpty.active=t.length<=0,this.svRankContent.setView(t,(function(n,t,i){n.getComponent(b).setData(t[1],i,e._selectIndex)})),this.svRankContent.scrollToTop(.1)}},e}(u)).prototype,"svRankContent",[h],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),x=e(z.prototype,"btnScoreRank",[R],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),S=e(z.prototype,"ndScoreRankOn",[d],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),O=e(z.prototype,"lbScoreRank",[w],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),V=e(z.prototype,"btnPowerRank",[g],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),G=e(z.prototype,"ndPowerRankOn",[y],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),L=e(z.prototype,"btnGiftRank",[m],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),B=e(z.prototype,"ndGiftRankOn",[v],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),D=e(z.prototype,"ndEmpty",[P],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),F=e(z.prototype,"lbPowerRank",[_],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),I=z))||I));o._RF.pop()}}}));

System.register("chunks:///_virtual/ViewRank.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ViewBase.ts","./ScrollViewProCom.ts","./ConfigHelper.ts","./Net.ts","./xcore.ts","./ConstGlobal.ts","./Tool.ts","./UnitRank.ts"],(function(e){var t,n,a,r,o,i,s,c,l,u,f,g,h,m,p,k,R,b,v,d,y;return{setters:[function(e){t=e.applyDecoratedDescriptor,n=e.inheritsLoose,a=e.initializerDefineProperty,r=e.assertThisInitialized,o=e.asyncToGenerator,i=e.regeneratorRuntime},function(e){s=e.cclegacy,c=e._decorator,l=e.Button,u=e.Label,f=e.v3,g=e.log,h=e.Color},function(e){m=e.ViewBase},function(e){p=e.default},function(e){k=e.ConfigHelper},function(e){R=e.default},function(e){b=e.xcore},function(e){v=e.E_EVENT},function(e){d=e.default},function(e){y=e.UnitRank}],execute:function(){var T,I,D,_,w,x,C,B,V,N,L,S,E,z,G,K,P;s._RF.push({},"cb465fcQypH7qXYK7lbAXIf","ViewRank",void 0);var H=c.ccclass,F=c.property,A=function(e){return e[e.score=0]="score",e[e.level=1]="level",e[e.live=2]="live",e[e.gift=3]="gift",e}(A||{});e("ViewRank",(T=H("ViewRank"),I=F([l]),D=F(u),_=F(u),w=F(u),x=F(u),C=F(u),B=F(p),T((L=t((N=function(e){function t(){for(var t,n=arguments.length,o=new Array(n),i=0;i<n;i++)o[i]=arguments[i];return t=e.call.apply(e,[this].concat(o))||this,a(t,"tags",L,r(t)),a(t,"lbTxtRankScore",S,r(t)),a(t,"lbTxtRankLev",E,r(t)),a(t,"lbTxtRankReward",z,r(t)),a(t,"lbTopTxtGiftScore",G,r(t)),a(t,"lbGiftTitle",K,r(t)),t._selectType=A.score,t._itemNum=0,a(t,"svRankContent",P,r(t)),t.onRefreshBottom=void 0,t._isBottom=!1,t._tempVec1=f(110,142,0),t._tempVec2=f(290,142,0),t}n(t,e);var s=t.prototype;return s.onLoadCompleted=function(){for(var e=this,t=function(t){e.tags[t].node.on("click",o(i().mark((function n(a){var r;return i().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(!e._isBottom){n.next=2;break}return n.abrupt("return");case 2:if(e._selectType!=t){n.next=4;break}return n.abrupt("return");case 4:return r=e._selectType,n.prev=5,e.tags.forEach((function(e){return e.enabled=!1})),e._itemNum=0,e._selectType=t,n.next=11,e.onRefreshData(a);case 11:e._isBottom=!1,e.svRankContent.scrollToTop(.1),e.tags.forEach((function(e){return e.enabled=!0})),n.next=20;break;case 16:n.prev=16,n.t0=n.catch(5),e.tags.forEach((function(e){return e.enabled=!0})),e._selectType=r;case 20:case"end":return n.stop()}}),n,null,[[5,16]])}))),e)},n=0;n<this.tags.length;n++)t(n);b.event.addEventListener(v.RankInfo,this.onRefreshView,this),this.onRefreshBottom=d.throttle(o(i().mark((function t(n){return i().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(void 0===n&&(n=!1),!e._isBottom){t.next=3;break}return t.abrupt("return");case 3:e.onRefreshData(n);case 4:case"end":return t.stop()}}),t)}))),1e3),this.svRankContent.node.on("scroll-to-bottom",this.onRefreshBottom.bind(this,!1),this),this.onRefreshBottom(!0)},s.onScrollBottom=function(){},s.onRefreshData=function(){var e=o(i().mark((function e(t){var n;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:this.tags.forEach((function(e){return e.node.getChildByName("ndOn").active=!1})),this.tags.forEach((function(e){return e.node.getChildByName("Label").getComponent(u).color=new h(52,36,24,255)})),this.tags[this._selectType].node.getChildByName("ndOn").active=!0,this.tags[this._selectType].node.getChildByName("Label").getComponent(u).color=new h(255,255,255,255),this.lbTxtRankScore.node.active=this._selectType==A.score,this.lbTxtRankLev.node.active=this._selectType!=A.score&&this._selectType!=A.gift,this.lbTxtRankReward.node.active=this._selectType==A.score,this.lbGiftTitle.node.active=this._selectType==A.gift,this.lbTopTxtGiftScore.node.active=this._selectType==A.gift,this._selectType==A.score?this.lbTxtRankScore.node.setPosition(this._tempVec1):this.lbTxtRankScore.node.setPosition(this._tempVec2),b.gameData.scoreRankInfo||(b.gameData.scoreRankInfo=[]),b.gameData.roundRankInfo||(b.gameData.roundRankInfo=[]),b.gameData.liverRankInfo||(b.gameData.liverRankInfo=[]),b.gameData.giftRankInfo||(b.gameData.giftRankInfo=[]),e.t0=this._selectType,e.next=e.t0===A.score?17:e.t0===A.level?25:e.t0===A.live?33:e.t0===A.gift?41:49;break;case 17:return n=k.getInstance().getRankKeyByMonth(),e.next=20,R.getRankInfo(n,b.gameData.scoreRankInfo.length,b.gameData.scoreRankInfo.length+10);case 20:if(t||b.gameData.scoreRankInfo.length!=this._itemNum){e.next=23;break}return b.ui.showToast("到底啦"),e.abrupt("return");case 23:return this._itemNum=b.gameData.scoreRankInfo.length,e.abrupt("break",50);case 25:return n=k.getInstance().getRankKeyByLev(),e.next=28,R.getRankInfo(n,b.gameData.roundRankInfo.length,b.gameData.roundRankInfo.length+10);case 28:if(t||b.gameData.roundRankInfo.length!=this._itemNum){e.next=31;break}return b.ui.showToast("到底啦"),e.abrupt("return");case 31:return this._itemNum=b.gameData.roundRankInfo.length,e.abrupt("break",50);case 33:return n=k.getInstance().getLiverKeyByLev(),e.next=36,R.getLiverRankInfo(n,b.gameData.liverRankInfo.length,b.gameData.liverRankInfo.length+10);case 36:if(t||b.gameData.liverRankInfo.length!=this._itemNum){e.next=39;break}return b.ui.showToast("到底啦"),e.abrupt("return");case 39:return this._itemNum=b.gameData.liverRankInfo.length,e.abrupt("break",50);case 41:return n=k.getInstance().getGiftRankKeyByForever(),e.next=44,R.getRankInfo(n,b.gameData.giftRankInfo.length,b.gameData.giftRankInfo.length+10);case 44:if(t||b.gameData.giftRankInfo.length!=this._itemNum){e.next=47;break}return b.ui.showToast("到底啦"),e.abrupt("return");case 47:return this._itemNum=b.gameData.giftRankInfo.length,e.abrupt("break",50);case 49:return e.abrupt("break",50);case 50:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),s.onRefreshView=function(){var e=this;switch(g("onrefreshInfo:",b.gameData.scoreRankInfo,b.gameData.roundRankInfo,b.gameData.liverRankInfo,b.gameData.giftRankInfo),this._selectType){case A.score:this.svRankContent.setView(b.gameData.scoreRankInfo,(function(t,n,a){t.getComponent(y).setData(n,e._selectType)}));break;case A.level:this.svRankContent.setView(b.gameData.roundRankInfo,(function(t,n,a){t.getComponent(y).setData(n,e._selectType)}));break;case A.live:this.svRankContent.setView(b.gameData.liverRankInfo,(function(t,n,a){t.getComponent(y).setData(n,e._selectType)}));break;case A.gift:this.svRankContent.setView(b.gameData.giftRankInfo,(function(t,n,a){t.getComponent(y).setData(n,e._selectType)}))}},s.onDestroyCompleted=function(){b.event.removeEventListener(v.RankInfo,this.onRefreshView,this),b.gameData.scoreRankInfo=[],b.gameData.roundRankInfo=[],b.gameData.liverRankInfo=[],b.gameData.giftRankInfo=[]},t}(m)).prototype,"tags",[I],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return[]}}),S=t(N.prototype,"lbTxtRankScore",[D],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),E=t(N.prototype,"lbTxtRankLev",[_],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),z=t(N.prototype,"lbTxtRankReward",[w],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),G=t(N.prototype,"lbTopTxtGiftScore",[x],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),K=t(N.prototype,"lbGiftTitle",[C],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),P=t(N.prototype,"svRankContent",[B],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),V=N))||V));s._RF.pop()}}}));

System.register("chunks:///_virtual/ViewRoundToast.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ViewBase.ts"],(function(e){var t,i,n,o,r,s,a,c;return{setters:[function(e){t=e.applyDecoratedDescriptor,i=e.inheritsLoose,n=e.initializerDefineProperty,o=e.assertThisInitialized},function(e){r=e.cclegacy,s=e._decorator,a=e.Label},function(e){c=e.ViewBase}],execute:function(){var l,u,p,f,d;r._RF.push({},"9620c0KEtNP76PS0E3rnZd6","ViewRoundToast",void 0);var h=s.ccclass,y=s.property;e("ViewRoundToast",(l=h("ViewRoundToast"),u=y(a),l((d=t((f=function(e){function t(){for(var t,i=arguments.length,r=new Array(i),s=0;s<i;s++)r[s]=arguments[s];return t=e.call.apply(e,[this].concat(r))||this,n(t,"lbToast",d,o(t)),t}return i(t,e),t.prototype.setData=function(e){var t=this;this.lbToast.string=e.desc,this.scheduleOnce((function(){t.closeSelf()}),2),this.removeBg()},t}(c)).prototype,"lbToast",[u],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),p=f))||p));r._RF.pop()}}}));

System.register("chunks:///_virtual/ViewSelectGameLevel.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ViewBase.ts","./ScrollViewProCom.ts","./UnitSelectGameLevel.ts","./ConfigHelper.ts","./xcore.ts","./ConstGlobal.ts"],(function(e){var t,n,i,o,c,r,l,s,a,u,f,v,p;return{setters:[function(e){t=e.applyDecoratedDescriptor,n=e.inheritsLoose,i=e.initializerDefineProperty,o=e.assertThisInitialized},function(e){c=e.cclegacy,r=e._decorator,l=e.log},function(e){s=e.ViewBase},function(e){a=e.default},function(e){u=e.UnitSelectGameLevel},function(e){f=e.ConfigHelper},function(e){v=e.xcore},function(e){p=e.E_EVENT}],execute:function(){var g,L,m,h,w;c._RF.push({},"fac0eXSb6JBPZw1Mjj0yGL8","ViewSelectGameLevel",void 0);var S=r.ccclass,d=r.property;e("ViewSelectGameLevel",(g=S("ViewSelectGameLevel"),L=d(a),g((w=t((h=function(e){function t(){for(var t,n=arguments.length,c=new Array(n),r=0;r<n;r++)c[r]=arguments[r];return t=e.call.apply(e,[this].concat(c))||this,i(t,"svLev",w,o(t)),t._configs=void 0,t._selectLevIndex=void 0,t}return n(t,e),t.prototype.start=function(){var e=this;this._configs=f.getInstance().getLevelConfigs(),this.svLev.setView(this._configs,(function(t,n,i){t.getComponent(u).setData(n,i,(function(t){l("selectIndex",t),v.gameData.gameSelectLev=t,e.closeSelf(),v.event.raiseEvent(p.GameConfig)}))}))},t}(s)).prototype,"svLev",[L],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),m=h))||m));c._RF.pop()}}}));

System.register("chunks:///_virtual/ViewSelectGameMode.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ViewBase.ts","./xcore.ts","./ConstGlobal.ts"],(function(e){var t,n,i,o,a,c,r,l,u,s,d,f,b,p,m;return{setters:[function(e){t=e.applyDecoratedDescriptor,n=e.inheritsLoose,i=e.initializerDefineProperty,o=e.assertThisInitialized},function(e){a=e.cclegacy,c=e._decorator,r=e.Button,l=e.director,u=e.log},function(e){s=e.ViewBase},function(e){d=e.xcore},function(e){f=e.E_GameMode,b=e.C_Bundle,p=e.C_Scene,m=e.E_EVENT}],execute:function(){var M,g,h,y,D,w,S,k,B,_,v;a._RF.push({},"f1b89gDe8FFAYMI8QfI/pUT","ViewSelectGameMode",void 0);var E=c.ccclass,G=c.property;e("ViewSelectGameMode",(M=E("ViewSelectGameMode"),g=G(r),h=G(r),y=G(r),D=G(r),M((k=t((S=function(e){function t(){for(var t,n=arguments.length,a=new Array(n),c=0;c<n;c++)a[c]=arguments[c];return t=e.call.apply(e,[this].concat(a))||this,i(t,"btnModeE",k,o(t)),i(t,"btnModeM",B,o(t)),i(t,"btnModeD",_,o(t)),i(t,"btnBack",v,o(t)),t._isBack=!1,t}n(t,e);var a=t.prototype;return a.onLoadCompleted=function(){var e=this;this.btnModeE.node.on("click",(function(){d.gameData.gameMode=f.Easy,e.closeSelf()}),this),this.btnModeM.node.on("click",(function(){d.gameData.gameMode=f.Middle,e.closeSelf()}),this),this.btnModeD.node.on("click",(function(){d.gameData.gameMode=f.Diffecult,e.closeSelf()}),this),this.btnBack.node.on("click",(function(){e._isBack=!0,e.closeSelf()}),this)},a.onDestroyCompleted=function(){"Main"!==l.getScene().name||this._isBack||(u("游戏难度",d.gameData.gameMode),d.gameData.gameType=0,d.ui.switchScene(b.abGame,p.Game)),d.event.raiseEvent(m.GameConfig)},t}(s)).prototype,"btnModeE",[g],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),B=t(S.prototype,"btnModeM",[h],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),_=t(S.prototype,"btnModeD",[y],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),v=t(S.prototype,"btnBack",[D],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),w=S))||w));a._RF.pop()}}}));

System.register("chunks:///_virtual/ViewSelectGameType.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ViewBase.ts","./ScrollViewProCom.ts","./ConfigHelper.ts","./UnitSelectGameType.ts"],(function(e){var t,n,i,o,r,c,a,l,s,u;return{setters:[function(e){t=e.applyDecoratedDescriptor,n=e.inheritsLoose,i=e.initializerDefineProperty,o=e.assertThisInitialized},function(e){r=e.cclegacy,c=e._decorator},function(e){a=e.ViewBase},function(e){l=e.default},function(e){s=e.ConfigHelper},function(e){u=e.UnitSelectGameType}],execute:function(){var p,f,y,g,m;r._RF.push({},"bcb346152ZHkr5KKAwxSum+","ViewSelectGameType",void 0);var w=c.ccclass,v=c.property;e("ViewSelectGameType",(p=w("ViewSelectGameType"),f=v(l),p((m=t((g=function(e){function t(){for(var t,n=arguments.length,r=new Array(n),c=0;c<n;c++)r[c]=arguments[c];return t=e.call.apply(e,[this].concat(r))||this,i(t,"svContent",m,o(t)),t}return n(t,e),t.prototype.onLoadCompleted=function(){var e=s.getInstance().getDungeonConfigs();this.svContent.setView(e,(function(e,t,n){e.getComponent(u).setData(t)}))},t}(a)).prototype,"svContent",[f],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),y=g))||y));r._RF.pop()}}}));

System.register("chunks:///_virtual/ViewSetting.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ViewBase.ts","./xcore.ts","./App.ts"],(function(e){var t,n,i,r,s,o,u,a,l,c,p,d,f;return{setters:[function(e){t=e.applyDecoratedDescriptor,n=e.inheritsLoose,i=e.initializerDefineProperty,r=e.assertThisInitialized},function(e){s=e.cclegacy,o=e._decorator,u=e.Slider,a=e.Sprite,l=e.Label,c=e.Button},function(e){p=e.ViewBase},function(e){d=e.xcore},function(e){f=e.default}],execute:function(){var g,b,h,m,S,D,M,y,w,z,B,v,I,V,x,C,R,L,N;s._RF.push({},"a34dbvDGXxPTaih7bRx60eO","ViewSetting",void 0);var U=o.ccclass,_=o.property;e("ViewSetting",(g=U("ViewSetting"),b=_(u),h=_(a),m=_(l),S=_(u),D=_(a),M=_(c),y=_(l),w=_(l),g((v=t((B=function(e){function t(){for(var t,n=arguments.length,s=new Array(n),o=0;o<n;o++)s[o]=arguments[o];return t=e.call.apply(e,[this].concat(s))||this,i(t,"slSound",v,r(t)),i(t,"sprSoundBar",I,r(t)),i(t,"lbSound",V,r(t)),i(t,"slMusic",x,r(t)),i(t,"sprMusicBar",C,r(t)),i(t,"btnConfirm",R,r(t)),i(t,"lbId",L,r(t)),i(t,"lbMusic",N,r(t)),t}n(t,e);var s=t.prototype;return s.onLoadCompleted=function(){var e=this;d.gameData&&d.gameData.soundData&&(this.slSound.progress=d.gameData.soundData.sound,this.slMusic.progress=d.gameData.soundData.music,this.refreshUI()),this.slSound.node.on("slide",(function(){e.refreshUI()}),this),this.slMusic.node.on("slide",(function(){e.refreshUI()}),this),this.btnConfirm.node.on("click",(function(){e.closeSelf(),d.gameData.soundData||(d.gameData.soundData={}),d.gameData.soundData.sound=e.slSound.progress,d.gameData.soundData.music=e.slMusic.progress,d.sound.setSoundNum(e.slSound.progress),d.sound.setMusicNum(e.slMusic.progress)}),this)},s.refreshUI=function(){this.sprSoundBar.fillRange=this.slSound.progress,this.lbSound.string=Math.floor(100*this.slSound.progress).toString(),this.sprMusicBar.fillRange=this.slMusic.progress,this.lbMusic.string=Math.floor(100*this.slMusic.progress).toString(),d.sound.setSoundNum(this.slSound.progress),d.sound.setMusicNum(this.slMusic.progress)},s.onDestroyCompleted=function(){f.getInstance().updateOperation()},t}(p)).prototype,"slSound",[b],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),I=t(B.prototype,"sprSoundBar",[h],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),V=t(B.prototype,"lbSound",[m],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),x=t(B.prototype,"slMusic",[S],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),C=t(B.prototype,"sprMusicBar",[D],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),R=t(B.prototype,"btnConfirm",[M],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),L=t(B.prototype,"lbId",[y],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),N=t(B.prototype,"lbMusic",[w],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),z=B))||z));s._RF.pop()}}}));

System.register("chunks:///_virtual/ViewSkillUpShow.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ViewBase.ts","./ConstGlobal.ts","./xcore.ts"],(function(e){var t,r,i,n,a,s,l,o,u,c,p,b,f,h,m,k;return{setters:[function(e){t=e.applyDecoratedDescriptor,r=e.inheritsLoose,i=e.initializerDefineProperty,n=e.assertThisInitialized,a=e.asyncToGenerator,s=e.regeneratorRuntime},function(e){l=e.cclegacy,o=e._decorator,u=e.sp,c=e.Sprite,p=e.Label,b=e.size},function(e){f=e.ViewBase},function(e){h=e.C_Bundle,m=e.E_SkillType},function(e){k=e.xcore}],execute:function(){var y,v,w,g,S,_,d,z,D,G,L;l._RF.push({},"5e2d6pITJNNpZs80Kf/c2mt","ViewSkillUpShow",void 0);var x=o.ccclass,V=o.property;e("ViewSkillUpShow",(y=x("ViewSkillUpShow"),v=V(u.Skeleton),w=V(c),g=V(p),S=V(p),y((z=t((d=function(e){function t(){for(var t,r=arguments.length,a=new Array(r),s=0;s<r;s++)a[s]=arguments[s];return t=e.call.apply(e,[this].concat(a))||this,i(t,"anim",z,n(t)),i(t,"sprAvatar",D,n(t)),i(t,"lbName",G,n(t)),i(t,"lbDesc",L,n(t)),t._cb=null,t._time=5,t}r(t,e);var l=t.prototype;return l.setData=function(){var e=a(s().mark((function e(t){var r,i,n;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:r=t.message,e.t0=r.skillType,e.next=e.t0===m.GreatFire?4:e.t0===m.GreatLightning?7:e.t0===m.GreatRock?10:e.t0===m.GreatSkyRock?13:e.t0===m.GreatFires?16:19;break;case 4:return i="朱雀扇",n="./res/anim/skilleffect/zhuque",e.abrupt("break",20);case 7:return i="五雷钉",n="./res/anim/skilleffect/leilingzhu",e.abrupt("break",20);case 10:return i="混元伞",n="./res/anim/skilleffect/hunyuansan",e.abrupt("break",20);case 13:return i="女娲石",n="./res/anim/skilleffect/lvwashi",e.abrupt("break",20);case 16:return i="神火炉",n="./res/anim/skilleffect/bagualu",e.abrupt("break",20);case 19:return e.abrupt("break",20);case 20:return i=i+"(Lv."+r.lev+")",this.lbDesc.string=i,this.lbName.string=r.name||"匿名用户",r.avatar&&k.res.remoteLoadSprite(r.avatar,this.sprAvatar,b(100,100)),e.next=26,k.res.bundleLoadSpine(h.abGame,n,this.anim);case 26:this.anim.setAnimation(0,"animation"),this._cb=t.cb,this._time=2.3;case 29:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),l.onDestroyCompleted=function(){this._cb&&this._cb()},l.update=function(e){this._time-=e,this._time<=0&&this.closeSelf()},t}(f)).prototype,"anim",[v],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),D=t(d.prototype,"sprAvatar",[w],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),G=t(d.prototype,"lbName",[g],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),L=t(d.prototype,"lbDesc",[S],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),_=d))||_));l._RF.pop()}}}));

System.register("chunks:///_virtual/ViewSkinDebrisReward.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ViewBase.ts","./ConfigHelper.ts","./xcore.ts","./Tool.ts","./UnitSkinDebrisReward.ts","./ConstGlobal.ts"],(function(e){var t,i,n,r,o,a,s,l,c,u,g,f,b,d,h,y,p,w;return{setters:[function(e){t=e.applyDecoratedDescriptor,i=e.inheritsLoose,n=e.initializerDefineProperty,r=e.assertThisInitialized},function(e){o=e.cclegacy,a=e._decorator,s=e.Label,l=e.Prefab,c=e.Node,u=e.Button,g=e.log,f=e.instantiate},function(e){b=e.ViewBase},function(e){d=e.ConfigHelper},function(e){h=e.xcore},function(e){y=e.default},function(e){p=e.UnitSkinDebrisReward},function(e){w=e.C_View}],execute:function(){var m,v,D,C,L,k,S,_,B,I,R,x,V;o._RF.push({},"4e8c8c0h/NFoa50ZgeyZjaZ","ViewSkinDebrisReward",void 0);var T=a.ccclass,z=a.property;e("ViewSkinDebrisReward",(m=T("ViewSkinDebrisReward"),v=z(s),D=z(l),C=z(s),L=z(c),k=z(u),m((B=t((_=function(e){function t(){for(var t,i=arguments.length,o=new Array(i),a=0;a<i;a++)o[a]=arguments[a];return t=e.call.apply(e,[this].concat(o))||this,n(t,"lbDesc",B,r(t)),n(t,"pfbUnitSkinDebrisReward",I,r(t)),n(t,"lbBtnTxt",R,r(t)),n(t,"ndContent",x,r(t)),n(t,"btnClose2",V,r(t)),t._time=21,t._cb=null,t}i(t,e);var o=t.prototype;return o.onLoadCompleted=function(){var e=this;this.btnClose2.node.on("click",(function(){var t;h.ui.closeView(w.ViewSkinDebrisReward,!0),null==(t=e.node)||t.destroy()}),this)},o.setData=function(e){var t=e.id,i=e.freeid;this._cb=e.cb;var n=d.getInstance().getConstantConfigByKey("lotteryScore")||200,r=d.getInstance().getConstantConfigByKey("freeLotteryScore")||100,o=d.getInstance().getLotteryConfigByJsonId(t);this.lbDesc.string=o[["easyText","normaText","hardText"][h.gameData.gameMode]];var a=[3,2,1,1];e.isPassLottery&&(a=[1,1,1,1]);for(var s=0;s<e.users.length;s++){var l=e.users[s],c=a[s]||1,u=l[1].giftscore,b=l[1].score,w=[],m=void 0;if(u>=n){var v=d.getInstance().getLotteryDebrisConfigsByJsonId(t);m=t;for(var D=0;D<v.length;D++){var C=v[D],L=[C.easyLotteryWeight,C.normalLotteryWeight,C.hardLotteryWeight][h.gameData.gameMode];w.push({lotteryid:t,id:C.skinFragmentId,weight:L})}}else{if(!(b>=r)){g("积分不足",u,b);continue}var k=d.getInstance().getLotteryDebrisConfigsByJsonId(i);m=i;for(var S=0;S<k.length;S++){var _=k[S],B=[_.easyLotteryWeight,_.normalLotteryWeight,_.hardLotteryWeight][h.gameData.gameMode];w.push({lotteryid:i,id:_.skinFragmentId,weight:B})}}g("碎片：",u,b,n,r,w,t,i);for(var I=[],R=0;R<c;R++){var x=y.selectArrayIdByWeight(w);I.push(x)}var V=f(this.pfbUnitSkinDebrisReward);V.setParent(this.ndContent),V.getComponent(p).setData({targetId:m,user:l,ids:I},s)}},o.update=function(e){this._time>0&&(this._time-=e,this.lbBtnTxt.string="继续关卡("+Math.floor(this._time)+")",this._time<=0&&this.closeSelf())},o.onDestroyCompleted=function(){this._cb&&this._cb()},t}(b)).prototype,"lbDesc",[v],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),I=t(_.prototype,"pfbUnitSkinDebrisReward",[D],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),R=t(_.prototype,"lbBtnTxt",[C],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),x=t(_.prototype,"ndContent",[L],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),V=t(_.prototype,"btnClose2",[k],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),S=_))||S));o._RF.pop()}}}));

System.register("chunks:///_virtual/ViewSkinDetail.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ViewBase.ts","./ConfigHelper.ts","./xcore.ts"],(function(e){var t,i,n,r,a,l,o,s,u,c,p,f,b,g,k,d;return{setters:[function(e){t=e.applyDecoratedDescriptor,i=e.inheritsLoose,n=e.initializerDefineProperty,r=e.assertThisInitialized},function(e){a=e.cclegacy,l=e._decorator,o=e.SpriteFrame,s=e.Sprite,u=e.Label,c=e.Node,p=e.size,f=e.log,b=e.instantiate},function(e){g=e.ViewBase},function(e){k=e.ConfigHelper},function(e){d=e.xcore}],execute:function(){var h,m,y,S,w,v,B,z,C,I,D,F,_,L,N,O,P,V,A,x,H,J,K,M,R,j,q;a._RF.push({},"5e045F9MKFAB5k8gWnjDE0y","ViewSkinDetail",void 0);var E=l.ccclass,T=l.property;e("ViewSkinDetail",(h=E("ViewSkinDetail"),m=T([o]),y=T([o]),S=T(s),w=T(s),v=T(s),B=T(u),z=T(u),C=T(u),I=T(u),D=T(u),F=T(c),_=T(c),h((O=t((N=function(e){function t(){for(var t,i=arguments.length,a=new Array(i),l=0;l<i;l++)a[l]=arguments[l];return t=e.call.apply(e,[this].concat(a))||this,n(t,"sfFrames",O,r(t)),n(t,"sfOns",P,r(t)),n(t,"sprBg",V,r(t)),n(t,"sprOn",A,r(t)),n(t,"sprSkin",x,r(t)),n(t,"lbSkinName",H,r(t)),n(t,"lbAtk",J,r(t)),n(t,"lbSpeed",K,r(t)),n(t,"lbSkill",M,r(t)),n(t,"lbProp",R,r(t)),n(t,"pfbBtn",j,r(t)),n(t,"ndBtnContent",q,r(t)),t._data=null,t}i(t,e);var a=t.prototype;return a.onLoadCompleted=function(){},a.setData=function(e){var t=this;this._data=e,this.refreshData(1);for(var i=k.getInstance().getSkinMaxLevelBySkinId(this._data.skinId),n=function(e){var i=b(t.pfbBtn);i.getChildByName("Label").getComponent(u).string="等级"+(e+1),i.parent=t.ndBtnContent,i.on("click",(function(){t.refreshData(e+1)}),t)},r=0;r<i;r++)n(r)},a.refreshData=function(e){var t=k.getInstance().getSkinLevelConfigBySkinId(this._data.skinId,e);if(t){var i=k.getInstance().getSkinConfigByJsonId(this._data.skinId);this.lbSkinName.string="Lv."+e+" "+this._data.skinName;var n=t.attack.split("|");this.lbAtk.string="攻击力："+(n[0]==n[1]?n[0]:n[0]+"~"+n[1]),this.lbSpeed.string="攻速："+t.attackCooldown,this.sprBg.spriteFrame=this.sfFrames[i.quality-1],this.sprOn.spriteFrame=this.sfOns[i.quality-1],d.res.bundleLoadSprite("resources","./res/image/"+i.path+"/"+i.skinRole,this.sprSkin,p(200,290)),this.lbSkill.node.active=!!t.skill,this.lbProp.node.active=!!t.attacKeffect;var r=k.getInstance().getEffectConfigByJsonId(t.attacKeffect),a=k.getInstance().getSkillConfigByJsonId(t.skill);a&&(this.lbSkill.string=a.name+":"+a.skillDescribe),r&&(this.lbProp.string=r.name+":"+r.describe)}f("skinConfig:",t)},t}(g)).prototype,"sfFrames",[m],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return[]}}),P=t(N.prototype,"sfOns",[y],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return[]}}),V=t(N.prototype,"sprBg",[S],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),A=t(N.prototype,"sprOn",[w],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),x=t(N.prototype,"sprSkin",[v],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),H=t(N.prototype,"lbSkinName",[B],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),J=t(N.prototype,"lbAtk",[z],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),K=t(N.prototype,"lbSpeed",[C],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),M=t(N.prototype,"lbSkill",[I],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),R=t(N.prototype,"lbProp",[D],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),j=t(N.prototype,"pfbBtn",[F],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),q=t(N.prototype,"ndBtnContent",[_],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),L=N))||L));a._RF.pop()}}}));

System.register("chunks:///_virtual/ViewSkinReward.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ViewBase.ts","./ConfigHelper.ts","./xcore.ts","./Tool.ts","./ConstGlobal.ts"],(function(e){var n,t,r,i,a,o,l,s,u,c,p,f,m,b,d,g,h,w,k,y,C;return{setters:[function(e){n=e.applyDecoratedDescriptor,t=e.inheritsLoose,r=e.initializerDefineProperty,i=e.assertThisInitialized,a=e.asyncToGenerator,o=e.regeneratorRuntime},function(e){l=e.cclegacy,s=e._decorator,u=e.sp,c=e.Sprite,p=e.Animation,f=e.Label,m=e.UITransform,b=e.log,d=e.size,g=e.v3},function(e){h=e.ViewBase},function(e){w=e.ConfigHelper},function(e){k=e.xcore},function(e){y=e.default},function(e){C=e.C_Bundle}],execute:function(){var v,P,S,A,x,B,_,z,I,L,N,R,V,X,Y;l._RF.push({},"c2b2be7eWlHjbxO/JHn4WXA","ViewSkinReward",void 0);var H=s.ccclass,U=s.property;e("ViewSkinReward",(v=H("ViewSkinReward"),P=U(u.Skeleton),S=U(c),A=U(p),x=U(c),B=U(f),_=U(f),v((L=n((I=function(e){function n(){for(var n,t=arguments.length,a=new Array(t),o=0;o<t;o++)a[o]=arguments[o];return n=e.call.apply(e,[this].concat(a))||this,r(n,"anim",L,i(n)),r(n,"sprBg",N,i(n)),r(n,"role",R,i(n)),r(n,"sprAvatar",V,i(n)),r(n,"lbUserName",X,i(n)),r(n,"lbName",Y,i(n)),n}return t(n,e),n.prototype.setData=function(){var e=a(o().mark((function e(n){var t,r,i,a,l,s,u,c,p,f,h,v,P=this;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:this.removeBg(),b("debris reward:",n.skinId),r=w.getInstance().getSkinConfigByJsonId(n.skinId),i=w.getInstance().getAnimConfigByJsonId(r.moveAnimation),a=r.quality||1,l="./res/anim/skinshow/"+a+"/tianhu",k.res.bundleLoadSpine(C.abGame,l,this.anim).then((function(){P.anim.setAnimation(0,"attack_1",!0)})),k.res.bundleLoadSprite(C.abGame,"./res/img_unpack/game_skinreward_bg0"+a,this.sprBg),this.lbUserName.string=n.user.nickName,k.res.remoteLoadSprite(n.user.iconUrl,this.sprAvatar,d(80,80)),this.lbName.string=r.name,s={sample:i.sample,duration:i.duration,speed:1,wrapMode:i.wrapMode,path:i.path,name:i.name},u=s.name.split("-")[0]||"default",c=k.res.getAtlas(u),p=0;case 15:if(!(p<s.sample)){e.next=26;break}if(f=s.name+"_"+(p<10?"0"+p:p),c.getSpriteFrame(f)){e.next=23;break}return e.next=21,k.res.bundleLoadSprite("resources","./res/image/"+s.path+"/"+f);case 21:h=e.sent,k.res.addAtlasSprite(u,f,h);case 23:p++,e.next=15;break;case 26:return null==i.XCenterPoint||null==i.XCenterPoint?i.XCenterPoint=.5:""==i.XCenterPoint&&(i.XCenterPoint=0),null==i.YCenterPoint||null==i.YCenterPoint?i.YCenterPoint=.5:""==i.YCenterPoint&&(i.YCenterPoint=0),e.next=30,y.createAnim(this.role,s,c);case 30:if(v=null==(t=this.role)||null==(t=t.node)?void 0:t.getComponent(m)){e.next=33;break}return e.abrupt("return");case 33:this.scheduleOnce((function(){if(v){var e=600/v.height;v.node.scale=g(e,e)}})),null==v||v.setAnchorPoint(i.XCenterPoint,i.YCenterPoint),this.scheduleOnce((function(){P.node&&P.node.isValid&&P.closeSelf()}),3);case 36:case"end":return e.stop()}}),e,this)})));return function(n){return e.apply(this,arguments)}}(),n}(h)).prototype,"anim",[P],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),N=n(I.prototype,"sprBg",[S],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),R=n(I.prototype,"role",[A],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),V=n(I.prototype,"sprAvatar",[x],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),X=n(I.prototype,"lbUserName",[B],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Y=n(I.prototype,"lbName",[_],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),z=I))||z));l._RF.pop()}}}));

System.register("chunks:///_virtual/ViewSkinSelect.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ViewBase.ts","./ScrollViewProCom.ts","./ConfigHelper.ts","./UnitSelectSkin.ts","./Net.ts","./xcore.ts","./UnitItemScore.ts"],(function(t){var e,n,i,r,o,a,s,c,l,u,f,h,p,g,b,k,m,d,v,S;return{setters:[function(t){e=t.applyDecoratedDescriptor,n=t.inheritsLoose,i=t.initializerDefineProperty,r=t.assertThisInitialized,o=t.asyncToGenerator,a=t.regeneratorRuntime},function(t){s=t.cclegacy,c=t._decorator,l=t.PageView,u=t.Label,f=t.Prefab,h=t.Button,p=t.instantiate},function(t){g=t.ViewBase},function(t){b=t.default},function(t){k=t.ConfigHelper},function(t){m=t.UnitSelectSkin},function(t){d=t.default},function(t){v=t.xcore},function(t){S=t.UnitItemScore}],execute:function(){var w,y,N,_,R,A,D,I,V,C,P,z,x,T,B,L,U,E,H,M,F;s._RF.push({},"dde78natMpDi40gnSp4dAoo","ViewSkinSelect",void 0);var j=c.ccclass,G=c.property;t("ViewSkinSelect",(w=j("ViewSkinSelect"),y=G(l),N=G(u),_=G(f),R=G(h),A=G(h),D=G(u),I=G(u),V=G(u),C=G({type:b,displayName:"垂直布局列表"}),w((x=e((z=function(t){function e(){for(var e,n=arguments.length,o=new Array(n),a=0;a<n;a++)o[a]=arguments[a];return e=t.call.apply(t,[this].concat(o))||this,i(e,"pvSkin",x,r(e)),i(e,"lbSkinName",T,r(e)),i(e,"pfbUnitSkin",B,r(e)),i(e,"btnArr01",L,r(e)),i(e,"btnArr02",U,r(e)),i(e,"lbAtkNum",E,r(e)),i(e,"lbEachatkNum",H,r(e)),i(e,"lbNeedScore",M,r(e)),e._isRankLoading=!1,e._selectSkinNum=0,e._itemNum=0,e._configs=[],i(e,"scrollviewV",F,r(e)),e}n(e,t);var s=e.prototype;return s.onLoadCompleted=function(){},s.start=function(){var t=o(a().mark((function t(){var e,n,i=this;return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:for(this._configs=k.getInstance().getSkinConfgs(),e=0;e<this._configs.length;e++)(n=p(this.pfbUnitSkin)).getComponent(m).setData(this._configs[e]),this.pvSkin.addPage(n);this.btnArr01.node.active=!1,this.addButtonEvent(this.btnArr01,(function(){i.pageTo(-1)}),this),this.addButtonEvent(this.btnArr02,(function(){i.pageTo(1)}),this),this.pvSkin.node.on("page-turning",this.onRefreshPageview.bind(this,!0),this),this.scrollviewV.node.on("scroll-to-bottom",this.onRefreshData,this),this.onRefreshPageview(!0);case 8:case"end":return t.stop()}}),t,this)})));return function(){return t.apply(this,arguments)}}(),s.onRefreshData=function(){var t=o(a().mark((function t(e){var n,i,r;return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return v.gameData.scoreRankInfo||(v.gameData.scoreRankInfo=[]),this._isRankLoading=!0,n=k.getInstance().getRankKeyByMonth(),t.next=5,d.getRankInfo(n,v.gameData.scoreRankInfo.length,v.gameData.scoreRankInfo.length+10);case 5:if(e||v.gameData.scoreRankInfo.length!=this._itemNum){t.next=8;break}return v.ui.showToast("到底啦"),t.abrupt("return");case 8:this._itemNum=v.gameData.scoreRankInfo.length,i=this._configs[this._selectSkinNum].openCondition,r=v.gameData.scoreRankInfo.filter((function(t){return t.score>=i})),this.scrollviewV.setView(r,(function(t,e){t.getComponent(S).setData(e)})),this._isRankLoading=!1;case 13:case"end":return t.stop()}}),t,this)})));return function(e){return t.apply(this,arguments)}}(),s.onRefreshPageview=function(t){void 0===t&&(t=!1),this._selectSkinNum=this.pvSkin.getCurrentPageIndex();var e=this._configs[this._selectSkinNum];this.lbSkinName.string=e.name;var n=e.attack.split("|");this.lbAtkNum.string=n[0]==n[1]?n[0]:n[0]+"~"+n[1],this.lbEachatkNum.string=e.attackCooldown+"秒",this.lbNeedScore.string=e.openCondition>0?"周"+e.openCondition+"积分可使用":"默认皮肤",0==this._selectSkinNum?(this.btnArr01.node.active=!1,this.btnArr02.node.active=!0):this._selectSkinNum==this.pvSkin.content.children.length-1?(this.btnArr01.node.active=!0,this.btnArr02.node.active=!1):(this.btnArr01.node.active=!0,this.btnArr02.node.active=!0),this.onRefreshData(t)},s.pageTo=function(t){var e=this.pvSkin.getCurrentPageIndex()+t;this.pvSkin.scrollToPage(e),this.scrollviewV.scrollToTop(.1)},e}(g)).prototype,"pvSkin",[y],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),T=e(z.prototype,"lbSkinName",[N],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),B=e(z.prototype,"pfbUnitSkin",[_],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),L=e(z.prototype,"btnArr01",[R],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),U=e(z.prototype,"btnArr02",[A],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),E=e(z.prototype,"lbAtkNum",[D],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),H=e(z.prototype,"lbEachatkNum",[I],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),M=e(z.prototype,"lbNeedScore",[V],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),F=e(z.prototype,"scrollviewV",[C],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),P=z))||P));s._RF.pop()}}}));

System.register("chunks:///_virtual/ViewSkinShow.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ViewBase.ts","./BaseLlistCtr.ts","./ConfigHelper.ts","./FightMgr.ts"],(function(t){var e,i,n,r,o,l,s,a,c,u,h,f,b,p;return{setters:[function(t){e=t.applyDecoratedDescriptor,i=t.inheritsLoose,n=t.initializerDefineProperty,r=t.assertThisInitialized},function(t){o=t.cclegacy,l=t._decorator,s=t.Node,a=t.Button,c=t.Label,u=t.Color},function(t){h=t.ViewBase},function(t){f=t.default},function(t){b=t.ConfigHelper},function(t){p=t.FightMgr}],execute:function(){var d,w,S,g,y,_,x,k,m,v,I,z,O,T,B,L,U,P,V,C,R;o._RF.push({},"3d6dczHIV5GTrJmaqMJGaFs","ViewSkinShow",void 0);var D=l.ccclass,F=l.property;t("ViewSkinShow",(d=D("ViewSkinShow"),w=F(f),S=F(f),g=F(s),y=F(a),_=F(c),x=F(s),k=F(a),m=F(c),v=F(s),d((O=e((z=function(t){function e(){for(var e,i=arguments.length,o=new Array(i),l=0;l<i;l++)o[l]=arguments[l];return e=t.call.apply(t,[this].concat(o))||this,n(e,"listShow",O,r(e)),n(e,"listPlayer",T,r(e)),n(e,"ndEmpty",B,r(e)),n(e,"btnShow",L,r(e)),n(e,"lbBtnShowTxt",U,r(e)),n(e,"ndShowOn",P,r(e)),n(e,"btnUserSkin",V,r(e)),n(e,"lbBtnUserSkinTxt",C,r(e)),n(e,"ndUserSkinOn",R,r(e)),e._seletIndex=null,e._configs=[],e._colorOn=new u(210,210,200,255),e._colorOff=new u(175,60,60,255),e}i(e,t);var o=e.prototype;return o.onLoadCompleted=function(){this._configs=b.getInstance().getSkinConfgs(),this._configs.sort((function(t,e){return e.sort-t.sort})),this.onRefreshList(1),this.btnShow.node.on("click",this.onRefreshList.bind(this,1),this),this.btnUserSkin.node.on("click",this.onRefreshList.bind(this,2),this)},o.onRefreshList=function(t){if(this._seletIndex!=t)if(this._seletIndex=t,this.listShow.node.active=1==this._seletIndex,this.listPlayer.node.active=2==this._seletIndex,this.ndShowOn.active=1==this._seletIndex,this.ndUserSkinOn.active=2==this._seletIndex,this.lbBtnShowTxt.color=1==this._seletIndex?this._colorOn:this._colorOff,this.lbBtnUserSkinTxt.color=1==this._seletIndex?this._colorOff:this._colorOn,this.ndEmpty.active=!1,1==this._seletIndex)this.listShow.refreshListData(this._configs),this.listShow.sv.scrollToTop(.1);else if(2==this._seletIndex){var e=p.getInstance().getUsers();this.ndEmpty.active=e.length<=0,this.listPlayer.refreshListData(e),this.listPlayer.sv.scrollToTop(.1)}},o.start=function(){},o.update=function(t){},e}(h)).prototype,"listShow",[w],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),T=e(z.prototype,"listPlayer",[S],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),B=e(z.prototype,"ndEmpty",[g],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),L=e(z.prototype,"btnShow",[y],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),U=e(z.prototype,"lbBtnShowTxt",[_],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),P=e(z.prototype,"ndShowOn",[x],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),V=e(z.prototype,"btnUserSkin",[k],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),C=e(z.prototype,"lbBtnUserSkinTxt",[m],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),R=e(z.prototype,"ndUserSkinOn",[v],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),I=z))||I));o._RF.pop()}}}));

System.register("chunks:///_virtual/ViewTaskRewardDesc.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ViewBase.ts","./ConfigHelper.ts"],(function(e){var n,t,i,r,a,s,l,o,c;return{setters:[function(e){n=e.applyDecoratedDescriptor,t=e.inheritsLoose,i=e.initializerDefineProperty,r=e.assertThisInitialized},function(e){a=e.cclegacy,s=e._decorator,l=e.Label},function(e){o=e.ViewBase},function(e){c=e.ConfigHelper}],execute:function(){var u,p,b,f,g,y,k,T,w,h,D;a._RF.push({},"ac1bey2sgxPB7QUcRVGV3Xn","ViewTaskRewardDesc",void 0);var d=s.ccclass,m=s.property;e("ViewTaskRewardDesc",(u=d("ViewTaskRewardDesc"),p=m(l),b=m(l),f=m(l),g=m(l),u((T=n((k=function(e){function n(){for(var n,t=arguments.length,a=new Array(t),s=0;s<t;s++)a[s]=arguments[s];return n=e.call.apply(e,[this].concat(a))||this,i(n,"lbDesc",T,r(n)),i(n,"lbDayTask",w,r(n)),i(n,"lbWeekTask",h,r(n)),i(n,"lbMonthTask",D,r(n)),n}return t(n,e),n.prototype.onLoadCompleted=function(){var e=c.getInstance().getTaskConfigByType(1),n=c.getInstance().getTaskConfigByType(2),t=c.getInstance().getTaskConfigByType(3);this.lbDayTask.string=e.name,this.lbWeekTask.string=n.name,this.lbMonthTask.string=t.name},n}(o)).prototype,"lbDesc",[p],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),w=n(k.prototype,"lbDayTask",[b],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),h=n(k.prototype,"lbWeekTask",[f],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),D=n(k.prototype,"lbMonthTask",[g],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),y=k))||y));a._RF.pop()}}}));

System.register("chunks:///_virtual/ViewTestGift.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ViewBase.ts","./ConstGlobal.ts","./xcore.ts","./Tool.ts","./ConfigHelper.ts"],(function(n){var e,t,i,r,o,a,s,l,u,c,f,h,g,d,p,b,k,y;return{setters:[function(n){e=n.applyDecoratedDescriptor,t=n.inheritsLoose,i=n.initializerDefineProperty,r=n.assertThisInitialized},function(n){o=n.cclegacy,a=n._decorator,s=n.Node,l=n.Button,u=n.Label,c=n.Toggle,f=n.instantiate,h=n.log},function(n){g=n.ViewBase},function(n){d=n.E_EVENT,p=n.C_GiftKey},function(n){b=n.xcore},function(n){k=n.default},function(n){y=n.ConfigHelper}],execute:function(){var S,v,m,_,C,B,I,U,w,G,T,M,D,N,x,z,E;o._RF.push({},"d81a4c+NrRPHavj7IWxdY0B","ViewTestGift",void 0);var R=a.ccclass,L=a.property;n("ViewTestGift",(S=R("ViewTestGift"),v=L(s),m=L(s),_=L(l),C=L(u),B=L(c),I=L(s),U=L(s),S((T=e((G=function(n){function e(){for(var e,t=arguments.length,o=new Array(t),a=0;a<t;a++)o[a]=arguments[a];return e=n.call.apply(n,[this].concat(o))||this,i(e,"ndBtn",T,r(e)),i(e,"ndBtnUser",M,r(e)),i(e,"btnRandomTest",D,r(e)),i(e,"lbDesc",N,r(e)),i(e,"togSelectGroup",x,r(e)),i(e,"ndContent",z,r(e)),i(e,"ndContentUser",E,r(e)),e._userId=void 0,e._group=void 0,e._heroUser=new Map,e._giftMgr=void 0,e}t(e,n);var o=e.prototype;return o.setData=function(n){this._giftMgr=n.giftMgr,this._heroUser=n.heroUser},o.start=function(){var n=this;this.addEventListener(d.BaseInfo,this.onRefreshData.bind(this));for(var e=["a01","a02","a03","a04","a05","a06","a07","a08","a09","a10","a11","a12","a13","a14","a15","a16","a17","a18","a19","a20"],t=0;t<e.length;t++){var i=e[t],r=f(this.ndBtnUser);r.parent=this.ndContentUser,r.getChildByName("Label").getComponent(u).string=i,r.on("click",this.onSelectUser.bind(this,i,r),this)}for(var o in p)if(Object.prototype.hasOwnProperty.call(p,o)){var a=p[o];if(a==p.Skin1||a==p.Skin2||a==p.Skin3||a==p.Skin4||a==p.Skin5||a==p.Skin6||a==p.Skin7||a==p.Skin8||a==p.Skin9||a==p.Skin10||a==p.Skin11||a==p.Skin12||a==p.Skin13||a==p.Skin14||a==p.Skin15||a==p.Skin16||a==p.Skin17||a==p.Skin18||a==p.Skin19||a==p.Skin20||a==p.Skin21||a==p.Skin22||a==p.Skin23||a==p.Skin24||a==p.Skin25){var s=y.getInstance().getSkinConfigByJsonId(a),l=f(this.ndBtn);l.parent=this.ndContent,l.getChildByName("Label").getComponent(u).string=s.barrage,l.on("click",this.onGiftKey.bind(this,s.barrage),this)}else if(a!=p.JoinHero&&a!=p.JoinMonster){var c=f(this.ndBtn);c.parent=this.ndContent,c.getChildByName("Label").getComponent(u).string=a,c.on("click",this.onGiftKey.bind(this,a),this)}}this.btnRandomTest.node.on("click",(function(){for(var e=k.randomNumber(4,10),t=0;t<e;t++){var i=k.guid(),r=["1","1","1","1","1","1","1","1","1","1","1","1","1","1","1","1","2","2","2","2","2","2","2","2","2","2","2","2","2","2","300001","300001","300001","300001","300001","300001",,"300001","300001","300001","300001","300001","300002","300002","300002","300002","300002","300002","300002","300002","300003","300003","300003","300003","300003","300004","300004","300004","300005","300005","300006","300006","300006","300006","300006","300006","300006","300006","300006","300006","300006","300006","300006","300006","300006","300006","300006","300006","300006","300006","300006","300006","300006","300006","300006","300007","300008"];r=k.randomSortArray(r);for(var o=0;o<3;o++){var a=r[o];n._giftMgr.addGifts([{userId:i,key:a,num:k.randomNumber(1,20)}])}}n.closeSelf()}),this)},o.onRefreshData=function(n){this._heroUser=n.heroUserInfo,this._userId&&this.onSelectUser(this._userId)},o.onGiftKey=function(n){if(h(n),this._userId){var e;if(n==p.ExchangeBossFight){for(var t=[],i=y.getInstance().getDungeonConfigs(),r=0;r<i.length;r++){var o=i[r];t.push(o.name)}e=t[Math.floor(Math.random()*t.length)]}else if(n==p.ExchangeWing){for(var a=[],s=y.getInstance().getExchangeConfigs(),l=0;l<s.length;l++){var u=s[l];"470001"==u.exchangeTypeId&&a.push(u.name)}e=a[Math.floor(Math.random()*a.length)]}this._giftMgr.addGifts([{userId:this._userId,key:n,num:1,content:e}])}else b.ui.showToast("请先选择用户")},o.onSelectUser=function(n,e){var t=this;e&&(this.ndContentUser.children.forEach((function(n){n.getChildByName("tagOn").active=!1})),e.getChildByName("tagOn").active=!0),this._userId=n;var i=this.findUser(n);this._group=i?i.type:null,this.lbDesc.string="userId:"+this._userId+"\ngourp:"+this._group+"\n",i&&i.giftKey.forEach((function(n){t.lbDesc.string+=n.key+"x"+n.num+" "}))},o.findUser=function(n,e){return this._heroUser.get(n)},e}(g)).prototype,"ndBtn",[v],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),M=e(G.prototype,"ndBtnUser",[m],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),D=e(G.prototype,"btnRandomTest",[_],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),N=e(G.prototype,"lbDesc",[C],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),x=e(G.prototype,"togSelectGroup",[B],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),z=e(G.prototype,"ndContent",[I],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),E=e(G.prototype,"ndContentUser",[U],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),w=G))||w));o._RF.pop()}}}));

System.register("chunks:///_virtual/ViewTowerLevUp.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ViewBase.ts"],(function(e){var r,t,i,n,o,l,a,c;return{setters:[function(e){r=e.applyDecoratedDescriptor,t=e.inheritsLoose,i=e.initializerDefineProperty,n=e.assertThisInitialized},function(e){o=e.cclegacy,l=e._decorator,a=e.Label},function(e){c=e.ViewBase}],execute:function(){var s,u,p,f,v,b,h;o._RF.push({},"d038eGxF8JIZIOukM4WgrrH","ViewTowerLevUp",void 0);var w=l.ccclass,g=l.property;e("ViewTowerLevUp",(s=w("ViewTowerLevUp"),u=g(a),p=g(a),s((b=r((v=function(e){function r(){for(var r,t=arguments.length,o=new Array(t),l=0;l<t;l++)o[l]=arguments[l];return r=e.call.apply(e,[this].concat(o))||this,i(r,"lbLev",b,n(r)),i(r,"lbHp",h,n(r)),r}return t(r,e),r.prototype.setData=function(e){var r=this;this.lbLev.string="等级提升到"+e.level,this.lbHp.string="血量上限+"+e.hp,this.scheduleOnce((function(){r.closeSelf()}),1.5),this.removeBg()},r}(c)).prototype,"lbLev",[u],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),h=r(v.prototype,"lbHp",[p],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),f=v))||f));o._RF.pop()}}}));

System.register("chunks:///_virtual/ViewUserInfo.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ViewBase.ts","./ConfigHelper.ts","./Tool.ts","./UnitSkinShow.ts","./UnitDimondDesc.ts","./xcore.ts","./FightMgr.ts"],(function(t){var e,i,n,r,o,a,l,s,d,c,u,f,h,b,p,m,g,k,_,S,v;return{setters:[function(t){e=t.applyDecoratedDescriptor,i=t.inheritsLoose,n=t.initializerDefineProperty,r=t.assertThisInitialized},function(t){o=t.cclegacy,a=t._decorator,l=t.Prefab,s=t.Node,d=t.Label,c=t.Sprite,u=t.Button,f=t.instantiate,h=t.size,b=t.Color},function(t){p=t.ViewBase},function(t){m=t.ConfigHelper},function(t){g=t.default},function(t){k=t.UnitSkinShow},function(t){_=t.UnitDimondDesc},function(t){S=t.xcore},function(t){v=t.FightMgr}],execute:function(){var w,D,y,x,B,z,E,C,U,N,A,L,T,I,H,M,R,V,F,O,P,j,G,J,K,W,X,Z,q,Q,Y,$,tt,et,it,nt,rt,ot,at,lt,st,dt,ct;o._RF.push({},"ee2e0X5HCZH0Kb1klG6WfpJ","ViewUserInfo",void 0);var ut=a.ccclass,ft=a.property;t("ViewUserInfo",(w=ut("ViewUserInfo"),D=ft(l),y=ft(l),x=ft(s),B=ft(s),z=ft(s),E=ft(s),C=ft(s),U=ft(s),N=ft(d),A=ft(d),L=ft(c),T=ft(d),I=ft(d),H=ft(d),M=ft(d),R=ft(d),V=ft(d),F=ft(u),O=ft(u),P=ft(u),w((J=e((G=function(t){function e(){for(var e,i=arguments.length,o=new Array(i),a=0;a<i;a++)o[a]=arguments[a];return e=t.call.apply(t,[this].concat(o))||this,n(e,"pfbUnitSkinShow",J,r(e)),n(e,"pfbUnitDimondDesc",K,r(e)),n(e,"ndSv",W,r(e)),n(e,"ndBaseInfo",X,r(e)),n(e,"ndDimond",Z,r(e)),n(e,"ndDimondDesc",q,r(e)),n(e,"ndUser",Q,r(e)),n(e,"ndList",Y,r(e)),n(e,"lbUserName",$,r(e)),n(e,"lbSkinName",tt,r(e)),n(e,"sprAvatar",et,r(e)),n(e,"lbAtk",it,r(e)),n(e,"lbAtkSpeed",nt,r(e)),n(e,"lbBaoHurtRat",rt,r(e)),n(e,"lbBaoAtkRat",ot,r(e)),n(e,"lbSkillCd",at,r(e)),n(e,"lbHp",lt,r(e)),n(e,"btnTagSkin",st,r(e)),n(e,"btnTagDimond",dt,r(e)),n(e,"btnTagBaseinfo",ct,r(e)),e._role=null,e._index=null,e}i(e,t);var o=e.prototype;return o.setData=function(t){this._role=t.role,this.refreshSkins(),this.btnTagSkin.node.on("click",this.btnSelect.bind(this,0),this),this.btnTagDimond.node.on("click",this.btnSelect.bind(this,1),this),this.btnTagBaseinfo.node.on("click",this.btnSelect.bind(this,2),this),this.btnSelect(t.index)},o.refreshSkins=function(){var t=this,e=m.getInstance().getSkinConfgs();e.sort((function(t,e){return e.sort-t.sort})),g.asyncModifyChildren(this,this.ndList,this.pfbUnitSkinShow,e.length,(function(i,n){i.getComponent(k).setData(e[n],n,t._role,(function(e){t.refreshSkins()}))}))},o.refreshData=function(){for(var t=m.getInstance().getBeadConfigs(),e=0;e<t.length;e++){var i=t[e],n=i.jsonId,r=this.ndDimond.children[e]||f(this.pfbUnitDimondDesc);r.parent=this.ndDimond;var o=void 0;switch(n){case"260001":o={lev:this._role.data.dimonEffects.attackLev,num:this._role.data.dimonEffects.attackDmNum,prop:this._role.data.dimonEffects.attack,txt:"攻击+"+this._role.data.dimonEffects.attack},this.lbAtk.string=this._role.data.minAtkNum+o.prop+"~"+(this._role.data.maxAtkNum+o.prop);break;case"260002":o={lev:this._role.data.dimonEffects.skillcdLev,num:this._role.data.dimonEffects.skillcdDmNum,prop:this._role.data.dimonEffects.skillcd,txt:"法宝速度+"+Math.floor(100*this._role.data.dimonEffects.skillcd)+"%"},this.lbSkillCd.string="+"+Math.floor(100*this._role.data.dimonEffects.skillcd)+"%";break;case"260003":o={lev:this._role.data.dimonEffects.atkspeedLev,num:this._role.data.dimonEffects.atkspeedDmNum,prop:this._role.data.dimonEffects.atkspeed,txt:"攻速+"+Math.floor(100*this._role.data.dimonEffects.atkspeed)+"%"},this.lbAtkSpeed.string=this._role.getAtkSpeed().toFixed(2)+"S";break;case"260004":o={lev:this._role.data.dimonEffects.atkmLev,num:this._role.data.dimonEffects.atkmDmNum,prop:this._role.data.dimonEffects.atkm,txt:"暴击伤害+"+Math.floor(100*this._role.data.dimonEffects.atkm)+"%"},this.lbBaoHurtRat.string=Math.floor(100*(this._role.data.dimonEffects.baseAtkM+o.prop))+"%";break;case"260005":o={lev:this._role.data.dimonEffects.hpLev,num:this._role.data.dimonEffects.hpDmNum,prop:this._role.data.dimonEffects.hp,txt:"城门血量+"+this._role.data.dimonEffects.hp},this.lbHp.string="+"+o.prop}r.getComponent(_).setData(i,o)}this.lbUserName.string=this._role.data.nickName||"匿名用户",S.res.remoteLoadSprite(this._role.data.iconUrl,this.sprAvatar,h(90,90)),this.lbBaoAtkRat.string=100*this._role.data.dimonEffects.atkr+"%";var a=v.getInstance().findUser(this._role.data.userId).skins.find((function(t){return 1==t.useStatus})),l=m.getInstance().getSkinLevelConfigBySkinId(this._role.data.skinId,a.level||1);l&&(this.lbSkinName.string="当前皮肤：Lv."+((null==a?void 0:a.level)||1)+" "+l.name)},o.btnSelect=function(t){this._index!==t&&(this._index=t,this.btnTagSkin.node.getChildByName("ndOn").active=0==this._index,this.btnTagDimond.node.getChildByName("ndOn").active=1==this._index,this.btnTagBaseinfo.node.getChildByName("ndOn").active=2==this._index,this.ndDimondDesc.active=1==this._index,this.btnTagSkin.node.getChildByName("Label").getComponent(d).color=0==this._index?new b(210,210,200,255):new b(50,36,24,255),this.btnTagDimond.node.getChildByName("Label").getComponent(d).color=1==this._index?new b(210,210,200,255):new b(50,36,24,255),this.btnTagBaseinfo.node.getChildByName("Label").getComponent(d).color=2==this._index?new b(210,210,200,255):new b(50,36,24,255),this.ndSv.active=0==this._index,this.ndDimond.active=1==this._index,this.ndBaseInfo.active=2==this._index,this.ndUser.active=1==this._index||2==this._index,this.refreshData())},e}(p)).prototype,"pfbUnitSkinShow",[D],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),K=e(G.prototype,"pfbUnitDimondDesc",[y],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),W=e(G.prototype,"ndSv",[x],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),X=e(G.prototype,"ndBaseInfo",[B],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Z=e(G.prototype,"ndDimond",[z],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),q=e(G.prototype,"ndDimondDesc",[E],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Q=e(G.prototype,"ndUser",[C],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Y=e(G.prototype,"ndList",[U],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),$=e(G.prototype,"lbUserName",[N],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),tt=e(G.prototype,"lbSkinName",[A],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),et=e(G.prototype,"sprAvatar",[L],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),it=e(G.prototype,"lbAtk",[T],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),nt=e(G.prototype,"lbAtkSpeed",[I],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),rt=e(G.prototype,"lbBaoHurtRat",[H],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),ot=e(G.prototype,"lbBaoAtkRat",[M],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),at=e(G.prototype,"lbSkillCd",[R],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),lt=e(G.prototype,"lbHp",[V],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),st=e(G.prototype,"btnTagSkin",[F],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),dt=e(G.prototype,"btnTagDimond",[O],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),ct=e(G.prototype,"btnTagBaseinfo",[P],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),j=G))||j));o._RF.pop()}}}));

System.register("chunks:///_virtual/ViewWingShow.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ViewBase.ts","./xcore.ts"],(function(e){var i,r,t,n,o,a,l,c,p,s,u;return{setters:[function(e){i=e.applyDecoratedDescriptor,r=e.inheritsLoose,t=e.initializerDefineProperty,n=e.assertThisInitialized},function(e){o=e.cclegacy,a=e._decorator,l=e.Sprite,c=e.Label,p=e.size},function(e){s=e.ViewBase},function(e){u=e.xcore}],execute:function(){var b,f,g,w,h,y,m,d,S;o._RF.push({},"83dc3dn+apC5ppAXX4S4ewb","ViewWingShow",void 0);var W=a.ccclass,v=a.property;e("ViewWingShow",(b=W("ViewWingShow"),f=v(l),g=v(c),w=v(c),b((m=i((y=function(e){function i(){for(var i,r=arguments.length,o=new Array(r),a=0;a<r;a++)o[a]=arguments[a];return i=e.call.apply(e,[this].concat(o))||this,t(i,"sprWing",m,n(i)),t(i,"lbName",d,n(i)),t(i,"lbProp",S,n(i)),i}return r(i,e),i.prototype.setData=function(e){u.res.remoteLoadSprite(e.img,this.sprWing,p(300,300)),this.lbName.string=e.name,this.lbProp.string=e.prop},i}(s)).prototype,"sprWing",[f],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),d=i(y.prototype,"lbName",[g],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),S=i(y.prototype,"lbProp",[w],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),h=y))||h));o._RF.pop()}}}));

(function(r) {
  r('virtual:///prerequisite-imports/abGame', 'chunks:///_virtual/abGame'); 
})(function(mid, cid) {
    System.register(mid, [cid], function (_export, _context) {
    return {
        setters: [function(_m) {
            var _exportObj = {};

            for (var _key in _m) {
              if (_key !== "default" && _key !== "__esModule") _exportObj[_key] = _m[_key];
            }
      
            _export(_exportObj);
        }],
        execute: function () { }
    };
    });
});