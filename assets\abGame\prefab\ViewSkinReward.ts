import { _decorator, Component, Animation, Label, Node, sp, Sprite, SpriteFrame, size, log, UITransform, v3 } from 'cc';
import { ViewBase } from '../../scripts/libs/manager/ViewBase';
import { ConfigHelper } from '../../scripts/config/ConfigHelper';
import { xcore } from '../../scripts/libs/xcore';
import Tool from '../../scripts/libs/utils/Tool';
import { C_Bundle } from '../../scripts/ConstGlobal';
const { ccclass, property } = _decorator;

@ccclass('ViewSkinReward')
export class ViewSkinReward extends ViewBase {

    @property(sp.Skeleton)
    private anim: sp.Skeleton = null;

    @property(Sprite)
    private sprBg: Sprite = null;

    @property(Animation)
    private role: Animation = null;

    @property(Sprite)
    private sprAvatar: Sprite = null;

    @property(Label)
    private lbUserName: Label = null;

    @property(Label)
    private lbName: Label = null;


    async setData(data: any) {
        this.removeBg();
        log("debris reward:", data.skinId)
        let config = ConfigHelper.getInstance().getSkinConfigByJsonId(data.skinId);
        let animConfig = ConfigHelper.getInstance().getAnimConfigByJsonId(config.moveAnimation);
        let quality = config.quality || 1;

        let spinePath = `./res/anim/skinshow/${quality}/tianhu`;
        xcore.res.bundleLoadSpine(C_Bundle.abGame, spinePath, this.anim).then(() => {
            this.anim.setAnimation(0, 'attack_1', true)
        });

        xcore.res.bundleLoadSprite(C_Bundle.abGame, `./res/img_unpack/game_skinreward_bg0${quality}`, this.sprBg)
        this.lbUserName.string = data.user.nickName;
        xcore.res.remoteLoadSprite(data.user.iconUrl, this.sprAvatar, size(80, 80));
        this.lbName.string = config.name;

        let animaData = {
            'sample': animConfig.sample,
            'duration': animConfig.duration,
            'speed': 1,
            'wrapMode': animConfig.wrapMode,
            'path': animConfig.path,
            'name': animConfig.name
        }
        let atlasName = animaData.name.split('-')[0] || 'default';
        let atlas = xcore.res.getAtlas(atlasName);

        for (let i = 0; i < animaData.sample; i++) {
            let name = `${animaData.name}_${i < 10 ? `0${i}` : i}`;
            let sf = atlas.getSpriteFrame(name)
            if (!sf) {
                // let sf = await xcore.res.remoteLoadSprite(xcore.gameData.cospath + `image/${animaData.path}/${name}.png`) //await xcore.res.bundleLoadSprite(C_Bundle.abGame, `./res/anim/${animaData.path}/${name}`)
                let sf = await xcore.res.bundleLoadSprite('resources', `./res/image/${animaData.path}/${name}`) //await xcore.res.bundleLoadSprite(C_Bundle.abGame, `./res/anim/${animaData.path}/${name}`)
                xcore.res.addAtlasSprite(atlasName, name, sf);

            }
        }
        //缩放配置
        if (animConfig.XCenterPoint == undefined || animConfig.XCenterPoint == null) {
            animConfig.XCenterPoint = 0.5;
        } else if (animConfig.XCenterPoint == "") {
            animConfig.XCenterPoint = 0;
        }
        if (animConfig.YCenterPoint == undefined || animConfig.YCenterPoint == null) {
            animConfig.YCenterPoint = 0.5;
        } else if (animConfig.YCenterPoint == "") {
            animConfig.YCenterPoint = 0;
        }



        await Tool.createAnim(this.role, animaData, atlas);

        //配置动画中心位置
        let animUITransform = this.role?.node?.getComponent(UITransform);
        if (!animUITransform) return
        this.scheduleOnce(() => {
            if (!animUITransform) return
            let h = animUITransform.height;
            let scale = 600 / h;
            animUITransform.node.scale = v3(scale, scale)
        })

        animUITransform?.setAnchorPoint(animConfig.XCenterPoint, animConfig.YCenterPoint);
        this.scheduleOnce(() => {
            if (this.node && this.node.isValid) {
                this.closeSelf();
            }

        }, 3)
    }
}


