import { _decorator, Component, Label, log, Node, size, Sprite, tween, Tween, v3, Vec3 } from 'cc';

import { RoleData } from '../../scripts/RoleData';
import { Skill } from '../../scripts/Skill';
import { E_SkillType } from '../../../scripts/ConstGlobal';

import { xcore } from '../../../scripts/libs/xcore';
import { ConfigHelper } from 'db://assets/scripts/config/ConfigHelper';

const { ccclass, property } = _decorator;

@ccclass('UnitSkillComp')
export class UnitSkillComp extends Component {

    @property(Sprite)
    private sprIcon: Sprite = null;

    @property(Sprite)
    private sprBar: Sprite = null;



    private targetRoleData: RoleData
    private targetSkill: Skill
    private type: E_SkillType


    private isLongSkill: boolean = false

    private offsetPos: Vec3 = v3();

    private tempPos: Vec3 = v3();



    onLoad() {
        this.scheduleOnce(() => {
            tween(this.sprIcon.node).repeatForever
                (
                    tween(this.sprIcon.node)
                        .to(2, { position: v3(0, 40) })
                        .to(2, { position: v3(0, -40) })
                )
                .start();
        }, Math.random() * 1)

    }

    async setData(data: any) {
        this.offsetPos = data.offsetPos || v3();
        this.targetRoleData = data.roleData;
        this.targetSkill = data.targetSkill;
        this.type = data.type;
        this.node.setPosition(v3(this.targetRoleData.pos.x + this.offsetPos.x, this.targetRoleData.pos.y + this.offsetPos.y));
        let config = ConfigHelper.getInstance().getWeaponConfigBySkillJsonId(data.jsonId);
        if (!config) {
            config = ConfigHelper.getInstance().getSkillConfigByType(this.type, data.lev || 1);
        }

        this.node.active = true;

        if (config.icon) {
            this.sprIcon.node.active = true;
            this.sprBar.node.active = true;
            // await xcore.res.remoteLoadSprite(xcore.gameData.cospath + `image/common/${config.icon}.png`, this.sprIcon, size(68, 68));
            // xcore.res.remoteLoadSprite(xcore.gameData.cospath + `image/common/${config.icon}.png`, this.sprBar, size(68, 68));
            await xcore.res.bundleLoadSprite('resources', `./res/image/common/${config.icon}`, this.sprIcon, size(68, 68));
            xcore.res.bundleLoadSprite('resources', `./res/image/common/${config.icon}`, this.sprBar, size(68, 68));
        } else {
            this.sprIcon.node.active = false;
            this.sprBar.node.active = false;
        }

    }
    getType() {
        return this.type
    }
    update(deltaTime: number) {
        if (!this.targetRoleData || !this.targetSkill || !this.type) return
        //这里可以插值实现平滑移动,demo简单的瞬移过去把,反正仅作显示, 数据处理帧会做好判断
        if (this.node.position.x != this.targetRoleData.pos.x + this.offsetPos.x
            || this.node.position.y != this.targetRoleData.pos.y + this.offsetPos.y) {
            //let tempPos = this.node.position.clone();
            //逻辑坐标映射成creator里的坐标,简单除10好了
            this.node.getPosition(this.tempPos);
            this.tempPos = this.tempPos.lerp(v3(this.targetRoleData.pos.x + this.offsetPos.x, this.targetRoleData.pos.y + this.offsetPos.y,), 0.3);
            this.node.position = (this.tempPos);
        }

        if (this.targetSkill.isLongSkill != this.isLongSkill) {
            this.isLongSkill = this.targetSkill.isLongSkill;

        }



        if (this.targetSkill.tickTime && this.targetSkill.nowSpeed) {
            let ran = (this.targetSkill.tickTime % this.targetSkill.nowSpeed) / this.targetSkill.nowSpeed;
            this.sprBar.fillRange = ran;
        }
    }
}


