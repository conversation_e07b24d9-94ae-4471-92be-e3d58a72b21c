[1, ["52fHu7D8hGm5vLaoALoXCl", "c4Ivt8SBZB25pLPpGg1u1P@f9941", "46IdxunHlHYYawWmS60uVs@f9941", "c0iAobRJ1HhJAWLc9syNlo@f9941", "c4Ivt8SBZB25pLPpGg1u1P@6c48a"], ["node", "_font", "_spriteFrame", "root", "lbProp", "lbName", "sprIcon", "btnClose", "sprFrame", "ndRoot", "data", "_textureSource"], [["cc.Node", ["_name", "_layer", "_components", "_prefab", "_children", "_parent", "_lpos"], 1, 9, 4, 2, 1, 5], ["cc.Node", ["_name", "_layer", "_parent", "_components", "_prefab", "_lpos", "_lscale"], 1, 1, 12, 4, 5, 5], ["cc.Sprite", ["_type", "node", "__prefab", "_spriteFrame"], 2, 1, 4, 6], ["cc.UITransform", ["node", "__prefab", "_contentSize"], 3, 1, 4, 5], ["cc.Layout", ["_resizeMode", "_layoutType", "_affectedByScale", "node", "__prefab"], 0, 1, 4], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_isSystemFontUsed", "_enableOutline", "_lineHeight", "node", "__prefab", "_color", "_outlineColor", "_font"], -3, 1, 4, 5, 5, 6], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["999d2sZ8NZDhruNZ9Nc/Y4K", ["node", "__prefab", "ndRoot", "sprFrame", "btnClose", "sprIcon", "lbName", "lbProp"], 3, 1, 4, 1, 1, 1, 1, 1, 1], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.Widget", ["_alignFlags", "_left", "_top", "node", "__prefab"], 0, 1, 4], ["cc.<PERSON><PERSON>", ["_transition", "node", "__prefab", "_normalColor", "_target"], 2, 1, 4, 5, 1]], [[8, 0, 2], [10, 0, 1, 2, 3, 4, 5, 5], [3, 0, 1, 2, 1], [0, 0, 1, 5, 4, 2, 3, 6, 3], [0, 0, 1, 5, 2, 3, 6, 3], [1, 0, 1, 2, 3, 4, 5, 3], [1, 0, 1, 2, 3, 4, 5, 6, 3], [3, 0, 1, 1], [2, 1, 2, 1], [5, 0, 1, 2, 5, 3, 6, 7, 8, 6], [7, 0, 2], [0, 0, 1, 4, 2, 3, 3], [0, 0, 1, 5, 4, 2, 3, 3], [1, 0, 1, 2, 3, 4, 3], [9, 0, 1, 2, 3, 4, 5, 6, 7, 1], [2, 0, 1, 2, 3, 2], [2, 1, 2, 3, 1], [11, 0, 1, 2, 3, 4, 4], [4, 0, 1, 2, 3, 4, 4], [4, 0, 1, 3, 4, 3], [5, 0, 1, 2, 3, 4, 6, 7, 8, 9, 10, 6], [12, 0, 1, 2, 3, 4, 2]], [[[[10, "ViewExchangeShow"], [11, "ViewExchangeShow", 33554432, [-10], [[7, -2, [0, "8bInXtgYRCLK3QK41BhNJU"]], [14, -9, [0, "bbxA0CqtFEP6YebQ4JJB4l"], -8, -7, -6, -5, -4, -3]], [1, "64SU7bLVZLH63Z0urSPFhI", null, null, null, -1, 0]], [12, "ndRoot", 33554432, 1, [-12, -13, -14, -15, -16, -17, -18], [[7, -11, [0, "d2350HolRBUZq/Yh/20da2"]]], [1, "923S/8L0JCvqXJTdsfXeL1", null, null, null, 1, 0]], [5, "btnClose", 33554432, 2, [[[2, -19, [0, "c3hHGfrxpGaK8GLkgmGoOz"], [5, 112, 113]], [15, 1, -20, [0, "c9kGLKvl9ByqksoA6Icchn"], 2], -21, [17, 9, 361.706, -310.18100000000004, -22, [0, "d6DaQYyStGm44IMJvdn5cz"]]], 4, 4, 1, 4], [1, "39THNNkLJCLKuSSYYerxtk", null, null, null, 1, 0], [1, 367.706, 303.68100000000004, 0]], [3, "game_title_viewuserinfo", 33554432, 2, [-25], [[2, -23, [0, "f3RlTnnflODrQQwT4V1eUS"], [5, 294, 77]], [16, -24, [0, "7d5Q5/j6BBgJFCMV8iravQ"], 1]], [1, "a6zIvu5ZtCKJ32GEW9itPv", null, null, null, 1, 0], [1, 0, 302.73800000000006, 0]], [3, "Node", 33554432, 2, [-28], [[2, -26, [0, "3erFNn3ChPdYokNXx1XePO"], [5, 190, 100]], [18, 1, 1, true, -27, [0, "97/voppxBOIq91Jk5CJmYB"]]], [1, "a5yhGZSeJPeLoRic0fk7BY", null, null, null, 1, 0], [1, 0, -218.57100000000003, 0]], [13, "sprFrame", 33554432, 2, [[[2, -29, [0, "f0JINMFcpEoosQdDDvCBVl"], [5, 775, 599]], -30], 4, 1], [1, "bdzgiXzQpHWrZ6z1w5Zft9", null, null, null, 1, 0]], [4, "Label", 33554432, 4, [[2, -31, [0, "884hEiXhJNg4oi6ny6olz5"], [5, 76, 54.4]], [20, "兑换", 36, 36, false, true, -32, [0, "c9V3A79m1P7IWuMelesX3D"], [4, 4292933887], [4, 4278223283], 0]], [1, "8cW6bx56RJzrOB+HD/mr+s", null, null, null, 1, 0], [1, 0, 4.5470000000000255, 0]], [4, "ndDimond", 33554432, 2, [[2, -33, [0, "4129e7Sd5CQ5SyVTWpKoA6"], [5, 100, 0]], [19, 1, 2, -34, [0, "ddp+LAXrlPrIRUhP01QbnP"]]], [1, "544bVJWg1NLqpnlkG2bPaW", null, null, null, 1, 0], [1, 0, -132.72299999999996, 0]], [6, "lbName", 33554432, 2, [[[2, -35, [0, "337b4atixDPKPCLmUZX3D4"], [5, 320, 100.8]], -36], 4, 1], [1, "abieIy7GxOSpO9C/CJgjf/", null, null, null, 1, 0], [1, 0, -163.49400000000003, 0], [1, 0.5, 0.5, 1]], [6, "lbProp", 33554432, 5, [[[2, -37, [0, "4ejPin+7NMSrB/ADgiTOQG"], [5, 380, 100.8]], -38], 4, 1], [1, "f7r8vjw9ZCvpSLM8cuJt8i", null, null, null, 1, 0], [1, 0, -11.331999999999994, 0], [1, 0.5, 0.5, 1]], [5, "sprWing", 33554432, 2, [[[2, -39, [0, "b84eaBQNxKDpS5iVOgcQBW"], [5, 40, 36]], -40], 4, 1], [1, "d4cUEqPKRCnoMDCeSd3Uql", null, null, null, 1, 0], [1, 0, 62.35799999999995, 0]], [8, 6, [0, "35fN/MwAlJ0J1rf6pqFZbr"]], [21, 3, 3, [0, "2d+yTvHq9Fl74QUMAWVLhS"], [4, 4292269782], 3], [9, "翅膀名称", 80, 80, 80, false, 9, [0, "eelh9ANDBHqa676S1yszYx"], [4, 4279772212]], [9, "装备效果：", 80, 80, 80, false, 10, [0, "535qxXuZFAQ5OnlRwiy3qf"], [4, 4279772212]], [8, 11, [0, "13Uh5XC6tFebl7tOdG2jea"]]], 0, [0, 3, 1, 0, 0, 1, 0, 4, 15, 0, 5, 14, 0, 6, 16, 0, 7, 13, 0, 8, 12, 0, 9, 2, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, -1, 6, 0, -2, 4, 0, -3, 3, 0, -4, 8, 0, -5, 9, 0, -6, 5, 0, -7, 11, 0, 0, 3, 0, 0, 3, 0, -3, 13, 0, 0, 3, 0, 0, 4, 0, 0, 4, 0, -1, 7, 0, 0, 5, 0, 0, 5, 0, -1, 10, 0, 0, 6, 0, -2, 12, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, -2, 14, 0, 0, 10, 0, -2, 15, 0, 0, 11, 0, -2, 16, 0, 10, 1, 40], [0, 0, 0, 12, 14, 15], [1, 2, 2, 2, 1, 1], [0, 1, 2, 3, 0, 0]], [[{"name": "game_title_viewuserinfo", "rect": {"x": 0, "y": 0, "width": 294, "height": 77}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 294, "height": 77}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-147, -38.5, 0, 147, -38.5, 0, -147, 38.5, 0, 147, 38.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 77, 294, 77, 0, 0, 294, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -147, "y": -38.5, "z": 0}, "maxPos": {"x": 147, "y": 38.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [6], 0, [0], [11], [4]]]]