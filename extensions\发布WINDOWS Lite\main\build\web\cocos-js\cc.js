System.register(["./_virtual_cc-49edb008.js"],(function(e){"use strict";return{setters:[function(a){e({Acceleration:a.et,AffineTransform:a.aZ,AlphaKey:a.cx,AmbientInfo:a.dx,AnimCurve:a.av,Animation:a.at,AnimationClip:a.ar,AnimationComponent:a.at,AnimationManager:a.az,AnimationState:a.as,Asset:a.dW,AssetLibrary:a.ee,AssetManager:a.ea,AsyncDelegate:a.c5,Atlas:a.A,AudioClip:a.aD,AudioPCMDataView:a.aE,AudioSource:a.aC,AudioSourceComponent:a.aC,BASELINE_RATIO:a.z,BITMASK_TAG:a.cZ,BaseNode:a.dk,BaseRenderData:a.r,BatchedSkinningModelComponent:a.aj,BatchingUtility:a.a8,Billboard:a.eU,BillboardComponent:a.eU,BitMask:a.bA,BitmapFont:a.B,BlockInputEvents:a.fs,BlockInputEventsComponent:a.fs,BloomStage:a.eL,BufferAsset:a.dX,BuiltinResMgr:a.eg,Burst:a.e_,Button:a.fd,ButtonComponent:a.fd,CCBoolean:a.bX,CCClass:a.bS,CCFloat:a.bW,CCInteger:a.bV,CCLoader:a.ec,CCObject:a.bU,CCString:a.bY,CacheMode:a.p,CachedArray:a.bz,CallbacksInvoker:a.d1,Camera:a.dK,CameraComponent:a.dK,Canvas:a.C,CanvasComponent:a.C,Color:a.b2,ColorKey:a.cw,CompactValueTypeArray:a.bZ,Component:a.dq,CurveRange:a.eY,DEFAULT_OCTREE_DEPTH:a.dD,DEFAULT_WORLD_MAX_POS:a.dC,DEFAULT_WORLD_MIN_POS:a.dB,DebugMode:a.cj,DebugView:a.dh,DeferredPipeline:a.eH,Details:a.dU,DirectionalLight:a.ac,DirectionalLightComponent:a.ac,Director:a.dP,DynamicAtlasManager:a.a3,ENUM_TAG:a.cY,EPSILON:a.b7,EasingMethod:a.d0,EditBox:a.fe,EditBoxComponent:a.fe,EditorExtendable:a.c2,EffectAsset:a.e3,Enum:a.bB,Event:a.ek,EventAcceleration:a.el,EventGamepad:a.ep,EventHMD:a.er,EventHandheld:a.es,EventHandle:a.eq,EventHandler:a.dp,EventInfo:a.aw,EventKeyboard:a.em,EventMouse:a.en,EventTarget:a.c3,EventTouch:a.eo,Eventify:a.c4,ExtrapolationMode:a.cr,FogInfo:a.dz,Font:a.F,ForwardFlow:a.eF,ForwardPipeline:a.eD,ForwardStage:a.eG,GCObject:a.cV,Game:a.dR,GbufferStage:a.eJ,Gradient:a.cy,GradientRange:a.eZ,Graphics:a.G,GraphicsComponent:a.G,HALF_PI:a.b5,HorizontalTextAlignment:a.H,HtmlTextParser:a.y,ImageAsset:a.e0,Input:a.ex,InstanceMaterialType:a.I,InstancedBuffer:a.dd,Intersection2D:a.eC,JavaScript:a.e7,JsonAsset:a.d$,KeyCode:a.eu,LOD:a.al,LODGroup:a.am,LRUCache:a.J,Label:a.q,LabelAtlas:a.L,LabelComponent:a.q,LabelOutline:a.k,LabelOutlineComponent:a.k,LabelShadow:a.n,Layers:a.dm,Layout:a.ff,LayoutComponent:a.ff,Light:a.ad,LightComponent:a.ad,LightProbeInfo:a.dH,LightingStage:a.eK,Line:a.eV,LineComponent:a.eV,MATH_FLOAT_ARRAY:a.bv,MIDDLE_RATIO:a.D,MainFlow:a.eI,Mask:a.f,MaskComponent:a.f,Mat3:a.aW,Mat4:a.aX,Material:a.e4,MathBase:a.bw,Mesh:a.a9,MeshBuffer:a.M,MeshRenderData:a.u,MeshRenderer:a.ab,MissingScript:a.dN,MobilityMode:a.dv,ModelComponent:a.ab,ModelRenderer:a.dL,MotionStreak:a.f0,MotionStreakAssemblerManager:a.f1,Node:a.dk,NodeActivator:a.dr,NodeEventType:a.dw,NodePool:a.ei,NodeSpace:a.dt,ObjectCurve:a.cv,OctreeInfo:a.dE,Overflow:a.O,PageView:a.fo,PageViewComponent:a.fo,PageViewIndicator:a.fp,PageViewIndicatorComponent:a.fp,ParticleAsset:a.f3,ParticleSystem:a.eW,ParticleSystem2D:a.e$,ParticleSystem2DAssembler:a.f2,ParticleSystemComponent:a.eW,ParticleUtils:a.eX,PipelineEventProcessor:a.df,PipelineEventType:a.dg,PipelineInputAssemblerData:a.dj,PipelineSceneData:a.dc,PipelineStateManager:a.de,PointLight:a.ag,Pool:a.bx,PostProcessStage:a.eM,PostSettingsInfo:a.dG,Prefab:a.ds,PrefabLink:a.dO,PrivateNode:a.dJ,Profiler:a.f4,ProgressBar:a.fg,ProgressBarComponent:a.fg,QuadRenderData:a.Q,Quat:a.aU,QuatCurve:a.ct,QuatInterpolationMode:a.cu,RangedDirectionalLight:a.ah,RatioSampler:a.au,RealCurve:a.cp,RealInterpolationMode:a.cq,Rect:a.b0,RecyclePool:a.by,ReflectionProbe:a.an,ReflectionProbeFlow:a.eP,ReflectionProbeManager:a.ao,ReflectionProbeStage:a.eQ,ReflectionProbeType:a.ap,RenderComponent:a.c,RenderData:a.t,RenderFlow:a.eS,RenderPipeline:a.eR,RenderRoot2D:a.R,RenderStage:a.eT,RenderTexture:a.e5,Renderable2D:a.c,RenderableComponent:a.dL,Renderer:a.dM,RenderingSubMesh:a.dY,ResolutionPolicy:a.fw,RichText:a.h,RichTextComponent:a.h,Root:a.aH,SafeArea:a.fq,SafeAreaComponent:a.fq,Scene:a.dl,SceneAsset:a.dZ,SceneGlobals:a.dI,Scheduler:a.co,Script:a.e6,ScrollBar:a.fh,ScrollBarComponent:a.fh,ScrollView:a.fi,ScrollViewComponent:a.fi,Settings:a.cz,ShadowFlow:a.eN,ShadowStage:a.eO,ShadowsInfo:a.dA,Size:a.a_,Skeleton:a.aa,SkinInfo:a.dF,SkinnedMeshBatchRenderer:a.aj,SkinnedMeshRenderer:a.ai,SkinnedMeshUnit:a.ak,SkinningModelComponent:a.ai,SkinningModelUnit:a.ak,SkyboxInfo:a.dy,Slider:a.fj,SliderComponent:a.fj,Sorting:a.a6,SortingLayers:a.a5,SphereLight:a.ae,SphereLightComponent:a.ae,SpotLight:a.af,SpotLightComponent:a.af,Sprite:a.i,SpriteAtlas:a.a,SpriteComponent:a.i,SpriteFrame:a.b,SpriteRenderer:a.e,StencilManager:a.S,SubContextView:a.ft,System:a.cB,SystemEvent:a.ez,SystemEventType:a.ej,TTFFont:a.T,TWO_PI:a.b6,TangentWeightMode:a.cs,TextAsset:a.d_,Texture2D:a.e1,TextureCube:a.e2,Toggle:a.fk,ToggleComponent:a.fk,ToggleContainer:a.fl,ToggleContainerComponent:a.fl,Touch:a.ev,TransformBit:a.du,Tween:a.fa,TweenAction:a.f9,TweenSystem:a.f8,TypeScript:a.e8,UI:a.w,UIComponent:a.U,UICoordinateTracker:a.fr,UICoordinateTrackerComponent:a.fr,UIDrawBatch:a.x,UIMeshRenderer:a.j,UIModelComponent:a.j,UIOpacity:a.o,UIOpacityComponent:a.o,UIRenderable:a.c,UIRenderer:a.c,UIReorderComponent:a.fy,UIStaticBatch:a.m,UIStaticBatchComponent:a.m,UITransform:a.d,UITransformComponent:a.d,UIVertexFormat:a.v,VERSION:a.aL,ValueType:a.bD,Vec2:a.aO,Vec3:a.aQ,Vec4:a.aS,VerticalTextAlignment:a.V,VideoClip:a.fz,VideoPlayer:a.fA,View:a.fv,ViewGroup:a.fm,WebGLDevice:a.eB,Widget:a.fn,WidgetComponent:a.fn,WorldNode3DToLocalNodeUI:a.bO,WorldNode3DToWorldNodeUI:a.bP,__checkObsoleteInNamespace__:a.bN,__checkObsolete__:a.bM,_decorator:a.bR,_resetDebugSetting:a.d3,absMax:a.br,absMaxComponent:a.bq,animation:a.aq,applyMixins:a.d2,approx:a.b9,assert:a.ca,assertID:a.cf,assertIsNonNullable:a.d7,assertIsTrue:a.d8,assertsArrayIndex:a.d9,assetManager:a.e9,bezier:a.cE,bezierByTime:a.cF,binarySearch:a.d4,binarySearchBy:a.d6,binarySearchEpsilon:a.d5,bits:a.aN,builtinResMgr:a.eh,ccenum:a.bC,cclegacy:a.aM,clamp:a.ba,clamp01:a.bb,color:a.b3,computeRatioByType:a.ay,convertUtils:a.bQ,createDefaultPipeline:a.eE,debug:a.c6,debugID:a.cb,deprecateModuleExportedName:a.bL,deserialize:a.dT,deserializeTag:a.b$,director:a.dQ,disallowAnimation:a.cP,displayName:a.cK,displayOrder:a.cL,dynamicAtlasManager:a.a4,easing:a.cC,editable:a.cH,editorExtrasTag:a.b_,enumerableProps:a.bs,equals:a.b8,error:a.c8,errorID:a.cd,find:a.dn,flattenCodeArray:a.da,floatToHalf:a.bt,formerlySerializedAs:a.cR,fragmentText:a.a2,game:a.dS,garbageCollectionManager:a.cU,geometry:a.aK,getBaselineOffset:a.E,getEnglishWordPartAtFirst:a.a0,getEnglishWordPartAtLast:a.a1,getError:a.ci,getPathFromRoot:a.aA,getPhaseID:a.di,getSerializationMetadata:a.c1,getSymbolAt:a.X,getSymbolCodeAt:a.Y,getSymbolLength:a.W,getWorldTransformUntilRoot:a.aB,gfx:a.aF,graphicsAssembler:a.g,halfToFloat:a.bu,input:a.ew,instantiate:a.dV,inverseLerp:a.bp,isCCClassOrFastDefined:a.bT,isCCObject:a.c_,isDisplayStats:a.cg,isEnglishWordPartAtFirst:a.Z,isEnglishWordPartAtLast:a.$,isUnicodeCJK:a.K,isUnicodeSpace:a.N,isValid:a.c$,js:a.bE,jsbUtils:a.cG,labelAssembler:a.l,lerp:a.bc,loader:a.ed,log:a.c7,logID:a.cc,macro:a.cm,markAsWarning:a.bK,mat4:a.aY,math:a.aI,memop:a.aJ,misc:a.bF,murmurhash2_32_gc:a.cD,native:a.eA,nextPow2:a.bm,override:a.cQ,path:a.bG,pingPong:a.bo,pipeline:a.db,preTransforms:a.b4,profiler:a.f5,pseudoRandom:a.bj,pseudoRandomRange:a.bk,pseudoRandomRangeInt:a.bl,quat:a.aV,random:a.bf,randomRange:a.bh,randomRangeInt:a.bi,range:a.cM,rangeStep:a.cN,rect:a.b1,removeProperty:a.bJ,renderer:a.aG,repeat:a.bn,replaceProperty:a.bI,resources:a.eb,safeMeasureText:a.P,sampleAnimationCurve:a.ax,screen:a.ck,serializable:a.cS,serializeTag:a.c0,setDefaultLogTimes:a.bH,setDisplayStats:a.ch,setPropertyEnumType:a.cW,setPropertyEnumTypeOnAttrs:a.cX,setRandGenerator:a.bg,settings:a.cA,shift:a.cT,size:a.a$,slide:a.cO,sp:a.f6,spriteAssembler:a.s,sys:a.cl,systemEvent:a.ey,toDegree:a.be,toRadian:a.bd,tooltip:a.cI,tween:a.fb,tweenProgress:a.f7,tweenUtil:a.fc,url:a.ef,utils:a.a7,v2:a.aP,v3:a.aR,v4:a.aT,view:a.fx,visible:a.cJ,visibleRect:a.cn,warn:a.c9,warnID:a.ce,widgetManager:a.fu})}],execute:function(){}}}));
