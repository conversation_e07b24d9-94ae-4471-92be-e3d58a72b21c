{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "a2249d2b-854d-40d4-80d0-149d875f7ec8", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "a2249d2b-854d-40d4-80d0-149d875f7ec8@6c48a", "displayName": "tianpeng-move_08", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "a2249d2b-854d-40d4-80d0-149d875f7ec8", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "a2249d2b-854d-40d4-80d0-149d875f7ec8@f9941", "displayName": "tianpeng-move_08", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": -11, "offsetY": -11, "trimX": 47, "trimY": 45, "width": 134, "height": 132, "rawWidth": 250, "rawHeight": 200, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-67, -66, 0, 67, -66, 0, -67, 66, 0, 67, 66, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [47, 155, 181, 155, 47, 23, 181, 23], "nuv": [0.188, 0.115, 0.724, 0.115, 0.188, 0.775, 0.724, 0.775], "minPos": [-67, -66, 0], "maxPos": [67, 66, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "a2249d2b-854d-40d4-80d0-149d875f7ec8@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "a2249d2b-854d-40d4-80d0-149d875f7ec8@6c48a"}}