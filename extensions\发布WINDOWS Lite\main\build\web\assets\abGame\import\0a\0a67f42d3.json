[1, ["52fHu7D8hGm5vLaoALoXCl", "d5YCkDZLVMLoeux6mZiRF/@f9941", "00mqYknmRGToDlnAzTFJ1O@f9941", "9cdOqhyqBLL7lNk77KcSSc@f9941", "9bX03K6LJHe4W/p2H25qsI@f9941", "32HU1udl9JwLYfxHFrPttL@f9941", "0f0+unuDpMNb3hIUZINkhG", "9cdOqhyqBLL7lNk77KcSSc@6c48a"], ["node", "_font", "_spriteFrame", "_parent", "root", "lbPowerRank", "ndEmpty", "ndGiftRankOn", "btnGiftRank", "ndPowerRankOn", "btnPowerRank", "lbScoreRank", "ndScoreRankOn", "btnScoreRank", "svRankContent", "btnClose", "sprFrame", "ndRoot", "data", "itemPrefab", "_textureSource"], [["cc.Node", ["_name", "_layer", "_active", "_components", "_prefab", "_parent", "_children", "_lpos", "_lscale"], 0, 9, 4, 1, 2, 5, 5], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_isSystemFontUsed", "_enableOutline", "_outlineWidth", "_lineHeight", "node", "__prefab", "_font", "_outlineColor"], -4, 1, 4, 6, 5], ["cc.Node", ["_name", "_layer", "_components", "_prefab", "_lpos", "_parent", "_children"], 1, 12, 4, 5, 1, 2], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.Sprite", ["node", "__prefab", "_spriteFrame"], 3, 1, 4, 6], ["cc.Layout", ["_resizeMode", "_layoutType", "_spacingX", "_spacingY", "_constraintNum", "node", "__prefab"], -2, 1, 4], ["cc.<PERSON><PERSON>", ["_transition", "_zoomScale", "node", "__prefab", "_normalColor", "_target"], 1, 1, 4, 5, 1], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cbff3/Uk3NIgL7y6GktPg2M", ["node", "__prefab", "ndRoot", "sprFrame", "btnClose", "svRankContent", "btnScoreRank", "ndScoreRankOn", "lbScoreRank", "btnPowerRank", "ndPowerRankOn", "btnGiftRank", "ndGiftRankOn", "ndEmpty", "lbPowerRank"], 3, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.Mask", ["node", "__prefab"], 3, 1, 4], ["cc.Widget", ["_alignFlags", "_left", "_right", "_originalWidth", "_originalHeight", "node", "__prefab"], -2, 1, 4], ["cc.Graphics", ["node", "__prefab", "_fillColor"], 3, 1, 4, 5], ["d2bd8Ybyq5JP6Lhg2nKR4ii", ["horizontal", "type", "node", "__prefab", "_content"], 1, 1, 4, 1]], [[9, 0, 2], [11, 0, 1, 2, 3, 4, 5, 5], [3, 0, 1, 2, 1], [0, 0, 1, 5, 3, 4, 7, 3], [4, 0, 1, 2, 1], [1, 0, 1, 2, 3, 7, 8, 9, 5], [2, 0, 1, 6, 2, 3, 4, 3], [3, 0, 1, 1], [3, 0, 1, 2, 3, 1], [6, 0, 1, 2, 3, 4, 5, 3], [0, 0, 1, 6, 3, 4, 3], [0, 0, 1, 5, 6, 3, 4, 7, 3], [0, 0, 1, 5, 3, 4, 3], [0, 0, 2, 1, 5, 3, 4, 4], [2, 0, 1, 5, 6, 2, 3, 4, 3], [2, 0, 1, 5, 2, 3, 4, 3], [1, 0, 1, 2, 3, 4, 5, 7, 8, 10, 7], [8, 0, 2], [0, 0, 1, 5, 6, 3, 4, 3], [0, 0, 1, 5, 3, 4, 7, 8, 3], [2, 0, 1, 5, 2, 3, 3], [10, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 1], [4, 0, 1, 1], [12, 0, 1, 1], [13, 0, 1, 2, 3, 4, 5, 6, 6], [14, 0, 1, 2, 1], [5, 0, 1, 2, 5, 6, 4], [5, 0, 1, 2, 3, 4, 5, 6, 6], [1, 0, 1, 2, 6, 3, 7, 8, 9, 6], [1, 0, 1, 2, 3, 4, 7, 8, 10, 9, 6], [1, 0, 1, 2, 3, 4, 5, 7, 8, 10, 9, 7], [6, 0, 2, 3, 4, 5, 2], [15, 0, 1, 2, 3, 4, 3]], [[[[17, "ViewPowerRank"], [10, "ViewPowerRank", 33554432, [-17], [[7, -2, [0, "11RzJszVBFxZmj97xjEVgG"]], [21, -16, [0, "94UxTdjRpPhKdoGeG5oX44"], -15, -14, -13, -12, -11, -10, -9, -8, -7, -6, -5, -4, -3]], [1, "19VDwdZMJJZ73iaGQ8ifcg", null, null, null, -1, 0]], [18, "ndRoot", 33554432, 1, [-19, -20, -21, -22, -23, -24, -25], [[7, -18, [0, "70GzeaRklO3bZ18da4Xgci"]]], [1, "5c90EXQRFEAYqxS0yCxKZ+", null, null, null, 1, 0]], [11, "Node-002", 33554432, 2, [-27, -28, -29, -30, -31, -32], [[7, -26, [0, "e1rAa3SqNA7YD61kVwPUBr"]]], [1, "cdGstvWAZFBpAIM5X30uOc", null, null, null, 1, 0], [1, 0, 250.1110000000001, 0]], [6, "btnScoreRank", 33554432, [-36, -37], [[[2, -33, [0, "ddS5Pm/7VITb9rh0J+/FEo"], [5, 192, 66]], [4, -34, [0, "2bEKshkHBC0r5xIs5jSLh6"], 10], -35], 4, 4, 1], [1, "435dLY/JlBIZwoRNNcj8Uq", null, null, null, 1, 0], [1, -202, 4.242999999999938, 0]], [6, "btnPowerRank", 33554432, [-41, -42], [[[2, -38, [0, "93Ac2PK6JFHK6UuYENCnb9"], [5, 192, 66]], [4, -39, [0, "50MLciGONDgZ06qRnC4j08"], 12], -40], 4, 4, 1], [1, "b6BY1YfwZMpYDteImv4tj4", null, null, null, 1, 0], [1, 0, 4.242999999999938, 0]], [6, "btnGiftRank", 33554432, [-46, -47], [[[2, -43, [0, "6fKP1F5OREvrwcnUV83sV7"], [5, 192, 66]], [4, -44, [0, "10B5Uebm1NALIlts4TpBxk"], 15], -45], 4, 4, 1], [1, "dePklgPmlNdavCTYUK/XWk", null, null, null, 1, 0], [1, 202, 4.242999999999938, 0]], [14, "btnClose", 33554432, 2, [-51], [[[2, -48, [0, "4btZzmVI9H0YEoWFrXTcGI"], [5, 294, 87]], [4, -49, [0, "d0Fqw2VuxF5pG4dpuXpkkq"], 2], -50], 4, 4, 1], [1, "bfKP1pmNJLibEzawuWZbES", null, null, null, 1, 0], [1, 0, -738.823, 0]], [10, "view", 33554432, [-56], [[8, -52, [0, "d6+yLY8bxCwrqeUXgBcvme"], [5, 860, 1050], [0, 0.5, 1]], [23, -53, [0, "e3knhPdrdFiYpG7kXnhcAH"]], [24, 45, -55, -55, 240, 250, -54, [0, "541gqCwr5C66e7JFISCkcD"]], [25, -55, [0, "5aFcfpey1JeptcnSOy85G+"], [4, 16777215]]], [1, "b2pRfhXwVO/7hZr3bzC/eU", null, null, null, 1, 0]], [11, "ndBtns", 33554432, 2, [4, 5, 6], [[2, -57, [0, "85Zo0nyDVJbq27Rcb7vTNt"], [5, 596, 100]], [26, 1, 1, 10, -58, [0, "afGDy2wJ5EeIQYL6Ht4lkc"]]], [1, "7232vzrgxMpbtMFq47yIBr", null, null, null, 1, 0], [1, 0, 510.3610000000001, 0]], [14, "svV", 33554432, 2, [8], [[[8, -59, [0, "b7CUDgyOZB/qJmJl9gcyzY"], [5, 750, 1050], [0, 0.5, 1]], -60], 4, 1], [1, "1ekVo+af9O4a2ga01jSFZm", null, null, null, 1, 0], [1, 0, 385.8030000000001, 0]], [12, "content", 33554432, 8, [[8, -61, [0, "76YBKmvftLVLstxdP5kegj"], [5, 750, -20], [0, 0.5, 1]], [27, 1, 2, 50, 20, -1.8, -62, [0, "403+oBbNdN6KUsKr6M1fqV"]]], [1, "4bxmcli9ZJl412138+uB+4", null, null, null, 1, 0]], [12, "ndOn", 33554432, 4, [[2, -63, [0, "74ddLiLWJHV5o8B/86YKPS"], [5, 192, 66]], [4, -64, [0, "54MQN+X8tC2a/RR9AjqDMx"], 9]], [1, "daX0k/xo1FrYEZLZ+TXizd", null, null, null, 1, 0]], [13, "ndOn", false, 33554432, 5, [[2, -65, [0, "f1fAGLXSJMJ5a83zPBDWDR"], [5, 192, 66]], [4, -66, [0, "16j/7/pq9OKZuIBjSIIDeY"], 11]], [1, "1aaJ1pI9lL3o3rCW4Jw1xG", null, null, null, 1, 0]], [13, "ndOn", false, 33554432, 6, [[2, -67, [0, "a7TCJAswpDkryDQ9QpBXk7"], [5, 192, 66]], [4, -68, [0, "65mSNJ3CpHJYM7sFAqgapi"], 13]], [1, "fdIZJBF4ZFj4jhh60FeNyD", null, null, null, 1, 0]], [19, "ndEmpty", 33554432, 2, [[2, -69, [0, "8bxieFSVFAYZBBzcCj7eKH"], [5, 320, 100.8]], [28, "暂无数据", 80, 80, 80, false, -70, [0, "beFVeyT3pK1IpiSVPap/D9"], 16]], [1, "2f/FmNmLdHXr5eVJS8eNdA", null, null, null, 1, 0], [1, 0, -22.048999999999978, 0], [1, 0.5, 0.5, 1]], [20, "sprFrame", 33554432, 2, [[[2, -71, [0, "124R703whDY5LyGunnQaJ3"], [5, 860, 1634]], -72], 4, 1], [1, "80fSF32ftChaAHQoCl7y5I", null, null, null, 1, 0]], [3, "game_title_viewpowerrank", 33554432, 2, [[2, -73, [0, "67L3zVUPZOYa0/90pJ5C3W"], [5, 449, 133]], [4, -74, [0, "9573tG12JLq7Q99dRNlben"], 0]], [1, "39jNGlbWpHwJXdVvyKpcwj", null, null, null, 1, 0], [1, 0, 673.8689999999999, 0]], [3, "Label", 33554432, 7, [[2, -75, [0, "ffyt5ys5hL6pp+Q+7b0cvO"], [5, 72, 54.4]], [29, "返回", 34, 34, false, true, -76, [0, "dbuRRyWyVBkKja7AFVpmbg"], [4, 4280507528], 1]], [1, "b3N/j37KRPEb4df9f8Sddr", null, null, null, 1, 0], [1, 0, 4.331999999999965, 0]], [3, "Label", 33554432, 3, [[2, -77, [0, "ddMe7UuBtOWYE9CzDTcu4t"], [5, 52, 50.4]], [5, "排名", 26, 26, false, -78, [0, "082vr9TslMV49KtirWEeiQ"], 3]], [1, "8aQL8wbOFKwJ/oMwYRXmGP", null, null, null, 1, 0], [1, -354.69399999999996, 177.731, 0]], [3, "Label-001", 33554432, 3, [[2, -79, [0, "20wb1tdmxCPb+F2JYFhmv6"], [5, 52, 50.4]], [5, "昵称", 26, 26, false, -80, [0, "9fnF2fYf5F/r/Vjpwsr//H"], 4]], [1, "9fncoP2pVDFrb6cNHpD598", null, null, null, 1, 0], [1, -221.538, 177.731, 0]], [3, "lbTopTxtScore", 33554432, 3, [[2, -81, [0, "1a+gDC8NpO3qsaTAZdvR4a"], [5, 52, 50.4]], [5, "积分", 26, 26, false, -82, [0, "09c5bJSOVLpr8yT2VaAkDk"], 5]], [1, "58XzjAkRlGb629pjAtTZ2B", null, null, null, 1, 0], [1, -81.72899999999998, 177.731, 0]], [3, "lbTopTxtLev", 33554432, 3, [[2, -83, [0, "c7QcAHUfFP5JUMtNmi2J6r"], [5, 52, 50.4]], [5, "输出", 26, 26, false, -84, [0, "f1DbisdOBLOIerl9GnZV/o"], 6]], [1, "b5PTcN2WBJm5Ypgmj/MF72", null, null, null, 1, 0], [1, 30.95399999999995, 177.731, 0]], [3, "Label-002", 33554432, 3, [[2, -85, [0, "d9NeFklexBVZ+S7oQhJtEO"], [5, 0, 50.4]], [5, "", 26, 26, false, -86, [0, "a1vHOuXm5LqrXSRH/GVMYP"], 7]], [1, "48O7QGCi1NOLltUppJEbcX", null, null, null, 1, 0], [1, -259.962, 340.00299999999993, 0]], [3, "lbTopTxtLev-001", 33554432, 3, [[2, -87, [0, "ca4fjOnjdPL5MzbimHPEj9"], [5, 52, 50.4]], [5, "法宝", 26, 26, false, -88, [0, "73844S9VNDlLfxYj9RIbtc"], 8]], [1, "deYko4cu1Ex6RITzAikXNw", null, null, null, 1, 0], [1, 267.12699999999995, 177.731, 0]], [15, "Label", 33554432, 4, [[[2, -89, [0, "fcn7SrPXJO2oCYy6NEwykv"], [5, 142, 56.4]], -90], 4, 1], [1, "e6Bt531b9AGJmzM/mmLKl5", null, null, null, 1, 0], [1, 0, 2.331999999999965, 0]], [15, "Label", 33554432, 5, [[[2, -91, [0, "b6Scvr1HhE/r6RHnBNNghb"], [5, 142, 56.4]], -92], 4, 1], [1, "de2S/41UdAdaPpLYmoJ37i", null, null, null, 1, 0], [1, 0, 2.331999999999965, 0]], [3, "Label", 33554432, 6, [[2, -93, [0, "2akPTEAt9E4b4vVtjdATYE"], [5, 142, 56.4]], [30, "礼物排名", 34, 34, false, true, 3, -94, [0, "c4d7f3TrhOb4p/WP+c1+qi"], [4, 4281677109], 14]], [1, "32r2zWQF5KNoqAuis+mP9W", null, null, null, 1, 0], [1, 0, 2.331999999999965, 0]], [22, 16, [0, "a4E07ICShGFLBZovjTeHGf"]], [31, 3, 7, [0, "efDRvG+NpO95kvHcszq/nB"], [4, 4292269782], 7], [32, false, 1, 10, [0, "599vHOO31NrI3fT4Nl2UiE"], 11], [16, "积分排行", 34, 34, false, true, 3, 25, [0, "b00oI6c51IlZz9qjzTsGjg"], [4, 4281677109]], [9, 3, 1.05, 4, [0, "a3d8YCsh1A+osbPtfHc/uT"], [4, 4292269782], 4], [16, "输出排名", 34, 34, false, true, 3, 26, [0, "25XHvB489KWqmPBgLFBwOl"], [4, 4281677109]], [9, 3, 1.05, 5, [0, "2ebJBxWINIg5/a061xmFhJ"], [4, 4292269782], 5], [9, 3, 1.05, 6, [0, "45Lovnva1G778YZi9ZWsfR"], [4, 4292269782], 6]], 0, [0, 4, 1, 0, 0, 1, 0, 5, 33, 0, 6, 15, 0, 7, 14, 0, 8, 35, 0, 9, 13, 0, 10, 34, 0, 11, 31, 0, 12, 12, 0, 13, 32, 0, 14, 30, 0, 15, 29, 0, 16, 28, 0, 17, 2, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, -1, 16, 0, -2, 17, 0, -3, 7, 0, -4, 3, 0, -5, 10, 0, -6, 9, 0, -7, 15, 0, 0, 3, 0, -1, 19, 0, -2, 20, 0, -3, 21, 0, -4, 22, 0, -5, 23, 0, -6, 24, 0, 0, 4, 0, 0, 4, 0, -3, 32, 0, -1, 12, 0, -2, 25, 0, 0, 5, 0, 0, 5, 0, -3, 34, 0, -1, 13, 0, -2, 26, 0, 0, 6, 0, 0, 6, 0, -3, 35, 0, -1, 14, 0, -2, 27, 0, 0, 7, 0, 0, 7, 0, -3, 29, 0, -1, 18, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, -1, 11, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, -2, 30, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, -2, 28, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, 0, 21, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, -2, 31, 0, 0, 26, 0, -2, 33, 0, 0, 27, 0, 0, 27, 0, 18, 1, 4, 3, 9, 5, 3, 9, 6, 3, 9, 8, 3, 10, 94], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 28, 30, 31, 33], [2, 1, 2, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 1, 2, 1, 2, 19, 1, 1], [3, 0, 4, 0, 0, 0, 0, 0, 0, 1, 2, 1, 2, 1, 0, 2, 0, 5, 6, 0, 0]], [[{"name": "game_title_viewpowerrank", "rect": {"x": 0, "y": 0, "width": 449, "height": 133}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 449, "height": 133}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-224.5, -66.5, 0, 224.5, -66.5, 0, -224.5, 66.5, 0, 224.5, 66.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 133, 449, 133, 0, 0, 449, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -224.5, "y": -66.5, "z": 0}, "maxPos": {"x": 224.5, "y": 66.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [7], 0, [0], [20], [7]]]]