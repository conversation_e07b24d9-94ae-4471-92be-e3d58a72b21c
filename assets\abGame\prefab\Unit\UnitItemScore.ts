import { _decorator, Component, Label, Node, Sprite } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('UnitItemScore')
export class UnitItemScore extends Component {
    @property(Label)
    private lbName: Label = null;

    @property(Label)
    private lbRank: Label = null;

    @property(Label)
    private lbScore: Label = null;

    @property(Sprite)
    private sprAvatar: Sprite = null;

    setData(data: any) {
        this.lbName.string = data.nickName || '匿名用户';
        this.lbRank.string = data.rank;
        this.lbScore.string = data.score;
    }
}


