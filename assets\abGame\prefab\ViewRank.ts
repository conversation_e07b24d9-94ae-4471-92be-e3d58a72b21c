import { _decorator, Button, Color, Component, Label, log, Node, UITransform, v3, Vec3 } from 'cc';
import { ViewBase } from '../../scripts/libs/manager/ViewBase';
import ScrollViewProCom from '../../scripts/libs/utils/ScrollViewProCom';
import { ConfigHelper } from '../../scripts/config/ConfigHelper';
import Net from '../../scripts/Net';
import { xcore } from '../../scripts/libs/xcore';
import { E_EVENT } from '../../scripts/ConstGlobal';
import Tool from '../../scripts/libs/utils/Tool';
import { UnitRank } from './Unit/UnitRank';
const { ccclass, property } = _decorator;
//0积分排行 1 通关排行 2 主播排行
enum E_RankType {
    score,
    level,
    live,
    gift
}
@ccclass('ViewRank')
export class ViewRank extends ViewBase {

    @property([Button])
    private tags: Button[] = [];

    @property(Label)
    private lbTxtRankScore: Label = null;

    @property(Label)
    private lbTxtRankLev: Label = null;

    @property(Label)
    private lbTxtRankReward: Label = null;

    @property(Label)
    private lbTopTxtGiftScore: Label = null;

    @property(Label)
    private lbGiftTitle: Label = null;

    private _selectType: E_RankType = E_RankType.score;
    private _itemNum: number = 0;

    @property(ScrollViewProCom)
    private svRankContent: ScrollViewProCom = null;

    private onRefreshBottom: Function
    private _isBottom: boolean = false;
    private _tempVec1: Vec3 = v3(110, 142, 0)
    private _tempVec2: Vec3 = v3(290, 142, 0)

    protected onLoadCompleted(): void {
        for (let i = 0; i < this.tags.length; i++) {
            this.tags[i].node.on('click', async (isFirstLoad: boolean) => {
                if (this._isBottom) {
                    return
                }
                if (this._selectType == i) {
                    return
                }
                let oldSelect = this._selectType
                try {
                    this.tags.forEach(e => e.enabled = false);
                    this._itemNum = 0;
                    this._selectType = i;
                    await this.onRefreshData(isFirstLoad);
                    this._isBottom = false;
                    this.svRankContent.scrollToTop(0.1);
                    this.tags.forEach(e => e.enabled = true);
                } catch (e) {
                    this.tags.forEach(e => e.enabled = true);
                    this._selectType = oldSelect;
                }

            }, this)
        }
        xcore.event.addEventListener(E_EVENT.RankInfo, this.onRefreshView, this)



        this.onRefreshBottom = Tool.throttle(async (isFirstLoad: boolean = false) => {
            if (this._isBottom) {

                return
            }

            this.onRefreshData(isFirstLoad)

        }, 1000)
        this.svRankContent.node.on('scroll-to-bottom', this.onRefreshBottom.bind(this, false), this);
        this.onRefreshBottom(true)
    }
    onScrollBottom() {

    }
    async onRefreshData(isFirstLoad: boolean) {

        this.tags.forEach(e => e.node.getChildByName('ndOn').active = false);
        this.tags.forEach(e => e.node.getChildByName('Label').getComponent(Label).color = new Color(52, 36, 24, 255));
        this.tags[this._selectType].node.getChildByName('ndOn').active = true;
        this.tags[this._selectType].node.getChildByName('Label').getComponent(Label).color = new Color(255, 255, 255, 255);
        this.lbTxtRankScore.node.active = this._selectType == E_RankType.score;
        this.lbTxtRankLev.node.active = this._selectType != E_RankType.score && this._selectType != E_RankType.gift;
        this.lbTxtRankReward.node.active = this._selectType == E_RankType.score;
        this.lbGiftTitle.node.active = this._selectType == E_RankType.gift;
        this.lbTopTxtGiftScore.node.active = this._selectType == E_RankType.gift;
        let key;
        if (this._selectType == E_RankType.score) {
            this.lbTxtRankScore.node.setPosition(this._tempVec1);
        } else {
            this.lbTxtRankScore.node.setPosition(this._tempVec2);
        }
        if (!xcore.gameData.scoreRankInfo) {
            xcore.gameData.scoreRankInfo = []
        }
        if (!xcore.gameData.roundRankInfo) {
            xcore.gameData.roundRankInfo = []
        }
        if (!xcore.gameData.liverRankInfo) {
            xcore.gameData.liverRankInfo = []
        }
        if (!xcore.gameData.giftRankInfo) {
            xcore.gameData.giftRankInfo = []
        }
        switch (this._selectType) {
            case E_RankType.score:
                //key = ConfigHelper.getInstance().getRankKeyByWeek();
                key = ConfigHelper.getInstance().getRankKeyByMonth();
                await Net.getRankInfo(key, xcore.gameData.scoreRankInfo.length, xcore.gameData.scoreRankInfo.length + 10);
                if (!isFirstLoad && xcore.gameData.scoreRankInfo.length == this._itemNum) {
                    xcore.ui.showToast('到底啦');
                    return

                }
                this._itemNum = xcore.gameData.scoreRankInfo.length
                break;
            case E_RankType.level:
                key = ConfigHelper.getInstance().getRankKeyByLev();
                await Net.getRankInfo(key, xcore.gameData.roundRankInfo.length, xcore.gameData.roundRankInfo.length + 10);
                if (!isFirstLoad && xcore.gameData.roundRankInfo.length == this._itemNum) {
                    xcore.ui.showToast('到底啦');
                    return

                }
                this._itemNum = xcore.gameData.roundRankInfo.length
                break;
            case E_RankType.live:
                key = ConfigHelper.getInstance().getLiverKeyByLev();
                await Net.getLiverRankInfo(key, xcore.gameData.liverRankInfo.length, xcore.gameData.liverRankInfo.length + 10);
                if (!isFirstLoad && xcore.gameData.liverRankInfo.length == this._itemNum) {
                    xcore.ui.showToast('到底啦');
                    return

                }
                this._itemNum = xcore.gameData.liverRankInfo.length
                break;
            case E_RankType.gift:
                key = ConfigHelper.getInstance().getGiftRankKeyByForever();
                await Net.getRankInfo(key, xcore.gameData.giftRankInfo.length, xcore.gameData.giftRankInfo.length + 10);
                if (!isFirstLoad && xcore.gameData.giftRankInfo.length == this._itemNum) {
                    xcore.ui.showToast('到底啦');
                    return
                }
                this._itemNum = xcore.gameData.giftRankInfo.length
                break
            default:
                break;

        }
    }

    onRefreshView() {
        log('onrefreshInfo:', xcore.gameData.scoreRankInfo, xcore.gameData.roundRankInfo, xcore.gameData.liverRankInfo, xcore.gameData.giftRankInfo)
        switch (this._selectType) {
            case E_RankType.score:
                this.svRankContent.setView(xcore.gameData.scoreRankInfo, (n: Node, data: any, index: number) => {
                    n.getComponent(UnitRank).setData(data, this._selectType);
                });
                break;

            case E_RankType.level:
                this.svRankContent.setView(xcore.gameData.roundRankInfo, (n: Node, data: any, index: number) => {
                    n.getComponent(UnitRank).setData(data, this._selectType);
                });
                break;

            case E_RankType.live:
                this.svRankContent.setView(xcore.gameData.liverRankInfo, (n: Node, data: any, index: number) => {
                    n.getComponent(UnitRank).setData(data, this._selectType);
                });
                break;
            case E_RankType.gift:

                this.svRankContent.setView(xcore.gameData.giftRankInfo, (n: Node, data: any, index: number) => {
                    n.getComponent(UnitRank).setData(data, this._selectType);
                });
                break;

            default:
                break;
        }
    }

    protected onDestroyCompleted(): void {
        xcore.event.removeEventListener(E_EVENT.RankInfo, this.onRefreshView, this);
        xcore.gameData.scoreRankInfo = []
        xcore.gameData.roundRankInfo = []
        xcore.gameData.liverRankInfo = []
        xcore.gameData.giftRankInfo = []

    }
}


