[1, ["52fHu7D8hGm5vLaoALoXCl", "b7w5JmbrZEiK7fdnvVXiLt@f9941", "6e4e2/Z5BMYr7bCWpDsEDn@f9941", "4bbyTqTcNPnrmVLDfyjIrt@f9941", "46IdxunHlHYYawWmS60uVs@f9941", "accW4FYjJNuLlcSWEyTkun", "73UARKop9LxLOrdW82Jd8w@f9941", "2c8hCyiEhL3KBAMa5+iwT7@f9941", "ff3reVIcZLdo1ocsK9Lga3@f9941", "9bBYHzhH5F25cHFMhUL3oq@f9941", "67E/Vobn1A7ImerLBhca6R@f9941", "52/AT6sJxIFa3eGrmrJbh5", "b7w5JmbrZEiK7fdnvVXiLt@6c48a"], ["node", "_spriteFrame", "_font", "root", "data", "ndContent", "lbName", "btnClose2", "lbBtnTxt", "lbDesc", "btnClose", "sprFrame", "ndRoot", "_parent", "pfbUnitDimondReward", "sprIcon", "lbNum", "spr<PERSON><PERSON><PERSON>", "lbRank", "ndDetail", "pfbUnitDimond", "_textureSource"], [["cc.Node", ["_name", "_layer", "_components", "_prefab", "_children", "_parent", "_lpos"], 1, 9, 4, 2, 1, 5], ["cc.Sprite", ["_type", "_sizeMode", "node", "__prefab", "_spriteFrame"], 1, 1, 4, 6], ["cc.Node", ["_name", "_layer", "_parent", "_components", "_prefab", "_lpos", "_children", "_lscale"], 1, 1, 12, 4, 5, 2, 5], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.Layout", ["_resizeMode", "_layoutType", "_spacingX", "_spacingY", "_constraintNum", "_affectedByScale", "node", "__prefab"], -3, 1, 4], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_lineHeight", "_isSystemFontUsed", "_horizontalAlign", "_overflow", "_enableOutline", "_outlineWidth", "node", "__prefab", "_color", "_outlineColor"], -6, 1, 4, 5, 5], ["cc.Mask", ["_type", "node", "__prefab"], 2, 1, 4], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["656442NCsZCi54JsECa5Chd", ["node", "__prefab", "ndRoot", "sprFrame", "btnClose", "lbDesc", "lbBtnTxt", "ndContent", "btnClose2", "pfbUnitDimondReward"], 3, 1, 4, 1, 1, 1, 1, 1, 1, 1, 6], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node", "__prefab"], 0, 1, 4], ["cc.Graphics", ["node", "__prefab", "_fillColor"], 3, 1, 4, 5], ["<PERSON><PERSON>", ["horizontal", "node", "__prefab", "_content"], 2, 1, 4, 1], ["cc.<PERSON><PERSON>", ["_transition", "_zoomScale", "node", "__prefab", "_normalColor", "_target"], 1, 1, 4, 5, 1], ["566a0ive+dIKaYBImHuwFSA", ["node", "__prefab", "lbName", "lbNum", "sprIcon"], 3, 1, 4, 1, 1, 1], ["5957eU9UINA64/sKmYekynL", ["node", "__prefab", "ndDetail", "lbRank", "lbName", "spr<PERSON><PERSON><PERSON>", "ndContent", "pfbUnitDimond"], 3, 1, 4, 1, 1, 1, 1, 1, 6]], [[9, 0, 2], [11, 0, 1, 2, 3, 4, 5, 5], [3, 0, 1, 2, 1], [0, 0, 1, 5, 2, 3, 6, 3], [2, 0, 1, 2, 3, 4, 5, 7, 3], [3, 0, 1, 1], [3, 0, 1, 2, 3, 1], [1, 2, 3, 4, 1], [5, 0, 1, 2, 3, 4, 9, 10, 11, 6], [8, 0, 2], [0, 0, 1, 4, 2, 3, 3], [0, 0, 1, 5, 4, 2, 3, 6, 3], [2, 0, 1, 2, 3, 4, 5, 3], [1, 2, 3, 1], [0, 0, 1, 5, 4, 2, 3, 3], [1, 0, 2, 3, 4, 2], [1, 1, 2, 3, 4, 2], [13, 0, 1, 2, 1], [15, 0, 1, 2, 3, 4, 5, 3], [0, 0, 1, 5, 2, 3, 3], [0, 0, 1, 4, 2, 3, 6, 3], [2, 0, 1, 2, 6, 3, 4, 5, 3], [2, 0, 1, 2, 3, 4, 3], [10, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 1], [1, 0, 1, 2, 3, 4, 3], [6, 1, 2, 1], [6, 0, 1, 2, 2], [12, 0, 1, 2, 3, 4, 4], [4, 0, 1, 2, 3, 4, 6, 7, 6], [4, 0, 1, 2, 5, 6, 7, 5], [4, 0, 1, 2, 6, 7, 4], [14, 0, 1, 2, 3, 2], [5, 0, 5, 1, 2, 3, 6, 9, 10, 11, 7], [5, 0, 1, 2, 3, 4, 7, 8, 9, 10, 12, 8], [16, 0, 1, 2, 3, 4, 1], [17, 0, 1, 2, 3, 4, 5, 6, 7, 1]], [[[[9, "ViewDimondReward"], [10, "ViewDimondReward", 33554432, [-11], [[5, -2, [0, "90pyTjcAtKcan/8zrj8fVP"]], [23, -10, [0, "8dIAxwhItLspoAl5S8RU4r"], -9, -8, -7, -6, -5, -4, -3, 4]], [1, "45OxXL49hGVYfM2OOiG8dN", null, null, null, -1, 0]], [14, "ndCore", 33554432, 1, [-13, -14, -15, -16, -17, -18, -19], [[5, -12, [0, "f8P4lczRhPCYwhefSpnMQ2"]]], [1, "15T5djmidHRbExt/GyyG0Q", null, null, null, 1, 0]], [21, "btnClose", 33554432, 2, [-23], [[[2, -20, [0, "f22HVexRxCsI2nXFrbJV/U"], [5, 301, 92]], [15, 1, -21, [0, "9cHWZa6cZCOawD9wGPiBPh"], 2], -22], 4, 4, 1], [1, "44H9Olb0VO2rCgO7bGHyyH", null, null, null, 1, 0], [1, 0, -706.344, 0]], [10, "view", 33554432, [-28], [[6, -24, [0, "10/EI2gVxJa49l7RJIcNwS"], [5, 750, 760], [0, 0.5, 1]], [25, -25, [0, "c7kKHMOWVDYK54kSj2j44P"]], [27, 45, 240, 250, -26, [0, "59t7cgamlN+ZOdYtbsDYPM"]], [17, -27, [0, "80lnLErR5LiZzsahnuN8KZ"], [4, 16777215]]], [1, "2cExB/2SlG3JuXovFsiZmy", null, null, null, 1, 0]], [19, "content", 33554432, 4, [[6, -29, [0, "763fswygZKapdv967zj4Go"], [5, 750, -17], [0, 0.5, 1]], [28, 1, 2, 50, 17, -1.8, -30, [0, "a1VR3gSa9E1J7SJVOrqVEa"]]], [1, "6bDCeLH5VAopZLqt8LV/pp", null, null, null, 1, 0]], [12, "btnClose-001", 33554432, 2, [[[2, -31, [0, "b4912U6uBLRYyjRBc0vwM1"], [5, 112, 113]], [15, 1, -32, [0, "68HsFYJ7BHu5p99P8/sQQJ"], 3], -33], 4, 4, 1], [1, "baeX+qe5JCE5G+Kowm5XNW", null, null, null, 1, 0], [1, 374.19899999999996, 613.4749999999999, 0]], [11, "svV", 33554432, 2, [4], [[6, -34, [0, "ccLaVhU9RBz4UE6eGnK9D9"], [5, 750, 760], [0, 0.5, 1]], [31, false, -35, [0, "09g6wG2QRLxojJ6Y/ZDOVY"], 5]], [1, "aaD1OrTvFJYpeNJWBhmzYN", null, null, null, 1, 0], [1, 0, 171.71900000000005, 0]], [12, "Sprite", 33554432, 2, [[[2, -36, [0, "1bPIkVZtJIE7TaqH7jMUXp"], [5, 40, 36]], -37], 4, 1], [1, "9cnxaDV2JA4I/KoqeWIUR+", null, null, null, 1, 0], [1, 0, 111.62699999999995, 0]], [3, "game_title_viewskindebrisreward", 33554432, 2, [[2, -38, [0, "d6AGYtbfFIA7eH+jumJLqC"], [5, 843, 315]], [7, -39, [0, "bajx1k6LVHZY1WbHkwmOxe"], 0]], [1, "da4vWzv8NNFZAmQkNVsvKj", null, null, null, 1, 0], [1, 0, 625.7370000000001, 0]], [3, "common_frame_01", 33554432, 2, [[2, -40, [0, "93d/Hs94pGPLWzaO29m2Fg"], [5, 800, 323]], [16, 0, -41, [0, "87JGNc2uVM/qfmazH0bgPi"], 1]], [1, "21sTyfG95Gk7PEcUEE3bVO", null, null, null, 1, 0], [1, 0, 339.6310000000001, 0]], [4, "lbDesc", 33554432, 2, [[[6, -42, [0, "d0ZB5cIftD+pkSdA3b6b7t"], [5, 1400, 75.6], [0, 0.5, 1]], -43], 4, 1], [1, "edLMrzRtpPBbQ3aEKncN8X", null, null, null, 1, 0], [1, 0, 470.028, 0], [1, 0.5, 0.5, 1]], [4, "Label", 33554432, 3, [[[2, -44, [0, "68y9YFdDhLApZlQCiPw/of"], [5, 434.219970703125, 163.2]], -45], 4, 1], [1, "09swKkjA5BsqrGvTwycC6I", null, null, null, 1, 0], [1, 0, 2.9990000000000236, 0], [1, 0.5, 0.5, 1]], [13, 8, [0, "a1mLt7KQdE4IuaX1KZYtQ2"]], [32, "", 0, 48, 48, 60, 3, 11, [0, "0ab5NH2+ZDsqzejTpLmxMJ"], [4, 4281023312]], [33, "继续关卡（10）", 60, 60, 120, false, true, 6, 12, [0, "e8MHtzkKxLm730OCs2Bh4t"], [4, 4280505736]], [18, 3, 1.02, 3, [0, "c8kGbhLsFIsLrGXsPX/xIA"], [4, 4292269782], 3], [18, 3, 1.02, 6, [0, "a0PsiMav5DsoyAx41immy6"], [4, 4292269782], 6]], 0, [0, 3, 1, 0, 0, 1, 0, 7, 17, 0, 5, 5, 0, 8, 15, 0, 9, 14, 0, 10, 16, 0, 11, 13, 0, 12, 2, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, -1, 8, 0, -2, 9, 0, -3, 10, 0, -4, 11, 0, -5, 3, 0, -6, 7, 0, -7, 6, 0, 0, 3, 0, 0, 3, 0, -3, 16, 0, -1, 12, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, -1, 5, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, -3, 17, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, -2, 13, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, -2, 14, 0, 0, 12, 0, -2, 15, 0, 4, 1, 4, 13, 7, 45], [0, 0, 0, 0, 0, 15], [1, 1, 1, 1, 14, 2], [1, 2, 3, 4, 5, 0]], [[[9, "UnitDimond"], [10, "UnitDimond", 33554432, [-7, -8, -9], [[5, -2, [0, "1bT5WRlAlDk485tugz+sgc"]], [34, -6, [0, "04qNEjj7ZPDK3ulRseH3i9"], -5, -4, -3]], [1, "2dGlL2f1tCB4166sG+gnC6", null, null, null, -1, 0]], [11, "Node", 33554432, 1, [-12, -13], [[2, -10, [0, "5bsqwn13lADLC2L3kqLVdV"], [5, 83.1099853515625, 100]], [29, 1, 1, 6, true, -11, [0, "41qSwXMWtCxbmyQ9ahijdt"]]], [1, "c526Bax7VNzqBqS1d/9H03", null, null, null, 1, 0], [1, 0, -55.019000000000005, 0]], [3, "game_unitframe_skindebris01", 33554432, 1, [[2, -14, [0, "97CneieOlBmqdDFr5K5LEy"], [5, 90, 90]], [24, 1, 0, -15, [0, "b6H2FOOXxMXItKhoS+mrAa"], 0]], [1, "59JslNfRZPF7w+/zfrEdC9", null, null, null, 1, 0], [1, 0, 8.19399999999996, 0]], [12, "sprIcon", 33554432, 1, [[[2, -16, [0, "b4aTaKxsBCF7LYMyal0/Kn"], [5, 80, 80]], -17], 4, 1], [1, "53wO4ShSdHO6YhxKz23aU/", null, null, null, 1, 0], [1, 0, 8.19399999999996, 0]], [4, "lbName", 33554432, 2, [[[2, -18, [0, "9eVS2u7oZIRLuZ8kcKjr75"], [5, 111.84799194335938, 100.8]], -19], 4, 1], [1, "03TrUW/sNOYbsWBcD9wKwb", null, null, null, 1, 0], [1, -13.592994689941406, 0, 0], [1, 0.5, 0.5, 1]], [4, "lbNum", 33554432, 2, [[[2, -20, [0, "c6nIt9U5pODrg+RR4T/7EP"], [5, 42.371978759765625, 100.8]], -21], 4, 1], [1, "38YdLAjEpBnJ0hWAqUU3/5", null, null, null, 1, 0], [1, 30.961997985839844, 0, 0], [1, 0.5, 0.5, 1]], [13, 4, [0, "b1Guiaw2ZIAJyO+UY7E3w7"]], [8, "宝珠+", 44, 44, 80, false, 5, [0, "cdFYMgov9N9qIWpiEYlaln"], [4, 4281023312]], [8, "+1", 44, 44, 80, false, 6, [0, "48ZHGhrstBXpl9N9zadYF3"], [4, 4278958086]]], 0, [0, 3, 1, 0, 0, 1, 0, 15, 7, 0, 16, 9, 0, 6, 8, 0, 0, 1, 0, -1, 3, 0, -2, 4, 0, -3, 2, 0, 0, 2, 0, 0, 2, 0, -1, 5, 0, -2, 6, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, -2, 7, 0, 0, 5, 0, -2, 8, 0, 0, 6, 0, -2, 9, 0, 4, 1, 21], [0, 8, 9], [1, 2, 2], [6, 0, 0]], [[[9, "UnitDimondReward"], [20, "UnitDimondReward", 33554432, [-10], [[2, -2, [0, "e9/EnjKAtDH4YZshHbKnwD"], [5, 776, 144]], [16, 0, -3, [0, "40OCA8HV9BBKjxirqCukMu"], 3], [35, -9, [0, "70aPjGzFZFP71pP2mYYqYe"], -8, -7, -6, -5, -4, 4]], [1, "6aIguD9MVAq6CYkMtP/A9P", null, null, null, -1, 0], [1, 0, -72, 0]], [14, "ndDetail", 33554432, 1, [-12, -13, -14, -15, -16, -17, -18], [[5, -11, [0, "15YQkDlfFFLL+Kc8+cGR/M"]]], [1, "afNOe+4rNKEYoSeTKKxltt", null, null, null, 1, 0]], [11, "Node-002", 33554432, 2, [-22], [[2, -19, [0, "41D8nisyZOIpyrhNJuDC4u"], [5, 70, 70]], [26, 1, -20, [0, "b60acJexVONqSYRlPWswnA"]], [17, -21, [0, "26TJ+/8nlPzZdjoNa4dKyy"], [4, 16777215]]], [1, "b5VXXRmghB8r7JLbHmBPAm", null, null, null, 1, 0], [1, -225.596, 20.533999999999992, 0]], [3, "Node", 33554432, 2, [[2, -23, [0, "43+EwV3qlNQK8R/0L36VHH"], [5, -40, 100]], [30, 1, 1, 40, -24, [0, "87Joe0xC1IOYI57C7yfnWB"]]], [1, "800qIXy+tDfYV8dqWOXYOi", null, null, null, 1, 0], [1, 184.64499999999998, 4.433999999999969, 0]], [3, "game_rankindex_03", 33554432, 2, [[2, -25, [0, "03CdOayz1G5r5Tu77CHGCb"], [5, 61, 61]], [7, -26, [0, "57yTb4LA9ELrKSwR+RkA2l"], 0]], [1, "ab3Xc4sRJK3qeUsT7u8UNU", null, null, null, 1, 0], [1, -339.349, 18.844999999999914, 0]], [3, "sprAFrame", 33554432, 2, [[2, -27, [0, "47uGCbaH5FAL04+hezJyaT"], [5, 70, 70]], [7, -28, [0, "7f09Ncye9F+44htc+MloR7"], 1]], [1, "c0/NgpoMxC7ajZwQpJxvYd", null, null, null, 1, 0], [1, -225.596, 19.668999999999983, 0]], [22, "spr<PERSON><PERSON><PERSON>", 33554432, 3, [[[2, -29, [0, "f6lx2gKupMoq8nt0XWdvsT"], [5, 99, 100]], -30], 4, 1], [1, "2cfA8L3udLorV8EdmIqjNz", null, null, null, 1, 0]], [3, "sprMask", 33554432, 2, [[2, -31, [0, "5bjYcbRG5OBb1gx2gfUh60"], [5, 70, 70]], [7, -32, [0, "2827oi9W9D7qVBWviscvAT"], 2]], [1, "18pusjlOhIa5E1LpcKN0jg", null, null, null, 1, 0], [1, -225.596, 20.533999999999992, 0]], [4, "lbRank", 33554432, 2, [[[2, -33, [0, "9cCkyeJoRJUa95hPbR8aOE"], [5, 66.239990234375, 100.8]], -34], 4, 1], [1, "9buyDgqTxJOaYtGHIJMuTT", null, null, null, 1, 0], [1, -339.624, 21.420999999999935, 0], [1, 0.5, 0.5, 1]], [4, "lbName", 33554432, 2, [[[2, -35, [0, "2evfN6nXZErbc1I7IpwWwf"], [5, 240, 100.8]], -36], 4, 1], [1, "b1MZSf9QxDF6OAhcssJHVN", null, null, null, 1, 0], [1, -225.596, -38.22000000000003, 0], [1, 0.5, 0.5, 1]], [13, 7, [0, "3d6zkAmKlB7pdo1yD+Hf3f"]], [8, "--", 72, 72, 80, false, 9, [0, "86D1V0CcxFab6xkcT6c7AV"], [4, 4292538105]], [8, "用户名名称", 48, 48, 80, false, 10, [0, "01+h8Hna1HObDMmvrdVJAL"], [4, 4279772212]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, 5, 4, 0, 17, 11, 0, 6, 13, 0, 18, 12, 0, 19, 2, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, -1, 5, 0, -2, 6, 0, -3, 3, 0, -4, 8, 0, -5, 9, 0, -6, 10, 0, -7, 4, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -1, 7, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, -2, 11, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, -2, 12, 0, 0, 10, 0, -2, 13, 0, 4, 1, 36], [0, 0, 0, 0, 0, 12, 13], [1, 1, 1, 1, 20, 2, 2], [7, 8, 9, 10, 11, 0, 0]], [[{"name": "game_title_view<PERSON><PERSON>reward", "rect": {"x": 0, "y": 0, "width": 843, "height": 315}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 843, "height": 315}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-421.5, -157.5, 0, 421.5, -157.5, 0, -421.5, 157.5, 0, 421.5, 157.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 315, 843, 315, 0, 0, 843, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -421.5, "y": -157.5, "z": 0}, "maxPos": {"x": 421.5, "y": 157.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [7], 0, [0], [21], [12]]]]