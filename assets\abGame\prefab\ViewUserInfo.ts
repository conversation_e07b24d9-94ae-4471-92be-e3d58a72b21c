import { _decorator, Button, Color, Component, instantiate, Label, log, Node, Prefab, size, Sprite } from 'cc';
import { ViewBase } from '../../scripts/libs/manager/ViewBase';
import BaseListCtr from '../../scripts/components/BaseLlistCtr';
import { ConfigHelper } from '../../scripts/config/ConfigHelper';
import Tool from '../../scripts/libs/utils/Tool';
import { UnitSkinShow } from './Unit/UnitSkinShow';
import { Role } from '../scripts/Role';
import { UnitDimondDesc } from './Unit/UnitDimondDesc';
import { xcore } from '../../scripts/libs/xcore';
import { FightMgr } from '../scripts/FightMgr';
const { ccclass, property } = _decorator;

@ccclass('ViewUserInfo')
export class ViewUserInfo extends ViewBase {

    @property(Prefab)
    private pfbUnitSkinShow: Prefab = null;

    @property(Prefab)
    private pfbUnitDimondDesc: Prefab = null;

    @property(Node)
    private ndSv: Node = null;

    @property(Node)
    private ndBaseInfo: Node = null;

    @property(Node)
    private ndDimond: Node = null;

    @property(Node)
    private ndDimondDesc: Node = null;

    @property(Node)
    private ndUser: Node = null;

    @property(Node)
    private ndList: Node = null;

    @property(Label)
    private lbUserName: Label = null;

    @property(Label)
    private lbSkinName: Label = null;

    @property(Sprite)
    private sprAvatar: Sprite = null;

    @property(Label)
    private lbAtk: Label = null;

    @property(Label)
    private lbAtkSpeed: Label = null;

    /**暴击伤害百分比 */
    @property(Label)
    private lbBaoHurtRat: Label = null;

    /**暴击率 */
    @property(Label)
    private lbBaoAtkRat: Label = null;

    @property(Label)
    private lbSkillCd: Label = null;

    @property(Label)
    private lbHp: Label = null;


    @property(Button)
    private btnTagSkin: Button = null;
    @property(Button)
    private btnTagDimond: Button = null;
    @property(Button)
    private btnTagBaseinfo: Button = null;

    private _role: Role = null;
    private _index: number = null;


    public setData(data: any): void {
        this._role = data.role;
        this.refreshSkins();
        this.btnTagSkin.node.on('click', this.btnSelect.bind(this, 0), this);
        this.btnTagDimond.node.on('click', this.btnSelect.bind(this, 1), this);
        this.btnTagBaseinfo.node.on('click', this.btnSelect.bind(this, 2), this);
        this.btnSelect(data.index);

    }
    refreshSkins() {
        let configs = ConfigHelper.getInstance().getSkinConfgs();
        configs.sort((a, b) => b.sort - a.sort)
        Tool.asyncModifyChildren(this, this.ndList, this.pfbUnitSkinShow, configs.length, (nd, i) => {
            nd.getComponent(UnitSkinShow).setData(configs[i], i, this._role, (skinId) => {
                this.refreshSkins();
            });
        })
    }
    refreshData() {
        let configs = ConfigHelper.getInstance().getBeadConfigs();
        for (let i = 0; i < configs.length; i++) {
            let config = configs[i];
            let type = config.jsonId;
            let nd = this.ndDimond.children[i] || instantiate(this.pfbUnitDimondDesc);
            nd.parent = this.ndDimond;
            let dimonddata;

            switch (type) {
                /**金珠 攻击加成*/
                case '260001':

                    dimonddata = {
                        lev: this._role.data.dimonEffects.attackLev,
                        num: this._role.data.dimonEffects.attackDmNum,
                        prop: this._role.data.dimonEffects.attack,
                        txt: `攻击+${this._role.data.dimonEffects.attack}`
                    }
                    this.lbAtk.string = `${this._role.data.minAtkNum + dimonddata.prop}~${this._role.data.maxAtkNum + dimonddata.prop}`;
                    break;
                /**木珠 法宝cd*/
                case '260002':
                    dimonddata = {
                        lev: this._role.data.dimonEffects.skillcdLev,
                        num: this._role.data.dimonEffects.skillcdDmNum,
                        prop: this._role.data.dimonEffects.skillcd,
                        txt: `法宝速度+${Math.floor(this._role.data.dimonEffects.skillcd * 100)}%`
                    }
                    this.lbSkillCd.string = `+${Math.floor(this._role.data.dimonEffects.skillcd * 100)}%`;
                    break;
                /**水珠 攻速加成*/
                case '260003':
                    dimonddata = {
                        lev: this._role.data.dimonEffects.atkspeedLev,
                        num: this._role.data.dimonEffects.atkspeedDmNum,
                        prop: this._role.data.dimonEffects.atkspeed,
                        txt: `攻速+${Math.floor(this._role.data.dimonEffects.atkspeed * 100)}%`
                    }
                    this.lbAtkSpeed.string = `${this._role.getAtkSpeed().toFixed(2)}S`;
                    break;
                /**火珠 暴击伤害*/
                case '260004':
                    dimonddata = {
                        lev: this._role.data.dimonEffects.atkmLev,
                        num: this._role.data.dimonEffects.atkmDmNum,
                        prop: this._role.data.dimonEffects.atkm,
                        txt: `暴击伤害+${Math.floor(this._role.data.dimonEffects.atkm * 100)}%`
                    }
                    this.lbBaoHurtRat.string = Math.floor((this._role.data.dimonEffects.baseAtkM + dimonddata.prop) * 100) + '%';
                    break;
                /**土珠 城门血量*/
                case '260005':
                    dimonddata = {
                        lev: this._role.data.dimonEffects.hpLev,
                        num: this._role.data.dimonEffects.hpDmNum,
                        prop: this._role.data.dimonEffects.hp,
                        txt: `城门血量+${this._role.data.dimonEffects.hp}`
                    }
                    this.lbHp.string = `+${dimonddata.prop}`;
                    break;

                default:
                    break;
            }
            //this._role.data.dimonEffects
            nd.getComponent(UnitDimondDesc).setData(config, dimonddata);
        }
        this.lbUserName.string = this._role.data.nickName || '匿名用户';
        xcore.res.remoteLoadSprite(this._role.data.iconUrl, this.sprAvatar, size(90, 90));
        this.lbBaoAtkRat.string = this._role.data.dimonEffects.atkr * 100 + '%';



        let userdata = FightMgr.getInstance().findUser(this._role.data.userId);

        let selectedSkin = userdata.skins.find(e => e.useStatus == 1);

        let skinConfig = ConfigHelper.getInstance().getSkinLevelConfigBySkinId(this._role.data.skinId, selectedSkin.level || 1);

        if (skinConfig) {
            this.lbSkinName.string = `当前皮肤：Lv.${selectedSkin?.level || 1} ` + skinConfig.name;
        }
    }
    btnSelect(index: number) {
        if (this._index === index) return
        this._index = index;
        this.btnTagSkin.node.getChildByName('ndOn').active = this._index == 0;
        this.btnTagDimond.node.getChildByName('ndOn').active = this._index == 1;
        this.btnTagBaseinfo.node.getChildByName('ndOn').active = this._index == 2;
        this.ndDimondDesc.active = this._index == 1;
        this.btnTagSkin.node.getChildByName('Label').getComponent(Label).color = this._index == 0 ? new Color(210, 210, 200, 255) : new Color(50, 36, 24, 255);
        this.btnTagDimond.node.getChildByName('Label').getComponent(Label).color = this._index == 1 ? new Color(210, 210, 200, 255) : new Color(50, 36, 24, 255);
        this.btnTagBaseinfo.node.getChildByName('Label').getComponent(Label).color = this._index == 2 ? new Color(210, 210, 200, 255) : new Color(50, 36, 24, 255);
        this.ndSv.active = this._index == 0;
        this.ndDimond.active = this._index == 1;
        this.ndBaseInfo.active = this._index == 2;
        this.ndUser.active = this._index == 1 || this._index == 2;
        //
        this.refreshData()
    }
}


