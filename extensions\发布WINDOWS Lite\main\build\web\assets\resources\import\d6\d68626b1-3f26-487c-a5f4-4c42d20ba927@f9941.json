[1, ["d6hiaxPyZIfKX0TELSC6kn@6c48a"], ["_textureSource"], ["cc.SpriteFrame"], 0, [{"name": "huodou-idle_03", "rect": {"x": 144, "y": 170, "width": 660, "height": 406}, "offset": {"x": 18.5, "y": -85}, "originalSize": {"width": 911, "height": 576}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-330, -203, 0, 330, -203, 0, -330, 203, 0, 330, 203, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [144, 406, 804, 406, 144, 0, 804, 0], "nuv": [0.15806805708013172, 0, 0.8825466520307355, 0, 0.15806805708013172, 0.7048611111111112, 0.8825466520307355, 0.7048611111111112], "minPos": {"x": -330, "y": -203, "z": 0}, "maxPos": {"x": 330, "y": 203, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]]