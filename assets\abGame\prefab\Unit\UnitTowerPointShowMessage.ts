import { _decorator, Component, Animation, Label, log, Node, sp, Sprite, v3, tween, TweenSystem, Tween, Vec3 } from 'cc';
import { GiftMessageMgr } from '../../scripts/GiftMessageMgr';
import { ConfigHelper } from 'db://assets/scripts/config/ConfigHelper';
import { xcore } from 'db://assets/scripts/libs/xcore';
import Tool from 'db://assets/scripts/libs/utils/Tool';
import { C_Bundle } from 'db://assets/scripts/ConstGlobal';
const { ccclass, property } = _decorator;

@ccclass('UnitTowerPointShowMessage')
export class UnitTowerPointShowMessage extends Component {
    private anim: sp.Skeleton = null;
    @property(Animation)
    private role: Animation = null;


    @property(sp.Skeleton)
    private bgAnim: sp.Skeleton = null;


    @property(Label)
    private lbRoleName: Label = null;

    @property(Label)
    private lbDesc: Label = null;

    private _cb: Function
    private _scaleTw: Tween
    private _tempScale: Vec3 = new Vec3(0, 0, 0)

    protected onLoad(): void {
        this.node.on(Node.EventType.TOUCH_END, () => { 
            this.kill();
        }, this)
    }

    protected onEnable(): void {
        if (!this._scaleTw) {
            this._scaleTw = new Tween(this.node)

                .to(0.2, { scale: new Vec3(1, 1, 1) }, { easing: "backOut" })
        }
        this.node.scale = this._tempScale;
        this._scaleTw.start()
    }
    async setData(data) {

        let config = ConfigHelper.getInstance().getAnimConfigByJsonId(data.anim);
        this.bgAnim.skeletonData = null;
        let towerpointconfig = ConfigHelper.getInstance().getTowerPointByType(data.type, data.lev);
        if (data.fromLev) {
            this.lbDesc.string = `等级提升Lv.${data.fromLev}`;
            this.doTxtEff(data.fromLev, data.lev)
        } else {
            this.lbDesc.string = `等级提升Lv.${data.lev}`;
        }
        this.lbRoleName.string = towerpointconfig.name;;

        this.role.enabled = false;
        let animaData = {
            'sample': config.sample,
            'duration': config.duration,
            'speed': 1,
            'wrapMode': config.wrapMode,
            'path': config.path,
            'name': config.name
        }
        let atlasName = animaData.name.split('-')[0] || 'default';
        let atlas = xcore.res.getAtlas(atlasName);

        // 等级1~10，升级特效：底座
        // 等级11~20，升级特效：底座+底座上的光
        // 等级21~30，升级特效：底座+底座上的光+红光
        // 等级31~100，升级特效：底座+底座上的光+红光+闪电
        let spineName = towerpointconfig.specialEffects;
        /* if (data.lev <= 10) {
            spineName = 'shengji01'
        } else if (data.lev <= 20) {
            spineName = 'shengji02'
        } else if (data.lev <= 30) {
            spineName = 'shengji03'
        } else {
            spineName = 'shengji04'
        } */

        let spinePath = `./res/anim/towerpoint/${spineName}`;
        xcore.res.bundleLoadSpine(C_Bundle.abGame, spinePath, this.bgAnim).then(() => {
            this.bgAnim.setAnimation(0, 'animation', true)
        });

        for (let i = 0; i < animaData.sample; i++) {
            let name = `${animaData.name}_${i < 10 ? `0${i}` : i}`;
            let sf = atlas.getSpriteFrame(name)
            if (!sf) {
                //let sf = await xcore.res.remoteLoadSprite(xcore.gameData.cospath + `image/${animaData.path}/${name}.png`) //await xcore.res.bundleLoadSprite(C_Bundle.abGame, `./res/anim/${animaData.path}/${name}`)
                let sf = await xcore.res.bundleLoadSprite('resources', `./res/image/${animaData.path}/${name}`) //await xcore.res.bundleLoadSprite(C_Bundle.abGame, `./res/anim/${animaData.path}/${name}`)
                xcore.res.addAtlasSprite(atlasName, name, sf);

            }
        }
        this.role.node.scale = v3(2, 2, 2)
        await Tool.createAnim(this.role, animaData, atlas);
        this.scheduleOnce(() => {
            this.kill()

        }, 1.8)
    }

    doTxtEff(fromLev: number, toLev: number) {
        TweenSystem.instance.ActionManager.removeAllActionsFromTarget(this.lbDesc);
        let tw = tween(this.lbDesc);
        let addLev = toLev - fromLev;
        let t = 1 / addLev;
        if (t > 0.1) {
            t = 0.1
        }
        for (let i = 0; i < addLev; i++) {
            tw.delay(t)
            tw.call(() => {
                this.lbDesc.string = `等级提升Lv.${fromLev + i + 1}`;
            })
        }
        tw.start()
    }
    kill() {
        this.node.removeFromParent();
        GiftMessageMgr.getInstance().killTowerPointMessage(this);
    }
}


