{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "065b5bdd-962e-4279-87f0-f943cd57b622", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "065b5bdd-962e-4279-87f0-f943cd57b622@6c48a", "displayName": "tianpeng-move_00", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "065b5bdd-962e-4279-87f0-f943cd57b622", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "065b5bdd-962e-4279-87f0-f943cd57b622@f9941", "displayName": "tianpeng-move_00", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": -11.5, "offsetY": -11, "trimX": 46, "trimY": 45, "width": 135, "height": 132, "rawWidth": 250, "rawHeight": 200, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-67.5, -66, 0, 67.5, -66, 0, -67.5, 66, 0, 67.5, 66, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [46, 155, 181, 155, 46, 23, 181, 23], "nuv": [0.184, 0.115, 0.724, 0.115, 0.184, 0.775, 0.724, 0.775], "minPos": [-67.5, -66, 0], "maxPos": [67.5, 66, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "065b5bdd-962e-4279-87f0-f943cd57b622@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "065b5bdd-962e-4279-87f0-f943cd57b622@6c48a"}}