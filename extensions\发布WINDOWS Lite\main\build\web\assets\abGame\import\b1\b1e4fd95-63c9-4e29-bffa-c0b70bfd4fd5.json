[1, ["52fHu7D8hGm5vLaoALoXCl", "b5YLPadOZMx60zj6fdCuKY@f9941", "ff3reVIcZLdo1ocsK9Lga3@f9941", "9bBYHzhH5F25cHFMhUL3oq@f9941"], ["node", "_spriteFrame", "_font", "root", "lbName", "lbUserName", "spr<PERSON><PERSON><PERSON>", "role", "sprBg", "lbTitle", "anim", "ndCore", "data"], [["cc.Node", ["_name", "_layer", "_components", "_prefab", "_parent", "_children", "_lpos"], 1, 9, 4, 1, 2, 5], ["cc.Node", ["_name", "_layer", "_parent", "_components", "_prefab", "_lpos", "_lscale"], 1, 1, 12, 4, 5, 5], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.Sprite", ["_sizeMode", "node", "__prefab", "_spriteFrame", "_color"], 2, 1, 4, 6, 5], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_lineHeight", "_isSystemFontUsed", "_horizontalAlign", "_enableOutline", "_outlineWidth", "node", "__prefab", "_color", "_outlineColor"], -5, 1, 4, 5, 5], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["sp.Skeleton", ["_preCacheMode", "node", "__prefab"], 2, 1, 4], ["cc.Animation", ["node", "__prefab"], 3, 1, 4], ["cc.Mask", ["_type", "node", "__prefab"], 2, 1, 4], ["cc.Graphics", ["node", "__prefab", "_fillColor"], 3, 1, 4, 5], ["cc.Layout", ["_resizeMode", "_layoutType", "node", "__prefab"], 1, 1, 4], ["ad7f8P+7QRInakdgyTGF1E/", ["node", "__prefab", "ndCore", "anim", "lbTitle", "sprBg", "role", "spr<PERSON><PERSON><PERSON>", "lbUserName", "lbName"], 3, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1]], [[6, 0, 2], [7, 0, 1, 2, 3, 4, 5, 5], [2, 0, 1, 2, 1], [1, 0, 1, 2, 3, 4, 5, 3], [3, 1, 2, 1], [0, 0, 1, 4, 2, 3, 6, 3], [0, 0, 1, 4, 5, 2, 3, 6, 3], [3, 1, 2, 3, 1], [4, 0, 5, 1, 2, 3, 4, 6, 7, 8, 9, 10, 11, 9], [5, 0, 2], [0, 0, 1, 5, 2, 3, 3], [0, 0, 1, 4, 5, 2, 3, 3], [0, 0, 1, 4, 2, 3, 3], [1, 0, 1, 2, 3, 4, 3], [1, 0, 1, 2, 3, 4, 5, 6, 3], [2, 0, 1, 3, 1], [2, 0, 1, 1], [3, 0, 1, 2, 4, 3, 2], [8, 0, 1, 2, 2], [9, 0, 1, 1], [10, 0, 1, 2, 2], [11, 0, 1, 2, 1], [4, 0, 1, 2, 3, 4, 8, 9, 6], [12, 0, 1, 2, 3, 3], [13, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 1]], [[9, "UnitSkinRewardMessage"], [10, "UnitSkinRewardMessage", 33554432, [-12], [[2, -2, [0, "6b78atfVtJcI8p2ZX6/uwh"], [5, 1000, 2000]], [24, -11, [0, "8a6oIpfTpDlbD3IO+xfEow"], -10, -9, -8, -7, -6, -5, -4, -3]], [1, "22isMA121NwJC6mNO4Fvbh", null, null, null, -1, 0]], [11, "ndCore", 33554432, 1, [-14, -15, -16, -17, -18, -19, -20, -21, -22], [[16, -13, [0, "7aQCQIVqpBeLEyJzdqpUfO"]]], [1, "15vIwlO99PQ4Qm0G4kZpSY", null, null, null, 1, 0]], [6, "Node-002", 33554432, 2, [-26], [[2, -23, [0, "3dye+rJGRAy7pJpvaDFWef"], [5, 70, 70]], [20, 1, -24, [0, "908gHZ9lNKJ5jI5cOdfVrS"]], [21, -25, [0, "8dVNQeMnhBc7JUdIgZf5/1"], [4, 16777215]]], [1, "1b5FUHFvxEhpqIZ2kYRIu9", null, null, null, 1, 0], [1, 0, 450.452, 0]], [6, "Node", 33554432, 2, [-29, -30], [[2, -27, [0, "a552azPHFM5bN4g9bhkf8v"], [5, 249.5999755859375, 100]], [23, 1, 1, -28, [0, "a1fHCiTilCHpcb8xcaiXcN"]]], [1, "f6OKzbQRJPA7/q7cRTWTHD", null, null, null, 1, 0], [1, 0, 314.0930000000001, 0]], [3, "role", 33554432, 2, [[[2, -31, [0, "48L63xkuNH6pOUlI09qz8S"], [5, 600, 600]], [4, -32, [0, "74Cg4WhcpIZ49VqK9Mb+gV"]], -33], 4, 4, 1], [1, "deUjs/tHpGrbp9x3VqAXRa", null, null, null, 1, 0], [1, 0, -214.99, 0]], [12, "sprFrame", 33554432, 2, [[2, -34, [0, "26cShehkFBS5q4GV3BcyYR"], [5, 900, 1160]], [17, 0, -35, [0, "faIAAuGChOr4WUFPC8upIG"], [4, 1426063360], 0]], [1, "a3gyK7U/RIQ4r95y808dVy", null, null, null, 1, 0]], [3, "sprBg", 33554432, 2, [[[2, -36, [0, "01oKTu7nJDQ4ntpLePbodz"], [5, 457, 276]], -37], 4, 1], [1, "69ec1UV+pO45iA8H6uSD3o", null, null, null, 1, 0], [1, 0, -323.197, 0]], [3, "anim", 33554432, 2, [[[15, -38, [0, "57iyY0vchJc74Locf5bW2C"], [0, 0.5, 0.5050208409627278]], -39], 4, 1], [1, "9eUXp1lldAp6XNyXADNf4E", null, null, null, 1, 0], [1, 0, -205.68600000000004, 0]], [5, "sprAFrame", 33554432, 2, [[2, -40, [0, "d2jbeetppP8pfq5/15KFg8"], [5, 70, 70]], [7, -41, [0, "84CveqvBpPJrRN6q8LAbFM"], 1]], [1, "0eGg3JwB1MjJZ8yzGw1oyT", null, null, null, 1, 0], [1, 0, 449.587, 0]], [13, "spr<PERSON><PERSON><PERSON>", 33554432, 3, [[[2, -42, [0, "ffbjYNpaFCNbSUh2uZskRe"], [5, 99, 100]], -43], 4, 1], [1, "a0CLBNPnBC74JvPNvypDL4", null, null, null, 1, 0]], [5, "sprMask", 33554432, 2, [[2, -44, [0, "a1GMooeoFHmppvudCAbpk+"], [5, 70, 70]], [7, -45, [0, "d1K8O8AZZAnLvD/Y7s6AzY"], 2]], [1, "afG3zs1e1E07M0wr1SPVl3", null, null, null, 1, 0], [1, 0, 450.452, 0]], [14, "lbName", 33554432, 2, [[[2, -46, [0, "cewRJ3P+dPALCI1IuUF+YX"], [5, 240, 100.8]], -47], 4, 1], [1, "a2chlSZhNEII1cjApoC1oc", null, null, null, 1, 0], [1, 0, 391.6980000000001, 0], [1, 0.5, 0.5, 1]], [3, "lbRoleName-001", 33554432, 4, [[[2, -48, [0, "faWWUwJEpG46IE0yrcUmPv"], [5, 168, 108.8]], -49], 4, 1], [1, "8bwmzTV+BDvp2mcHd9BLeR", null, null, null, 1, 0], [1, -40.79998779296875, 5.676999999999907, 0]], [3, "lbRoleName", 33554432, 4, [[[2, -50, [0, "c8GTCyVHdJPJzYy+0CyyMy"], [5, 81.5999755859375, 108.8]], -51], 4, 1], [1, "352I7cqc5K5JC0E4hWw8RV", null, null, null, 1, 0], [1, 84, 5.676999999999907, 0]], [4, 7, [0, "99TbiMiu5Hir2zPUKXlyTq"]], [18, 0, 8, [0, "8aphzFiWxFBoucwKvbh8Y3"]], [19, 5, [0, "baj2CAS29DnrdXmfGtCkMA"]], [4, 10, [0, "c8SirqKFRE0ovaY5DK00ED"]], [22, "用户名名称", 48, 48, 80, false, 12, [0, "ceEDkt/YNE0Y2usjQypXgU"]], [8, "解锁", 0, 80, 80, 80, false, true, 4, 13, [0, "d9zx6JxLVEf68aP9rIcMwo"], [4, 4291819775], [4, 4280098273]], [8, "--", 0, 80, 80, 80, false, true, 4, 14, [0, "38pEo6XMtMRYW1Hos/Nlv9"], [4, 4282967039], [4, 4280098273]]], 0, [0, 3, 1, 0, 0, 1, 0, 4, 21, 0, 5, 19, 0, 6, 18, 0, 7, 17, 0, 8, 15, 0, 9, 20, 0, 10, 16, 0, 11, 2, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, -1, 6, 0, -2, 7, 0, -3, 8, 0, -4, 5, 0, -5, 9, 0, -6, 3, 0, -7, 11, 0, -8, 12, 0, -9, 4, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -1, 10, 0, 0, 4, 0, 0, 4, 0, -1, 13, 0, -2, 14, 0, 0, 5, 0, 0, 5, 0, -3, 17, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, -2, 15, 0, 0, 8, 0, -2, 16, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, -2, 18, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, -2, 19, 0, 0, 13, 0, -2, 20, 0, 0, 14, 0, -2, 21, 0, 12, 1, 51], [0, 0, 0, 19, 20, 21], [1, 1, 1, 2, 2, 2], [1, 2, 3, 0, 0, 0]]