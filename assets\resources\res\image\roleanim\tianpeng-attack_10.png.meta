{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "47b53403-4a7c-4e9d-8ade-bd453096cb89", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "47b53403-4a7c-4e9d-8ade-bd453096cb89@6c48a", "displayName": "tianpeng-attack_10", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "47b53403-4a7c-4e9d-8ade-bd453096cb89", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "47b53403-4a7c-4e9d-8ade-bd453096cb89@f9941", "displayName": "tianpeng-attack_10", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": -8, "offsetY": -15.5, "trimX": 43, "trimY": 50, "width": 148, "height": 131, "rawWidth": 250, "rawHeight": 200, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-74, -65.5, 0, 74, -65.5, 0, -74, 65.5, 0, 74, 65.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [43, 150, 191, 150, 43, 19, 191, 19], "nuv": [0.172, 0.095, 0.764, 0.095, 0.172, 0.75, 0.764, 0.75], "minPos": [-74, -65.5, 0], "maxPos": [74, 65.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "47b53403-4a7c-4e9d-8ade-bd453096cb89@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "47b53403-4a7c-4e9d-8ade-bd453096cb89@6c48a"}}