[1, ["52fHu7D8hGm5vLaoALoXCl", "7dj5uJT9FMn6OrOOx83tfK@f9941", "b5YLPadOZMx60zj6fdCuKY@f9941", "4b1ocMsJlA45ARE0cj0KmA@f9941", "41Xr0ILK1KeZhrAveJ6ptp@f9941", "26hA86zmVLKYZ30Cd+lOot@f9941", "3bvJBntRxNdLTqkOiS7Nx7@f9941"], ["node", "_spriteFrame", "_font", "root", "lbDesc", "lbName", "spr<PERSON><PERSON><PERSON>", "anim", "data"], [["cc.Node", ["_name", "_layer", "_active", "_components", "_prefab", "_parent", "_children", "_lpos"], 0, 9, 4, 1, 2, 5], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.Sprite", ["_sizeMode", "node", "__prefab", "_spriteFrame", "_color"], 2, 1, 4, 6, 5], ["cc.Node", ["_name", "_layer", "_parent", "_components", "_prefab", "_lpos"], 1, 1, 12, 4, 5], ["cc.Label", ["_string", "_actualFontSize", "_isSystemFontUsed", "_fontSize", "_lineHeight", "_horizontalAlign", "_enableOutline", "_outlineWidth", "node", "__prefab", "_color", "_outlineColor"], -5, 1, 4, 5, 5], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["sp.Skeleton", ["_preCacheMode", "node", "__prefab"], 2, 1, 4], ["cc.Mask", ["_type", "node", "__prefab"], 2, 1, 4], ["cc.Graphics", ["node", "__prefab", "_fillColor"], 3, 1, 4, 5], ["cc.Layout", ["_resizeMode", "_layoutType", "node", "__prefab"], 1, 1, 4], ["2146aW4BCJJx6LMZddMM4k0", ["node", "__prefab", "anim", "spr<PERSON><PERSON><PERSON>", "lbName", "lbDesc"], 3, 1, 4, 1, 1, 1, 1]], [[6, 0, 2], [7, 0, 1, 2, 3, 4, 5, 5], [1, 0, 1, 2, 1], [0, 0, 1, 5, 6, 3, 4, 7, 3], [0, 0, 1, 5, 3, 4, 7, 3], [1, 0, 1, 1], [3, 0, 1, 2, 3, 4, 5, 3], [1, 0, 1, 2, 3, 1], [2, 0, 1, 2, 4, 3, 2], [2, 1, 2, 3, 1], [5, 0, 2], [0, 0, 1, 6, 3, 4, 3], [0, 0, 2, 1, 5, 3, 4, 4], [3, 0, 1, 2, 3, 4, 3], [1, 0, 1, 3, 1], [2, 0, 1, 2, 2], [2, 0, 1, 2, 3, 2], [8, 0, 1, 2, 2], [9, 0, 1, 2, 2], [10, 0, 1, 2, 1], [4, 0, 1, 3, 4, 2, 8, 9, 10, 6], [4, 0, 5, 1, 2, 6, 7, 8, 9, 10, 11, 7], [11, 0, 1, 2, 3, 3], [12, 0, 1, 2, 3, 4, 5, 1]], [[10, "UnitSkillUpMessage"], [11, "UnitSkillUpMessage", 33554432, [-8, -9], [[2, -2, [0, "e1BfWp8KlMEYk/ZMsKNBOe"], [5, 1000, 2000]], [23, -7, [0, "7coNPFjf5BX4xUOMeJkepn"], -6, -5, -4, -3]], [1, "3aRh4BjBtPo7Cs3W/ySAA/", null, null, null, -1, 0]], [3, "ndRoot", 33554432, 1, [-11, -12, -13], [[5, -10, [0, "f8MO6aPjRCz6PZ4yNbOlCR"]]], [1, "3aW5HUTAxNKIVHl1k7vgHz", null, null, null, 1, 0], [1, 0, 511.4119999999999, 0]], [3, "Node", 33554432, 2, [-15, -16, -17], [[5, -14, [0, "8brAH6rW9HUbFQfVB4veKL"]]], [1, "68GyQxcnVEfrzS1+nD3Ktb", null, null, null, 1, 0], [1, 0, -996.1469999999998, 0]], [3, "ndAvatar", 33554432, 3, [-19, -20, -21], [[5, -18, [0, "54MB1UFMhMkbQjHuqeDZFZ"]]], [1, "305bvW02ZDiq17Xd4WH3uu", null, null, null, 1, 0], [1, 75.423, 80.39300000000003, 0]], [3, "Node", 33554432, 4, [-25], [[5, -22, [0, "77W5U64PpD14wu/a0+SHty"]], [18, 1, -23, [0, "95eudtySZIbp8rlbiZ+/ez"]], [19, -24, [0, "1aRHHKfMJDJbUma8s8+GOn"], [4, 16777215]]], [1, "64URpyY6dJfIkU0mQhGfQS", null, null, null, 1, 0], [1, -259.885, 2.2737367544323206e-13, 0]], [3, "ndDetail", 33554432, 3, [-28, -29], [[2, -26, [0, "8b+nRL0j5Bw48CkIsHSSzL"], [5, 100, 96.19999999999999]], [22, 1, 2, -27, [0, "55Cz+JTdpI/o+ERAM7VQI/"]]], [1, "16q1o9BW1DxaJG28pjuIrN", null, null, null, 1, 0], [1, -93.42899999999997, 76.55200000000002, 0]], [12, "Sprite", false, 33554432, 1, [[2, -30, [0, "1ewREyr3dE5YW+Z07kzWbc"], [5, 1080, 1920]], [8, 0, -31, [0, "f7g6XpQx5Dkp4AOEUHyPWB"], [4, 3355443200], 0]], [1, "26uq95X5tOBJkAk89Hi/Dd", null, null, null, 1, 0]], [4, "Sprite", 33554432, 2, [[2, -32, [0, "96w6Fwvx1GxoiKYRPmHQVI"], [5, 900, 1160]], [8, 0, -33, [0, "ccJi6tDLxOlKW8lnnXoSBh"], [4, 2348810240], 1]], [1, "076G7F3kZIBbH5AZYiJ1sJ", null, null, null, 1, 0], [1, 0, -588.9079999999998, 0]], [6, "anim", 33554432, 2, [[[14, -34, [0, "1an+1Gu9NEUL1UkOZV1vB5"], [0, 0.5, 0]], -35], 4, 1], [1, "d4YzdNxcpMmLh/wMJHJO+7", null, null, null, 1, 0], [1, 0, -541.3619999999999, 0]], [4, "sprFrame01", 33554432, 3, [[2, -36, [0, "f4PuZB8BFFvKtCk2mPDfiR"], [5, 674, 178]], [9, -37, [0, "80e4Db/C1B1bgvaMevusrK"], 2]], [1, "3e/Rc98k9JhJEmOpJTfjRz", null, null, null, 1, 0], [1, 75.423, 91.84500000000003, 0]], [4, "sprAvatarBg", 33554432, 4, [[2, -38, [0, "94onM+K49LlLTDzanHg6gI"], [5, 113, 113]], [9, -39, [0, "8fYV8RaZFCqKSg9oRt5tNs"], 3]], [1, "66oR6od3hF7Yml5Lu9JAVX", null, null, null, 1, 0], [1, -257.885, 0, 0]], [13, "spr<PERSON><PERSON><PERSON>", 33554432, 5, [[[2, -40, [0, "50TXHgvyRAK4Z8HUmu/Fwv"], [5, 104, 104]], -41], 4, 1], [1, "94XI84kEhHb4kZPmuEx18G", null, null, null, 1, 0]], [4, "sprAvatarMask", 33554432, 4, [[2, -42, [0, "a9KcqnWLRDwZC65mYS5qvr"], [5, 104, 104]], [16, 0, -43, [0, "faAGhoUgJDOKJ6+oNiXweT"], 4]], [1, "e7MBmCVm1NTKIV4kABGvoC", null, null, null, 1, 0], [1, -257.885, 0, 0]], [6, "lbName", 33554432, 6, [[[7, -44, [0, "9bzbkzPHBK7645IKAf5iHa"], [5, 154, 37.8], [0, 0, 0.5]], -45], 4, 1], [1, "5f8AUVsQtPlJldR2fNBwjk", null, null, null, 1, 0], [1, -12.68300000000005, 29.200000000000045, 0]], [6, "lbDesc", 33554432, 6, [[[7, -46, [0, "1btF9Wl4dHPYriTglQzmXM"], [5, 238.63998413085938, 58.4], [0, 0, 0.5]], -47], 4, 1], [1, "b81O2san9AAYwhe3GhLsNS", null, null, null, 1, 0], [1, -12.68300000000005, -18.899999999999977, 0]], [17, 0, 9, [0, "951K7L5vZCbJmaQ5r4/5Bl"]], [15, 0, 12, [0, "f38EQLAnBKUrxGUwS9KlsH"]], [20, "玩家一般七个字", 22, 22, 30, false, 14, [0, "a8IvSIx1RP26fwZCrY5GLw"], [4, 4294965492]], [21, "技能（Lv.1）", 0, 40, false, true, 4, 15, [0, "69sar9CvpB3II1qfFKOhSo"], [4, 4282967039], [4, 4280098273]]], 0, [0, 3, 1, 0, 0, 1, 0, 4, 19, 0, 5, 18, 0, 6, 17, 0, 7, 16, 0, 0, 1, 0, -1, 7, 0, -2, 2, 0, 0, 2, 0, -1, 8, 0, -2, 9, 0, -3, 3, 0, 0, 3, 0, -1, 10, 0, -2, 4, 0, -3, 6, 0, 0, 4, 0, -1, 11, 0, -2, 5, 0, -3, 13, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, -1, 12, 0, 0, 6, 0, 0, 6, 0, -1, 14, 0, -2, 15, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, -2, 16, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, -2, 17, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, -2, 18, 0, 0, 15, 0, -2, 19, 0, 8, 1, 47], [0, 0, 0, 0, 0, 17, 18, 19], [1, 1, 1, 1, 1, 1, 2, 2], [1, 2, 3, 4, 5, 6, 0, 0]]