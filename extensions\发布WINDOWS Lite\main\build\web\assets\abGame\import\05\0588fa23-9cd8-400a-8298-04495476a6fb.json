[1, ["09B5+DVYpC/6lpV8lwjQps@f9941", "00YULWD2BBwKKTHthRCpVm@f9941", "52fHu7D8hGm5vLaoALoXCl", "455+pC371M/aG1+iGHZpB5@f9941", "1b1aT19HpIH581RzN3FSap@f9941", "5fjWVImFpHU4L57AjwtCkk@f9941", "a1hwVthSBM1aaC078Ba20L@f9941", "9fOGNCS6NLRrPHfAM2gfhG@f9941", "32K7bDPx1GG4+LjicPvUti@f9941", "020TEjX7VDX7SAokJvIRlP@f9941"], ["node", "_spriteFrame", "_font", "root", "lbCost", "lbName", "sprIcon", "sprOn", "sprBg", "data"], [["cc.Node", ["_name", "_layer", "_children", "_components", "_prefab", "_lpos", "_parent"], 1, 2, 9, 4, 5, 1], ["cc.Node", ["_name", "_layer", "_parent", "_components", "_prefab", "_lpos", "_lscale"], 1, 1, 12, 4, 5, 5], ["cc.Sprite", ["node", "__prefab", "_spriteFrame"], 3, 1, 4, 6], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_isSystemFontUsed", "_lineHeight", "node", "__prefab", "_color"], -2, 1, 4, 5], ["cc.Prefab", ["_name"], 2], ["cc.UITransform", ["node", "__prefab", "_contentSize"], 3, 1, 4, 5], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["9c5c8CMZodDZIBp/dvM8oXX", ["node", "__prefab", "sfFrames", "sfOns", "sprBg", "sprOn", "sprIcon", "lbName", "lbCost"], 3, 1, 4, 3, 3, 1, 1, 1, 1, 1], ["cc.<PERSON><PERSON>", ["node", "__prefab"], 3, 1, 4]], [[6, 0, 2], [5, 0, 1, 2, 1], [7, 0, 1, 2, 3, 4, 5, 5], [1, 0, 1, 2, 3, 4, 5, 3], [2, 0, 1, 1], [0, 0, 1, 6, 2, 3, 4, 5, 3], [1, 0, 1, 2, 3, 4, 6, 3], [4, 0, 2], [0, 0, 1, 2, 3, 4, 5, 3], [2, 0, 1, 2, 1], [3, 0, 1, 2, 4, 3, 5, 6, 6], [3, 0, 1, 2, 3, 5, 6, 7, 5], [8, 0, 1, 2, 3, 4, 5, 6, 7, 8, 1], [9, 0, 1, 1]], [[7, "UnitExchange"], [8, "UnitExchange", 33554432, [-10, -11, -12, -13, -14], [[1, -2, [0, "99hRbLSgpCdalEFmRJ/QP9"], [5, 250, 380]], [12, -8, [0, "bahwOinPlN2LJuHsN1SO+V"], [1, 2, 3, 4], [5, 6, 7, 8], -7, -6, -5, -4, -3], [13, -9, [0, "1ddVg+ALJFX5qahXQEzgeY"]]], [2, "23XHNNEnFNhoh08YykYIcj", null, null, null, -1, 0], [1, 0, -190, 0]], [5, "game_unitframe_exchange", 33554432, 1, [-17], [[1, -15, [0, "8f6iWBMbxJQoDsA361SfRo"], [5, 224, 47]], [9, -16, [0, "14lriyHPtIHr1YyZVcXF5a"], 0]], [2, "5eIcKYgcpGhKa7WFrutnEr", null, null, null, 1, 0], [1, 0, -159.35299999999984, 0]], [3, "sprFrame", 33554432, 1, [[[1, -18, [0, "be66CmRQVDs6AB6oAcOfoC"], [5, 212, 298]], -19], 4, 1], [2, "50z4eQOABKN4vS9M0DeXpS", null, null, null, 1, 0], [1, 0, 23.760999999999967, 0]], [3, "spr<PERSON><PERSON>", 33554432, 1, [[[1, -20, [0, "9bu341Z9BKTo++v3ncrlrl"], [5, 212, 298]], -21], 4, 1], [2, "13Rjed7sxKa5OhAm6i5RXm", null, null, null, 1, 0], [1, 0, 23.760999999999967, 0]], [3, "sprOn", 33554432, 1, [[[1, -22, [0, "85CrmwCs1P/IwvUk3IVB6S"], [5, 237, 315]], -23], 4, 1], [2, "6fTrmiFf9OE66jKtH6VshY", null, null, null, 1, 0], [1, -5.0090000000000146, 23.760999999999967, 0]], [5, "Node", 33554432, 1, [-25], [[1, -24, [0, "71Yz3r45hDFLvNvQshfi/H"], [5, 100, 22.68]]], [2, "5aD3HibshPHZTElWfKmLmQ", null, null, null, 1, 0], [1, 0, -85.93200000000002, 0]], [6, "lbName", 33554432, 6, [[[1, -26, [0, "c6fLYJg7xEn45l3M4njm7b"], [5, 33.1199951171875, 45.36]], -27], 4, 1], [2, "bcBTT4WftB5Ztx3a9t16LS", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [6, "lbCost", 33554432, 2, [[[1, -28, [0, "dfD2li8pxP5J8j/xgkUOzh"], [5, 249.1199951171875, 50.4]], -29], 4, 1], [2, "14JuX2Q4VMuoR5NaBic1Yg", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [4, 3, [0, "41D7sZRxBDDpVp3PW2Awww"]], [4, 4, [0, "aepiu3P3BEybUB2MY0Hzg6"]], [4, 5, [0, "3c0iLfJBBNAIdzdpcgBv0U"]], [10, "--", 36, 36, 36, false, 7, [0, "cdrgHKsrBDk7p57AfPNcWx"]], [11, "--礼物积分兑换", 36, 36, false, 8, [0, "c5ZL86l+lP2p9hwFDFVcLf"], [4, 4280891721]]], 0, [0, 3, 1, 0, 0, 1, 0, 4, 13, 0, 5, 12, 0, 6, 10, 0, 7, 11, 0, 8, 9, 0, 0, 1, 0, 0, 1, 0, -1, 3, 0, -2, 4, 0, -3, 5, 0, -4, 6, 0, -5, 2, 0, 0, 2, 0, 0, 2, 0, -1, 8, 0, 0, 3, 0, -2, 9, 0, 0, 4, 0, -2, 10, 0, 0, 5, 0, -2, 11, 0, 0, 6, 0, -1, 7, 0, 0, 7, 0, -2, 12, 0, 0, 8, 0, -2, 13, 0, 9, 1, 29], [0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 11, 12, 13], [1, -1, -2, -3, -4, -1, -2, -3, -4, 1, 1, 2, 2], [3, 0, 4, 5, 6, 1, 7, 8, 9, 0, 1, 2, 2]]