import { Game, game } from 'cc'
import {
	RoomGameAwardPush,
	RoomStatusEnum,
	RoomStatusPushPb,
	RoomUserMainPb,
	RoomUserPushPb
} from '../protos/room'
import { GameSocket, SocketLogLevel } from './ioGame/GameSocket'
import EventDispatcher from './EventDispatcher'
import ResGameStatus from './ioGame/Messages/ResGameStatus'
import { WEB_SOCKET_URL } from '../config/api'
import { GameEvent } from './ioGame/GameEvent'


import CmdLogin from './ioGame/Messages/CmdLogin'

import { xcore } from '../libs/xcore'


import { C_View, E_EVENT } from '../ConstGlobal'

export interface IGameStatusPush {
	msg_type: string
	msg_id: string,
	sec_openid: string,
	sec_gift_id: string,
	content: string
	gift_num: number,
	avatar_url: string,
	nickname: string,
	timestamp: number
}
export type TypeGameStatusPush = {
	msgType: string,
	data: IGameStatusPush[]
}
export enum GameSocketEventEnum {
	//状态变更
	GAME_STATUS = 'GAME_STATUS',
	//链接成功
	CONNECT_SUCCESS = 'CONNECT_SUCCESS',
	//socket关闭
	SOCKET_CLOSE = 'SOCKET_CLOSE'
}

export default class GameSocketCtrl extends EventDispatcher {
	constructor() {
		super()

	}
	private static _inst: GameSocketCtrl
	static get inst() {
		if (!GameSocketCtrl._inst) {
			GameSocketCtrl._inst = new GameSocketCtrl()
		}

		return GameSocketCtrl._inst
	}

	private url: string = WEB_SOCKET_URL
	public socket: GameSocket


	create(url: string) {
		this.url = url
		this.socket = GameSocket.createCls(this.url, { logLevel: SocketLogLevel.Log })

		return this.socket
	}
	connect(url: string = this.url) {
		this.closeSocket();
		// 游戏状态变更
		GameSocketCtrl.inst.off(GameSocketEventEnum.GAME_STATUS, this.ioGameStatusChange, this)
		GameSocketCtrl.inst.off(GameSocketEventEnum.CONNECT_SUCCESS, this.onGameConnectSuccess, this);
		GameSocketCtrl.inst.on(GameSocketEventEnum.CONNECT_SUCCESS, this.onGameConnectSuccess, this);

		this.create(url)
		this.socket.on(GameEvent.OPEN, this.onSocketOpen, this)
		this.socket.on(GameEvent.CLOSE, this.onSocketClose, this)
		this.socket.on(GameEvent.ERROR, this.onConnectError, this)
		this.socket.connect()

		return this.socket
	}
	closeSocket() {
		if (this.socket) {
			this.socket.off(GameEvent.OPEN, this.onSocketOpen, this)
			this.socket.off(GameEvent.CLOSE, this.onSocketClose, this)
			this.socket.off(GameEvent.ERROR, this.onConnectError, this)
			this.socket.close && this.socket.close();
		}


	}
	private async onSocketOpen() {
		console.log('链接成功')
		try {
			await this.login()
			this.addGameListen()
			//await this.joinRoom()
			console.log('链接登录成功')
			this.emit(GameSocketEventEnum.CONNECT_SUCCESS)

		} catch (err) {
			//xcore.ui.showToast('链接失败');
			xcore.ui.addView(C_View.ViewCommonTips, { desc: '您与服务器断开链接，请重启玩法' })
			xcore.event.raiseEvent(E_EVENT.GameLogin, false);
		}
	}

	private addGameListen() {
		// 监听房间状态
		ResGameStatus.inst.listen(this, this.ioGameStatusChange)


	}


	private onSocketClose() {
		console.log('断开连接')
		// 手动踢出不需要
		//xcore.ui.showToast('断开连接')
		xcore.event.raiseEvent(E_EVENT.GameOut);
		//xcore.ui.addView(C_View.ViewCommonTips, { desc: '您与服务器断开链接，请重启玩法' })
	}
	private onConnectError() {
		xcore.ui.showToast('连接失败')
		xcore.event.raiseEvent(E_EVENT.GameBack)
	}

	async login() {
		let loginRet = await CmdLogin.inst.sendSync({
			token: xcore.http.defaults.headers['Authorization'],
			roomId: xcore.gameData.baseInfo.roomId,

		})
		console.log('CmdLogin', loginRet)
		return new Promise(async (resolve, reject) => {
			if (!loginRet.isSucc) {

				return reject(loginRet.code)
			}


			return resolve(loginRet)
		})
	}

	private ioGameStatusChange(res: TypeGameStatusPush) {
		res.data.forEach(e => {
			e.msg_type = res.msgType;
			e.content = e.content ? e.content.trim() : '';
		})

		xcore.event.raiseEvent(E_EVENT.Gift, res.data)

	}

	private onGameConnectSuccess() {

		xcore.event.raiseEvent(E_EVENT.GameLogin, true)
	}
}
