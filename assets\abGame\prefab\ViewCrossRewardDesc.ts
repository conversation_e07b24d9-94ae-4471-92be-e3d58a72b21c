import { _decorator, Component, Label, Node } from 'cc';
import { ViewBase } from '../../scripts/libs/manager/ViewBase';
import { ConfigHelper } from '../../scripts/config/ConfigHelper';
const { ccclass, property } = _decorator;

@ccclass('ViewCrossRewardDesc')
export class ViewCrossRewardDesc extends ViewBase {
    @property(Label)
    private lbDesc: Label = null;

    protected onLoadCompleted(): void {
        let txt = ConfigHelper.getInstance().getConstantConfigByKey('firstPassRule');
        if (txt) {
            this.lbDesc.string = txt;
        }
    }
}


