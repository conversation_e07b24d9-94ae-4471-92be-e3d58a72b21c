import { _decorator, Button, Component, instantiate, Label, log, Node, Prefab } from 'cc';
import { ViewBase } from '../../scripts/libs/manager/ViewBase';
import { ConfigHelper } from '../../scripts/config/ConfigHelper';
import { xcore } from '../../scripts/libs/xcore';
import Tool from '../../scripts/libs/utils/Tool';
import { UnitDimondReward } from './Unit/UnitDimondReward';
import { C_View } from '../../scripts/ConstGlobal';
const { ccclass, property } = _decorator;

@ccclass('ViewDimondReward')
export class ViewDimondReward extends ViewBase {
    @property(Label)
    private lbDesc: Label = null;

    @property(Prefab)
    private pfbUnitDimondReward: Prefab = null;

    @property(Label)
    private lbBtnTxt: Label = null;

    @property(Node)
    private ndContent: Node = null;

    @property(Button)
    private btnClose2: Button = null;



    private _time: number = 21
    private _cb: Function = null

    onLoadCompleted() {
        this.btnClose2.node.on('click', () => {
            xcore.ui.closeView(C_View.ViewDimondReward, true);
            this.node?.destroy();

        }, this)
    }

    public setData(data: any): void {
        let id = data.id;
        let freeid = data.freeid;
        this._cb = data.cb;
        //礼物积分最低值
        let targetScore = ConfigHelper.getInstance().getConstantConfigByKey('lotteryScore') || 200;
        //总积分最低值
        let targetFreeScore = ConfigHelper.getInstance().getConstantConfigByKey('freeLotteryScore') || 100;

        let dconfig = ConfigHelper.getInstance().getLotteryConfigByJsonId(id);
        this.lbDesc.string = dconfig[['easyText', 'normaText', 'hardText'][xcore.gameData.gameMode]];
        let getNums = [3, 2, 1, 1];
        // log("configs:", configs, id, data.users, weights);
        for (let i = 0; i < data.users.length; i++) {
            let user = data.users[i];
            let num = getNums[i] || 1;
            let score = user[1].giftscore;
            let totoalscore = user[1].score;
            let weights = [];
            let targetId;
            if (score >= targetScore) {
                let configs = ConfigHelper.getInstance().getLotteryDebrisConfigsByJsonId(id);
                targetId = id;
                for (let i = 0; i < configs.length; i++) {
                    let config = configs[i];
                    let weight = [config.easyLotteryWeight, config.normalLotteryWeight, config.hardLotteryWeight][xcore.gameData.gameMode]
                    weights.push({

                        id: config.skinFragmentId,
                        weight
                    })
                }

            } else if (totoalscore >= targetFreeScore) {
                let configs = ConfigHelper.getInstance().getLotteryDebrisConfigsByJsonId(freeid);
                targetId = freeid;
                for (let i = 0; i < configs.length; i++) {
                    let config = configs[i];
                    let weight = [config.easyLotteryWeight, config.normalLotteryWeight, config.hardLotteryWeight][xcore.gameData.gameMode]
                    weights.push({

                        id: config.skinFragmentId,
                        weight
                    })
                }

            } else {
                log("积分不足", score, totoalscore)
                continue;
            }



            log('宝珠：', score, totoalscore, weights)
            let ids = [];
            for (let j = 0; j < num; j++) {
                let selectId = Tool.selectArrayIdByWeight(weights);
                if (selectId) {
                    ids.push(selectId)
                }
            }
            if (weights.length > 0) {
                let reward = instantiate(this.pfbUnitDimondReward);
                reward.setParent(this.ndContent);
                reward.getComponent(UnitDimondReward).setData({
                    targetId,
                    user: user,
                    ids
                }, i)
            }



        }

        // log('weights:', weights, 'selectIds:')
    }

    protected update(dt: number): void {
        if (this._time > 0) {
            this._time -= dt;
            this.lbBtnTxt.string = `继续关卡(${Math.floor(this._time)})`;
            if (this._time <= 0) {

                this.closeSelf();
            }
        }



    }
    protected onDestroyCompleted(): void {
        this._cb && this._cb()
    }
}


