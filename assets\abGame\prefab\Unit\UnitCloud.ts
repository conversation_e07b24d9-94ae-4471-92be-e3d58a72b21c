import { _decorator, Component, Node, Sprite, UITransform, v3 } from 'cc';
import { xcore } from '../../../scripts/libs/xcore';
import { C_Bundle } from '../../../scripts/ConstGlobal';
const { ccclass, property } = _decorator;

@ccclass('UnitCloud')
export class UnitCloud extends Component {


    @property(Node)
    private ndMapCloud: Node[] = [];

    private _maxY: number = 30;

    private _width: number

    onLoad() {
        this._width = this.node.getComponent(UITransform).width
    }

    start() {
        this.initCloud();
    }
    initCloud() {
        let names = ["game_cloud_01", "game_cloud_01" ];
        for (let i = 0; i < this.ndMapCloud.length; i++) {
            let cloud = this.ndMapCloud[i].getComponent(Sprite);
            xcore.res.bundleLoadSprite(C_Bundle.abGame, `./res/img_unpack/${names[i]}`, cloud)
        }

    }

    updateCloud() {
        for (let i = 0; i < this.ndMapCloud.length; i++) {

            let targetX = this.ndMapCloud[i].position.x + 0.5;
            let targetY = this.ndMapCloud[i].position.y
            this.ndMapCloud[i].setPosition(v3(targetX, targetY));
            if (targetX >= this._width * 1 ) {
                targetX = - this._width;
                this.ndMapCloud[i].setPosition(v3(targetX, 0));

            }

        }
    }

    update(deltaTime: number) {
        this.updateCloud();
    }
}


