import { _decorator, Component, director, Label, Node, Sprite } from 'cc';
import { C_Bundle, C_Scene, C_View, E_EVENT } from 'db://assets/scripts/ConstGlobal';
import { xcore } from 'db://assets/scripts/libs/xcore';
const { ccclass, property } = _decorator;

@ccclass('UnitSelectGameType')
export class UnitSelectGameType extends Component {
    @property(Label)
    private lbName: Label = null;

    @property(Label)
    private lbCost: Label = null;

    @property(Sprite)
    private sprBg: Sprite = null;

    private jsonId: string = null;

    protected onLoad(): void {
        this.node.on('click', () => {
            xcore.gameData.gameTypeJsonId = this.jsonId;
            xcore.gameData.gameType = 1;
            if (director.getScene().name === 'Main') {
                xcore.ui.switchScene(C_Bundle.abGame, C_Scene.Game);
            } else {
                xcore.ui.closeView(C_View.ViewSelectGameType);
                xcore.event.raiseEvent(E_EVENT.GameConfig);
            }

        }, this)
    }

    setData(config: any) {
        this.lbName.string = config.name;
        let path = `./res/image/${config.path}/${config.picture}`;
        this.refreshImg(path);
        this.jsonId = config.jsonId;
        let txt = ["日", "周", "月"][config.type - 1];
        this.lbCost.string = `每${txt}获得${config.limitation}次挑战机会`;
    }

    async refreshImg(path) {
        let sf = await xcore.res.bundleLoadSprite('resources', path);
        this.sprBg.spriteFrame = sf;
    }
}


