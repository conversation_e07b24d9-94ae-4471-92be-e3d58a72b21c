{"name": "log4js", "version": "6.9.1", "description": "Port of Log4js to work with node.", "homepage": "https://log4js-node.github.io/log4js-node/", "files": ["lib", "types/*.d.ts", "CHANGELOG.md", "SECURITY.md"], "keywords": ["logging", "log", "log4j", "node"], "license": "Apache-2.0", "main": "./lib/log4js", "types": "./types/log4js.d.ts", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "repository": {"type": "git", "url": "https://github.com/log4js-node/log4js-node.git"}, "bugs": {"url": "http://github.com/log4js-node/log4js-node/issues"}, "engines": {"node": ">=8.0"}, "scripts": {"prepare": "is-ci || husky install", "pretest": "npm run prettier --if-present && eslint \"lib/**/*.js\" \"test/**/*.js\"", "prettier": "prettier --check \"**/*.*\"", "prettier:fix": "prettier --write \"**/*.*\"", "test": "tap \"test/tap/**/*.js\" --cov --reporter=classic --coverage-report=text --coverage-report=html --timeout=45", "typings": "tsc -p types/tsconfig.json"}, "directories": {"test": "test", "lib": "lib"}, "dependencies": {"date-format": "^4.0.14", "debug": "^4.3.4", "flatted": "^3.2.7", "rfdc": "^1.3.0", "streamroller": "^3.1.5"}, "devDependencies": {"@commitlint/cli": "^17.4.4", "@commitlint/config-conventional": "^17.4.4", "@log4js-node/sandboxed-module": "^2.2.1", "callsites": "^3.1.0", "deep-freeze": "0.0.1", "eslint": "^8.34.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.6.0", "eslint-import-resolver-node": "^0.3.7", "eslint-plugin-import": "^2.27.5", "eslint-plugin-prettier": "^4.2.1", "fs-extra": "^11.1.0", "husky": "^8.0.3", "is-ci": "^3.0.1", "nyc": "^15.1.0", "prettier": "^2.8.4", "proxyquire": "^2.1.3", "tap": "^16.3.4", "typescript": "^4.9.5"}, "browser": {"os": false, "streamroller": false, "./lib/clustering.js": "./lib/clusteringBrowser.js", "./lib/appenders/dateFile.js": "./lib/appenders/ignoreBrowser.js", "./lib/appenders/file.js": "./lib/appenders/ignoreBrowser.js", "./lib/appenders/fileSync.js": "./lib/appenders/ignoreBrowser.js", "./lib/appenders/multiFile.js": "./lib/appenders/ignoreBrowser.js"}, "prettier": {"trailingComma": "es5", "arrowParens": "always", "overrides": [{"files": ["*.cjs"], "options": {"parser": "typescript"}}]}, "tap": {"check-coverage": true}, "nyc": {"all": true, "include": ["lib/**/*.js"], "require": ["./test/sandbox-coverage"]}}