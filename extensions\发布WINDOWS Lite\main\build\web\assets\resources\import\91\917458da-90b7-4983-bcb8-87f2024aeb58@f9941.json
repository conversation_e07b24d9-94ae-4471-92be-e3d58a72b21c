[1, ["91dFjakLdJg7y4h/ICSutY@6c48a"], ["_textureSource"], ["cc.SpriteFrame"], 0, [{"name": "taotie-idle_13", "rect": {"x": 330, "y": 381, "width": 552, "height": 440}, "offset": {"x": 150.5, "y": -84}, "originalSize": {"width": 911, "height": 1034}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-276, -220, 0, 276, -220, 0, -276, 220, 0, 276, 220, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [330, 653, 882, 653, 330, 213, 882, 213], "nuv": [0.36223929747530187, 0.2059961315280464, 0.9681668496158068, 0.2059961315280464, 0.36223929747530187, 0.6315280464216635, 0.9681668496158068, 0.6315280464216635], "minPos": {"x": -276, "y": -220, "z": 0}, "maxPos": {"x": 276, "y": 220, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]]