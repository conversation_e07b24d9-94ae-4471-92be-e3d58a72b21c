import { _decorator, Component, Node, game, director } from 'cc'

const { ccclass } = _decorator

@ccclass('EventManager')
export class EventManager extends Component {
	static _inst: EventManager
	static get inst(): EventManager {
		if (this._inst == null) {
			this._inst = new Node().addComponent(EventManager)
			director.addPersistRootNode(this._inst.node)
		}
		return this._inst
	}

	on(type: string, callback: Function, caller: any) {
		//console.log(this?.node?.isValid, type)
		if (this?.node?.isValid) {
			this.node.on(type, callback, caller)
		}
	}

	off(type: string, callback: Function, caller: any) {
		if (this?.node?.isValid) {
			this.node?.off(type, callback, caller)
		}
	}
	once(type: string, callback: Function, caller: any) {
		if (this?.node?.isValid) {
			this.node.once(type, callback, caller)
		}
	}
	emit(type: string, ...arg) {
		if (this?.node?.isValid) {
			this.node.emit(type, arg)
		}
	}
}
