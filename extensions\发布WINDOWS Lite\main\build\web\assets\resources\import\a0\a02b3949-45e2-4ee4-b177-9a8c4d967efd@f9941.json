[1, ["a0KzlJReJO5LF3moxNln79@6c48a"], ["_textureSource"], ["cc.SpriteFrame"], 0, [{"name": "huodou-idle_01", "rect": {"x": 144, "y": 131, "width": 660, "height": 445}, "offset": {"x": 18.5, "y": -65.5}, "originalSize": {"width": 911, "height": 576}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-330, -222.5, 0, 330, -222.5, 0, -330, 222.5, 0, 330, 222.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [144, 445, 804, 445, 144, 0, 804, 0], "nuv": [0.15806805708013172, 0, 0.8825466520307355, 0, 0.15806805708013172, 0.7725694444444444, 0.8825466520307355, 0.7725694444444444], "minPos": {"x": -330, "y": -222.5, "z": 0}, "maxPos": {"x": 330, "y": 222.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]]