//例子 状态机1
// this._fsm1 = new FSM(
//     {
//         statenames: [
//             E_CustomerState.None,
//             E_CustomerState.Wait,
//             E_CustomerState.InWalk,
//             E_CustomerState.OnTable,
//             E_CustomerState.AskFood,
//             E_CustomerState.WaitFood,
//             E_CustomerState.EatFood,
//             E_CustomerState.OffTable,
//             E_CustomerState.OutWalk,
//             E_CustomerState.OutWalkAngry,
//         ],
//         transitions: [



//             { name: 'wait', from: E_CustomerState.None, to: E_CustomerState.Wait },
//             { name: 'inwalk', from: E_CustomerState.Wait, to: E_CustomerState.InWalk },
//             { name: 'ontable', from: E_CustomerState.InWalk, to: E_CustomerState.OnTable },
//             { name: 'askfood', from: E_CustomerState.OnTable, to: E_CustomerState.AskFood },
//             { name: 'waitfood', from: E_CustomerState.AskFood, to: E_CustomerState.WaitFood },
//             { name: 'eatfood', from: E_CustomerState.WaitFood, to: E_CustomerState.EatFood },
//             { name: 'offtable', from: E_CustomerState.EatFood, to: E_CustomerState.OffTable },
//             { name: 'outwalk', from: E_CustomerState.OffTable, to: E_CustomerState.OutWalk },
//             { name: 'outwalkangry', from: E_CustomerState.OffTable, to: E_CustomerState.OutWalkAngry },

//         ], 
//状态机1切换状态触发回调
//         switchCb: this.customerStateSwitch.bind(this),

//     }
// );
//例子 状态机1
// this.fsm2 = new FSM({
//     statenames: [
//         E_GameState.Gaming,
//         E_GameState.Resume,
//         E_GameState.Pause
//     ],

//     transitions: [

//         { name: 'gameing', from: E_GameState.Gaming, to: E_GameState.Gaming },
//         { name: 'gameing', from: E_GameState.Resume, to: E_GameState.Gaming },
//         { name: 'pause', from: E_GameState.Gaming, to: E_GameState.Pause },
//         { name: 'resume', from: E_GameState.Pause, to: E_GameState.Resume },

//     ],
//  状态机2切换状态触发回调
//     switchCb: this.gameStateSwitch.bind(this)
// })

//嵌套使用
// stateUpdate() {
//     if (!this._fsm1 || !this._fsm2) return
//     switch (this._fsm2.currState.name) {

//         case E_GameState.Gaming:

//             //
//             switch (this._fsm1.currState.name) {
//                 case E_CustomerState.None:
//                     log('None')
//                     break;
//                 case E_CustomerState.Wait:
//                     log('Wait')
//                     break;
//                 case E_CustomerState.InWalk:
//                     log('InWalk')
//                     break;
//                 case E_CustomerState.OnTable:
//                     log('OnTable')
//                     break;
//                 case E_CustomerState.AskFood:
//                     log('AskFood')
//                     break;
//                 case E_CustomerState.WaitFood:
//                     log('WaitFood')
//                     break;
//                 case E_CustomerState.EatFood:
//                     log('EatFood')
//                     break;
//                 case E_CustomerState.OffTable:
//                     log('OffTable')
//                     break;
//                 case E_CustomerState.OutWalk:
//                     log('OutWalk')
//                     break;
//                 case E_CustomerState.OutWalkAngry:
//                     log('OutWalkAngry')
//                     break;


//                 default:

//                     break;
//             }
//             break;
//         case E_GameState.Pause:

//             break;
//         case E_GameState.Resume:

//             break;

//         default:
//             break;
//     }


// }
import { log, warn } from "cc"

/**
 * 状态切换接口
 */
interface transition {
    /** 状态名 */
    name: string
    /** 从form状态转换到to状态  */
    from?: string
    /** 从form状态转换到to状态 如果from状态不存在，则可以从任何状态转换 */
    to?: string

    cb?: Function
}

interface options {
    /** 初始化状态 排序首个状态被认为默认状态 */
    statenames: string[]

    /** 状态切换map */
    transitions: transition[]

    /**状态切换回调 */
    switchCb: (s1: FSMState, s2: FSMState) => void,


}

/** 状态类 */
export class FSMState {
    public name: string = null;
    public translationMap: Map<string, FSMTranslation> = null;  //<指令名，跳转>
    constructor(name: string) {
        this.name = name;
        this.translationMap = new Map<string, FSMTranslation>();
    }
}
/** 跳转类 */
export class FSMTranslation {
    public name: string = null; //指令名
    public fromState: FSMState = null;  //上一个状态
    public toState: FSMState = null;    //下一个状态
    public cb?: Function = null

    public constructor(name: string, fromState: FSMState, toState: FSMState, cb?: Function) {
        this.name = name;
        this.fromState = fromState;
        this.toState = toState;
        if (cb) {
            this.cb = cb;
        }

    }
}


/** 状态机 */
export class FSM {

    constructor(options: options) {
        //
        for (let i = 0; i < options.statenames.length; i++) {
            let state = new FSMState(options.statenames[i]);
            this.addState(state);
            //初始化状态 设置默认状态
            if (i == 0) {
                this._currState = state;
            }
        }
        //
        for (let i = 0; i < options.transitions.length; i++) {
            let data = options.transitions[i];
            let fSMTranslation = new FSMTranslation(data.name, this.stateMap.get(data.from), this.stateMap.get(data.to), data.cb);
            this.addTranslation(fSMTranslation);
        }
        this._switchCb = options.switchCb;

    }


    public _switchCb: Function = null;   //状态切换回调
    private _currState: FSMState = null; // 当前状态
    public get currState(): FSMState { return this._currState; }

    public stateMap: Map<string, FSMState> = new Map<string, FSMState>();

    /**
     * 添加状态
     * @param state FSMState
     */
    public addState(state: FSMState): void {
        this.stateMap.set(state.name, state);
    }

    /**
     * 添加状态切换map
     * @param translation FSMTranslation
     */
    public addTranslation(translation: FSMTranslation): void {
        this.stateMap.get(translation.fromState.name).translationMap.set(translation.name, translation);
    }


    //触发状态切换 
    public handleEvent(name: string): boolean {
        if (this._currState != null && this._currState.translationMap.has(name)) {
            let targetTranslation = this._currState.translationMap.get(name);
            let fromState = targetTranslation.fromState;
            this._currState = targetTranslation.toState;
            targetTranslation.cb && targetTranslation.cb();
            //状态切换回调
            this._switchCb && this._switchCb(fromState, this._currState);
            return true
        } else {
            //相同状态或不可切换时 
            warn(`${name} not in ${this._currState.name} 's translationMap： `)
            // log(this._currState.translationMap)
            return false

        }
    }

}