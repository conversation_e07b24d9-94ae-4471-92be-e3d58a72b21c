import { _decorator, Component, Label, Node } from 'cc';
import { ViewBase } from '../../scripts/libs/manager/ViewBase';
const { ccclass, property } = _decorator;

@ccclass('ViewTowerLevUp')
export class ViewTowerLevUp extends ViewBase {
    @property(Label)
    private lbLev: Label = null;

    @property(Label)
    private lbHp: Label = null;
    public setData(data: any): void {

        this.lbLev.string = '等级提升到' + data.level;
        this.lbHp.string = '血量上限+' + data.hp;
        this.scheduleOnce(() => {
            this.closeSelf();
        }, 1.5)
        this.removeBg();
    }
}


