import { error, log, Node, Rect, Sprite, tween, Tween, UITransform, v2, v3, Vec2, Vec3, view, warn } from "cc";
import { C_GiftKey, C_GreatSkill, C_View, E_Channel, E_EVENT, E_GameState, E_GiftMessageType, E_MonsterTag, E_MonsterType, E_RoleType, E_SkillType, E_SkinType, E_TowerPointType } from "../../scripts/ConstGlobal";
import { Singleton } from "../../scripts/libs/utils/Singleton";
import { Role } from "./Role";
import { Skill } from "./Skill";
import { IGift } from "./GiftMgr";

import { Quadtree } from "../../scripts/libs/utils/quadTree/QuadTree";
import { EventManager } from "../../scripts/libs/manager/EventManager";

import { List } from "../../scripts/libs/utils/List";
import Tool from "../../scripts/libs/utils/Tool";
import { xcore } from "../../scripts/libs/xcore";

import { UnitRoleComp } from "../prefab/Unit/UnitRoleComp";

import { Buff } from "./Buff";

import Net from "../../scripts/Net";

import { ConfigHelper } from "../../scripts/config/ConfigHelper";

import TimeUtil from "../../scripts/libs/utils/TimeUtil";

export interface IUser {
    userId: string,
    nickName: string,
    iconUrl: string
    type: E_RoleType,
    giftKey: Omit<IGift, 'userId'>[];
    /**仙族  一个用户一个角色，交互可使角色生成技能， 妖族 一个用户可通过交互不断产出怪物 */
    role?: Role
    //总积分
    score?: number
    //礼物积分
    giftscore?: number
    /**当局需结算灵韵 */
    gold?: number

    //击杀积分
    killscore?: number
    //瓜分到的对象池积分
    poolScore?: number
    /*   poolgiftScore?: number
      poolkillScore?: number */
    //加成积分
    upscore?: number


    //击杀boss数量
    killboss: number
    killmonster: number
    helptower?: number
    rank?: number
    //总共礼物积分
    giftTitleScore?: number
    /**灵韵 */
    lingyun?: number
    //皮肤信息
    skins?: any[]
    //碎片信息
    debris?: any[]
    //输出总伤害
    atkPower?: number

    bossChangeTimeTxt?: string
}

interface IRoleSpace {
    x?: number,
    y?: number,
    left: number,
    right: number,
    top: number,
    bottom: number,
    width?: number,
    height?: number
}
interface ILastUser {
    type: E_RoleType,
    userId: string,
    nickName?: string,
    iconUrl?: string
}
/**战斗管理器：
 * 玩家信息
 * 礼物指令转换为战斗指令
 * 游戏时间轮转逻辑驱动
 *  
 * 
 */
export class FightMgr extends Singleton {

    /**游戏状态 */
    gameState: E_GameState = E_GameState.None;

    /******************************************仙族***********************************/
    /**防守方玩家 包括玩家role和礼物指令集合 */
    heroUser: Map<string, IUser> = new Map();
    /**仙族role */
    heroRoles: Role[] = [];

    /******************************************妖族***********************************/
    /**进攻方玩家 包括玩家role和礼物指令集合*/
    //  monsterUser: Map<string, IUser> = new Map();
    /**底部妖族出怪点role */
    //   monsterCreatePointRoles: Role[] = [];

    /******************************************等待加入阵容**************************************/
    waitUser: Map<string, IUser> = new Map();

    /**生成怪物 */
    monsterRoles: List<Role> = new List<Role>(true);

    monsterNum: number = 0;

    attackTargets: Map<string, Role> = new Map();

    buffList: List<Buff> = new List<Buff>(true);

    /**塔 */
    tower: Role
    towerPoints: Role[] = []
    towerGiftNums: number[] = [0, 0, 0, 0]
    /**游戏时间 */
    tickTime: number = 0;


    /**等待创建中的妖族数据队列 */
    //   waitMonsterData: CreateMonster[] = [];

    /***父节点  */
    heroParentNd: Node
    monsterParentNd: Node
    towerParentNd: Node
    //技能父节点
    skillParentNd: Node

    /** 妖族角色活动范围 */
    monsterSpace: IRoleSpace = {
        left: 0,
        right: 0,
        top: 0,
        bottom: 0,
        width: 0,
        height: 0
    }
    /** 仙族角色活动范围 */
    heroSpace: IRoleSpace = {
        left: 0,
        right: 0,
        top: 0,
        bottom: 0,
        width: 0,
        height: 0
    }

    //role对象池
    rolePool: List<Role> = new List();
    //skill对象池
    skillPool: List<Skill> = new List()

    buffPool: Buff[] = [];

    /**游戏时间 */
    gameoverTime: number

    gameRoundConfigs: any[];
    /**关卡轮次 */
    gameRound: number = 0;

    gameLevel: number = 0;
    gameLevelJsonId: string
    scoreAddedValue: number

    newGameLevel: number = 0;
    autoNextGameLevel: boolean = false;


    /**下一轮次出现剩余时间 */
    nextRoundTime: number = 3;
    /**每次产生怪间隔 */
    eachCreateMonsterTime: number = 1;
    tempeachCreateMonsterTime: number = 1;
    eachCreateMonsterNum: number = 3;
    /**本轮剩余需创建怪物数量 */
    leftRoundMonsterNum: number = 0;
    roundMonsterJsonId: string
    /**炮台  */
    public towerPointUpGiftNum: number = 100;



    private gameTotalScore: number = 0;
    private gameTotalKillScore: number = 0;
    private gameTotalGiftScore: number = 0;
    private firstJoinGame: boolean = true;

    isOldTargetPriority: boolean = true;
    public quadTree: Quadtree
    private checkRect: Rect
    private roundEmitData: any

    private towerTw: Tween
    private towerTwFunc: Function
    //宝珠id
    private dimondId: string = null;
    private freeDimondId: string = null;
    //抽奖id
    private lotteryId: string = null;
    private freeLotteryId: string = null;
    private firstPassLotteryId: string = null;
    private needUpdateUser: string[] = []
    private onRefreshUserRank: Function = null;

    private lastUser: List<ILastUser> = new List();
    private isOnline: boolean = false;
    //能量电池礼物数量
    private giftPowerNum: number = 0;
    constructor() {
        super();

    }

    initTower() {
        if (!this.tower) {
            this.tower = new Role();
        }
        this.tower.add(null, E_RoleType.Tower);
        this.createTowerPoints();
        //  log("initTower",this, this.tower)
    }
    createTowerPoints() {
        this.towerGiftNums = [0, 0, 0, 0]
        let pointTypes = [
            E_TowerPointType.Point1, E_TowerPointType.Point2, E_TowerPointType.Point3, E_TowerPointType.Point4
        ]
        for (let i = 0; i < 4; i++) {

            let towerPoint = this.towerPoints[i];
            if (!towerPoint) {
                towerPoint = new Role();
                this.towerPoints.push(towerPoint);
            }
            let pos = v2(-this.monsterSpace.width / 2 + (this.monsterSpace.width / 4 * (i + 0.5)), this.towerParentNd.position.y + 30)
            towerPoint.add('towerPoint', E_RoleType.TowerPoint, pointTypes[i], 1, pos);

        }
    }
    setRoleParentNd(nd1: Node, nd2: Node, n3: Node, n4: Node) {
        this.heroParentNd = nd1;
        this.monsterParentNd = nd2;
        this.towerParentNd = n3;
        this.skillParentNd = n4;
    }

    setRoleMoveSpace(monsterSpace: Node, heroSpace: Node) {
        let mspaceWidth = monsterSpace.getComponent(UITransform).width;
        let mspaceHeight = monsterSpace.getComponent(UITransform).height;
        let hspaceWidth = heroSpace.getComponent(UITransform).width;
        let hspaceHeight = heroSpace.getComponent(UITransform).height;
        this.monsterSpace.left = monsterSpace.position.x - mspaceWidth / 2;
        this.monsterSpace.right = monsterSpace.position.x + mspaceWidth / 2;
        this.monsterSpace.top = monsterSpace.position.y + mspaceHeight / 2;
        this.monsterSpace.bottom = monsterSpace.position.y - mspaceHeight / 2;
        this.monsterSpace.x = monsterSpace.position.x;
        this.monsterSpace.y = monsterSpace.position.y;
        this.monsterSpace.width = monsterSpace.getComponent(UITransform).width;
        this.monsterSpace.height = monsterSpace.getComponent(UITransform).height;

        this.heroSpace.left = heroSpace.position.x - hspaceWidth / 2;
        this.heroSpace.right = heroSpace.position.x + hspaceWidth / 2;
        this.heroSpace.top = heroSpace.position.y + hspaceHeight / 2;
        this.heroSpace.bottom = heroSpace.position.y - hspaceHeight / 2;
    }

    /**
         * 获取四叉树的推荐检测列表
         * @param checkRect 
         */
    getTreeColliderList(checkRect: Node) {
        if (this.quadTree) {
            return this.quadTree.retrieve(checkRect)
        }
        return []
    }

    //重置四叉树
    rebuildTree() {
        if (!this.checkRect) {
            this.checkRect = new Rect(this.monsterSpace.x, this.monsterSpace.y, this.monsterSpace.width, this.monsterSpace.height) //view.getViewportRect();
        }

        this.quadTree = new Quadtree(this.checkRect, 0);
        // for (let i = 0; i < this.heroRoles.length; i++) {
        //     let role = this.heroRoles[i];
        //     if (role && role.isRoleAlive()) {
        //         let node = role?.comp?.node;
        //         if (node) {
        //             this.quadTree.insert(node);
        //         }
        //     }
        // }
        for (let i = 0; i < this.monsterRoles.count; i++) {
            let role = this.monsterRoles.get(i);
            if (role && role.isRoleAlive()) {
                let node = role?.comp?.node;
                if (node) {
                    this.quadTree.insert(node);
                }
            }
        }


    }
    /**
     *  四叉树 目标范围内怪物
     *
     */
    checkNearMonster(orgNd: Node, range: number, attNum: number = 1): Role[] {
        if (range <= 0) {
            range = 10
        }
        let posList = [];
        let checkList = this.getTreeColliderList(orgNd);
        //checkList = checkList.filter(nd => nd.getComponent(UnitRoleComp).getRoleType() == findType);
        if (checkList.length <= 0) return posList;
        let num = 0;
        let orgPos = orgNd.getComponent(UnitRoleComp).getFromRole().data.pos;


        for (let i = 0; i < checkList.length; i++) {
            let obj = checkList[i];
            let comp = obj.getComponent(UnitRoleComp)
            let role = comp.getFromRole();
            let pos = role.data.pos;
            if (num >= attNum) break

            if (role && role.isRoleAlive()) {
                let isCollision: any = this.isCollision(orgPos, pos, range);
                if (isCollision) {
                    num += 1;
                    posList.push(role);

                }

            }

        }

        // log("checklist:", '攻击范围：', range, '预期数量：', attNum, '可检测数量：', checkList.length, '结果数量：', posList.length, 'num:', num, '怪物总数量:', this.monsterRoles.count)
        return posList;
    }
    checkLineMonster(orgNd: Node, range: number, attNum: number = 1): Role[] {
        if (range <= 0) {
            range = 10
        }
        let posList = [];
        let checkList = this.getTreeColliderList(orgNd);
        //checkList = checkList.filter(nd => nd.getComponent(UnitRoleComp).getRoleType() == findType);
        if (checkList.length <= 0) return posList;
        let num = 0;

        for (let i = 0; i < checkList.length; i++) {
            let obj = checkList[i];
            let comp = obj.getComponent(UnitRoleComp)
            let role = comp.getFromRole();
            let pos = role.data.pos;
            if (num >= attNum) break

            if (role && role.isRoleAlive()) {
                num += 1;
                posList.push(role);

            }

        }

        // log("checklist:", '攻击范围：', range, '预期数量：', attNum, '可检测数量：', checkList.length, '结果数量：', posList.length, 'num:', num, '怪物总数量:', this.monsterRoles.count)
        return posList;
    }
    isCollision(pos1: Vec2, pos2: Vec2, range: number) {
        if (!pos1 || !pos2) {
            return false
        }
        /*  const nodePos1 = node1.getPosition();
         const nodePos2 = node2.getPosition(); */
        /* let mag = pos1.clone().subtract(pos2).length();

        return mag <= range; */
        let dis = range * range;
        let rag = Math.abs(((pos1.y - pos2.y) * (pos1.y - pos2.y)) + ((pos1.x - pos2.x) * (pos1.x - pos2.x)));

        return rag <= dis;
    }

    /**查找用户 */
    findUser(userId: string, type?: E_RoleType): IUser {
        let user = this.heroUser.get(userId);
        /* if (type == E_RoleType.Hero) {
            user = this.heroUser.get(userId);
            log("暂未加入仙族阵营");
        }
        else if (type == E_RoleType.Monster) {
            user = this.monsterUser.get(userId);
            log("暂未加入妖族阵营");
        }
        else {
            user = this.heroUser.get(userId);
            if (!user) {
                user = this.monsterUser.get(userId);
            }

            if (!user) {
                log("暂未加入任何阵营");
            }
        } */
        return user
    }
    getUserRank(userId: string): number {
        let user = this.findUser(userId);
        if (!user) {
            return -1;
        }
        return user.rank;
    }
    getIUsers(): Map<string, IUser> {
        return this.heroUser
    }
    getUsers() {
        return this.heroRoles;
    }
    //用户杀敌 或 刷礼物 可添加积分
    addUserScore(userId: string, score: number, isKillScore: boolean = false, giftKey?: string) {
        let user = this.findUser(userId);
        if (user) {
            if (!user.score) {
                user.score = 0;
            }
            if (!user.killscore) {
                user.killscore = 0;
            }
            if (!user.giftscore) {
                user.giftscore = 0;
            }
            if (!user.gold) {
                user.gold = 0;
            }
            if (!user.giftTitleScore) {
                user.giftTitleScore = 0;
            }
            let s = Math.floor(score);
            user.score += s;
            this.addTotalScore(s * 2);
            //击杀积分
            if (isKillScore) {
                user.killscore += s;
                this.addtotalKillScore(s * 2);
            } else {
                user.giftscore += s;


                if (giftKey != C_GiftKey.Like && giftKey != C_GiftKey.SixSix) {
                    user.giftTitleScore += s;
                    user.gold += s;
                }

                this.addTotalGiftScore(s * 2);
                this.checkTask(user);
            }
        }

        xcore.event.raiseEvent(E_EVENT.GameScore);
    }
    //用户输出攻击力
    addUserAtkPower(userId: string, power: number) {
        let user = this.findUser(userId);
        if (user) {
            if (!user.atkPower) {
                user.atkPower = 0;
            }
            user.atkPower += Math.floor(power);
            // log("atkPoser", userId, ":", user.atkPower)
        }
    }
    addTotalScore(score: number) {
        if (!this.gameTotalScore) {
            this.gameTotalScore = 0;

        }

        this.gameTotalScore += score;

        xcore.event.raiseEvent(E_EVENT.GameScore);
    }
    addTotalGiftScore(score: number) {
        if (!this.gameTotalGiftScore) {
            this.gameTotalGiftScore = 0;

        }

        this.gameTotalGiftScore += score;
    }
    addtotalKillScore(score: number) {
        if (!this.gameTotalKillScore) {
            this.gameTotalKillScore = 0;

        }

        this.gameTotalKillScore += score;
    }
    getTotalScore() {
        if (!this.gameTotalScore) {
            this.gameTotalScore = 0;
        }
        return this.gameTotalScore;
    }
    getTotalGiftScore() {
        if (!this.gameTotalGiftScore) {
            this.gameTotalGiftScore = 0;
        }
        return this.gameTotalGiftScore;
    }
    getTotalKillScore() {
        if (!this.gameTotalKillScore) {
            this.gameTotalKillScore = 0;
        }
        return this.gameTotalKillScore;
    }
    checkIfAbleGift() {
        return this.gameState != E_GameState.None
    }
    //
    async checkExchangeWing(userId: string, content: string) {
        let userData = this.findUser(userId);
        if (!userData) {
            console.log("user not exist", userId);
            return
        }

        let config = ConfigHelper.getInstance().getExchaengConfigByNameContent(content);

        if (config) {
            if (userData.score < config.yingYunConsume) {
                xcore.ui.showToast('灵韵不足');
                return;
            }
            console.log("wing exchange", userId, content, config);

            Net.costGold(config.yingYunConsume, userId).then(async res => {
                if (res.data == 1) {
                    await Net.addDebris(userId, config.rewardNum, config.skinFragmentId);

                    let skinConfig = ConfigHelper.getInstance().getSkinConfigByDebrisId(config.skinFragmentId);
                    if (skinConfig) {
                        this.checkIfExchangeSkin(userData, config.skinFragmentId, skinConfig.skinFragmentNum, skinConfig.jsonId, skinConfig.period, config.skinFragmentId)

                    }
                } else {
                    xcore.ui.showToast('灵韵不足');
                }
            });

        }

    }
    checkIfExchangeSkin(user, skinFragment, skinFragmentNum, skinId, time, debrisId) {
        let isHaveSkin = user.skins.find(e => e.prop == skinId)
        let maxLev = ConfigHelper.getInstance().getSkinMaxLevelBySkinId(skinId);
        log("maxLev:", maxLev, isHaveSkin?.level, isHaveSkin)
        if (!isHaveSkin || isHaveSkin?.level < maxLev) {
            let debris = user.debris.find(e => e.prop == skinFragment);
            let isAbleUnlock = debris?.num >= skinFragmentNum;

            // log("checkIfRewardSkin:", isAbleUnlock, skinFragment, debris?.num, skinFragmentNum, time, debrisId, skinId);
            if (isAbleUnlock) {
                Net.exchangeSkin(user.userId, skinId, skinFragmentNum, time, debrisId);
                let id = skinId;
                debris.num -= skinFragmentNum;
                let skin = { prop: skinId, time: TimeUtil.getServerTime() + (time * 1000), level: isHaveSkin ? isHaveSkin.level += 1 : 1 }
                log("up", skin)
                user.skins.push(skin)
                //  xcore.ui.addView(C_View.ViewSkinReward, { skinId: id, user, lev: skin.level }, 1);

                xcore.event.raiseEvent(E_EVENT.GiftMessage, {
                    type: E_GiftMessageType.SkinReward,
                    skinId: id, user, lev: skin.level
                })
            }
        } else {
            log('已有皮肤' + skinId)
        }


    }
    async checkLingyunExchange(userId: string, type: E_RoleType, nickName?: string, iconUrl?: string, content?: string) {
        let user = this.findUser(userId);
        if (user) {
            return
        }
        if (xcore.gameData.gameType == 1) {
            let config = ConfigHelper.getInstance().getDungeonExchangeConfigByJsonId(xcore.gameData.gameTypeJsonId);
            console.log('content', content, config.name)
            if (content != config.name) {
                xcore.ui.showToast(`兑换副本错误 \n错误的副本${content} 当前副本${config.name}`);
                return
            }
            if (config) {
                //灵韵
                let key3 = ConfigHelper.getInstance().getLinyunKeyByForever();
                Net.getRoleRankInfo(key3, [userId], (datas: any) => {
                    console.log('灵韵', datas[0].score, content);
                    if (datas[0].score >= config.yingYunConsume) {
                        Net.costGold(config.yingYunConsume, userId).then(res => {
                            if (res.data == 1) {
                                this.addUser(userId, type, nickName, iconUrl, true);
                            } else {
                                xcore.ui.showToast('灵韵不足');
                            }
                        });

                    } else {
                        xcore.ui.showToast('灵韵不足');
                    }
                }, false);
            }

        }
    }
    /********************************************指令**********************************/
    /** 选择阵营指令  注：改版本现修改为塔防类型 不需要加阵营 只有仙族阵营*/
    async addUser(userId: string, type: E_RoleType, nickName?: string, iconUrl?: string, isExchangeChnage: boolean = false) {
        log("adduser", type)


        let user = this.findUser(userId);
        if (user) {
            log(`已是${user.type == E_RoleType.Hero ? '仙族' : '妖族'}阵营玩家，无需再加入${type == E_RoleType.Hero ? '仙族' : '妖族'}`);
            return
        }
        let bossChangeTimeTxt;
        //挑战副本模式
        if (xcore.gameData.gameType == 1 && !isExchangeChnage) {
            let config = ConfigHelper.getInstance().getDungeonConfigByJsonId(xcore.gameData.gameTypeJsonId);
            if (config) {
                console.log("", config.type, config.limitation)
                // let txt = ["日", "周", "月"][config.type - 1];
                let time;
                if (config.type == 1) {
                    time = TimeUtil.formatTimestampToDate(TimeUtil.getServerTime(), '-');
                } else if (config.type == 2) {
                    time = TimeUtil.getFirstDayOfTheWeek(TimeUtil.formatTimestampToDate(TimeUtil.getServerTime(), '-'));
                } else {
                    time = TimeUtil.getFirstDayOfTheMonth();
                }

                let key = `bossFight_${xcore.gameData.gameTypeJsonId}`;
                await Net.getPlayerOperate(userId, key);
                if (xcore.gameData.playersOperation[userId][key] != null) {
                    let oldDatas = xcore.gameData.playersOperation[userId][key].split('_');
                    let oldTime = oldDatas[0];
                    let num = Number(oldDatas[1]);
                    //不是相同时间 重置次数
                    if (oldTime != time) {
                        num = 0;
                    }
                    if (num >= config.limitation) {
                        log("已超过挑战次数")
                        xcore.ui.showToast(`${nickName || '匿名用户'}已超过挑战次数\n限制${config.limitation} 已玩${num}`);
                        return;
                    } else {
                        num += 1;
                        bossChangeTimeTxt = `${time}_${num}`
                        // Net.setPlayerOperate(userId, key, `${time}_${num}`);
                    }
                } else {
                    let txt = `${time}_1`;
                    bossChangeTimeTxt = txt
                    // Net.setPlayerOperate(userId, key, txt);
                }
            }
        }
        this.lastUser.push({
            userId, type, nickName, iconUrl
        })
        //加入仙族阵营
        if (type == E_RoleType.Hero) {

            let role = this.createHero(userId, type, nickName, iconUrl);
            let user = {
                userId,
                type,
                role,
                nickName,
                iconUrl,
                giftKey: [
                    {
                        key: C_GiftKey.JoinHero, num: 1

                    }
                ],
                gold: 0,
                score: 0,
                poolScore: 0,
                /*  poolgiftScore: 0,
                 poolkillScore: 0, */
                upscore: 0,
                killboss: 0,
                killmonster: 0,
                helptower: 0,
                skins: [],
                debris: [],
                bossChangeTimeTxt
            } as IUser
            this.heroUser.set(userId, user);
            this.heroRoles.push(role);
            this.refreshHeroZindex();
            await Net.roleJoin(nickName, iconUrl, userId);



            //皮肤信息
            await this.updateSkinInfo(userId);
            //宝珠信息
            this.updateDimondInfo(userId);
            this.needUpdateUser.push(userId);
            if (!this.onRefreshUserRank) {
                this.onRefreshUserRank = Tool.debounce(async () => {
                    //  let key = ConfigHelper.getInstance().getRankKeyByWeek();
                    let key = ConfigHelper.getInstance().getRankKeyByMonth();
                    let list = this.needUpdateUser;
                    let ids = '';
                    list.forEach((element, i) => {
                        ids += i == 0 ? element : `,${element}`
                    });

                    this.needUpdateUser = [];
                    //碎片信息
                    Net.getDebrisInfo(ids, (datas) => {
                        for (let i = 0; i < list.length; i++) {
                            let playerId = list[i];
                            let userdata = this.findUser(playerId);
                            let debris = datas.filter(e => e.playerId == playerId);
                            userdata.debris = debris;
                        }
                    })

                    await Net.getRoleRankInfo(key, list, (datas: any) => {
                        datas.forEach(user => {
                            let userdata = this.findUser(user.playerId);
                            if (userdata) {
                                userdata.rank = user.rank
                                if (userdata.role) {
                                    userdata.role.data.weekScore = user.score;
                                    userdata.role.checkSwitchSkin();
                                }

                            }
                            // userdata.role?.comp.setRankInfo();
                        });
                    });
                    let key2 = ConfigHelper.getInstance().getGiftRankKeyByForever();
                    await Net.getRoleRankInfo(key2, list, (datas: any) => {
                        datas.forEach(user => {
                            let userdata = this.findUser(user.playerId);
                            if (userdata) {
                                userdata.giftTitleScore = user.score
                                userdata.role?.checkGiftTitle(userdata.giftTitleScore)
                            }
                            // userdata.role?.comp.setRankInfo();
                        });
                    }, false);
                    //灵韵
                    let key3 = ConfigHelper.getInstance().getLinyunKeyByForever();
                    Net.getRoleRankInfo(key3, list, (datas: any) => {
                        datas.forEach(user => {
                            let userdata = this.findUser(user.playerId);
                            if (userdata) {
                                userdata.lingyun = user.score;
                            }
                        });
                    }, false);
                }, 1000, false);

            }
            this.onRefreshUserRank();


        }

        //加入妖族阵营 
        /*  else if (type == E_RoleType.Monster) {
             let point = this.createMonsterPoint(userId);
             this.monsterUser.set(userId, {
                 userId,
                 type,
                 roles: new List<Role>(true),
                 point,
                 giftKey: [
                     {
                         key: C_GiftKey.JoinMonster,
                         num: 1
                     }
                 ]
             })
             this.monsterCreatePointRoles.push(point);
         } */
        EventManager.getInstance().raiseEvent(E_EVENT.BaseInfo, {
            heroUserInfo: this.heroUser,
            monsterRoles: this.monsterRoles
        })
    }
    updateSkinInfo(userId: string) {
        Net.getSKinInfo(userId, async (datas) => {
            let userdata = this.findUser(userId);
            userdata.skins = datas;
            let defaultSkinId = '170001'
            if (!userdata.skins || userdata.skins.length <= 0) {
                let timeDiff = 60 * 60 * 24 * 30// Math.floor(TimeUtil.getTimeUntilNextSaturday() / 1000);
                await Net.rewardSkin(userId, 4, [{
                    num: 1, prop: defaultSkinId,
                    time: timeDiff
                }])
                userdata.skins.push({
                    prop: defaultSkinId,
                    time: timeDiff,
                    useStatus: null,
                })
            }
            let selectedSkin = userdata.skins.find(e => e.useStatus == 1);
            if (!selectedSkin) {
                let defaultSkin = userdata.skins.find(e => e.prop == defaultSkinId);
                Net.setSelectSkin(userId, defaultSkinId);
                defaultSkin && (defaultSkin.useStatus = 1);
                selectedSkin = defaultSkin;
            }
            userdata.skins.forEach(e => {
                let time = e.time;
                let t = TimeUtil.getServerTime() + time * 1000;
                e.time = t;
            })
            userdata.role.switchSkin(selectedSkin?.prop, selectedSkin?.level)
            log('皮肤：', userId, userdata.skins)

        })
    }

    async updateDimondInfo(userId: string) {
        /*  await Net.adDimond(userId, '260001', 3);
         await Net.adDimond(userId, '260002', 3);
         await Net.adDimond(userId, '260003', 3);
         await Net.adDimond(userId, '260004', 3);
         await Net.adDimond(userId, '260005', 3); */
        Net.getDimondInfo(userId).then(res => {
            let userdata = this.findUser(userId);
            if (userdata && res.data.length > 0) {
                log('dimondInfo:', res.data)

                userdata.role.updateDimondInfo(res.data);
            }

        })

    }
    updateGiftTitle() {

    }
    updateTowerPointLev(giftNum: number = 1) {
        let targetIndex = 0
        let targetRole = this.towerPoints[targetIndex];
        let lev = this.towerPoints[targetIndex].data.lev;
        let maxLev = ConfigHelper.getInstance().getTowerPointMaxLev();

        for (let i = 0; i < this.towerPoints.length; i++) {
            let role = this.towerPoints[i];
            if (lev > role.data.lev) {
                targetIndex = i;
                targetRole = role;
                break
            }
        }
        let addGiftNum = giftNum > this.towerPointUpGiftNum ? this.towerPointUpGiftNum : giftNum
        this.towerGiftNums[targetIndex] += addGiftNum;
        let uplev = 1 + Math.floor(this.towerGiftNums[targetIndex] / this.towerPointUpGiftNum);

        if (targetRole.data.lev < maxLev && targetRole.data.lev < uplev) {
            // log("升级炮台", uplev, targetRole.data.lev, this.towerGiftNums[targetIndex], this.towerPointUpGiftNum)
            targetRole.towerPointupLev(uplev)
            if (giftNum > this.towerPointUpGiftNum) {
                this.updateTowerPointLev(giftNum - addGiftNum)
            }
        }
    }
    updateTowerPoint(giftNum: number = 1) {
        let oldLevs = [];
        for (let i = 0; i < this.towerPoints.length; i++) {
            oldLevs.push(this.towerPoints[i].data.lev);
        }
        this.updateTowerPointLev(giftNum);
        for (let i = 0; i < this.towerPoints.length; i++) {
            let targetRole = this.towerPoints[i]
            let oldLev = oldLevs[i];
            let nowLev = targetRole.data.lev;
            if (oldLev < nowLev) {
                xcore.event.raiseEvent(E_EVENT.GiftMessage, {
                    type: E_GiftMessageType.SkillUp,
                    fromLev: oldLev,
                    anim: targetRole.data.moveAnimation, lev: nowLev, towerPointType: targetRole.data.monsterType
                })

            }
        }
        //  log('升级炮台uplev', oldLevs, this.towerGiftNums)


    }
    /**换皮肤指令 */
    doSwitchSkin(user: IUser, skinName: string, selectSkinId?: string) {
        let jsonId = selectSkinId ? selectSkinId : ConfigHelper.getInstance().getSkinJsonIdByContent(skinName);
        let skinId = jsonId;
        log('doSwitchSkin:', user, skinName, skinId)
        if (user.type == E_RoleType.Hero) {
            let role = user.role;
            role?.switchSkin(skinId);
        }
        // else if (user.type == E_RoleType.Monster) {
        //     let role = user.point;
        //     role?.switchSkin(skinId);

        // }

        user.giftKey.push(
            {
                key: skinId,
                num: 1
            }
        );
    }

    //礼物指令
    addGift(user: IUser | string, giftKey: string, type: E_SkillType | E_MonsterType, keyNum: number) {
        log('addgift', user, giftKey, type)
        if (typeof user == "string") {
            user = this.findUser(user);
        }
        //log("giftgift", giftKey, user.type, type)
        if (!user) {
            return
        }
        if (!type) {
            error("addGift key err");
            return
        }

        //积分同步
        let giftConfig = ConfigHelper.getInstance().getGiftConfigByJsonId(giftKey);
        // 该用户指令累计次数
        let totalGiftNum = this.getUserGiftByKey(user, giftKey, keyNum, giftConfig.integra);


        // 仙族  加技能
        if (user.type == E_RoleType.Hero) {
            let role = user.role;

            //配置表读取礼物累计次数
            let greatSkillType = C_GreatSkill[type] as E_SkillType;
            let bigNum = ConfigHelper.getInstance().getGreateSkillAbleLevelByNum(greatSkillType, totalGiftNum);
            role.updateSkill(type as E_SkillType, greatSkillType, bigNum, keyNum);

            //礼物消息提示
            if (type != E_SkillType.Attack) {
                xcore.event.raiseEvent(E_EVENT.GiftMessage, {
                    type: E_GiftMessageType.Gift,
                    userId: user.userId,
                    name: user.nickName,
                    avatar: user.iconUrl,
                    giftType: giftKey,
                    num: keyNum,
                    totalNum: totalGiftNum,
                })

                xcore.event.raiseEvent(E_EVENT.GiftMessage, {
                    type: E_GiftMessageType.GetSmallSkill,
                    userId: role.data.userId,
                    name: role.data.nickName,
                    avatar: role.data.iconUrl,
                    skillType: type,
                    num: keyNum
                })

                // if (bigNum && isCreateBigSkill) {
                //     xcore.event.raiseEvent(E_EVENT.GiftMessage, {
                //         type: E_GiftMessageType.GetBigSkill,
                //         userId: role.data.userId,
                //         name: role.data.nickName,
                //         avatar: role.data.iconUrl,
                //         skillType: greatSkillType,
                //         num: 1,
                //         lev: bigNum
                //     })
                // }
            }


            let addScore = giftConfig.integra * keyNum
            this.addUserScore(role.data.userId, addScore, false, giftKey);




        }

        // 用户信息同步
        EventManager.getInstance().raiseEvent(E_EVENT.BaseInfo, {
            heroUserInfo: this.heroUser,
            monsterRoles: this.monsterRoles
        })

    }

    //获取当前用户已有该指令累计次数
    getUserGiftByKey(user: IUser, key: string, num: number, price: number) {
        //改用户历史指令记录
        let keyData = user.giftKey.find(e => e.key == key);
        //如果key是礼物key 返回礼物type
        let giftType = ConfigHelper.getInstance().getGiftTypeByJsonId(key);
        if (!keyData) {
            user.giftKey.push({
                key, num, giftType, price
            })
            return num
        } else {
            keyData.num += num;
            return keyData.num
        }
    }




    //创建本轮次怪物
    createRoundMonsters() {

        this.tempeachCreateMonsterTime = this.eachCreateMonsterTime

        if (this.leftRoundMonsterNum <= 0) {
            this.gameRound += 1;
            this.refreshLevelMonsterConfig();

            return
        }

        let num = this.leftRoundMonsterNum < this.eachCreateMonsterNum ? this.leftRoundMonsterNum : this.eachCreateMonsterNum
        for (let i = 0; i < num; i++) {

            // let monsterConfig = ConfigHelper.getInstance().getMonsterConfigByJsonId(this.roundMonsterJsonId);
            // let type = monsterConfig.monsterType;
            // let lev = monsterConfig.monsterLevel;
            this.createMonster(null, null, this.roundMonsterJsonId);
        }
        this.leftRoundMonsterNum -= num;
        log("leftRoundMonsterNum", this.leftRoundMonsterNum, num, this.newGameLevel)

        this.emitRoundData();

    }
    goNextGameLevel() {
        this.newGameLevel = 0;
    }
    setAbleAutoNextGame(boolean: boolean) {
        this.autoNextGameLevel = boolean;
        if (this.autoNextGameLevel) {
            this.checkOpenBox();
            this.newGameLevel = 0;
        }
        if (this.gameState == E_GameState.Pause) {
            this.gameState = E_GameState.Resume;
        }
    }
    setGameOnlineMode(isOnline: boolean) {
        this.isOnline = isOnline;
    }
    refreshLevelMonsterConfig() {

        let roundConfig = this.gameRoundConfigs[this.gameRound];
        console.log("refreshLevelMonsterConfig", this.gameRound)
        if (!roundConfig) {
            // xcore.ui.showToast('游戏结束')
            console.log("游戏结束")
            this.nextRoundTime = 100;
            if (this.monsterNum <= 0) {
                this.checkOpenBox(true)
            }
            return
        }
        if (roundConfig.lotteryId) {
            this.lotteryId = roundConfig.lotteryId;
            this.freeLotteryId = roundConfig.freeLotteryId;
        }
        if (roundConfig.firstPassLotteryId) {
            this.firstPassLotteryId = roundConfig.firstPassLotteryId;
        }
        if (roundConfig.beadlotteryId) {
            this.dimondId = roundConfig.beadlotteryId;
            this.freeDimondId = roundConfig.freeBeadlotteryId;

            log(" this.dimondId", this.dimondId)
        }
        this.gameLevelJsonId = roundConfig.gameLevel;
        if (this.gameLevelJsonId) {
            let levelConfig = ConfigHelper.getInstance().getLevelConfigByJsonId(this.gameLevelJsonId);
            //关卡加成系数
            this.scoreAddedValue = levelConfig.scoreAddedValue;
            if (this.gameLevel != levelConfig.gameLevel) {
                if (!this.firstJoinGame && levelConfig.gameLevel != 1) {
                    this.newGameLevel = levelConfig.gameLevel;
                    //xcore.event.raiseEvent(E_EVENT.NextGameLevel, levelConfig.gameLevel)

                }
                log(`关卡更新${this.gameLevel}-->${levelConfig.gameLevel}`)
            } else {
                this.newGameLevel = 0;
            }

            this.gameLevel = levelConfig.gameLevel;
        }

        this.firstJoinGame = false;
        this.nextRoundTime = roundConfig.refreshInterval;
        this.leftRoundMonsterNum = roundConfig.monsterTotal;
        this.roundMonsterJsonId = roundConfig.monsterId;
        this.eachCreateMonsterTime = roundConfig.monsterInterval || 1;
        this.tempeachCreateMonsterTime = this.eachCreateMonsterTime;
        this.eachCreateMonsterNum = roundConfig.monsterNum || 3;

        //xcore.ui.showToast(roundConfig.name);
        // if (DEBUG) {
        //     this.nextRoundTime = 2;
        // }
        //log('refreshInterval', this.nextRoundTime)
        if (this.newGameLevel == 0) {
            xcore.ui.addView(C_View.ViewRoundToast, { desc: roundConfig.name })

        }

        //boss出场动画
        if (roundConfig.preview) {
            xcore.event.raiseEvent(E_EVENT.NewsEffect, { anim: roundConfig.preview, anim02: roundConfig.admission })
        }



    }
    emitRoundData() {
        let config = this.gameRoundConfigs[this.gameRound];
        if (!config) return
        this.roundEmitData = {
            roundDesc: config.name
        } as any
        if (config.type == E_MonsterTag.normal) {
            this.roundEmitData.monsterId = config.monsterId;
            this.roundEmitData.leftNum = this.leftRoundMonsterNum;
            this.roundEmitData.monsterSub = config.prompt;
        }

        let bossConfig = ConfigHelper.getInstance().getBossConfigStartBy(config.refresh);

        if (bossConfig) {
            this.roundEmitData.bossId = bossConfig.monsterId;
            this.roundEmitData.bossRoundDesc = bossConfig.name;
            this.roundEmitData.bossSub = bossConfig.prompt;
        }
        xcore.event.raiseEvent(E_EVENT.Round, this.roundEmitData);


    }
    /**妖族 创建怪物 */
    async createMonster(type: E_MonsterType, lev: number, jsonId?: string, pos?: Vec2): Promise<Role> {

        let role = null as Role;
        if (this.rolePool.count > 0) {
            role = this.rolePool.shift();
        } else {
            role = new Role();
        }
        this.monsterNum += 1;
        this.monsterRoles.push(role);
        await role.add('monster', E_RoleType.Monster, type, lev, pos, null, null, jsonId);
        return role
    }

    /**仙族 创建英雄 */
    createHero(userId: string, type: E_RoleType, nickName: string, avatar: string): Role {
        let role = new Role();
        role.add(userId, type, null, 1, null, nickName, avatar);
        return role;
    }
    getRandomPosByType(type: E_RoleType): Vec2 {
        if (type == E_RoleType.Hero) {

            return v2(Tool.randomNumber(this.heroSpace.left, this.heroSpace.right), Tool.randomNumber(this.heroSpace.bottom, this.heroSpace.top));

        } else if (type == E_RoleType.Monster) {
            return v2(Tool.randomNumber(this.monsterSpace.left, this.monsterSpace.right), Tool.randomNumber(this.monsterSpace.bottom, this.monsterSpace.top));
        } else {
            return v2(0, 0)
        }
    }


    /**离塔距离 */
    getDistanceFromTower(ownRole: Role) {
        if (!ownRole?.comp?.node) return
        return Math.abs(ownRole.comp.node.position.y - this.towerParentNd.position.y);
    }
    /**攻击塔 */
    attackTower(ownRole: Role) {

        if (!this.tower) return
        if (!ownRole.isRoleAlive()) return
        if (!this.towerTw) {
            let nd = this.tower.getAnimNode();
            if (!nd) return
            this.towerTw = tween(nd)
                .to(
                    0.1, { scale: v3(1.02, 1.02, 1.02) },

                )
                .by(0.1, { position: v3(0, 10) })
                .by(0.1, { position: v3(0, -10) })
                .to(
                    0.05, { scale: v3(1, 1, 1) },

                )
            this.towerTwFunc = Tool.throttle(() => {
                this.towerTw.stop();
                nd.setPosition(Vec3.ZERO)
                nd.scale = v3(1, 1, 1)
                this.towerTw.start()
            }, 800)

        }
        this.towerTwFunc();
        let num = this.tower.hurt(ownRole.data.atkNum/*  * (1 + ownRole.data.atkNumBuff) */);
        if (!num || num <= 0) {
            log('游戏结束 塔防失守');

            this.fightOver(false);
        }
    }
    addMaxHpTower(num: number) {
        if (!this.tower) return
        this.tower.towerUpHp(num);

    }
    saveTower(num: number, time: number, fromUserId: string) {
        if (!this.tower) return
        this.tower.saveTower(num, 5);
        let user = this.findUser(fromUserId);
        if (user) {
            if (!user.helptower) {
                user.helptower = 0;
            }
            user.helptower += num;

            log("help:", user.helptower)
        }
    }
    upLevTower(num: number) {
        if (!this.tower) return
        this.giftPowerNum += num;
        let lev = Math.floor(this.giftPowerNum / 10) + 1;
        this.tower.towerUpLev(lev, this.gameState == E_GameState.Gameing);
    }

    buffTower(time: number) {
        if (!this.tower) return
        this.tower.setInvincible(time);
    }
    //获取攻击目标角色
    getAttackNearEnemys(ownRole: Role, attackNum: number = 1, isNormalAttack: boolean = false): Role[] {

        if (ownRole.data.type == E_RoleType.Hero || ownRole.data.type == E_RoleType.TowerPoint) {
            // let targets = [] as Role[];
            //锁定目标优先
            if (this.isOldTargetPriority && !isNormalAttack) {
                let target = this.attackTargets.get(ownRole.data.userId);

                if (target && target.isRoleAlive()) {
                    return [target];
                }
            }

            let targets = this.filterNearEnemy(ownRole, attackNum);
            if (targets[0]) {
                this.attackTargets.set(ownRole.data.userId, targets[0]);
            }
            return targets;
        }

        else if (ownRole.data.type == E_RoleType.Monster) {
            return this.getHeroRoles();
        }

    }
    getTower() {
        return [this.tower]
    }
    getHeroRoles() {
        return this.heroRoles;
    }
    getMonsters() {
        return this.monsterRoles;
    }
    //筛选目标怪物
    /**
     * @param ownRole   发起role
     * @param attackNum 筛选数量
     * @param attackRag 筛选范围
     * @returns 
     */
    filterNearEnemy(ownRole: Role, attackNum: number, attackRag: number = 500): Role[] {
        let ownNd = ownRole.comp?.node;
        if (!ownNd) return [];
        if (ownRole.data.type == E_RoleType.Hero || ownRole.data.type == E_RoleType.TowerPoint) {

            let nearXMonster = this.monsterRoles.filter(role => {
                let xRag = Math.abs(role.data.pos.x - ownNd.position.x);

                return xRag <= attackRag && role.isRoleAlive();
            })

            //目标user不足 扩大筛选范围
            if (nearXMonster.length <= 0) {

                //超过搜索范围还是没有目标
                if (attackRag + 30 >= view.getVisibleSize().width) {
                    return [];
                } else {
                    return this.filterNearEnemy(ownRole, attackNum, attackRag + 30);
                }

            }

            nearXMonster.sort((a, b) => b?.comp?.node?.position.y - a?.comp?.node?.position.y);
            if (nearXMonster.length > attackNum) {
                nearXMonster = nearXMonster.slice(0, attackNum);
            }

            return nearXMonster

        }
    }
    //目标附近范围role筛选
    filterEnemyByDistance(targetPos: Vec2, distance: number) {
        let dis = distance * distance;
        let nearMonster = this.monsterRoles.filter(role => {
            let rag = Math.abs((targetPos.y - role.data.pos.y) * (targetPos.y - role.data.pos.y) - (targetPos.x - role.data.pos.x) * (targetPos.x - role.data.pos.x));
            return rag <= dis && role.isRoleAlive();
        })

        return nearMonster;
    }


    //妖族怪物被击杀
    killMonster(role: Role, fromUserId?: string) {


        if (fromUserId && this.attackTargets.get(fromUserId)) {
            this.attackTargets.delete(fromUserId);
        };
        if (this.monsterRoles.has(role)) {
            let r = this.monsterRoles.remove(role);
            setTimeout(() => {
                this.rolePool.push(r);
            }, 200);
        } else {
            role.destroy(true);
            warn('monster killed no find')
        }

        this.monsterNum -= 1;

        log('monsterRoles:', this.newGameLevel, this.monsterRoles.count, this.monsterNum, this.monsterParentNd.children.length)
        if (/* this.leftRoundMonsterNum <= 0 && */ /* this.monsterRoles.count <= 0 || */ this.monsterNum <= 0 || this.monsterParentNd.children.length <= 0) {

            if (this.monsterRoles.count > 0) {
                for (let i = 0; i < this.monsterRoles.count; i++) {
                    let role = this.monsterRoles.get(i);
                    if (role) {
                        role.destroy(true);
                    }
                }
                warn('monsterRoles clear');
                this.monsterRoles.clear();

            }
            if (!this.gameRoundConfigs[this.gameRound]) {
                /* if (xcore.gameData.gameLev != this.gameLevel) {
                    if (this.firstPassLotteryId) {
                        this.lotteryId = this.firstPassLotteryId;
                    }
                } */
                this.checkOpenBox(true);

            } else {
                if ((this.lotteryId || this.dimondId) && this.newGameLevel) {
                    this.checkOpenBox();
                } else {
                    if (!this.newGameLevel) {

                        this.createRoundMonsters();
                    } else {
                        if (!this.autoNextGameLevel) {
                            xcore.event.raiseEvent(E_EVENT.NextGameLevel, this.newGameLevel)
                        } else {
                            this.createRoundMonsters();
                        }
                    }
                }



                return
            }

        }

    }
    //检查是否有宝箱
    checkIfHaveBox() {
        //log('checkIfHaveBox:', this.lotteryId, this.newGameLevel, this.monsterNum, this.monsterParentNd.children.length, this.monsterRoles.count, this.leftRoundMonsterNum)
        if ((this.lotteryId || this.dimondId) && this.newGameLevel && (this.monsterNum <= 0 || this.monsterParentNd.children.length <= 0 || this.monsterRoles.count <= 0)) {
            this.checkOpenBox();
        }
    }
    //开宝箱
    checkOpenBox(isGameOver: boolean = false) {
        if (this.lotteryId || this.dimondId) {
            let maxLev = ConfigHelper.getInstance().getConstantConfigByKey('maxLevel') | 120;
            let isPassLottery = (isGameOver && this.firstPassLotteryId && xcore.gameData.gameLev != maxLev)
            let id = isPassLottery ? this.firstPassLotteryId : this.lotteryId;
            let freeid = this.freeLotteryId;
            let dimondId = this.dimondId;
            let freeDimondId = this.freeDimondId;
            this.lotteryId = null;
            this.freeLotteryId = null;
            this.dimondId = null;
            this.firstPassLotteryId = null;
            this.gameState = E_GameState.Pause;
            /* if (isPassLottery) {
                xcore.gameData.gameLev = this.gameLevel;
            } */
            console.log('最高关卡：', maxLev, "是否通关", isGameOver, '是否有通关皮肤：', isPassLottery, '结算关卡：', this.gameLevel, '历史最高关卡：', xcore.gameData.gameLev);
            // let targetScore = ConfigHelper.getInstance().getConstantConfigByKey('lotteryScore') || 200;
            let userNum = ConfigHelper.getInstance().getConstantConfigByKey('lotteryNum') || 10;
            var arrayObj = Array.from(this.heroUser);
            arrayObj.sort(function (a, b) { return b[1].score - a[1].score });
            let users = arrayObj.filter((userG, i) => {
                //let user = userG[1];
                return /* user.giftscore >= targetScore &&  */i <= userNum - 1;
            })
            log('targetscore', userNum, arrayObj)
            xcore.ui.addView(C_View.ViewOpenBox, {
                dimondId, lotteryId: id, freeid, freeDimondId, isPassLottery, users, cb: () => {
                    if (isGameOver) {
                        this.fightOver(true)
                    } else {
                        this.gameState = E_GameState.Resume;
                        if (!this.newGameLevel) {

                            this.createRoundMonsters();
                        } else {
                            if (!this.autoNextGameLevel) {
                                xcore.event.raiseEvent(E_EVENT.NextGameLevel, this.newGameLevel)
                            } else {
                                this.createRoundMonsters();
                            }
                        }
                    }

                    log('resume', this.newGameLevel, this.autoNextGameLevel)
                }
            });
        } else if (isGameOver) {
            this.fightOver(true)
        }


    }
    //技能回收
    killSkill(skill: Skill) {
        let isPut = this.skillPool.push(skill);
        log('poolNum:', this.skillPool.count, isPut)
    }

    //获取技能
    getSkill(): Skill {

        let skill = this.skillPool.shift();
        if (!skill) {
            skill = new Skill();
            log("get new skill")
        }

        log("pool1", this.skillPool)
        return skill
    }
    //buff回收
    killBuff(buff: Buff) {
        this.buffList.remove(buff);
        this.buffPool.push(buff);

    }
    //获取buff
    getBuff(): Buff {
        let buff = null as Buff;
        if (this.buffPool.length > 0) {
            buff = this.buffPool.shift();

        } else {
            buff = new Buff();
        }
        this.buffList.push(buff);
        return buff
    }
    setGameOverTime(time: number) {
        this.gameoverTime = time;
    }
    checkLastUser(cb?: Function) {
        //挂机模式老用户加入
        if (this.isOnline) {
            for (let i = 0; i < this.lastUser.count; i++) {
                let user = this.lastUser.get(i);
                if (user) {
                    this.addUser(user.userId, user.type, user.nickName, user.iconUrl);
                }
            }
            cb && cb()
        }
    }

    //战斗开始
    async fightStart(gameoverTime: number = 1000000000001, hp: number) {
        if (xcore.gameData.gameType == 0) {
            let levConfig = ConfigHelper.getInstance().getLevelConfigByLevel(xcore.gameData.gameSelectLev);
            this.gameRoundConfigs = ConfigHelper.getInstance().getMonsterRefreshConfigByLevelJsonId(levConfig?.jsonId);
        } else {
            this.gameRoundConfigs = ConfigHelper.getInstance().getDungeonRefreshConfigByLevelJsonId(xcore.gameData.gameTypeJsonId);
        }

        if (!this.gameRoundConfigs || this.gameRoundConfigs.length <= 0) {
            xcore.ui.showToast('配置错误');
            xcore.gameData.gameSelectLev = 1;
            xcore.event.raiseEvent(E_EVENT.GameConfig);
            setTimeout(() => {
                xcore.event.raiseEvent(E_EVENT.GameReplay);
            }, 50);
            return
        }
        console.log("配置：", gameoverTime, this.gameRoundConfigs)
        console.log('开始游戏', '开始关卡：', xcore.gameData.gameSelectLev, '已完成最高关卡：', xcore.gameData.gameLev)
        try {
            if (xcore.channel == E_Channel.TIKTOK) {
                // await Net.getCombatId()
                // await Net.relogin(xcore.channel, xcore.gameData.channelId, xcore.gameData.token, null, null, xcore.gameData.appId, xcore.gameData.combatId);
                await Net.startFight(xcore.gameData.combatId, xcore.gameData.gameMode);
            }
            this.monsterNum = 0;
            this.firstJoinGame = true;
            xcore.gameData.oldRankInfo = {}
            //this.gameTotalScore = 0;
            this.gameoverTime = gameoverTime;
            this.newGameLevel = 0;
            if (!this.tower) {
                await this.initTower();
                setTimeout(() => {
                    this.tower.setTowerHp(hp);
                }, 2000);
            } else {
                this.tower.setTowerHp(hp);
            }


            this.gameRound = 0;
            this.refreshLevelMonsterConfig();
            this.gameState = E_GameState.Gameing;
            xcore.event.raiseEvent(E_EVENT.GameScore)
        } catch (err) {
            xcore.ui.showToast('开始游戏失败' + err)
            this.gameState = E_GameState.Stop;
            setTimeout(() => {
                xcore.event.raiseEvent(E_EVENT.GameReplay);
            }, 50);
        }
    }

    //战斗结束
    async fightOver(isWin: boolean) {
        if (this.gameState == E_GameState.None) return
        this.gameState = E_GameState.None;
        if (this.tower && !isWin) {
            this.tower.destroy(true);
            this.tower = null;
            for (let i = 0; i < this.towerPoints.length; i++) {
                let towerPoint = this.towerPoints[i]
                towerPoint.destroy();
                towerPoint = null;
            }
            this.towerPoints = [];
        }
        //池子积分
        let totlalScore = this.getTotalScore();
        let totlalGiftScore = this.getTotalGiftScore();
        let totlalKillScore = this.getTotalKillScore();


        let userList = [];
        let giftList = [];
        let scoreList = [];
        //let upscores = [];
        let playerIds = [];
        let oldWeekDatas = {

        }
        let bossFightTypeDatas = [];
        //积分最多用户
        let firstFightUser: IUser
        //击杀boss最多用户
        let firstKillBossUser: IUser
        //击杀怪物最多用户
        let firstKillMonsterUser: IUser
        //帮助塔防回血最多用户
        let firstHelpTowerUser: IUser
        //是否通关
        let isCross = isWin && this.leftRoundMonsterNum <= 0 && !!this.gameRoundConfigs[this.gameRound];

        //礼物排行加成
        var arrayObj = Array.from(this.heroUser);
        arrayObj.sort(function (a, b) { return b[1]?.giftscore - a[1]?.giftscore });
        //前五单人 瓜分到的象池瓜分积分
        // let addGiftScore = Math.floor(totlalGiftScore / 3);
        for (let i = 0; i < 3; i++) {
            let userG = arrayObj[i];
            if (!userG) continue
            let userId = userG[0];
            let user = userG[1];
            if (!userId || !user) continue
            if (userG) {
                let data = this.heroUser.get(userId);
                if (data) {
                    let addScore = ConfigHelper.getInstance().getRankUpScore(1, i + 1);
                    data.poolScore += Math.floor(totlalGiftScore * addScore);
                    /* let totalscore = data.score + data.poolScore;
                    data.score = Math.floor(totalscore * this.scoreAddedValue);
                    data.upscore = data.score - totalscore;
                    upscores.push(data.upscore); */
                }
            }
        }


        arrayObj.sort(function (a, b) { return b[1]?.killscore - a[1]?.killscore });
        //前五单人 瓜分到的象池瓜分积分
        // let addKillScore = Math.floor(totlalKillScore / 3);
        for (let i = 0; i < 3; i++) {
            let userG = arrayObj[i];
            if (!userG) continue
            let userId = userG[0];
            let user = userG[1];
            if (!userId || !user) continue
            if (userG) {
                let data = this.heroUser.get(userId);
                if (data) {
                    let addScore = ConfigHelper.getInstance().getRankUpScore(2, i + 1);
                    data.poolScore += Math.floor(totlalKillScore * addScore);
                    /* let totalscore = data.score + data.poolScore;
                    data.score = Math.floor(totalscore * this.scoreAddedValue);
                    data.upscore = data.score - totalscore;
                    upscores.push(data.upscore); */
                }
            }
        }

        //积分排行加成

        try {
            //前五单人 瓜分到的象池瓜分积分
            // let addScore = Math.floor(totlalScore / 5);
            for (let i = 0; i < arrayObj.length; i++) {
                let userG = arrayObj[i];
                if (!userG) continue
                let userId = userG[0];
                let user = userG[1];
                if (!userId || !user) continue

                if (userG) {
                    let data = this.heroUser.get(userId);
                    if (data) {
                        //有礼物积分加成或击杀积分加成（相应类型积分排名前三）
                        if (data.poolScore) {
                            let totalscore = data.score + data.poolScore;
                            data.score = Math.floor(totalscore * this.scoreAddedValue);
                            data.upscore = data.score - totalscore;
                        }

                    }
                }
                if (!firstFightUser && user.score > 0) {
                    firstFightUser = user;
                }
                if (!firstKillBossUser && user.killboss > 0) {
                    firstKillBossUser = user;
                }

                if (!firstKillMonsterUser && user.killmonster > 0) {
                    firstKillMonsterUser = user;
                }
                if (!firstHelpTowerUser && user.helptower > 0) {
                    firstHelpTowerUser = user
                }


                if (firstFightUser && firstFightUser.score < user.score) {
                    firstFightUser = user;
                }

                if (firstKillBossUser && firstKillBossUser.killboss < user.killboss) {
                    firstKillBossUser = user;
                }

                if (firstKillMonsterUser && firstKillMonsterUser.killmonster < user.killmonster) {
                    firstKillMonsterUser = user;
                }

                if (firstHelpTowerUser && firstHelpTowerUser.helptower < user.helptower) {
                    firstHelpTowerUser = user
                }
            }
            this.heroUser.forEach(e => {
                if (e && e.userId) {
                    playerIds.push(e.userId);
                    oldWeekDatas[e.userId] = e.rank;
                    userList.push({
                        userId: e.userId,
                        nickName: e.nickName,
                        iconUrl: e.iconUrl,
                    })
                    e.giftKey.forEach(g => {
                        //刷礼物的用户
                        if (g.giftType) {
                            giftList.push({
                                userId: e.userId,
                                giftType: g.giftType,
                                num: g.num,
                                price: g.price
                            })
                        }

                    })

                    scoreList.push({
                        score1: null,
                        score2: null,
                        time: Math.floor(this.tickTime),
                        //1 胜利,0:失败,2平局,null 不处理
                        result: null,
                        gold: e.gold,
                        groupId: null,
                        userId: e.userId,
                        score: e.score || 0,
                        poolScore: e.poolScore,
                    })
                    if (xcore.gameData.gameType == 1) {
                        bossFightTypeDatas.push(
                            {
                                playerId: e.userId,
                                key: `bossFight_${xcore.gameData.gameTypeJsonId}`,
                                value: e.bossChangeTimeTxt
                            }
                        )
                    }
                }

            });
            scoreList = scoreList.sort((a, b) => b.score - a.score);


            console.log('bossFightTypeDatas', bossFightTypeDatas)
            await Net.settleFight(xcore.gameData.combatId, userList, giftList, scoreList, 0, 0, this.gameLevel);
            //let key = ConfigHelper.getInstance().getRankKeyByWeek();
            let key = ConfigHelper.getInstance().getRankKeyByMonth();
            let weekRankDatas = {};
            await Net.getRoleRankInfo(key, playerIds, (datas) => {
                datas.forEach(user => {
                    let userdata = this.findUser(user.playerId);
                    if (userdata) {
                        userdata.rank = user.rank

                        weekRankDatas[user.playerId] = user.rank;
                        if (userdata.role) {
                            userdata.role.data.weekScore = user.score;
                            userdata.role.checkSwitchSkin();
                        }

                    }

                });
            }, false)
            let key2 = ConfigHelper.getInstance().getLiverKeyByLev();
            Net.updateAnchorRankGameLev(key2, xcore.gameData.baseInfo.id);
            if (xcore.gameData.gameType == 1 && bossFightTypeDatas.length > 0) {
                Net.setPlayersOperate(bossFightTypeDatas);
            }
            //结算弹窗消息
            xcore.event.raiseEvent(E_EVENT.GameOver, {
                isWin,
                isCross,
                userList,
                giftList,
                scoreList,
                maxLevel: this.gameLevel,
                firstFightUser,
                firstKillBossUser,
                firstKillMonsterUser,
                firstHelpTowerUser,
                corssTime: this.tickTime,
                totalUserNum: this.heroUser.size,
                //upscores,
                oldWeekDatas,
                weekRankDatas
            });
        } catch (err) {

            log('end err:::', err)
            //结算弹窗消息
            xcore.event.raiseEvent(E_EVENT.GameOver, {
                isWin,
                isCross,
                userList,
                giftList,
                scoreList,
                maxLevel: this.gameLevel,
                firstFightUser,
                firstKillBossUser,
                firstKillMonsterUser,
                firstHelpTowerUser,
                corssTime: this.tickTime,
                totalUserNum: this.heroUser.size,
                //upscores,
                oldWeekDatas,
            });
            Tool.log('error', `fightover combatId:${xcore.gameData.combatId} err message:${err}`)
        }


    }

    //准备重新开始
    fightToReplay() {
        this.gameTotalScore = 0;
        this.gameTotalGiftScore = 0;
        this.gameTotalKillScore = 0;
        this.giftPowerNum = 0;
        this.towerTw = null;
        xcore.event.raiseEvent(E_EVENT.GameScore);
        this.destroy();
    }

    destroy() {
        this.gameState = E_GameState.Pause;
        this.tickTime = 0;
        this.attackTargets.clear();
        for (let i = 0; i < this.heroRoles.length; i++) {
            this.heroRoles[i].destroy(true);
        }

        for (let i = 0; i < this.monsterRoles.count; i++) {
            let role = this.monsterRoles.get(i);
            role.destroy(true);
        }
        for (let i = 0; i < this.buffList.count; i++) {
            let buff = this.buffList.get(i);
            buff.destroy();
        }

        this.heroRoles = [];
        //this.monsterCreatePointRoles = [];
        // this.waitMonsterData.length = 0;
        this.heroUser.clear();
        //this.monsterUser.clear();
        this.monsterRoles.clear();
        this.buffList.clear();

        this.quadTree && this.quadTree.clear();
        if (!this.isOnline) {
            this.lastUser.clear()
        }
        if (this.tower) {
            this.tower.clearAllBuff();
        }
        //this.tower = null;
    }
    //获取战斗时长
    getTime() {
        return Math.floor(this.tickTime);
    }
    refreshZindex() {
        let children = this.monsterParentNd.children.concat();
        children.sort((a: any, b: any): number => {

            return b.position.y - a.position.y
        });
        let maxIndex = children.length;
        for (const node of children) {
            node.setSiblingIndex(maxIndex);
        }
    }
    refreshHeroZindex() {
        let children = this.heroParentNd.children.concat();
        children.sort((a: any, b: any): number => {

            return b.position.y - a.position.y
        });
        let maxIndex = children.length;
        for (const node of children) {
            node.setSiblingIndex(maxIndex);
        }
    }

    //判断任务是否完成任务
    async checkTask(user: IUser) {

        let dayTask = ConfigHelper.getInstance().getTaskConfigByType(1);
        let WeekTask = ConfigHelper.getInstance().getTaskConfigByType(2);
        let MonthTask = ConfigHelper.getInstance().getTaskConfigByType(3);
        //每日任务
        if (user.giftTitleScore >= dayTask.giftScore) {
            let dayT = TimeUtil.formatTimestampToDate(TimeUtil.getServerTime(), '-');
            await Net.getPlayerOperate(user.userId, 'daytask',)
            let data = xcore.gameData.playersOperation[user.userId].daytask;
            //首次获取每日任务奖励
            if (dayT != data) {
                xcore.gameData.playersOperation[user.userId].daytask = dayT;
                Net.setPlayerOperate(user.userId, 'daytask', dayT);
                let rewardBeadIds = dayTask.rewardBeadId.split('|');
                let rewardNums = dayTask.rewardNum.split('|');
                let totalNum = 0;
                rewardBeadIds.forEach((beadid, i) => {
                    let num = Number(rewardNums[i]) || 1;
                    totalNum += num;
                    Net.addDimond(user.userId, beadid, num)
                    this.updateDimond(user.role, beadid, num)
                });
                xcore.event.raiseEvent(E_EVENT.GiftMessage, {
                    type: E_GiftMessageType.TaskReward,
                    name: user.nickName,
                    avatar: user.iconUrl,
                    totalNum: totalNum,
                    desc: MonthTask.name,
                })
            }
        }
        //每周任务
        if (user.giftTitleScore >= WeekTask.giftScore) {
            let weekT = TimeUtil.getFirstDayOfTheWeek(TimeUtil.formatTimestampToDate(TimeUtil.getServerTime(), '-'));
            await Net.getPlayerOperate(user.userId, 'weektask');
            let data = xcore.gameData.playersOperation[user.userId].weektask;
            //首次获取每周任务奖励
            if (weekT != data) {
                xcore.gameData.playersOperation[user.userId].weektask = weekT;
                Net.setPlayerOperate(user.userId, 'weektask', weekT);
                let rewardBeadIds = WeekTask.rewardBeadId.split('|');
                let rewardNums = WeekTask.rewardNum.split('|');
                let totalNum = 0;
                rewardBeadIds.forEach((beadid, i) => {
                    let num = Number(rewardNums[i]) || 1;
                    totalNum += num;
                    Net.addDimond(user.userId, beadid, num)
                    this.updateDimond(user.role, beadid, num)
                });
                xcore.event.raiseEvent(E_EVENT.GiftMessage, {
                    type: E_GiftMessageType.TaskReward,
                    name: user.nickName,
                    avatar: user.iconUrl,
                    totalNum: totalNum,
                    desc: MonthTask.name,
                })
            }
        }
        //每月任务
        if (user.giftTitleScore >= MonthTask.giftScore) {
            let monthT = TimeUtil.getFirstDayOfTheMonth();
            console.log("monthT", monthT)
            await Net.getPlayerOperate(user.userId, 'monthtask');
            let data = xcore.gameData.playersOperation[user.userId].monthtask;
            //首次获取每月任务奖励
            if (monthT != data) {
                xcore.gameData.playersOperation[user.userId].monthtask = monthT;
                Net.setPlayerOperate(user.userId, 'monthtask', monthT);
                let rewardBeadIds = MonthTask.rewardBeadId.split('|');
                let rewardNums = MonthTask.rewardNum.split('|');
                let totalNum = 0;
                rewardBeadIds.forEach((beadid, i) => {
                    let num = Number(rewardNums[i]) || 1;
                    totalNum += num;
                    Net.addDimond(user.userId, beadid, num)
                    this.updateDimond(user.role, beadid, num)

                });
                xcore.event.raiseEvent(E_EVENT.GiftMessage, {
                    type: E_GiftMessageType.TaskReward,
                    name: user.nickName,
                    avatar: user.iconUrl,
                    totalNum: totalNum,
                    desc: MonthTask.name,
                })
            }
        }
    }
    updateDimond(role: Role, type: string, addNum: number) {
        if (!role) return
        let cf
        switch (type) {
            /**金珠 攻击加成*/
            case '260001':
                if (!role.data.dimonEffects.attackDmNum) {
                    role.data.dimonEffects.attackDmNum = 0;
                }
                role.data.dimonEffects.attackDmNum += addNum;
                cf = ConfigHelper.getInstance().getBeadConfigByNum(type, role.data.dimonEffects.attackDmNum);
                role.data.dimonEffects.attackLev = cf.beadLevel;
                role.data.dimonEffects.attack = Number(cf.attack);

                break;
            /**木珠 法宝cd*/
            case '260002':
                if (!role.data.dimonEffects.skillcdDmNum) {
                    role.data.dimonEffects.skillcdDmNum = 0;
                }
                role.data.dimonEffects.skillcdDmNum += addNum;
                cf = ConfigHelper.getInstance().getBeadConfigByNum(type, role.data.dimonEffects.skillcdDmNum);
                role.data.dimonEffects.skillcdLev = cf.beadLevel;
                role.data.dimonEffects.skillcd = Number(cf.weaponInterval);

                break;
            /**水珠 攻速加成*/
            case '260003':
                if (!role.data.dimonEffects.atkspeedDmNum) {
                    role.data.dimonEffects.atkspeedDmNum = 0;
                }
                role.data.dimonEffects.atkspeedDmNum += addNum;
                cf = ConfigHelper.getInstance().getBeadConfigByNum(type, role.data.dimonEffects.atkspeedDmNum);
                role.data.dimonEffects.atkspeedLev = cf.beadLevel;
                role.data.dimonEffects.atkspeed = Number(cf.attackCooldown);

                break;
            /**火珠 暴击伤害*/
            case '260004':
                if (!role.data.dimonEffects.atkmDmNum) {
                    role.data.dimonEffects.atkmDmNum = 0;
                }
                role.data.dimonEffects.atkmDmNum += addNum;
                cf = ConfigHelper.getInstance().getBeadConfigByNum(type, role.data.dimonEffects.atkmDmNum);
                role.data.dimonEffects.atkmLev = cf.beadLevel;
                role.data.dimonEffects.atkm = Number(cf.criticalDamage);

                break;
            /**土珠 城门血量*/
            case '260005':
                if (!role.data.dimonEffects.hpDmNum) {
                    role.data.dimonEffects.hpDmNum = 0;
                }
                role.data.dimonEffects.hpDmNum += addNum;
                cf = ConfigHelper.getInstance().getBeadConfigByNum(type, role.data.dimonEffects.hpDmNum);
                role.data.dimonEffects.hpLev = cf.beadLevel;
                break;

            default:
                break;
        }
    }
    tick(dt) {


        switch (this.gameState) {
            case E_GameState.None:

                break;

            case E_GameState.Gameing:
                //重构四叉树
                this.rebuildTree()
                // 游戏时间
                this.tickTime += dt;
                this.nextRoundTime -= dt;
                if (this.tickTime >= this.gameoverTime) {
                    this.fightOver(false);
                    //游戏结束
                    return
                }

                // 角色层级调整
                if (this.tickTime % 0.5 <= dt) {
                    this.refreshZindex();
                    this.refreshHeroZindex();
                    this.monsterRoles.sort((a, b) => a?.comp?.node?.position.y - b?.comp?.node?.position.y);

                }
                /**仙族tick */
                this.heroUser.forEach(e => e.role?.tick(dt));
                /**妖族tick */
                for (let i = 0; i < this.monsterRoles.count; i++) {
                    let role = this.monsterRoles.get(i);
                    role && role.tick(dt);
                }
                /**buff tick */
                for (let i = 0; i < this.buffList.count; i++) {
                    let buffList = this.buffList.get(i);
                    buffList && buffList.tick(dt);
                }

                if (this.nextRoundTime <= 0 && (!this.newGameLevel || (this.autoNextGameLevel && !this.lotteryId && !this.dimondId))) {
                    this.tempeachCreateMonsterTime -= dt;
                    if (this.tempeachCreateMonsterTime <= 0 && this.monsterNum < 300) {
                        this.createRoundMonsters();
                    }
                } else {
                    this.checkIfHaveBox();
                    // log("下一轮开始时间：", this.nextRoundTime)
                    if (!this.autoNextGameLevel && this.newGameLevel && (this.monsterNum <= 0 || this.monsterParentNd.children.length <= 0)) {
                        xcore.event.raiseEvent(E_EVENT.NextGameLevel, this.newGameLevel)

                    }
                }

                this.tower && this.tower.tick(dt);
                if (this.towerPoints.length > 0) {
                    this.towerPoints.forEach(e => e.tick(dt));
                }
                break;
            case E_GameState.Pause:

                break;
            case E_GameState.Resume:
                this.gameState = E_GameState.Gameing
                break;

            default:
                break;
        }


    }
}