[1, ["52fHu7D8hGm5vLaoALoXCl", "1bvbi2IFNPgbGy3H7UlWSA@f9941", "dclhO4Tt5E7LRdiPIIioSG@f9941", "6aJS5bORNNepru/00ht7oP@f9941", "3dLPHxACZI25GlBE5iNtPV@f9941", "76OyCZqdRMNY6wLhQvK40f@f9941", "ff3reVIcZLdo1ocsK9Lga3@f9941", "9bBYHzhH5F25cHFMhUL3oq@f9941"], ["node", "_font", "_spriteFrame", "root", "lbHp", "lbSkillCd", "lbBaoAtkRat", "lbBaoHurtRat", "lbAtkSpeed", "lbAtk", "spr<PERSON><PERSON><PERSON>", "lbSkinName", "lbUserName", "data"], [["cc.Node", ["_name", "_layer", "_components", "_prefab", "_parent", "_children", "_lpos", "_lscale"], 1, 9, 4, 1, 2, 5, 5], ["cc.Sprite", ["_enabled", "_sizeMode", "node", "__prefab", "_spriteFrame"], 1, 1, 4, 6], ["cc.Label", ["_string", "_actualFontSize", "_isSystemFontUsed", "_fontSize", "_lineHeight", "node", "__prefab", "_outlineColor", "_color", "_font"], -2, 1, 4, 5, 5, 6], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_layer", "_parent", "_components", "_prefab", "_lpos"], 1, 1, 12, 4, 5], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.Mask", ["_type", "node", "__prefab"], 2, 1, 4], ["cc.Graphics", ["node", "__prefab", "_fillColor"], 3, 1, 4, 5], ["9bfd5cpQs9LmpVc0au9hR2X", ["node", "__prefab", "lbUserName", "lbSkinName", "spr<PERSON><PERSON><PERSON>", "lbAtk", "lbAtkSpeed", "lbBaoHurtRat", "lbBaoAtkRat", "lbSkillCd", "lbHp"], 3, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1]], [[6, 0, 2], [7, 0, 1, 2, 3, 4, 5, 5], [3, 0, 1, 2, 1], [0, 0, 1, 4, 2, 3, 6, 3], [5, 0, 1, 2, 3, 4, 5, 3], [3, 0, 1, 2, 3, 1], [0, 0, 1, 4, 5, 2, 3, 6, 3], [1, 0, 2, 3, 4, 2], [2, 0, 1, 3, 4, 2, 5, 6, 8, 7, 9, 6], [2, 0, 1, 3, 4, 2, 5, 6, 8, 7, 6], [0, 0, 1, 4, 5, 2, 3, 6, 7, 3], [1, 2, 3, 4, 1], [1, 1, 2, 3, 4, 2], [4, 0, 2], [0, 0, 1, 5, 2, 3, 3], [0, 0, 1, 4, 2, 3, 3], [3, 0, 1, 1], [1, 2, 3, 1], [2, 0, 1, 2, 5, 6, 7, 4], [2, 0, 1, 2, 5, 6, 8, 7, 4], [8, 0, 1, 2, 2], [9, 0, 1, 2, 1], [10, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 1]], [[13, "UnitUserInfoMessage"], [14, "UnitUserInfoMessage", 33554432, [-13, -14, -15], [[2, -2, [0, "a2PydaQLZEQ6H5FObWfqVC"], [5, 430, 500]], [22, -12, [0, "1bF0sa2uxECKQBAlRiI4qd"], -11, -10, -9, -8, -7, -6, -5, -4, -3]], [1, "fbUJ19gR5PFqbp2P+zFN99", null, null, null, -1, 0]], [10, "ndBaseInfo", 33554432, 1, [-17, -18, -19, -20, -21, -22, -23, -24, -25, -26, -27, -28], [[16, -16, [0, "c5i9A14oJDS6v1IW7Ef3MT"]]], [1, "c8XveZrOVNBq+R/NMFWLJp", null, null, null, 1, 0], [1, -62.012, -21.849000000000046, 0], [1, 0.5, 0.5, 1]], [10, "ndUser", 33554432, 1, [-30, -31, -32, -33, -34, -35], [[2, -29, [0, "88uv8C96NCXIfG+yW9Bpr5"], [5, 700, 230]]], [1, "easFsndttJxrPh5eqetwHp", null, null, null, 1, 0], [1, 28.23599999999999, 130.3409999999999, 0], [1, 0.7, 0.7, 1]], [6, "Node", 33554432, 3, [-39], [[2, -36, [0, "89zMlB2K1EFb9Otv9beRH2"], [5, 90, 90]], [20, 1, -37, [0, "cfjD9cmE5JQ4BI7f6z8SSq"]], [21, -38, [0, "45Pt5FYFxLyoP+P17B+PZA"], [4, 16777215]]], [1, "aeZNdhapFMCr95qEmqGMQQ", null, null, null, 1, 0], [1, -235.176, 13.269, 0]], [6, "1", 33554432, 2, [-42], [[2, -40, [0, "44JpDl9dtH9KgYhMlXBnsh"], [5, 169, 63]], [7, false, -41, [0, "c9OdCwhz5KMqFShgs4aBcZ"], 2]], [1, "7cogp1QjRL6rfQW/DBZLoc", null, null, null, 1, 0], [1, -107.37400000000002, 114.03800000000001, 0]], [6, "4", 33554432, 2, [-45], [[2, -43, [0, "c3EvA9P/JFp74IHT+2+au3"], [5, 169, 63]], [7, false, -44, [0, "69a6F1zkJFdrOUMdXCBPx3"], 4]], [1, "96dlqBbthLw5mXYP0U+pQd", null, null, null, 1, 0], [1, -107.37400000000002, 18.054000000000087, 0]], [6, "2", 33554432, 2, [-48], [[2, -46, [0, "7f4eNThIZDe6PFSno2IA60"], [5, 169, 63]], [7, false, -47, [0, "86wwWvquBAHYIkMvQw/nek"], 6]], [1, "3b9yo6mMhDEaROG7KmL4sm", null, null, null, 1, 0], [1, -107.37400000000002, -78.05599999999981, 0]], [6, "5", 33554432, 2, [-51], [[2, -49, [0, "58s3sGW0FOeL39kZGFh0ts"], [5, 169, 63]], [7, false, -50, [0, "c69ph7dixIQKmmOPGd9wAq"], 8]], [1, "62e+7+2ptOMr43CFkB40Gm", null, null, null, 1, 0], [1, -107.37400000000002, -174.16599999999994, 0]], [6, "3", 33554432, 2, [-54], [[2, -52, [0, "38MszWQ75OpZQgJkESO3g8"], [5, 286, 72]], [7, false, -53, [0, "2bbZ9ln8BCsabMTESNxfj9"], 10]], [1, "56dWzanwZEv7PnCm2KzvKD", null, null, null, 1, 0], [1, -48.874000000000024, -270.2739999999999, 0]], [6, "6", 33554432, 2, [-57], [[2, -55, [0, "af/DHEWi1GRLFnvHxSYCFi"], [5, 286, 72]], [7, false, -56, [0, "93AiJtn+1GZbBS+qbMrruF"], 12]], [1, "e6km9ASChI+J52unqrY1CZ", null, null, null, 1, 0], [1, -48.874000000000024, -366.384, 0]], [15, "common_frame_12", 33554432, 1, [[2, -58, [0, "0feFEong1NKqbgKB7M6hkX"], [5, 429, 495]], [11, -59, [0, "29SQnmfxZBhLQc46BRmZon"], 0]], [1, "80vDMnSZFG+ouzgYCub5nA", null, null, null, 1, 0]], [3, "Label", 33554432, 5, [[2, -60, [0, "01Bl43udxPIpyhWMVcTK3x"], [5, 165, 75.6]], [8, "攻击：", 60, 60, 60, false, -61, [0, "2bKf/SztpGiLE/XDwdSegz"], [4, 4278201412], [4, 4278190335], 1]], [1, "cdHLOLFBhNi42l3mkrRLNz", null, null, null, 1, 0], [1, -17.065999999999917, 2.4950000000001182, 0]], [3, "Label", 33554432, 6, [[2, -62, [0, "c0tflaSFlGkKcsUndXwVC6"], [5, 165, 75.6]], [8, "攻速：", 60, 60, 60, false, -63, [0, "0eNOPlFvVPqr+2AxM7a+st"], [4, 4278201412], [4, 4278190335], 3]], [1, "51cXvID9BLl7e+3vd0TmD0", null, null, null, 1, 0], [1, -17.065999999999917, 2.494999999999891, 0]], [3, "Label", 33554432, 7, [[2, -64, [0, "c06r6sADZEjoAl5vJXIVcb"], [5, 165, 75.6]], [8, "爆伤：", 60, 60, 60, false, -65, [0, "afas1Xyx5DnpUk3FHxlH3/"], [4, 4278201412], [4, 4294115328], 5]], [1, "3eEAjGootAo7WR2TXSSpZx", null, null, null, 1, 0], [1, -17.065999999999917, 2.494000000000142, 0]], [3, "Label", 33554432, 8, [[2, -66, [0, "8dfnHJnr9Do7QOhoNeYpaX"], [5, 225, 75.6]], [8, "暴击率：", 60, 60, 60, false, -67, [0, "57VJG2SSdNuby3bV5XTZmR"], [4, 4278201412], [4, 4294115328], 7]], [1, "07+9InXYRDF43E79UIBv4w", null, null, null, 1, 0], [1, 12.934000000000083, 2.4950000000001182, 0]], [3, "Label", 33554432, 9, [[2, -68, [0, "4ctbQYA/hKBp6A3IPf2YMS"], [5, 285, 75.6]], [8, "法宝间隔：", 60, 60, 60, false, -69, [0, "32ng6JUWFK7L8HWt8UYQQ2"], [4, 4278201412], [4, 4278219007], 9]], [1, "85FWOr3rxCdZWiZ0AWgKTZ", null, null, null, 1, 0], [1, -15.565999999999917, 2.494000000000142, 0]], [3, "Label", 33554432, 10, [[2, -70, [0, "22bS0AoVpImJu342FuEJwx"], [5, 345, 75.6]], [8, "南天门血量：", 60, 60, 60, false, -71, [0, "a6dNQdMwxBo6bIRIDnZs03"], [4, 4278201412], [4, 4278219007], 11]], [1, "43gFOaFKxPqKJXV/3lhxUn", null, null, null, 1, 0], [1, 14.434000000000083, 2.4960000000000946, 0]], [4, "lbAtk", 33554432, 2, [[[5, -72, [0, "e3AHngB6BPnbXJXBgzfVRM"], [5, 332.75994873046875, 75.6], [0, 0, 0.5]], -73], 4, 1], [1, "37TTU0CdJGj7+AJwtrpwIf", null, null, null, 1, 0], [1, 47.422000000000025, 114.03800000000001, 0]], [4, "lbAtkSpeed", 33554432, 2, [[[5, -74, [0, "f2NCqQfY9PCaJI6lFLrnsh"], [5, 93.719970703125, 75.6], [0, 0, 0.5]], -75], 4, 1], [1, "4d+HMQH21BCLhTsawlAvUi", null, null, null, 1, 0], [1, 47.422000000000025, 18.054000000000087, 0]], [4, "lbBaoHurt", 33554432, 2, [[[5, -76, [0, "18Q37qsixCrJ2b4WBPm3mC"], [5, 93.719970703125, 75.6], [0, 0, 0.5]], -77], 4, 1], [1, "19wEsvf1FJ3YDOi3yLJyLf", null, null, null, 1, 0], [1, 47.422000000000025, -78.68799999999987, 0]], [4, "lbBaoAtk", 33554432, 2, [[[5, -78, [0, "107pNXjxpOG4dqJGNIfpFJ"], [5, 93.719970703125, 75.6], [0, 0, 0.5]], -79], 4, 1], [1, "5dex5V/CdFq708jRRNwzSZ", null, null, null, 1, 0], [1, 47.422000000000025, -176.91999999999985, 0]], [4, "lbSkillCd", 33554432, 2, [[[5, -80, [0, "6aXcAwvFVGUZUXM+zjFDij"], [5, 93.719970703125, 75.6], [0, 0, 0.5]], -81], 4, 1], [1, "a1gDE+o6tP1JSQD/jwm7EY", null, null, null, 1, 0], [1, 134.99400000000003, -264.568, 0]], [4, "lbHp", 33554432, 2, [[[5, -82, [0, "f0isdFCGtErrdeQTthNDcl"], [5, 147.8399658203125, 75.6], [0, 0, 0.5]], -83], 4, 1], [1, "ab5GY+eCNJzpsH25ZpdweJ", null, null, null, 1, 0], [1, 134.99400000000003, -367.54999999999995, 0]], [3, "0", 33554432, 3, [[2, -84, [0, "8fHpWrFfJPJZ3wGw2ItLg3"], [5, 329, 72]], [11, -85, [0, "3bblDTTztHI5yuMSbefXUc"], 13]], [1, "7fOreoks5P+4ENTN1eT7k5", null, null, null, 1, 0], [1, -2.8759999999999764, 38.52099999999996, 0]], [4, "lbUserName", 33554432, 3, [[[5, -86, [0, "cabKDMvvZLtLDb89wB+3Py"], [5, 160, 50.4], [0, 0, 0.5]], -87], 4, 1], [1, "945R584IJLAY0bS4nybbNR", null, null, null, 1, 0], [1, -139.759, 42.033, 0]], [4, "lbSkinName", 33554432, 3, [[[5, -88, [0, "ab6RmxXi9EtJxq3QktP0zh"], [5, 160, 50.4], [0, 0, 0.5]], -89], 4, 1], [1, "f3jcUVnddNzIHWh9dzmkOQ", null, null, null, 1, 0], [1, -139.75900000000001, -32.09899999999993, 0]], [3, "sprAFrame", 33554432, 3, [[2, -90, [0, "a84okvrXBAaKFHjPlVUCtd"], [5, 90, 90]], [12, 0, -91, [0, "ednGqbRsdHHYd3/XmNlZz/"], 14]], [1, "73FI0O3JBOA5XKQoDrelLJ", null, null, null, 1, 0], [1, -235.17, 14.093, 0]], [4, "spr<PERSON><PERSON><PERSON>", 33554432, 4, [[[2, -92, [0, "b3VOr+dTBJmIIfVQ/Xn2i7"], [5, 90, 90]], -93], 4, 1], [1, "f23e3YkHxKXZJnH70F4JZj", null, null, null, 1, 0], [1, 0, 1.6889999999999645, 0]], [3, "sprMask", 33554432, 3, [[2, -94, [0, "a5L2bimQpOjIbiNnQYYOWE"], [5, 90, 90]], [12, 0, -95, [0, "2dHlP7LkVB6oQKRbXEhRLz"], 15]], [1, "a0XrHB7fRFjZ3CvxiKrHAQ", null, null, null, 1, 0], [1, -235.176, 14.959, 0]], [9, "5000~8000", 60, 60, 60, false, 18, [0, "d5Bjmv54ZNuIQ2yFqjbPhS"], [4, 4278190330], [4, 4278219007]], [9, "0.5", 60, 60, 60, false, 19, [0, "37JCGUi5hE162tq7W5v3OZ"], [4, 4278190330], [4, 4278219007]], [9, "0.5", 60, 60, 60, false, 20, [0, "6bS14SEyRB85JiDA3kakSB"], [4, 4294926858], [4, 4278219007]], [9, "0.5", 60, 60, 60, false, 21, [0, "571kCdfA9Lmr/fH/1IjbVa"], [4, 4294926858], [4, 4278219007]], [9, "0.5", 60, 60, 60, false, 22, [0, "f3kfTmk6ZA+o0MlZgsWL48"], [4, 4278219007], [4, 4278219007]], [9, "5000", 60, 60, 60, false, 23, [0, "e2ziB/1G9A2qnR97y9Jzt/"], [4, 4278219007], [4, 4278219007]], [18, "用户名称", 40, false, 25, [0, "0eRwitFWFCGYQjhEPsbZL5"], [4, 4282664004]], [19, "皮肤名称", 40, false, 26, [0, "dejTI5GERJu5zKj5jf9FdN"], [4, 4278190330], [4, 4278219007]], [17, 28, [0, "187jR9S/xKCLXk0XfgnNuw"]]], 0, [0, 3, 1, 0, 0, 1, 0, 4, 35, 0, 5, 34, 0, 6, 33, 0, 7, 32, 0, 8, 31, 0, 9, 30, 0, 10, 38, 0, 11, 37, 0, 12, 36, 0, 0, 1, 0, -1, 11, 0, -2, 2, 0, -3, 3, 0, 0, 2, 0, -1, 5, 0, -2, 6, 0, -3, 7, 0, -4, 8, 0, -5, 9, 0, -6, 10, 0, -7, 18, 0, -8, 19, 0, -9, 20, 0, -10, 21, 0, -11, 22, 0, -12, 23, 0, 0, 3, 0, -1, 24, 0, -2, 25, 0, -3, 26, 0, -4, 27, 0, -5, 4, 0, -6, 29, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, -1, 28, 0, 0, 5, 0, 0, 5, 0, -1, 12, 0, 0, 6, 0, 0, 6, 0, -1, 13, 0, 0, 7, 0, 0, 7, 0, -1, 14, 0, 0, 8, 0, 0, 8, 0, -1, 15, 0, 0, 9, 0, 0, 9, 0, -1, 16, 0, 0, 10, 0, 0, 10, 0, -1, 17, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, -2, 30, 0, 0, 19, 0, -2, 31, 0, 0, 20, 0, -2, 32, 0, 0, 21, 0, -2, 33, 0, 0, 22, 0, -2, 34, 0, 0, 23, 0, -2, 35, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, -2, 36, 0, 0, 26, 0, -2, 37, 0, 0, 27, 0, 0, 27, 0, 0, 28, 0, -2, 38, 0, 0, 29, 0, 0, 29, 0, 13, 1, 95], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 30, 31, 32, 33, 34, 35, 36, 37], [2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1], [4, 0, 1, 0, 1, 0, 2, 0, 2, 0, 3, 0, 3, 5, 6, 7, 0, 0, 0, 0, 0, 0, 0, 0]]