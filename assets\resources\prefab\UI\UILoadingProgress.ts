import { _decorator, Button, CCFloat, color, Component, Label, log, Node, PageView, RichText, size, sp, Sprite, tween, v3, view, warn } from 'cc';
import { xcore } from '../../../scripts/libs/xcore';
const { ccclass, property } = _decorator;
type InitCallback = (sprBg: Sprite, sprBar: Sprite) => void
interface ILoadingPgsRes {
    sprBg?: {
        bundleName: string,
        path: string
    }
    sprBar?: {
        bundleName: string,
        path: string
    }
}
@ccclass('UILoadingProgress')
export class UILoadingProgress extends Component {

    @property(Label)
    private lbProgressTxt: Label = null;

    @property(Label)
    private lbProgressVal: Label = null;

    @property(Sprite)
    private sprBg: Sprite = null;

    @property(Sprite)
    private sprBar: Sprite = null;

    @property({ type: CCFloat, displayName: '距离底部长度' })
    private lenToBottom: number = 160

    @property({ type: CCFloat, displayName: '进度条底文字切换间隔' })
    private switchTimeLoadingTxt: number = 0.3


    private _loadingTxtIndex: number = 0;
    private _loadingTxts: string[] = ['资源加载中', '资源加载中.', '资源加载中..', '资源加载中...'];

    private _isLoadingStart: boolean = false;
    private _isLoadingFinished: boolean = false;

    private _timer: number = 0;


    /**
     * 
     * @param loadingTxts    进度条底部显示文本
     * @param onLoginFinish  进度条完成回调   
     * @param onInitCb  进度条初始化回调 注：进度条样式图片可在此回调加载 
     * @returns 
     */
    public initUI(loadingTxts: string[], onLoginFinish: () => {}, loadingRes: ILoadingPgsRes): Function {
        this._loadingTxtIndex = 0;
        this.lbProgressVal.string = '0%';
        this.lbProgressTxt.string = this._loadingTxts[this._loadingTxtIndex] || '';
        (loadingTxts && loadingTxts.length > 0) && (this._loadingTxts = loadingTxts);
        this.loadRes(loadingRes);
        this.onLoginFinish = onLoginFinish;
        this.node.setPosition(v3(0, -view.getVisibleSize().height / 2 + this.lenToBottom));
        return () => {
            this._isLoadingFinished = true;
        };
    }
    async loadRes(loadingRes: ILoadingPgsRes) {
        if (loadingRes) {
            for (const key in loadingRes) {
                if (Object.prototype.hasOwnProperty.call(loadingRes, key)) {
                    const resData = loadingRes[key];
                    await xcore.res.bundleLoadSprite(resData.bundleName, resData.path, this[key]);
                }
            }
        }
        this._isLoadingStart = true;
    }

    //进度条
    private doProgressLoading(dt) {
        if (!this._isLoadingStart || !this.sprBar.spriteFrame) return
        this._timer += dt;
        let val = (1 - 1 / (1 + this._timer));

        //接口及资源初始化完成后触发走完进度条
        if (this._isLoadingFinished) {
            this._isLoadingStart = false;
            let lbVal = this.lbProgressVal;
            tween(this.sprBar)
                .to(0.3, { fillRange: 1 }, {
                    onUpdate(target: Sprite, ratio: number) {
                        lbVal.string = `${Math.floor(Math.min(val + ((1 - val) * ratio), 1) * 100)}%`;
                    }
                })
                .call(() => {
                    this.lbProgressVal.string = `100%`;
                    this.lbProgressTxt.string = `加载完成`;
                })
                .delay(0.2)
                .call(() => {
                    this.onLoginFinish();
                    xcore.ui.closeUI('UILoading');
                    xcore.ui.closeUI('UILoadingProgress');
                })
                .start()
            return
        }
        this.lbProgressVal.string = `${Math.floor(val * 100)}%`;
        this.sprBar.fillRange = val;
        if (this._timer % this.switchTimeLoadingTxt <= dt) {
            let length = this._loadingTxts.length
            this.lbProgressTxt.string = this._loadingTxts[this._loadingTxtIndex % length] || '';
            this._loadingTxtIndex++
            if (this._loadingTxtIndex >= length) {
                this._loadingTxtIndex = 0
            }
        }
    }
    //进度条完成回调
    onLoginFinish() { }

    update(deltaTime: number) {
        this.doProgressLoading(deltaTime);
    }
}


