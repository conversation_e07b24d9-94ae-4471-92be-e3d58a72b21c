[1, ["dftzNaoexEbZGEPIE68NqE@6c48a"], ["_textureSource"], ["cc.SpriteFrame"], 0, [{"name": "huodou", "rect": {"x": 2, "y": 2, "width": 2044, "height": 1984}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 2048, "height": 1988}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-1022, -992, 0, 1022, -992, 0, -1022, 992, 0, 1022, 992, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [2, 1986, 2046, 1986, 2, 2, 2046, 2], "nuv": [0.0009765625, 0.001006036217303823, 0.9990234375, 0.001006036217303823, 0.0009765625, 0.9989939637826962, 0.9990234375, 0.9989939637826962], "minPos": {"x": -1022, "y": -992, "z": 0}, "maxPos": {"x": 1022, "y": 992, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]]