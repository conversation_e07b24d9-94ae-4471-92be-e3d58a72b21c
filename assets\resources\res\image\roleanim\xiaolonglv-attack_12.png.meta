{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "ae7e4925-a05f-4d59-a654-2e54c8dd08df", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "ae7e4925-a05f-4d59-a654-2e54c8dd08df@6c48a", "displayName": "xiaolonglv-attack_12", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "ae7e4925-a05f-4d59-a654-2e54c8dd08df", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "ae7e4925-a05f-4d59-a654-2e54c8dd08df@f9941", "displayName": "xiaolonglv-attack_12", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": -25, "offsetY": -13, "trimX": 18, "trimY": 44, "width": 114, "height": 138, "rawWidth": 200, "rawHeight": 200, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-57, -69, 0, 57, -69, 0, -57, 69, 0, 57, 69, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [18, 156, 132, 156, 18, 18, 132, 18], "nuv": [0.09, 0.09, 0.66, 0.09, 0.09, 0.78, 0.66, 0.78], "minPos": [-57, -69, 0], "maxPos": [57, 69, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "ae7e4925-a05f-4d59-a654-2e54c8dd08df@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "ae7e4925-a05f-4d59-a654-2e54c8dd08df@6c48a"}}