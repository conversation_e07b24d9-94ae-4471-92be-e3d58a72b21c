[1, ["52fHu7D8hGm5vLaoALoXCl", "20g1ukYUVPvKWKBRznAKo+@f9941", "54TknWPwVPqJqeCR+Y/Czo@f9941", "95EkngnxZFbYuFpsqVTaFr@f9941", "330HKrFs1CkZNPS/xdaog9@f9941", "6dBon2ralKwZXNUTqBRw2D@f9941", "e3++Dt4MBOUL46MNXfLRgv@f9941", "40IhpSmmFNy71lG186TxTK@6c48a", "afxHkx8GZGsJC+n+YfITQo@6c48a", "4auqsBnXdMdqpFbJDnfGgL@f9941", "c6cbqAlNFAhqxf/4JtbC6U@f9941", "a4MkB+uthFw5i2VtThog2+@f9941", "2c8hCyiEhL3KBAMa5+iwT7@f9941", "ff3reVIcZLdo1ocsK9Lga3@f9941", "9bBYHzhH5F25cHFMhUL3oq@f9941", "34t0pp21ZPYZyoDBQkvZ7a@f9941", "7dyltG3WBNtLHeX9EVgqeQ@f9941", "56gkGz0itOlqntWwkCHBKn@f9941", "a8bBbhLlVH+7ImLX99p7Ol@f9941", "d98+DDYAVHv680Q8PhWpjM@f9941", "f0udWJcE9CTa9bPe718L84", "40IhpSmmFNy71lG186TxTK@f9941", "afxHkx8GZGsJC+n+YfITQo@f9941", "3bvJBntRxNdLTqkOiS7Nx7@f9941"], ["node", "_spriteFrame", "_font", "_normalSprite", "_hoverSprite", "_pressedSprite", "_disabledSprite", "_textureSource", "root", "data", "_parent", "scrollviewV", "lbNeedScore", "lbEachatkNum", "lbAtkNum", "btnArr02", "btnArr01", "lbSkinName", "pvSkin", "btnClose", "sprFrame", "ndRoot", "spr<PERSON><PERSON><PERSON>", "lbScore", "lbRank", "lbName", "pfbUnitSkin", "anim"], [["cc.Sprite", ["_sizeMode", "_type", "_name", "node", "__prefab", "_spriteFrame", "_color"], 0, 1, 4, 6, 5], ["cc.Node", ["_name", "_layer", "_active", "_components", "_prefab", "_children", "_lpos", "_parent"], 0, 9, 4, 2, 5, 1], ["cc.Label", ["_string", "_actualFontSize", "_isSystemFontUsed", "_fontSize", "_lineHeight", "_outlineWidth", "_enableOutline", "node", "__prefab", "_color", "_outlineColor", "_font"], -4, 1, 4, 5, 5, 6], ["cc.UITransform", ["_name", "node", "__prefab", "_contentSize", "_anchorPoint"], 2, 1, 4, 5, 5], ["cc.Node", ["_name", "_layer", "_parent", "_components", "_prefab", "_lpos", "_children"], 1, 1, 12, 4, 5, 2], ["cc.Widget", ["_alignFlags", "_left", "_top", "_originalWidth", "_originalHeight", "node", "__prefab", "_target"], -2, 1, 4, 1], "cc.SpriteFrame", ["cc.Mask", ["_type", "node", "__prefab"], 2, 1, 4], ["cc.Layout", ["_resizeMode", "_layoutType", "_spacingX", "_spacingY", "_constraintNum", "_isAlign", "node", "__prefab"], -3, 1, 4], ["cc.<PERSON><PERSON>", ["_transition", "_zoomScale", "node", "__prefab", "_normalColor", "_target"], 1, 1, 4, 5, 1], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["dde78natMpDi40gnSp4dAoo", ["animStyle", "node", "__prefab", "ndRoot", "sprFrame", "btnClose", "pvSkin", "lbSkinName", "btnArr01", "btnArr02", "lbAtkNum", "lbEachatkNum", "lbNeedScore", "scrollviewV", "pfbUnitSkin"], 2, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 6], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["39d08HIBTlPIZQzcsYE/eDd", ["node", "__prefab", "lbName", "lbRank", "lbScore", "spr<PERSON><PERSON><PERSON>"], 3, 1, 4, 1, 1, 1, 1], ["cc.Graphics", ["node", "__prefab", "_fillColor"], 3, 1, 4, 5], ["cc.PageViewIndicator", ["spacing", "node", "__prefab", "_cellSize"], 2, 1, 4, 5], ["<PERSON>.<PERSON>", ["_name", "horizontal", "node", "__prefab", "_content", "_indicator"], 1, 1, 4, 1, 1], ["d2bd8Ybyq5JP6Lhg2nKR4ii", ["horizontal", "node", "__prefab", "_content", "itemNode"], 2, 1, 4, 1, 1], ["0226e2lN9JFhbr+rZVIa+tz", ["node", "__prefab", "anim"], 3, 1, 4, 1], ["cc.Animation", ["node", "__prefab"], 3, 1, 4]], [[11, 0, 2], [13, 0, 1, 2, 3, 4, 5, 5], [3, 1, 2, 3, 1], [1, 0, 1, 7, 3, 4, 6, 3], [4, 0, 1, 2, 3, 4, 5, 3], [0, 3, 4, 5, 1], [3, 1, 2, 3, 4, 1], [1, 0, 1, 7, 5, 3, 4, 3], [2, 0, 1, 3, 2, 7, 8, 9, 11, 5], [1, 0, 1, 5, 3, 4, 6, 3], [4, 0, 1, 2, 3, 4, 3], [0, 1, 3, 4, 5, 2], [15, 0, 1, 2, 1], [2, 0, 1, 4, 2, 5, 7, 8, 9, 10, 11, 6], [2, 0, 1, 4, 2, 5, 7, 8, 9, 10, 6], [10, 0, 2], [4, 0, 1, 2, 6, 3, 4, 5, 3], [0, 0, 3, 4, 2], [7, 1, 2, 1], [2, 0, 1, 3, 2, 7, 8, 9, 5], [9, 0, 1, 2, 3, 4, 5, 3], [1, 0, 1, 5, 3, 4, 3], [1, 0, 1, 7, 5, 3, 4, 6, 3], [1, 0, 2, 1, 7, 5, 3, 4, 6, 4], [3, 1, 2, 1], [3, 0, 1, 2, 3, 2], [12, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 2], [0, 0, 3, 4, 5, 2], [0, 2, 1, 0, 3, 4, 4], [0, 0, 3, 4, 6, 5, 2], [0, 3, 4, 1], [14, 0, 1, 2, 3, 4, 5, 1], [5, 0, 5, 6, 7, 2], [5, 0, 1, 2, 5, 6, 7, 4], [5, 0, 3, 4, 5, 6, 4], [7, 0, 1, 2, 2], [8, 0, 1, 2, 3, 4, 6, 7, 6], [8, 0, 1, 5, 6, 7, 4], [2, 0, 1, 3, 4, 2, 6, 5, 7, 8, 9, 10, 8], [2, 0, 1, 3, 2, 7, 8, 5], [9, 0, 2, 3, 4, 5, 2], [16, 0, 1, 2, 3, 2], [17, 0, 1, 2, 3, 4, 5, 3], [18, 0, 1, 2, 3, 4, 2], [19, 0, 1, 2, 1], [20, 0, 1, 1]], [[[{"name": "viewskinselect_frame_01", "rect": {"x": 0, "y": 0, "width": 1080, "height": 1920}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 1080, "height": 1920}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-540, -960, 0, 540, -960, 0, -540, 960, 0, 540, 960, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 1920, 1080, 1920, 0, 0, 1080, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -540, "y": -960, "z": 0}, "maxPos": {"x": 540, "y": 960, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [6], 0, [0], [7], [7]], [[{"name": "default_scrollbar_vertical", "rect": {"x": 0, "y": 0, "width": 15, "height": 30}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 15, "height": 30}, "rotated": false, "capInsets": [4, 10, 4, 10], "vertices": {"rawPosition": [-7.5, -15, 0, 7.5, -15, 0, -7.5, 15, 0, 7.5, 15, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 30, 15, 30, 0, 0, 15, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -7.5, "y": -15, "z": 0}, "maxPos": {"x": 7.5, "y": 15, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [6], 0, [0], [7], [8]], [[[15, "ViewSkinSelect"], [21, "ViewSkinSelect", ********, [-15], [[2, -2, [0, "9emz1/qmVOCLpz7WA66hgf"], [5, 1080, 1920]], [26, 0, -14, [0, "8fl/QCmalK6ZJnwpxQ5Rq1"], -13, -12, -11, -10, -9, -8, -7, -6, -5, -4, -3, 21]], [1, "91ztAqgVhBuIkfkpx4/0QN", null, null, null, -1, 0]], [9, "UnitItemScore", ********, [-23, -24, -25, -26, -27, -28, -29, -30, -31, -32], [[2, -16, [0, "ecFPhv+MlFkpMi77ja85vG"], [5, 877, 96]], [5, -17, [0, "d7fmYqLBpKoqiIPObT0f7e"], 9], [31, -22, [0, "0cqB/USRdKhIcHYbRDac9x"], -21, -20, -19, -18]], [1, "7aZlA8BMlGTI7B1E+m/jS7", null, null, null, 1, 0], [1, 0, -48, 0]], [9, "ndBottom", ********, [-36, -37, -38, -39, -40, -41, -42, -43, -44], [[2, -33, [0, "01f0s0WkxKxqlzZkPxsRXp"], [5, 1080, 800]], [27, 0, -34, [0, "d6CYSTkLFGPLd8Exs/nLxd"], 20], [32, 4, -35, [0, "e9Gx9e9kxD25nr3RF8kXS1"], 1]], [1, "5dW0JtEXdDepTUpNEYzlDp", null, null, null, 1, 0], [1, 0, -560, 0]], [7, "ndRoot", ********, 1, [-46, -47, -48, -49, -50, -51, 3], [[24, -45, [0, "32j8T6Ds5HNrjC5Htd8opn"]]], [1, "a583LOsJBPxJpHMePRzwpM", null, null, null, 1, 0]], [16, "svV", ********, 3, [-54, -55, -56, -57, -58], [[[6, -52, [0, "237Q5WFVRLR4FKxXuAcz0c"], [5, 880, 482], [0, 0.5, 1]], -53], 4, 1], [1, "e7El9/MklCq5g+f0GJBzKd", null, null, null, 1, 0], [1, 0, 81.072, 0]], [4, "btnClose", ********, 4, [[[2, -59, [0, "761phfpVZHRpivuVOVuC/A"], [5, 112, 113]], [11, 1, -60, [0, "dfM8iVr0xPUL30tUQzHtBH"], 0], -61, [33, 9, 29.060000000000002, 47.432000000000016, -62, [0, "c0aih6fqlFD58UeCF0XgPL"], 1]], 4, 4, 1, 4], [1, "38DW0r0L1JS5hVMqcYiIOf", null, null, null, 1, 0], [1, -454.94, 856.068, 0]], [16, "<PERSON><PERSON><PERSON><PERSON>", ********, 4, [-66, -67], [[[25, "pageView-horizontal<UITransform>", -63, [0, "16QsvMajRJELq2rg3Och99"], [5, 1080, 800]], [28, "pageView-horizontal<Sprite>", 1, 0, -64, [0, "a3Ns08f45Bp7dKzF4LpX+F"]], -65], 4, 4, 1], [1, "b52S3ci6JKtIOi2LZkKbd2", null, null, null, 1, 0], [1, -0.9400000000000546, 323.21000000000004, 0]], [7, "view", ********, 5, [-72], [[6, -68, [0, "0eVMFWeAVH0JV9nLY39iRn"], [5, 880, 482], [0, 0.5, 1]], [18, -69, [0, "92pdCm3sBHq7hcJ6G4NXoh"]], [34, 45, 240, 250, -70, [0, "9b4cYG7IBHq7VxusMV3YVc"]], [12, -71, [0, "1bNfcTmv9GJJtXFgvktttC"], [4, 16777215]]], [1, "6avWnPx+hFa51GrSx1nNpw", null, null, null, 1, 0]], [7, "view", ********, 7, [-76], [[2, -73, [0, "f3Lpw1U3lBvarOS8NffiHv"], [5, 1080, 800]], [18, -74, [0, "a5k7yDXUtEs78v81dobDsS"]], [12, -75, [0, "cbwc25NuZHn4cnSwn+9opS"], [4, 16777215]]], [1, "3b/5/1qclJhrZfUuhLL1MN", null, null, null, 1, 0]], [4, "btnArr01", ********, 4, [[[2, -77, [0, "3bdgFu36tDpa3RKy+FqTxk"], [5, 50, 123]], [11, 1, -78, [0, "26ArdTND1Px7603JREt0My"], 1], -79], 4, 4, 1], [1, "11yo/YBtdPsJ+cpOjE9dys", null, null, null, 1, 0], [1, -370, 312.414, 0]], [4, "btnArr02", ********, 4, [[[2, -80, [0, "99iiz3bilOKqsLk5E5JPy3"], [5, 49, 123]], [11, 1, -81, [0, "bbJdkaPt1MyIqsqjtQ85Hr"], 2], -82], 4, 4, 1], [1, "dbj9IkQCJLh7FZVBVtcm2R", null, null, null, 1, 0], [1, 370, 312.414, 0]], [7, "content", ********, 8, [2], [[6, -83, [0, "ff6AYB3aRJkZ0YhN7e7L/N"], [5, 880, 96], [0, 0.5, 1]], [36, 1, 2, 50, 10, -1.8, -84, [0, "39RrTvHzlLN6pA0afXMUv6"]]], [1, "31LttKU7RKmYSa0fG/fGM1", null, null, null, 1, 0]], [22, "Node", ********, 2, [-88], [[2, -85, [0, "22Cw70XoZDC5nr6oDw0OQ9"], [5, 70, 70]], [35, 1, -86, [0, "c6jSVcHQNCfI+95IsQH3pN"]], [12, -87, [0, "4aw5t6BWtBVpTafDg8Pf77"], [4, 16777215]]], [1, "fcRoHP/D5OSroDQnE5qB0m", null, null, null, 1, 0], [1, -258.48699999999997, 0, 0]], [3, "content", ********, 9, [[6, -89, [0, "f9I16OAp5P9LKmS2+HCRS9"], [5, 0, 800], [0, 0, 0.5]], [37, 1, 1, true, -90, [0, "44gbYdCmJOyYEotW/xvrkN"]]], [1, "d9FKOxsupDu7Ww7JajYC49", null, null, null, 1, 0], [1, -540, 7.549516567451064e-15, 0]], [23, "ndDesc", false, ********, 3, [-93], [[2, -91, [0, "d9bI8GcSxNpZXeyLRRMPpR"], [5, 1080, 580]], [29, 0, -92, [0, "bbBrLBfP1K0oDrjjTKpKKC"], [4, **********], 15]], [1, "1fx408CIpAh5pbPrnAlHGy", null, null, null, 1, 0], [1, 0, -110, 0]], [10, "sprFrame", ********, 4, [[[2, -94, [0, "152JSy2OtAkYrxyoFiaZV9"], [5, 1080, 1920]], -95], 4, 1], [1, "3cFKJLfUtF2I/kwX8FNdDi", null, null, null, 1, 0]], [4, "indicator", ********, 7, [[[2, -96, [0, "08gmq3a3BNO5UWQjIsObMA"], [5, 500, 60]], -97], 4, 1], [1, "31OpfnmvlLQKI1s+HgzXq/", null, null, null, 1, 0], [1, 0, -440.572, 0]], [4, "lbName", ********, 4, [[[2, -98, [0, "77tA03tstMYYagIjw2Ib+N"], [5, 230, 81.6]], -99], 4, 1], [1, "11+zPvmpxPJYfRar2ZaqtX", null, null, null, 1, 0], [1, 0, 642.8019999999999, 0]], [3, "game_rankindex_03", ********, 2, [[2, -100, [0, "f78u5Xqa1BuJVHnmURJ/GG"], [5, 71, 69]], [5, -101, [0, "93cP8mFv1Pi4SpjbqDE9XZ"], 3]], [1, "e2xkeRmEdGJrl0jjXp51wz", null, null, null, 1, 0], [1, -383.33500000000004, -2.6569999999999823, 0]], [3, "game_rankindex_02", ********, 2, [[2, -102, [0, "4dG/rLyP5J9a/GUTfNdlhK"], [5, 71, 69]], [5, -103, [0, "78J4UWAkNEX7E8J3fxU5rC"], 4]], [1, "0d59D1HwdMY7ag9W7TNBPo", null, null, null, 1, 0], [1, -383.33500000000004, -2.6569999999999823, 0]], [3, "game_rankindex_01", ********, 2, [[2, -104, [0, "70AZ/WztBPh5C3RtNIkMtW"], [5, 71, 69]], [5, -105, [0, "74fxtSY1BGhaj3WKAvPeaW"], 5]], [1, "7b5fK420lKoIxbaZVMM264", null, null, null, 1, 0], [1, -383.33500000000004, -2.6569999999999823, 0]], [3, "game_rankindex_00", ********, 2, [[2, -106, [0, "04rpq25fVNZYURp+7x69ne"], [5, 61, 61]], [5, -107, [0, "6bOhhI6MFIaLpH15QhDMl9"], 6]], [1, "a5wsLgdXlAVL6iIt3SQyAB", null, null, null, 1, 0], [1, -383.33500000000004, -2.6569999999999823, 0]], [4, "lbRankIndex", ********, 2, [[[2, -108, [0, "e7M2W9iIxCb4WNrD8Nsuq8"], [5, 46.283966064453125, 50.4]], -109], 4, 1], [1, "5atfiwtV1JxoXTrUGttOz4", null, null, null, 1, 0], [1, -383.52099999999996, -0.507000000000005, 0]], [3, "game_viewrank_avatarbg", ********, 2, [[2, -110, [0, "91Alifg1RNYaqBiooN0uEP"], [5, 70, 70]], [5, -111, [0, "9dRqNVQJdNT4tpIZGh0Pfw"], 7]], [1, "75XH83IqtO8r0+wURdEELY", null, null, null, 1, 0], [1, -258.48699999999997, 0, 0]], [10, "spr<PERSON><PERSON><PERSON>", ********, 13, [[[2, -112, [0, "55ycry0bBAMpRm5LlBCP4l"], [5, 70, 70]], -113], 4, 1], [1, "828z+9s31P5K8eVI5jD5ZJ", null, null, null, 1, 0]], [3, "game_viewrank_avatarmask", ********, 2, [[2, -114, [0, "769/hJj3lAyITGcB3+b93m"], [5, 70, 70]], [5, -115, [0, "649BGbkHJCQaWzegqWhoKh"], 8]], [1, "4bBfoR+b5JebFDT7g/ttry", null, null, null, 1, 0], [1, -258.48699999999997, 0, 0]], [4, "lbName", ********, 2, [[[2, -116, [0, "fc/eSgG0hIkLkLGtgid+hD"], [5, 154, 50.4]], -117], 4, 1], [1, "8cU8qSXjBAR6xnPfq52+bD", null, null, null, 1, 0], [1, -27.124000000000024, -0.507000000000005, 0]], [4, "lbScore", ********, 2, [[[2, -118, [0, "e0n3+oOx9IDJKlx+qRrAB8"], [5, 40.65596008300781, 50.4]], -119], 4, 1], [1, "55gUxAGWNIy6UYrIBfCCJY", null, null, null, 1, 0], [1, 298.424, -0.507000000000005, 0]], [3, "lbName", ********, 5, [[2, -120, [0, "062rrialtJLYsheCTzw80+"], [5, 84, 50.4]], [8, "周排名", 28, 28, false, -121, [0, "e96YlmvMpEr4fFycZCOwhr"], [4, **********], 10]], [1, "c6JMaKpsFCXaN7TEKrbZch", null, null, null, 1, 0], [1, -376.39599999999996, 56.**************, 0]], [3, "lbName-001", ********, 5, [[2, -122, [0, "71LjYAIkdOVKKtVNzSIaDY"], [5, 56, 50.4]], [8, "头像", 28, 28, false, -123, [0, "f9uBpicVpMoJnPXB70Ugs2"], [4, **********], 11]], [1, "b48j4BNDxKva8Yx5zFWvuK", null, null, null, 1, 0], [1, -254.709, 56.**************, 0]], [3, "lbName-002", ********, 5, [[2, -124, [0, "587H6Jy6dPHays1fGfJt/9"], [5, 112, 50.4]], [8, "玩家昵称", 28, 28, false, -125, [0, "51L2Iz+/lD07q120A97+X9"], [4, **********], 12]], [1, "f5+jvGuJJAc6AYMjcVrXK5", null, null, null, 1, 0], [1, -64.55900000000003, 56.**************, 0]], [3, "lbName-003", ********, 5, [[2, -126, [0, "a5XbWuGq1H8aRcWZ2orp+b"], [5, 84, 50.4]], [8, "周积分", 28, 28, false, -127, [0, "86ajolam9A/ZSdj65xtvV4"], [4, **********], 13]], [1, "c9LOekWwpDqplbBL91tv+q", null, null, null, 1, 0], [1, 295.**************, 56.**************, 0]], [3, "lbDesc", ********, 15, [[2, -128, [0, "3brFVsT1tH2p+U+gprcMwu"], [5, 640, 289.68]], [13, "换肤说明\n输入“皮肤”可切换可使用角色\n下次进入游戏会默认选择换肤后形象\n皮肤使用条件根据周积分计算", 40, 68, false, 3, -129, [0, "b9Qd6tQU9KdZmvjFEgscSx"], [4, **********], [4, **********], 14]], [1, "4fhH0hCuJBUYeDbANKKcu9", null, null, null, 1, 0], [1, 0, 101.**************, 0]], [4, "lbSubtitle", ********, 3, [[[2, -130, [0, "abb7Cl0FxDwbwFQMNd4EPv"], [5, 36.**************, 75.6]], -131], 4, 1], [1, "92V9TLBgxLsLJIuBtu+9sW", null, null, null, 1, 0], [1, 0, 331.**************, 0]], [3, "lbSubtitle-001", ********, 3, [[6, -132, [0, "6amNFZW5hDS6X/L7DsHdSn"], [5, 120, 75.6], [0, 0, 0.5]], [13, "攻击力", 40, 60, false, 3, -133, [0, "84zbrF/ZpJ0YeLI9a/qRGT"], [4, **********], [4, **********], 16]], [1, "d1JGN0GMxBspj8XsxK0yxn", null, null, null, 1, 0], [1, -306.**************, 239.952, 0]], [3, "lbSubtitle-002", ********, 3, [[6, -134, [0, "94iiefBnVPzou7i0XBmBJM"], [5, 160, 75.6], [0, 0, 0.5]], [13, "攻击间隔", 40, 60, false, 3, -135, [0, "c1Cr42CQdKY5yOH99BDsIT"], [4, **********], [4, **********], 17]], [1, "2fuEfNpaRNuY7cw8z8Reqq", null, null, null, 1, 0], [1, 167.**************, 239.952, 0]], [3, "game_icon_atk", ********, 3, [[2, -136, [0, "11k4AWcsJJpLPCeRbS0Qct"], [5, 64, 60]], [5, -137, [0, "999XXgsd9Nx6DXheecWVvQ"], 18]], [1, "d7HZBR4CdKcaCzwP2UgipI", null, null, null, 1, 0], [1, -358.406, 240.861, 0]], [3, "game_icon_eachatk", ********, 3, [[2, -138, [0, "d7lPZWJn1OGL+rDhscJCDw"], [5, 70, 67]], [5, -139, [0, "7diJlhu0FFCKCGDD6WHTlE"], 19]], [1, "c23ogN8XFGuZeA+lUN7crl", null, null, null, 1, 0], [1, 105.66600000000005, 240.861, 0]], [4, "lbEachatkNum", ********, 3, [[[6, -140, [0, "dageVwkrhC+rw8ZcXXMKvZ"], [5, 16.839996337890625, 75.6], [0, 0, 0.5]], -141], 4, 1], [1, "37ZlDqf2pLd5nmUKvwTt0n", null, null, null, 1, 0], [1, 340.909, 239.952, 0]], [4, "lbAtkNum", ********, 3, [[[6, -142, [0, "8cHa4MiAlCNLbwP0o8Iuu3"], [5, 49.2**************, 75.6], [0, 0, 0.5]], -143], 4, 1], [1, "e8S38oK6VN27mriXnwirQq", null, null, null, 1, 0], [1, -164.73700000000002, 239.952, 0]], [17, 0, 16, [0, "bfKmCchatLaKyr3CbxiVID"]], [40, 3, 6, [0, "31ljcS1d9L17BKOM9tiz58"], [4, 4292269782], 6], [41, 10, 17, [0, "60uflFHihDpZsTg3OQWiG8"], [5, 10, 10]], [42, "pageView-horizontal<PageView>", false, 7, [0, "999Unqnm1KJYg29A0aLzJ0"], 14, 43], [20, 3, 1.1, 10, [0, "7exaVVY6ZHtKn/dXy3KXio"], [4, 4292269782], 10], [20, 3, 1.1, 11, [0, "a0lAffzE9Fv4t80inqfMSV"], [4, 4292269782], 11], [38, "皮肤名称", 56, 56, 60, false, true, 3, 18, [0, "77naev1X1JUICwKnFxw8Jl"], [4, 4291948799], [4, **********]], [39, "100", 28, 28, false, 23, [0, "4evfskZ99Iyb67DQ71iB/E"]], [17, 0, 25, [0, "88r3Mz5PdLYpUkx3qNqVLV"]], [19, "玩家姓名七个字", 22, 22, false, 27, [0, "b40dw4NYVBFY2UzEw1Z2DF"], [4, **********]], [19, "999", 22, 22, false, 28, [0, "cfYTLiBq5DTpm+QwmQ09Tr"], [4, 4278859720]], [43, false, 5, [0, "2cmXOGmNVNdasVjbIHUfSO"], 12, 2], [14, "--", 40, 60, false, 3, 34, [0, "53E5Hlgo5LY4j/foSrseen"], [4, **********], [4, **********]], [14, "1", 40, 60, false, 3, 39, [0, "bcox6L8ANDTIqpF6NYt49B"], [4, **********], [4, **********]], [14, "50", 40, 60, false, 3, 40, [0, "bbSoEcPp1GV7AuP0a0RFwA"], [4, **********], [4, **********]]], 0, [0, 8, 1, 0, 0, 1, 0, 11, 52, 0, 12, 53, 0, 13, 54, 0, 14, 55, 0, 15, 46, 0, 16, 45, 0, 17, 47, 0, 18, 44, 0, 19, 42, 0, 20, 41, 0, 21, 4, 0, 0, 1, 0, -1, 4, 0, 0, 2, 0, 0, 2, 0, 22, 49, 0, 23, 51, 0, 24, 48, 0, 25, 50, 0, 0, 2, 0, -1, 19, 0, -2, 20, 0, -3, 21, 0, -4, 22, 0, -5, 23, 0, -6, 24, 0, -7, 13, 0, -8, 26, 0, -9, 27, 0, -10, 28, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -1, 5, 0, -2, 15, 0, -3, 34, 0, -4, 35, 0, -5, 36, 0, -6, 37, 0, -7, 38, 0, -8, 39, 0, -9, 40, 0, 0, 4, 0, -1, 16, 0, -2, 6, 0, -3, 7, 0, -4, 10, 0, -5, 11, 0, -6, 18, 0, 0, 5, 0, -2, 52, 0, -1, 8, 0, -2, 29, 0, -3, 30, 0, -4, 31, 0, -5, 32, 0, 0, 6, 0, 0, 6, 0, -3, 42, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, -3, 44, 0, -1, 9, 0, -2, 17, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, -1, 12, 0, 0, 9, 0, 0, 9, 0, 0, 9, 0, -1, 14, 0, 0, 10, 0, 0, 10, 0, -3, 45, 0, 0, 11, 0, 0, 11, 0, -3, 46, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 13, 0, -1, 25, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, -1, 33, 0, 0, 16, 0, -2, 41, 0, 0, 17, 0, -2, 43, 0, 0, 18, 0, -2, 47, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, 0, 21, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, -2, 48, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, -2, 49, 0, 0, 26, 0, 0, 26, 0, 0, 27, 0, -2, 50, 0, 0, 28, 0, -2, 51, 0, 0, 29, 0, 0, 29, 0, 0, 30, 0, 0, 30, 0, 0, 31, 0, 0, 31, 0, 0, 32, 0, 0, 32, 0, 0, 33, 0, 0, 33, 0, 0, 34, 0, -2, 53, 0, 0, 35, 0, 0, 35, 0, 0, 36, 0, 0, 36, 0, 0, 37, 0, 0, 37, 0, 0, 38, 0, 0, 38, 0, 0, 39, 0, -2, 54, 0, 0, 40, 0, -2, 55, 0, 9, 1, 2, 10, 12, 3, 10, 4, 143], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 41, 42, 42, 42, 42, 43, 45, 45, 45, 45, 46, 46, 46, 46, 47, 48, 49, 50, 51, 53, 54, 55], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 1, 2, 2, 1, 1, 1, 26, 1, 3, 4, 5, 6, 1, 3, 4, 5, 6, 3, 4, 5, 6, 2, 2, 1, 2, 2, 2, 2, 2], [4, 5, 6, 9, 10, 11, 12, 13, 14, 15, 0, 0, 0, 0, 0, 16, 0, 0, 17, 18, 19, 20, 21, 4, 1, 2, 3, 22, 5, 1, 2, 3, 6, 1, 2, 3, 0, 0, 23, 0, 0, 0, 0, 0]], [[[15, "UnitSelectSkin"], [9, "UnitSelectSkin", ********, [-5], [[2, -2, [0, "1eSj/q3XRORL2IfGCw8Hi/"], [5, 1080, 800]], [44, -4, [0, "76T0v0yMVH0bdVuOuzGypE"], -3]], [1, "e1WR47UTBHW5RBdmCVkzx1", null, null, null, -1, 0], [1, 540, 0, 0]], [10, "Sprite", ********, 1, [[[2, -6, [0, "1c5b+oxRtAFb5vmrhl9rM7"], [5, 40, 36]], [30, -7, [0, "72trEMEvJDdI0f1p3mVJYi"]], -8], 4, 4, 1], [1, "b68GKMjAxKEIWiqOs44IQw", null, null, null, 1, 0]], [45, 2, [0, "11lp5VygtJnaK91cfHqUNo"]]], 0, [0, 8, 1, 0, 0, 1, 0, 27, 3, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, 0, 2, 0, -3, 3, 0, 9, 1, 8], [], [], []]]]