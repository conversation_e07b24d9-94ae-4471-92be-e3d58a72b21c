[1, ["7dJwPE0axHDZqTVbguJfyI@6c48a"], ["_textureSource"], ["cc.SpriteFrame"], 0, [{"name": "huodou-idle_12", "rect": {"x": 145, "y": 101, "width": 614, "height": 475}, "offset": {"x": -3.5, "y": -50.5}, "originalSize": {"width": 911, "height": 576}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-307, -237.5, 0, 307, -237.5, 0, -307, 237.5, 0, 307, 237.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [145, 475, 759, 475, 145, 0, 759, 0], "nuv": [0.15916575192096596, 0, 0.8331503841931943, 0, 0.15916575192096596, 0.8246527777777778, 0.8331503841931943, 0.8246527777777778], "minPos": {"x": -307, "y": -237.5, "z": 0}, "maxPos": {"x": 307, "y": 237.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]]