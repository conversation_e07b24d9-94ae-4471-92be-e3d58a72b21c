syntax = "proto3";
package pb;

// 
message GameScoreReqPb {
  int32 score = 1;
}

// <p>绫绘弿杩�</p>
message RoomBusinessConfigPb {
  // 鑳屾櫙鍥惧湴鍧�
  string bgImg = 1;
  // 骞挎挱闊抽鍦板潃
  string broadcastAudioUrl = 2;
  // 寮�濮嬭闊�
  string startAudioUrl = 3;
}

// 
message RoomGameAwardPush {
  repeated RoomUserInfoPb ranks = 1;
  // 
  RoomUserInfoPb my = 2;
  int32 total = 3;
  string awardId = 4;
  string awardUrl = 5;
  string awardName = 6;
  string address = 7;
  int32 maxScore = 8;
  int32 awardPeopleCount = 9;
  bool emptyAward = 10;
  bool result = 11;
}

// 
message RoomGameEndPush {
  repeated RoomUserInfoPb users = 1;
}

// 
message RoomGamePush {
  repeated RoomUserInfoPb users = 1;
}

// <p>绫绘弿杩�</p>
message RoomMainInfoPb {
  // 鎴块棿id
  string busId = 1;
  // 鎴块棿鐘舵��
  RoomStatusEnum status = 2;
  // 鏄剧ず鐨勭帺瀹朵俊鎭�
  repeated RoomUserInfoPb userList = 3;
  // 涓嬩竴涓樁娈垫椂闂存埑
  int64 nextStepTime = 4;
  // 娓告垙寮�鍚椂闂�
  int64 gameStartTime = 5;
  // 鎬讳汉鏁�
  int32 totalPeople = 6;
  // 鏄惁棣栧満
  bool firstRound = 7;
  // 褰撳墠娓告垙鍓╀綑鏃堕棿(绉�)
  int64 gameTime = 8;
  string busName = 9;
  // 娲诲姩鍚嶇О
  string activityName = 10;
  // 娲诲姩ID
  int64 activityId = 11;
}

// <p>鎴块棿鐘舵��-鏋氫妇绫�</p>
enum RoomStatusEnum {
  // 绛夊緟娲诲姩寮�鍚樁娈�
  WAITING = 0;
  // 娓告垙鍑嗗闃舵
  GAME_READY = 1;
  // 娓告垙寮�濮嬪ぇ灞忓�掕鏃�
  GAME_BIG_COUNTDOWN = 2;
  // 娓告垙寮�濮嬪�掕鏃�
  GAME_COUNTDOWN = 3;
  // 娓告垙寮�鍚樁娈�
  GAME_START = 4;
  // 娓告垙缁撴潫闃舵
  GAME_END = 5;
  // 娲诲姩鍏抽棴
  CLOSE = 6;
  // 娲诲姩鏈紑鍚�
  UN_OPEN = 7;
}

// 
message RoomStatusPushPb {
  // 鎴块棿鐘舵��
  RoomStatusEnum status = 1;
  // 涓嬩竴涓樁娈垫椂闂存埑
  int64 nextStepTime = 2;
  // 涓嬩竴鍦哄紑濮嬫椂闂�
  int64 nextGameStartTime = 3;
  // 娓告垙鏃堕暱
  int64 gameTime = 4;
  // 娲诲姩鍚嶇О
  string activityName = 5;
  // 娲诲姩ID
  int64 activityId = 6;
  // 鎴块棿鍦烘鍞竴id
  string roundId = 7;
}

// 
message RoomUserInfoPb {
  string userId = 1;
  string nickName = 2;
  string avatarUrl = 3;
  int64 score = 4;
  int32 rank = 5;
  string awardUrl = 6;
}

// 
message RoomUserMainPb {
  // 鎴块棿鐘舵��
  RoomStatusEnum status = 1;
  // 涓嬩竴涓樁娈垫椂闂存埑
  int64 nextStepTime = 2;
  // 褰撳墠娓告垙鍓╀綑鏃堕棿(绉�)
  int64 gameTime = 3;
  // 涓嬩竴鍦烘父鎴忓紑鍚椂闂存椂闂存埑
  int64 nextGameStartTime = 4;
  string busName = 5;
  // 褰撳墠浜烘暟
  int32 totalPeople = 6;
  // 娲诲姩鍚嶇О
  string activityName = 7;
  // 娲诲姩ID
  int64 activityId = 8;
}

// 
message RoomUserPushPb {
  // 
  RoomUserInfoPb userInfo = 1;
  bool add = 2;
  int32 total = 3;
}

