[1, 0, 0, [["cc.Json<PERSON>set", ["_name", "json"], 1]], [[0, 0, 1, 3]], [[0, "__文档描述", {"_mainifest.json": "配置的md5信息", "_total.json": "所有配置表的合并信息", "config.d.ts": "TypeScript描述文件", "constant.json": {"配置表描述": "常量表", "camp1": "加入神仙弹幕 - int", "camp2": "加入妖怪弹幕 - int", "giftScoreReservoirSharing": "积分池X%分给前三名 - int", "giftScoreReservoirReserve": "积分池X%分给进入下一局 - int", "hurtColour": "伤害数字展示 - string"}, "monster.json": {"配置表描述": "妖族怪物表", "jsonId": "id - string", "name": "怪物名称 - string", "camp": "阵营\n1.神仙\n2.妖怪 - int", "eliteMonster": "是否精英怪\n0.不是\n1.是 - int", "monsterType": "相同怪物标识\n数字相同代表同一只怪 - int", "monsterLevel": "怪物等级 - int", "monsterHp": "血量 - int", "attack": "攻击\n最小值|最大值 - string", "callNum": "召唤数量 - int", "animationScale": "动画缩放 - string", "attackCooldown": "攻击速度\n秒/攻击1次\n-1不会攻击 - string", "attacKeffect": "怪物攻击特效id - string", "attacKeffectInterval": "怪物攻击特效动画间隔 - string", "skill": "技能id\n（不配置则无技能） - int", "movementSpeed": "移动速度 - int", "rebornTime": "复活时间/秒\n（0则不能复活） - int", "monsterScore": "击杀怪物获得积分 - int", "standbyAnimation": "待机动画 - string", "moveAnimation": "移动动画 - string", "attackAnimation": "攻击动画 - string", "deathAnimation": "死亡动画\n（蹲下消失） - string", "rebornAnimation": "复活动画 - string", "skillAnimation": "技能动画 - string", "giftId": "礼物表id - string", "giftName": "礼物表名称 - string", "kuaishouGiftId": "快手\n礼物ID - string", "kuaishouGiftName": "快手\n物名称 - string", "kuaishouGiftNum": "快手物数量\n（单局游戏累计） - int", "douyinGiftId": "抖音\n召唤精英怪礼物编号 - string", "douyinGiftName": "抖音\n召唤精英怪礼物名称 - string", "douyinGiftNum": "抖音礼物数量\n（单局游戏累计） - int"}, "attacKeffect.json": {"配置表描述": "攻击特效表", "jsonId": "id - string", "name": "特效名称 - string", "type": "特效类型\n1.近战刀光\n2.远程火球\n3.远程光束\n4.远程剑气（类似月牙天冲）\n5.单次攻击多段伤害 - int", "attacDistance": "攻击距离 - int", "doubleHit": "多段伤害次数 - int", "Animation": "动画文件 - string", "impactAnimation": "受击动画 - string"}, "skin.json": {"配置表描述": "皮肤表", "jsonId": "id - string", "name": "皮肤名称 - string", "camp": "阵营\n1.神仙\n2.妖怪 - int", "HP": "血量 - int", "attack": "攻击力 - string", "attackCooldown": "攻击速度\n秒/攻击1次\n-1不会攻击 - string", "attacKeffect": "攻击特效id - string", "skill": "技能id\n（不配置则无技能） - int", "standbyAnimation": "待机动画 - string", "moveAnimation": "移动动画 - string", "attackAnimation": "攻击动画 - string", "skillAnimation": "技能动画 - string", "InitialIimage": "是否初始形象\n0.不是\n1.是 - int", "skinRole": "介绍页面角色图 - string", "openCondition": "累计多少积分解锁 - int", "barrage": "触发换肤弹幕 - string"}, "skill.json": {"配置表描述": "技能表", "jsonId": "id - string", "name": "技能名称 - string", "type": "技能类型\n1.飞剑\n2.巨型飞剑\n3.天火\n4.闪电\n5.飞沙走石\n6.护天阵\n7.三味真火\n8.天外陨石\n9.南天门生命回复\n101.给身边友方增加状态\n102.敌方所有角色施加技能间隔\n103.给敌方所有角色施加晕眩\n\n203.火灵珠\n204.五雷钉\n205.混元伞\n\n207.八卦炉\n208.女娲石 - int", "skillDescribe": "技能描述 - string", "skillLevel": "技能等级 - int", "skillDamage": "技能伤害 - int", "skillCooldown": "技能释放间隔/秒\n-1.光环技能永远有效 - int", "lightningNum": "4.闪电弹射次数 - int", "guardTime": "守护时间/秒\n（6护天阵使用） - int", "healing": "9.南天门生命回复值/百分比 - int", "healingTime": "9.南天门生命回复时间/秒 - int", "hpDeclinePercentage": "血量低于XX% - string", "state": "技能附加状态id - string", "skillRange": "技能伤害范围\n0.单体\n数字：范围距离 - int", "skillAnimation": "技能动画 - string", "impactAnimation": "受击动画 - string", "skillTarget": "技能目标\n1.敌方\n2.自己\n3.自己及友军 - int"}, "state.json": {"配置表描述": "状态表", "jsonId": "id - string", "name": "状态名称 - string", "type": "状态类型\n1.灼烧\n2.增减角色移动速度（百分比）\n3.晕眩\n4.增减加攻击力（百分比）\n5.增减攻击间隔（百分比）\n6.增减技能试放间隔（百分比） - int", "stateLevel": "状态等级 - int", "stateDescribe": "持续时间/秒 - string", "hurt": "伤害 - int", "hurtInterval": "伤害间隔时间/秒\n - int", "percentage": "效果百分比\n类型45678使用 - string", "icon": "状态图标 - string"}, "weapon.json": {"配置表描述": "法宝表", "jsonId": "id - string", "name": "法宝名称 - string", "showWeapon": "是否展示法宝\n1.不展示\n2.展示 - int", "weaponType": "法宝位置\n（同一个位置法宝只能存在一个，优先高等级法宝） - int", "describe": "描述 - string", "weaponLevel": "法宝等级 - int", "skilId": "法宝技能id - string", "skillNum": "试放次数\n-1，不限制\n数字，试放次数 - int", "icon": "法宝图标 - string", "skillAnimation": "法宝试放技能动画 - string", "giftId": "礼物表id - string", "giftName": "礼物表名称 - string", "kuaishouGiftId": "快手\n礼物ID - string", "kuaishouGiftName": "快手\n物名称 - string", "kuaishouGiftNum": "快手物数量\n（单局游戏累计） - int", "douyinGiftId": "抖音\n召唤精英怪礼物编号 - string", "douyinGiftName": "抖音\n召唤精英怪礼物名称 - string", "douyinGiftNum": "抖音礼物数量\n（单局游戏累计） - int"}, "gift.json": {"配置表描述": "礼物对应效果", "jsonId": "id - string", "name": "名称 - string", "giftType": "礼物类型\n1.刷的礼物\n2.弹幕\n3.点赞 - int", "kuaishouGiftId": "快手\n礼物ID - string", "kuaishouGiftName": "快手\n礼物名称 - string", "douyinGiftId": "抖音\n怪礼物编号 - string", "douyinGiftName": "抖音\n礼物名称 - string", "automationCamp": "是否自动加入阵营\n0.不是\n1.是 - int", "integra": "礼物获得积分 - int", "integralUpperLimit": "一局获得积分上限 - int", "kuaishouGiftPicture": "快手礼物图 - string", "douyinGiftPicture": "抖音礼物图片 - string"}, "selectDifficulty.json": {"配置表描述": "游戏难度", "jsonId": "id - string", "name": "难度名称 - string", "Hp": "南天门血\n血量1\n - string", "time": "游戏时间/秒\n时间1|时间2|时间3\n主播可难度调整 - string"}, "gameLevel.json": {"配置表描述": "游戏关卡", "jsonId": "id - string", "name": "名称 - string", "gameLevel": "关卡数字 - int"}, "monsterRefresh.json": {"配置表描述": "刷怪配置", "jsonId": "id - string", "name": "刷怪轮次 - string", "gameLevel": "归属管卡 - string", "refresh": "刷怪波次 - int", "monsterId": "怪物ID - string", "monsterTotal": "怪物数量 - int", "monsterInterval": "刷怪间隔（秒） - string", "monsterNum": "刷怪频次（1秒X个） - int", "type": "怪物类型\n1.小兵\n2.首领级（小boss）\n3.妖王级（大boss） - int", "refreshInterval": "刷怪结束后几秒开始下一波 - int", "preview": "boss预警 - string", "admission": "关底boss进场动画 - string"}, "animation.json": {"配置表描述": "动画配置表", "jsonId": "id\n000.刀光\n100.受击\n200.妖怪技能\n300.神仙技能\n400.怪物攻击动画\n500.怪物移动动画\n600.怪物技能动画\n700.神族攻击动画\n800.神族移动动画 - string", "sample": "帧数 - int", "duration": "时长 - int", "speed": "速度 - int", "wrapMode": "动画是否循环\n1.播放1次\n2.循环 - int", "path": "路径\nskilleffect：技能\natkeffect：攻击\nroleanim：怪物 - string", "name": "动画名字 - string", "target": "技能目标\n1本人 \n2目标 \n3 路径 \n0没有 - int", "XAxisOffset": "X轴偏移 - string", "YAxisOffset": "Y轴偏移 - string", "notes": "备注 - string"}}]], 0, 0, [], [], []]