import { log, warn } from "cc";
import { Singleton } from "../utils/Singleton";

/*
*   事件管理器，事件的监听、触发、移除
*   
*   
*/
export type EventManagerCallFunc = (eventName: string, eventData: any) => void;

interface CallBackTarget {
    callBack: EventManagerCallFunc,
    target: any,
}

export class EventManager extends Singleton {



    private _eventListeners: { [key: string]: CallBackTarget[] } = {};

    private getEventListenersIndex(eventName: string, callBack: EventManagerCallFunc, target?: any): number {
        let index = -1;
        for (let i = 0; i < this._eventListeners[eventName].length; i++) {
            let iterator = this._eventListeners[eventName][i];
            if (iterator.callBack == callBack && (!target || iterator.target == target)) {
                index = i;
                break;
            }
        }
        return index;
    }
    /**
     * 事件监听
     * @param eventName 事件名称
     * @param callBack 回调
     * @param target 
     * @returns 
     */
    addEventListener(eventName: string, callBack: EventManagerCallFunc, target?: any): boolean {
        if (!eventName) {
            warn("eventName is empty" + eventName);
            return;
        }

        if (null == callBack) {
            log('addEventListener callBack is nil');
            return false;
        }
        let callTarget: CallBackTarget = { callBack: callBack, target: target };
        if (null == this._eventListeners[eventName]) {
            this._eventListeners[eventName] = [callTarget];

        } else {
            let index = this.getEventListenersIndex(eventName, callBack, target);
            if (-1 == index) {
                this._eventListeners[eventName].push(callTarget);
            }
        }

        return true;
    }

    setEventListener(eventName: string, callBack: EventManagerCallFunc, target?: any): boolean {
        if (!eventName) {
            warn("eventName is empty" + eventName);
            return;
        }

        if (null == callBack) {
            log('setEventListener callBack is nil');
            return false;
        }
        let callTarget: CallBackTarget = { callBack: callBack, target: target };
        this._eventListeners[eventName] = [callTarget];
        return true;
    }
    /**
     * 移除监听事件
     * @param eventName 事件名称
     * @param callBack 回调
     * @param target 
     */
    removeEventListener(eventName: string, callBack: EventManagerCallFunc, target?: any) {
        if (null != this._eventListeners[eventName]) {
            let index = this.getEventListenersIndex(eventName, callBack, target);
            if (-1 != index) {
                this._eventListeners[eventName].splice(index, 1);
            }
        }
    }
    /**
     * 广播事件
     * @param eventName 事件名称 
     * @param eventData 数据
     */
    async raiseEvent(eventName: string, eventData?: any) {
        //log(`==================== raiseEvent ${eventName} begin  `);
        if (null != this._eventListeners[eventName]) {
            // 将所有回调提取出来，再调用，避免调用回调的时候操作了事件的删除
            let callbackList: CallBackTarget[] = [];
            for (const iterator of this._eventListeners[eventName]) {
                callbackList.push({ callBack: iterator.callBack, target: iterator.target });
            }
            for (const iterator of callbackList) {
                await iterator.callBack.call(iterator.target, eventName, eventData);
            }
        }
        //log(`==================== raiseEvent ${eventName} end`);
    }
}

