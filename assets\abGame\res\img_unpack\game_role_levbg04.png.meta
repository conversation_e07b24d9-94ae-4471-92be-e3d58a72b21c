{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "f448377c-2d3e-4801-a16f-7b066d9fd1a7", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "f448377c-2d3e-4801-a16f-7b066d9fd1a7@6c48a", "displayName": "game_role_levbg04", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "f448377c-2d3e-4801-a16f-7b066d9fd1a7", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "f448377c-2d3e-4801-a16f-7b066d9fd1a7@f9941", "displayName": "game_role_levbg04", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 135, "height": 48, "rawWidth": 135, "rawHeight": 48, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-67.5, -24, 0, 67.5, -24, 0, -67.5, 24, 0, 67.5, 24, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 48, 135, 48, 0, 0, 135, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-67.5, -24, 0], "maxPos": [67.5, 24, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "f448377c-2d3e-4801-a16f-7b066d9fd1a7@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "f448377c-2d3e-4801-a16f-7b066d9fd1a7@6c48a"}}