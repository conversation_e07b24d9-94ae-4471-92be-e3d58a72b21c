import user from '../../../protos/user.js'
import BroadcastCtrl from '../BroadcastCtrl'
import { MessageCmdEvent, MessageRoomSubCmd } from '../MessageEvent'
import { _decorator } from 'cc'
const { ccclass } = _decorator

/**
 * 游戏状态
 */
@ccclass()
export default class ResGameStatus extends BroadcastCtrl {
	////////////////////消息单例////////////////////
	public static get inst() {
		return this.getInst(ResGameStatus)
	}

	constructor() {
		super()
		this.cmd = MessageCmdEvent.userCmd
		this.subCmd = MessageRoomSubCmd.roomStatusPush
		this.addListen()
	}

	receive(code: number, data: any) {
		console.log("ResGameStatus:", user.pb.RoomStatusPushPb.decode(data));
		let res = user.pb.RoomStatusPushPb.decode(data).json;

		super.receive(code, JSON.parse(res))
	}
}
