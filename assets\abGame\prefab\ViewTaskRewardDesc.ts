import { _decorator, Component, Label, Node } from 'cc';
import { ViewBase } from '../../scripts/libs/manager/ViewBase';
import { ConfigHelper } from '../../scripts/config/ConfigHelper';
const { ccclass, property } = _decorator;

@ccclass('ViewTaskRewardDesc')
export class ViewTaskRewardDesc extends ViewBase {
    @property(Label)
    private lbDesc: Label = null;

    @property(Label)
    private lbDayTask: Label = null;

    @property(Label)
    private lbWeekTask: Label = null;

    @property(Label)
    private lbMonthTask: Label = null;

    protected onLoadCompleted(): void {
        let dayTask = ConfigHelper.getInstance().getTaskConfigByType(1);
        let WeekTask = ConfigHelper.getInstance().getTaskConfigByType(2);
        let MonthTask = ConfigHelper.getInstance().getTaskConfigByType(3);
        this.lbDayTask.string = dayTask.name;
        this.lbWeekTask.string = WeekTask.name;
        this.lbMonthTask.string = MonthTask.name;
    }
}


