import { _decorator, Button, Component, instantiate, Label, log, Node, PageView, Prefab } from 'cc';
import { ViewBase } from '../../scripts/libs/manager/ViewBase';
import ScrollViewProCom from '../../scripts/libs/utils/ScrollViewProCom';
import { ConfigHelper } from '../../scripts/config/ConfigHelper';
import { UnitSelectSkin } from './Unit/UnitSelectSkin';
import Tool from '../../scripts/libs/utils/Tool';
import Net from '../../scripts/Net';
import { xcore } from '../../scripts/libs/xcore';
import { UnitItemScore } from './Unit/UnitItemScore';
const { ccclass, property } = _decorator;

@ccclass('ViewSkinSelect')
export class ViewSkinSelect extends ViewBase {

    @property(PageView)
    private pvSkin: PageView = null;

    @property(Label)
    private lbSkinName: Label = null;

    @property(Prefab)
    private pfbUnitSkin: Prefab = null;

    @property(Button)
    private btnArr01: Button = null;

    @property(Button)
    private btnArr02: Button = null;

    @property(Label)
    private lbAtkNum: Label = null;

    @property(Label)
    private lbEachatkNum: Label = null;

    @property(Label)
    private lbNeedScore: Label = null;


    private _isRankLoading: boolean = false;

    private _selectSkinNum: number = 0;
    private _itemNum: number = 0;
    private _configs: any[] = []


    @property({ type: ScrollViewProCom, displayName: "垂直布局列表" })
    public scrollviewV: ScrollViewProCom = null;

    protected onLoadCompleted(): void {

    }

    async start() {
        this._configs = ConfigHelper.getInstance().getSkinConfgs();

        for (let i = 0; i < this._configs.length; i++) {
            let skin = instantiate(this.pfbUnitSkin);
            skin.getComponent(UnitSelectSkin).setData(this._configs[i])
            this.pvSkin.addPage(skin);
        }
        this.btnArr01.node.active = false;

        this.addButtonEvent(this.btnArr01, () => {
            this.pageTo(-1);
        }, this)
        this.addButtonEvent(this.btnArr02, () => {
            this.pageTo(1);
        }, this)


        this.pvSkin.node.on('page-turning', this.onRefreshPageview.bind(this, true), this);
        this.scrollviewV.node.on('scroll-to-bottom', this.onRefreshData, this);
        this.onRefreshPageview(true);
    }
    async onRefreshData(isFirstLoad: boolean) {

        if (!xcore.gameData.scoreRankInfo) {
            xcore.gameData.scoreRankInfo = []
        }
        this._isRankLoading = true;
        //let key = ConfigHelper.getInstance().getRankKeyByWeek();
        let key = ConfigHelper.getInstance().getRankKeyByMonth();
        await Net.getRankInfo(key, xcore.gameData.scoreRankInfo.length, xcore.gameData.scoreRankInfo.length + 10);
        if (!isFirstLoad && xcore.gameData.scoreRankInfo.length == this._itemNum) {
            xcore.ui.showToast('到底啦');
            return
        }
        this._itemNum = xcore.gameData.scoreRankInfo.length;
        let targetScore = this._configs[this._selectSkinNum].openCondition;
        let datas = xcore.gameData.scoreRankInfo.filter(e => e.score >= targetScore);
        this.scrollviewV.setView(datas, (n, data) => {
            n.getComponent(UnitItemScore).setData(data);
        })
        this._isRankLoading = false;
    }
    onRefreshPageview(isFirstLoad: boolean = false) {
        this._selectSkinNum = this.pvSkin.getCurrentPageIndex();
        let config = this._configs[this._selectSkinNum];
        this.lbSkinName.string = config.name;
        let atks = config.attack.split('|');
        this.lbAtkNum.string = atks[0] == atks[1] ? atks[0] : `${atks[0]}~${atks[1]}`;
        this.lbEachatkNum.string = `${config.attackCooldown}秒`;
        this.lbNeedScore.string = config.openCondition > 0 ? `周${config.openCondition}积分可使用` : '默认皮肤'
        if (this._selectSkinNum == 0) {
            this.btnArr01.node.active = false;
            this.btnArr02.node.active = true;
        } else if (this._selectSkinNum == this.pvSkin.content.children.length - 1) {
            this.btnArr01.node.active = true;
            this.btnArr02.node.active = false;
        } else {
            this.btnArr01.node.active = true;
            this.btnArr02.node.active = true;
        }
        this.onRefreshData(isFirstLoad);
    }

    pageTo(num: number) {
        let index = this.pvSkin.getCurrentPageIndex();
        let toIndex = index + num;
        this.pvSkin.scrollToPage(toIndex);
        this.scrollviewV.scrollToTop(0.1)
    }


}


