import { instantiate, log, Node } from "cc";
import { Singleton } from "../../scripts/libs/utils/Singleton";
import { xcore } from "../../scripts/libs/xcore";
import { C_Bundle, C_View, E_EVENT, E_GiftMessageType, E_SkillType, E_TowerPointType } from "../../scripts/ConstGlobal";
import { UnitGiftMessage } from "../prefab/Unit/UnitGiftMessage";
import { UnitBossMessage } from "../prefab/Unit/UnitBossMessage";
import { UnitSkillUpMessage } from "../prefab/Unit/UnitSkillUpMessage";
import { UnitTowerPointShowMessage } from "../prefab/Unit/UnitTowerPointShowMessage";
import { UnitUserInfoMessage } from "../prefab/Unit/UnitUserInfoMessage";
import { UnitSkinRewardMessage } from "../prefab/Unit/UnitSkinRewardMessage";
import { UnitTaskRewardMessage } from "../prefab/Unit/UnitTaskRewardMessage";


export interface IGiftMessage {
    type: E_GiftMessageType,
    /**用户userid */
    userId: string
    /**用户名称 */
    name: string,
    /**头像 */
    avatar: string,
    skinId: string,
    minAtkNum: number,
    maxAtkNum: number,
    dimonEffects: any,
    speed: string
    giftType: string,
    skillType: E_SkillType
    towerPointType: E_TowerPointType
    anim: string
    monsterName: string
    totalNum: number
    num: number
    lev: number
    fromLev: number
    user?: any
    desc?: string
}

/**
 * 侧栏礼物消息的显示
 * 技能升级效果
 * 大哥进场效果
 */
export class GiftMessageMgr extends Singleton {
    //内容显示父节点
    private ndContent: Node = null;
    //内容显示父节点
    private ndContent2: Node = null;

    private giftUnitMessagePool: UnitGiftMessage[] = [];

    private towerpointUnitMessage: UnitTowerPointShowMessage = null;
    private skillUnitMessage: UnitSkillUpMessage = null;
    private bossUnitMessage: UnitBossMessage = null;
    private userinfoUnitMessage: UnitUserInfoMessage = null;
    private skinrewardUnitMessage: UnitSkinRewardMessage = null;

    private taskrewardUnitMessage: UnitTaskRewardMessage = null

    //侧栏底
    private giftMessages00: IGiftMessage[] = []
    //侧栏1
    private giftMessages01: IGiftMessage[] = []
    //侧栏2
    private giftMessages02: IGiftMessage[] = []
    //侧栏3
    private giftMessages03: IGiftMessage[] = []


    private giftMessages04: IGiftMessage[] = []



    private bossOrSkillMessage: IGiftMessage[] = [];

    private skinrewardMessage: IGiftMessage[] = [];

    private taskrewardMessage: IGiftMessage[] = [];


    /**大哥进场显示 */

    private isBossShowMsg: boolean = false;

    /**技能升级显示 */

    private isSkillUpShowMsg: boolean = false;

    /** */
    private isUserinfoShowMsg: boolean = false;

    private isSkinRewardShowMsg: boolean = false;

    private isTaskRewardShowMsg: boolean = false;

    //轮播
    private _interval: any
    private _syncRate: number = 1.8;

    private _addIndex: number = 0;





    init(ndContent: Node, ndContent2: Node) {

        this.giftUnitMessagePool = [];
        this.ndContent = ndContent;
        this.ndContent2 = ndContent2;
        xcore.event.addEventListener(E_EVENT.GiftMessage, this.addMessage, this);
        this._interval = setInterval(() => {
            this.showMessage()
        }, 1000 * this._syncRate);

    }


    clear() {

        this.giftMessages00.length = 0;
        this.giftMessages01.length = 0;
        this.giftMessages02.length = 0;
        this.giftMessages03.length = 0;
        this.giftMessages04.length = 0;
        this.bossOrSkillMessage.length = 0;
        this.skinrewardMessage.length = 0;
        this.taskrewardMessage.length = 0;
        this.isBossShowMsg = false;
        this.isSkillUpShowMsg = false;
        this.isUserinfoShowMsg = false;
        this.isSkinRewardShowMsg = false;
        this.isTaskRewardShowMsg = false;
        this.ndContent.destroyAllChildren();

    }

    destroy() {
        this.clear();
        this._interval && clearInterval(this._interval);
        xcore.event.removeEventListener(E_EVENT.GiftMessage, this.addMessage, this);
    }

    async addMessage(event: string, data: IGiftMessage) {
        let pool;
        if (data.type == E_GiftMessageType.GetBigSkill) {
            pool = this.giftMessages00;
        } else if (data.type == E_GiftMessageType.RankBoss) {
            pool = this.bossOrSkillMessage;
        } else if (data.type == E_GiftMessageType.SkillUp) {
            pool = this.bossOrSkillMessage;

        } else if (data.type == E_GiftMessageType.GetSmallSkill) {
            pool = this.giftMessages01;
        } else if (data.type == E_GiftMessageType.Gift) {
            pool = this.giftMessages02;
        } else if (data.type == E_GiftMessageType.UseProp) {
            pool = this.giftMessages03;
        } else if (data.type == E_GiftMessageType.UserInfo) {
            pool = this.giftMessages04;
        } else if (data.type == E_GiftMessageType.SkinReward) {
            pool = this.skinrewardMessage;
        } else if (data.type == E_GiftMessageType.TaskReward) {
            pool = this.taskrewardMessage;
        }
        else {
            if (this._addIndex == 0) {
                pool = this.giftMessages01;
            } else if (this._addIndex == 1) {
                pool = this.giftMessages02;
            } else {
                pool = this.giftMessages03;
            }
            this._addIndex += 1;
            if (this._addIndex >= 3) {
                this._addIndex = 0;
            }
        }

        let message = pool.find(e => this.checkSame(e, data));
        if (message) {
            message.num += data.num;
            message.totalNum = data.totalNum;

        } else {
            pool.push(data);
        }
    }

    async showMessage() {
        if (this.giftMessages01.length > 0) {
            let message = this.giftMessages01.shift();
            this.getUnitMessage(message, 2);
        }
        if (this.giftMessages02.length > 0) {
            let message = this.giftMessages02.shift();
            this.getUnitMessage(message, 1);
        }
        if (this.giftMessages03.length > 0) {
            let message = this.giftMessages03.shift();
            this.getUnitMessage(message, 0);
        }

        if (this.giftMessages00.length > 0) {
            let message = this.giftMessages00.shift();
            this.getUnitMessage(message, 0);
        }

        this.showBossOrSkillMessage();
        this.showUserInfoMessage();
        this.showSkinRewardMessage();
        this.showTaskRewardMessage();
    }
    showBossOrSkillMessage() {
        if (this.bossOrSkillMessage.length > 0) {
            if (this.isBossShowMsg || this.isSkillUpShowMsg) return
            let message = this.bossOrSkillMessage.shift();
            if (message) {
                this.getUnitBossOrSkillMessage(message)
            }

        }
    }
    showUserInfoMessage() {
        if (this.giftMessages04.length > 0) {
            if (this.isUserinfoShowMsg) return
            let message = this.giftMessages04.shift();
            this.getUserInfoMessage(message);
        }
    }
    showSkinRewardMessage() {
        if (this.skinrewardMessage.length > 0) {
            if (this.isSkinRewardShowMsg) return
            let message = this.skinrewardMessage.shift();
            if (message) {
                this.getSkinRewardMessage(message)
            }

        }
    }
    showTaskRewardMessage() {

        if (this.taskrewardMessage.length > 0) {
            if (this.isTaskRewardShowMsg) return
            let message = this.taskrewardMessage.shift();
            if (message) {
                this.getTaskRewardMessage(message)
            }
        }

    }

    checkSame(e: IGiftMessage, data: IGiftMessage) {
        switch (data.type) {
            case E_GiftMessageType.Gift:
                return e.giftType == data.giftType

            case E_GiftMessageType.GetSmallSkill:

                return e.skillType == data.skillType
            case E_GiftMessageType.GetBigSkill:

                return false
            case E_GiftMessageType.UseProp:

                return e.skillType == data.skillType
            case E_GiftMessageType.Kill:

                return e.userId == data.userId

            default:
                return false

        }
    }

    //回收实例化对象
    killMessage(msg: UnitGiftMessage) {
        this.giftUnitMessagePool.push(msg);
    }
    killBossMessage(msg: UnitBossMessage) {
        this.isBossShowMsg = false;
        this.showBossOrSkillMessage();
    }
    killTowerPointMessage(msg: UnitTowerPointShowMessage) {
        this.isSkillUpShowMsg = false;
        this.showBossOrSkillMessage();
    }
    killSkillMessage(msg) {
        this.isSkillUpShowMsg = false;
        this.showBossOrSkillMessage();
    }
    killUserInfoMessage(msg: UnitUserInfoMessage) {
        this.isUserinfoShowMsg = false;
        this.showUserInfoMessage();
    }
    killSkinRewardMessage(msg: UnitSkinRewardMessage) {
        this.isSkinRewardShowMsg = false;
        this.showSkinRewardMessage();
    }
    killTaskRewardMessage(msg: UnitTaskRewardMessage) {
        this.isTaskRewardShowMsg = false;
        this.showTaskRewardMessage();
    }
    //获取实例化对象
    async getUnitMessage(data: IGiftMessage, index: number): Promise<UnitGiftMessage> {
        if (!this.giftUnitMessagePool) {
            this.giftUnitMessagePool = [];
        }
        let message = null as UnitGiftMessage;
        if (this.giftUnitMessagePool.length <= 0) {
            let pfb = await xcore.res.bundleLoadPrefab(C_Bundle.abGame, './prefab/Unit/UnitGiftMessage');
            message = instantiate(pfb).getComponent(UnitGiftMessage);
        } else {
            message = this.giftUnitMessagePool.shift();
        }
        message.node.parent = this.ndContent;

        message.setData(data, index);
        return message;
    }

    async getUserInfoMessage(message: IGiftMessage) {
        if (!this.userinfoUnitMessage) {
            let pfb = await xcore.res.bundleLoadPrefab(C_Bundle.abGame, './prefab/Unit/UnitUserInfoMessage');
            this.userinfoUnitMessage = instantiate(pfb).getComponent(UnitUserInfoMessage);

        }
        this.isUserinfoShowMsg = true;
        this.userinfoUnitMessage.node.parent = this.ndContent2;
        this.userinfoUnitMessage.setData({
            type: E_GiftMessageType.UserInfo,
            userId: message.userId,
            name: message.name,
            avatar: message.avatar,
            skinId: message.skinId,
            minAtkNum: message.minAtkNum,
            maxAtkNum: message.maxAtkNum,
            dimonEffects: message.dimonEffects,
            speed: message.speed,
        });
    }
    async getSkinRewardMessage(message: IGiftMessage) {
        if (!this.skinrewardUnitMessage) {
            let pfb = await xcore.res.bundleLoadPrefab(C_Bundle.abGame, './prefab/Unit/UnitSkinRewardMessage');
            this.skinrewardUnitMessage = instantiate(pfb).getComponent(UnitSkinRewardMessage);

        }
        this.isSkinRewardShowMsg = true;
        this.skinrewardUnitMessage.node.parent = this.ndContent2;
        this.skinrewardUnitMessage.setData({
            type: E_GiftMessageType.SkinReward,
            skinId: message.skinId, user: message.user, level: message.lev
        });


    }
    async getTaskRewardMessage(message: IGiftMessage) {
        if (!this.taskrewardUnitMessage) {
            let pfb = await xcore.res.bundleLoadPrefab(C_Bundle.abGame, './prefab/Unit/UnitTaskRewardMessage');
            this.taskrewardUnitMessage = instantiate(pfb).getComponent(UnitTaskRewardMessage);

        }
        this.isTaskRewardShowMsg = true;
        this.taskrewardUnitMessage.node.parent = this.ndContent2;
        this.taskrewardUnitMessage.setData({
            type: E_GiftMessageType.TaskReward,
            avatar: message.avatar,
            nickname: message.name,
            totalNum: message.totalNum,
            desc: message.desc,
 
        });

    }
    async getUnitBossOrSkillMessage(message: IGiftMessage) {

        if (message.type == E_GiftMessageType.RankBoss) {
            this.isBossShowMsg = true;
            /* if (!this.bossUnitMessage) {
                let pfb = await xcore.res.bundleLoadPrefab(C_Bundle.abGame, './prefab/Unit/UnitBossMessage');
                this.bossUnitMessage = instantiate(pfb).getComponent(UnitBossMessage);
            }
 */
            let pfb = await xcore.res.bundleLoadPrefab(C_Bundle.abGame, './prefab/Unit/UnitBossMessage');
            this.bossUnitMessage = instantiate(pfb).getComponent(UnitBossMessage);
            this.bossUnitMessage.node.parent = this.ndContent2;
            this.bossUnitMessage.setData(message);

        } else if (message.type == E_GiftMessageType.SkillUp) {
            this.isSkillUpShowMsg = true;
            //炮台技能升级
            if (message.towerPointType) {
                if (!this.towerpointUnitMessage) {
                    let pfb = await xcore.res.bundleLoadPrefab(C_Bundle.abGame, './prefab/Unit/UnitTowerPointShowMessage');
                    this.towerpointUnitMessage = instantiate(pfb).getComponent(UnitTowerPointShowMessage);

                }

                this.towerpointUnitMessage.node.parent = this.ndContent2;
                this.towerpointUnitMessage.setData({
                    fromLev: message.fromLev,
                    anim: message.anim, lev: message.lev, type: message.towerPointType,
                });
            }
            //仙族技能升级
            else {
                if (!this.skillUnitMessage) {
                    let pfb = await xcore.res.bundleLoadPrefab(C_Bundle.abGame, './prefab/Unit/UnitSkillUpMessage');
                    this.skillUnitMessage = instantiate(pfb).getComponent(UnitSkillUpMessage);

                }

                this.skillUnitMessage.node.parent = this.ndContent2;
                this.skillUnitMessage.setData({ message });
            }


        }
    }

}


