import { Component, EventTouch, Node, ScrollView, _decorator, log } from "cc";

const { ccclass, property } = _decorator;

interface TEventTouch extends EventTouch {
    simulate: boolean
    sham?: boolean
}

@ccclass
export default class ViewGroupNesting extends Component {


    private events: TEventTouch[] = [];

    onLoad() {
        this.node.on(Node.EventType.TOUCH_START, this.onTouchHandle, this, true);
        this.node.on(Node.EventType.TOUCH_MOVE, this.onTouchHandle, this, true);
        this.node.on(Node.EventType.TOUCH_END, this.onTouchHandle, this, true);
        this.node.on(Node.EventType.TOUCH_CANCEL, this.onTouchHandle, this, true);
    }
    
    private onTouchHandle(event: TEventTouch, type) {
        if (event.sham || event.simulate || event.target === this.node) return;
        let delta = event.getDelta();
        if (Math.abs(delta.x) > 3 && Math.abs(delta.x) * 1.2 > Math.abs(delta.y)) {
            return
        }
        const cancelEvent: TEventTouch = new EventTouch(event.getTouches(), event.bubbles, type);
        cancelEvent.type = event.type;
        cancelEvent.touch = event.touch;
        cancelEvent.sham = true;
        this.events.push(cancelEvent);
    }

    update() {
        if (this.events.length === 0) return;
        for (let index = 0; index < this.events.length; index++) {
            this.node.dispatchEvent(this.events[index]);
        }
        this.events.length = 0;
    }
}