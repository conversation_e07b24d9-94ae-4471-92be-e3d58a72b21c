[{"__type__": "cc.Prefab", "_name": "ViewTestGift", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "ViewTestGift", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [{"__id__": 130}, {"__id__": 132}], "_prefab": {"__id__": 134}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "ndRoot", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 27}, {"__id__": 41}, {"__id__": 47}, {"__id__": 55}, {"__id__": 61}, {"__id__": 67}, {"__id__": 73}, {"__id__": 93}, {"__id__": 113}], "_active": true, "_components": [{"__id__": 127}], "_prefab": {"__id__": 129}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "btnUser", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 4}, {"__id__": 10}, {"__id__": 16}], "_active": true, "_components": [{"__id__": 22}, {"__id__": 24}], "_prefab": {"__id__": 26}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Sprite", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [], "_active": true, "_components": [{"__id__": 5}, {"__id__": 7}], "_prefab": {"__id__": 9}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 4}, "_enabled": true, "__prefab": {"__id__": 6}, "_contentSize": {"__type__": "cc.Size", "width": 340, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "69vaR9Ek9JKbDNGxVkRxt9"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 4}, "_enabled": true, "__prefab": {"__id__": 8}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 180, "g": 180, "b": 180, "a": 255}, "_spriteFrame": {"__uuid__": "7dca5b46-dd60-4db4-b1de-5fd11582a790@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "22euUEuz9DjrXjsz/15kPX"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d5Qvm28GZODoRusK8ycQiQ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [], "_active": true, "_components": [{"__id__": 11}, {"__id__": 13}], "_prefab": {"__id__": 15}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 10}, "_enabled": true, "__prefab": {"__id__": 12}, "_contentSize": {"__type__": "cc.Size", "width": 42.255859375, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "36HJjHTLRK4pMI4cGUkZtG"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 10}, "_enabled": true, "__prefab": {"__id__": 14}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "label", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "27pBIQX7RHzqsg5vCTwKV+"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7ehvVyaR9Kh7rnBdh+BmH7", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "tagOn", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [], "_active": false, "_components": [{"__id__": 17}, {"__id__": 19}], "_prefab": {"__id__": 21}, "_lpos": {"__type__": "cc.Vec3", "x": 136.889, "y": -1.016, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 16}, "_enabled": true, "__prefab": {"__id__": 18}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 36}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "49HobvaApDabrHPQPmnO5Y"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 16}, "_enabled": true, "__prefab": {"__id__": 20}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "57520716-48c8-4a19-8acf-41c9f8777fb0@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ffLXsUVX9EzZPELwnMkS7h"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "af//T279pNbaeTe5yzlxl9", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 23}, "_contentSize": {"__type__": "cc.Size", "width": 340, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0bIAp3w/dCC5mU44n5uoUE"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 25}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.05, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "18upWkhQpFl6Nr8UTdGMEJ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4cA9Rn0ypL6onHfBvqb5f7", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btnOption", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 28}], "_active": true, "_components": [{"__id__": 34}, {"__id__": 36}, {"__id__": 38}], "_prefab": {"__id__": 40}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 27}, "_children": [], "_active": true, "_components": [{"__id__": 29}, {"__id__": 31}], "_prefab": {"__id__": 33}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 28}, "_enabled": true, "__prefab": {"__id__": 30}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 30.240000000000002}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9b68smaZZCB5TSrV6Fq0dv"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 28}, "_enabled": true, "__prefab": {"__id__": 32}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 211, "g": 0, "b": 0, "a": 255}, "_string": "", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 21, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 24, "_overflow": 2, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "89vM3qIuxBdoPsge8HEGdV"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "eehG9CQZRHtpfpBELSSWr9", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 27}, "_enabled": true, "__prefab": {"__id__": 35}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "69XHbqiXBPWLpjjB67F6JQ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 27}, "_enabled": true, "__prefab": {"__id__": 37}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "61I/zfiURL4bdWGr5UqAQM"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 27}, "_enabled": true, "__prefab": {"__id__": 39}, "clickEvents": [], "_interactable": true, "_transition": 2, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_pressedSprite": {"__uuid__": "544e49d6-3f05-4fa8-9a9e-091f98fc2ce8@f9941", "__expectedType__": "cc.SpriteFrame"}, "_disabledSprite": {"__uuid__": "951249e0-9f16-456d-8b85-a6ca954da16b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 27}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dblncwzPtAzIO4IRK50rme"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "05Fq5KBYJL2ayKoNBi2T+w", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "sprFrame", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 42}, {"__id__": 44}], "_prefab": {"__id__": 46}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 41}, "_enabled": true, "__prefab": {"__id__": 43}, "_contentSize": {"__type__": "cc.Size", "width": 760, "height": 1200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "faWz5NuDBPx7KJuN+yFhxB"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 41}, "_enabled": true, "__prefab": {"__id__": 45}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "7d8f9b89-4fd1-4c9f-a3ab-38ec7cded7ca@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "01ZY+4PN1GZps5ZPphfHUs"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9dLuAw4K5IVYZajMDHCi0o", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btnClose", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 48}, {"__id__": 50}, {"__id__": 52}], "_prefab": {"__id__": 54}, "_lpos": {"__type__": "cc.Vec3", "x": 307.202, "y": 689.981, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 47}, "_enabled": true, "__prefab": {"__id__": 49}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "55F/gfjU1Ezrf+Xy9U1H4I"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 47}, "_enabled": true, "__prefab": {"__id__": 51}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "55VTlgea9IuZe9aFlxrVlj"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 47}, "_enabled": true, "__prefab": {"__id__": 53}, "clickEvents": [], "_interactable": true, "_transition": 2, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_pressedSprite": {"__uuid__": "544e49d6-3f05-4fa8-9a9e-091f98fc2ce8@f9941", "__expectedType__": "cc.SpriteFrame"}, "_disabledSprite": {"__uuid__": "951249e0-9f16-456d-8b85-a6ca954da16b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 47}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ba2V1J/vtPrI3UJKSij0Xp"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c3HVR3GvhANany7jA/0zy8", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbDesc", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 56}, {"__id__": 58}], "_prefab": {"__id__": 60}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 469.037, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 55}, "_enabled": true, "__prefab": {"__id__": 57}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f7qoiM+ypF1K7rZIuWv7kg"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 55}, "_enabled": true, "__prefab": {"__id__": 59}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 32, "g": 32, "b": 32, "a": 255}, "_string": "暂未选择用户", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9dSkAJ1kRGTrP8iwyyN/oX"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "95+kF73h1EyYCcaVEoO3+0", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 62}, {"__id__": 64}], "_prefab": {"__id__": 66}, "_lpos": {"__type__": "cc.Vec3", "x": -176.59, "y": 268.242, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 61}, "_enabled": true, "__prefab": {"__id__": 63}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "34HflLDN1MubCtpoSxQ5f2"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 61}, "_enabled": true, "__prefab": {"__id__": 65}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 32, "g": 32, "b": 32, "a": 255}, "_string": "指令", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "01+d0FMlhON6Kn5ZcE3Wv6"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "39S3heLFNBPJu3KY+Zxv6w", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "ndContentKey", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 68}, {"__id__": 70}], "_prefab": {"__id__": 72}, "_lpos": {"__type__": "cc.Vec3", "x": -176.59, "y": 242.694, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 67}, "_enabled": true, "__prefab": {"__id__": 69}, "_contentSize": {"__type__": "cc.Size", "width": 340, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "51tQt/0uFAT5folFIVXwYm"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 67}, "_enabled": true, "__prefab": {"__id__": 71}, "_resizeMode": 1, "_layoutType": 3, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 10, "_spacingY": 10, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d4X6W4ieJBZ42iLErZe7Dm"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c6L7ZBwIZJsZWd3eRGWkBB", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "user", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 74}], "_active": true, "_components": [{"__id__": 88}, {"__id__": 90}], "_prefab": {"__id__": 92}, "_lpos": {"__type__": "cc.Vec3", "x": 157.755, "y": 37.482, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "view", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 73}, "_children": [{"__id__": 75}], "_active": true, "_components": [{"__id__": 81}, {"__id__": 83}, {"__id__": 85}], "_prefab": {"__id__": 87}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 74}, "_children": [], "_active": true, "_components": [{"__id__": 76}, {"__id__": 78}], "_prefab": {"__id__": 80}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 300, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 75}, "_enabled": true, "__prefab": {"__id__": 77}, "_contentSize": {"__type__": "cc.Size", "width": 340, "height": -10}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "65G6W3LzRFjbnicZgTc6nU"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 75}, "_enabled": true, "__prefab": {"__id__": 79}, "_resizeMode": 1, "_layoutType": 2, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 10, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8eOCCEYHZBmoult0Fz3Mt8"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "80aQQEzN5C1LvQB6RQGxOZ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 74}, "_enabled": true, "__prefab": {"__id__": 82}, "_contentSize": {"__type__": "cc.Size", "width": 340, "height": 600}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2aSYovZM1BQofnGi3SKsaW"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 74}, "_enabled": true, "__prefab": {"__id__": 84}, "_type": 0, "_inverted": false, "_segments": 64, "_alphaThreshold": 0.1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "58ZTiKk5VOtb/Bwq0Brx86"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 74}, "_enabled": true, "__prefab": {"__id__": 86}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_miterLimit": 10, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "71geKE7IlDlodX8J0wZz2L"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "19zEh6P5pKp6aX0ujcFiNT", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 73}, "_enabled": true, "__prefab": {"__id__": 89}, "_contentSize": {"__type__": "cc.Size", "width": 340, "height": 600}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "06m2Bb4GFNUa3Vu2I2osp0"}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 73}, "_enabled": true, "__prefab": {"__id__": 91}, "bounceDuration": 0.23, "brake": 0.75, "elastic": true, "inertia": true, "horizontal": false, "vertical": true, "cancelInnerEvents": true, "scrollEvents": [], "_content": {"__id__": 75}, "_horizontalScrollBar": null, "_verticalScrollBar": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "aawCQmad1FeLIZ4PdoF7pk"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "923Hs4cC9LcIqs/laW1Crr", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Toggle", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 94}, {"__id__": 100}], "_active": true, "_components": [{"__id__": 106}, {"__id__": 108}, {"__id__": 110}], "_prefab": {"__id__": 112}, "_lpos": {"__type__": "cc.Vec3", "x": 83.547, "y": -656.357, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Checkmark", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 93}, "_children": [], "_active": true, "_components": [{"__id__": 95}, {"__id__": 97}], "_prefab": {"__id__": 99}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 94}, "_enabled": true, "__prefab": {"__id__": 96}, "_contentSize": {"__type__": "cc.Size", "width": 26, "height": 26}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a9JHn0CEZGsrw3wcB/qTL3"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 94}, "_enabled": true, "__prefab": {"__id__": 98}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "158e7e52-3220-4cd7-9694-713e0e6e8278@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a3V6HX+cpCdqaHMj3vPFoR"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "175L9BSFRC4rRXL5w3pO9e", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 93}, "_children": [], "_active": true, "_components": [{"__id__": 101}, {"__id__": 103}], "_prefab": {"__id__": 105}, "_lpos": {"__type__": "cc.Vec3", "x": -102.01, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 100}, "_enabled": true, "__prefab": {"__id__": 102}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e1QJkgPGlC75fScR5KCv7y"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 100}, "_enabled": true, "__prefab": {"__id__": 104}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "是否自动选择阵营", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f6952RyoZMYZ534KijzPFO"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "53cGywo/JPWoSSEg4Ql//h", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 93}, "_enabled": true, "__prefab": {"__id__": 107}, "_contentSize": {"__type__": "cc.Size", "width": 28, "height": 28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cccGmrhUxEwZNZy183/lTw"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 93}, "_enabled": true, "__prefab": {"__id__": 109}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "11bdc4b0-64a8-4eb7-a2a7-9fb9e233e977@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "00+8mnaRRFdKZMdPHzJjs6"}, {"__type__": "cc.Toggle", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 93}, "_enabled": true, "__prefab": {"__id__": 111}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "11bdc4b0-64a8-4eb7-a2a7-9fb9e233e977@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 93}, "checkEvents": [], "_isChecked": true, "_checkMark": {"__id__": 97}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f5MhKsYxpL27padEbdv8zt"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b8qDj69W1AbKh9Q632d6vV", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btnRandomCreateTest", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 114}], "_active": true, "_components": [{"__id__": 120}, {"__id__": 122}, {"__id__": 124}], "_prefab": {"__id__": 126}, "_lpos": {"__type__": "cc.Vec3", "x": -5.618000000000052, "y": 640.4269999999999, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 113}, "_children": [], "_active": true, "_components": [{"__id__": 115}, {"__id__": 117}], "_prefab": {"__id__": 119}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 114}, "_enabled": true, "__prefab": {"__id__": 116}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 30.240000000000002}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dcxK9ex+BC+401tT8YVx1N"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 114}, "_enabled": true, "__prefab": {"__id__": 118}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 211, "g": 0, "b": 0, "a": 255}, "_string": "", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 21, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 24, "_overflow": 2, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "747uMppGJJmatmn0fiBd6J"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "44FH2/u2dPY4YKEGmCTp1S", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 113}, "_enabled": true, "__prefab": {"__id__": 121}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7aBbL3CkFCia6yGWoFFhYJ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 113}, "_enabled": true, "__prefab": {"__id__": 123}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bdKAkuqhVCwZouWLfzKb19"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 113}, "_enabled": true, "__prefab": {"__id__": 125}, "clickEvents": [], "_interactable": true, "_transition": 2, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_pressedSprite": {"__uuid__": "544e49d6-3f05-4fa8-9a9e-091f98fc2ce8@f9941", "__expectedType__": "cc.SpriteFrame"}, "_disabledSprite": {"__uuid__": "951249e0-9f16-456d-8b85-a6ca954da16b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 113}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b5EaLpoulDV7FZZkXVSKhV"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "80RI5h6ulAiadEbr5GS3la", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 128}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "02ieot0PtAZIZvNz4AU7nq"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d7ze3X5OtLUa4Z6jU2/TSP", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 131}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "76Ds1qRXROtoqEczhT8+Bg"}, {"__type__": "d81a4c+NrRPHavj7IWxdY0B", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 133}, "maskOpacity": 200, "animStyle": 1, "ndRoot": {"__id__": 2}, "sprFrame": {"__id__": 44}, "btnClose": {"__id__": 52}, "ndBtn": {"__id__": 27}, "ndBtnUser": {"__id__": 3}, "btnRandomTest": {"__id__": 124}, "lbDesc": {"__id__": 58}, "togSelectGroup": {"__id__": 110}, "ndContent": {"__id__": 67}, "ndContentUser": {"__id__": 75}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1frwtOEy5GYYCy58f5iR0Q"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "13O2TTy+FPtY192bW+Robr", "instance": null, "targetOverrides": null}]