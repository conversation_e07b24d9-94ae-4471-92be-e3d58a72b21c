import { _decorator, Button, Component, Label, log, Node, Slider, Sprite } from 'cc';
import { ViewBase } from '../../scripts/libs/manager/ViewBase';
import ElectronAPI from '../../ElectronAPI';
import { xcore } from '../../scripts/libs/xcore';
import App from '../../scripts/App';
const { ccclass, property } = _decorator;

@ccclass('ViewSetting')
export class ViewSetting extends ViewBase {

    @property(Slider)
    private slSound: Slider = null;

    @property(Sprite)
    private sprSoundBar: Sprite = null;

    @property(Label)
    private lbSound: Label = null;

    @property(Slider)
    private slMusic: Slider = null;

    @property(Sprite)
    private sprMusicBar: Sprite = null;

    @property(Button)
    private btnConfirm: Button = null;

    @property(Label)
    private lbId: Label = null;

    @property(Label)
    private lbMusic: Label = null;


    protected onLoadCompleted(): void {

        if (xcore.gameData && xcore.gameData.soundData) {
            this.slSound.progress = xcore.gameData.soundData.sound;
            this.slMusic.progress = xcore.gameData.soundData.music;
            this.refreshUI();
        }
        this.slSound!.node.on('slide', () => {
            //log("this.slSound.progress", this.slSound.progress);
            this.refreshUI();
        }, this);
        this.slMusic!.node.on('slide', () => {
            //log("this.slMusic.progress", this.slMusic.progress);
            this.refreshUI();
        }, this);

        this.btnConfirm.node.on('click', () => {
            this.closeSelf();
            if (!xcore.gameData.soundData) {
                xcore.gameData.soundData = {}
            }
            xcore.gameData.soundData.sound = this.slSound.progress;
            xcore.gameData.soundData.music = this.slMusic.progress;

            xcore.sound.setSoundNum(this.slSound.progress);
            xcore.sound.setMusicNum(this.slMusic.progress);

        }, this)

        //this.lbId.string = xcore.gameData.baseInfo.roomId;
    }

    refreshUI() {
        this.sprSoundBar.fillRange = this.slSound.progress;
        this.lbSound.string = Math.floor(100 * this.slSound.progress).toString();
        this.sprMusicBar.fillRange = this.slMusic.progress;
        this.lbMusic.string = Math.floor(100 * this.slMusic.progress).toString();
        xcore.sound.setSoundNum(this.slSound.progress);
        xcore.sound.setMusicNum(this.slMusic.progress);
    }

    protected onDestroyCompleted(): void {
        App.getInstance().updateOperation();
    }
}


