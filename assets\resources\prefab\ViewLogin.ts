import { _decorator, But<PERSON>, Component, EditBox, log, Node, Sprite } from 'cc';
import { ViewBase } from '../../scripts/libs/manager/ViewBase';
import { xcore } from '../../scripts/libs/xcore';
import App from '../../scripts/App';
import { C_Bundle, C_Scene, E_Channel } from '../../scripts/ConstGlobal';
import Net from '../../scripts/Net';
import Tool from '../../scripts/libs/utils/Tool';
 
const { ccclass, property } = _decorator;

@ccclass('ViewLogin')
export class ViewLogin extends ViewBase {

    @property(EditBox)
    private edTxt: EditBox = null;

    @property(Button)
    private btnLogin: Button = null;

    protected onLoadCompleted(): void {
        this.btnLogin.node.on('click', async () => {
            if (!this.edTxt.string) {
                xcore.ui.showToast('请先输入匹配码');
                return
            }
            try {
                this.btnLogin.interactable = false;
                this.btnLogin.getComponent(Sprite).grayscale = true;
                await this.login();
                await this.loadRes();
                App.getInstance().initOperation();
                xcore.ui.switchScene(C_Bundle.abMain, C_Scene.Main);
            } catch (err) {
                this.btnLogin.interactable = true;
                this.btnLogin.getComponent(Sprite).grayscale = false;
                xcore.ui.showToast('登录失败 err:' + err);
            }
        }, this)

    }

    async loadRes() {
        let configUrl = 'https://gz-cdn-1258783731.cos.ap-guangzhou.myqcloud.com/2024/2nantianmen/game_configs/dev/_total.json';
        if (!xcore.config) {
            xcore.config = await xcore.res.remoteLoadJson(configUrl);
        }
        log("gameConfig:", xcore.config);
    }

    //登录
    async login() {

        let query = Tool.getCommandLineArgs();
        await Net.getCombatId()
        console.log("query", query, query.token);
        let parmas = {

        } as any
        if (query.token) {




            xcore.gameData.token = query.token;


            xcore.channel = E_Channel.TIKTOK;

        }
        //本地测试
        else {

            xcore.gameData.appId = '65e700e54ad6a'
            xcore.gameData.channelId = '1008'
            xcore.channel = E_Channel.GAME560;
            xcore.gameData.userName = 'akayip'
            xcore.gameData.password = '841892819'

        }



        await Net.login(xcore.channel, xcore.gameData.channelId, xcore.gameData.token, xcore.gameData.userName, xcore.gameData.password, xcore.gameData.appId, xcore.gameData.combatId);
        await Net.getLiverInfo();
        if (xcore.channel == E_Channel.TIKTOK) {
            await Net.updateTokenToIo();
        }


        console.log('liveinfo', xcore.gameData.baseInfo);
        return new Promise((resolve, reject) => {
            this.scheduleOnce(() => {
                resolve(true)
            }, 0.5)
        })
    }

}


