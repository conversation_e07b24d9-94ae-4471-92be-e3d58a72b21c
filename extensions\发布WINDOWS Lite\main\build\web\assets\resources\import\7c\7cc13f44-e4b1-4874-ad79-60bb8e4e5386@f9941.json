[1, ["7cwT9E5LFIdK15YLuOTlOG@6c48a"], ["_textureSource"], ["cc.SpriteFrame"], 0, [{"name": "huodou-idle_05", "rect": {"x": 144, "y": 122, "width": 665, "height": 454}, "offset": {"x": 21, "y": -61}, "originalSize": {"width": 911, "height": 576}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-332.5, -227, 0, 332.5, -227, 0, -332.5, 227, 0, 332.5, 227, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [144, 454, 809, 454, 144, 0, 809, 0], "nuv": [0.15806805708013172, 0, 0.8880351262349067, 0, 0.15806805708013172, 0.7881944444444444, 0.8880351262349067, 0.7881944444444444], "minPos": {"x": -332.5, "y": -227, "z": 0}, "maxPos": {"x": 332.5, "y": 227, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]]