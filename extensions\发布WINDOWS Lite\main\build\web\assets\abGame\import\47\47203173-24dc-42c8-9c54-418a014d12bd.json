[1, ["5a/M8bDEdFZ4wdRlmpWQao@f9941", "24X8INrVlHuJgya+Yncu6n@f9941", "52fHu7D8hGm5vLaoALoXCl", "a9C+8/kqFPXJvBubJNT3+r@f9941", "b9nIcXRDtGd5iyL84l5lpl@f9941", "0bGLmQBF5EUpm5CjBY2AUk@f9941", "aageco5a1MqaQK/lxUBaUe@f9941", "78FqCAaT9Fu4UGICSBAAvO@f9941", "45HqLUpzlDIJrYTgureG7X@f9941"], ["node", "_spriteFrame", "_font", "root", "lbNum", "lbName", "spr<PERSON><PERSON><PERSON>", "sprMask", "sprFrame", "data"], [["cc.Node", ["_name", "_layer", "_children", "_components", "_prefab", "_lpos", "_parent"], 1, 2, 9, 4, 5, 1], ["cc.Node", ["_name", "_layer", "_parent", "_components", "_prefab", "_lpos", "_lscale"], 1, 1, 12, 4, 5, 5], ["cc.Prefab", ["_name"], 2], ["cc.UITransform", ["node", "__prefab", "_contentSize"], 3, 1, 4, 5], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.Sprite", ["node", "__prefab"], 3, 1, 4], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_lineHeight", "_isSystemFontUsed", "node", "__prefab", "_color"], -2, 1, 4, 5], ["cc.Layout", ["_resizeMode", "_layoutType", "_spacingX", "_affectedByScale", "node", "__prefab"], -1, 1, 4], ["7860aohVe1J9oF7PLMCHD0V", ["node", "__prefab", "sfFrams", "sfMasks", "sprFrame", "sprMask", "spr<PERSON><PERSON><PERSON>", "lbName", "lbNum"], 3, 1, 4, 3, 3, 1, 1, 1, 1, 1]], [[4, 0, 2], [3, 0, 1, 2, 1], [6, 0, 1, 2, 3, 4, 5, 5], [1, 0, 1, 2, 3, 4, 5, 3], [5, 0, 1, 1], [1, 0, 1, 2, 3, 4, 5, 6, 3], [7, 0, 1, 2, 3, 4, 5, 6, 7, 6], [2, 0, 2], [0, 0, 1, 2, 3, 4, 5, 3], [0, 0, 1, 6, 2, 3, 4, 5, 3], [8, 0, 1, 2, 3, 4, 5, 5], [9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 1]], [[7, "UnitSkinDebris"], [8, "UnitSkinDebris", 33554432, [-9, -10, -11, -12], [[1, -2, [0, "10A1c/QPJK+ITdn2qzMLrY"], [5, 96, 96]], [11, -8, [0, "00A9g+z0xApaKZF5CImeU2"], [0, 1, 2, 3], [4, 5, 6, 7], -7, -6, -5, -4, -3]], [2, "a4JA649yVOQ71Hc9nOzZcs", null, null, null, -1, 0], [1, 48, 0, 0]], [9, "Node", 33554432, 1, [-15, -16], [[1, -13, [0, "b9/2L1SnhN4ZosiW8MBbwZ"], [5, 82.16398620605469, 100]], [10, 1, 1, 6, true, -14, [0, "eeNfOG7QhIDKjXRn1XHZyE"]]], [2, "c01nQyz/pNoLw1gQbak4wD", null, null, null, 1, 0], [1, 0, -55.019000000000005, 0]], [3, "game_unitframe_skindebris01", 33554432, 1, [[[1, -17, [0, "19Eule+5tFYrlUThcBgIpq"], [5, 96, 96]], -18], 4, 1], [2, "6ewY6Zmj1D4LqXhercvbHd", null, null, null, 1, 0], [1, 0, 8.19399999999996, 0]], [3, "game_unitframe_skindebrismask01", 33554432, 1, [[[1, -19, [0, "dbjiH9xclDbZjo1o+XpnJC"], [5, 102, 102]], -20], 4, 1], [2, "3cPIJq4INOBIcWS/sbj/sD", null, null, null, 1, 0], [1, 0, 8.19399999999996, 0]], [3, "spr<PERSON><PERSON><PERSON>", 33554432, 1, [[[1, -21, [0, "64uEpW3yFDBr3DGAZ/ETP7"], [5, 80, 80]], -22], 4, 1], [2, "97xFhwUndMzpZqiDkIzx13", null, null, null, 1, 0], [1, 0, 8.19399999999996, 0]], [5, "lbName", 33554432, 2, [[[1, -23, [0, "6ewF7D4QNMDZsZ8w4M11on"], [5, 111.84799194335938, 100.8]], -24], 4, 1], [2, "a9T6MMmahGr4+p2O5wcRAe", null, null, null, 1, 0], [1, -13.1199951171875, 0, 0], [1, 0.5, 0.5, 1]], [5, "lbNum", 33554432, 2, [[[1, -25, [0, "f1ycAbtelL+7q8YvVdquNH"], [5, 40.47998046875, 100.8]], -26], 4, 1], [2, "65JJL2RZxIX5CTr293cYta", null, null, null, 1, 0], [1, 30.961997985839844, 0, 0], [1, 0.5, 0.5, 1]], [4, 3, [0, "c3d2+Wvo9FsLSYgZg/IM0O"]], [4, 4, [0, "33NR77VSNH57WC5v0R+Svx"]], [4, 5, [0, "fdOY1hP/JMCqRI08PC5tuP"]], [6, "碎片+", 44, 44, 80, false, 6, [0, "d4N5RWAypL/Z12BOb8JZS1"], [4, 4281023312]], [6, "--", 44, 44, 80, false, 7, [0, "9b7rLB62VGdq6qtATtwz69"], [4, 4278958086]]], 0, [0, 3, 1, 0, 0, 1, 0, 4, 12, 0, 5, 11, 0, 6, 10, 0, 7, 9, 0, 8, 8, 0, 0, 1, 0, -1, 3, 0, -2, 4, 0, -3, 5, 0, -4, 2, 0, 0, 2, 0, 0, 2, 0, -1, 6, 0, -2, 7, 0, 0, 3, 0, -2, 8, 0, 0, 4, 0, -2, 9, 0, 0, 5, 0, -2, 10, 0, 0, 6, 0, -2, 11, 0, 0, 7, 0, -2, 12, 0, 9, 1, 26], [0, 0, 0, 0, 0, 0, 0, 0, 8, 9, 11, 12], [-1, -2, -3, -4, -1, -2, -3, -4, 1, 1, 2, 2], [0, 3, 4, 5, 1, 6, 7, 8, 0, 1, 2, 2]]