import { _decorator, Component, Label, Node, size, Sprite, SpriteFrame, v3 } from 'cc';
import { xcore } from '../../../scripts/libs/xcore';
import { StringUtil } from 'db://assets/scripts/libs/utils/StringUtil';
const { ccclass, property } = _decorator;

@ccclass('UnitTopRank')
export class UnitTopRank extends Component {

    @property(Sprite)
    private sprTitle: Sprite = null;

    @property([SpriteFrame])
    private sprTitles: SpriteFrame[] = []

    @property(Sprite)
    private sprFrame: Sprite = null;

    @property([SpriteFrame])
    private sprFrames: SpriteFrame[] = []

    @property(Sprite)
    private sprAvatar: Sprite = null;

    @property(Node)
    private ndIconUp: Node = null;

    @property(Label)
    private lbName: Label = null;

    @property(Label)
    private lbRankNum: Label = null;

    @property(Label)
    private lbRoundNum: Label = null;

    @property(Label)
    private lbRoundChange: Label = null;

    @property(Label)
    private lbScore: Label = null;

    @property(Label)
    private lbScoreDesc: Label = null;



    setData(data: any, nickName: string, iconUrl: string, rank: number, upscore: number, level: number, oldWeekRank: number, weekRank: number) {
        this.lbName.string = StringUtil.sub(nickName || '匿名用户', 9, true);
        this.lbRankNum.string = `${rank + 1}`;
        xcore.res.remoteLoadSprite(iconUrl, this.sprAvatar, size(70, 70));
        this.lbScore.string = data.score;
        this.lbScoreDesc.string = `积分池 +${data.poolScore}\n${upscore > 0 ? `关卡加成 +${upscore}` : ''}`;
        this.lbRoundNum.string = `${level}`;
        let pos = [v3(0, 20), v3(-240, 0), v3(240, 0)];
        this.node.setPosition(pos[rank]);
        this.sprFrame.spriteFrame = this.sprFrames[rank];
        this.sprTitle.spriteFrame = this.sprTitles[rank];
        this.lbRankNum.string = weekRank < 0 ? '' : (weekRank + 1).toString();
        this.ndIconUp.active = oldWeekRank > 0 && weekRank < oldWeekRank;

    }
}


