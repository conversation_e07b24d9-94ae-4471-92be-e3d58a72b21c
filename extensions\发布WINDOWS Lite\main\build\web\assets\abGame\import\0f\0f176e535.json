[1, ["4bs10AwvBMF4kUhMi/m3dm", "52fHu7D8hGm5vLaoALoXCl", "73cFsvnEtFKol8v2Ae8edS@6c48a", "0fCA/ytgBOOILfaV5Ibkyc@6c48a", "66V7o+PWlJOYh6NkcTPbdH@6c48a", "66V7o+PWlJOYh6NkcTPbdH@f9941", "8faM3hQF1BRJ42RSJkfQ5B@f9941", "4faJTJ1KlAEpdcsi40iUc1@f9941", "5b9gWt389PnrCrg94mHw6f@f9941", "60EzrAcGZNd6K4F7V6fzFS@f9941", "d7a+b+DSZPMYfV1KZST343", "39cc2QmuZLJ7G4qG3uIQGH", "8faM3hQF1BRJ42RSJkfQ5B@6c48a", "a4Awt23oVGe4Wn+uVJiOZF@6c48a"], ["node", "_skeletonData", "_spriteFrame", "_textureSource", "_font", "root", "animAddHp", "animIconInvincible", "lbInvincibleTime", "sprBar", "ndPgs", "lbLev", "lbHp", "anim", "data"], [["cc.Node", ["_name", "_layer", "_components", "_prefab", "_children", "_parent", "_lpos"], 1, 9, 4, 2, 1, 5], ["cc.Node", ["_name", "_layer", "_active", "_parent", "_components", "_prefab", "_lpos", "_lscale"], 0, 1, 12, 4, 5, 5], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.Sprite", ["_sizeMode", "_type", "_fillRange", "node", "__prefab", "_spriteFrame"], 0, 1, 4, 6], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_lineHeight", "_isSystemFontUsed", "_outlineWidth", "_enableOutline", "node", "__prefab", "_color"], -4, 1, 4, 5], "cc.SpriteFrame", ["sp.Skeleton", ["defaultSkin", "defaultAnimation", "_preCacheMode", "_premultipliedAlpha", "node", "__prefab", "_skeletonData"], -1, 1, 4, 6], ["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["29221bHNehCFYfFcJLvXSB6", ["node", "__prefab", "anim", "lbHp", "lbLev", "ndPgs", "sprBar", "lbInvincibleTime", "animIconInvincible", "animAddHp"], 3, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.Animation", ["node", "__prefab"], 3, 1, 4]], [[9, 0, 2], [11, 0, 1, 2, 3, 4, 5, 5], [2, 0, 1, 2, 1], [0, 0, 1, 5, 2, 3, 6, 3], [6, 0, 1, 3, 2, 4, 5, 6, 5], [7, 0, 1, 2, 3, 4, 5], [3, 3, 4, 5, 1], [1, 0, 1, 3, 4, 5, 3], [1, 0, 1, 3, 4, 5, 6, 7, 3], [1, 0, 1, 3, 4, 5, 6, 3], [6, 0, 1, 2, 4, 5, 4], [8, 0, 2], [0, 0, 1, 4, 2, 3, 3], [0, 0, 1, 5, 4, 2, 3, 6, 3], [0, 0, 1, 5, 4, 2, 3, 3], [1, 0, 2, 1, 3, 4, 5, 6, 4], [2, 0, 1, 1], [2, 0, 1, 2, 3, 1], [10, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 1], [3, 0, 3, 4, 5, 2], [3, 1, 0, 2, 3, 4, 4], [12, 0, 1, 1], [4, 0, 1, 2, 3, 4, 6, 5, 7, 8, 9, 8], [4, 0, 1, 3, 4, 5, 7, 8, 6], [4, 0, 1, 2, 7, 8, 9, 4]], [[[[5, "jn_nantianmeihuixue_01", "\njn_nantianmeihuixue_01.png\nsize: 1253,408\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\n311\n  rotate: false\n  xy: 1182, 319\n  size: 69, 87\n  orig: 129, 136\n  offset: 31, 25\n  index: -1\n3112\n  rotate: false\n  xy: 2, 10\n  size: 296, 396\n  orig: 512, 512\n  offset: 108, 44\n  index: -1\nTX_zhiliao_0001\n  rotate: true\n  xy: 519, 2\n  size: 103, 137\n  orig: 113, 160\n  offset: 7, 13\n  index: -1\nTX_zhiliao_0003\n  rotate: true\n  xy: 658, 2\n  size: 107, 137\n  orig: 113, 160\n  offset: 3, 15\n  index: -1\nTX_zhiliao_0005\n  rotate: false\n  xy: 628, 262\n  size: 110, 144\n  orig: 113, 160\n  offset: 0, 9\n  index: -1\nTX_zhiliao_0007\n  rotate: false\n  xy: 409, 107\n  size: 109, 148\n  orig: 113, 160\n  offset: 0, 7\n  index: -1\nTX_zhiliao_0009\n  rotate: false\n  xy: 300, 257\n  size: 108, 149\n  orig: 113, 160\n  offset: 0, 6\n  index: -1\nTX_zhiliao_0011\n  rotate: false\n  xy: 300, 106\n  size: 107, 149\n  orig: 113, 160\n  offset: 0, 7\n  index: -1\nTX_zhiliao_0013\n  rotate: false\n  xy: 410, 258\n  size: 107, 148\n  orig: 113, 160\n  offset: 0, 8\n  index: -1\nTX_zhiliao_0015\n  rotate: false\n  xy: 519, 258\n  size: 107, 148\n  orig: 113, 160\n  offset: 0, 9\n  index: -1\nTX_zhiliao_0017\n  rotate: false\n  xy: 732, 122\n  size: 106, 138\n  orig: 113, 160\n  offset: 1, 11\n  index: -1\nTX_zhiliao_0019\n  rotate: false\n  xy: 740, 270\n  size: 104, 136\n  orig: 113, 160\n  offset: 6, 13\n  index: -1\nguangquan\n  rotate: false\n  xy: 1037, 6\n  size: 103, 104\n  orig: 120, 120\n  offset: 8, 7\n  index: -1\nguangquan2\n  rotate: true\n  xy: 908, 5\n  size: 128, 127\n  orig: 150, 150\n  offset: 10, 12\n  index: -1\nskill1_guangzhu_0001\n  rotate: false\n  xy: 520, 109\n  size: 64, 147\n  orig: 77, 152\n  offset: 6, 2\n  index: -1\nxingguang\n  rotate: false\n  xy: 1091, 150\n  size: 56, 52\n  orig: 70, 70\n  offset: 9, 8\n  index: -1\nxingguang0001\n  rotate: true\n  xy: 797, 2\n  size: 118, 109\n  orig: 150, 150\n  offset: 18, 23\n  index: -1\nxingguang0002\n  rotate: true\n  xy: 1074, 290\n  size: 116, 106\n  orig: 150, 150\n  offset: 19, 25\n  index: -1\nxingguang0003\n  rotate: true\n  xy: 953, 286\n  size: 120, 119\n  orig: 150, 150\n  offset: 14, 16\n  index: -1\nxingguang0004\n  rotate: true\n  xy: 586, 111\n  size: 145, 144\n  orig: 150, 150\n  offset: 1, 3\n  index: -1\nxingxing\n  rotate: false\n  xy: 1178, 190\n  size: 61, 57\n  orig: 80, 80\n  offset: 10, 13\n  index: -1\nxingxing3\n  rotate: false\n  xy: 1130, 117\n  size: 38, 31\n  orig: 50, 50\n  offset: 7, 8\n  index: -1\nxuli0001\n  rotate: false\n  xy: 846, 279\n  size: 105, 127\n  orig: 150, 150\n  offset: 11, 15\n  index: -1\nxuli0002\n  rotate: true\n  xy: 401, 4\n  size: 100, 116\n  orig: 150, 150\n  offset: 19, 18\n  index: -1\nxuli0003\n  rotate: false\n  xy: 1142, 11\n  size: 97, 104\n  orig: 150, 150\n  offset: 23, 25\n  index: -1\nxuli0004\n  rotate: false\n  xy: 840, 135\n  size: 132, 133\n  orig: 150, 150\n  offset: 12, 8\n  index: -1\nxuli0005\n  rotate: false\n  xy: 974, 167\n  size: 115, 117\n  orig: 150, 150\n  offset: 20, 16\n  index: -1\nxuli0006\n  rotate: false\n  xy: 300, 3\n  size: 99, 101\n  orig: 150, 150\n  offset: 27, 25\n  index: -1\nxuli0007\n  rotate: true\n  xy: 1091, 204\n  size: 84, 85\n  orig: 150, 150\n  offset: 35, 33\n  index: -1\nxuli0008\n  rotate: true\n  xy: 1182, 249\n  size: 68, 69\n  orig: 150, 150\n  offset: 43, 41\n  index: -1\nxuli0009\n  rotate: false\n  xy: 1037, 112\n  size: 52, 53\n  orig: 150, 150\n  offset: 50, 50\n  index: -1\nxuli0010\n  rotate: true\n  xy: 1091, 113\n  size: 35, 37\n  orig: 150, 150\n  offset: 58, 58\n  index: -1\n", ["jn_nantianmeihuixue_01.png"], {"skeleton": {"hash": "5m6eCfzn501zb4QZ5LN4F1H0rTs", "spine": "3.8.97", "x": -255.5, "y": -256, "width": 512, "height": 559.43, "images": "", "audio": ""}, "bones": [{"name": "root"}, {"name": "guangquan", "parent": "root", "length": 22.44, "rotation": 89.09, "x": -0.15, "y": 207.99}, {"name": "guangquan2", "parent": "root", "length": 21.38, "rotation": 89.09, "x": -0.45, "y": 208.59}, {"name": "xingugnag", "parent": "root", "length": 30.35, "rotation": 89.09, "x": 0.3, "y": 208.21}, {"name": "xingxing", "parent": "root", "length": 21.16, "rotation": 87.74, "x": -0.12, "y": 208.78}, {"name": "xuli", "parent": "root", "length": 12.09, "rotation": 90, "x": -0.46, "y": 208.18}, {"name": "xuli2", "parent": "root", "length": 12.09, "rotation": 90, "x": -0.46, "y": 208.18}, {"name": "shitou", "parent": "root", "y": -75.52}, {"name": "shangguang", "parent": "root", "y": 216.51}, {"name": "shanguang92", "parent": "root", "y": 214.7}, {"name": "huixue", "parent": "root", "y": 25.01, "scaleX": 2, "scaleY": 2}, {"name": "guangzhu", "parent": "root"}, {"name": "bone2", "parent": "root"}], "slots": [{"name": "3112", "bone": "bone2", "attachment": "3112"}, {"name": "guangquan", "bone": "guangquan", "attachment": "guangquan"}, {"name": "xuli", "bone": "xuli", "attachment": "xuli0010"}, {"name": "xuli2", "bone": "xuli2", "attachment": "xuli0010"}, {"name": "xing<PERSON><PERSON>", "bone": "xingugnag"}, {"name": "xingxing", "bone": "xingxing", "attachment": "xingxing"}, {"name": "guangquan2", "bone": "guangquan2", "attachment": "guangquan2"}, {"name": "311", "bone": "shitou", "attachment": "311"}, {"name": "xingxing3", "bone": "shangguang", "attachment": "xingxing3"}, {"name": "xingguang2", "bone": "shanguang92", "attachment": "xing<PERSON><PERSON>"}, {"name": "TX_zhiliao_0001", "bone": "huixue", "attachment": "TX_zhiliao_0019"}, {"name": "skill1_guangzhu_0001", "bone": "guangzhu", "attachment": "skill1_guangzhu_0001"}], "skins": [{"name": "default", "attachments": {"311": {"311": {"x": 0.5, "y": 290.55, "scaleX": 1.3, "scaleY": 1.3, "width": 129, "height": 136}}, "3112": {"3112": {"x": 0.5, "width": 512, "height": 512}}, "TX_zhiliao_0001": {"TX_zhiliao_0001": {"x": 0.5, "width": 113, "height": 160}, "TX_zhiliao_0003": {"x": 0.5, "width": 113, "height": 160}, "TX_zhiliao_0005": {"x": 0.5, "width": 113, "height": 160}, "TX_zhiliao_0007": {"x": 0.5, "width": 113, "height": 160}, "TX_zhiliao_0009": {"x": 0.5, "width": 113, "height": 160}, "TX_zhiliao_0011": {"x": 0.5, "width": 113, "height": 160}, "TX_zhiliao_0013": {"x": 0.5, "width": 113, "height": 160}, "TX_zhiliao_0015": {"x": 0.5, "width": 113, "height": 160}, "TX_zhiliao_0017": {"x": 0.5, "width": 113, "height": 160}, "TX_zhiliao_0019": {"x": 0.5, "width": 113, "height": 160}}, "guangquan": {"guangquan": {"x": 0.47, "y": -0.14, "rotation": -89.09, "width": 120, "height": 120}}, "guangquan2": {"guangquan2": {"x": -0.13, "y": -0.45, "rotation": -89.09, "width": 150, "height": 150}}, "skill1_guangzhu_0001": {"skill1_guangzhu_0001": {"x": 0.5, "width": 77, "height": 152}}, "xingguang": {"xingguang0001": {"x": 0.23, "y": 0.3, "rotation": -89.09, "width": 150, "height": 150}, "xingguang0002": {"x": 0.23, "y": 0.3, "rotation": -89.09, "width": 150, "height": 150}, "xingguang0003": {"x": 0.23, "y": 0.3, "rotation": -89.09, "width": 150, "height": 150}, "xingguang0004": {"x": 0.23, "y": 0.3, "rotation": -89.09, "width": 150, "height": 150}}, "xingguang2": {"xingguang": {"width": 70, "height": 70}}, "xingxing": {"xingxing": {"x": -0.32, "y": -0.13, "rotation": -87.74, "width": 80, "height": 80}}, "xingxing3": {"xingxing3": {"width": 50, "height": 50}}, "xuli": {"xuli0001": {"x": 0.27, "y": -0.46, "rotation": -90, "width": 150, "height": 150}, "xuli0002": {"x": 0.27, "y": -0.46, "rotation": -90, "width": 150, "height": 150}, "xuli0003": {"x": 0.27, "y": -0.46, "rotation": -90, "width": 150, "height": 150}, "xuli0004": {"x": 0.27, "y": -0.46, "rotation": -90, "width": 150, "height": 150}, "xuli0005": {"x": 0.27, "y": -0.46, "rotation": -90, "width": 150, "height": 150}, "xuli0006": {"x": 0.27, "y": -0.46, "rotation": -90, "width": 150, "height": 150}, "xuli0007": {"x": 0.27, "y": -0.46, "rotation": -90, "width": 150, "height": 150}, "xuli0008": {"x": 0.27, "y": -0.46, "rotation": -90, "width": 150, "height": 150}, "xuli0009": {"x": 0.27, "y": -0.46, "rotation": -90, "width": 150, "height": 150}, "xuli0010": {"x": 0.27, "y": -0.46, "rotation": -90, "width": 150, "height": 150}}, "xuli2": {"xuli0001": {"x": 0.27, "y": -0.46, "rotation": -90, "width": 150, "height": 150}, "xuli0002": {"x": 0.27, "y": -0.46, "rotation": -90, "width": 150, "height": 150}, "xuli0003": {"x": 0.27, "y": -0.46, "rotation": -90, "width": 150, "height": 150}, "xuli0004": {"x": 0.27, "y": -0.46, "rotation": -90, "width": 150, "height": 150}, "xuli0005": {"x": 0.27, "y": -0.46, "rotation": -90, "width": 150, "height": 150}, "xuli0006": {"x": 0.27, "y": -0.46, "rotation": -90, "width": 150, "height": 150}, "xuli0007": {"x": 0.27, "y": -0.46, "rotation": -90, "width": 150, "height": 150}, "xuli0008": {"x": 0.27, "y": -0.46, "rotation": -90, "width": 150, "height": 150}, "xuli0009": {"x": 0.27, "y": -0.46, "rotation": -90, "width": 150, "height": 150}, "xuli0010": {"x": 0.27, "y": -0.46, "rotation": -90, "width": 150, "height": 150}}}}], "events": {"event_hit": {}}, "animations": {"animation": {"slots": {"311": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.5, "color": "ffffff9b"}, {"time": 0.8333, "color": "ffffffff"}, {"time": 1.2, "color": "ffffff9b"}], "attachment": [{"name": null}, {"time": 0.1667, "name": "311"}]}, "3112": {"color": [{"color": "ffffff00"}, {"time": 0.1667, "color": "ffffffb8"}, {"time": 0.5, "color": "ffffff51"}, {"time": 0.8333, "color": "ffffff8e"}, {"time": 1.1667, "color": "ffffff00"}]}, "TX_zhiliao_0001": {"attachment": [{"name": null}, {"time": 0.1667, "name": "TX_zhiliao_0001"}, {"time": 0.2333, "name": "TX_zhiliao_0003"}, {"time": 0.3, "name": "TX_zhiliao_0005"}, {"time": 0.3667, "name": "TX_zhiliao_0007"}, {"time": 0.4333, "name": "TX_zhiliao_0009"}, {"time": 0.5, "name": "TX_zhiliao_0011"}, {"time": 0.5667, "name": "TX_zhiliao_0013"}, {"time": 0.6667, "name": "TX_zhiliao_0015"}, {"time": 0.7333, "name": "TX_zhiliao_0017"}, {"time": 0.8, "name": "TX_zhiliao_0019"}, {"time": 0.8667, "name": "TX_zhiliao_0001"}, {"time": 0.9333, "name": "TX_zhiliao_0003"}, {"time": 1, "name": "TX_zhiliao_0005"}, {"time": 1.0667, "name": "TX_zhiliao_0007"}, {"time": 1.1333, "name": "TX_zhiliao_0009"}, {"time": 1.2, "name": "TX_zhiliao_0011"}]}, "guangquan": {"color": [{"time": 0.2, "color": "ffffffff"}, {"time": 0.8333, "color": "ffffff00"}], "attachment": [{"name": null}, {"time": 0.1667, "name": "guangquan"}]}, "guangquan2": {"color": [{"time": 1.0333, "color": "ffffffff"}, {"time": 1.1667, "color": "ffffff00"}], "attachment": [{"name": null}, {"time": 0.9333, "name": "guangquan2"}]}, "skill1_guangzhu_0001": {"color": [{"time": 0.1667, "color": "ffffffa6"}, {"time": 0.5, "color": "ffffff55"}, {"time": 0.8333, "color": "ffffffb2"}, {"time": 1.1667, "color": "ffffff00"}, {"time": 1.2, "color": "ffffff4b"}]}, "xingguang": {"attachment": [{"time": 0.8667, "name": "xingguang0001"}, {"time": 0.9, "name": "xingguang0002"}, {"time": 0.9333, "name": "xingguang0003"}, {"time": 0.9667, "name": "xingguang0004"}, {"time": 1, "name": null}]}, "xingguang2": {"color": [{"color": "ffffff01"}, {"time": 0.1, "color": "ffffffff"}, {"time": 0.3, "color": "ffffff0c"}]}, "xingxing": {"color": [{"time": 1, "color": "ffffff00"}], "attachment": [{"name": null}, {"time": 0.1667, "name": "xingxing"}]}, "xingxing3": {"color": [{"color": "ffffffec"}, {"time": 0.2, "color": "ffffff0b"}]}, "xuli": {"attachment": [{"name": null}, {"time": 0.1667, "name": "xuli0010"}, {"time": 0.2, "name": "xuli0002"}, {"time": 0.2333, "name": "xuli0003"}, {"time": 0.2667, "name": "xuli0004"}, {"time": 0.3, "name": "xuli0005"}, {"time": 0.3333, "name": "xuli0006"}, {"time": 0.3667, "name": "xuli0007"}, {"time": 0.4, "name": "xuli0008"}, {"time": 0.4333, "name": "xuli0009"}, {"time": 0.4667, "name": "xuli0010"}, {"time": 0.5, "name": null}]}, "xuli2": {"attachment": [{"name": null}, {"time": 0.4, "name": "xuli0010"}, {"time": 0.4333, "name": "xuli0002"}, {"time": 0.4667, "name": "xuli0003"}, {"time": 0.5, "name": "xuli0004"}, {"time": 0.5333, "name": "xuli0005"}, {"time": 0.5667, "name": "xuli0006"}, {"time": 0.6, "name": "xuli0007"}, {"time": 0.6333, "name": "xuli0008"}, {"time": 0.6667, "name": "xuli0009"}, {"time": 0.7, "name": "xuli0010"}, {"time": 0.7333, "name": null}]}}, "bones": {"guangquan": {"translate": [{"time": 0.1667, "x": 0.99, "y": 49.08}], "scale": [{"time": 0.1667, "x": 2, "y": 2}, {"time": 0.8333, "x": 0.5, "y": 0.5}]}, "guangquan2": {"translate": [{"time": 0.1667, "y": 50.39}], "scale": [{"time": 0.1667, "x": 2, "y": 2}, {"time": 0.9333, "x": 0.5, "y": 0.5}, {"time": 1.0333, "x": 2, "y": 2}, {"time": 1.1667, "x": 3, "y": 3}]}, "xingugnag": {"translate": [{"time": 0.1667, "y": 35.14}], "scale": [{"time": 0.1667, "x": 2, "y": 2}]}, "xingxing": {"rotate": [{"time": 0.5667}, {"time": 0.9667, "angle": -130.44}], "translate": [{"time": 0.1667, "y": 41.72}, {"time": 0.5667, "y": 45.41}], "scale": [{"time": 0.1667, "x": 2, "y": 2, "curve": "stepped"}, {"time": 0.5667, "x": 2, "y": 2}, {"time": 0.9667, "x": 4, "y": 4}]}, "xuli": {"rotate": [{"time": 0.1667}, {"time": 0.8667, "angle": -130.44}], "translate": [{"time": 0.1667, "y": 43.75}], "scale": [{"time": 0.1667, "x": 2, "y": 2}]}, "xuli2": {"rotate": [{"time": 0.4}, {"time": 1.1, "angle": -130.44}], "translate": [{"time": 0.4, "y": 43.75}], "scale": [{"time": 0.4, "x": 2, "y": 2}]}, "shitou": {"translate": [{"y": 42.06}]}, "shangguang": {"rotate": [{}, {"time": 0.2, "angle": -117.44}], "translate": [{"y": 42.32}], "scale": [{}, {"time": 0.2, "x": 2, "y": 2}]}, "shanguang92": {"rotate": [{"time": 0.1, "angle": -101.71}, {"time": 0.3, "angle": 97.55}], "translate": [{"y": 42.76}], "scale": [{"time": 0.1, "x": 2, "y": 2}]}, "guangzhu": {"translate": [{"y": 113.26}, {"time": 0.1667, "y": 113.26}], "scale": [{"x": 0.179}, {"time": 0.1667, "x": 3.589, "y": 3.41, "curve": "stepped"}, {"time": 1.0333, "x": 3.589, "y": 3.41}, {"time": 1.1667, "x": 7.578, "y": 4.316}]}, "bone2": {"translate": [{"y": 119.45}, {"time": 0.1667, "x": 6.28, "y": 217.38}], "scale": [{"x": 0.066}, {"time": 0.1667, "x": 1.62, "y": 1.467, "curve": "stepped"}, {"time": 1, "x": 1.62, "y": 1.467}, {"time": 1.1667, "x": 2, "y": 2}]}}, "events": [{"time": 0.4667, "name": "event_hit"}, {"time": 0.6, "name": "event_hit"}, {"time": 0.7667, "name": "event_hit"}, {"time": 0.9, "name": "event_hit"}, {"time": 1.0667, "name": "event_hit"}, {"time": 1.2, "name": "event_hit"}]}}}, [0]]], 0, 0, [0], [-1], [2]], [[[5, "yun", "\nyun.png\nsize: 773,51\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nhuoqiu02\n  rotate: true\n  xy: 440, 5\n  size: 44, 66\n  orig: 60, 80\n  offset: 8, 2\n  index: -1\nhuoqiu03\n  rotate: true\n  xy: 576, 4\n  size: 45, 65\n  orig: 60, 80\n  offset: 7, 2\n  index: -1\nhuoqiu04\n  rotate: true\n  xy: 371, 6\n  size: 43, 67\n  orig: 60, 80\n  offset: 7, 2\n  index: -1\nhuoqiu05\n  rotate: true\n  xy: 508, 5\n  size: 44, 66\n  orig: 60, 80\n  offset: 7, 1\n  index: -1\nhuoqiu06\n  rotate: true\n  xy: 224, 6\n  size: 43, 70\n  orig: 60, 80\n  offset: 10, 1\n  index: -1\nhuoqiu07\n  rotate: true\n  xy: 296, 9\n  size: 40, 73\n  orig: 60, 80\n  offset: 12, 2\n  index: -1\nhuoqiu08\n  rotate: true\n  xy: 79, 2\n  size: 47, 73\n  orig: 60, 80\n  offset: 7, 3\n  index: -1\nhuoqiu09\n  rotate: true\n  xy: 2, 2\n  size: 47, 75\n  orig: 60, 80\n  offset: 7, 2\n  index: -1\nhuoqiu10\n  rotate: true\n  xy: 154, 2\n  size: 47, 68\n  orig: 60, 80\n  offset: 6, 2\n  index: -1\nhuoqiu11\n  rotate: true\n  xy: 709, 3\n  size: 46, 62\n  orig: 60, 80\n  offset: 6, 3\n  index: -1\nhuoqiu12\n  rotate: true\n  xy: 643, 3\n  size: 46, 64\n  orig: 60, 80\n  offset: 7, 2\n  index: -1\n", ["yun.png"], {"skeleton": {"hash": "isyUHwrxR4ymJhQ3WhysxXCqm+E", "spine": "3.8.97", "x": -30, "y": -40, "width": 60, "height": 80, "images": "", "audio": "C:/Users/<USER>/Desktop/tianbing_01"}, "bones": [{"name": "root"}, {"name": "huo01", "parent": "root"}], "slots": [{"name": "huoqiu02", "bone": "huo01", "attachment": "huoqiu12"}], "skins": [{"name": "default", "attachments": {"huoqiu02": {"huoqiu02": {"width": 60, "height": 80}, "huoqiu03": {"width": 60, "height": 80}, "huoqiu04": {"width": 60, "height": 80}, "huoqiu05": {"width": 60, "height": 80}, "huoqiu06": {"width": 60, "height": 80}, "huoqiu07": {"width": 60, "height": 80}, "huoqiu08": {"width": 60, "height": 80}, "huoqiu09": {"width": 60, "height": 80}, "huoqiu10": {"width": 60, "height": 80}, "huoqiu11": {"width": 60, "height": 80}, "huoqiu12": {"width": 60, "height": 80}}}}], "animations": {"animation": {"slots": {"huoqiu02": {"color": [{"color": "ffffffc6"}], "attachment": [{"name": "huoqiu02"}, {"time": 0.0667, "name": "huoqiu03"}, {"time": 0.1333, "name": "huoqiu04"}, {"time": 0.2, "name": "huoqiu05"}, {"time": 0.2667, "name": "huoqiu06"}, {"time": 0.3333, "name": "huoqiu07"}, {"time": 0.4, "name": "huoqiu08"}, {"time": 0.4667, "name": "huoqiu09"}, {"time": 0.5333, "name": "huoqiu10"}, {"time": 0.6, "name": "huoqiu11"}, {"time": 0.6667, "name": "huoqiu12"}]}}}}}, [0]]], 0, 0, [0], [-1], [3]], [[{"name": "tower_01", "rect": {"x": 0, "y": 0, "width": 1080, "height": 411}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 1080, "height": 411}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-540, -205.5, 0, 540, -205.5, 0, -540, 205.5, 0, 540, 205.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 411, 1080, 411, 0, 0, 1080, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -540, "y": -205.5, "z": 0}, "maxPos": {"x": 540, "y": 205.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [5], 0, [0], [3], [4]], [[[11, "UnitTower"], [12, "UnitTower", 33554432, [-12, -13, -14, -15, -16, -17, -18], [[2, -2, [0, "74dG17Fk1H/6kqXYr8ry9W"], [5, 1080, 376]], [18, -11, [0, "65oDEWuQRONacWqdC+q91m"], -10, -9, -8, -7, -6, -5, -4, -3]], [1, "41+NuunPBPn6mkD5bFXrKh", null, null, null, -1, 0]], [13, "ndPgs", 33554432, 1, [-21, -22, -23, -24], [[2, -19, [0, "0adDcTEbpH3JVVX1pELqFf"], [5, 456, 40]], [19, 0, -20, [0, "fdLWqIlOpNL4fvAm8pUBTb"], 7]], [1, "942+hcq3FLwK7wU0tbnT94", null, null, null, 1, 0], [1, 0, 516.8969999999999, 0]], [14, "ndFire", 33554432, 1, [-26, -27, -28, -29], [[16, -25, [0, "ff34+vOtJOZarwLo4+y0GW"]]], [1, "07vWER10lOwajlplYpBnqz", null, null, null, 1, 0]], [7, "Sprite", 33554432, 1, [[[2, -30, [0, "723OOV3zNNCaj4FcnNXBGU"], [5, 1080, 411]], [6, -31, [0, "cf0UpY/ZxCsIS4eBCfBRMl"], 0], -32], 4, 4, 1], [1, "de0lL0b6RNMYtcjclgZt+j", null, null, null, 1, 0]], [3, "game_cloud_00", 33554432, 1, [[2, -33, [0, "c8CQbrHqhLgKkRpQNSa/z9"], [5, 1080, 502]], [6, -34, [0, "caFbTj9L1Ni45L69jq+zYa"], 1]], [1, "1cY+sSgwxF7bVyKvu6P5gv", null, null, null, 1, 0], [1, 0, -170.39999999999998, 0]], [3, "Node", 33554432, 3, [[2, -35, [0, "bawh1JebdBz62cHhgby6gW"], [5, 60, 80]], [4, "default", "animation", false, 0, -36, [0, "55BeqGLORKJqbT7LyqUus2"], 2]], [1, "a5Id3Zu1tJaaIEL2z1hGbh", null, null, null, 1, 0], [1, -367.2, 23.067000000000007, 0]], [3, "Node-001", 33554432, 3, [[2, -37, [0, "06FrXEbINOhaAkucRbUcCU"], [5, 60, 80]], [4, "default", "animation", false, 0, -38, [0, "31yx6meDdIaaWkZcI6Mr8o"], 3]], [1, "7eM8HSpoVF3b+gLEJb0Tm8", null, null, null, 1, 0], [1, -193.099, 13.994000000000028, 0]], [3, "Node-002", 33554432, 3, [[2, -39, [0, "a6ljd3yw5AIpP/shI2FYOh"], [5, 60, 80]], [4, "default", "animation", false, 0, -40, [0, "64TKf8qjtEn6Iu6O7ldU9z"], 4]], [1, "08xXruqAdLWKaP0bjinLYr", null, null, null, 1, 0], [1, 200.596, 13.698999999999955, 0]], [3, "Node-003", 33554432, 3, [[2, -41, [0, "c6If7xCjhMAaT+CP9grP7H"], [5, 60, 80]], [4, "default", "animation", false, 0, -42, [0, "e8UQHWUiRNm4RMRO2hiUa7"], 5]], [1, "2fXGsht8pM9qBZcqZ2yl7U", null, null, null, 1, 0], [1, 356.591, 35.28399999999999, 0]], [7, "sprBar", 33554432, 2, [[[2, -43, [0, "741kemZqFGF4ENHNFwhMs1"], [5, 448, 32]], -44], 4, 1], [1, "b4xVC0nVxNF4F1HqNsTx3G", null, null, null, 1, 0]], [8, "lbHp", 33554432, 2, [[[2, -45, [0, "45KMokSehEV6T+IITr5yFt"], [5, 72.39999389648438, 108.8]], -46], 4, 1], [1, "4f99A7AEBJNIqQHpOut0BS", null, null, null, 1, 0], [1, 0, 0.7479999999998199, 0], [1, 0.5, 0.5, 1]], [3, "game_tower_levbg", 33554432, 2, [[2, -47, [0, "54UENbPWBDDpil3Q7kLZlU"], [5, 184, 38]], [6, -48, [0, "49w/volERPNpjZVt4RWcCt"], 6]], [1, "9bgYYCLe5DNqgo3zf2tOTK", null, null, null, 1, 0], [1, 0, -38.15300000000002, 0]], [8, "lbLev", 33554432, 2, [[[2, -49, [0, "4eyMnLCK1H85hKXBaAl8xh"], [5, 216.83999633789062, 100.8]], -50], 4, 1], [1, "feVwH8GzpMwJo3aJzw2u4Y", null, null, null, 1, 0], [1, 0, -35.75999999999999, 0], [1, 0.5, 0.5, 1]], [9, "iconInvincible", 33554432, 1, [[[2, -51, [0, "9a6r0uaLtCnp3yVdM8dYaS"], [5, 256, 256]], -52], 4, 1], [1, "e2NUTyj31JlKR1ZJTqUCqT", null, null, null, 1, 0], [1, 0, -45.311, 0]], [9, "lbInvincibleTime", 33554432, 1, [[[2, -53, [0, "b1MfxaAeNEqq/38vtrQxbc"], [5, 0, 50.4]], -54], 4, 1], [1, "4bxnZMW55ABrNQe3P2FbiR", null, null, null, 1, 0], [1, 0, -42.636, 0]], [15, "saveHp", false, 33554432, 1, [[[17, -55, [0, "0cs4APsCVIer89zLHk0z+K"], [5, 512, 559.4299926757812], [0, 0.4990234375, 0.45760864335417445]], -56], 4, 1], [1, "07C82mqVVFn7XtIgkRC6II", null, null, null, 1, 0], [1, 0, 93.52600000000007, 0]], [21, 4, [0, "457NQivKlI14jrSdFVN7uX"]], [20, 3, 0, 1, 10, [0, "c6jTnoRiVEg7aHW84R41x5"]], [22, "--", 70, 70, 80, false, true, 4, 11, [0, "ffcRLUl8dHdKrXVsGH9dIy"], [4, 4293519871]], [23, "南天门等级1", 40, 80, false, 4, 13, [0, "1eM87GjMVC2px6De08NMaI"]], [10, "default", "01", 0, 14, [0, "dcGLdzSBhEkqrOSXmcvmfu"]], [24, "", 20, 20, 15, [0, "579QS+heJPAI0usSZzTrMe"], [4, 4278190080]], [10, "default", "animation", 0, 16, [0, "d0PH7C8ZpFVLq2nf3Yty/G"]]], 0, [0, 5, 1, 0, 0, 1, 0, 6, 23, 0, 7, 21, 0, 8, 22, 0, 9, 18, 0, 10, 2, 0, 11, 20, 0, 12, 19, 0, 13, 17, 0, 0, 1, 0, -1, 4, 0, -2, 5, 0, -3, 3, 0, -4, 2, 0, -5, 14, 0, -6, 15, 0, -7, 16, 0, 0, 2, 0, 0, 2, 0, -1, 10, 0, -2, 11, 0, -3, 12, 0, -4, 13, 0, 0, 3, 0, -1, 6, 0, -2, 7, 0, -3, 8, 0, -4, 9, 0, 0, 4, 0, 0, 4, 0, -3, 17, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, -2, 18, 0, 0, 11, 0, -2, 19, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, -2, 20, 0, 0, 14, 0, -2, 21, 0, 0, 15, 0, -2, 22, 0, 0, 16, 0, -2, 23, 0, 14, 1, 56], [0, 0, 0, 0, 0, 0, 0, 0, 18, 19, 20, 21, 23], [2, 2, 1, 1, 1, 1, 2, 2, 2, 4, 4, 1, 1], [5, 6, 0, 0, 0, 0, 7, 8, 9, 1, 1, 10, 11]], [[{"name": "game_cloud_00", "rect": {"x": 0, "y": 0, "width": 1080, "height": 502}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 1080, "height": 502}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-540, -251, 0, 540, -251, 0, -540, 251, 0, 540, 251, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 502, 1080, 502, 0, 0, 1080, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -540, "y": -251, "z": 0}, "maxPos": {"x": 540, "y": 251, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [5], 0, [0], [3], [12]], [[[5, "fengying", "\nfengying.png\nsize: 723,1048\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\n1\n  rotate: false\n  xy: 585, 136\n  size: 66, 67\n  orig: 512, 512\n  offset: 221, 356\n  index: -1\n10\n  rotate: false\n  xy: 473, 91\n  size: 110, 112\n  orig: 512, 512\n  offset: 203, 197\n  index: -1\n11\n  rotate: true\n  xy: 263, 2\n  size: 201, 208\n  orig: 512, 512\n  offset: 156, 151\n  index: -1\n12\n  rotate: false\n  xy: 407, 546\n  size: 236, 236\n  orig: 512, 512\n  offset: 138, 138\n  index: -1\n13\n  rotate: false\n  xy: 407, 784\n  size: 262, 262\n  orig: 512, 512\n  offset: 125, 125\n  index: -1\n14\n  rotate: false\n  xy: 375, 205\n  size: 341, 339\n  orig: 512, 512\n  offset: 86, 87\n  index: -1\n15\n  rotate: false\n  xy: 2, 9\n  size: 259, 259\n  orig: 512, 512\n  offset: 127, 127\n  index: -1\n16\n  rotate: false\n  xy: 2, 270\n  size: 371, 371\n  orig: 512, 512\n  offset: 71, 70\n  index: -1\n17\n  rotate: false\n  xy: 2, 270\n  size: 371, 371\n  orig: 512, 512\n  offset: 71, 70\n  index: -1\n2\n  rotate: true\n  xy: 653, 145\n  size: 58, 68\n  orig: 512, 512\n  offset: 132, 316\n  index: -1\n3\n  rotate: true\n  xy: 653, 87\n  size: 56, 68\n  orig: 512, 512\n  offset: 92, 221\n  index: -1\n4\n  rotate: false\n  xy: 585, 68\n  size: 66, 66\n  orig: 512, 512\n  offset: 128, 123\n  index: -1\n5\n  rotate: true\n  xy: 263, 205\n  size: 63, 67\n  orig: 512, 512\n  offset: 224, 87\n  index: -1\n6\n  rotate: false\n  xy: 645, 715\n  size: 58, 67\n  orig: 512, 512\n  offset: 319, 124\n  index: -1\n7\n  rotate: true\n  xy: 645, 648\n  size: 65, 64\n  orig: 512, 512\n  offset: 357, 224\n  index: -1\n8\n  rotate: false\n  xy: 473, 18\n  size: 67, 71\n  orig: 512, 512\n  offset: 314, 313\n  index: -1\n9\n  rotate: false\n  xy: 2, 643\n  size: 403, 403\n  orig: 512, 512\n  offset: 55, 54\n  index: -1\n", ["fengying.png"], {"skeleton": {"hash": "nlk9a4HK5WaE3/477AgEg60MOLc", "spine": "3.8.97", "x": -128, "y": -128, "width": 256, "height": 256, "images": "", "audio": "C:/Users/<USER>/Desktop/tianbing_01"}, "bones": [{"name": "root", "scaleX": 0.5, "scaleY": 0.5}, {"name": "bone", "parent": "root"}, {"name": "bone2", "parent": "root"}, {"name": "bone3", "parent": "root"}, {"name": "bone4", "parent": "root"}, {"name": "bone5", "parent": "root"}, {"name": "bone6", "parent": "root"}], "slots": [{"name": "bone4", "bone": "bone4", "attachment": "13"}, {"name": "bone5", "bone": "bone4", "attachment": "14"}, {"name": "15", "bone": "bone5", "attachment": "15"}, {"name": "bone6", "bone": "bone5", "attachment": "16"}, {"name": "17", "bone": "bone6", "attachment": "17"}, {"name": "12", "bone": "bone4", "attachment": "12"}, {"name": "bone7", "bone": "bone6", "attachment": "9"}, {"name": "11", "bone": "bone3", "attachment": "11"}, {"name": "10", "bone": "bone2", "attachment": "10"}, {"name": "1", "bone": "bone", "attachment": "1"}], "skins": [{"name": "default", "attachments": {"1": {"1": {"width": 512, "height": 512}, "2": {"width": 512, "height": 512}, "3": {"width": 512, "height": 512}, "4": {"width": 512, "height": 512}, "5": {"width": 512, "height": 512}, "6": {"width": 512, "height": 512}, "7": {"width": 512, "height": 512}, "8": {"width": 512, "height": 512}}, "10": {"10": {"width": 512, "height": 512}}, "11": {"11": {"width": 512, "height": 512}}, "12": {"12": {"width": 512, "height": 512}}, "15": {"15": {"width": 512, "height": 512}}, "17": {"17": {"width": 512, "height": 512}}, "bone4": {"13": {"width": 512, "height": 512}}, "bone5": {"14": {"width": 512, "height": 512}}, "bone6": {"16": {"width": 512, "height": 512}}, "bone7": {"9": {"width": 512, "height": 512}}}}], "animations": {"01": {"slots": {"1": {"attachment": [{"time": 0.3667, "name": "2"}, {"time": 0.7333, "name": "3"}, {"time": 1, "name": "4"}, {"time": 1.7667, "name": "5"}, {"time": 2.2333, "name": "6"}, {"time": 2.5667, "name": "7"}, {"time": 3, "name": "8"}]}, "10": {"color": [{"color": "ffffffff"}, {"time": 1.4333, "color": "ffffff22"}, {"time": 3, "color": "ffffffff"}]}, "11": {"color": [{"color": "ffffffff"}, {"time": 1.4333, "color": "ffffffbe"}, {"time": 3, "color": "ffffffff"}]}, "17": {"color": [{"color": "ffffffac"}, {"time": 1.4333, "color": "ffffff00"}, {"time": 3, "color": "ffffffac"}]}, "bone6": {"color": [{"color": "ffffff86"}, {"time": 1.4333, "color": "ffffff0d"}, {"time": 3, "color": "ffffff96"}]}}, "bones": {"bone3": {"scale": [{"x": 1.4, "y": 1.4}, {"time": 1.4333, "x": 1.1, "y": 1.1}, {"time": 3, "x": 1.4, "y": 1.4}]}, "bone2": {"scale": [{"x": 1.5, "y": 1.5}, {"time": 1.4333, "x": 1.1, "y": 1.1}, {"time": 3, "x": 1.5, "y": 1.5}]}, "bone4": {"rotate": [{}, {"time": 1.4333, "angle": 180}, {"time": 3}]}}}}}, [0]]], 0, 0, [0], [-1], [13]]]]