import { _decorator, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Component, director, error, instantiate, JsonAsset, Label, log, math, Node, Prefab, Sprite, tween, UITransform, v3, view, warn } from 'cc';
import App from './scripts/App';
import { xcore } from './scripts/libs/xcore';
import { C_Bundle, C_Scene, C_View, E_Channel } from './scripts/ConstGlobal';
import TimeUtil from './scripts/libs/utils/TimeUtil';
import Tool from './scripts/libs/utils/Tool';
import Net from './scripts/Net';
import { DEBUG } from 'cc/env';
import { ConfigHelper } from './scripts/config/ConfigHelper';




const { ccclass, property } = _decorator;

@ccclass('Ready')
export class Ready extends Component {

    @property(Button)
    private btnLogin: Button = null;


    async onLoad() {

        this.btnLogin.interactable = false;
        await App.getInstance().init();
        this.btnLogin.interactable = true;
        this.btnLogin.node.on('click', this.doLogin, this);

    }

    async doLogin() {

        //xcore.ui.addView(C_View.ViewLogin)
        try {
            this.btnLogin.interactable = false;
            this.btnLogin.getComponent(Sprite).grayscale = true;
            await this.loadRes();
            await this.login();
            await this.updateGiftTopInfo();
            App.getInstance().initOperation();
            xcore.ui.switchScene(C_Bundle.abMain, C_Scene.Main);
        } catch (err) {
            this.btnLogin.interactable = true;
            this.btnLogin.getComponent(Sprite).grayscale = false;
            xcore.ui.showToast('登录失败 err:' + err);
        }

    }




    async loadRes() {

        let configUrl = (xcore.channel == E_Channel.TIKTOK /* || DEBUG */) ? `https://gz-cdn-1258783731.cos.ap-guangzhou.myqcloud.com/2024/2nantianmen/game_configs/prod/_total.json?t=${new Date().getTime()}` : `https://gz-cdn-1258783731.cos.ap-guangzhou.myqcloud.com/2024/2nantianmen/game_configs/dev/_total.json?t=${new Date().getTime()}`;
        if (!xcore.config) {
            xcore.config = await xcore.res.remoteLoadJson(configUrl);
        }
        log("gameConfig:", xcore.config);
    }

    //登录
    async login() {

        let query = DEBUG ? xcore.gameData.query : Tool.getCommandLineArgs();
        await Net.getCombatId()
        await Net.getServerTimeStamp();
        console.log("query", query, query.token);
        let parmas = {

        } as any
        let channelId = ConfigHelper.getInstance().getConstantConfigByKey('channelId');
        let appId = ConfigHelper.getInstance().getConstantConfigByKey('douyinAppID');
        if (xcore.channel == E_Channel.TIKTOK) {
            xcore.gameData.token = query.token;
            xcore.gameData.appId = appId || 'ttdb73adce4447fe4a10';
            xcore.gameData.channelId = channelId || '1006';
        }
        //本地测试
        else {
            // xcore.gameData.appId = '65e700e54ad6a'
            // xcore.gameData.channelId = '1008'
            // xcore.channel = E_Channel.GAME560;
            // xcore.gameData.userName = 'akayip'
            // xcore.gameData.password = '841892819'
            xcore.gameData.appId = 'merchant-d8748165-38f1-4381-b524-bd84a8e34ff0'
            xcore.gameData.channelId = '1006'
            xcore.channel = E_Channel.UHO;
            xcore.gameData.userName = 'grndpagaming';
            xcore.gameData.password = '123456';
        }



        await Net.login(xcore.channel, xcore.gameData.channelId, xcore.gameData.token, xcore.gameData.userName, xcore.gameData.password, xcore.gameData.appId, xcore.gameData.combatId);
        await Net.getLiverInfo();
        let key = ConfigHelper.getInstance().getLiverKeyByLev();
        await Net.updateAnchorRankGameLev(key, xcore.gameData.baseInfo.id);
        if (xcore.channel == E_Channel.TIKTOK) {
            await Net.updateTokenToIo();
        }


        console.log('liveinfo', xcore.gameData.baseInfo, xcore.gameData.gameLev);
        return new Promise((resolve, reject) => {
            this.scheduleOnce(() => {
                resolve(true)
            }, 0.5)
        })
    }


    async updateGiftTopInfo() {
        if (xcore.channel == E_Channel.TIKTOK) {
            let gifts = [];
            let giftConigs = ConfigHelper.getInstance().getGiftConfigs();
            for (let i = 0; i < giftConigs.length; i++) {
                let douyinGiftId = giftConigs[i].douyinGiftId
                if (douyinGiftId) {
                    gifts.push(douyinGiftId)
                }
            }
            await Net.updateGiftTopInfo(xcore.gameData.combatId, gifts)
        }

    }

}


