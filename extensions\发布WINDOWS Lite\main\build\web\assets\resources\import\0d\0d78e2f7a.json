[1, ["61MvPmsfVNyouaLOWc9nMQ@f9941", "52fHu7D8hGm5vLaoALoXCl", "3bgd84wDlBK7V9CJoyfLSz@6c48a", "61MvPmsfVNyouaLOWc9nMQ@6c48a", "3bgd84wDlBK7V9CJoyfLSz@f9941", "c4QFjyHkxBQoGsOs+ecoqi@f9941", "c4QFjyHkxBQoGsOs+ecoqi@6c48a"], ["node", "_spriteFrame", "_textureSource", "_font", "root", "btnLogin", "edTxt", "btnClose", "sprFrame", "ndRoot", "data", "_backgroundImage"], [["cc.Node", ["_name", "_layer", "_active", "_components", "_prefab", "_parent", "_children", "_lpos"], 0, 9, 4, 1, 2, 5], ["cc.Node", ["_name", "_layer", "_active", "_parent", "_components", "_prefab", "_lpos", "_children"], 0, 1, 12, 4, 5, 2], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_isSystemFontUsed", "_horizontalAlign", "_overflow", "_enableWrapText", "_enableOutline", "node", "__prefab", "_color", "_outlineColor", "_font"], -5, 1, 4, 5, 5, 6], "cc.SpriteFrame", ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.Sprite", ["_type", "_sizeMode", "node", "__prefab", "_spriteFrame"], 1, 1, 4, 6], ["cc.TTFFont", ["_name", "_native"], 1], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["da5ebcAY+pPHoAKTa2kUGpr", ["animStyle", "node", "__prefab", "ndRoot", "sprFrame", "btnClose", "edTxt", "btnLogin"], 2, 1, 4, 1, 1, 1, 1, 1], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.<PERSON><PERSON>", ["_transition", "_zoomScale", "node", "__prefab"], 1, 1, 4], ["cc.EditBox", ["_string", "_inputMode", "node", "__prefab", "_textLabel", "_placeholder<PERSON><PERSON><PERSON>"], 1, 1, 4, 1, 1]], [[8, 0, 2], [10, 0, 1, 2, 3, 4, 5, 5], [4, 0, 1, 2, 1], [0, 0, 1, 5, 3, 4, 7, 3], [1, 0, 1, 3, 7, 4, 5, 6, 3], [1, 0, 1, 3, 4, 5, 6, 3], [4, 0, 1, 1], [4, 0, 1, 2, 3, 1], [5, 2, 3, 4, 1], [5, 2, 3, 1], [11, 0, 1, 2, 3, 3], [6, 0, 1, 3], [7, 0, 2], [0, 0, 1, 6, 3, 4, 3], [0, 0, 1, 5, 6, 3, 4, 3], [0, 0, 2, 1, 5, 3, 4, 7, 4], [1, 0, 1, 3, 4, 5, 3], [1, 0, 2, 1, 3, 4, 5, 6, 4], [9, 0, 1, 2, 3, 4, 5, 6, 7, 2], [5, 0, 1, 2, 3, 4, 3], [2, 0, 1, 3, 7, 8, 9, 10, 11, 12, 5], [2, 0, 1, 2, 3, 8, 9, 10, 11, 12, 5], [2, 0, 4, 1, 2, 5, 6, 8, 9, 7], [2, 0, 4, 1, 2, 5, 6, 8, 9, 10, 7], [12, 0, 1, 2, 3, 4, 5, 3]], [[[{"name": "common_btn_gray", "rect": {"x": 0, "y": 1, "width": 226, "height": 73}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 226, "height": 75}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-113, -36.5, 0, 113, -36.5, 0, -113, 36.5, 0, 113, 36.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 74, 226, 74, 0, 1, 226, 1], "nuv": [0, 0.013333333333333334, 1, 0.013333333333333334, 0, 0.9866666666666667, 1, 0.9866666666666667], "minPos": {"x": -113, "y": -36.5, "z": 0}, "maxPos": {"x": 113, "y": 36.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [3], 0, [0], [2], [2]], [[[11, "Alimama_DongFangDaKai_Regular", "Alimama_DongFangDaKai_Regular.ttf"], -1], 0, 0, [], [], []], [[{"name": "common_unitframe_01", "rect": {"x": 1, "y": 0, "width": 400, "height": 43}, "offset": {"x": 0.5, "y": 0}, "originalSize": {"width": 401, "height": 43}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-200, -21.5, 0, 200, -21.5, 0, -200, 21.5, 0, 200, 21.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [1, 43, 401, 43, 1, 0, 401, 0], "nuv": [0.0024937655860349127, 0, 1, 0, 0.0024937655860349127, 1, 1, 1], "minPos": {"x": -200, "y": -21.5, "z": 0}, "maxPos": {"x": 200, "y": 21.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [3], 0, [0], [2], [3]], [[[12, "ViewLogin"], [13, "ViewLogin", 33554432, [-9], [[6, -2, [0, "f7CTKnahVHgYE37TzfQfv0"]], [18, 0, -8, [0, "00HhDUIrNDWakSk4CWhQQy"], -7, -6, -5, -4, -3]], [1, "a9Nwn9KLNIRJe6Ipng/3ld", null, null, null, -1, 0]], [14, "ndRoot", 33554432, 1, [-11, -12, -13, -14, -15, -16], [[6, -10, [0, "01xw9SYuNBXailP14w/FML"]]], [1, "7dicTcjQhOHYCHMd5M9BFf", null, null, null, 1, 0]], [4, "EditBox", 33554432, 2, [-20, -21], [[[2, -17, [0, "43PMVnzGhJNbPitB4y311B"], [5, 400, 40]], [19, 1, 0, -18, [0, "95He9zsppD+7tSP5ZbzsPP"], 4], -19], 4, 4, 1], [1, "3eTStEZvFMH4k+eLykqSJ0", null, null, null, 1, 0], [1, 0, 27.524, 0]], [4, "btnLogin", 33554432, 2, [-25], [[[2, -22, [0, "3fA2iQfcxKbbGI+hRMgzmc"], [5, 226, 73]], [8, -23, [0, "58ws1IndBGPqQ5HXK4P/yO"], 3], -24], 4, 4, 1], [1, "f6r8P7PLpAv4DqUxw1eRSE", null, null, null, 1, 0], [1, 0, -161.34000000000003, 0]], [5, "btnClose", 33554432, 2, [[[2, -26, [0, "1e49f8LftHep/V9ihuBGIh"], [5, 226, 73]], [9, -27, [0, "3cFOnPg9lGNKKfWsKTORe+"]], -28], 4, 4, 1], [1, "9fkfIs5qlE2ppZFaTJLghw", null, null, null, 1, 0], [1, 236.28, 318.721, 0]], [16, "sprFrame", 33554432, 2, [[[2, -29, [0, "89xzTMJ6NKPZIEYJXQPihN"], [5, 706, 512]], -30], 4, 1], [1, "c9kHvlpVpEiKYzED9N4pey", null, null, null, 1, 0]], [15, "sprSubframe", false, 33554432, 2, [[2, -31, [0, "c2tAvVDl5L3IiEoQ0+qT+n"], [5, 400, 43]], [8, -32, [0, "d9Rv2mV0hM95MuQQs6lN7p"], 0]], [1, "53rOXvg0RA2aOfbtvwgo9A", null, null, null, 1, 0], [1, 0, 26.889999999999986, 0]], [3, "lbTitle", 33554432, 2, [[2, -33, [0, "b6CkE7Rd5PW5cL6ZAXl/UF"], [5, 204, 54.4]], [20, "匹配码登录", 40, false, true, -34, [0, "4ey+E3h8ZNfJoblLglsCSA"], [4, 4293064703], [4, 4280050606], 1]], [1, "6dMusw8N9IvbgrC2y/eCyh", null, null, null, 1, 0], [1, 0, 221.72399999999993, 0]], [3, "txt", 33554432, 4, [[2, -35, [0, "5c6kH2BwNBb7S9T1rw+Nyj"], [5, 68, 50.4]], [21, "登录", 34, 34, false, -36, [0, "55pEdHaI1EjYxCJ56nMquO"], [4, 4294180095], [4, 4280050606], 2]], [1, "04qf0h89lLea4FpyY+NFpS", null, null, null, 1, 0], [1, 0, 3.6800000000000637, 0]], [5, "TEXT_LABEL", 33554432, 3, [[[7, -37, [0, "12g9Igi/ROa6TS0iH7mEWm"], [5, 398, 40], [0, 0, 1]], -38], 4, 1], [1, "6bO1Eng05HBL5n3Gqj/VUg", null, null, null, 1, 0], [1, -198, 20, 0]], [17, "PLACEHOLDER_LABEL", false, 33554432, 3, [[[7, -39, [0, "63WvJ6repEy7LquMSILj/k"], [5, 398, 40], [0, 0, 1]], -40], 4, 1], [1, "4b0yfAHaVHHIuFgI2K9+dZ", null, null, null, 1, 0], [1, -198, 20, 0]], [9, 6, [0, "bcz3r8EldGMoy10KRd4+WE"]], [10, 3, 1.1, 5, [0, "18zaSroYFJu5TpFaYpznMz"]], [10, 3, 1.1, 4, [0, "e0TM5FqddIn5bOzHlp6Bnl"]], [22, "123456", 0, 30, 30, 1, false, 10, [0, "0caARZwZhKb6V9dg32pC93"]], [23, "匹配码...", 0, 30, 30, 1, false, 11, [0, "e1KNLBFpNFBp0ROWsZaR6C"], [4, 4293519849]], [24, "123456", 6, 3, [0, "fbXCObgXtLeIihIQEALC9w"], 15, 16]], 0, [0, 4, 1, 0, 0, 1, 0, 5, 14, 0, 6, 17, 0, 7, 13, 0, 8, 12, 0, 9, 2, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, -1, 6, 0, -2, 7, 0, -3, 8, 0, -4, 5, 0, -5, 4, 0, -6, 3, 0, 0, 3, 0, 0, 3, 0, -3, 17, 0, -1, 10, 0, -2, 11, 0, 0, 4, 0, 0, 4, 0, -3, 14, 0, -1, 9, 0, 0, 5, 0, 0, 5, 0, -3, 13, 0, 0, 6, 0, -2, 12, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, -2, 15, 0, 0, 11, 0, -2, 16, 0, 10, 1, 40], [0, 0, 0, 0, 0, 12, 17], [1, 3, 3, 1, 1, 1, 11], [0, 1, 1, 4, 0, 5, 0]], [[{"name": "common_frame_03", "rect": {"x": 0, "y": 0, "width": 706, "height": 512}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 706, "height": 512}, "rotated": false, "capInsets": [0, 94, 0, 50], "vertices": {"rawPosition": [-353, -256, 0, 353, -256, 0, -353, 256, 0, 353, 256, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 512, 706, 512, 0, 0, 706, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -353, "y": -256, "z": 0}, "maxPos": {"x": 353, "y": 256, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [3], 0, [0], [2], [6]]]]