import { RoomGameAwardPush } from '../../../protos/room'
import BroadcastCtrl from '../BroadcastCtrl'
import { MessageCmdEvent, MessageRoomSubCmd } from '../MessageEvent'
import { _decorator } from 'cc'
const { ccclass } = _decorator

/**
 * 游戏结算
 */
@ccclass()
export default class ResGameResult extends BroadcastCtrl {
	////////////////////消息单例////////////////////
	public static get inst() {
		return this.getInst(ResGameResult)
	}

	constructor() {
		super()
		this.cmd = MessageCmdEvent.roomCmd
		this.subCmd = MessageRoomSubCmd.rewardPush
		this.addListen()
	}

	receive(code: number, data: any) {
		super.receive(code, RoomGameAwardPush.decode(data))
	}
}
