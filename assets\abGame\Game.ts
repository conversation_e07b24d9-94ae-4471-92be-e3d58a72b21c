import {
	_decorator,
	Button,
	Color,
	Component,
	EventKeyboard,
	EventTouch,
	Graphics,
	Input,
	input,
	instantiate,
	KeyCode,
	Label,
	log,
	Node,

	Prefab,

	size,
	Sprite,
	Tween,
	tween,
	TweenSystem,
	UITransform,
	v3,
	Vec2,
	Vec3,
	view
} from 'cc'
import {
	C_Bundle,
	C_GiftKey,
	C_Scene,
	C_View,
	E_Channel,
	E_EVENT,
	E_GameState,
	E_GiftMessageType
} from '../scripts/ConstGlobal'
import { xcore } from '../scripts/libs/xcore'

import { EffectMgr } from './scripts/EffectMgr'
import { FightMgr } from './scripts/FightMgr'
import { GiftMgr } from './scripts/GiftMgr'

import { GiftMessageMgr } from './scripts/GiftMessageMgr'

import Tool from '../scripts/libs/utils/Tool'



import Net from '../scripts/Net'
import GameSocketCtrl, { GameSocketEventEnum } from '../scripts/components/GameSocketCtrl'
import { DEBUG } from 'cc/env'
import { ConfigHelper } from '../scripts/config/ConfigHelper'
import TimeUtil from '../scripts/libs/utils/TimeUtil'
import { Collection } from '../scripts/libs/utils/Collection'



const { ccclass, property } = _decorator

/**
 * 游戏场景
 */
@ccclass('Game')
export class Game extends Component {

	@property(Node)
	private ndUITop: Node = null

	@property(Node)
	private ndUIContent: Node = null

	@property(Node)
	private ndUISelectMode: Node = null;

	@property(Node)
	private ndUISelectGameType: Node = null;

	@property(Node)
	private ndContentTower: Node = null


	@property(Node)
	private ndCam: Node = null;

	@property(Node)
	private ndContentHero: Node = null

	@property(Node)
	private ndContentMonster: Node = null

	@property(Node)
	private ndContentGiftTips: Node = null

	@property(Node)
	private ndContentBossOrSkillTips: Node = null

	@property(Node)
	private ndContentSkill: Node = null

	@property(Node)
	private ndContentHeroEffect: Node = null

	@property(Node)
	private ndContentMonsterEffect: Node = null

	@property(Node)
	private ndContentHurtEffect: Node = null

	@property(Node)
	private ndContentHurtTips: Node = null

	@property(Button)
	private btnSetting: Button = null;

	@property(Button)
	private btnPlay: Button = null

	@property(Button)
	private btnPlay2: Button = null

	@property(Button)
	private btnMode: Button = null

	@property(Button)
	private btnMode2: Button = null

	@property(Button)
	private btnSelectLev: Button = null

	@property(Button)
	private btnSkin: Button = null;

	@property(Button)
	private btnBack: Button = null

	@property(Button)
	private btnBack2: Button = null



	@property(Button)
	private btnRank: Button = null

	@property(Button)
	private btnPowerRank: Button = null

	@property(Button)
	private btnAutoGameLevel: Button = null;

	@property(Button)
	private btnOnlineMode: Button = null;

	@property(Button)
	private btnTask: Button = null;

	@property(Button)
	private btnGameSpeed: Button = null;
	@property(Label)
	private lbGameSpeed: Label = null;

	@property(Node)
	private ndGameSpeedLeft: Node = null;

	@property(Node)
	private ndGameSpeedRight: Node = null;

	@property(Sprite)
	private sprTagAuto: Sprite = null;

	@property(Sprite)
	private sprTagOnlineMode: Sprite = null;

	@property(Button)
	private btnTest: Button = null



	@property(Label)
	private lbLev: Label = null;

	@property(Label)
	private lbMode: Label = null;

	@property(Label)
	private lbBossMode: Label = null;

	@property(Label)
	private lbTime: Label = null;


	@property(Label)
	private lbJoinNum: Label = null;

	@property(Node)
	private ndSpaceMonster: Node = null

	@property(Node)
	private ndSpaceHero: Node = null

	@property(Label)
	private lbGameTime: Label = null

	@property(Label)
	private lbDesc: Label = null



	//底部信息


	@property(Label)
	private lbRound: Label = null;

	@property(Label)
	private lbBossDesc: Label = null;

	@property(Sprite)
	private sprBoss: Sprite = null;

	@property(Label)
	private lbMonsterDesc: Label = null;

	@property(Sprite)
	private sprMonster: Sprite = null;

	@property(Label)
	private lbScore: Label = null;

	@property(Button)
	private btnMoveLeft: Button = null;

	@property(Button)
	private btnMoveRight: Button = null;

	@property(Button)
	private btnExchange: Button = null;

	@property(Node)
	private ndRoundDetail: Node = null;

	@property(Node)
	private ndMonsterDetail: Node = null;

	@property(Node)
	private ndBossDetail: Node = null;

	@property(Node)
	private ndMonsterSub: Node = null;

	@property(Node)
	private ndBossSub: Node = null;

	@property(Label)
	private lbMonsterSub: Label = null;

	@property(Label)
	private lbBossSub: Label = null;

	@property(Button)
	private btnGameNextRoundStart: Button = null;

	@property(Button)
	private btnCrossReward: Button = null;

	@property(Node)
	private ndRoundStart: Node = null;

	@property(Label)
	private lbGameNextRoundStart: Label = null;


	//测试节点
	@property(Graphics)
	private gp: Graphics = null;

	private gpT: number = 0.5;



	/**礼物弹幕交互管理器 */
	private _giftMgr: GiftMgr
	/**战斗管理器 */
	private _fightMgr: FightMgr
	/**技能特效管理器 */
	private _effectMgr: EffectMgr

	private _giftMessageMgr: GiftMessageMgr

	private _gameoverTime: number = 10000000000000;
	private _towerHp: number

	private _isConnect: boolean = false;
	private _shankTw: Tween
	private _camTempPos: Vec3 = new Vec3()

	private _downKeys: Collection<string, number> = new Collection()
	private _upKeys: Collection<string, number> = new Collection()

	private _createDebugNum: number = 0;
	private _isCreateDebug: boolean = false;
	private _gameSpeed: number = 1;

	/**是否挂机模式 */
	private _onlineMode: boolean = false;

	private _reloadWsNum: number = 0;

	protected onLoad(): void {

		this._giftMgr = new GiftMgr();
		this._fightMgr = FightMgr.getInstance();
		this._effectMgr = EffectMgr.getInstance();
		this._giftMessageMgr = GiftMessageMgr.getInstance();
		this._effectMgr.setEffectParentNd(this.ndContentHeroEffect, this.ndContentMonsterEffect, this.ndContentHurtEffect, this.ndContentHurtTips)
		this._fightMgr.setRoleParentNd(
			this.ndContentHero,
			this.ndContentMonster,
			this.ndContentTower,
			this.ndContentSkill
		)
		this._fightMgr.setRoleMoveSpace(this.ndSpaceMonster, this.ndSpaceHero);
		this._giftMessageMgr.init(this.ndContentGiftTips, this.ndContentBossOrSkillTips);
		/**读取配置文件 */

		this.onRefreshGameConfig();

		this.initUI();

		this.gameSocketInit();
		/* let day = ConfigHelper.getInstance().getRankKeyByMonth();
		log("day", day) */


	}

	/**ui初始化 */
	async initUI() {

		this.ndUITop.active = false
		this.ndUISelectMode.active = false
		this.ndUISelectGameType.active = false
		this.lbGameTime.string = '未开始'
		/**背景 */
		/**防御塔 */
		this._fightMgr.initTower()
		/**云层 */
		// const pfbCloud = await xcore.res.bundleLoadPrefab(
		// 	C_Bundle.abGame,
		// 	'./prefab/Unit/UnitCloud'
		// )
		// instantiate(pfbCloud).setParent(this.ndContentCloud)


		this.ndUITop.active = true
		if (xcore.gameData.gameType == 0) {
			this.ndUISelectMode.active = true;
		} else {
			this.ndUISelectGameType.active = true;
		}

		this.addListener();
		this._fightMgr.gameState = E_GameState.Stop;
		this.sprTagAuto.node.active = false;
		this._fightMgr.setAbleAutoNextGame(false);
		this.sprTagOnlineMode.node.active = false;
		this._fightMgr.setGameOnlineMode(false);
		this._onlineMode = false;
		//测试

		// let pfb = await xcore.res.bundleLoadPrefab(C_Bundle.abGame, './prefab/Unit/UnitBossMessage');
		// let msg = instantiate(pfb).getComponent("UnitBossMessage");
		// msg.node.parent = this.node;
		// msg.setData({ lev: 2 })
		let giftPicUrl = ConfigHelper.getInstance().getConstantConfigByKey('giftPicture');
		if (giftPicUrl) {

			let pfb = await xcore.res.bundleLoadPrefab(C_Bundle.abGame, './prefab/UIGiftDesc');
			let ui = instantiate(pfb)
			ui.parent = this.ndUIContent;

		}
		tween(this.ndRoundStart)
			.repeatForever(tween(this.ndRoundStart)
				.to(0.8, { scale: v3(1.2, 1.2) })
				.to(0.8, { scale: v3(1, 1) })
			).start();


		Tool.setChildrenNodeSortByPriority(this.ndContentBossOrSkillTips, 2000)
		if (xcore.channel == E_Channel.UHO) {
			xcore.gameData.gameLev = 120;
		}

		if (xcore.gameData.gameType != 0) {
			this.btnAutoGameLevel.node.active = false;
			this.btnOnlineMode.node.active = false;

		}
	}


	addListener() {
		this.btnMode.node.on('click', () => {
			xcore.ui.addView(C_View.ViewSelectGameMode)
		}, this);
		this.btnMode2.node.on('click', () => {
			xcore.ui.addView(C_View.ViewSelectGameType)
		}, this);
		this.btnPlay.node.on('click', this.gameStart, this);
		this.btnPlay2.node.on('click', this.gameStart, this);
		this.btnBack.node.on('click', this.gameBackMain, this);
		this.btnBack2.node.on('click', this.gameBackMain, this);
		this.btnSkin.node.on('click', this.selectSkin, this);
		this.btnRank.node.on('click', () => {
			xcore.ui.addView(C_View.ViewRank)
		}, this);
		this.btnPowerRank.node.on('click', () => {
			xcore.ui.addView(C_View.ViewPowerRank);
		})
		this.btnSelectLev.node.on('click', () => {
			xcore.ui.addView(C_View.ViewSelectGameLevel)
		}, this)
		this.btnSetting.node.on('click', () => {
			xcore.ui.addView(C_View.ViewSetting)
		}, this)
		this.btnExchange.node.on('click', () => {
			xcore.ui.addView(C_View.ViewExchange)
		}, this)
		this.btnTest.node.on(
			'click',
			() => {
				xcore.ui.addView(C_View.ViewTestGift, {
					heroUser: this._fightMgr.heroUser,

					giftMgr: this._giftMgr
				})
			},
			this
		)
		this.btnGameNextRoundStart.node.on('click', () => {
			this._fightMgr.goNextGameLevel();
			this.btnGameNextRoundStart.node.active = false;
		}, this);
		this.btnAutoGameLevel.node.on('click', () => {
			let isAuto = !this.sprTagAuto.node.active
			this.sprTagAuto.node.active = isAuto;
			this._fightMgr.setAbleAutoNextGame(isAuto);
			if (isAuto) {
				this.btnGameNextRoundStart.node.active = false;
			}

		}, this)
		this.btnOnlineMode.node.on('click', () => {

			let isAuto = !this.sprTagOnlineMode.node.active
			this.sprTagOnlineMode.node.active = isAuto;
			this._onlineMode = isAuto;
			this._fightMgr.setGameOnlineMode(isAuto);

			if (isAuto) {
				this.sprTagAuto.node.active = isAuto;
				this._fightMgr.setAbleAutoNextGame(isAuto);
				if (isAuto) {
					this.btnGameNextRoundStart.node.active = false;
				}
			}
		}, this)
		this.btnCrossReward.node.on('click', () => {
			xcore.ui.addView(C_View.ViewCrossRewardDesc)
		}, this)
		this.btnTask.node.on('click', () => {
			xcore.ui.addView(C_View.ViewTaskRewardDesc)
		}, this)
		this.btnGameSpeed.node.on('click', this.onClickGameSpeed, this)
		this.btnMoveLeft.node.on('click', this.checkRoleMove.bind(this, 'left'), this)
		this.btnMoveRight.node.on('click', this.checkRoleMove.bind(this, 'right'), this)
		xcore.event.addEventListener(E_EVENT.BaseInfo, this.onRefreshUserInfo, this);
		xcore.event.addEventListener(E_EVENT.GameStart, this.gameStart, this);
		xcore.event.addEventListener(E_EVENT.GameOver, this.gameOver, this);
		xcore.event.addEventListener(E_EVENT.GameReplay, this.gameToReplay, this);
		xcore.event.addEventListener(E_EVENT.GameBack, this.gameBack, this);
		xcore.event.addEventListener(E_EVENT.GameConfig, this.onRefreshGameConfig, this);
		xcore.event.addEventListener(E_EVENT.Round, this.onRefreshRoundData, this);
		xcore.event.addEventListener(E_EVENT.GameScore, this.onRefreshGameScore, this);
		//xcore.event.addEventListener(E_EVENT.GraphicsEffectRange, this.GraphicsEffectRange, this);
		xcore.event.addEventListener(E_EVENT.GameLogin, this.onGameLogin, this);
		xcore.event.addEventListener(E_EVENT.CrateMoreMonster, this.onCreateMonster, this);
		xcore.event.addEventListener(E_EVENT.NextGameLevel, this.onShowNextGameLevelUI, this);
		xcore.event.addEventListener(E_EVENT.GameOut, this.onGameOut, this);
		xcore.event.addEventListener(E_EVENT.ShakeCam, this.Shake, this);

		// if (DEBUG && this.ndUITop.getChildByName('btnTest')) {
		// 	this.ndUITop.getChildByName('btnTest').active = true;
		// }

		input.on(Input.EventType.KEY_DOWN, this.onKeyDown, this);
		input.on(Input.EventType.KEY_UP, this.onKeyUp, this);

		this.onClickGameSpeed(false);
	}

	// 	按住alt，连续点5下G，开启测试模式

	// 开始测试后模式后
	// ctrl+H,随机刷机器人与礼物，用于测试性能

	// 数字1~9代表机器人编号
	// Q:仙女棒-天火技能，累计刷10个获得朱雀扇法宝
	// W:能力药丸-闪电技能，累计刷10个获得雷灵珠法宝
	// E:魔法镜-飞沙走石技能，累计刷10个获得混元伞法宝
	// R:能量电池-恢复20%血量
	// T:恶魔炸弹-三味真火技能，累计刷10个获得神火炉法宝
	// Y:神秘空投-天外陨石技能，累计刷10个获得女娲石法宝
	// U:点攒

	// 例按1+Q， 机器人1刷1个仙女棒
	onKeyDown(event: EventKeyboard) {

		this._downKeys.set(`${event.keyCode}`, event.keyCode);
		switch (event.keyCode) {
			case KeyCode.KEY_G:
				if (this._downKeys.has(`${KeyCode.ALT_LEFT}`)) {

					if (this._isCreateDebug) return
					this._createDebugNum += 1;
					if (this._createDebugNum >= 5) {
						this._isCreateDebug = true;
						log('开启调试模式')
						/* if (this.ndUITop.getChildByName('btnTest')) {
							this.ndUITop.getChildByName('btnTest').active = true;
						} */
					}
				}
				break;
			case KeyCode.KEY_H:
				if (this._isCreateDebug && this._downKeys.has(`${KeyCode.CTRL_LEFT}`)) {
					log("随机刷机器人与礼物，用于测试性能")
					this.testRandomCreateUser();
				}
				break
			case KeyCode.DIGIT_1:

			case KeyCode.DIGIT_2:

			case KeyCode.DIGIT_3:

			case KeyCode.DIGIT_4:

			case KeyCode.DIGIT_5:

			case KeyCode.DIGIT_6:

			case KeyCode.DIGIT_7:

			case KeyCode.DIGIT_8:

			case KeyCode.DIGIT_9:
				this.testAddUser(event.keyCode)
				break
			case KeyCode.KEY_Q:

			case KeyCode.KEY_W:

			case KeyCode.KEY_E:

			case KeyCode.KEY_R:

			case KeyCode.KEY_T:

			case KeyCode.KEY_Y:

			case KeyCode.KEY_U:
				this.testAddGift(event.keyCode)
				break

		}
	}
	onKeyUp(event: EventKeyboard) {

		this._downKeys.delete(`${event.keyCode}`);

	}
	testAddUser(keyCode: KeyCode) {
		if (!this._isCreateDebug) return
		let userId
		if (keyCode == KeyCode.DIGIT_1) {
			userId = 'a01';
		} else if (keyCode == KeyCode.DIGIT_2) {
			userId = 'a02';
		} else if (keyCode == KeyCode.DIGIT_3) {
			userId = 'a03';
		} else if (keyCode == KeyCode.DIGIT_4) {
			userId = 'a04';
		} else if (keyCode == KeyCode.DIGIT_5) {
			userId = 'a05';
		} else if (keyCode == KeyCode.DIGIT_6) {
			userId = 'a06';
		} else if (keyCode == KeyCode.DIGIT_7) {
			userId = 'a07';
		} else if (keyCode == KeyCode.DIGIT_8) {
			userId = 'a08';
		} else if (keyCode == KeyCode.DIGIT_9) {
			userId = 'a09';
		} else {
			return
		}
		this._giftMgr.addGifts([
			{
				userId,
				key: '1',
				num: 1
			}
		])
	}
	testAddGift(giftKey: KeyCode) {
		if (!this._isCreateDebug) return
		let userId
		if (this._downKeys.has(`${KeyCode.DIGIT_1}`)) {
			userId = 'a01';
		} else if (this._downKeys.has(`${KeyCode.DIGIT_2}`)) {
			userId = 'a02';
		} else if (this._downKeys.has(`${KeyCode.DIGIT_3}`)) {
			userId = 'a03';
		} else if (this._downKeys.has(`${KeyCode.DIGIT_4}`)) {
			userId = 'a04';
		} else if (this._downKeys.has(`${KeyCode.DIGIT_5}`)) {
			userId = 'a05';
		} else if (this._downKeys.has(`${KeyCode.DIGIT_6}`)) {
			userId = 'a06';
		} else if (this._downKeys.has(`${KeyCode.DIGIT_7}`)) {
			userId = 'a07';
		} else if (this._downKeys.has(`${KeyCode.DIGIT_8}`)) {
			userId = 'a08';
		} else if (this._downKeys.has(`${KeyCode.DIGIT_9}`)) {
			userId = 'a09';
		} else {
			return
		}
		let key;
		if (giftKey == KeyCode.KEY_Q) {
			key = C_GiftKey.Gift01;
		} else if (giftKey == KeyCode.KEY_W) {
			key = C_GiftKey.Gift02;
		} else if (giftKey == KeyCode.KEY_E) {
			key = C_GiftKey.Gift03;
		} else if (giftKey == KeyCode.KEY_R) {
			key = C_GiftKey.Gift04;
		} else if (giftKey == KeyCode.KEY_T) {
			key = C_GiftKey.Gift05;
		} else if (giftKey == KeyCode.KEY_Y) {
			key = C_GiftKey.Gift06;
		} else if (giftKey == KeyCode.KEY_U) {
			key = C_GiftKey.Like;
		} else {
			return
		}


		this._giftMgr.addGifts([
			{
				userId,
				key,
				num: this._downKeys.has(`${KeyCode.CTRL_LEFT}`) ? 10 : 1
			}
		])
	}
	testRandomCreateUser() {
		let rankNum = Tool.randomNumber(4, 10);
		for (let i = 0; i < rankNum; i++) {
			let userId = Tool.guid();
			let keys = [ /**加入仙族 */
				  /*   JoinHero:  */'1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1',

					/**点赞 */
				  /*   Like:  */'300001', '300001', '300001', '300001', '300001', '300001', , '300001', '300001', '300001', '300001', '300001',
					/**666 */
				 /*    SixSix:  */'300002', '300002', '300002', '300002', '300002', '300002', '300002', '300002',

					/**仙女棒 */
				  /*   Gift01:  */'300003', '300003', '300003', '300003', '300003',
					/**药丸 */
				 /*    Gift02:  */'300004', '300004', '300004',
					/**魔法镜 */
				 /*    Gift03:  */'300005', '300005',
					/**甜甜圈 */
				   /*  Gift04:  */'300006', '300006', '300006', '300006', '300006',
					/**恶魔炸弹 */
				 /*    Gift05: */ '300007',
					/**神秘空投 */
				  /*   Gift06: */ '300008',
			]

			keys = Tool.randomSortArray(keys);

			for (let i = 0; i < 3; i++) {
				let key = keys[i];
				this._giftMgr.addGifts([
					{
						userId: userId,
						key: key,
						num: Tool.randomNumber(1, 20)
					}
				])
			}
		}

	}
	removeListener() {
		this.btnMode.node.off('click');
		this.btnMode2.node.off('click');
		this.btnPlay.node.off('click');
		this.btnPlay2.node.off('click');
		this.btnBack.node.off('click');
		this.btnBack2.node.off('click');
		this.btnSkin.node.off('click');
		this.btnSetting.node.off('click');
		this.btnExchange.node.off('click');
		this.btnRank.node.off('click');
		this.btnPowerRank.node.off('click');
		this.btnGameNextRoundStart.node.off('click');
		this.btnAutoGameLevel.node.off('click');
		this.btnGameSpeed.node.off('click');
		this.btnMoveLeft.node.off('click');
		this.btnMoveRight.node.off('click');
		this.btnOnlineMode.node.off('click');
		this.btnCrossReward.node.off('click');
		xcore.event.removeEventListener(E_EVENT.BaseInfo, this.onRefreshUserInfo, this);
		xcore.event.removeEventListener(E_EVENT.GameStart, this.gameStart, this);
		xcore.event.removeEventListener(E_EVENT.GameOver, this.gameOver, this);
		xcore.event.removeEventListener(E_EVENT.GameReplay, this.gameToReplay, this);
		xcore.event.removeEventListener(E_EVENT.GameBack, this.gameBack, this);
		xcore.event.removeEventListener(E_EVENT.GameConfig, this.onRefreshGameConfig, this);
		xcore.event.removeEventListener(E_EVENT.Round, this.onRefreshRoundData, this);
		xcore.event.removeEventListener(E_EVENT.GameScore, this.onRefreshGameScore, this);
		//xcore.event.removeEventListener(E_EVENT.GraphicsEffectRange, this.GraphicsEffectRange, this);
		xcore.event.removeEventListener(E_EVENT.GameLogin, this.onGameLogin, this);
		xcore.event.removeEventListener(E_EVENT.CrateMoreMonster, this.onCreateMonster, this);
		xcore.event.removeEventListener(E_EVENT.NextGameLevel, this.onShowNextGameLevelUI, this);
		xcore.event.removeEventListener(E_EVENT.GameOut, this.onGameOut, this);
		xcore.event.removeEventListener(E_EVENT.ShakeCam, this.Shake, this);
		input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);
		input.off(Input.EventType.KEY_UP, this.onKeyUp, this);
	}

	GraphicsEffectRange(event: string, data: any) {
		// this.gpT = 1;
		// this.gp.clear();
		// this.gp.circle(data.pos.x, data.pos.y, data.range);
		// this.gp.fill()

	}
	onGameOut() {
		this._isConnect = false;
		if (this._reloadWsNum < 3) {
			this._reloadWsNum++;
			xcore.ui.showToast('正在重连中...');
			this.gameSocketInit();
			//xcore.ui.addView(C_View.ViewCommonTips, { desc: '您与服务器断开链接，请重启玩法' })
		} else {
			this._reloadWsNum = 0;
			this._fightMgr.setGameOverTime(0);
			xcore.ui.addView(C_View.ViewCommonTips, { desc: '您与服务器断开链接，请重启玩法' })
		}
		//this._fightMgr.setGameOverTime(0)
	}
	onCreateMonster(event: string, data: any) {
		this._fightMgr.createMonster(null, null, data.jsonId, data.pos);
	}
	onShowNextGameLevelUI(event: string, lev: number) {
		if (!this.btnGameNextRoundStart.node.active && this._fightMgr.gameState == E_GameState.Gameing || this._fightMgr.gameState == E_GameState.Resume) {
			this.btnGameNextRoundStart.node.active = true;
			this.lbGameNextRoundStart.string = `开始挑战第${lev}关卡`;
		}

	}
	onRefreshGameConfig() {
		//xcore.gameData.gameLev = 10
		//选关模式
		let modeJosnId = ['310001', '310002', '310003'][xcore.gameData.gameMode];
		let config = ConfigHelper.getInstance().getDifficultyConfigByJsonId(modeJosnId);
		this._towerHp = parseInt(config.Hp);
		if (xcore.gameData.gameType == 0) {

			if (!config) {
				xcore.ui.showToast(`配置错误，无法找到${modeJosnId} 难度配置`);
				this.gameBack();
				return
			}

			//初始游戏时间	
			this._gameoverTime = 10000000000000;
			//游戏关卡
			this.lbTime.string = `通关时间：${this._gameoverTime}秒`;

			if (!xcore.gameData.gameSelectLev) {
				xcore.gameData.gameSelectLev = 1;
			}

			this.lbLev.string = `选择关卡：${xcore.gameData.gameSelectLev}`;
			this.lbMode.string = `难度：${["容易", "中等", "困难"][xcore.gameData.gameMode]}`;
			log('fightStartconfig', xcore.gameData.gameSelectLev, config, this._gameoverTime, this._towerHp);
		}
		//副本模式
		else {

			let config = ConfigHelper.getInstance().getDungeonConfigByJsonId(xcore.gameData.gameTypeJsonId)
			console.log("bossmode:", xcore.gameData.gameMode, xcore.gameData.gameTypeJsonId, config);
			let modeTxt = `${["容易", "中等", "困难"][xcore.gameData.gameMode]}`;
			this.lbBossMode.string = `副本Boss:${config.name}`;
		}

	}
	onRefreshUserInfo(eventName: string, data: any) {
		if (!data.heroUserInfo) return


		this.lbDesc.string = '';
		this.lbJoinNum.string = `参与人数：${data.heroUserInfo.size || 0}`;
		// if (data.heroUserInfo as Map<string, IUser>) {
		// 	let heroDesc = '仙族key:\n'
		// 	let info = data.heroUserInfo as Map<string, IUser>
		// 	info.forEach((e) => {
		// 		heroDesc += `${e.userId}:`
		// 		e.giftKey.forEach((a) => {
		// 			heroDesc += `${a.key}x${a.num} `
		// 		})
		// 		heroDesc += '\n'
		// 	})
		// 	this.lbDesc.string += heroDesc
		// }

		// if (data.monsterUserInfo) {
		// 	let monsterDesc = '妖族key:\n'
		// 	let info = data.monsterUserInfo as Map<string, IUser>
		// 	info.forEach((e) => {
		// 		monsterDesc += `${e.userId}:`
		// 		e.giftKey.forEach((a) => {
		// 			monsterDesc += `${a.key}x${a.num} `
		// 		})
		// 		monsterDesc += '\n'
		// 	})
		// 	this.lbDesc.string += monsterDesc
		// }
	}
	onRefreshGameScore() {
		let score = this._fightMgr.getTotalScore();

		this.lbScore.string = `积分池：${Tool.numberToTenThousand(score)}`;
	}
	onRefreshRoundData(eventName: string, data: any) {
		if (!data) {
			this.ndRoundDetail.active = false;
			this.ndBossDetail.active = false;
			this.ndMonsterDetail.active = false;
			return
		}
		if (data.roundDesc) {

			this.lbRound.string = ["简单", "中等", "困难"][xcore.gameData.gameMode] + ' ' + data.roundDesc;
		}
		if (data.monsterId) {
			let config = ConfigHelper.getInstance().getMonsterConfigByJsonId(data.monsterId);
			/* let animaData = ConfigHelper.getInstance().getAnimConfigByJsonId(config.moveAnimation);
			let name = `${animaData.name}_00`; */
			if (!config) return
			xcore.res.remoteLoadSprite(/* xcore.gameData.cospath + `image/${animaData.path}/${name}.png` */config.icon, this.sprMonster, size(90, 90));
			this.lbMonsterDesc.string = `${config.name}\n剩余数量:${data.leftNum}`;
			this.ndMonsterSub.active = !!data.monsterSub;
			this.lbMonsterSub.string = data.monsterSub || '';
		}

		if (data.bossId) {
			let bossConfig = ConfigHelper.getInstance().getMonsterConfigByJsonId(data.bossId);
			if (!bossConfig) return
			let animaData = ConfigHelper.getInstance().getAnimConfigByJsonId(bossConfig.moveAnimation);
			let name = `${animaData.name}_00`;
			this.lbBossDesc.string = `${bossConfig.name}\n${data.bossRoundDesc}`
			xcore.res.remoteLoadSprite(/* xcore.gameData.cospath + `image/${animaData.path}/${name}.png` */bossConfig.icon, this.sprBoss, size(90, 90));

			this.ndBossSub.active = !!data.bossSub;
			this.lbBossSub.string = data.bossSub || '';
		}

		this.ndRoundDetail.active = !!data.roundDesc;
		this.ndBossDetail.active = true;
		this.ndMonsterDetail.active = true;


	}

	/* async updateGiftTopInfo() {
		if (xcore.channel == E_Channel.TIKTOK) {
			let gifts = [];
			let giftConigs = ConfigHelper.getInstance().getGiftConfigs();
			for (let i = 0; i < giftConigs.length; i++) {
				let douyinGiftId = giftConigs[i].douyinGiftId
				if (douyinGiftId) {
					gifts.push(douyinGiftId)
				}
			}
			await Net.updateGiftTopInfo(xcore.gameData.combatId, gifts)
		}

	} */
	gameSocketInit() {
		let url = xcore.channel == E_Channel.TIKTOK ? 'ws://barrage-game.xiaoyisz.com:10088/websocket' : 'ws://42.194.174.223:10088/websocket';
		log("gameSocketInit", url, xcore.channel)
		GameSocketCtrl.inst.connect(url);
	}

	/**游戏开始 */
	async gameStart() {
		if (!this._isConnect && xcore.channel == E_Channel.TIKTOK) {
			xcore.ui.showToast('链接失败');

			this.gameBack();
			return
		}
		try {
			this.btnPlay.interactable = false;
			this.btnPlay2.interactable = false;
			if (!xcore.gameData.combatId) {
				await Net.getCombatId();
				await Net.relogin(xcore.channel, xcore.gameData.channelId, xcore.gameData.token, null, null, xcore.gameData.appId, xcore.gameData.combatId);
				this._fightMgr.checkLastUser();
			}
			await this._fightMgr.fightStart(this._gameoverTime, this._towerHp)
			this.ndUISelectMode.active = false;
			this.ndUISelectGameType.active = false;
			this.lbDesc.string = '';
			this.btnPlay.interactable = true;
			this.btnPlay2.interactable = true;
		} catch (err) {
			console.log('error', err)
			Tool.log('error', err)
			this.gameBack();
		}

	}

	//ws链接成功
	onGameLogin(event: string, isSucess: boolean) {
		//log('onGameLogin:', event, isSucess)
		if (xcore.channel == E_Channel.TIKTOK) {
			this._isConnect = isSucess;
		} else if (xcore.channel == E_Channel.GAME560) {
			this._isConnect = isSucess;

		} else {
			this._isConnect = false;
		}
		/* if (this._isConnect) {
			this._reloadWsNum = 0;
		} */
	}

	/**游戏结束 */
	async gameOver(event: string, data: any) {
		//GameSocketCtrl.inst.closeSocket();
		this.onRefreshUserInfo(null, { heroNum: 0, monsterNum: 0, heroUserInfo: new Map() });


		this._giftMessageMgr.clear();
		await xcore.ui.closeAllView();

		this.scheduleOnce(() => {
			xcore.ui.addView(C_View.ViewGameOver, data);
		}, 0.1)
	}

	/**准备重新开始 */
	async gameToReplay() {

		this.btnPlay.node.active = false;
		this.btnPlay2.node.active = false;
		this.ndRoundDetail.active = false;
		this.ndBossDetail.active = false;
		this.ndMonsterDetail.active = false;
		if (xcore.gameData.gameType == 0) {
			this.ndUISelectMode.active = true;
		} else {
			this.ndUISelectGameType.active = true;
		}
		this.btnGameNextRoundStart.node.active = false;
		//this.btnAutoGameLevel.node.active = false;
		if (!xcore.gameData.gameSelectLev) {
			xcore.gameData.gameSelectLev = 1;
		}
		if (this._onlineMode) {
			xcore.gameData.gameSelectLev = 1;
			this.ndUISelectMode.active = false;
			this.ndUISelectGameType.active = false;
		}
		this.scheduleOnce(() => {
			this.btnPlay.node.active = true;
			this.btnPlay2.node.active = true;
		}, 1.2)

		this._fightMgr.fightToReplay();
		this.onRefreshGameConfig();
		this._fightMgr.initTower();
		xcore.gameData.combatId = null;
		try {
			await Net.getCombatId();
			await Net.relogin(xcore.channel, xcore.gameData.channelId, xcore.gameData.token, null, null, xcore.gameData.appId, xcore.gameData.combatId);
			this.btnPlay.node.active = true;
			this.btnPlay2.node.active = true;
			if (this.ndContentSkill.children.length > 0) {
				this.ndContentSkill.destroyAllChildren();
			}
			if (this.ndContentMonster.children.length > 0) {
				this.ndContentMonster.destroyAllChildren();
			}
			// if (this.ndContentHero.children.length > 0) {
			// 	this.ndContentHero.destroyAllChildren();
			// }
			this._fightMgr.checkLastUser(() => {
				this.gameStart();
			});
		} catch (err) {
			this.btnOnlineMode.node.emit('click');
			if (xcore.gameData.gameType == 0) {
				this.ndUISelectMode.active = true;
			} else {
				this.ndUISelectGameType.active = true;
			}
		}

	}
	/**
  
   * 抖动方法1（左右抖动）
  
   */

	public Shake() {
		TweenSystem.instance.ActionManager.removeAllActionsFromTarget(this.ndCam);
		if (!this._shankTw) {
			this._camTempPos.set(1, 1, 1)
			this._shankTw = new Tween(this.ndCam)
				.call((target) => {
					target.setPosition(this._camTempPos);
				})
				.by(0.05, { worldPosition: new Vec3(10, 10) })

				.by(0.05, { worldPosition: new Vec3(-20, -20) })

				.by(0.05, { worldPosition: new Vec3(10, 10) })


		}

		this._shankTw.start();

	}
	onClickGameSpeed(ifClick: boolean = true) {
		if (ifClick) {
			this._gameSpeed = this._gameSpeed == 1 ? 2 : 1;
		}

		this.lbGameSpeed.string = this._gameSpeed.toString();
		this.ndGameSpeedLeft.active = this._gameSpeed == 2;
		this.ndGameSpeedRight.active = this._gameSpeed == 1;
	}
	/**返回首页 */
	gameBack() {
		GameSocketCtrl.inst.closeSocket();
		this._giftMgr.destroy();
		this._fightMgr.destroy();
		this._effectMgr.destroy();
		this._giftMessageMgr.destroy();
		this.removeListener();
		xcore.res.clearCache();
		xcore.ui.switchScene(null, C_Scene.Ready);
	}
	gameBackMain() {
		GameSocketCtrl.inst.closeSocket();
		this._giftMgr.destroy();
		this._fightMgr.destroy();
		this._effectMgr.destroy();
		this._giftMessageMgr.destroy();
		this.removeListener();
		xcore.res.clearCache();
		xcore.ui.switchScene(null, C_Scene.Main);
	}
	checkRoleMove(type: string) {
		log(type)
		if (type == 'left') {
			let roles = this._fightMgr.getHeroRoles();
			let needmoveRoles = roles.filter((e) => {
				return e.data.pos.x >= 0;
			})

			needmoveRoles.forEach((e, i) => {
				if (i == 0 || Math.random() < 0.5) {
					e.moveLeft();
				}

			})
		} else {
			let roles = this._fightMgr.getHeroRoles();
			let needmoveRoles = roles.filter((e) => {
				return e.data.pos.x < 0
			})

			needmoveRoles.forEach((e, i) => {
				if (i == 0 || Math.random() < 0.5) {
					e.moveRight();
				}
			})
		}
	}
	selectSkin() {
		xcore.ui.addView(C_View.ViewSkinShow)
	}

	async start() {
		//this.updateGiftTopInfo()


	}


	update(deltaTime: number) {
		if (!this._fightMgr) return
		this._fightMgr.tick(deltaTime)
		if (this._gameSpeed == 2) {
			this._fightMgr.tick(deltaTime)
		}
		this.lbGameTime.string = `${this._fightMgr.getTime()}/${this._gameoverTime}`;
		if (this.gpT > 0) {
			this.gpT -= deltaTime;
			if (this.gpT <= 0) {
				this.gp.clear();
			}
		}

	}

	protected onDestroy(): void {
		this._giftMgr.destroy();
		this._fightMgr.destroy();
		//GameSocketCtrl.inst.closeSocket();
	}


}
