{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "fc4e262c-a30a-4969-85dd-a9d7e3446763", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "fc4e262c-a30a-4969-85dd-a9d7e3446763@6c48a", "displayName": "zhinv-attack_17", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "fc4e262c-a30a-4969-85dd-a9d7e3446763", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "fc4e262c-a30a-4969-85dd-a9d7e3446763@f9941", "displayName": "zhinv-attack_17", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": -9, "offsetY": -32, "trimX": 75, "trimY": 69, "width": 82, "height": 126, "rawWidth": 250, "rawHeight": 200, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-41, -63, 0, 41, -63, 0, -41, 63, 0, 41, 63, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [75, 131, 157, 131, 75, 5, 157, 5], "nuv": [0.3, 0.025, 0.628, 0.025, 0.3, 0.655, 0.628, 0.655], "minPos": [-41, -63, 0], "maxPos": [41, 63, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "fc4e262c-a30a-4969-85dd-a9d7e3446763@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "fc4e262c-a30a-4969-85dd-a9d7e3446763@6c48a"}}