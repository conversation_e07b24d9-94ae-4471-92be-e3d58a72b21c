[1, ["9fQRQTVLpLJaUyvdNq9+i5@6c48a"], ["_textureSource"], ["cc.SpriteFrame"], 0, [{"name": "huodou-idle_16", "rect": {"x": 144, "y": 155, "width": 646, "height": 421}, "offset": {"x": 11.5, "y": -77.5}, "originalSize": {"width": 911, "height": 576}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-323, -210.5, 0, 323, -210.5, 0, -323, 210.5, 0, 323, 210.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [144, 421, 790, 421, 144, 0, 790, 0], "nuv": [0.15806805708013172, 0, 0.867178924259056, 0, 0.15806805708013172, 0.7309027777777778, 0.867178924259056, 0.7309027777777778], "minPos": {"x": -323, "y": -210.5, "z": 0}, "maxPos": {"x": 323, "y": 210.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]]