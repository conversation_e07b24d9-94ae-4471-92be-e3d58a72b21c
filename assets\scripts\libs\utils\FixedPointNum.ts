const dotLen = 3;    // 精确度保留3位小数点（可以按需修改此数值）
const scale = Math.pow(10, dotLen);
type Sign = 1 | -1;



export class FixedPointNum {
    sign: Sign = 1;      // 符号位 正负
    v1: number = 0;             // 商
    v2: number = 0;             // 余

    /**
     * 传入字符串数字
     * @param strNum 字符串数字
     */
    constructor(strNum: string)
    /**
     * 传入整数数字
     * @param intNum 整数数字
     */
    constructor(intNum: number)
    /**
     * 传入 符号位，商，余   （外部最好不要调用此构造）
     * @param sign 符号位 1 / -1
     * @param v1 商 （>=0）
     * @param v2 余 （>=0）
     */
    constructor(sign: Sign, v1: number, v2: number)
    /**
     * 构造 0
     */
    constructor()
    constructor(...args: any[]) {
        if (args.length === 3) {
            this.sign = args[0];
            this.v1 = args[1];
            this.v2 = args[2];
            if (this.v2 >= scale) {
                throw new Error("FixedPointNum v2 too big -- " + this.v2);
            }
            if (this.v1 === 0 && this.v2 === 0) {
                this.sign = 1;
            }
            return;
        }

        if (args.length === 0) {
            return;
        }

        if (args.length === 1) {
            // if (typeof args[0] === "number") {
            //     if (args[0].toString().indexOf(".") !== -1) {
            //         throw new Error("FixedPointNum need an int number -- " + args[0]);
            //     }
            //     if (args[0] >= 0) {
            //         this.sign = 1;
            //         this.v1 = args[0];
            //     } else {
            //         this.sign = -1;
            //         this.v1 = -args[0];
            //     }
            // } else {
            //     if ((args[0] as string).length === 0) {
            //         return;
            //     }
            //     let strArr = (args[0] as string).split(".");
            //     this.sign = strArr[0][0] === "-" ? -1 : 1;
            //     this.v1 = parseInt(strArr[0]);
            //     if (this.sign === -1) {
            //         this.v1 = -this.v1;
            //     }
            //     if (strArr[1]) {
            //         this.v2 = parseInt(strLen(strArr[1]));
            //     }
            //     if (this.v1 === 0 && this.v2 === 0) {
            //         this.sign = 1;
            //     }
            // }
            if (typeof args[0] === "number") {
                args[0] = `${args[0]}`;
            }

            if ((args[0] as string).length === 0) {
                return;
            }
            let strArr = (args[0] as string).split(".");
            this.sign = strArr[0][0] === "-" ? -1 : 1;
            this.v1 = parseInt(strArr[0]);
            if (this.sign === -1) {
                this.v1 = -this.v1;
            }
            if (strArr[1]) {
                this.v2 = parseInt(strLen(strArr[1]));
            }
            if (this.v1 === 0 && this.v2 === 0) {
                this.sign = 1;
            }
        }
    }

    /**
     * 0
     */
    static get ZERO() {
        return new FixedPointNum();
    }

    /**
     * 0 （唯一的，不要修改此值，主要用作比较大小）
     */
    static get ZERO_R() {
        return ZERO_FixedPointNum;
    }

    /**
     * 转为浮点数
     */
    toNumber(): number {
        return this.sign * (this.v1 + this.v2 / scale);
    }

    /**
     * 转为字符串
     */
    toString(): string {
        if (this.sign === 1) {
            return this.v1.toString() + "." + "0".repeat(dotLen - this.v2.toString().length) + this.v2;
        }
        return "-" + this.v1.toString() + "." + "0".repeat(dotLen - this.v2.toString().length) + this.v2;
    }

    /**
     * 转为大整数
     */
    getHugeValue(): number {
        return this.sign * (this.v1 * scale + this.v2);
    }

    /**
     * 克隆
     */
    clone(): FixedPointNum {
        return new FixedPointNum(this.sign, this.v1, this.v2);
    }

    /**
     * 向下取整
     */
    floor(): number {
        return this.sign * this.v1;
    }

    /**
     * 向上取整
     */
    ceil(): number {
        return this.sign * (this.v1 + (this.v2 > 0 ? 1 : 0));
    }

    /**
     * 四舍五入
     */
    round(): number {
        return this.sign * (this.v1 + (this.v2 > scale >> 1 ? 1 : 0));
    }

    /**
     * 相反数
     */
    reverse(): FixedPointNum {
        return new FixedPointNum(-this.sign as Sign, this.v1, this.v2);

    }

    /**
     * 加法
     * @param other 
     */
    add(other: FixedPointNum): FixedPointNum {
        let num = this.getHugeValue() + other.getHugeValue();
        let sign: Sign = 1;
        if (num < 0) {
            sign = -1;
            num = -num;
        }
        let res = getShangYu(num, scale);
        return new FixedPointNum(sign, res.shang, res.yu);
    }

    /**
     * 加法，赋值自己
     * @param other 
     */
    addSelf(other: FixedPointNum): FixedPointNum {
        let tmp = this.add(other);
        this.sign = tmp.sign;
        this.v1 = tmp.v1;
        this.v2 = tmp.v2;
        return this;
    }

    /**
     * 减法
     * @param other 
     */
    sub(other: FixedPointNum): FixedPointNum {
        return this.add(other.reverse());
    }

    /**
     * 减法，赋值自己
     * @param other 
     */
    subSelf(other: FixedPointNum): FixedPointNum {
        let tmp = this.sub(other);
        this.sign = tmp.sign;
        this.v1 = tmp.v1;
        this.v2 = tmp.v2;
        return this;
    }

    /**
     * 乘法
     * @param other 
     */
    mul(other: FixedPointNum): FixedPointNum {
        let num = this.v1 * other.v1;
        let tmp1 = getShangYu(this.v1 * other.v2, scale);
        let tmp2 = getShangYu(this.v2 * other.v1, scale);
        let tmp3 = getShangYu(this.v2 * other.v2, scale);

        let tmp4 = new FixedPointNum(1, num, 0).addSelf(new FixedPointNum(1, tmp1.shang, tmp1.yu)).addSelf(new FixedPointNum(1, tmp2.shang, tmp2.yu)).addSelf(new FixedPointNum(1, 0, tmp3.shang));
        let sign: Sign = this.sign === other.sign ? 1 : -1;
        return new FixedPointNum(sign, tmp4.v1, tmp4.v2);
    }
    /**
     * 乘法，赋值自己
     * @param other 
     */
    mulSelf(other: FixedPointNum): FixedPointNum {
        let tmp = this.mul(other);
        this.sign = tmp.sign;
        this.v1 = tmp.v1;
        this.v2 = tmp.v2;
        return this;
    }

    /**
     * 除法
     * @param other 
     */
    div(other: FixedPointNum): FixedPointNum {
        let down = other.v1 * scale + other.v2;
        if (down === 0) {
            
            throw new Error("FixedPointNum div has a zero number");
        }
        let up = this.v1 * scale + this.v2;
        let tmp1 = getShangYu(up, down);
        let tmp2 = getShangYu(tmp1.yu * scale, down);
        let sign: Sign = this.sign === other.sign ? 1 : -1;
        return new FixedPointNum(sign, tmp1.shang, tmp2.shang);
    }

    /**
     * 除法，赋值自己
     * @param other 
     */
    divSelf(other: FixedPointNum): FixedPointNum {
        let tmp = this.div(other);
        this.sign = tmp.sign;
        this.v1 = tmp.v1;
        this.v2 = tmp.v2;
        return this;
    }

    /**
     * 小于
     * @param other 
     */
    LT(other: FixedPointNum): boolean {
        return this.getHugeValue() < other.getHugeValue();
    }

    /**
     * 小于等于
     * @param other 
     */
    LE(other: FixedPointNum): boolean {
        return this.getHugeValue() <= other.getHugeValue();
    }

    /**
     * 等于
     * @param other 
     */
    EQ(other: FixedPointNum): boolean {
        return this.getHugeValue() === other.getHugeValue();
    }

    /**
     * 不等于
     * @param other 
     */
    NE(other: FixedPointNum): boolean {
        return this.getHugeValue() !== other.getHugeValue();
    }

    /**
     * 大于
     * @param other 
     */
    GT(other: FixedPointNum): boolean {
        return this.getHugeValue() > other.getHugeValue();
    }

    /**
     * 大于等于
     * @param other 
     */
    GE(other: FixedPointNum): boolean {
        return this.getHugeValue() >= other.getHugeValue();
    }

    /**
     * 取较大方
     * @param other 
     */
    max(other: FixedPointNum): FixedPointNum {
        return this.GT(other) ? this : other;
    }
    /**
     * 取较小方
     * @param other 
     */
    min(other: FixedPointNum): FixedPointNum {
        return this.LT(other) ? this : other;
    }
}

const ZERO_FixedPointNum = new FixedPointNum();
/**
 * 截取字符串到相应长度
 * @param str 字符串
 * @param len 截取长度
 * @returns 
 */
function strLen(str: string, len = dotLen) {
    let strLen = str.length;
    if (strLen === len) {
        return str;
    } else if (strLen > len) {
        return str.substring(0, len);
    } else {
        return str + "0".repeat(len - strLen);
    }
}

// 获取商数和余数
function getShangYu(up: number, down: number): { "shang": number, "yu": number } {
    if (up < down) {
        return { "shang": 0, "yu": up }
    }
    if (up === down) {
        return { "shang": 1, "yu": 0 };
    }
    let shang = Math.floor(up / down) - 1;
    up = up - (down * shang);
    while (up >= down) {
        shang += 1;
        up -= down;
    }
    return { "shang": shang, "yu": up };
}