{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "083f2107-4c29-4e4b-884c-9cc4ae745e79", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "083f2107-4c29-4e4b-884c-9cc4ae745e79@6c48a", "displayName": "xiaolonglv-attack_10", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "083f2107-4c29-4e4b-884c-9cc4ae745e79", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "083f2107-4c29-4e4b-884c-9cc4ae745e79@f9941", "displayName": "xiaolonglv-attack_10", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": -22.5, "offsetY": -11, "trimX": 34, "trimY": 42, "width": 87, "height": 138, "rawWidth": 200, "rawHeight": 200, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-43.5, -69, 0, 43.5, -69, 0, -43.5, 69, 0, 43.5, 69, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [34, 158, 121, 158, 34, 20, 121, 20], "nuv": [0.17, 0.1, 0.605, 0.1, 0.17, 0.79, 0.605, 0.79], "minPos": [-43.5, -69, 0], "maxPos": [43.5, 69, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "083f2107-4c29-4e4b-884c-9cc4ae745e79@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "083f2107-4c29-4e4b-884c-9cc4ae745e79@6c48a"}}