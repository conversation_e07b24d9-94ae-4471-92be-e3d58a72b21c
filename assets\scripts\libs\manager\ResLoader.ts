/** 资源释放：通过引用计数
 *  1、资源的静态引用：当开发者在编辑器中编辑资源时（例如场景、预制体、材质等），需要在这些资源的属性中配置一些其他的资源，例如在材质中设置贴图，在场景的 Sprite 组件上设置 SpriteFrame。那么这些引用关系会被记录在资源的序列化数据中，引擎可以通过这些数据分析出依赖资源列表，像这样的引用关系就是静态引用
 *  2、资源的动态引用：当开发者在编辑器中没有对资源做任何设置，而是通过代码动态加载资源并设置到场景的组件上，则资源的引用关系不会记录在序列化数据中，引擎无法统计到这部分的引用关系，这些引用关系就是动态引用
 *  
 *  资源加载管理器
 *  
 */

import { Asset, AssetManager, ImageAsset, assetManager, js, log, resources } from "cc";

// 资源加载的处理回调
export type ProcessCallback = (completedCount: number, totalCount: number, item: any) => void;
// 资源加载的完成回调
export type CompletedCallback = (error: Error, resource: any | any[], urls?: string[]) => void;

// load方法的参数结构
export interface LoadArgs {
    bundle?: string;
    url?: string | string[];
    type?: typeof Asset;
    onCompleted?: CompletedCallback;
    onProgess?: ProcessCallback;
    resolve?: any;
    reject?: any;
}

// release方法的参数结构
export interface ReleaseArgs {
    bundle?: string;
    url?: string | string[] | Asset | Asset[],
    type?: typeof Asset,
}

// 兼容性处理
let isChildClassOf = js["isChildClassOf"]
if (!isChildClassOf) {
    isChildClassOf = isChildClassOf;
}

export default class ResLoader {

    public static getLoader(resArgs: any): any {

        return new Promise(async (resolve, reject) => {
            if (resArgs.bundle) {
                let bundle = await ResLoader.getBundle(resArgs.bundle);
                resolve(bundle)
            } else {
                resolve(resources);
            }

        })

    }


    public static formatArgs(): LoadArgs {
        do {
            if (arguments.length < 2) {
                break;
            }
            let ret: LoadArgs = {};
            if (typeof arguments[1] == "string" || arguments[1] instanceof Array) {
                if (typeof arguments[0] == "string") {
                    ret.bundle = arguments[0];
                    ret.url = arguments[1];
                    if (arguments.length > 2 && isChildClassOf(arguments[2], Asset)) {
                        ret.type = arguments[2];

                    }
                } else {
                    break;
                }
            } else if (typeof arguments[0] == "string" || arguments[0] instanceof Array) {
                ret.url = arguments[0];
                if (isChildClassOf(arguments[1], Asset)) {
                    ret.type = arguments[1];
                }
            } else {
                break;
            }
            if (typeof arguments[arguments.length - 1] == "function" && !isChildClassOf(arguments[arguments.length - 1], Asset)) {
                ret.onCompleted = arguments[arguments.length - 1];
                if (typeof arguments[arguments.length - 2] == "function"
                    && !isChildClassOf(arguments[arguments.length - 2], Asset)) {
                    ret.onProgess = arguments[arguments.length - 2];
                }
            }

            return ret;
        } while (false);

        console.error(`formatArgs error ${arguments}`);
        return null;
    }

    static async getBundle(bundleName: string): Promise<AssetManager.Bundle> {
        return new Promise((resolve, reject) => {
            let bundle = assetManager.getBundle(bundleName);
            if (bundle) {
                resolve(bundle)
            } else {
                assetManager.loadBundle(bundleName, (err, bundle) => {
                    if (!err) {
                        resolve(bundle)
                    } else {
                        console.log(`load bundle fail err:${err}`);
                        reject(null)
                    }
                })
            }

        })

    }
    //finishCallback
    private static makeFinishCallback(resArgs: LoadArgs): CompletedCallback {
        //console.time("load|" + resArgs.url);
        let finishCallback = (error: Error, resource: any) => {
            if (resArgs.onCompleted) {
                resArgs.onCompleted(error, resource);
            }
            if (!error) {
                resArgs.resolve(resource);
            } else {
                resArgs.reject(error);
            }
        };
        return finishCallback;
    }

    private static getUuid(ccloader, url: string, type: typeof Asset) {

        if (typeof ccloader.getInfoWithPath == 'function') {
            let assetInfo = ccloader.getInfoWithPath(url, type);

            if (!assetInfo) return null;
            let uuid = assetInfo.uuid;
            return uuid;
        } else {
            return null;
        }
    }
    private static async startLoad(resArgs) {
        return new Promise((resolve, reject) => {
            resArgs.resolve = resolve;
            resArgs.reject = reject;
            let finishCallback = ResLoader.makeFinishCallback(resArgs);
            ResLoader.getLoader(resArgs)
                .then((ccloader) => {

                    if (typeof resArgs.url == "string") {
                        let uuid = ResLoader.getUuid(ccloader, resArgs.url, resArgs.type);
                        if (uuid) {
                            if (resArgs.type) {
                                ccloader.load(resArgs.url, resArgs.type, resArgs.onProgess, finishCallback);
                            } else {
                                ccloader.load(resArgs.url, resArgs.onProgess, finishCallback);
                            }
                        }
                        //远程加载资源没有uuid 
                        else {
                            assetManager.loadRemote(resArgs.url, resArgs.onProgess, finishCallback);
                        }
                    } else {
                        ccloader.load(resArgs.url, resArgs.type, resArgs.onProgess, finishCallback);
                    }
                });
        })
    }

    /**
     * 开始加载资源(重载)
     * @param bundle        assetbundle的路径
     * @param url           资源url或url数组
     * @param type          资源类型，默认为null
     * @param onProgess     加载进度回调
     * @param onCompleted   加载完成回调
     */
    public static load(url: string, type: typeof Asset);
    public static load(url: string, type: typeof ImageAsset);
    public static load(url: string, onCompleted: CompletedCallback);
    public static load(url: string, onProgess: ProcessCallback, onCompleted: CompletedCallback);
    public static load(url: string, type: typeof Asset, onCompleted: CompletedCallback);
    public static load(url: string, type: typeof Asset, onProgess: ProcessCallback, onCompleted: CompletedCallback);
    public static load(url: string[], onCompleted: CompletedCallback);
    public static load(url: string[], onProgess: ProcessCallback, onCompleted: CompletedCallback);
    public static load(url: string[], type: typeof Asset, onCompleted: CompletedCallback);
    public static load(url: string[], type: typeof Asset, onProgess: ProcessCallback, onCompleted: CompletedCallback);
    public static load(bundle: string, url: string, onCompleted: CompletedCallback);
    public static load(bundle: string, url: string, onProgess: ProcessCallback, onCompleted: CompletedCallback);
    public static load(bundle: string, url: string, type: typeof Asset);
    public static load(bundle: string, url: string, type: typeof ImageAsset);
    public static load(bundle: string, url: string, type: typeof Asset, onCompleted: CompletedCallback);
    public static load(bundle: string, url: string, type: typeof Asset, onProgess: ProcessCallback, onCompleted: CompletedCallback);
    public static load(bundle: string, url: string[], onCompleted: CompletedCallback);
    public static load(bundle: string, url: string[], onProgess: ProcessCallback, onCompleted: CompletedCallback);
    public static load(bundle: string, url: string[], type: typeof Asset, onCompleted: CompletedCallback);
    public static load(bundle: string, url: string[], type: typeof Asset, onProgess: ProcessCallback, onCompleted: CompletedCallback);
    public static load() {
        let resArgs: LoadArgs = ResLoader.formatArgs.apply(ResLoader, arguments);
        return ResLoader.startLoad(resArgs);
    }

}