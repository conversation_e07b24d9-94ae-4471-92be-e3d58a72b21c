declare namespace xysz.config {
    const animation: I_animation[];
    const monsterRefresh: I_monsterRefresh[];
    const gameLevel: I_gameLevel[];
    const selectDifficulty: I_selectDifficulty[];
    const gift: I_gift[];
    const weapon: I_weapon[];
    const state: I_state[];
    const skill: I_skill[];
    const skin: I_skin[];
    const attacKeffect: I_attacKeffect[];
    const monster: I_monster[];
    const constant: I_constant;
}
interface I_constant {
    camp1: number;
    camp2: number;
    giftScoreReservoirSharing: number;
    giftScoreReservoirReserve: number;
    hurtColour: string;
}
interface I_monster {
    jsonId: string;
    name: string;
    camp: number;
    eliteMonster: number;
    monsterType: number;
    monsterLevel: number;
    monsterHp: number;
    attack: string;
    callNum: number;
    attackCooldown: string;
    attacKeffect: string;
    attacKeffectInterval: string;
    skill: number;
    movementSpeed: number;
    rebornTime: number;
    monsterScore: number;
    standbyAnimation: string;
    moveAnimation: string;
    attackAnimation: string;
    deathAnimation: string;
    rebornAnimation: string;
    skillAnimation: string;
    giftId: string;
    giftName: string;
    kuaishouGiftId: string;
    kuaishouGiftName: string;
    kuaishouGiftNum: number;
    douyinGiftId: string;
    douyinGiftName: string;
    douyinGiftNum: number;
}
interface I_attacKeffect {
    jsonId: string;
    name: string;
    type: number;
    attacDistance: number;
    doubleHit: number;
    Animation: string;
    impactAnimation: string;
}
interface I_skin {
    jsonId: string;
    name: string;
    camp: number;
    HP: number;
    attack: string;
    attackCooldown: string;
    attacKeffect: string;
    skill: number;
    standbyAnimation: string;
    moveAnimation: string;
    attackAnimation: string;
    skillAnimation: string;
    InitialIimage: number;
    skinRole: string;
    openCondition: number;
    barrage: string;
}
interface I_skill {
    jsonId: string;
    name: string;
    type: number;
    skillDescribe: string;
    skillLevel: number;
    skillDamage: number;
    skillCooldown: number;
    lightningNum: number;
    guardTime: number;
    healing: number;
    healingTime: number;
    hpDeclinePercentage: string;
    state: string;
    skillRange: number;
    skillAnimation: string;
    impactAnimation: string;
    skillTarget: number;
}
interface I_state {
    jsonId: string;
    name: string;
    type: number;
    stateLevel: number;
    stateDescribe: string;
    hurt: number;
    hurtInterval: number;
    percentage: string;
    icon: string;
}
interface I_weapon {
    jsonId: string;
    name: string;
    showWeapon: number;
    weaponType: number;
    weaponLevel: number;
    skilId: string;
    skillNum: number;
    icon: string;
    skillAnimation: string;
    giftId: string;
    giftName: string;
    kuaishouGiftId: string;
    kuaishouGiftName: string;
    kuaishouGiftNum: number;
    douyinGiftId: string;
    douyinGiftName: string;
    douyinGiftNum: number;
}
interface I_gift {
    jsonId: string;
    name: string;
    giftType: number;
    kuaishouGiftId: string;
    kuaishouGiftName: string;
    douyinGiftId: string;
    douyinGiftName: string;
    automationCamp: number;
    integra: number;
    integralUpperLimit: number;
}
interface I_selectDifficulty {
    jsonId: string;
    name: string;
    Hp: string;
    time: string;
}
interface I_gameLevel {
    jsonId: string;
    name: string;
    gameLevel: number;
}
interface I_monsterRefresh {
    jsonId: string;
    name: string;
    gameLevel: string;
    refresh: number;
    monsterId: string;
    monsterTotal: number;
    monsterInterval: string;
    monsterNum: number;
    type: number;
    refreshInterval: number;
}
interface I_animation {
    jsonId: string;
    sample: number;
    duration: number;
    speed: number;
    wrapMode: number;
    path: string;
    name: string;
    target: number;
    XAxisOffset: string;
    YAxisOffset: string;
    notes: string;
}