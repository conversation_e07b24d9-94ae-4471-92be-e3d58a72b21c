System.register("chunks:///_virtual/Agent.ts",["./rollupPluginModLoBabelHelpers.js","cc","./RVOMath.ts"],(function(i){var t,e,s,n,o,r;return{setters:[function(i){t=i.createClass},function(i){e=i.cclegacy},function(i){s=i.RVOMath,n=i.Vector2,o=i.KeyValuePair,r=i.Line}],execute:function(){e._RF.push({},"0887513IChCJZbtGTD8MaEy","Agent",void 0);i("Agent",function(){function i(){this.agentNeighbors_=[],this.obstaclNeighbors_=[],this.orcaLines_=[],this.position_=new n(0,0),this.prefVelocity_=new n(0,0),this.velocity_=new n(0,0),this.id=0,this.maxNeighbors_=0,this.maxSpeed_=0,this._neighborDist=0,this.radius_=0,this.timeHorizon=0,this.timeHorizonObst=0,this.newVelocity_=new n(0,0),this.mass=1}var e=i.prototype;return e.computeNeighbors=function(i){this.obstaclNeighbors_.length=0;var t=Math.pow(this.timeHorizonObst*this.maxSpeed_+this.radius_,2);i.kdTree.computeObstacleNeighbors(this,t),this.agentNeighbors_.length=0,this.maxNeighbors_>0&&(t=Math.pow(this.neighborDist,2),t=i.kdTree.computeAgentNeighbors(this,t))},e.computeNewVelocity=function(i){this.orcaLines_.length=0;for(var t=this.orcaLines_,e=1/this.timeHorizonObst,o=0;o<this.obstaclNeighbors_.length;++o){for(var a=this.obstaclNeighbors_[o].value,c=a.next,h=a.point.minus(this.position_),l=c.point.minus(this.position_),u=!1,p=0;p<t.length;++p)if(s.det(h.scale(e).minus(t[p].point),t[p].direction)-e*this.radius_>=-s.RVO_EPSILON&&s.det(l.scale(e).minus(t[p].point),t[p].direction)-e*this.radius_>=-s.RVO_EPSILON){u=!0;break}if(!u){var d=s.absSq(h),_=s.absSq(l),g=s.sqr(this.radius_),m=c.point.minus(a.point),y=h.scale(-1).multiply(m)/s.absSq(m),v=s.absSq(h.scale(-1).minus(m.scale(y))),b=new r;if(y<0&&d<=g)a.convex&&(b.point=new n(0,0),b.direction=s.normalize(new n(-h.y,h.x)),t.push(b));else if(y>1&&_<=g)c.convex&&s.det(l,c.direction)>=0&&(b.point=new n(0,0),b.direction=s.normalize(new n(-l.y,l.x)),t.push(b));else if(y>=0&&y<=1&&v<=g)b.point=new n(0,0),b.direction=a.direction.scale(-1),t.push(b);else{var f=void 0,x=void 0;if(y<0&&v<=g){if(!a.convex)continue;c=a;var w=Math.sqrt(d-g);f=new n(h.x*w-h.y*this.radius_,h.x*this.radius_+h.y*w).scale(1/d),x=new n(h.x*w+h.y*this.radius_,-h.x*this.radius_+h.y*w).scale(1/d)}else if(y>1&&v<=g){if(!c.convex)continue;a=c;var N=Math.sqrt(_-g);f=new n(l.x*N-l.y*this.radius_,l.x*this.radius_+l.y*N).scale(1/_),x=new n(l.x*N+l.y*this.radius_,-l.x*this.radius_+l.y*N).scale(1/_)}else{if(a.convex){var q=Math.sqrt(d-g);f=new n(h.x*q-h.y*this.radius_,h.x*this.radius_+h.y*q).scale(1/d)}else f=a.direction.scale(-1);if(c.convex){var S=Math.sqrt(_-g);x=new n(l.x*S+l.y*this.radius_,-l.x*this.radius_+l.y*S).scale(1/_)}else x=a.direction}var V=a.previous,M=!1,O=!1;a.convex&&s.det(f,V.direction.scale(-1))>=0&&(f=V.direction.scale(-1),M=!0),c.convex&&s.det(x,c.direction)<=0&&(x=c.direction,O=!0);var P=a.point.minus(this.position_).scale(e),z=c.point.minus(this.position_).scale(e),L=z.minus(P),k=a==c?.5:this.velocity_.minus(P).multiply(L)/s.absSq(L),R=this.velocity_.minus(P).multiply(f),D=this.velocity_.minus(z).multiply(x);if(k<0&&R<0||a==c&&R<0&&D<0){var H=s.normalize(this.velocity_.minus(P));b.direction=new n(H.y,-H.x),b.point=P.plus(H.scale(this.radius_*e)),t.push(b)}else if(k>1&&D<0){var A=s.normalize(this.velocity_.minus(z));b.direction=new n(A.y,-A.x),b.point=z.plus(A.scale(this.radius_*e)),t.push(b)}else{var E=k<0||k>1||a==c?1/0:s.absSq(this.velocity_.minus(L.scale(k).plus(P))),I=R<0?1/0:s.absSq(this.velocity_.minus(f.scale(R).plus(P))),C=D<0?1/0:s.absSq(this.velocity_.minus(x.scale(D).plus(z)));if(E<=I&&E<=C){b.direction=a.direction.scale(-1);var T=new n(-b.direction.y,b.direction.x);b.point=T.scale(this.radius_*e).plus(P),t.push(b)}else if(I<=C){if(M)continue;b.direction=f;var F=new n(-b.direction.y,b.direction.x);b.point=F.scale(this.radius_*e).plus(P),t.push(b)}else if(!O){b.direction=x.scale(-1);var j=new n(-b.direction.y,b.direction.x);b.point=j.scale(this.radius_*e).plus(z),t.push(b)}}}}}for(var B=t.length,G=1/this.timeHorizon,J=0;J<this.agentNeighbors_.length;++J){var K=this.agentNeighbors_[J].value,Z=K.position_.minus(this.position_),Q=K.mass/(this.mass+K.mass),U=this.mass/(this.mass+K.mass),W=Q>=.5?this.velocity_.minus(this.velocity_.scale(Q)).scale(2):this.prefVelocity_.plus(this.velocity_.minus(this.prefVelocity_).scale(2*Q)),X=U>=.5?K.velocity_.scale(2).scale(1-U):K.prefVelocity_.plus(K.velocity_.minus(K.prefVelocity_).scale(2*U)),Y=W.minus(X),$=s.absSq(Z),ii=this.radius_+K.radius_,ti=s.sqr(ii),ei=new r,si=void 0;if($>ti){var ni=Y.minus(Z.scale(G)),oi=s.absSq(ni),ri=ni.multiply(Z);if(ri<0&&s.sqr(ri)>ti*oi){var ai=Math.sqrt(oi),ci=ni.scale(1/ai);ei.direction=new n(ci.y,-ci.x),si=ci.scale(ii*G-ai)}else{var hi=Math.sqrt($-ti);if(s.det(Z,ni)>0){var li=new n(Z.x*hi-Z.y*ii,Z.x*ii+Z.y*hi);ei.direction=li.scale(1/$)}else{var ui=new n(Z.x*hi+Z.y*ii,-Z.x*ii+Z.y*hi);ei.direction=ui.scale(-1/$)}var pi=Y.multiply(ei.direction);si=ei.direction.scale(pi).minus(Y)}}else{var di=1/i,_i=Y.minus(Z.scale(di)),gi=s.abs(_i),mi=_i.scale(1/gi);ei.direction=new n(mi.y,-mi.x),si=mi.scale(ii*di-gi)}ei.point=W.plus(si.scale(Q)),t.push(ei)}var yi=this.linearProgram2(t,this.maxSpeed_,this.prefVelocity_,!1,this.newVelocity_);yi<t.length&&this.linearProgram3(t,B,yi,this.maxSpeed_,this.newVelocity_)},e.insertAgentNeighbor=function(i,t){if(this!=i){var e=s.absSq(this.position_.minus(i.position_));if(e<t){this.agentNeighbors_.length<this.maxNeighbors_&&this.agentNeighbors_.push(new o(e,i));for(var n=this.agentNeighbors_.length-1;0!=n&&e<this.agentNeighbors_[n-1].key;)this.agentNeighbors_[n]=this.agentNeighbors_[n-1],--n;this.agentNeighbors_[n]=new o(e,i),this.agentNeighbors_.length==this.maxNeighbors_&&(t=this.agentNeighbors_[this.agentNeighbors_.length-1].key)}}return t},e.insertObstacleNeighbor=function(i,t){var e=i.next,n=s.distSqPointLineSegment(i.point,e.point,this.position_);if(n<t){this.obstaclNeighbors_.push(new o(n,i));for(var r=this.obstaclNeighbors_.length-1;0!=r&&n<this.obstaclNeighbors_[r-1].key;)this.obstaclNeighbors_[r]=this.obstaclNeighbors_[r-1],--r;this.obstaclNeighbors_[r]=new o(n,i)}},e.update=function(i){this.velocity_.copy(this.newVelocity_),this.position_.copy(this.position_.plus(this.velocity_.scale(i)))},e.linearProgram1=function(i,t,e,n,o,r){var a=i[t].point.multiply(i[t].direction),c=s.sqr(a)+s.sqr(e)-s.absSq(i[t].point);if(c<0)return!1;for(var h=Math.sqrt(c),l=-a-h,u=-a+h,p=0;p<t;++p){var d=s.det(i[t].direction,i[p].direction),_=s.det(i[p].direction,i[t].point.minus(i[p].point));if(Math.abs(d)<=s.RVO_EPSILON){if(_<0)return!1}else{var g=_/d;if(d>=0?u=Math.min(u,g):l=Math.max(l,g),l>u)return!1}}if(o)n.multiply(i[t].direction)>0?r.copy(i[t].point.plus(i[t].direction.scale(u))):r.copy(i[t].point.plus(i[t].direction.scale(l)));else{var m=i[t].direction.multiply(n.minus(i[t].point));m<l?r.copy(i[t].point.plus(i[t].direction.scale(l))):m>u?r.copy(i[t].point.plus(i[t].direction.scale(u))):r.copy(i[t].point.plus(i[t].direction.scale(m)))}return!0},e.linearProgram2=function(i,t,e,n,o){n?o.copy(e.scale(t)):s.absSq(e)>s.sqr(t)?o.copy(s.normalize(e).scale(t)):o.copy(e);for(var r=0;r<i.length;++r)if(s.det(i[r].direction,i[r].point.minus(o))>0){var a=o.clone();if(!this.linearProgram1(i,r,t,e,n,o))return o.copy(a),r}return i.length},e.linearProgram3=function(i,t,e,o,a){for(var c=0,h=e;h<i.length;++h)if(s.det(i[h].direction,i[h].point.minus(a))>c){for(var l=[],u=0;u<t;++u)l.push(i[u]);for(var p=t;p<h;++p){var d=new r,_=s.det(i[h].direction,i[p].direction);if(Math.abs(_)<=s.RVO_EPSILON){if(i[h].direction.multiply(i[p].direction)>0)continue;d.point=i[h].point.plus(i[p].point).scale(.5)}else d.point=i[h].point.plus(i[h].direction.scale(s.det(i[p].direction,i[h].point.minus(i[p].point))/_));d.direction=s.normalize(i[p].direction.minus(i[h].direction)),l.push(d)}var g=a.clone();this.linearProgram2(l,o,new n(-i[h].direction.y,i[h].direction.x),!0,a)<l.length&&a.copy(g),c=s.det(i[h].direction,i[h].point.minus(a))}},t(i,[{key:"neighborDist",get:function(){return this._neighborDist},set:function(i){this._neighborDist=i}}]),i}());e._RF.pop()}}}));

System.register("chunks:///_virtual/api.ts",["cc"],(function(e){var c;return{setters:[function(e){c=e.cclegacy}],execute:function(){c._RF.push({},"2821fLqMMZEZb7A7puzAWyM","api",void 0);e("WEB_SOCKET_URL","ws://barrage-game.xiaoyisz.com:10088/websocket");c._RF.pop()}}}));

System.register("chunks:///_virtual/App.ts",["./rollupPluginModLoBabelHelpers.js","cc","./Singleton.ts","./xcore.ts","./Sound.ts","./TimerManager.ts","./ConstGlobal.ts","./Tool.ts","./ElectronAPI.ts","./TimeUtil.ts"],(function(t){var e,n,a,o,i,s,r,u,c,d,m,l,p,f,g,D,h,v;return{setters:[function(t){e=t.inheritsLoose,n=t.asyncToGenerator,a=t.regeneratorRuntime},function(t){o=t.cclegacy,i=t.warn,s=t.Node,r=t.director,u=t.game,c=t.Game,d=t.Director},function(t){m=t.Singleton},function(t){l=t.xcore},function(t){p=t.Sound},function(t){f=t.TimerManager},function(t){g=t.E_Channel},function(t){D=t.default},function(t){h=t.default},function(t){v=t.default}],execute:function(){o._RF.push({},"badb3C5JVBDo6RisCuKe5ee","App",void 0);t("default",function(t){function o(){for(var e,n=arguments.length,a=new Array(n),o=0;o<n;o++)a[o]=arguments[o];return(e=t.call.apply(t,[this].concat(a))||this).persistRootNode=null,e}e(o,t);var m=o.prototype;return m.initServerInfo=function(){var t=n(a().mark((function t(){return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:this.initRuntime(),this.initEnv(),l.http.addHeader("content-type","application/json"),console.log("sEnv",l.channel),l.http.defaults.baseURL=l.channel==g.TIKTOK?"https://apig.xiaoyisz.com/live-game-ntm-v1/":"https://dev-api.xiaoyisz.com/live-game-service/",l.http.setRequestInterceptor((function(t){return t})),l.http.setResponseInterceptor((function(t){var e=t.data;return 0===e.code?e:903==e.code||904==e.code||802==e.code||907==e.code?(l.ui.showToast("网络异常，请手动刷新再试！"),new Error("网络异常，请手动刷新再试！")):(2026==e.code||2027==e.code||(i(""+e.message),l.ui.showToast(""+e.message),D.log("error","http req:"+t.path+" code:"+e.code+" message:"+e.message)),new Error(e.code))}));case 7:case"end":return t.stop()}}),t,this)})));return function(){return t.apply(this,arguments)}}(),m.init=function(){var t=n(a().mark((function t(){return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.initServerInfo();case 2:return t.next=4,this.initGameConfig();case 4:this.persistRootNode=new s("PersistRootNode"),r.addPersistRootNode(this.persistRootNode),l.sound=this.persistRootNode.addComponent(p),l.timer=this.persistRootNode.addComponent(f),l.ui.initViewConfig(),this.initOperation(),u.on(c.EVENT_SHOW,(function(){})),u.on(c.EVENT_HIDE,(function(){})),r.on(d.EVENT_BEFORE_SCENE_LOADING,(function(){l.ui.closeAllView()}),this);case 13:case"end":return t.stop()}}),t,this)})));return function(){return t.apply(this,arguments)}}(),m.initGameConfig=function(){var t=n(a().mark((function t(){return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}(),m.initEnv=function(){D.getCommandLineArgs().token?l.channel=g.TIKTOK:l.channel=g.GAME560},m.initRuntime=function(){l.gameData.query=D.parseQuery((null===location||void 0===location?void 0:location.search)||"")},m.initOperation=function(){var t=!1,e=h.readFile("sound.json");if(e){var n=JSON.parse(e);l.gameData.soundData=n,null!=l.gameData.soundData.sound&&null!=l.gameData.soundData.sound||(l.gameData.soundData.sound=1,t=!0),null!=l.gameData.soundData.music&&null!=l.gameData.soundData.music||(l.gameData.soundData.music=1,t=!0)}else l.gameData.soundData={sound:1,music:1},t=!0;l.sound.setSoundNum(l.gameData.soundData.sound),l.sound.setMusicNum(l.gameData.soundData.music),l.gameData.gameJoinTime=v.formatTimestampToDate(Math.floor(v.getServerTime()),"_");var a=h.readFile("gift_"+l.gameData.gameJoinTime+".json");if(a){var o=JSON.parse(a);l.gameData.giftData=o}else l.gameData.giftData={},t=!0;t&&this.updateOperation()},m.updateOperation=function(){l.gameData&&(l.gameData.soundData&&h.writeFile("sound.json",{sound:l.gameData.soundData.sound,music:l.gameData.soundData.music}),l.gameData.giftData&&h.writeFile("gift_"+l.gameData.gameJoinTime+".json",l.gameData.giftData))},o}(m));o._RF.pop()}}}));

System.register("chunks:///_virtual/ArrayUtil.ts",["cc"],(function(r){var n;return{setters:[function(r){n=r.cclegacy}],execute:function(){n._RF.push({},"c2ed6X45k1IZomzfeY2ZVh+","ArrayUtil",void 0);r("ArrayUtil",function(){function r(){}return r.noRepeated=function(r){for(var n=[r[0]],t=1;t<r.length;t++){for(var e=!1,o=0;o<n.length;o++)if(r[t]==n[o]){e=!0;break}e||n.push(r[t])}return n},r.copy2DArray=function(r){for(var n=[],t=0;t<r.length;t++)n.push(r[t].concat());return n},r.fisherYatesShuffle=function(r){for(var n=r.length;n;){var t=Math.floor(Math.random()*n--),e=r[n];r[n]=r[t],r[t]=e}return r},r.confound=function(r){return r.slice().sort((function(){return Math.random()-.5}))},r.flattening=function(r){for(;r.some((function(r){return Array.isArray(r)}));)r=[].concat.apply([],r);return r},r.removeItem=function(r,n){for(var t=r.concat(),e=0;e<t.length;e++){if(n==t[e]){r.splice(e,1);break}}},r.combineArrays=function(r,n){return[].concat(r,n)},r.getRandomValueInArray=function(r){return r[Math.floor(Math.random()*r.length)]},r}());n._RF.pop()}}}));

System.register("chunks:///_virtual/AsyncQueue.ts",["./rollupPluginModLoBabelHelpers.js","cc"],(function(s){var n,i,e,u;return{setters:[function(s){n=s.createClass},function(s){i=s.cclegacy,e=s.warn,u=s.log}],execute:function(){i._RF.push({},"60bafNvuNhFLZwOC+2D/i8E","AsyncQueue",void 0),s("AsyncQueue",function(){function s(){this._runningAsyncTask=null,this._queues=[],this._isProcessingTaskUUID=0,this._enable=!0,this.complete=null}var i=s.prototype;return i.push=function(n,i){void 0===i&&(i=null);var e=s._$uuid_count++;return this._queues.push({uuid:e,callbacks:[n],params:i}),e},i.pushMulti=function(n){for(var i=s._$uuid_count++,e=arguments.length,u=new Array(e>1?e-1:0),t=1;t<e;t++)u[t-1]=arguments[t];return this._queues.push({uuid:i,callbacks:u,params:n}),i},i.remove=function(s){var n;if((null==(n=this._runningAsyncTask)?void 0:n.uuid)!==s){for(var i=0;i<this._queues.length;i++)if(this._queues[i].uuid===s){this._queues.splice(i,1);break}}else e("正在执行的任务不可以移除")},i.clear=function(){this._queues=[],this._isProcessingTaskUUID=0,this._runningAsyncTask=null},i.step=function(){this.isProcessing&&this.next(this._isProcessingTaskUUID)},i.play=function(s){var n=this;if(void 0===s&&(s=null),!this.isProcessing&&this._enable){var i=this._queues.shift();if(i){this._runningAsyncTask=i;var e=i.uuid;this._isProcessingTaskUUID=e;var u=i.callbacks;if(1==u.length){u[0]((function(s){void 0===s&&(s=null),n.next(e,s)}),i.params,s)}else for(var t=u.length,r=[],c=function(s){void 0===s&&(s=null),--t,r.push(s||null),0===t&&n.next(e,r)},l=t,a=0;a<l;a++)u[a](c,i.params,s)}else this._isProcessingTaskUUID=0,this._runningAsyncTask=null,this.complete&&this.complete(s)}},i.yieldTime=function(s,n){void 0===n&&(n=null);this.push((function(i,e,u){var t=setTimeout((function(){clearTimeout(t),n&&n(),i(u)}),s)}),{des:"AsyncQueue.yieldTime"})},i.next=function(s,n){void 0===n&&(n=null),this._isProcessingTaskUUID===s?(this._isProcessingTaskUUID=0,this._runningAsyncTask=null,this.play(n)):this._runningAsyncTask&&u(this._runningAsyncTask)},s.excuteTimes=function(s,n){void 0===n&&(n=null);var i=s;return function(){0===--i&&n&&n()}},n(s,[{key:"queues",get:function(){return this._queues}},{key:"enable",get:function(){return this._enable},set:function(s){this._enable!==s&&(this._enable=s,s&&this.size>0&&this.play())}},{key:"size",get:function(){return this._queues.length}},{key:"isProcessing",get:function(){return this._isProcessingTaskUUID>0}},{key:"isStop",get:function(){return!(this._queues.length>0)&&!this.isProcessing}},{key:"runningParams",get:function(){return this._runningAsyncTask?this._runningAsyncTask.params:null}}]),s}())._$uuid_count=1,i._RF.pop()}}}));

System.register("chunks:///_virtual/BaseLlistCtr.ts",["./rollupPluginModLoBabelHelpers.js","cc","./List.ts","./ListItem.ts"],(function(t){var o,n,i,e,s,l,r,c,a,u,h;return{setters:[function(t){o=t.applyDecoratedDescriptor,n=t.inheritsLoose,i=t.initializerDefineProperty,e=t.assertThisInitialized},function(t){s=t.cclegacy,l=t._decorator,r=t.ScrollView,c=t.warn,a=t.Component},function(t){u=t.default},function(t){h=t.default}],execute:function(){var d,f,p,L,v;s._RF.push({},"d1a26AN+WBOrL2OAdolqlDt","BaseLlistCtr",void 0);var m=l.ccclass,g=l.property;t("default",(d=m("BaseListCtr"),f=g(r),d((v=o((L=function(t){function o(){for(var o,n=arguments.length,s=new Array(n),l=0;l<n;l++)s[l]=arguments[l];return o=t.call.apply(t,[this].concat(s))||this,i(o,"sv",v,e(o)),o.list=null,o.data=void 0,o._scrollToIndex=null,o}n(o,t);var s=o.prototype;return s.onLoadCompleted=function(){this.list=this.sv.getComponent(u),this.sv.node.on("scroll-to-top",this.onScrollToTop,this),this.sv.node.on("scroll-to-bottom",this.onScrollToBottom,this),this.sv.node.on("scroll-to-left",this.onScrollToLeft,this),this.sv.node.on("scroll-to-right",this.onScrollToRight,this),this.onLoadCompletedB()},s.onLoadCompletedB=function(){},s.refreshListData=function(t){this.list||(this.list=this.sv.getComponent(u),this.list)?t&&(t=t.filter((function(t){return t})),this.data=t,this.list.numItems=this.data.length):c("检查ScrollView上是否挂载List")},s.onListVRender=function(t,o){t.getComponent(h).setData(this.data[o],o),this._scrollToIndex=o},s.onListHRender=function(t,o){},s.onListGridRender=function(t,o){},s.onListGrid2Render=function(t,o){},s.onListSelected=function(t,o,n,i){},o}(a)).prototype,"sv",[f],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),p=L))||p));s._RF.pop()}}}));

System.register("chunks:///_virtual/BroadcastCtrl.ts",["./rollupPluginModLoBabelHelpers.js","cc","./MessageCtrl.ts"],(function(n){var t,e,r,o,s;return{setters:[function(n){t=n.inheritsLoose,e=n.asyncToGenerator,r=n.regeneratorRuntime},function(n){o=n.cclegacy},function(n){s=n.default}],execute:function(){o._RF.push({},"215d6tXiulEq5qbVnzUaNXa","BroadcastCtrl",void 0);n("default",function(n){function o(){return n.apply(this,arguments)||this}t(o,n);var s=o.prototype;return s.listen=function(n,t){this.node.on("recode",t,n)},s.unListen=function(n,t){this.node.off("recode",t,n)},s.send=function(n){console.warn("广播监听无法发送")},s.sendSync=function(){var n=e(r().mark((function n(t){return r().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return this.send(),n.abrupt("return",this.succFalse);case 2:case"end":return n.stop()}}),n,this)})));return function(t){return n.apply(this,arguments)}}(),s.receive=function(n,t){this.node.emit("recode",t)},o}(s));o._RF.pop()}}}));

System.register("chunks:///_virtual/Canvas2Image.ts",["cc"],(function(t){var e,n;return{setters:[function(t){e=t.cclegacy,n=t._decorator}],execute:function(){var a;e._RF.push({},"2a623uap0lEO6Puk8uUAILd","Canvas2Image",void 0);var r=n.ccclass;n.property,t("Canvas2Image",r("Canvas2Image")(a=function(){function t(){}return t.getInstance=function(){var t,e,n=(t=document.createElement("canvas"),{canvas:!!(e=t.getContext("2d")),imageData:!!e.getImageData,dataURL:!!t.toDataURL,btoa:!!window.btoa});function a(t,e,n){var a=t.width,r=t.height;null==e&&(e=a),null==n&&(n=r);var o=document.createElement("canvas"),c=o.getContext("2d");return o.width=e,o.height=n,c.drawImage(t,0,0,a,r,0,0,e,n),o}function r(t,e,n){s(t,e,n)}function o(t){var e=document.createElement("img");return e.src=t,e}function c(t){return"image/"+(t=t.toLowerCase().replace(/jpg/i,"jpeg")).match(/png|jpeg|bmp|gif/)[0]}function u(t){if(!window.btoa)throw"btoa undefined";var e="";if("string"==typeof t)e=t;else for(var n=0;n<t.length;n++)e+=String.fromCharCode(t[n]);return btoa(e)}function i(t){var e=t.width,n=t.height;return t.getContext("2d").getImageData(0,0,e,n)}function g(t,e){return"data:"+e+";base64,"+t}var f=function(t){var e=t.width,n=t.height,a=e*n*3,r=a+54,o=[66,77,255&r,r>>8&255,r>>16&255,r>>24&255,0,0,0,0,54,0,0,0],c=[40,0,0,0,255&e,e>>8&255,e>>16&255,e>>24&255,255&n,n>>8&255,n>>16&255,n>>24&255,1,0,24,0,0,0,0,0,255&a,a>>8&255,a>>16&255,a>>24&255,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],i=(4-3*e%4)%4,g=t.data,f="",d=e<<2,m=n,s=String.fromCharCode;do{for(var v=d*(m-1),l="",p=0;p<e;p++){var h=p<<2;l+=s(g[v+h+2])+s(g[v+h+1])+s(g[v+h])}for(var b=0;b<i;b++)l+=String.fromCharCode(0);f+=l}while(--m);return u(o.concat(c))+u(f)},d=function(t,e,o,u,d){if(n.canvas&&n.dataURL)if("string"==typeof t&&(t=document.getElementById(t)),null==u&&(u="png"),u=c(u),/bmp/.test(u)){var m=i(a(t,e,o));r(g(f(m),"image/octet-stream"),u.replace("image/",""),d)}else(t=a(t,e,o)).toBlob((function(t){r(URL.createObjectURL(t),u.replace("image/",""),d)}),u,1)},m=function(t,e,r,u){if(n.canvas&&n.dataURL){if("string"==typeof t&&(t=document.getElementById(t)),null==u&&(u="png"),u=c(u),/bmp/.test(u)){var d=i(a(t,e,r));return o(g(f(d),"image/bmp"))}return o(function(t,e,n,r){return(t=a(t,n,r)).toDataURL(e)}(t,u,e,r))}},s=function(t,e,n){var a=document.createElement("a");a.style.display="none",a.href=t,a.download=n+"."+e,document.body.appendChild(a),a.click(),document.body.removeChild(a)};return{saveAsImage:d,saveAsPNG:function(t,e,n){return d(t,e,n,"png","defaultpng")},saveAsJPEG:function(t,e,n){return d(t,e,n,"jpeg","defaultjpg")},saveAsGIF:function(t,e,n){return d(t,e,n,"gif","defaultgif")},saveAsBMP:function(t,e,n){return d(t,e,n,"bmp","defaultbmp")},convertToImage:m,convertToPNG:function(t,e,n){return m(t,e,n,"png")},convertToJPEG:function(t,e,n){return m(t,e,n,"jpeg")},convertToGIF:function(t,e,n){return m(t,e,n,"gif")},convertToBMP:function(t,e,n){return m(t,e,n,"bmp")}}},t}())||a);e._RF.pop()}}}));

System.register("chunks:///_virtual/CCCEventManager.ts",["./rollupPluginModLoBabelHelpers.js","cc","./CCCSctiptSingleton.ts"],(function(t){var n,e,r,c,s;return{setters:[function(t){n=t.inheritsLoose,e=t.createClass},function(t){r=t.cclegacy,c=t._decorator},function(t){s=t.default}],execute:function(){var a;r._RF.push({},"7077e9w2s5MrLndcHdlhRTQ","CCCEventManager",void 0);var i=c.ccclass;t("CCCEventManager",i("UIEventManager")(a=function(t){function r(){return t.apply(this,arguments)||this}return n(r,t),e(r,null,[{key:"inst",get:function(){return this.getInst(r)}}]),r}(s))||a);r._RF.pop()}}}));

System.register("chunks:///_virtual/CCCSctiptSingleton.ts",["./rollupPluginModLoBabelHelpers.js","cc","./EventManager.ts"],(function(n){var e,t,o,i,s,l;return{setters:[function(n){e=n.inheritsLoose},function(n){t=n.cclegacy,o=n.js,i=n.Node,s=n.director},function(n){l=n.EventManager}],execute:function(){t._RF.push({},"e06abHqzFtB84PpMNc0XKlX","CCCSctiptSingleton",void 0);var r=n("default",function(n){function t(){return n.apply(this,arguments)||this}return e(t,n),t.getInst=function(n,e){void 0===e&&(e="");var l=o.getClassName(n);if(""!=e&&(l+=e),!this._insts.has(l)){var r=(new i).addComponent(n);r.node.name=l,null==t.singletonHome&&(t.singletonHome=new i,t.singletonHome.name="SingletonHome",s.addPersistRootNode(this.singletonHome)),this.singletonHome.addChild(r.node),this._insts.set(l,r),console.log("创建单例",l)}return this._insts.get(l)},t}(l));r.singletonHome=void 0,r._insts=new Map,t._RF.pop()}}}));

System.register("chunks:///_virtual/CmdLogin.ts",["./rollupPluginModLoBabelHelpers.js","cc","./MessageCtrl.ts","./MessageEvent.ts","./user.mjs_cjs=&original=.js","./user.js"],(function(e){var n,t,r,s,o,c,i,u,a,l;return{setters:[function(e){n=e.inheritsLoose,t=e.createClass,r=e.asyncToGenerator,s=e.regeneratorRuntime},function(e){o=e.cclegacy,c=e._decorator},function(e){i=e.default},function(e){u=e.MessageCmdEvent,a=e.MessageUserSubCmd},null,function(e){l=e.default}],execute:function(){var d;o._RF.push({},"7a04fWNhodJda4gqW4oNwSp","CmdLogin",void 0);var g=c.ccclass;e("default",g()(d=function(e){function o(){var n;return(n=e.call(this)||this).cmd=u.userCmd,n.subCmd=a.login,n.addListen(),n}n(o,e);var c=o.prototype;return c.sendSync=function(){var n=r(s().mark((function n(t){var r;return s().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return r=l.pb.UserLoginReqPb.encode(t),console.log("decode::::::",l.pb.UserLoginReqPb.decode(r.finish())),n.next=4,e.prototype.sendSync.call(this,r.finish());case 4:return n.abrupt("return",n.sent);case 5:case"end":return n.stop()}}),n,this)})));return function(e){return n.apply(this,arguments)}}(),c.receive=function(n,t){try{console.log("cmdlogin receive1:",t);var r=l.pb.UserLoginResPb.decode(t);console.log("cmdlogin receive2::",r),e.prototype.receive.call(this,n,r)}catch(t){e.prototype.receive.call(this,n,t)}},t(o,null,[{key:"inst",get:function(){return this.getInst(o)}}]),o}(i))||d);o._RF.pop()}}}));

System.register("chunks:///_virtual/cocoscreator-game.cjs.min.ts",["cc"],(function(t){var e,r;return{setters:[function(t){e=t.cclegacy,r=t.sys}],execute:function(){e._RF.push({},"441eeXt7e1CGaBy0/S0ToLK","cocoscreator-game.cjs.min",void 0);var n=t("default",{is_first_launch:!1,launched:!1,_queue:[],mpshow_time:null,sa_referrer:"直接打开",query_share_depth:0,share_distinct_id:"",share_method:"",current_scene:"",inited:!1,para:{server_url:"",send_timeout:1e3,show_log:!1,allow_amend_share_path:!0,max_string_length:500,datasend_timeout:3e3,source_channel:[],batch_send:{send_timeout:6e3,max_length:6},preset_properties:{}},platform:"",lib:{version:"0.6.2",name:"MiniGame",method:"code"},properties:{$lib:"MiniGame",$lib_version:"0.6.2"},source_channel_standard:"utm_source utm_medium utm_campaign utm_content utm_term",latest_source_channel:["$latest_utm_source","$latest_utm_medium","$latest_utm_campaign","$latest_utm_content","$latest_utm_term","$latest_sa_utm"],latest_share_info:["$latest_share_distinct_id","$latest_share_url_path","$latest_share_depth","$latest_share_method"],currentProps:{}}),a=Object.prototype.toString,i=Object.prototype.hasOwnProperty,o=Array.prototype.indexOf,s=Array.prototype.slice,c=Array.isArray,l=Array.prototype.forEach,u=Function.prototype.bind;function f(t){return"[object String]"==a.call(t)}function p(t){return"[object Date]"==a.call(t)}function g(t){return"[object Boolean]"==a.call(t)}function d(t){return"[object Number]"==a.call(t)&&/[\d\.]+/.test(String(t))}var m=c||function(t){return"[object Array]"===Object.prototype.toString.call(t)};function _(t){try{JSON.parse(t)}catch(t){return!1}return!0}function h(t){return null!=t&&"[object Object]"===a.call(t)}function S(t){return"[object Object]"===a.call(t)}function b(t){return!(!t||!i.call(t,"callee"))}function y(t,e,r){if(null==t)return!1;if(l&&t.forEach===l)t.forEach(e,r);else if(t.length===+t.length){for(var n=0,a=t.length;n<a;n++)if(n in t&&e.call(r,t[n],n,t)==={})return!1}else for(var o in t)if(i.call(t,o)&&e.call(r,t[o],o,t)==={})return!1}function A(t,e){if(!t)return[];var r=[];return t.toArray&&(r=t.toArray()),m(t)&&(r=s.call(t)),b(t)&&(r=s.call(t)),r=v(t),e&&d(e)?r.slice(e):r}function v(t){var e=[];return null==t||y(t,(function(t){e[e.length]=t})),e}function P(t,e){var r=!1;return null==t?r:o&&t.indexOf===o?-1!=t.indexOf(e):(y(t,(function(t){if(r=r||t===e)return{}})),r)}function I(t){function e(t){return t<10?"0"+t:t}return t.getFullYear()+"-"+e(t.getMonth()+1)+"-"+e(t.getDate())+" "+e(t.getHours())+":"+e(t.getMinutes())+":"+e(t.getSeconds())+"."+e(t.getMilliseconds())}function j(t){h(t)&&y(t,(function(e,r){h(e)?j(t[r]):p(e)&&(t[r]=I(e))}))}function C(t){return!!t&&("[object Function]"==(t=Object.prototype.toString.call(t))||"[object AsyncFunction]"==t||"[object GeneratorFunction]"==t)}function D(t){return y(s.call(arguments,1),(function(e){for(var r in e)void 0!==e[r]&&(t[r]=e[r])})),t}function M(t){return y(s.call(arguments,1),(function(e){for(var r in e)void 0!==e[r]&&null!==e[r]&&(h(e[r])&&h(t[r])?D(t[r],e[r]):t[r]=e[r])})),t}function k(t){if(h(t)){for(var e in t)if(i.call(t,e))return!1;return!0}return!1}function O(t){return t.length>n.para.max_string_length?(n.log("字符串长度超过限制，已经做截取--"+t),t.slice(0,n.para.max_string_length)):t}function w(t){h(t)&&y(t,(function(e,r){h(e)?w(t[r]):f(e)&&(t[r]=O(e))}))}function T(t){for(var e,r="",n=e=0,a=(t=(t+"").replace(/\r\n/g,"\n").replace(/\r/g,"\n")).length,i=0;i<a;i++){var o=t.charCodeAt(i),s=null;o<128?e++:s=127<o&&o<2048?String.fromCharCode(o>>6|192,63&o|128):String.fromCharCode(o>>12|224,o>>6&63|128,63&o|128),null!==s&&(n<e&&(r+=t.substring(n,e)),r+=s,n=e=i+1)}return n<e&&(r+=t.substring(n,t.length)),r}function E(t){var e,r,n,a,i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",o=0,s=0,c="",l=[];if(!t)return t;for(t=T(t);e=(a=t.charCodeAt(o++)<<16|t.charCodeAt(o++)<<8|t.charCodeAt(o++))>>12&63,r=a>>6&63,n=63&a,l[s++]=i.charAt(a>>18&63)+i.charAt(e)+i.charAt(r)+i.charAt(n),o<t.length;);switch(c=l.join(""),t.length%3){case 1:c=c.slice(0,-2)+"==";break;case 2:c=c.slice(0,-1)+"="}return c}function N(t){var e=t.toLowerCase();return"ios"===e?"iOS":"android"===e?"Android":t}var L,$=(L=(new Date).getTime(),function(t){return Math.ceil((L=(9301*L+49297)%233280)/233280*t)});function V(){if("function"==typeof Uint32Array){var t="";if("undefined"!=typeof crypto?t=crypto:"undefined"!=typeof msCrypto&&(t=msCrypto),h(t)&&t.getRandomValues){var e=new Uint32Array(1);return t.getRandomValues(e)[0]/Math.pow(2,32)}}return $(1e19)/1e19}function x(){return Date.now()+"-"+Math.floor(1e7*V())+"-"+V().toString(16).replace(".","")+"-"+String(31242*V()).replace(".","").slice(0,8)}var R={getUUID:x,formatSystem:N,indexOf:o,slice:s,forEach:l,bind:u,_hasOwnProperty:i,_toString:a,isUndefined:function(t){return void 0===t},isString:f,isDate:p,isBoolean:g,isNumber:d,isJSONString:_,isObject:h,isPlainObject:S,isArray:m,isFuction:function(t){try{return/^\s*\bfunction\b/.test(t)}catch(t){return!1}},isArguments:b,toString:function(t){return null==t?"":m(t)||S(t)&&t.toString===a?JSON.stringify(t,null,2):String(t)},unique:function(t){for(var e,r=[],n={},a=0;a<t.length;a++)n[e=t[a]]||(n[e]=!0,r.push(e));return r},include:P,values:v,toArray:A,each:y,formatDate:I,searchObjDate:j,utf8Encode:T,decodeURIComponent:function t(e){var r="";try{r=t(e)}catch(t){r=e}return r},encodeDates:function t(e){return y(e,(function(r,n){p(r)?e[n]=I(r):h(r)&&(e[n]=t(r))})),e},base64Encode:E,trim:function(t){return t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")},isFunction:C,extend:D,extend2Lev:M,isEmptyObject:k,searchObjString:w,formatString:O};function F(){var t,e;!n.batch_state.sended||0<(e=(t=n.batch_state.mem).length)&&(n.batch_state.sended=!1,function(t){if(m(t.data)&&0<t.data.length){var e=Date.now(),r=n.para.datasend_timeout;t.data.forEach((function(t){t._flush_time=e})),t.data=JSON.stringify(t.data);var a={url:n.para.server_url,method:"POST",dataType:"text",data:"data_list="+encodeURIComponent(E(t.data)),timeout:r,success:function(){t.success(t.len)},fail:function(){t.fail()}};n.lib&&"KuaishouMini"===n.lib.name&&(a.header={"Content-Type":"application/x-www-form-urlencoded"}),n.system_api.request(a)}else t.success(t.len)}({data:t,len:e,success:U,fail:J}))}function J(){n.batch_state.sended=!0,n.batch_state.failTime++}function U(t){n.batch_state.clear(t),n.batch_state.sended=!0,n.batch_state.changed=!0,G(),n.batch_state.failTime=0}function G(){n.batch_state.changed&&(n.batch_state.is_first_batch_write&&(n.batch_state.is_first_batch_write=!1,setTimeout((function(){F()}),1e3)),n.batch_state.syncStorage&&(n.system_api.setStorageSync("sensors_prepare_data",n.batch_state.mem),n.batch_state.changed=!1))}n.batch_state={mem:[],changed:!1,sended:!0,is_first_batch_write:!0,sync_storage:!1,failTime:0,getLength:function(){return this.mem.length},add:function(t){this.mem.push(t)},clear:function(t){this.mem.splice(0,t)}},n.prepareData=function(t,e){var r,a=D(a={distinct_id:n.store.getDistinctId(),lib:{$lib:n.lib.name,$lib_method:n.lib.method,$lib_version:String(n.lib.version)},properties:{}},t);if(h(t.properties)&&!k(t.properties)&&(a.properties=D(a.properties,t.properties)),t.type&&"profile"===t.type.slice(0,7)||(n.para.batch_send&&(a._track_id=Number(String(V()).slice(2,5)+String(V()).slice(2,4)+String(Date.now()).slice(-4))),a.properties=D({},n.properties,n.currentProps,a.properties),"object"==typeof n.store._state&&"number"==typeof n.store._state.first_visit_day_time&&n.store._state.first_visit_day_time>(new Date).getTime()?a.properties.$is_first_day=!0:a.properties.$is_first_day=!1),a.properties.$time&&p(a.properties.$time)?(a.time=+a.properties.$time,delete a.properties.$time):a.time=+new Date,h(r=a.properties)&&y(r,(function(t,e){var a;m(t)&&(a=[],y(t,(function(e){f(e)?a.push(e):n.log("您的数据-",t,"的数组里的值必须是字符串,已经将其删除")})),0!==a.length?r[e]=a:(delete r[e],n.log("已经删除空的数组"))),f(t)||d(t)||p(t)||g(t)||m(t)||(n.log("您的数据-",t,"-格式不满足要求，我们已经将其删除"),delete r[e])})),j(a),w(a),!n.para.server_url)return!1;n.log(a),n.send(a)},n.send=function(t){if(t._nocache=(String(V())+String(V())+String(V())).slice(2,15),"sensorsdata2015_binance"===n.storageName&&"native"===n.para.data_report_type)return function(t){var e,r="sensors_";t._flush_time=Date.now(),e=t.event?r+t.event:r+t.type,t.dataSource="sensors",n.log("report_event, name: ",e,"-- key: ",t),__mp_private_api__.reportEvent(e,t)}(t),!1;if(n.para.batch_send){if(300<=n.batch_state.getLength())return n.log("数据量存储过大，有异常"),!1;n.batch_state.add(t),n.batch_state.changed=!0,n.batch_state.getLength()>=n.para.batch_send.max_length&&F()}else!function(t){t._flush_time=Date.now(),t=JSON.stringify(t);var e=-1!==n.para.server_url.indexOf("?")?n.para.server_url+"&data="+encodeURIComponent(E(t)):n.para.server_url+"?data="+encodeURIComponent(E(t));t=n.para.datasend_timeout,n.system_api.request({url:e,dataType:"text",method:"GET",timeout:t})}(t)},n.log=function(){if(n.para.show_log&&"object"==typeof console&&console.log)try{var t=Array.prototype.slice.call(arguments);return console.log.apply(console,t)}catch(t){console.log(arguments[0])}},n.track=function(t,e,r){n.prepareData({type:"track",event:t,properties:e},r)},n.setProfile=function(t){n.prepareData({type:"profile_set",properties:t})},n.setOnceProfile=function(t,e){n.prepareData({type:"profile_set_once",properties:t},e)},n.login=function(t){var e=n.store.getFirstId(),r=n.store.getDistinctId();t!==r&&(e||n.store.set("first_id",r),n.trackSignup(t,"$SignUp"))},n.logout=function(t){var e=n.store.getFirstId();e?(n.store.set("first_id",""),!0===t?n.store.set("distinct_id",x()):n.store.set("distinct_id",e)):n.log("没有first_id，logout失败")},n.identify=function(t){if("number"==typeof t)t=String(t);else if("string"!=typeof t)return!1;n.store.getFirstId()?n.store.set("first_id",t):n.store.set("distinct_id",t)},n.trackSignup=function(t,e,r){n.prepareData({original_id:n.store.getFirstId()||n.store.getDistinctId(),distinct_id:t,type:"track_signup",event:e,properties:r}),n.store.set("distinct_id",t)},n.registerApp=function(t){h(t)&&!k(t)&&(n.currentProps=D(n.currentProps,t))},n.clearAppRegister=function(t){m(t)&&y(n.currentProps,(function(e,r){P(t,r)&&delete n.currentProps[r]}))},n.use=function(t){var e=this._installedPlugins||(this._installedPlugins=[]);if(-1<e.indexOf(t))return this;var r=A(arguments,1);return r.unshift(this),"function"==typeof t.init?t.init.apply(t,r):"function"==typeof t&&t.apply(null,r),e.push(t),this},n.init=function(){if(!0===n.hasExeInit)return!1;this.hasExeInit=!0,n.store.init(),n.system.init(),n.para.batch_send&&(n.system_api.getStorage("sensors_prepare_data",(function(t){var e=[];t&&t.data&&m(t.data)&&(e=t.data,n.batch_state.mem=e.concat(n.batch_state.mem)),n.batch_state.syncStorage=!0})),function t(){setTimeout((function(){G(),t()}),1e3)}(),function t(){setTimeout((function(){F(),t()}),n.para.batch_send.send_timeout*Math.pow(2,n.batch_state.failTime))}())},n.setPara=function(t){n.para=M(n.para,t);var e=[];if(m(n.para.source_channel))for(var r=n.para.source_channel.length,a=0;a<r;a++)-1===" utm_source utm_medium utm_campaign utm_content utm_term sa_utm ".indexOf(" "+n.para.source_channel[a]+" ")&&e.push(n.para.source_channel[a]);n.para.source_channel=e,"number"!=typeof n.para.send_timeout&&(n.para.send_timeout=1e3);var i={send_timeout:6e3,max_length:6};t&&t.datasend_timeout||n.para.batch_send&&(n.para.datasend_timeout=1e4),!0===n.para.batch_send?n.para.batch_send=D({},i):h(n.para.batch_send)&&(n.para.batch_send=D({},i,n.para.batch_send)),n.para.server_url?n.para.preset_properties=h(n.para.preset_properties)?n.para.preset_properties:{}:n.log("请使用 setPara() 方法设置 server_url 数据接收地址")},n.checkInit=function(){!0===n.system.inited&&!0===n.store.inited&&(n.inited=!0,0<n._queue.length&&(y(n._queue,(function(t){n[t[0]].apply(n,s.call(t[1]))})),n._queue=[]))},y(["setProfile","setOnceProfile","track","login","logout","identify","registerApp","clearAppRegister"],(function(t){var e=n[t];n[t]=function(){n.inited?e.apply(n,arguments):n._queue.push([t,arguments])}}));var q={inited:!0,storageInfo:null,_state:{},toState:function(t){h(t)&&t.distinct_id?this._state=t:this.set("distinct_id",x())},getFirstId:function(){return this._state.first_id},getDistinctId:function(){return this._state.distinct_id},getUnionId:function(){var t={},e=this._state.first_id,r=this._state.distinct_id;return e&&r?(t.login_id=r,t.anonymous_id=e):t.anonymous_id=r,t},getProps:function(){return this._state.props||{}},setProps:function(t,e){var r=this._state.props||{};e?this.set("props",t):(D(r,t),this.set("props",r))},set:function(t,e){var r,n={};for(r in"string"==typeof t?n[t]=e:"object"==typeof t&&(n=t),this._state=this._state||{},n)this._state[r]=n[r];this.save()},save:function(){n.system_api.setStorageSync(n.storageName,this._state)},init:function(){var t,e=n.system_api.getStorageSync(n.storageName);e?this.toState(e):(n.is_first_launch=!0,e=(t=new Date).getTime(),t.setHours(23),t.setMinutes(59),t.setSeconds(60),n.setOnceProfile({$first_visit_time:new Date}),this.set({distinct_id:x(),first_visit_time:e,first_visit_day_time:t.getTime()}))}};n.store=q;var B={request:function(t){var e;t.timeout&&(e=t.timeout,delete t.timeout);var r=n.platform_obj.request(t);setTimeout((function(){try{h(r)&&C(r.abort)&&r.abort()}catch(t){n.log(t)}}),e)},getStorage:function(t,e){try{n.platform_obj.getStorage({key:t,success:r,fail:r})}catch(e){try{n.platform_obj.getStorage({key:t,success:r,fail:r})}catch(t){n.log("获取 storage 失败！",t)}}function r(t){if(t&&t.data&&_(t.data))try{var r=JSON.parse(t.data);t.data=r}catch(t){n.log("parse res.data 失败！",t)}e(t)}},setStorage:function(t,e){var r;try{r=JSON.stringify(e)}catch(e){n.log("序列化缓存对象失败！",e)}try{n.platform_obj.setStorage({key:t,data:r})}catch(e){try{n.platform_obj.setStorage({key:t,data:r})}catch(e){n.log("设置 storage 失败: ",e)}}},getStorageSync:function(t){var e="";try{e=n.platform_obj.getStorageSync(t)}catch(r){try{e=n.platform_obj.getStorageSync(t)}catch(t){n.log("获取 storage 失败！")}}return _(e)?JSON.parse(e):e},setStorageSync:function(t,e){var r;try{r=JSON.stringify(e)}catch(t){n.log("序列化缓存对象失败！",t)}e=function(){n.platform_obj.setStorageSync(t,r)};try{e()}catch(t){n.log("set Storage fail --",t);try{e()}catch(t){n.log("set Storage fail again --",t)}}}};var H={inited:!1,init:function(){var t=(new Date).getTimezoneOffset();d(t)&&(n.properties.$timezone_offset=t);var e=function(){var t;if(h(t=n.platform_obj.getAccountInfoSync?n.platform_obj.getAccountInfoSync():t)&&h(t.miniProgram))return t.miniProgram.appId}()||n.para.app_id||n.para.appid;e&&(n.properties.$app_id=e),t=new Promise((function(t){n.platform_obj.getNetworkType({success:function(t){n.properties.$network_type=t.networkType},fail:function(t){n.log("获取网络状态信息失败： ",t)},complete:function(){t()}})})),e=new Promise((function(t,e){n.platform_obj.getSystemInfo({success:function(t){var e=n.properties;h(t)&&(e.$manufacturer=t.brand,e.$model=t.model,e.$screen_width=Number(t.screenWidth),e.$screen_height=Number(t.screenHeight),e.$os=N(t.platform||"android"),e.$os_version=-1<t.system.indexOf(" ")?t.system.split(" ")[1]:t.system)},fail:function(t){n.log("获取系统信息失败: ",t)},complete:function(){t()}})})),Promise.all([t,e]).then((function(){n.system.inited=!0,n.checkInit()}))}},Q={inited:!1,init:function(){var t=(new Date).getTimezoneOffset();d(t)&&(n.properties.$timezone_offset=t),(t=n.para.app_id||n.para.appid)&&(n.properties.$app_id=t);try{n.properties.$screen_width=Number(window.screen.width),n.properties.$screen_height=Number(window.screen.height)}catch(t){n.log("获取screen异常")}n.system.inited=!0,n.checkInit()}},Y={get:function(t){return window.localStorage.getItem(t)},parse:function(t){var e;try{e=JSON.parse(this.get(t))||null}catch(t){n.log(t)}return e},set:function(t,e){window.localStorage.setItem(t,e)},remove:function(t){window.localStorage.removeItem(t)},isSupport:function(){var t=!0;try{var e="__sensorsdatasupport__",r="testIsSupportStorage";this.set(e,r),this.get(e)!==r&&(t=!1),this.remove(e)}catch(e){t=!1}return t}};var z={request:function(t){if((t=t||{}).timeout=t.timeout||3e4,!t.method||!t.url)return!1;try{(new Image).src=t.url}catch(t){n.log("创建AJAX请求失败"+t)}},getStorageSync:function(t){return Y.parse(t)},setStorageSync:function(t,e){var r;try{r=JSON.stringify(e)}catch(t){n.log("序列化缓存对象失败！")}try{Y.set(t,r)}catch(t){n.log("set Storage fail!")}}},W={init:function(t){jsb.reflection.callStaticMethod("com/cocos/game/CCSensorsDataAPI","initSA","(Ljava/lang/String;)V",t)},track:function(t,e){jsb.reflection.callStaticMethod("com/cocos/game/CCSensorsDataAPI","track","(Ljava/lang/String;Ljava/lang/String;)V",t,e)},login:function(t){jsb.reflection.callStaticMethod("com/cocos/game/CCSensorsDataAPI","login","(Ljava/lang/String;)V",t)},logout:function(){jsb.reflection.callStaticMethod("com/cocos/game/CCSensorsDataAPI","logout","()V")},identify:function(t){jsb.reflection.callStaticMethod("com/cocos/game/CCSensorsDataAPI","identify","(Ljava/lang/String;)V",t)},registerApp:function(t){jsb.reflection.callStaticMethod("com/cocos/game/CCSensorsDataAPI","registerApp","(Ljava/lang/String;)V",t)},clearAppRegister:function(t){jsb.reflection.callStaticMethod("com/cocos/game/CCSensorsDataAPI","clearAppRegister","(Ljava/lang/String;)V",t)},setProfile:function(t){jsb.reflection.callStaticMethod("com/cocos/game/CCSensorsDataAPI","setProfile","(Ljava/lang/String;)V",t)},setOnceProfile:function(t){jsb.reflection.callStaticMethod("com/cocos/game/CCSensorsDataAPI","setOnceProfile","(Ljava/lang/String;)V",t)},setIncrementProfile:function(t,e){jsb.reflection.callStaticMethod("com/cocos/game/CCSensorsDataAPI","setIncrementProfile","(Ljava/lang/String;F)V",t,e)},setAppendProfile:function(t){jsb.reflection.callStaticMethod("com/cocos/game/CCSensorsDataAPI","setAppendProfile","(Ljava/lang/String;)V",t)},profileUnset:function(t){jsb.reflection.callStaticMethod("com/cocos/game/CCSensorsDataAPI","profileUnset","(Ljava/lang/String;)V",t)},profileDelete:function(){jsb.reflection.callStaticMethod("com/cocos/game/CCSensorsDataAPI","profileDelete","()V")},flush:function(){jsb.reflection.callStaticMethod("com/cocos/game/CCSensorsDataAPI","flush","()V")},trackTimerStart:function(t){return jsb.reflection.callStaticMethod("com/cocos/game/CCSensorsDataAPI","trackTimerStart","(Ljava/lang/String;)Ljava/lang/String;",t)},trackTimerPause:function(t){jsb.reflection.callStaticMethod("com/cocos/game/CCSensorsDataAPI","trackTimerPause","(Ljava/lang/String;)V",t)},trackTimerResume:function(t){jsb.reflection.callStaticMethod("com/cocos/game/CCSensorsDataAPI","trackTimerResume","(Ljava/lang/String;)V",t)},trackTimerEnd:function(t,e){jsb.reflection.callStaticMethod("com/cocos/game/CCSensorsDataAPI","trackTimerEnd","(Ljava/lang/String;Ljava/lang/String;)V",t,e)},trackAppInstall:function(t){jsb.reflection.callStaticMethod("com/cocos/game/CCSensorsDataAPI","trackAppInstall","(Ljava/lang/String;)V",t)},trackAppViewScreen:function(t,e){jsb.reflection.callStaticMethod("com/cocos/game/CCSensorsDataAPI","trackAppViewScreen","(Ljava/lang/String;Ljava/lang/String;)V",t,e)},getDistinctId:function(){return jsb.reflection.callStaticMethod("com/cocos/game/CCSensorsDataAPI","getDistinctId","()Ljava/lang/String;")},getAnonymousId:function(){return jsb.reflection.callStaticMethod("com/cocos/game/CCSensorsDataAPI","getAnonymousId","()Ljava/lang/String;")},getLoginId:function(){return jsb.reflection.callStaticMethod("com/cocos/game/CCSensorsDataAPI","getLoginId","()Ljava/lang/String;")},getPresetProperties:function(){return jsb.reflection.callStaticMethod("com/cocos/game/CCSensorsDataAPI","getPresetProperties","()Ljava/lang/String;")}},K={init:function(t){jsb.reflection.callStaticMethod("CCSensorsDataAPI","initSA:",t)},track:function(t,e){jsb.reflection.callStaticMethod("CCSensorsDataAPI","track:properties:",t,e)},login:function(t){jsb.reflection.callStaticMethod("CCSensorsDataAPI","login:",t)},logout:function(){jsb.reflection.callStaticMethod("CCSensorsDataAPI","logout")},identify:function(t){jsb.reflection.callStaticMethod("CCSensorsDataAPI","identify:",t)},registerApp:function(t){jsb.reflection.callStaticMethod("CCSensorsDataAPI","registerSuperProperties:",t)},clearAppRegister:function(t){jsb.reflection.callStaticMethod("CCSensorsDataAPI","unregisterSuperProperty:",t)},setProfile:function(t){jsb.reflection.callStaticMethod("CCSensorsDataAPI","setProfile:",t)},setOnceProfile:function(t){jsb.reflection.callStaticMethod("CCSensorsDataAPI","setOnceProfile:",t)},setIncrementProfile:function(t,e){jsb.reflection.callStaticMethod("CCSensorsDataAPI","increment:by:",t,e)},setAppendProfile:function(t){jsb.reflection.callStaticMethod("CCSensorsDataAPI","profileAppend:",t)},profileUnset:function(t){jsb.reflection.callStaticMethod("CCSensorsDataAPI","profileUnset:",t)},profileDelete:function(){jsb.reflection.callStaticMethod("CCSensorsDataAPI","profileDelete")},flush:function(){jsb.reflection.callStaticMethod("CCSensorsDataAPI","flush")},trackTimerStart:function(t){return jsb.reflection.callStaticMethod("CCSensorsDataAPI","trackTimerStart:",t)},trackTimerPause:function(t){jsb.reflection.callStaticMethod("CCSensorsDataAPI","trackTimerPause:",t)},trackTimerResume:function(t){jsb.reflection.callStaticMethod("CCSensorsDataAPI","trackTimerResume:",t)},trackTimerEnd:function(t,e){jsb.reflection.callStaticMethod("CCSensorsDataAPI","trackTimerEnd:withProperties:",t,e)},trackAppInstall:function(t){jsb.reflection.callStaticMethod("CCSensorsDataAPI","trackAppInstall:",t)},trackAppViewScreen:function(t,e){jsb.reflection.callStaticMethod("CCSensorsDataAPI","trackAppViewScreen:withProperties:",t,e)},getDistinctId:function(){return jsb.reflection.callStaticMethod("CCSensorsDataAPI","getDistinctId")},getAnonymousId:function(){return jsb.reflection.callStaticMethod("CCSensorsDataAPI","getAnonymousId")},getLoginId:function(){return jsb.reflection.callStaticMethod("CCSensorsDataAPI","getLoginId")},getPresetProperties:function(){return jsb.reflection.callStaticMethod("CCSensorsDataAPI","getPresetProperties")}};function X(t){return"[object String]"==Object.prototype.toString.call(t)}function Z(t){return"[object Boolean]"==Object.prototype.toString.call(t)}var et=Array.isArray||function(t){return"[object Array]"===Object.prototype.toString.call(t)};var rt=["track","login","logout","identify","registerApp","clearAppRegister","setProfile","setOnceProfile"],nt=["setIncrementProfile","setAppendProfile","profileUnset","profileDelete","flush","trackTimerStart","trackTimerPause","trackTimerResume","trackTimerEnd","trackAppInstall","trackAppViewScreen","getDistinctId","getAnonymousId","getLoginId","getPresetProperties"];switch(n._=R,n.system_api=B,n.system=H,function(){var t=r.platform,e=r.Platform.WECHAT_GAME,n=r.Platform.QQ_PLAY||"",a=r.Platform.BYTEDANCE_GAME;if(t===r.Platform.OPPO_MINI_GAME)return"OPPO_MINI_GAME";if(t===r.Platform.VIVO_MINI_GAME)return"VIVO_MINI_GAME";return t===e?"WECHAT_GAME":t===n?"QQ_GAME":t===a?"BYTEDANCE_GAME":"DEFAULT"}()){case"WECHAT_GAME":n.platform_obj=wx,n.storageName="sd2015-wechat-game";break;case"QQ_GAME":n.platform_obj=qq,n.storageName="sd2015-qq-game";break;case"BYTEDANCE_GAME":n.platform_obj=tt,n.storageName="sd2015-bytedance-game";break;case"VIVO_MINI_GAME":n.platform_obj=qg,n.storageName="sd2015-vivo-game";break;case"OPPO_MINI_GAME":n.platform_obj=qg,n.storageName="sd2015-oppo-game";break;default:n.para.batch_send=!1,n.system_api=z,n.system=Q,n.storageName="sd2015-h5-game"}!function(t){if(nt.forEach((function(e){t[e]=function(){return console.log("该方法未定义！"),!1}})),!r.isNative||"iOS"!=r.os&&"Android"!=r.os)return!1;rt.forEach((function(e){t[e]=function(){return console.log("该方法未定义！"),!1}})),t.para.app_start=!1,t.para.app_end=!1,t.para.enable_encrypt=!1,t.para.enable_native=!1;var e={iOS:K,Android:W}[r.os];t.init=function(){if(!Z(t.para.enable_native)||!t.para.enable_native)return!1;Z(t.para.app_start)||(t.para.app_start=!1),Z(t.para.app_end)||(t.para.app_end=!1),Z(t.para.enable_encrypt)||(t.para.enable_encrypt=!1),Z(t.para.show_log)||(t.para.show_log=!1);var r={serverUrl:t.para.server_url,appStart:t.para.app_start,appEnd:t.para.app_end,enableLog:t.para.show_log,enableEncrypt:t.para.enable_encrypt};!function(t,e){t.track=function(t,r){if(!X(t))return console.log("传入的事件名称不是一个合法的字符串！"),!1;r=JSON.stringify(r),e.track(t,r)},t.login=function(t){X(t)?e.login(t):console.log("传入的 id 不是一个合法的字符串！")},t.logout=function(){e.logout()},t.identify=function(t){e.identify(t)},t.registerApp=function(t){t=JSON.stringify(t),e.registerApp(t)},t.clearAppRegister=function(t){if(!et(t))return console.log("参数类型错误！"),!1;for(var r in t)X(t[r])?e.clearAppRegister(t[r]):console.log("参数类型错误！")},t.setProfile=function(t){t=JSON.stringify(t),e.setProfile(t)},t.setOnceProfile=function(t){t=JSON.stringify(t),e.setOnceProfile(t)},t.setIncrementProfile=function(t,r){t&&function(t){return"[object Number]"==Object.prototype.toString.call(t)&&/[\d\.]+/.test(String(t))}(r)?e.setIncrementProfile(t,r):console.log("输入参数错误！")},t.setAppendProfile=function(t,r){var n={};n[t]=r,n=JSON.stringify(n),e.setAppendProfile(n)},t.profileUnset=function(t){e.profileUnset(t)},t.profileDelete=function(){e.profileDelete()},t.flush=function(){e.flush()},t.trackTimerStart=function(t){return e.trackTimerStart(t)},t.trackTimerPause=function(t){e.trackTimerPause(t)},t.trackTimerResume=function(t){e.trackTimerResume(t)},t.trackTimerEnd=function(t,r){r=JSON.stringify(r),e.trackTimerEnd(t,r)},t.trackAppInstall=function(t){t=JSON.stringify(t),e.trackAppInstall(t)},t.trackAppViewScreen=function(t,r){r=JSON.stringify(r),e.trackAppViewScreen(t,r)},t.getDistinctId=function(){return e.getDistinctId()},t.getAnonymousId=function(){return e.getAnonymousId()},t.getLoginId=function(){return e.getLoginId()},t.getPresetProperties=function(){return e.getPresetProperties()}}(t,e),r=JSON.stringify(r),e.init(r)}}(n),e._RF.pop()}}}));

System.register("chunks:///_virtual/Collection.ts",["./rollupPluginModLoBabelHelpers.js","cc"],(function(t){var r,e,a,i;return{setters:[function(t){r=t.inheritsLoose,e=t.createClass,a=t.wrapNativeSuper},function(t){i=t.cclegacy}],execute:function(){i._RF.push({},"99a6a0AMRJAyJId4coN9wOd","Collection",void 0);t("Collection",function(t){function a(){for(var r,e=arguments.length,a=new Array(e),i=0;i<e;i++)a[i]=arguments[i];return(r=t.call.apply(t,[this].concat(a))||this)._array=[],r}r(a,t);var i=a.prototype;return i.set=function(r,e){if(this.has(r)){var a=this.get(r),i=this._array.indexOf(a);this._array[i]=e}else this._array.push(e);return t.prototype.set.call(this,r,e)},i.delete=function(r){var e=this.get(r);if(e){var a=this._array.indexOf(e);return a>-1&&this._array.splice(a,1),t.prototype.delete.call(this,r)}return!1},i.clear=function(){this._array.splice(0,this._array.length),t.prototype.clear.call(this)},e(a,[{key:"array",get:function(){return this._array}}]),a}(a(Map)));i._RF.pop()}}}));

System.register("chunks:///_virtual/ConfigHelper.ts",["./rollupPluginModLoBabelHelpers.js","cc","./Singleton.ts","./xcore.ts","./TimeUtil.ts"],(function(n){var e,t,o,i,r;return{setters:[function(n){e=n.inheritsLoose},function(n){t=n.cclegacy},function(n){o=n.Singleton},function(n){i=n.xcore},function(n){r=n.default}],execute:function(){t._RF.push({},"6cc12ixdERF0ribS6u4h3y9","ConfigHelper",void 0);n("ConfigHelper",function(n){function t(){return n.apply(this,arguments)||this}e(t,n);var o=t.prototype;return o.getMonsterConfigByType=function(n,e){return void 0===e&&(e=1),i.config["monster.json"].find((function(t){return t.monsterType==n&&t.monsterLevel==e}))},o.getMonsterConfigByJsonId=function(n){return i.config["monster.json"].find((function(e){return e.jsonId==n}))},o.getHeroConfigByJsonId=function(n){return i.config["skin.json"].find((function(e){return e.jsonId==n}))},o.getTowerPointByJsonId=function(n){return i.config["fort.json"].find((function(e){return e.jsonId==n}))},o.getTowerPointByType=function(n,e){return void 0===e&&(e=1),i.config["fort.json"].find((function(t){var o=t.monsterLevel.split("|");return t.monsterType==n&&e>=o[0]&&e<=o[1]}))},o.getTowerPointMaxLev=function(){for(var n=0,e=0;e<i.config["fort.json"].length;e++){var t=i.config["fort.json"][e],o=parseInt(t.monsterLevel.split("|")[1]);if(n>=o)return n;n=o}return n},o.getSkinConfgs=function(){var n=i.config["skin.json"];return n||(n=[]),n},o.getSkinConfigByJsonId=function(n){return i.config["skin.json"].find((function(e){return e.jsonId==n}))},o.getSkinConfigByDebrisId=function(n){return i.config["skin.json"].find((function(e){return e.skinFragment==n}))},o.getSkinJsonIdByContent=function(n){var e=i.config["skin.json"].find((function(e){return e.barrage==n}));return e?e.jsonId:null},o.getSkinLevelConfigBySkinId=function(n,e){return void 0===e&&(e=1),i.config["skinLevel.json"].find((function(t){return t.skinId==n&&t.skin==e}))},o.getSkinMaxLevelBySkinId=function(n){var e=i.config["skinLevel.json"].filter((function(e){return e.skinId==n}));console.log("getSkinMaxLevelBySkinId",n);for(var t=0,o=0;o<e.length;o++){var r=e[o].skin;if(t>=r)return t;t=r}return t},o.getLotteryDebrisConfigsByJsonId=function(n){return i.config["lottery.json"].filter((function(e){return e.lotteryId==n}))},o.getLotteryDebrisConfigByJsonId=function(n,e){return e?i.config["lottery.json"].find((function(t){return t.skinFragmentId==n&&t.lotteryId==e})):i.config["lottery.json"].find((function(e){return e.skinFragmentId==n}))},o.getLotteryConfigByJsonId=function(n){return i.config["lotteryId.json"].find((function(e){return e.jsonId==n}))},o.getWeaponConfigByType=function(n,e){return void 0===e&&(e=1),i.config["weapon.json"].find((function(t){return t.weaponType==n&&t.weaponLevel==e}))},o.getWeaponConfigBySkillJsonId=function(n){return i.config["weapon.json"].find((function(e){return e.skilId==n}))},o.getSkillConfigByType=function(n,e){return void 0===e&&(e=1),i.config["skill.json"].find((function(t){return t.type==n&&t.skillLevel==e}))},o.getSkillConfigByJsonId=function(n){return i.config["skill.json"].find((function(e){return e.jsonId==n}))},o.getSkillConfigsByType=function(n){return i.config["skill.json"].filter((function(e){return e.type==n}))},o.getGreateSkillAbleLevelByNum=function(n,e){if(!n||!e)return 0;var t=0,o=this.getSkillConfigsByType(n).length;if(!o||o.length<=0)return 0;for(var i=0;i<o;i++){var r=this.getSkillConfigByType(n,i+1),f=this.getWeaponConfigBySkillJsonId(r.jsonId);f&&e>=f.douyinGiftNum&&(t+=1)}return t},o.getGreateSkillAbleLevelNeedGiftNum=function(n,e){for(var t,o,i,r=this.getSkillConfigsByType(n).length,f=0;f<r;f++){i=this.getSkillConfigByType(n,f+1);var g=this.getWeaponConfigBySkillJsonId(i.jsonId);if(o=g.douyinGiftNum,0==f&&(t=g.douyinGiftNum),e<o)return[o,i,t]}return[o,i]},o.getBuffConfigByType=function(n,e){return void 0===e&&(e=1),i.config["state.json"].find((function(t){return t.type==n&&t.stateLevel==e}))},o.getBuffConfigByJsonId=function(n){return i.config["state.json"].find((function(e){return e.jsonId==n}))},o.getGiftConfigs=function(){return i.config["gift.json"]},o.getGiftConfigByJsonId=function(n){return i.config["gift.json"].find((function(e){return e.jsonId==n}))},o.getGiftJsonIdByGiftId=function(n){var e=i.config["gift.json"].find((function(e){return e.douyinGiftId==n}));return e?e.jsonId:null},o.getGiftTypeByJsonId=function(n){var e=i.config["gift.json"].find((function(e){return e.jsonId==n}));return e&&e.douyinGiftId?e.giftType:null},o.getGiftJsonIdByType=function(n){var e=i.config["gift.json"].find((function(e){return e.giftType==n}));return e?e.jsonId:null},o.getConstantConfigByKey=function(n){return i.config["constant.json"][n]},o.getDifficultyConfigByJsonId=function(n){return i.config["selectDifficulty.json"].find((function(e){return e.jsonId==n}))},o.getLevelConfigs=function(){return i.config["gameLevel.json"]},o.getLevelConfigByLevel=function(n){return this.getLevelConfigs().find((function(e){return e.gameLevel==n}))},o.getLevelConfigByJsonId=function(n){return this.getLevelConfigs().find((function(e){return e.jsonId==n}))},o.getMonsterRefreshConfigByLevelJsonId=function(n){return i.config["monsterRefresh.json"].filter((function(e){return e.gameLevel>=n}))},o.getDungeonRefreshConfigByLevelJsonId=function(n){return i.config["dungeonRefresh.json"].filter((function(e){return e.dungeonId==n}))},o.getBossConfigStartBy=function(n){for(var e=i.config["monsterRefresh.json"],t=0;t<e.length;t++){var o=e[t];if(n<o.refresh&&1!=o.type)return o}return null},o.getAnimConfigByJsonId=function(n){return i.config["animation.json"].find((function(e){return e.jsonId==n}))},o.getEffectConfigByJsonId=function(n){return i.config["attacKeffect.json"].find((function(e){return e.jsonId==n}))},o.getLinyunKeyByForever=function(){return"rank:"+i.gameData.appId+":"+i.gameData.channelId+":gold:forever"},o.getGiftRankKeyByDay=function(){var n=r.getLastDayByIndex(r.formatTimestampToDate(r.getServerTime(),"-"),6);return"rank:"+i.gameData.appId+":"+i.gameData.channelId+":gift:price:day:"+n},o.getGiftRankKeyByWeek=function(){var n=r.getLastDayByIndex(r.formatTimestampToDate(r.getServerTime(),"-"),6);return"rank:"+i.gameData.appId+":"+i.gameData.channelId+":gift:price:week:"+n},o.getGiftRankKeyByMonth=function(){var n=r.getLastDayByIndex(r.formatTimestampToDate(r.getServerTime(),"-"),6);return"rank:"+i.gameData.appId+":"+i.gameData.channelId+":gift:price:month:"+n},o.getGiftRankKeyByForever=function(){return"rank:"+i.gameData.appId+":"+i.gameData.channelId+":gift:price:forever"},o.getRankKeyByWeek=function(){var n=r.getLastDayByIndex(r.formatTimestampToDate(r.getServerTime(),"-"),6);return"rank:"+i.gameData.appId+":"+i.gameData.channelId+":week:"+n},o.getRankKeyByMonth=function(){var n=r.getFirstDayOfTheMonth();return"rank:"+i.gameData.appId+":"+i.gameData.channelId+":month:"+n},o.getRankKeyByLev=function(){var n=r.getFirstDayOfTheMonth();return"rank:"+i.gameData.appId+":"+i.gameData.channelId+":level:month:"+n},o.getLiverKeyByLev=function(){var n=r.getFirstDayOfTheMonth();return"rank:"+i.gameData.appId+":"+i.gameData.channelId+":anchor:level:month:"+n},o.getBossJoinConfig=function(n){if(n<=0)return null;console.log("getBossJoinConfig",n);for(var e=0;e<i.config["bossAnimation.json"].length;e++){var t=i.config["bossAnimation.json"][e],o=t.sample.split("|");if(n>=o[0]&&n<=o[1])return t}return null},o.getDebriConfigByJsonId=function(n){return i.config["skinFragment.json"].find((function(e){return e.jsonId==n}))},o.getDoorConfigByLev=function(n){for(var e=i.config["door.json"],t=0;t<e.length;t++){var o=e[t],r=o.doorLevel.split("|");if(n>=r[0]&&n<=r[1])return o}return null},o.getBeadConfigs=function(){return i.config["bead.json"]},o.getBeadConfigById=function(n){return i.config["bead.json"].find((function(e){return e.jsonId==n}))},o.getBeadConfigByNum=function(n,e){return i.config["beadLevel.json"].find((function(t){var o=(""+t.beadNum).split("|"),i=Number(o[0]),r=Number(o[1]||o[0]);return t.beadId==n&&e>=i&&e<=r}))},o.getBeadConfigByLev=function(n,e){return i.config["beadLevel.json"].find((function(t){return t.beadId==n&&t.beadLevel==e}))},o.getRankUpScore=function(n,e){var t=i.config["scorePool.json"].find((function(t){return t.poolType==n&&t.ranking==e}));return t?Number(t.proportion):0},o.getAbleSelectLev=function(n){var e=this.getConstantConfigByKey("gameLevelCoordinate");return!e||!!e.split("|").find((function(e){return e==n}))},o.getRankRewardByRankIndex=function(n){return i.config["rankReward.json"].find((function(e){return e.rankMax>=n&&e.rankMin<=n}))},o.getWingConfigByJsonId=function(n){return i.config["wing.json"].find((function(e){return e.jsonId==n}))},o.getWingConfigByRankIndex=function(n){var e=this.getRankRewardByRankIndex(n);return e&&1==e.rewardType&&e.rewardId?this.getWingConfigByJsonId(e.rewardId):null},o.getWingSkinConfigByJsonId=function(n){return i.config["wingSkill.json"].find((function(e){return e.jsonId==n}))},o.getWingSkinConfigByRankIndex=function(n){var e=this.getRankRewardByRankIndex(n);if(e&&1==e.rewardType&&e.rewardId){var t=this.getWingConfigByJsonId(e.rewardId);if(t&&t.wingSkillId)return this.getWingSkinConfigByJsonId(t.wingSkillId)}return null},o.getTaskConfigByType=function(n){return i.config["task.json"].find((function(e){return e.type==n}))},o.getGiftRankTitleByScore=function(n){return i.config["giftLevel.json"].find((function(e){return e.giftScoreMin<=n&&e.giftScoreMax>=n}))},o.getDungeonConfigs=function(){return i.config["dungeon.json"]},o.getDungeonConfigByJsonId=function(n){return i.config["dungeon.json"].find((function(e){return e.jsonId==n}))},o.getDungeonExchangeConfigByJsonId=function(n){return i.config["dungeonExchange.json"].find((function(e){return e.dungeonId==n}))},o.getExchaengConfigByNameContent=function(n){return i.config["exchange.json"].find((function(e){return e.name==n}))},o.getExchangeConfigs=function(){return i.config["exchange.json"]},o.getExchangeTypeConfigs=function(){return i.config["exchangeType.json"]},o.getExchangeConfigsByJsonId=function(n){return i.config["exchange.json"].filter((function(e){return e.exchangeTypeId==n}))},t}(o));t._RF.pop()}}}));

System.register("chunks:///_virtual/ConstGlobal.ts",["cc"],(function(e){var i;return{setters:[function(e){i=e.cclegacy}],execute:function(){var t,n,a,o;i._RF.push({},"e4117tlNrlCbaNddUpG83g7","ConstGlobal",void 0);e("E_UILAYER",function(e){return e[e.BG_LAYER=1]="BG_LAYER",e[e.CONTENT_LAYER=10]="CONTENT_LAYER",e[e.UI_LAYER=15]="UI_LAYER",e[e.VIEW_LAYER=20]="VIEW_LAYER",e[e.LOADING_LAYER=25]="LOADING_LAYER",e[e.TIPS_LAYER=30]="TIPS_LAYER",e[e.GUIDE_LAYER=35]="GUIDE_LAYER",e[e.TOAST_LAVER=40]="TOAST_LAVER",e}({})),e("E_EVENT",function(e){return e.GameShow="GameShow",e.GameHide="GameHide",e.StroageData="StroageData",e.UiClose="UiClose",e.ShowGuide="ShowGuide",e.HideGuide="HideGuide",e.BaseInfo="BaseInfo",e.RankInfo="RankInfo",e.GameMode="GameMode",e.GameSelectLev="GameSelectLev",e.GameLogin="GameLogin",e.GameBack="GameBack",e.GameStart="GameStart",e.GameOver="GameOver",e.GameReplay="GameReplay",e.GameOut="GameOut",e.GameResume="GameResume",e.CrateMoreMonster="CrateMoreMonster",e.Gift="Gift",e.SkillEffect="SkillEffect",e.HeroAtkEffect="HeroAtkEffect",e.MonsterAtkEffect="MonsterAtkEffect",e.HurtEffect="HurtEffect",e.MonsterDead="MonsterDead",e.NewsEffect="NewsEffect",e.SkillTips="SkillTips",e.GameConfig="GameConfig",e.HurtTips="HurtTips",e.Round="Round",e.NextGameLevel="NextGameLevel",e.GameScore="GameScore",e.GiftMessage="GiftMessage",e.GraphicsEffectRange="GraphicsEffectRange",e.GraphicsEffectRange2="GraphicsEffectRange2",e.ShakeCam="ShakeCam",e.RefreshList="RefreshList",e}({})),e("E_GiftMessageType",function(e){return e[e.Gift=0]="Gift",e[e.GetSmallSkill=1]="GetSmallSkill",e[e.GetBigSkill=2]="GetBigSkill",e[e.UseProp=3]="UseProp",e[e.Kill=4]="Kill",e[e.RankBoss=5]="RankBoss",e[e.SkillUp=6]="SkillUp",e[e.UserInfo=7]="UserInfo",e[e.SkinReward=8]="SkinReward",e[e.TaskReward=9]="TaskReward",e}({})),e("E_GameState",function(e){return e[e.None=0]="None",e[e.Stop=1]="Stop",e[e.Pause=2]="Pause",e[e.Resume=3]="Resume",e[e.Gameing=4]="Gameing",e}({}));var r=e("C_GiftKey",{JoinHero:"1",JoinMonster:"2",Like:"300001",SixSix:"300002",Gift01:"300003",Gift02:"300004",Gift03:"300005",Gift04:"300006",Gift05:"300007",Gift06:"300008",Skin1:"170001",Skin2:"170002",Skin3:"170003",Skin4:"170004",Skin5:"170005",Skin6:"170006",Skin7:"170007",Skin8:"171001",Skin9:"171002",Skin10:"171003",Skin11:"171004",Skin12:"171005",Skin13:"171006",Skin14:"171007",Skin15:"171008",Skin16:"171009",Skin17:"171010",Skin18:"171011",Skin19:"171012",Skin20:"171013",Skin21:"171014",Skin22:"171015",Skin23:"171016",Skin24:"171017",Skin25:"171018",MoveLeft:"ml",MoveRight:"mr",Baozhu:"baozhu",ExchangeBossFight:"exchangebossfight",ExchangeWing:"exchangewing"}),S=(e("E_SkinType",function(e){return e.Skin1="170001",e.Skin2="170002",e.Skin3="170003",e.Skin4="170004",e.Skin5="170005",e.Skin6="170006",e.Skin7="170007",e.Skin8="171001",e.Skin9="171002",e.Skin10="171003",e.Skin11="171004",e.Skin12="171005",e.Skin13="171006",e.Skin14="171007",e.Skin15="171008",e.Skin16="171009",e.Skin17="171010",e.Skin18="171011",e.Skin19="171012",e.Skin20="171013",e.Skin21="171014",e.Skin22="171015",e.Skin23="171016",e.Skin24="171017",e.Skin25="171018",e}({})),e("E_SkinUnlockType",function(e){return e[e.Score=1]="Score",e[e.Debris=2]="Debris",e}({})),e("E_SkinLayer",function(e){return e[e.Normal=1]="Normal",e[e.Good=2]="Good",e[e.Legend=3]="Legend",e[e.Epic=4]="Epic",e}({})),e("E_SkillType",function(e){return e.Attack="1",e.GreatAttack="2",e.Fire="3",e.GreatFire="203",e.Lightning="4",e.GreatLightning="204",e.Rock="5",e.GreatRock="205",e.Fires="7",e.GreatFires="207",e.Invincible="6",e.AddHp="9",e.SkyRock="8",e.GreatSkyRock="208",e.MoveSpeed="101",e.SkillSpeed="102",e.Dizziness="103",e.Crazy="104",e.Weak="105",e.CreateMore="106",e.LineAttack="107",e.MoveSpeed2="401",e.SkillSpeed2="402",e.Dizziness2="403",e.Crazy2="404",e.Weak2="405",e.CreateMore2="406",e}({}))),k=(e("C_GiftSkill",["1","2","3","203","4","204","5","205","7","207","6","9","8","208","101","102","103","104","105","106","107"]),e("C_GreatSkill",((t={})[""+S.Attack]=S.GreatAttack,t[""+S.Fire]=S.GreatFire,t[""+S.Lightning]=S.GreatLightning,t[""+S.Rock]=S.GreatRock,t[""+S.Fires]=S.GreatFires,t[""+S.SkyRock]=S.GreatSkyRock,t)),e("C_HeroGiftKeyToSkillType",((n={})[""+r.Like]=S.Attack,n[""+r.SixSix]=S.Attack,n[""+r.Gift01]=S.Fire,n[""+r.Gift02]=S.Lightning,n[""+r.Gift03]=S.Rock,n[""+r.Gift04]=S.AddHp,n[""+r.Gift05]=S.Fires,n[""+r.Gift06]=S.SkyRock,n)),e("C_MonsterGiftKeyToSkillType",((a={})[""+r.Like]=S.Attack,a[""+r.SixSix]=S.Attack,a[""+r.Gift01]=S.Fire,a[""+r.Gift02]=S.Lightning,a[""+r.Gift03]=S.Rock,a[""+r.Gift04]=S.AddHp,a[""+r.Gift05]=S.Fires,a[""+r.Gift06]=S.SkyRock,a)),e("C_TowerPointGiftKey",r.Gift01),e("E_TowerPointType",function(e){return e.Point1="1",e.Point2="2",e.Point3="3",e.Point4="4",e}({})),e("E_MonsterType",function(e){return e.Bat="1",e.GreatBat="1001",e.Zuanfeng="2",e.GreateZuanfeng="1002",e.Child="3",e.GreatChild="1003",e.Pig="4",e.GreatPig="1004",e.Eagle="5",e.GreatEagle="1005",e.Monkey="6",e.GreatMonkey="1006",e.Cattle="7",e.GreatCattle="1007",e.Baize="2021",e.Huodou="2022",e.Jiuwei="2023",e.Qiongqi="2024",e.Suanni="2025",e.Taotie="2026",e}({})));e("E_MonsterTag",function(e){return e[e.normal=1]="normal",e[e.smallboss=2]="smallboss",e[e.bigboss=3]="bigboss",e}({})),e("C_GreatMonster",((o={})[""+k.Bat]=k.GreatBat,o[""+k.Zuanfeng]=k.GreateZuanfeng,o[""+k.Child]=k.GreatChild,o[""+k.Pig]=k.GreatPig,o[""+k.Eagle]=k.GreatEagle,o[""+k.Monkey]=k.GreatMonkey,o[""+k.Cattle]=k.GreatCattle,o)),e("E_BuffType",function(e){return e[e.Fire=1]="Fire",e[e.MoveSpeed=2]="MoveSpeed",e[e.Dizziness=3]="Dizziness",e[e.AttackNum=4]="AttackNum",e[e.AttackSpeed=5]="AttackSpeed",e[e.SkillSpeed=6]="SkillSpeed",e[e.HurtWeak=7]="HurtWeak",e}({})),e("E_RoleState",function(e){return e[e.None=0]="None",e[e.Idle=1]="Idle",e[e.Move=2]="Move",e[e.Attack=3]="Attack",e[e.Skill=4]="Skill",e[e.Dizziness=5]="Dizziness",e[e.Hurt=6]="Hurt",e[e.WaitRelive=7]="WaitRelive",e[e.Dead=8]="Dead",e}({})),e("E_RoleType",function(e){return e.Hero="Hero",e.Monster="Monster",e.Tower="Tower",e.MonsterPoint="MonsterPoint",e.TowerPoint="TowerPoint",e}({})),e("E_AtkType",function(e){return e[e.Skill=0]="Skill",e[e.Single=1]="Single",e[e.Multiple=2]="Multiple",e[e.Aoe=3]="Aoe",e[e.Jump=4]="Jump",e}({})),e("E_AtkStyle",function(e){return e[e.Near=1]="Near",e[e.LongDistance=2]="LongDistance",e[e.Multiple=5]="Multiple",e}({})),e("E_JumpType",function(e){return e.h5="h5",e.MiniProgram="MiniProgram",e}({})),e("C_Runtime",{browser_wx:"browser_wx",program_wx:"program_wx",other:"other"}),e("E_TaskType",function(e){return e[e.login=1]="login",e[e.invite=2]="invite",e[e.share=3]="share",e[e.gameAim=4]="gameAim",e[e.jumpInside=5]="jumpInside",e[e.jumpOutside=6]="jumpOutside",e}({})),e("C_Bundle",{abMain:"abMain",abGame:"abGame"}),e("E_GameMode",function(e){return e[e.Easy=0]="Easy",e[e.Middle=1]="Middle",e[e.Diffecult=2]="Diffecult",e}({})),e("E_GameType",function(e){return e[e.Day=0]="Day",e[e.Week=1]="Week",e[e.Month=2]="Month",e}({})),e("C_Scene",{Ready:"Ready",Main:"Main",Game:"Game"}),e("C_View",{ViewLogin:"ViewLogin",ViewMatch:"ViewMatch",ViewGameOver:"ViewGameOver",ViewTestGift:"ViewTestGift",ViewSelectGameLevel:"ViewSelectGameLevel",ViewSelectGameMode:"ViewSelectGameMode",ViewGameSetting:"ViewGameSetting",ViewGameSound:"ViewGameSound",ViewFightRank:"ViewFightRank",ViewRank:"ViewRank",ViewSkinSelect:"ViewSkinSelect",ViewRoundToast:"ViewRoundToast",ViewSetting:"ViewSetting",ViewCommonVideo:"ViewCommonVideo",ViewBossMsg:"ViewBossMsg",ViewCommonTips:"ViewCommonTips",ViewSkinShow:"ViewSkinShow",ViewOpenBox:"ViewOpenBox",ViewSkinDebrisReward:"ViewSkinDebrisReward",ViewSkinReward:"ViewSkinReward",ViewSkinDetail:"ViewSkinDetail",ViewPowerRank:"ViewPowerRank",ViewTowerLevUp:"ViewTowerLevUp",ViewCrossRewardDesc:"ViewCrossRewardDesc",ViewUserInfo:"ViewUserInfo",ViewDimondReward:"ViewDimondReward",ViewWingShow:"ViewWingShow",ViewTaskRewardDesc:"ViewTaskRewardDesc",ViewSelectGameType:"ViewSelectGameType",ViewExchange:"ViewExchange",ViewExchangeShow:"ViewExchangeShow"}),e("E_Channel",function(e){return e.TIKTOK="TIKTOK",e.UHO="UHO",e.GAME560="GAME560",e}({}));i._RF.pop()}}}));

System.register("chunks:///_virtual/Data.ts",["cc","./ConstGlobal.ts","./EventManager2.ts"],(function(t){var n,e,o,a;return{setters:[function(t){n=t.cclegacy},function(t){e=t.C_Runtime,o=t.E_EVENT},function(t){a=t.EventManager}],execute:function(){n._RF.push({},"6f0307Vg/tKE6FLZEz/lE/n","Data",void 0);t("default",new(function(){function t(){this.appId=void 0,this.token=void 0,this.runtime=e.other,this.combatId=null,this.baseInfo=null,this.channelId=void 0,this.gameJoinTime=void 0,this.soundData={},this.giftData={},this.userName=void 0,this.password=void 0,this.giftInfo=null,this.query=void 0,this.cospath=void 0,this.gameType=0,this.gameTypeJsonId=null,this.gameMode=0,this.gameSelectLev=0,this.gameLev=0,this.oldRankInfo=void 0,this.scoreRankInfo=[],this.roundRankInfo=[],this.liverRankInfo=[],this.giftRankInfo=[],this.playersOperation={},this.animationClips=null,this.cospath="https://gz-cdn-1258783731.cos.ap-guangzhou.myqcloud.com/2024/livegametower/dev/";var n=[{key:"baseInfo",boardcast:o.BaseInfo},{key:"scoreRankInfo",boardcast:o.RankInfo},{key:"roundRankInfo",boardcast:o.RankInfo},{key:"liverRankInfo",boardcast:o.RankInfo},{key:"giftRankInfo",boardcast:o.RankInfo}];t.listenDataChange(this,n)}return t.listenDataChange=function(t,n){n.forEach((function(n){Object.defineProperty(t,n.key,{get:function(){return t._store=t._store||{},t._store[n.key]},set:function(e){if(t._store=t._store||{},n.modifyFunc){var o=n.modifyFunc(e,t._store[n.key]);o&&(e=o)}t._store[n.key]=e,n.boardcast&&a.getInstance().raiseEvent(n.boardcast,e)}})}))},t}()));n._RF.pop()}}}));

System.register("chunks:///_virtual/dataTrack.ts",["cc"],(function(t){var c;return{setters:[function(t){c=t.cclegacy}],execute:function(){c._RF.push({},"f3c55ACVndE5Yu/YiYPsFZV","dataTrack",void 0);t("projectName","tanyuShakeH5");c._RF.pop()}}}));

System.register("chunks:///_virtual/decorator.ts",["cc"],(function(t){var n,r,e;return{setters:[function(t){n=t.cclegacy,r=t.Node,e=t.js}],execute:function(){function i(t){for(var n,r=arguments.length,e=new Array(r>1?r-1:0),i=1;i<r;i++)e[i-1]=arguments[i];(n=console).warn.apply(n,["[decorators] [warn] "+t].concat(e))}function u(t,n){if(!t||!n)return null;var r=t.children;if(r.length){for(var e=0;e<r.length;e++)if(!1===n(r[e]))return!1;for(var i=0;i<r.length;i++)if(!1===u(r[i],n))return!1}}function o(t,n,e,i){if(!t||!n)return[];var o=t instanceof r?t:t.node;if(!o.children.length)return[];var l=null,a=[];return u(o,(function(t){if((l=t.name===n&&(e&&e!==r?t.getComponent(e):t))&&(a.push(l),!i))return!1})),a}function l(t){return"Array"===function(t){return toString.call(t).slice(8,-1)}(t)}function a(t){return null!==t&&"object"==typeof t&&!l(t)}function c(t){return l(t)?!t.length:null==t}t("autoProperty",s),n._RF.push({},"af5bcDuREtFr76rwZ/IzAcQ","decorator",void 0);var f="_$auto_property_attr_cache$";function h(){for(var t=arguments.length,n=new Array(t),e=0;e<t;e++)n[e]=arguments[e];n[0];var u=n[1],l=n[2],a=void 0===l?{}:l,h=n[3],s=void 0===h?r:h,v=n[4],d=n[5],g=void 0!==d&&d,p=a.get,y=a.set,b=!!p&&!!y,m={configurable:a.configurable,enumerable:a.enumerable,get:function(){var t="$"+u;if(b)return p.call(this);if(this[f]&&!c(this[f][t]))return this[f][t];var n=o(v?"function"==typeof v?v.call(this,this):this[v]:this.node,t,s,g);0===n.length&&i("[autoProperty] ["+this.node.name+"] 未查询到名字为{"+t+"}并带有{"+(s.name||s)+"}组件的节点");var r=g?n:n[0]||null;if(n.length){if(b)return y.call(this,r),p.call(this);this[f]||Object.defineProperty(this,f,{value:{}}),this[f][t]=r}return r},set:function(t){b?y.call(this,t):(this[f]||Object.defineProperty(this,f,{value:{}}),this[f]["$"+u]=t)}};return m}function s(){if(arguments.length<=1){var t=arguments.length<=0?void 0:arguments[0],n=null,e=null,i=!1;return a(t)?(n=t.type,e=t.parent):n=t,l(n)&&(n=n[0]||r,i=!0),function(t,r,u){return h(t,r,u,n,e,i)}}return h.apply(void 0,arguments)}s.delCache=function(t,n){t&&t[f]&&(n?t[f]&&delete t[f]["$"+n]:e.clear(t[f]))},n._RF.pop()}}}));

System.register("chunks:///_virtual/Effect2DFollow3D.ts",["./rollupPluginModLoBabelHelpers.js","cc","./MathUtil.ts","./xcore.ts"],(function(t){var e,o,i,n,r,a,s,c,l,u,d;return{setters:[function(t){e=t.applyDecoratedDescriptor,o=t.inheritsLoose,i=t.initializerDefineProperty,n=t.assertThisInitialized},function(t){r=t.cclegacy,a=t._decorator,s=t.Node,c=t.Vec3,l=t.Component},function(t){u=t.MathUtil},function(t){d=t.xcore}],execute:function(){var f,p,h,m,y,w,b,v;r._RF.push({},"df371w5yONHX5BKsO27bl3V","Effect2DFollow3D",void 0);var D=a.ccclass,z=a.property;t("Effect2DFollow3D",(f=D("Effect2DFollow3D"),p=z({type:s}),h=z({type:s}),f((w=e((y=function(t){function e(){for(var e,o=arguments.length,r=new Array(o),a=0;a<o;a++)r[a]=arguments[a];return e=t.call.apply(t,[this].concat(r))||this,i(e,"node3d",w,n(e)),i(e,"nodeUi",b,n(e)),i(e,"distance",v,n(e)),e.camera=null,e.pos=new c,e}o(e,t);var r=e.prototype;return r.setTarget=function(t){this.node3d=t},r.start=function(){var t=this.zoom();this.node.setScale(t,t,1)},r.lateUpdate=function(t){var e=this.zoom();e=u.lerp(this.node.scale.x,e,.1),this.node.setScale(e,e,1)},r.zoom=function(){this.camera.convertToUINode(this.node3d.worldPosition,d.ndUI,this.pos),this.nodeUi.setPosition(this.pos),c.transformMat4(this.pos,this.node3d.worldPosition,this.camera._camera.matView);var t=this.distance/Math.abs(this.pos.z);return Math.floor(100*t)/100},e}(l)).prototype,"node3d",[p],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),b=e(y.prototype,"nodeUi",[h],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),v=e(y.prototype,"distance",[z],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 10}}),m=y))||m));r._RF.pop()}}}));

System.register("chunks:///_virtual/ElectronAPI.ts",["./rollupPluginModLoBabelHelpers.js","cc"],(function(e){var n,r,t,i;return{setters:[function(e){n=e.inheritsLoose},function(e){r=e.cclegacy,t=e._decorator,i=e.Component}],execute:function(){var o;r._RF.push({},"e0c4dNtOPRAbqJ78h+Ep8Ro","ElectronAPI",void 0);var c=t.ccclass,s=(t.property,window.electron);e("default",c("ElectronAPI")(o=function(e){function r(){return e.apply(this,arguments)||this}return n(r,e),r.center=function(){s&&s.ipcRenderer.send("e_center")},r.fullScreen=function(){s&&s.ipcRenderer.send("e_fullScreen")},r.window=function(){s&&s.ipcRenderer.send("e_window")},r.openDevTools=function(){s&&s.ipcRenderer.send("e_openDevTools")},r.closeDevTools=function(){s&&s.ipcRenderer.send("e_closeDevTools")},r.setSize=function(e,n){s&&s.ipcRenderer.send("e_setSize",e.toString(),n.toString())},r.setResolution=function(e,n){s&&s.ipcRenderer.send("e_setResolution",e.toString(),n.toString())},r.isFullScreen=function(){if(s)return s.ipcRenderer.sendSync("e_isFullScreen")},r.setScreenResolution=function(e,n){s&&(1==this.isFullScreen()?this.setResolution(e,n):this.setSize(e,n))},r.nircmdUD=function(e){s&&s.ipcRenderer.send("e_nircmdUD",e)},r.getMassage=function(){if(s)return s.ipcRenderer.sendSync("e_getMassage")},r.getQuery=function(){return s?s.ipcRenderer.sendSync("e_sendquery"):""},r.getAllResolutions=function(){if(s)return s.ipcRenderer.sendSync("e_getAllResolutions")},r.getCurrentResolution=function(){if(s)return s.ipcRenderer.sendSync("e_getCurrentResolution")},r.quit=function(){s&&s.ipcRenderer.send("e_quit")},r.readFile=function(e){if(s)return s.ipcRenderer.sendSync("e_readFile",e)},r.writeFile=function(e,n){s&&s.ipcRenderer.send("e_writeFile",e,n)},r.log=function(e,n){s&&s.ipcRenderer.send("e_logErr",e,n)},r}(i))||o);r._RF.pop()}}}));

System.register("chunks:///_virtual/ErrorCode.ts",["cc"],(function(r){var e;return{setters:[function(r){e=r.cclegacy}],execute:function(){e._RF.push({},"6d952vNNiZMMakasVvHrosI","ErrorCode",void 0);r("GameErrorCode",{1e3:"用户名不存在",1001:"密码错误",1002:"用户id不存在",1003:"登录信息过期,请重新登录",1004:"账号无权限,无法操作",1005:"房间初始化错误",1006:"活动房间不存在,请稍后再试",1007:"房间人数已满",1008:"游戏未开始",1100:"不能加入自己的房间",1101:"不能加入自己的房间",1102:"房间信息不存在",1103:"房间信息状态错误",1104:"房间信息已失效",1201:"您不是房主，无权限开始游戏！",1202:"游戏已开始！",1203:"游戏已结束！"});e._RF.pop()}}}));

System.register("chunks:///_virtual/EventDispatcher.ts",["cc"],(function(t){var e;return{setters:[function(t){e=t.cclegacy}],execute:function(){e._RF.push({},"7df89jyt9pGWpjiRgbnHAtm","EventDispatcher",void 0);t("default",function(){function t(){this._listeners={}}var e=t.prototype;return e.once=function(t,e,i,n){this.on(t,e,i,n,"once")},e.on=function(t,e,i,n,r){void 0===this._listeners&&(this._listeners={});var s=this._listeners;void 0===s[t]&&(s[t]=[]),-1===s[t].findIndex((function(t){return t.listener===e&&(!i||t.target===i)}))&&s[t].push({emitTimes:r||"more",listener:e,target:i,args:n||[]})},e.hasEventListener=function(t,e,i){if(void 0===this._listeners)return!1;var n=this._listeners;return void 0!==n[t]&&-1!==n[t].findIndex((function(t){return t.listener===e&&(!i||t.target===i)}))},e.off=function(t,e,i){if(void 0!==this._listeners){var n=this._listeners[t];if(void 0!==n){var r=n.findIndex((function(t){return t.listener===e&&(!i||t.target===i)}));-1!==r&&n.splice(r,1)}}},e.emit=function(t,e){if(void 0!==this._listeners){var i=this._listeners[t];if(void 0!==i){for(var n=i.slice(0),r=[],s=0,o=n.length;s<o;s++)n[s].listener.apply(n[s].target||this,[].concat(n[s].args,e||[])),"once"===n[s].emitTimes&&r.push(n[s]);for(var c=0,f=r.length;c<f;c++){var u=r[c],a=u.listener,l=u.target;console.log("once"+t),this.off(t,a,l)}}}},t}());e._RF.pop()}}}));

System.register("chunks:///_virtual/EventManager.ts",["./rollupPluginModLoBabelHelpers.js","cc"],(function(n){var t,e,i,o,s,r,l;return{setters:[function(n){t=n.inheritsLoose,e=n.createClass},function(n){i=n.cclegacy,o=n._decorator,s=n.Node,r=n.director,l=n.Component}],execute:function(){var a,u;i._RF.push({},"4c232CxWzBHf5X30YE4hekq","EventManager",void 0);var c=o.ccclass;n("EventManager",c("EventManager")(((u=function(n){function i(){return n.apply(this,arguments)||this}t(i,n);var o=i.prototype;return o.on=function(n,t,e){var i;null!=this&&null!=(i=this.node)&&i.isValid&&this.node.on(n,t,e)},o.off=function(n,t,e){var i,o;null!=this&&null!=(i=this.node)&&i.isValid&&(null==(o=this.node)||o.off(n,t,e))},o.once=function(n,t,e){var i;null!=this&&null!=(i=this.node)&&i.isValid&&this.node.once(n,t,e)},o.emit=function(n){var t;if(null!=this&&null!=(t=this.node)&&t.isValid){for(var e=arguments.length,i=new Array(e>1?e-1:0),o=1;o<e;o++)i[o-1]=arguments[o];this.node.emit(n,i)}},e(i,null,[{key:"inst",get:function(){return null==this._inst&&(this._inst=(new s).addComponent(i),r.addPersistRootNode(this._inst.node)),this._inst}}]),i}(l))._inst=void 0,a=u))||a);i._RF.pop()}}}));

System.register("chunks:///_virtual/EventManager2.ts",["./rollupPluginModLoBabelHelpers.js","cc","./Singleton.ts"],(function(e){var t,n,r,s,i,a,c,l;return{setters:[function(e){t=e.inheritsLoose,n=e.asyncToGenerator,r=e.regeneratorRuntime,s=e.createForOfIteratorHelperLoose},function(e){i=e.cclegacy,a=e.warn,c=e.log},function(e){l=e.Singleton}],execute:function(){i._RF.push({},"ee052oZcnJG0rkBRnNK+41+","EventManager",void 0);e("EventManager",function(e){function i(){for(var t,n=arguments.length,r=new Array(n),s=0;s<n;s++)r[s]=arguments[s];return(t=e.call.apply(e,[this].concat(r))||this)._eventListeners={},t}t(i,e);var l=i.prototype;return l.getEventListenersIndex=function(e,t,n){for(var r=-1,s=0;s<this._eventListeners[e].length;s++){var i=this._eventListeners[e][s];if(i.callBack==t&&(!n||i.target==n)){r=s;break}}return r},l.addEventListener=function(e,t,n){if(e){if(null==t)return c("addEventListener callBack is nil"),!1;var r={callBack:t,target:n};if(null==this._eventListeners[e])this._eventListeners[e]=[r];else-1==this.getEventListenersIndex(e,t,n)&&this._eventListeners[e].push(r);return!0}a("eventName is empty"+e)},l.setEventListener=function(e,t,n){if(e){if(null==t)return c("setEventListener callBack is nil"),!1;var r={callBack:t,target:n};return this._eventListeners[e]=[r],!0}a("eventName is empty"+e)},l.removeEventListener=function(e,t,n){if(null!=this._eventListeners[e]){var r=this.getEventListenersIndex(e,t,n);-1!=r&&this._eventListeners[e].splice(r,1)}},l.raiseEvent=function(){var e=n(r().mark((function e(t,n){var i,a,c,l,o,u,v;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(null==this._eventListeners[t]){e.next=11;break}for(i=[],a=s(this._eventListeners[t]);!(c=a()).done;)l=c.value,i.push({callBack:l.callBack,target:l.target});o=0,u=i;case 4:if(!(o<u.length)){e.next=11;break}return v=u[o],e.next=8,v.callBack.call(v.target,t,n);case 8:o++,e.next=4;break;case 11:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}(),i}(l));i._RF.pop()}}}));

System.register("chunks:///_virtual/ExternalMessage.ts",["./rollupPluginModLoBabelHelpers.js","cc","./index.js","./minimal.mjs_cjs=&original=.js","./minimal.js"],(function(e){var n,r,t,a;return{setters:[function(e){n=e.createForOfIteratorHelperLoose},function(e){r=e.cclegacy},function(e){t=e.default},null,function(e){a=e.default}],execute:function(){r._RF.push({},"7b407nKqjBD1opxFE+0I/eE","ExternalMessage",void 0);e("protobufPackage","com.iohao.message");function u(){return{cmdCode:0,protocolSwitch:0,cmdMerge:0,responseStatus:0,validMsg:"",data:new Uint8Array(0)}}var o=e("ExternalMessage",{encode:function(e,n){return void 0===n&&(n=a.Writer.create()),0!==e.cmdCode&&n.uint32(8).int32(e.cmdCode),0!==e.protocolSwitch&&n.uint32(16).int32(e.protocolSwitch),0!==e.cmdMerge&&n.uint32(24).int32(e.cmdMerge),0!==e.responseStatus&&n.uint32(32).sint32(e.responseStatus),""!==e.validMsg&&n.uint32(42).string(e.validMsg),0!==e.data.length&&n.uint32(50).bytes(e.data),n},decode:function(e,n){for(var r=e instanceof a.Reader?e:a.Reader.create(e),t=void 0===n?r.len:r.pos+n,o=u();r.pos<t;){var i=r.uint32();switch(i>>>3){case 1:if(8!==i)break;o.cmdCode=r.int32();continue;case 2:if(16!==i)break;o.protocolSwitch=r.int32();continue;case 3:if(24!==i)break;o.cmdMerge=r.int32();continue;case 4:if(32!==i)break;o.responseStatus=r.sint32();continue;case 5:if(42!==i)break;o.validMsg=r.string();continue;case 6:if(50!==i)break;o.data=r.bytes();continue}if(4==(7&i)||0===i)break;r.skipType(7&i)}return o},fromJSON:function(e){return{cmdCode:N(e.cmdCode)?Number(e.cmdCode):0,protocolSwitch:N(e.protocolSwitch)?Number(e.protocolSwitch):0,cmdMerge:N(e.cmdMerge)?Number(e.cmdMerge):0,responseStatus:N(e.responseStatus)?Number(e.responseStatus):0,validMsg:N(e.validMsg)?String(e.validMsg):"",data:N(e.data)?S(e.data):new Uint8Array(0)}},toJSON:function(e){var n={};return void 0!==e.cmdCode&&(n.cmdCode=Math.round(e.cmdCode)),void 0!==e.protocolSwitch&&(n.protocolSwitch=Math.round(e.protocolSwitch)),void 0!==e.cmdMerge&&(n.cmdMerge=Math.round(e.cmdMerge)),void 0!==e.responseStatus&&(n.responseStatus=Math.round(e.responseStatus)),void 0!==e.validMsg&&(n.validMsg=e.validMsg),void 0!==e.data&&(n.data=function(e){if(V.Buffer)return V.Buffer.from(e).toString("base64");var n=[];return e.forEach((function(e){n.push(String.fromCharCode(e))})),V.btoa(n.join(""))}(void 0!==e.data?e.data:new Uint8Array(0))),n},create:function(e){return o.fromPartial(null!=e?e:{})},fromPartial:function(e){var n,r,t,a,o,i,l=u();return l.cmdCode=null!=(n=e.cmdCode)?n:0,l.protocolSwitch=null!=(r=e.protocolSwitch)?r:0,l.cmdMerge=null!=(t=e.cmdMerge)?t:0,l.responseStatus=null!=(a=e.responseStatus)?a:0,l.validMsg=null!=(o=e.validMsg)?o:"",l.data=null!=(i=e.data)?i:new Uint8Array(0),l}});var i=e("IntValue",{encode:function(e,n){return void 0===n&&(n=a.Writer.create()),0!==e.value&&n.uint32(8).sint32(e.value),n},decode:function(e,n){for(var r=e instanceof a.Reader?e:a.Reader.create(e),t=void 0===n?r.len:r.pos+n,u={value:0};r.pos<t;){var o=r.uint32();switch(o>>>3){case 1:if(8!==o)break;u.value=r.sint32();continue}if(4==(7&o)||0===o)break;r.skipType(7&o)}return u},fromJSON:function(e){return{value:N(e.value)?Number(e.value):0}},toJSON:function(e){var n={};return void 0!==e.value&&(n.value=Math.round(e.value)),n},create:function(e){return i.fromPartial(null!=e?e:{})},fromPartial:function(e){var n,r={value:0};return r.value=null!=(n=e.value)?n:0,r}});var l=e("IntValueList",{encode:function(e,r){void 0===r&&(r=a.Writer.create()),r.uint32(10).fork();for(var t,u=n(e.values);!(t=u()).done;){var o=t.value;r.sint32(o)}return r.ldelim(),r},decode:function(e,n){for(var r=e instanceof a.Reader?e:a.Reader.create(e),t=void 0===n?r.len:r.pos+n,u={values:[]};r.pos<t;){var o=r.uint32();switch(o>>>3){case 1:if(8===o){u.values.push(r.sint32());continue}if(10===o){for(var i=r.uint32()+r.pos;r.pos<i;)u.values.push(r.sint32());continue}}if(4==(7&o)||0===o)break;r.skipType(7&o)}return u},fromJSON:function(e){return{values:Array.isArray(null==e?void 0:e.values)?e.values.map((function(e){return Number(e)})):[]}},toJSON:function(e){var n={};return e.values?n.values=e.values.map((function(e){return Math.round(e)})):n.values=[],n},create:function(e){return l.fromPartial(null!=e?e:{})},fromPartial:function(e){var n,r={values:[]};return r.values=(null==(n=e.values)?void 0:n.map((function(e){return e})))||[],r}});var s=e("LongValue",{encode:function(e,n){return void 0===n&&(n=a.Writer.create()),0!==e.value&&n.uint32(8).sint64(e.value),n},decode:function(e,n){for(var r=e instanceof a.Reader?e:a.Reader.create(e),t=void 0===n?r.len:r.pos+n,u={value:0};r.pos<t;){var o=r.uint32();switch(o>>>3){case 1:if(8!==o)break;u.value=k(r.sint64());continue}if(4==(7&o)||0===o)break;r.skipType(7&o)}return u},fromJSON:function(e){return{value:N(e.value)?Number(e.value):0}},toJSON:function(e){var n={};return void 0!==e.value&&(n.value=Math.round(e.value)),n},create:function(e){return s.fromPartial(null!=e?e:{})},fromPartial:function(e){var n,r={value:0};return r.value=null!=(n=e.value)?n:0,r}});var c=e("LongValueList",{encode:function(e,r){void 0===r&&(r=a.Writer.create()),r.uint32(10).fork();for(var t,u=n(e.values);!(t=u()).done;){var o=t.value;r.sint64(o)}return r.ldelim(),r},decode:function(e,n){for(var r=e instanceof a.Reader?e:a.Reader.create(e),t=void 0===n?r.len:r.pos+n,u={values:[]};r.pos<t;){var o=r.uint32();switch(o>>>3){case 1:if(8===o){u.values.push(k(r.sint64()));continue}if(10===o){for(var i=r.uint32()+r.pos;r.pos<i;)u.values.push(k(r.sint64()));continue}}if(4==(7&o)||0===o)break;r.skipType(7&o)}return u},fromJSON:function(e){return{values:Array.isArray(null==e?void 0:e.values)?e.values.map((function(e){return Number(e)})):[]}},toJSON:function(e){var n={};return e.values?n.values=e.values.map((function(e){return Math.round(e)})):n.values=[],n},create:function(e){return c.fromPartial(null!=e?e:{})},fromPartial:function(e){var n,r={values:[]};return r.values=(null==(n=e.values)?void 0:n.map((function(e){return e})))||[],r}});var v=e("StringValue",{encode:function(e,n){return void 0===n&&(n=a.Writer.create()),""!==e.value&&n.uint32(10).string(e.value),n},decode:function(e,n){for(var r=e instanceof a.Reader?e:a.Reader.create(e),t=void 0===n?r.len:r.pos+n,u={value:""};r.pos<t;){var o=r.uint32();switch(o>>>3){case 1:if(10!==o)break;u.value=r.string();continue}if(4==(7&o)||0===o)break;r.skipType(7&o)}return u},fromJSON:function(e){return{value:N(e.value)?String(e.value):""}},toJSON:function(e){var n={};return void 0!==e.value&&(n.value=e.value),n},create:function(e){return v.fromPartial(null!=e?e:{})},fromPartial:function(e){var n,r={value:""};return r.value=null!=(n=e.value)?n:"",r}});var f=e("StringValueList",{encode:function(e,r){void 0===r&&(r=a.Writer.create());for(var t,u=n(e.values);!(t=u()).done;){var o=t.value;r.uint32(10).string(o)}return r},decode:function(e,n){for(var r=e instanceof a.Reader?e:a.Reader.create(e),t=void 0===n?r.len:r.pos+n,u={values:[]};r.pos<t;){var o=r.uint32();switch(o>>>3){case 1:if(10!==o)break;u.values.push(r.string());continue}if(4==(7&o)||0===o)break;r.skipType(7&o)}return u},fromJSON:function(e){return{values:Array.isArray(null==e?void 0:e.values)?e.values.map((function(e){return String(e)})):[]}},toJSON:function(e){var n={};return e.values?n.values=e.values.map((function(e){return e})):n.values=[],n},create:function(e){return f.fromPartial(null!=e?e:{})},fromPartial:function(e){var n,r={values:[]};return r.values=(null==(n=e.values)?void 0:n.map((function(e){return e})))||[],r}});var d=e("BoolValue",{encode:function(e,n){return void 0===n&&(n=a.Writer.create()),!0===e.value&&n.uint32(8).bool(e.value),n},decode:function(e,n){for(var r=e instanceof a.Reader?e:a.Reader.create(e),t=void 0===n?r.len:r.pos+n,u={value:!1};r.pos<t;){var o=r.uint32();switch(o>>>3){case 1:if(8!==o)break;u.value=r.bool();continue}if(4==(7&o)||0===o)break;r.skipType(7&o)}return u},fromJSON:function(e){return{value:!!N(e.value)&&Boolean(e.value)}},toJSON:function(e){var n={};return void 0!==e.value&&(n.value=e.value),n},create:function(e){return d.fromPartial(null!=e?e:{})},fromPartial:function(e){var n,r={value:!1};return r.value=null!=(n=e.value)&&n,r}});var p=e("BoolValueList",{encode:function(e,r){void 0===r&&(r=a.Writer.create()),r.uint32(10).fork();for(var t,u=n(e.values);!(t=u()).done;){var o=t.value;r.bool(o)}return r.ldelim(),r},decode:function(e,n){for(var r=e instanceof a.Reader?e:a.Reader.create(e),t=void 0===n?r.len:r.pos+n,u={values:[]};r.pos<t;){var o=r.uint32();switch(o>>>3){case 1:if(8===o){u.values.push(r.bool());continue}if(10===o){for(var i=r.uint32()+r.pos;r.pos<i;)u.values.push(r.bool());continue}}if(4==(7&o)||0===o)break;r.skipType(7&o)}return u},fromJSON:function(e){return{values:Array.isArray(null==e?void 0:e.values)?e.values.map((function(e){return Boolean(e)})):[]}},toJSON:function(e){var n={};return e.values?n.values=e.values.map((function(e){return e})):n.values=[],n},create:function(e){return p.fromPartial(null!=e?e:{})},fromPartial:function(e){var n,r={values:[]};return r.values=(null==(n=e.values)?void 0:n.map((function(e){return e})))||[],r}});var m=e("IntPb",{encode:function(e,n){return void 0===n&&(n=a.Writer.create()),0!==e.intValue&&n.uint32(8).sint32(e.intValue),n},decode:function(e,n){for(var r=e instanceof a.Reader?e:a.Reader.create(e),t=void 0===n?r.len:r.pos+n,u={intValue:0};r.pos<t;){var o=r.uint32();switch(o>>>3){case 1:if(8!==o)break;u.intValue=r.sint32();continue}if(4==(7&o)||0===o)break;r.skipType(7&o)}return u},fromJSON:function(e){return{intValue:N(e.intValue)?Number(e.intValue):0}},toJSON:function(e){var n={};return void 0!==e.intValue&&(n.intValue=Math.round(e.intValue)),n},create:function(e){return m.fromPartial(null!=e?e:{})},fromPartial:function(e){var n,r={intValue:0};return r.intValue=null!=(n=e.intValue)?n:0,r}});var g=e("IntListPb",{encode:function(e,r){void 0===r&&(r=a.Writer.create()),r.uint32(10).fork();for(var t,u=n(e.intValues);!(t=u()).done;){var o=t.value;r.sint32(o)}return r.ldelim(),r},decode:function(e,n){for(var r=e instanceof a.Reader?e:a.Reader.create(e),t=void 0===n?r.len:r.pos+n,u={intValues:[]};r.pos<t;){var o=r.uint32();switch(o>>>3){case 1:if(8===o){u.intValues.push(r.sint32());continue}if(10===o){for(var i=r.uint32()+r.pos;r.pos<i;)u.intValues.push(r.sint32());continue}}if(4==(7&o)||0===o)break;r.skipType(7&o)}return u},fromJSON:function(e){return{intValues:Array.isArray(null==e?void 0:e.intValues)?e.intValues.map((function(e){return Number(e)})):[]}},toJSON:function(e){var n={};return e.intValues?n.intValues=e.intValues.map((function(e){return Math.round(e)})):n.intValues=[],n},create:function(e){return g.fromPartial(null!=e?e:{})},fromPartial:function(e){var n,r={intValues:[]};return r.intValues=(null==(n=e.intValues)?void 0:n.map((function(e){return e})))||[],r}});var b=e("LongPb",{encode:function(e,n){return void 0===n&&(n=a.Writer.create()),0!==e.longValue&&n.uint32(8).sint64(e.longValue),n},decode:function(e,n){for(var r=e instanceof a.Reader?e:a.Reader.create(e),t=void 0===n?r.len:r.pos+n,u={longValue:0};r.pos<t;){var o=r.uint32();switch(o>>>3){case 1:if(8!==o)break;u.longValue=k(r.sint64());continue}if(4==(7&o)||0===o)break;r.skipType(7&o)}return u},fromJSON:function(e){return{longValue:N(e.longValue)?Number(e.longValue):0}},toJSON:function(e){var n={};return void 0!==e.longValue&&(n.longValue=Math.round(e.longValue)),n},create:function(e){return b.fromPartial(null!=e?e:{})},fromPartial:function(e){var n,r={longValue:0};return r.longValue=null!=(n=e.longValue)?n:0,r}});var h=e("LongListPb",{encode:function(e,r){void 0===r&&(r=a.Writer.create()),r.uint32(10).fork();for(var t,u=n(e.longValues);!(t=u()).done;){var o=t.value;r.sint64(o)}return r.ldelim(),r},decode:function(e,n){for(var r=e instanceof a.Reader?e:a.Reader.create(e),t=void 0===n?r.len:r.pos+n,u={longValues:[]};r.pos<t;){var o=r.uint32();switch(o>>>3){case 1:if(8===o){u.longValues.push(k(r.sint64()));continue}if(10===o){for(var i=r.uint32()+r.pos;r.pos<i;)u.longValues.push(k(r.sint64()));continue}}if(4==(7&o)||0===o)break;r.skipType(7&o)}return u},fromJSON:function(e){return{longValues:Array.isArray(null==e?void 0:e.longValues)?e.longValues.map((function(e){return Number(e)})):[]}},toJSON:function(e){var n={};return e.longValues?n.longValues=e.longValues.map((function(e){return Math.round(e)})):n.longValues=[],n},create:function(e){return h.fromPartial(null!=e?e:{})},fromPartial:function(e){var n,r={longValues:[]};return r.longValues=(null==(n=e.longValues)?void 0:n.map((function(e){return e})))||[],r}}),V=function(){if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if("undefined"!=typeof global)return global;throw"Unable to locate global object"}();function S(e){if(V.Buffer)return Uint8Array.from(V.Buffer.from(e,"base64"));for(var n=V.atob(e),r=new Uint8Array(n.length),t=0;t<n.length;++t)r[t]=n.charCodeAt(t);return r}function k(e){if(e.gt(Number.MAX_SAFE_INTEGER))throw new V.Error("Value is larger than Number.MAX_SAFE_INTEGER");return e.toNumber()}function N(e){return null!=e}a.util.Long!==t&&(a.util.Long=t,a.configure()),r._RF.pop()}}}));

System.register("chunks:///_virtual/f_Math.ts",["cc","./FixedPointNum.ts"],(function(n){var r,t;return{setters:[function(n){r=n.cclegacy},function(n){t=n.FixedPointNum}],execute:function(){r._RF.push({},"63789vfkzxGXJ1hYf9975tE","f_Math",void 0);var e=new t(1e3),i=new t(2),o=n("f_Math",function(){function n(){this.random=void 0}return n.setRandSeed=function(n){this.randomSeed=n},n.getRandomSeed=function(){return this.randomSeed},n.random=function(){return this.randomSeed=(9301*this.randomSeed+49297)%233280,new t(this.randomSeed).div(this.randomSeedFixedPointNum)},n.initSinCos=function(){for(var n=0;n<this.sinArr.length;n++)this.sinArr[n]=new t(this.sinArr[n]).div(e);for(var r=0;r<this.cosArr.length;r++)this.cosArr[r]=new t(this.cosArr[r]).div(e)},n.sin=function(n){return(n%=360)<0&&(n+=360),this.sinArr[n]},n.cos=function(n){return(n%=360)<0&&(n+=360),this.cosArr[n]},n.tan=function(n){return(n%=360)<0&&(n+=360),this.tanArr[n]},n.atan2=function(n,r){if(0===r)return n>0?90:-90;var i=new t(n).div(new t(r)).mul(e).floor();i<0&&(i=-i);for(var o=0,u=this.tanArr;i>u[o];)o++;return n>=0?r<=0&&(o=180-o):r<=0?o+=180:o=360-o,o},n.sqrt=function(n,r){var e=new t(n);r=r||e.div(i);var o=e.div(r).add(r).div(i);return o.mul(o).toNumber()==e.toNumber()||o.toNumber()==e.div(o).add(o).div(i).toNumber()?o:this.sqrt(n,o)},n.randomFloat=function(){return this.randomBetween(0,9)/10},n.randomIntNum=function(n){return 0===n&&(n=1),this.random().mul(new t(n)).floor()},n.randomArrElement=function(n){return n[this.randomIntNum(n.length)]},n.randomBetween=function(n,r){return n+this.randomIntNum(r-n+1)},n}());o.randomSeed=5,o.randomSeedFixedPointNum=new t(233280),o.sinArr=[0,17,35,52,70,87,105,122,139,156,174,191,208,225,242,259,276,292,309,326,342,358,375,391,407,423,438,454,469,485,500,515,530,545,559,574,588,602,616,629,643,656,669,682,695,707,719,731,743,755,766,777,788,799,809,819,829,839,848,857,866,875,883,891,899,906,914,921,927,934,940,946,951,956,961,966,970,974,978,982,985,988,990,993,995,996,998,999,999,1e3,1e3,1e3,999,999,998,996,995,993,990,988,985,982,978,974,970,966,961,956,951,946,940,934,927,921,914,906,899,891,883,875,866,857,848,839,829,819,809,799,788,777,766,755,743,731,719,707,695,682,669,656,643,629,616,602,588,574,559,545,530,515,500,485,469,454,438,423,407,391,375,358,342,326,309,292,276,259,242,225,208,191,174,156,139,122,105,87,70,52,35,17,0,-17,-35,-52,-70,-87,-105,-122,-139,-156,-174,-191,-208,-225,-242,-259,-276,-292,-309,-326,-342,-358,-375,-391,-407,-423,-438,-454,-469,-485,-500,-515,-530,-545,-559,-574,-588,-602,-616,-629,-643,-656,-669,-682,-695,-707,-719,-731,-743,-755,-766,-777,-788,-799,-809,-819,-829,-839,-848,-857,-866,-875,-883,-891,-899,-906,-914,-921,-927,-934,-940,-946,-951,-956,-961,-966,-970,-974,-978,-982,-985,-988,-990,-993,-995,-996,-998,-999,-999,-1e3,-1e3,-1e3,-999,-999,-998,-996,-995,-993,-990,-988,-985,-982,-978,-974,-970,-966,-961,-956,-951,-946,-940,-934,-927,-921,-914,-906,-899,-891,-883,-875,-866,-857,-848,-839,-829,-819,-809,-799,-788,-777,-766,-755,-743,-731,-719,-707,-695,-682,-669,-656,-643,-629,-616,-602,-588,-574,-559,-545,-530,-515,-500,-485,-469,-454,-438,-423,-407,-391,-375,-358,-342,-326,-309,-292,-276,-259,-242,-225,-208,-191,-174,-156,-139,-122,-105,-87,-70,-52,-35,-17],o.cosArr=[1e3,1e3,999,999,998,996,995,993,990,988,985,982,978,974,970,966,961,956,951,946,940,934,927,921,914,906,899,891,883,875,866,857,848,839,829,819,809,799,788,777,766,755,743,731,719,707,695,682,669,656,643,629,616,602,588,574,559,545,530,515,500,485,469,454,438,423,407,391,375,358,342,326,309,292,276,259,242,225,208,191,174,156,139,122,105,87,70,52,35,17,0,-17,-35,-52,-70,-87,-105,-122,-139,-156,-174,-191,-208,-225,-242,-259,-276,-292,-309,-326,-342,-358,-375,-391,-407,-423,-438,-454,-469,-485,-500,-515,-530,-545,-559,-574,-588,-602,-616,-629,-643,-656,-669,-682,-695,-707,-719,-731,-743,-755,-766,-777,-788,-799,-809,-819,-829,-839,-848,-857,-866,-875,-883,-891,-899,-906,-914,-921,-927,-934,-940,-946,-951,-956,-961,-966,-970,-974,-978,-982,-985,-988,-990,-993,-995,-996,-998,-999,-999,-1e3,-1e3,-1e3,-999,-999,-998,-996,-995,-993,-990,-988,-985,-982,-978,-974,-970,-966,-961,-956,-951,-946,-940,-934,-927,-921,-914,-906,-899,-891,-883,-875,-866,-857,-848,-839,-829,-819,-809,-799,-788,-777,-766,-755,-743,-731,-719,-707,-695,-682,-669,-656,-643,-629,-616,-602,-588,-574,-559,-545,-530,-515,-500,-485,-469,-454,-438,-423,-407,-391,-375,-358,-342,-326,-309,-292,-276,-259,-242,-225,-208,-191,-174,-156,-139,-122,-105,-87,-70,-52,-35,-17,0,17,35,52,70,87,105,122,139,156,174,191,208,225,242,259,276,292,309,326,342,358,375,391,407,423,438,454,469,485,500,515,530,545,559,574,588,602,616,629,643,656,669,682,695,707,719,731,743,755,766,777,788,799,809,819,829,839,848,857,866,875,883,891,899,906,914,921,927,934,940,946,951,956,961,966,970,974,978,982,985,988,990,993,995,996,998,999,999,1e3],o.tanArr=[0,17,35,52,70,87,105,123,141,158,176,194,213,231,249,268,287,306,325,344,364,384,404,424,445,466,488,510,532,554,577,601,625,649,675,700,727,754,781,810,839,869,900,933,966,1e3,1036,1072,1111,1150,1192,1235,1280,1327,1376,1428,1483,1540,1600,1664,1732,1804,1881,1963,2050,2145,2246,2356,2475,2605,2747,2904,3078,3271,3487,3732,4011,4331,4705,5145,5671,6314,7115,8144,9514,11430,14301,19081,28636,57290,1/0],o.initSinCos(),r._RF.pop()}}}));

System.register("chunks:///_virtual/FixedPointNum.ts",["./rollupPluginModLoBabelHelpers.js","cc"],(function(t){var i,n;return{setters:[function(t){i=t.createClass},function(t){n=t.cclegacy}],execute:function(){n._RF.push({},"93fd8qbGrBLe7E9o9Ml5h6t","FixedPointNum",void 0);var e=Math.pow(10,3),s=new(t("FixedPointNum",function(){function t(){this.sign=1,this.v1=0,this.v2=0;for(var t=arguments.length,i=new Array(t),n=0;n<t;n++)i[n]=arguments[n];if(3!==i.length){if(0!==i.length&&1===i.length){if("number"==typeof i[0]&&(i[0]=""+i[0]),0===i[0].length)return;var s=i[0].split(".");this.sign="-"===s[0][0]?-1:1,this.v1=parseInt(s[0]),-1===this.sign&&(this.v1=-this.v1),s[1]&&(this.v2=parseInt(u(s[1]))),0===this.v1&&0===this.v2&&(this.sign=1)}}else{if(this.sign=i[0],this.v1=i[1],this.v2=i[2],this.v2>=e)throw new Error("FixedPointNum v2 too big -- "+this.v2);0===this.v1&&0===this.v2&&(this.sign=1)}}var n=t.prototype;return n.toNumber=function(){return this.sign*(this.v1+this.v2/e)},n.toString=function(){return 1===this.sign?this.v1.toString()+"."+"0".repeat(3-this.v2.toString().length)+this.v2:"-"+this.v1.toString()+"."+"0".repeat(3-this.v2.toString().length)+this.v2},n.getHugeValue=function(){return this.sign*(this.v1*e+this.v2)},n.clone=function(){return new t(this.sign,this.v1,this.v2)},n.floor=function(){return this.sign*this.v1},n.ceil=function(){return this.sign*(this.v1+(this.v2>0?1:0))},n.round=function(){return this.sign*(this.v1+(this.v2>e>>1?1:0))},n.reverse=function(){return new t(-this.sign,this.v1,this.v2)},n.add=function(i){var n=this.getHugeValue()+i.getHugeValue(),s=1;n<0&&(s=-1,n=-n);var u=r(n,e);return new t(s,u.shang,u.yu)},n.addSelf=function(t){var i=this.add(t);return this.sign=i.sign,this.v1=i.v1,this.v2=i.v2,this},n.sub=function(t){return this.add(t.reverse())},n.subSelf=function(t){var i=this.sub(t);return this.sign=i.sign,this.v1=i.v1,this.v2=i.v2,this},n.mul=function(i){var n=this.v1*i.v1,s=r(this.v1*i.v2,e),u=r(this.v2*i.v1,e),h=r(this.v2*i.v2,e),v=new t(1,n,0).addSelf(new t(1,s.shang,s.yu)).addSelf(new t(1,u.shang,u.yu)).addSelf(new t(1,0,h.shang));return new t(this.sign===i.sign?1:-1,v.v1,v.v2)},n.mulSelf=function(t){var i=this.mul(t);return this.sign=i.sign,this.v1=i.v1,this.v2=i.v2,this},n.div=function(i){var n=i.v1*e+i.v2;if(0===n)throw new Error("FixedPointNum div has a zero number");var s=r(this.v1*e+this.v2,n),u=r(s.yu*e,n);return new t(this.sign===i.sign?1:-1,s.shang,u.shang)},n.divSelf=function(t){var i=this.div(t);return this.sign=i.sign,this.v1=i.v1,this.v2=i.v2,this},n.LT=function(t){return this.getHugeValue()<t.getHugeValue()},n.LE=function(t){return this.getHugeValue()<=t.getHugeValue()},n.EQ=function(t){return this.getHugeValue()===t.getHugeValue()},n.NE=function(t){return this.getHugeValue()!==t.getHugeValue()},n.GT=function(t){return this.getHugeValue()>t.getHugeValue()},n.GE=function(t){return this.getHugeValue()>=t.getHugeValue()},n.max=function(t){return this.GT(t)?this:t},n.min=function(t){return this.LT(t)?this:t},i(t,null,[{key:"ZERO",get:function(){return new t}},{key:"ZERO_R",get:function(){return s}}]),t}()));function u(t,i){void 0===i&&(i=3);var n=t.length;return n===i?t:n>i?t.substring(0,i):t+"0".repeat(i-n)}function r(t,i){if(t<i)return{shang:0,yu:t};if(t===i)return{shang:1,yu:0};var n=Math.floor(t/i)-1;for(t-=i*n;t>=i;)n+=1,t-=i;return{shang:n,yu:t}}n._RF.pop()}}}));

System.register("chunks:///_virtual/GameEvent.ts",["cc"],(function(e){var t;return{setters:[function(e){t=e.cclegacy}],execute:function(){t._RF.push({},"2989erLp5tOFI6bY17hfQnf","GameEvent",void 0);e("GameEvent",function(e){return e.OPEN="GameSocketOpen",e.CLOSE="GameSocketClose",e.MESSAGE="GameSocketmESSAGE",e.ERROR="GameSocketError",e}({}));t._RF.pop()}}}));

System.register("chunks:///_virtual/GameSocket.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ExternalMessage.ts","./EventManager.ts","./GameEvent.ts"],(function(e){var o,n,t,r,s,i,c,a,g;return{setters:[function(e){o=e.inheritsLoose,n=e.createClass,t=e.assertThisInitialized},function(e){r=e.cclegacy,s=e.Node,i=e.director},function(e){c=e.ExternalMessage},function(e){a=e.EventManager},function(e){g=e.GameEvent}],execute:function(){r._RF.push({},"b4e69SjQWJPUInOjFCSl10P","GameSocket",void 0);var l=e("SocketLogLevel",function(e){return e[e.All=0]="All",e[e.Log=1]="Log",e[e.Warn=2]="Warn",e[e.Error=3]="Error",e[e.None=4]="None",e}({})),d=e("GameSocket",function(e){function r(){for(var o,n=arguments.length,s=new Array(n),i=0;i<n;i++)s[i]=arguments[i];return(o=e.call.apply(e,[this].concat(s))||this).logLevel=void 0,o.pingpongEnable=!0,o.socket=void 0,o.pingpongMax=-1,o.pingpongIndex=0,o.pingpong=void 0,o._url=void 0,o.connect=function(){o.socket=new WebSocket(o._url),o.socket.binaryType="arraybuffer",o.socket.onopen=o.onSocketOpen,o.socket.onclose=o.onSocketClose,o.socket.onmessage=o.onMessageReveived,o.socket.onerror=o.onConnectError,r.sockets.push(t(o))},o.close=function(e){var n;void 0===e&&(e=1e3),o.log("执行断开链接"),null==(n=o.socket)||n.close(e,"客户端执行断开")},o.onSocketOpen=function(e){o.log("Connected",e),o.startPingPong(),o.emit(g.OPEN,e)},o.onSocketClose=function(e){o.log("Socket closed",e),o.stopPingPong(),o.emit(g.CLOSE,e),r.sockets.splice(r.sockets.indexOf(t(o)),1),o.node.destroy()},o.onMessageReveived=function(e){var n=e.data;if("string"==typeof n)o.log("Message from server string: "+n);else if(n instanceof ArrayBuffer){var t=c.decode(new Uint8Array(n));1==t.cmdCode&&o.log("Message from server info: "+JSON.stringify(t));try{if(0==t.cmdCode)o.pingpongIndex=0;else if(r.messageCtrls.has(t.cmdMerge)){var s=r.messageCtrls.get(t.cmdMerge);if(s&&s.receive(t.responseStatus,t.data),o.logLevel<=l.Log){var i=t.cmdMerge>>16;o.log("收到ID! cmd:"+i+" sub:"+(t.cmdMerge-(i<<16)))}}else{var a=t.cmdMerge>>16;o.error("获取ID!失败,未注册ID cmd:"+a+" sub:"+(t.cmdMerge-(a<<16)))}o.log("info:::",t,r.messageCtrls.get(t.cmdMerge))}catch(e){o.error(e)}}o.emit(g.MESSAGE,n)},o.onConnectError=function(e){o.log("connect error",e),o.emit(g.ERROR,e)},o.log=function(){var e;if(!(o.logLevel>l.Log)){for(var n=arguments.length,t=new Array(n),r=0;r<n;r++)t[r]=arguments[r];(e=console).log.apply(e,["gameSocket!!"].concat(t))}},o.warn=function(){var e;if(!(o.logLevel>l.Warn)){for(var n=arguments.length,t=new Array(n),r=0;r<n;r++)t[r]=arguments[r];(e=console).warn.apply(e,["gameSocket!!"].concat(t))}},o.error=function(){var e;if(!(o.logLevel>l.Error)){for(var n=arguments.length,t=new Array(n),r=0;r<n;r++)t[r]=arguments[r];(e=console).error.apply(e,["gameSocket!!"].concat(t))}},o}o(r,e),r.createCls=function(e,o){void 0===o&&(o={}),void 0===o.logLevel&&(o.logLevel=l.Error),o.pingpongMax||(o.pingpongMax=10);var n=(new s).addComponent(r);return i.addPersistRootNode(n.node),n._url!=e&&(n._url=e),n.logLevel!=o.logLevel&&(n.logLevel=o.logLevel),n.pingpongMax!=o.pingpongMax&&(n.pingpongMax=o.pingpongMax),n};var a=r.prototype;return a.send=function(e){if(this.log(e),1===this.socket.readyState){var o={cmdCode:1,protocolSwitch:e.protocolSwitch,cmdMerge:(e.cmd<<16)+e.subCmd,responseStatus:100,validMsg:"",data:e.data};this.log("sendMsg",JSON.stringify(o)),this.log("发送ID! cmd:"+e.cmd+" sub:"+e.subCmd),this.socket.send(c.encode(o).finish())}else this.error("socket未准备好,无法发送：发送ID! cmd:"+e.cmd+" sub:"+e.subCmd)},r.onListenMessage=function(e,o){if(r.messageCtrls.has(e)){var n=e>>16;this.mainSocket.error("重复ID cmd:"+n+" sub:"+(e-(n<<16)))}else r.messageCtrls.set(e,o)},a.startPingPong=function(){var e=this;if(this.log("开启心跳包"),this.pingpongEnable){this.stopPingPong();var o=c.encode({cmdCode:0,protocolSwitch:1,cmdMerge:65538,responseStatus:100,validMsg:"",data:new Uint8Array(0)}).finish();this.pingpong=setInterval((function(){e.socket.send(o),++e.pingpongIndex>e.pingpongMax&&e.close(3001),e.log("当前心跳包index : "+e.pingpongIndex+" max: "+e.pingpongMax)}),1e3)}},a.stopPingPong=function(){clearInterval(this.pingpong)},n(r,[{key:"url",set:function(e){this._url=e}},{key:"state",get:function(){var e;return null==(e=this.socket)?void 0:e.readyState}}],[{key:"mainSocket",get:function(){return null!=this.sockets&&this.sockets.length>0?this.sockets[this.sockets.length-1]:null}}]),r}(a));d.messageCtrls=new Map,d.sockets=[],r._RF.pop()}}}));

System.register("chunks:///_virtual/GameSocketCtrl.ts",["./rollupPluginModLoBabelHelpers.js","cc","./GameSocket.ts","./EventDispatcher.ts","./ResGameStatus.ts","./api.ts","./GameEvent.ts","./CmdLogin.ts","./xcore.ts","./ConstGlobal.ts"],(function(t){var e,n,o,s,i,c,r,a,u,h,f,S,l,C,E;return{setters:[function(t){e=t.inheritsLoose,n=t.createClass,o=t.asyncToGenerator,s=t.regeneratorRuntime},function(t){i=t.cclegacy},function(t){c=t.GameSocket,r=t.SocketLogLevel},function(t){a=t.default},function(t){u=t.default},function(t){h=t.WEB_SOCKET_URL},function(t){f=t.GameEvent},function(t){S=t.default},function(t){l=t.xcore},function(t){C=t.E_EVENT,E=t.C_View}],execute:function(){i._RF.push({},"b4e49AqhsBAP74dpug438YK","GameSocketCtrl",void 0);var m=t("GameSocketEventEnum",function(t){return t.GAME_STATUS="GAME_STATUS",t.CONNECT_SUCCESS="CONNECT_SUCCESS",t.SOCKET_CLOSE="SOCKET_CLOSE",t}({}));t("default",function(t){function i(){var e;return(e=t.call(this)||this).url=h,e.socket=void 0,e}e(i,t);var a=i.prototype;return a.create=function(t){return this.url=t,this.socket=c.createCls(this.url,{logLevel:r.Log}),this.socket},a.connect=function(t){return void 0===t&&(t=this.url),this.closeSocket(),i.inst.off(m.GAME_STATUS,this.ioGameStatusChange,this),i.inst.off(m.CONNECT_SUCCESS,this.onGameConnectSuccess,this),i.inst.on(m.CONNECT_SUCCESS,this.onGameConnectSuccess,this),this.create(t),this.socket.on(f.OPEN,this.onSocketOpen,this),this.socket.on(f.CLOSE,this.onSocketClose,this),this.socket.on(f.ERROR,this.onConnectError,this),this.socket.connect(),this.socket},a.closeSocket=function(){this.socket&&(this.socket.off(f.OPEN,this.onSocketOpen,this),this.socket.off(f.CLOSE,this.onSocketClose,this),this.socket.off(f.ERROR,this.onConnectError,this),this.socket.close&&this.socket.close())},a.onSocketOpen=function(){var t=o(s().mark((function t(){return s().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return console.log("链接成功"),t.prev=1,t.next=4,this.login();case 4:this.addGameListen(),console.log("链接登录成功"),this.emit(m.CONNECT_SUCCESS),t.next=13;break;case 9:t.prev=9,t.t0=t.catch(1),l.ui.addView(E.ViewCommonTips,{desc:"您与服务器断开链接，请重启玩法"}),l.event.raiseEvent(C.GameLogin,!1);case 13:case"end":return t.stop()}}),t,this,[[1,9]])})));return function(){return t.apply(this,arguments)}}(),a.addGameListen=function(){u.inst.listen(this,this.ioGameStatusChange)},a.onSocketClose=function(){console.log("断开连接"),l.event.raiseEvent(C.GameOut)},a.onConnectError=function(){l.ui.showToast("连接失败"),l.event.raiseEvent(C.GameBack)},a.login=function(){var t=o(s().mark((function t(){var e;return s().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,S.inst.sendSync({token:l.http.defaults.headers.Authorization,roomId:l.gameData.baseInfo.roomId});case 2:return e=t.sent,console.log("CmdLogin",e),t.abrupt("return",new Promise(o(s().mark((function t(n,o){return s().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.isSucc){t.next=2;break}return t.abrupt("return",o(e.code));case 2:return t.abrupt("return",n(e));case 3:case"end":return t.stop()}}),t)})))));case 5:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}(),a.ioGameStatusChange=function(t){t.data.forEach((function(e){e.msg_type=t.msgType,e.content=e.content?e.content.trim():""})),l.event.raiseEvent(C.Gift,t.data)},a.onGameConnectSuccess=function(){l.event.raiseEvent(C.GameLogin,!0)},n(i,null,[{key:"inst",get:function(){return i._inst||(i._inst=new i),i._inst}}]),i}(a))._inst=void 0,i._RF.pop()}}}));

System.register("chunks:///_virtual/Guide.ts",["./rollupPluginModLoBabelHelpers.js","cc","./Tool.ts","./xcore.ts","./ConstGlobal.ts"],(function(e){var t,i,r,n,o,s,h,d,a,u,_,l,c,m,g,T,f,p,y,x,v,F,C,b,G;return{setters:[function(e){t=e.createForOfIteratorHelperLoose,i=e.createClass},function(e){r=e.cclegacy,n=e.SpriteFrame,o=e.warn,s=e.Node,h=e.Camera,d=e.color,a=e.UITransform,u=e.view,_=e.Vec3,l=e.log,c=e.Graphics,m=e.Sprite,g=e.v3,T=e.Label,f=e.RichText,p=e.TweenSystem,y=e.tween,x=e.Button,v=e.v2,F=e.director},function(e){C=e.default},function(e){b=e.xcore},function(e){G=e.E_UILAYER}],execute:function(){r._RF.push({},"e5a6bUwbQ1BXqmXsycDQ5ox","Guide",void 0);var S=e("CheckGuidedStrategy",function(){function e(e){this.checkIfAbleGuide=e}return e.prototype.checkIfAbleGuide=function(e){return!1},e}());e("CheckTestStrategy",function(){function e(e){this.checkIfAbleGuide=e}return e.prototype.checkIfAbleGuide=function(e){return!1},e}());e("default",function(){function e(){this._txtEffTimer=null,this._richtxtEffTimer=null,this._txtEffSpeedTime=100,this._richtxtEffSpeedTime=100,this._camGroupIndex=2,this._orgTargetNdGroupIndex=0,this._orgTargetNdParent=null,this._orgTargetNdPos=null,this._layerIndex=G.GUIDE_LAYER,this._spriteFrameGroups=void 0,this._ndRoot=void 0,this._ndInject=void 0,this._ndFinger=void 0,this._ndMask=void 0,this._ndFrame1=void 0,this._ndFrame2=void 0,this._ndFrame3=void 0,this._ndJump=void 0,this._lbTxt=void 0,this._lbRichTxt=void 0,this._canvas=null,this._guideCamera=void 0,this._autoHideGuideTimer=void 0,this._delayShowGuideTimer=void 0,this._delayTouchGuideTimer=void 0,this._delayShowFingerTimer=void 0,this._delayShowTargetNdTimer=void 0,this._guideConfig=void 0,this._guideInfo=null,this.checkGuidedStrategy=void 0,this.tempCheckGuidedStrategy=void 0,l("引导层摄像机layer 掩码：",this._camGroupIndex.toString(2)),this._canvas=F.getScene().getChildByName("Canvas"),this.checkGuidedStrategy=new S((function(){return!1}))}var r=e.prototype;return r.initGuide=function(e,t){this._guideConfig=e,this.setCheckAbleGuideFunc(t)},r.setCheckAbleGuideFunc=function(e,t){void 0===t&&(t=!1),t&&(this.tempCheckGuidedStrategy=this.checkGuidedStrategy),this.checkGuidedStrategy=e},r.backTempCheckGuideFunc=function(){if(this.tempCheckGuidedStrategy){var e=this.tempCheckGuidedStrategy;this.tempCheckGuidedStrategy=null,this.checkGuidedStrategy=e}},r.initGuideSfriteFrame=function(e,t){t&&t instanceof n&&e?(this._spriteFrameGroups||(this._spriteFrameGroups={}),this._spriteFrameGroups[e]=t):o("initGuideSfriteFrame err")},r.checkIfGuide=function(e,t){if(!this.checkGuidedStrategy.checkIfAbleGuide(e.StepName))return this.backTempCheckGuideFunc(),o("已完成过引导",e.StepName),this.hideGuide(),void(e.GuideReadyCb&&e.GuideReadyCb(!1));if(this.backTempCheckGuideFunc(),!this._guideConfig||!this._guideConfig[e.StepName])return o("引导配置文件错误",e.StepName),this.hideGuide(),void(e.GuideReadyCb&&e.GuideReadyCb(!1));var i=this._guideConfig[e.StepName];if(t&&i.ShowAfter)for(var r=0;r<i.ShowAfter.length;r++)if(!t.includes(i.ShowAfter[r]))return o(e.StepName+" should show after "+i.ShowAfter+" finished"),this.hideGuide(),void(e.GuideReadyCb&&e.GuideReadyCb(!1));e.GuideReadyCb&&e.GuideReadyCb(!0),e.TargetNd&&(i.TargetNode=e.TargetNd),e.TouchCb&&(i.TouchCb=e.TouchCb),e.JumpCb&&(i.JumpCb=e.JumpCb),e.AutoCb&&(i.AutoCb=e.AutoCb),this.showGuide(i)},r.showGuide=function(e){var t=this;if(this.clearTimer(),!this._guideCamera){var i;this._guideCamera=(new s).addComponent(h),this._guideCamera.node.name="guideCamera",this._guideCamera.visibility=this._camGroupIndex,this._guideCamera.priority=1,this._guideCamera.projection=h.ProjectionType.ORTHO;var r=null==(i=this._canvas.getChildByName("Camera"))?void 0:i.getComponent(h).orthoHeight;if(!r)return void o("Camera no find");this._guideCamera.orthoHeight=r,this._guideCamera.near=0,this._guideCamera.far=1e3,this._guideCamera.clearColor=d(0,0,0,0),this._guideCamera.clearFlags=h.ClearFlag.DONT_CLEAR,this._guideCamera.node.parent=this._canvas}if(this._ndRoot&&this._ndRoot.isValid&&this._ndRoot.active&&this.hideGuide(),e.DelayShowGuide)return this.setMask(0),void(this._delayShowGuideTimer=setTimeout((function(){e.DelayShowGuide=null,clearTimeout(t._delayShowGuideTimer),t._delayShowGuideTimer=null,t.showGuide(e)}),e.DelayShowGuide));if(e.StepName){if(this._delayShowGuideTimer&&clearTimeout(this._delayShowGuideTimer),this.setMask(e.MaskOpacity),this._ndRoot&&this._ndRoot.isValid||(this._ndRoot=(new s).addComponent(a).node,this._ndRoot.getComponent(a).width=u.getVisibleSize().width,this._ndRoot.getComponent(a).height=u.getVisibleSize().height,this._ndRoot.parent=this._canvas,this._ndRoot.layer=this._camGroupIndex,this._ndRoot.name="ndRoot",C.setChildrenNodeSortByPriority(this._ndRoot,this._layerIndex+1)),this._ndRoot.active=!0,e.Frame1SpriteFrame){var n=new _;e.PropertyFrame1.x&&(n.x=e.PropertyFrame1.x),e.PropertyFrame1.y&&(n.y=e.PropertyFrame1.y),this.setFrame(1,e.Frame1SpriteFrame,e.PropertyFrame1,n,e.Frame1Index)}if(e.Frame2SpriteFrame){var l=new _;e.PropertyFrame2.x&&(l.x=e.PropertyFrame2.x),e.PropertyFrame2.y&&(l.y=e.PropertyFrame2.y),this.setFrame(2,e.Frame2SpriteFrame,e.PropertyFrame2,l,e.Frame2Index)}if(e.Frame3SpriteFrame){var c=new _;e.PropertyFrame3.x&&(c.x=e.PropertyFrame3.x),e.PropertyFrame3.y&&(c.y=e.PropertyFrame3.y),this.setFrame(3,e.Frame3SpriteFrame,e.PropertyFrame3,c,e.Frame3Index)}this.setTxt(e.LbTxt,e.PropertyTxt,e.TxtIndex,e.IsTxtShowEff),this.setRichTxt(e.LbRichTxt,e.PropertyRichTxt,e.RichTxtIndex,e.IsRichTxtShowEff),this.setJump(!!e.JumpBtnSpriteFrame,e.JumpBtnSpriteFrame,e.JumpBtnOffsetPos,e.JumpBtnIndex),this.setTargetNd(e.TargetNode,e.DelayShowTargetNd),this.setFinger(!!e.FingerSpriteFrame,e.FingerSpriteFrame,e.TargetNode,e.FingerOffsetPos,e.FingerIndex,e.DelayShowFinger),this._delayTouchGuideTimer=setTimeout((function(){t._guideInfo=e,e.DelayTouchGuide=0,clearTimeout(t._delayTouchGuideTimer),e.AutoHideGuideTime&&(t._autoHideGuideTimer=setTimeout((function(){clearTimeout(t._autoHideGuideTimer),t._ndRoot&&t._ndRoot.active&&(t._guideInfo.AutoCb&&t._guideInfo.AutoCb(),t.hideGuide())}),e.AutoHideGuideTime))}),e.DelayTouchGuide||0)}else o("stepname undefined")},r.setMask=function(e){void 0===e&&(e=210),l("setMask",e),this._ndMask&&this._ndMask.isValid||(this._ndMask=(new s).addComponent(a).node,this._ndMask.parent=this._canvas,this._ndMask.getComponent(a).width=u.getVisibleSize().width,this._ndMask.getComponent(a).height=u.getVisibleSize().height,this._ndMask.addComponent(c),this._ndMask.layer=this._camGroupIndex,this._ndMask.name="ndMask",C.setChildrenNodeSortByPriority(this._ndMask,this._layerIndex)),this._ndMask.off(s.EventType.TOUCH_END,this.onTouch,this),this._ndMask.on(s.EventType.TOUCH_END,this.onTouch,this);var t=this._ndMask.getComponent(c);t.clear(),t.fillColor=d(0,0,0,e),t.rect(-u.getVisibleSize().width/2,-u.getVisibleSize().height/2,u.getVisibleSize().width,u.getVisibleSize().height),t.fill(),this._ndMask.active=!0},r.setFrame=function(e,t,i,r,n){if(t){var h=this["_ndFrame"+e];for(var d in h&&h.isValid||((h=new s).addComponent(a),h.addComponent(m),h.parent=this._ndRoot,h.name="ndFrame"+e,this["_ndFrame"+e]=h,this["_ndFrame"+e].layer=this._camGroupIndex),C.setChildrenNodeSortByPriority(h,n||3),this._spriteFrameGroups[t]?h.getComponent(m).spriteFrame=this._spriteFrameGroups[t]:o("ndFrame spriteframe null",t),h.setPosition(r||g()),i)if(Object.prototype.hasOwnProperty.call(i,d)){var u=i[d];void 0!==h.getComponent(a)[d]?h.getComponent(a)[d]=u:void 0!==h.getComponent(m)[d]?h.getComponent(m)[d]=u:h.getComponent(m).spriteFrame[d]=u}h.active=!0}},r.setTxt=function(e,t,i,r){var n;if(e){for(var o in this._lbTxt&&null!=(n=this._lbTxt.node)&&n.isValid||(this._lbTxt=(new s).addComponent(T),this._lbTxt.node.parent=this._ndRoot,this._lbTxt.node.layer=this._camGroupIndex,this._lbTxt.node.name="txt",C.setChildrenNodeSortByPriority(this._lbTxt.node,i||20)),t)if(Object.prototype.hasOwnProperty.call(t,o)){var h=t[o];void 0!==this._lbTxt[o]?this._lbTxt[o]=h:"x"==o?this._lbTxt.node.setPosition(g(h,this._lbTxt.node.getPosition().y)):"y"==o?this._lbTxt.node.setPosition(g(this._lbTxt.node.getPosition().x,h)):this._lbTxt.node.getComponent(a)[o]=h}r?this.doTxtEff(e):this._lbTxt.string=e,this._lbTxt.node.active=!0}},r.doTxtEff=function(e){var t=this;if(e){var i=e.split(""),r=0;this._txtEffTimer&&clearInterval(this._txtEffTimer),this._txtEffTimer=setInterval((function(){r>=i.length?(t._txtEffTimer&&clearInterval(t._txtEffTimer),t._txtEffTimer=null):(r+=1,t._lbTxt.string=i.slice(0,r).join(""))}),this._txtEffSpeedTime)}},r.setRichTxt=function(e,t,i,r){var n;if(e){for(var o in this._lbRichTxt&&null!=(n=this._lbRichTxt)&&n.node.isValid||(this._lbRichTxt=(new s).addComponent(a).addComponent(f),this._lbRichTxt.node.parent=this._ndRoot,this._lbRichTxt.string="",this._lbRichTxt.node.name="rtx",this._lbRichTxt.node.layer=this._camGroupIndex,C.setChildrenNodeSortByPriority(this._lbRichTxt.node,i||20)),t)if(Object.prototype.hasOwnProperty.call(t,o)){var h=t[o];void 0!==this._lbRichTxt[o]?this._lbRichTxt[o]=h:"x"==o?this._lbRichTxt.node.setPosition(g(h,this._lbRichTxt.node.getPosition().y)):"y"==o?this._lbRichTxt.node.setPosition(g(this._lbRichTxt.node.getPosition().x,h)):this._lbRichTxt.node.getComponent(a)[o]=h}r?this.doRichTxtEff(e):this._lbRichTxt.string=e,this._lbRichTxt.node.active=!0}},r.doRichTxtEff=function(e){var i=this;void 0===e&&(e="");for(var r,n=/<.+?\/?>/g,s=e.match(n),h="│",d=e.replace(n,h).split(h),a=[],u=0,_=t(d);!(r=_()).done;){var l=r.value;""!==l&&(l="$["+u+"]",u+=1),a.push(l)}for(var c=a.join(h),m=0;m<d.length;m++)""===d[m]&&(d.splice(m,1),m-=1);for(;-1!==c.search(h);)s[0]?(c=c.replace(h,s[0].toString()),s.splice(0,1)):(c=c.replace(h,""),o("matchArr not enough"));for(var g=[],T=new Array(u).fill(""),f=0;f<d.length;f++)for(var p,y=t(d[f]);!(p=y()).done;){var x=p.value;T[f]=T[f]+x;for(var v=c,F=0;F<u;F++)v=v.replace("$["+F+"]",T[F]);g.push(v)}var C=0;this._richtxtEffTimer&&clearInterval(this._richtxtEffTimer),this._richtxtEffTimer=setInterval((function(){if(C>=g.length)return i._richtxtEffTimer&&clearInterval(i._richtxtEffTimer),void(i._richtxtEffTimer=null);i._lbRichTxt.string=g[C],C+=1}),this._richtxtEffSpeedTime)},r.setJump=function(e,t,i,r){e?(this._ndJump&&this._ndJump.isValid||(this._ndJump=(new s).addComponent(a).node,this._ndJump.addComponent(m),this._ndJump.layer=this._camGroupIndex,this._ndJump.parent=this._ndRoot,this._ndJump.name="jump"),this._spriteFrameGroups[t]?this._ndJump.getComponent(m).spriteFrame=this._spriteFrameGroups[t]:o("jump spriteframe null",t),this._ndJump.setPosition(i||g()),C.setChildrenNodeSortByPriority(this._ndJump,r||30),this._ndJump.active=!0):this._ndJump&&(this._ndJump.active=!1)},r.setFinger=function(e,t,i,r,n,h){var d=this;if(e&&i&&this._spriteFrameGroups[t]){this._ndFinger||(this._ndFinger=(new s).addComponent(a).node,this._ndFinger.addComponent(m),this._ndFinger.parent=this._ndRoot,this._ndFinger.layer=this._camGroupIndex,this._ndFinger.name="finger"),h?(this._ndFinger.active=!1,this._delayShowFingerTimer=setTimeout((function(){d._ndFinger.active=!0,clearTimeout(d._delayShowFingerTimer)}),h)):this._ndFinger.active=!0;var u=this._ndRoot.getComponent(a).convertToNodeSpaceAR(i.getWorldPosition());r&&(u.x+=r.x),r&&(u.y+=r.y),this._ndFinger.setPosition(u||g(1,1)),C.setChildrenNodeSortByPriority(this._ndFinger,n||40),this._spriteFrameGroups[t]?this._ndFinger.getComponent(m).spriteFrame=this._spriteFrameGroups[t]:o("Finger spriteframe null",t),p.instance.ActionManager.removeAllActionsFromTarget(this._ndFinger),y(this._ndFinger).repeatForever(y(this._ndFinger).to(1,{position:g(u.x-60,u.y+60),scale:g(1,1)},{easing:"cubicInOut"}).to(1.2,{position:g(u.x,u.y-20),scale:g(1.2,1.2)},{easing:"sineOut"})).start()}else this._ndFinger&&(this._ndFinger.active=!1)},r.setTargetNd=function(e,t){var i=this;e&&e.isValid&&e.active&&(t?this._delayShowTargetNdTimer=setTimeout((function(){i.showTargetNd(e)}),t):this.showTargetNd(e))},r.showTargetNd=function(e){if(e&&e.isValid){!this._orgTargetNdGroupIndex&&(this._orgTargetNdGroupIndex=e.layer),!this._orgTargetNdParent&&(this._orgTargetNdParent=e.parent),!this._orgTargetNdPos&&(this._orgTargetNdPos=e.position.clone());var t=this._ndRoot.getComponent(a).convertToNodeSpaceAR(e.getWorldPosition());e.parent=this._ndRoot,e.setPosition(t),C.setChildrenNodeSortByPriority(e,2),this.resetAbleButtonTouch(e,!1),this._camGroupIndex&&this.resetTargetNdLayer(e,this._camGroupIndex)}this._delayShowTargetNdTimer&&clearTimeout(this._delayShowTargetNdTimer)},r.resetTargetNdLayer=function(e,t){for(var i=0;i<e.children.length;i++){var r=e.children[i];r.layer=t,r.children.length>0&&this.resetTargetNdLayer(r,t)}e.layer=t},r.resetAbleButtonTouch=function(e,t){if(e&&e.isValid){for(var i=0;i<e.children.length;i++){var r=e.children[i];r.getComponent(x)&&(r.getComponent(x).enabled=t),r.children.length>0&&this.resetAbleButtonTouch(r,t)}e.getComponent(x)&&(e.getComponent(x).enabled=t)}},r.injectNode=function(e,t,i){this._ndInject&&this._ndInject.isValid&&(this._ndInject.destroy(),this._ndInject=null),this._ndInject=e,this._ndInject.parent=this._ndRoot,this._ndInject.position=t,C.setChildrenNodeSortByPriority(this._ndInject,i||0)},r.hideGuide=function(){if(this.targetNodeBackLayer(),this._ndRoot&&(this._ndRoot.active=!1),this._ndMask&&this._ndMask.off(s.EventType.TOUCH_END,this.onTouch,this),this._ndFinger&&(this._ndFinger.active=!1,p.instance.ActionManager.removeAllActionsFromTarget(this._ndFinger)),this._ndMask&&(this._ndMask.active=!1),this._ndFrame1&&(this._ndFrame1.active=!1),this._ndFrame2&&(this._ndFrame2.active=!1),this._ndFrame3&&(this._ndFrame3.active=!1),this._ndJump&&(this._ndJump.active=!1),this._lbTxt&&(this._lbTxt.node.active=!1,this._lbTxt.string=""),this._lbRichTxt&&(this._lbRichTxt.node.active=!1,this._lbRichTxt.string=""),this._ndInject&&(this._ndInject.destroy(),this._ndInject=null),this._guideInfo){this.clearTimer();var e=this._guideInfo.StepName;this._guideInfo=null,this._txtEffTimer=null,this._delayShowGuideTimer=null,this._delayShowFingerTimer=null,this._delayShowTargetNdTimer=null,this._delayTouchGuideTimer=null,this._autoHideGuideTimer=null,this._orgTargetNdPos=null,b.event.raiseEvent("HideGuide",e)}},r.onTouch=function(e){if(l("ontouch"),this._guideInfo)if(this._txtEffTimer)this._guideInfo.IsClickOffTxtShowEff&&(clearInterval(this._txtEffTimer),this._txtEffTimer=null,this._lbTxt.string=this._guideInfo.LbTxt,this._guideInfo.FingerSpriteFrame&&this._guideInfo.TargetNode&&this._ndFinger&&(this._delayShowFingerTimer&&clearTimeout(this._delayShowFingerTimer),this._ndFinger.active=!0),this.showTargetNd(this._guideInfo.TargetNode));else if(this._richtxtEffTimer)this._guideInfo.IsClickOffRichTxtShowEff&&(clearInterval(this._richtxtEffTimer),this._richtxtEffTimer=null,this._lbRichTxt.string=this._guideInfo.LbRichTxt,this._guideInfo.FingerSpriteFrame&&this._guideInfo.TargetNode&&this._ndFinger&&(this._delayShowFingerTimer&&clearTimeout(this._delayShowFingerTimer),this._ndFinger.active=!0),this.showTargetNd(this._guideInfo.TargetNode));else{if(this._ndJump&&this._guideInfo.JumpCb){var t=e.getUILocation(),i=this._ndJump.getComponent(a).convertToWorldSpaceAR(_.ZERO),r=Math.max(this._ndJump.getComponent(a).width/2,50);if(t.subtract(v(i.x,i.y)).length()<=r)return void this.onJumpCb()}if(this._guideInfo.IsFullTouch||!this._guideInfo.TargetNode)this.doTaregetNdTouch();else{var n=e.getUILocation(),o=this._guideInfo.TargetNode.getWorldPosition(),s=Math.max(this._guideInfo.TargetNode.getComponent(a).width/2,50);if(n.subtract(v(o.x,o.y)).length()<=s)return void this.doTaregetNdTouch()}}else l("延迟点击")},r.onJumpCb=function(){this.targetNodeBackLayer(),this._guideInfo.JumpCb&&this._guideInfo.JumpCb(),l("doJumpTouch")},r.doTaregetNdTouch=function(){this.targetNodeBackLayer(),this._guideInfo.TouchCb&&this._guideInfo.TouchCb(),l("doTaregetNdTouch or fullTouch")},r.targetNodeBackLayer=function(){this._guideInfo&&this._guideInfo.TargetNode&&(this._orgTargetNdGroupIndex&&this.resetTargetNdLayer(this._guideInfo.TargetNode,this._orgTargetNdGroupIndex),this._orgTargetNdParent&&(this._guideInfo.TargetNode.parent=this._orgTargetNdParent),this._orgTargetNdPos&&this._guideInfo.TargetNode.setPosition(this._orgTargetNdPos),this._guideInfo.TargetNode&&this.resetAbleButtonTouch(this._guideInfo.TargetNode,!0)),this._orgTargetNdParent=null,this._orgTargetNdPos=null,this._orgTargetNdGroupIndex=null},r.clearTimer=function(){this._richtxtEffTimer&&clearInterval(this._richtxtEffTimer),this._txtEffTimer&&clearInterval(this._txtEffTimer),this._delayShowGuideTimer&&clearTimeout(this._delayShowGuideTimer),this._delayShowFingerTimer&&clearTimeout(this._delayShowFingerTimer),this._delayShowTargetNdTimer&&clearTimeout(this._delayShowTargetNdTimer),this._delayTouchGuideTimer&&clearTimeout(this._delayTouchGuideTimer),this._autoHideGuideTimer&&clearTimeout(this._autoHideGuideTimer)},i(e,null,[{key:"Instance",get:function(){return this._instance||(this._instance=new e),this._instance}}]),e}())._instance=null,r._RF.pop()}}}));

System.register("chunks:///_virtual/HttpRequest.ts",["./rollupPluginModLoBabelHelpers.js","cc","./Singleton.ts"],(function(e){var t,s,n,a,r,o,i;return{setters:[function(e){t=e.inheritsLoose,s=e.extends,n=e.asyncToGenerator,a=e.regeneratorRuntime},function(e){r=e.cclegacy,o=e.error},function(e){i=e.Singleton}],execute:function(){function u(e){if("string"==typeof e)try{return JSON.parse(e),!0}catch(e){return!1}return!1}r._RF.push({},"0b4b4MEpZVCEZT0cChFXoDg","HttpRequest",void 0);e("HttpRequest",function(e){function r(){for(var t,s=arguments.length,n=new Array(s),a=0;a<s;a++)n[a]=arguments[a];return(t=e.call.apply(e,[this].concat(n))||this).requestFulfilled=void 0,t.responseFulfilled=void 0,t.defaults={timeout:5e3,headers:{},params:{}},t}t(r,e);var i=r.prototype;return i.addHeader=function(e,t){this.defaults.headers[e]=t},i.mergeDefaultConfig=function(e){return e.baseURL||(e.baseURL=this.defaults.baseURL),this.defaults.headers&&(e.headers?e.headers=s({},this.defaults.headers,e.headers):e.headers=s({},this.defaults.headers)),this.defaults.params&&(e.params?e.params=s({},this.defaults.params,e.params):e.params=s({},this.defaults.params)),e},i.formatConfig=function(e){return e.baseURL&&e.baseURL.endsWith("/")&&(e.baseURL=e.baseURL.substring(0,e.baseURL.length-1)),e.path&&!e.path.startsWith("/")&&(e.path="/"+e.path),e},i.doRequest=function(e){var t=this;return new Promise(n(a().mark((function n(r,i){var p,d,l,f,h,c,m;return a().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:for(f in t.requestFulfilled&&(p=t.requestFulfilled(s({},e)))&&(e=p),d=function(e){e.response&&(u(e.response)?e.data=JSON.parse(e.response):e.data=e.response,delete e.response),t.responseFulfilled&&(e=t.responseFulfilled(e))instanceof Error?i(e):r(e)},l="",e.params)null!==e.params[f]&&void 0!==e.params[f]&&(l+=l?"&"+f+"="+e.params[f]:"?"+f+"="+e.params[f]);for(m in h=""+e.baseURL+e.path+l,(c=new XMLHttpRequest).timeout=e.timeout,c.open(e.method,h),e.headers)null!==e.headers[m]&&void 0!==e.headers[m]&&c.setRequestHeader(m,e.headers[m]);c.onloadend=function(){var t=c.response,s=c.readyState,n=c.responseURL,a=c.status,r={response:t,readyState:s,responseURL:n,status:a,path:e.path};4==c.readyState&&c.status>=200&&c.status<400?d(r):(console.log("onloadend-error:",r),o(a),i(a))},!e.data||"application/json"!==e.headers["content-type"]&&"application/json"!==e.headers["Content-Type"]?c.send(e.data):c.send(JSON.stringify(e.data));case 11:case"end":return n.stop()}}),n)}))))},i.raw=function(e,t,s,n){return void 0===t&&(t="GET"),new Promise((function(a,r){var o=new XMLHttpRequest;if(o.open(t,e),n)for(var i in n)null!==n[i]&&void 0!==n[i]&&o.setRequestHeader(i,n[i]);o.onloadend=function(){4==o.readyState&&o.status>=200&&o.status<400?u(o.response)?a(JSON.parse(o.response)):a(o.response):r(o.status)},s&&n&&("application/json"===n["content-type"]||"application/json"===n["Content-Type"])?o.send(JSON.stringify(s)):o.send(s)}))},i.get=function(e,t){return(t=t||{}).method="GET",t.path=e,t=this.mergeDefaultConfig(t),t=this.formatConfig(t),this.doRequest(t)},i.post=function(e,t,s){return(s=s||{}).data=t,s.method="POST",s.path=e,s=this.mergeDefaultConfig(s),s=this.formatConfig(s),this.doRequest(s)},i.setRequestInterceptor=function(e){this.requestFulfilled=e},i.setResponseInterceptor=function(e){this.responseFulfilled=e},r}(i));r._RF.pop()}}}));

System.register("chunks:///_virtual/ImageUtil.ts",["cc"],(function(e){var t,n,r,o;return{setters:[function(e){t=e.cclegacy,n=e.Color,r=e.Texture2D,o=e.ImageAsset}],execute:function(){t._RF.push({},"7360ek2JfFC0L0PGA6i3cHC","ImageUtil",void 0);e("ImageUtil",function(){function e(){}return e.getPixelColor=function(e,t,r){var o=document.createElement("canvas"),a=o.getContext("2d");o.width=e.width,o.height=e.height;var i=e.getHtmlElementObj();a.drawImage(i,0,0,e.width,e.height);var c=a.getImageData(0,0,e.width,e.height),g=(r-1)*e.width*4+4*(t-1),u=c.data.slice(g,g+4),l=new n(u[0],u[1],u[2],u[3]);return i.remove(),o.remove(),l},e.imageToBase64=function(e,t){return new Promise((function(n){var r,o=null==(r=/\.png|\.jpg|\.jpeg/.exec(e))?void 0:r[0];if([".png",".jpg",".jpeg"].includes(o)){var a=document.createElement("canvas"),i=a.getContext("2d"),c=new Image;c.src=e,c.onload=function(){a.height=c.height,a.width=c.width,i.drawImage(c,0,0),o=".jpg"===o?"jpeg":o.replace(".","");var e=a.toDataURL("image/"+o);t&&t(e),n(e),c.remove(),a.remove()}}else console.warn("Not a jpg/jpeg or png resource!"),t&&t(""),n("")}))},e.base64ToTexture=function(e,t){var n=document.createElement("img");n.src=e,n.onerror=function(e){t(null)},n.onload=function(){var e=new r;e.image=new o(n),n.remove(),t(e)}},e.base64ToBlob=function(e){for(var t=e.split(","),n=/image\/\w+|;/.exec(t[0])[0],r=window.atob(t[1]),o=new ArrayBuffer(r.length),a=new Uint8Array(o),i=0;i<r.length;i++)a[i]=255&r.charCodeAt(i);return new Blob([a],{type:n})},e}());t._RF.pop()}}}));

System.register("chunks:///_virtual/kdtree.ts",["cc","./RVOMath.ts","./Simulator.ts"],(function(e){var t,i,n,s;return{setters:[function(e){t=e.cclegacy},function(e){i=e.RVOMath,n=e.Obstacle},function(e){s=e.Simulator}],execute:function(){t._RF.push({},"606ceQUrEZLCacNAPsWxW5W","kdtree",void 0);var r=function(){function e(e,t){this.a=void 0,this.b=void 0,this.a=e,this.b=t}var t=e.prototype;return t.lessThan=function(e){return this.a<e.a||!(e.a<this.a)&&this.b<e.b},t.lessEqualThan=function(e){return this.a==e.a&&this.b==e.b||this.lessThan(e)},t.bigThan=function(e){return!this.lessEqualThan(e)},t.bigEqualThan=function(e){return!this.lessThan(e)},e}(),a=function(){this.begin=void 0,this.end=void 0,this.left=void 0,this.right=void 0,this.maxX=void 0,this.maxY=void 0,this.minX=void 0,this.minY=void 0},h=function(){this.obstacle=void 0,this.left=void 0,this.right=void 0};e("KdTree",function(){function e(){this.MAX_LEAF_SIZE=10,this.agents=null,this.agentTree=[],this.obstacleTree=null}var t=e.prototype;return t.buildAgentTree=function(e){if(!this.agents||this.agents.length!=e){this.agents=new Array(e);for(var t=0;t<this.agents.length;t++)this.agents[t]=s.instance.getAgent(t);this.agentTree=new Array(2*this.agents.length);for(var i=0;i<this.agentTree.length;i++)this.agentTree[i]=new a}0!=this.agents.length&&this.buildAgentTreeRecursive(0,this.agents.length,0)},t.buildObstacleTree=function(){this.obstacleTree=new h;for(var e=new Array(s.instance.obstacles.length),t=0;t<e.length;t++)e[t]=s.instance.obstacles[t];this.obstacleTree=this.buildObstacleTreeRecursive(e)},t.computeAgentNeighbors=function(e,t){return this.queryAgentTreeRecursive(e,t,0)},t.computeObstacleNeighbors=function(e,t){this.queryObstacleTreeRecursive(e,t,this.obstacleTree)},t.queryVisibility=function(e,t,i){return this.queryVisibilityRecursive(e,t,i,this.obstacleTree)},t.buildAgentTreeRecursive=function(e,t,i){this.agentTree[i].begin=e,this.agentTree[i].end=t,this.agentTree[i].minX=this.agentTree[i].maxX=this.agents[e].position_.x,this.agentTree[i].minY=this.agentTree[i].maxY=this.agents[e].position_.y;for(var n=e+1;n<t;++n)this.agentTree[i].maxX=Math.max(this.agentTree[i].maxX,this.agents[n].position_.x),this.agentTree[i].minX=Math.min(this.agentTree[i].minX,this.agents[n].position_.x),this.agentTree[i].maxY=Math.max(this.agentTree[i].maxY,this.agents[n].position_.y),this.agentTree[i].minY=Math.min(this.agentTree[i].minY,this.agents[n].position_.y);if(t-e>this.MAX_LEAF_SIZE){for(var s=this.agentTree[i].maxX-this.agentTree[i].minX>this.agentTree[i].maxY-this.agentTree[i].minY,r=.5*(s?this.agentTree[i].maxX+this.agentTree[i].minX:this.agentTree[i].maxY+this.agentTree[i].minY),a=e,h=t;a<h;){for(;a<h&&(s?this.agents[a].position_.x:this.agents[a].position_.y)<r;)++a;for(;h>a&&(s?this.agents[h-1].position_.x:this.agents[h-1].position_.y)>=r;)--h;if(a<h){var o=this.agents[a];this.agents[a]=this.agents[h-1],this.agents[h-1]=o,++a,--h}}var g=a-e;0==g&&(++g,++a,++h),this.agentTree[i].left=i+1,this.agentTree[i].right=i+2*g,this.buildAgentTreeRecursive(e,a,this.agentTree[i].left),this.buildAgentTreeRecursive(a,t,this.agentTree[i].right)}},t.buildObstacleTreeRecursive=function(e){if(0==e.length)return null;for(var t=new h,a=0,o=e.length,g=o,u=0;u<e.length;++u){for(var l=0,c=0,T=e[u],f=T.next,v=0;v<e.length;v++)if(u!=v){var p=e[v],b=p.next,m=i.leftOf(T.point,f.point,p.point),x=i.leftOf(T.point,f.point,b.point);m>=-i.RVO_EPSILON&&x>=-i.RVO_EPSILON?++l:(m<=i.RVO_EPSILON&&x<=i.RVO_EPSILON||++l,++c);var q=new r(Math.max(l,c),Math.min(l,c)),y=new r(Math.max(o,g),Math.min(o,g));if(q.bigEqualThan(y))break}var R=new r(Math.max(l,c),Math.min(l,c)),O=new r(Math.max(o,g),Math.min(o,g));R.lessThan(O)&&(o=l,g=c,a=u)}for(var d=[],_=0;_<o;++_)d.push(null);for(var M=[],A=0;A<g;++A)M.push(null);for(var V=0,E=0,X=a,S=e[X],Y=S.next,N=0;N<e.length;++N)if(X!=N){var L=e[N],w=L.next,I=i.leftOf(S.point,Y.point,L.point),P=i.leftOf(S.point,Y.point,w.point);if(I>=-i.RVO_EPSILON&&P>=-i.RVO_EPSILON)d[V++]=e[N];else if(I<=i.RVO_EPSILON&&P<=i.RVO_EPSILON)M[E++]=e[N];else{var F=i.det(Y.point.minus(S.point),L.point.minus(S.point))/i.det(Y.point.minus(S.point),L.point.minus(w.point)),k=L.point.plus(w.point.minus(L.point).scale(F)),Z=new n;Z.point=k,Z.previous=L,Z.next=w,Z.convex=!0,Z.direction=L.direction,Z.id=s.instance.obstacles.length,s.instance.obstacles.push(Z),L.next=Z,w.previous=Z,I>0?(d[V++]=L,M[E++]=Z):(M[E++]=L,d[V++]=Z)}}return t.obstacle=S,t.left=this.buildObstacleTreeRecursive(d),t.right=this.buildObstacleTreeRecursive(M),t},t.queryAgentTreeRecursive=function(e,t,n){if(this.agentTree[n].end-this.agentTree[n].begin<=this.MAX_LEAF_SIZE)for(var s=this.agentTree[n].begin;s<this.agentTree[n].end;++s)t=e.insertAgentNeighbor(this.agents[s],t);else{var r=i.sqr(Math.max(0,this.agentTree[this.agentTree[n].left].minX-e.position_.x))+i.sqr(Math.max(0,e.position_.x-this.agentTree[this.agentTree[n].left].maxX))+i.sqr(Math.max(0,this.agentTree[this.agentTree[n].left].minY-e.position_.y))+i.sqr(Math.max(0,e.position_.y-this.agentTree[this.agentTree[n].left].maxY)),a=i.sqr(Math.max(0,this.agentTree[this.agentTree[n].right].minX-e.position_.x))+i.sqr(Math.max(0,e.position_.x-this.agentTree[this.agentTree[n].right].maxX))+i.sqr(Math.max(0,this.agentTree[this.agentTree[n].right].minY-e.position_.y))+i.sqr(Math.max(0,e.position_.y-this.agentTree[this.agentTree[n].right].maxY));r<a?r<t&&a<(t=this.queryAgentTreeRecursive(e,t,this.agentTree[n].left))&&(t=this.queryAgentTreeRecursive(e,t,this.agentTree[n].right)):a<t&&r<(t=this.queryAgentTreeRecursive(e,t,this.agentTree[n].right))&&(t=this.queryAgentTreeRecursive(e,t,this.agentTree[n].left))}return t},t.queryObstacleTreeRecursive=function(e,t,n){if(null==n)return t;var s=n.obstacle,r=s.next,a=i.leftOf(s.point,r.point,e.position_);return t=this.queryObstacleTreeRecursive(e,t,a>=0?n.left:n.right),i.sqr(a)/i.absSq(r.point.minus(s.point))<t&&(a<0&&e.insertObstacleNeighbor(n.obstacle,t),this.queryObstacleTreeRecursive(e,t,a>=0?n.right:n.left)),t},t.queryVisibilityRecursive=function(e,t,n,s){if(null==s)return!0;var r=s.obstacle,a=r.next,h=i.leftOf(r.point,a.point,e),o=i.leftOf(r.point,a.point,t),g=1/i.absSq(a.point.minus(r.point));if(h>=0&&o>=0)return this.queryVisibilityRecursive(e,t,n,s.left)&&(i.sqr(h)*g>=i.sqr(n)&&i.sqr(o)*g>=i.sqr(n)||this.queryVisibilityRecursive(e,t,n,s.right));if(h<=0&&o<=0)return this.queryVisibilityRecursive(e,t,n,s.right)&&(i.sqr(h)*g>=i.sqr(n)&&i.sqr(o)*g>=i.sqr(n)||this.queryVisibilityRecursive(e,t,n,s.left));if(h>=0&&o<=0)return this.queryVisibilityRecursive(e,t,n,s.left)&&this.queryVisibilityRecursive(e,t,n,s.right);var u=i.leftOf(e,t,r.point),l=i.leftOf(e,t,a.point),c=1/i.absSq(t.minus(e));return u*l>=0&&i.sqr(u)*c>i.sqr(n)&&i.sqr(l)*c>i.sqr(n)&&this.queryVisibilityRecursive(e,t,n,s.left)&&this.queryVisibilityRecursive(e,t,n,s.right)},e}());t._RF.pop()}}}));

System.register("chunks:///_virtual/List.ts",["./rollupPluginModLoBabelHelpers.js","cc","./env","./ListItem.ts"],(function(t){var e,i,s,o,n,a,l,r,c,h,_,d,u,m,p,g,f,v,I,y,T,S,w,z,D,N,b;return{setters:[function(t){e=t.applyDecoratedDescriptor,i=t.inheritsLoose,s=t.initializerDefineProperty,o=t.assertThisInitialized,n=t.createClass},function(t){a=t.cclegacy,l=t._decorator,r=t.ScrollView,c=t.Enum,h=t.Node,_=t.Prefab,d=t.CCFloat,u=t.EventHandler,m=t.CCBoolean,p=t.CCInteger,g=t.isValid,f=t.UITransform,v=t.Layout,I=t.instantiate,y=t.NodePool,T=t.Vec3,S=t.Size,w=t.Widget,z=t.tween,D=t.Component},function(t){N=t.DEV},function(t){b=t.default}],execute:function(){var E,P,L,G,C,O,R,A,U,B,k,M,x,V,F,H,X,Y,Z,W,j,q,K,J,Q,$,tt,et,it,st,ot,nt,at,lt,rt,ct,ht,_t,dt,ut,mt,pt,gt;a._RF.push({},"1465aK3BeFERYvLMRafKnXR","List",void 0);var ft=l.ccclass,vt=l.property,It=l.disallowMultiple,yt=l.menu,Tt=l.executionOrder,St=l.requireComponent,wt=function(t){return t[t.NODE=1]="NODE",t[t.PREFAB=2]="PREFAB",t}(wt||{}),zt=function(t){return t[t.NORMAL=1]="NORMAL",t[t.ADHERING=2]="ADHERING",t[t.PAGE=3]="PAGE",t}(zt||{}),Dt=function(t){return t[t.NONE=0]="NONE",t[t.SINGLE=1]="SINGLE",t[t.MULT=2]="MULT",t}(Dt||{});t("default",(E=It(),P=yt("List"),L=St(r),G=Tt(-5e3),C=vt({type:c(wt),tooltip:N}),O=vt({type:h,tooltip:N,visible:function(){return this.templateType==wt.NODE}}),R=vt({type:_,tooltip:N,visible:function(){return this.templateType==wt.PREFAB}}),A=vt({}),U=vt({type:c(zt),tooltip:N}),B=vt({type:d,range:[0,1,.1],tooltip:N,slide:!0,visible:function(){return this._slideMode==zt.PAGE}}),k=vt({type:u,tooltip:N,visible:function(){return this._slideMode==zt.PAGE}}),M=vt({}),x=vt({type:m,tooltip:N}),V=vt({tooltip:N,visible:function(){var t=this.slideMode==zt.NORMAL;return t||(this.cyclic=!1),t}}),F=vt({tooltip:N,visible:function(){return this.virtual}}),H=vt({tooltip:N,visible:function(){var t=this.virtual&&!this.lackCenter;return t||(this.lackSlide=!1),t}}),X=vt({type:p}),Y=vt({type:p,range:[0,6,1],tooltip:N,slide:!0}),Z=vt({type:p,range:[0,12,1],tooltip:N,slide:!0}),W=vt({type:u,tooltip:N}),j=vt({type:c(Dt),tooltip:N}),q=vt({type:u,tooltip:N,visible:function(){return this.selectedMode>Dt.NONE}}),K=vt({tooltip:N,visible:function(){return this.selectedMode==Dt.SINGLE}}),J=vt({serializable:!1}),ft(Q=E(Q=P(Q=L(Q=G((tt=e(($=function(t){function e(){for(var e,i=arguments.length,n=new Array(i),a=0;a<i;a++)n[a]=arguments[a];return e=t.call.apply(t,[this].concat(n))||this,s(e,"templateType",tt,o(e)),s(e,"tmpNode",et,o(e)),s(e,"tmpPrefab",it,o(e)),s(e,"_slideMode",st,o(e)),s(e,"pageDistance",ot,o(e)),s(e,"pageChangeEvent",nt,o(e)),s(e,"_virtual",at,o(e)),s(e,"cyclic",lt,o(e)),s(e,"lackCenter",rt,o(e)),s(e,"lackSlide",ct,o(e)),s(e,"_updateRate",ht,o(e)),s(e,"frameByFrameRenderNum",_t,o(e)),s(e,"renderEvent",dt,o(e)),s(e,"selectedMode",ut,o(e)),s(e,"selectedEvent",mt,o(e)),s(e,"repeatEventSingle",pt,o(e)),e._selectedId=-1,e._lastSelectedId=void 0,e.multSelected=void 0,e._forceUpdate=!1,e._align=void 0,e._horizontalDir=void 0,e._verticalDir=void 0,e._startAxis=void 0,e._alignCalcType=void 0,e.content=void 0,e._contentUt=void 0,e.firstListId=void 0,e.displayItemNum=void 0,e._updateDone=!0,e._updateCounter=void 0,e._actualNumItems=void 0,e._cyclicNum=void 0,e._cyclicPos1=void 0,e._cyclicPos2=void 0,s(e,"_numItems",gt,o(e)),e._inited=!1,e._scrollView=void 0,e._layout=void 0,e._resizeMode=void 0,e._topGap=void 0,e._rightGap=void 0,e._bottomGap=void 0,e._leftGap=void 0,e._columnGap=void 0,e._lineGap=void 0,e._colLineNum=void 0,e._lastDisplayData=void 0,e.displayData=void 0,e._pool=void 0,e._itemTmp=void 0,e._itemTmpUt=void 0,e._needUpdateWidget=!1,e._itemSize=void 0,e._sizeType=void 0,e._customSize=void 0,e.frameCount=void 0,e._aniDelRuning=!1,e._aniDelCB=void 0,e._aniDelItem=void 0,e._aniDelBeforePos=void 0,e._aniDelBeforeScale=void 0,e.viewTop=void 0,e.viewRight=void 0,e.viewBottom=void 0,e.viewLeft=void 0,e._doneAfterUpdate=!1,e.elasticTop=void 0,e.elasticRight=void 0,e.elasticBottom=void 0,e.elasticLeft=void 0,e.scrollToListId=void 0,e.adhering=!1,e._adheringBarrier=!1,e.nearestListId=void 0,e.curPageNum=0,e._beganPos=void 0,e._scrollPos=void 0,e._curScrollIsTouch=void 0,e._scrollToListId=void 0,e._scrollToEndTime=void 0,e._scrollToSo=void 0,e._lack=void 0,e._allItemSize=void 0,e._allItemSizeNoEdge=void 0,e._scrollItem=void 0,e._thisNodeUt=void 0,e}i(e,t);var a=e.prototype;return a.onLoad=function(){this._init()},a.onDestroy=function(){var t=this;g(t._itemTmp)&&t._itemTmp.destroy(),g(t.tmpNode)&&t.tmpNode.destroy(),t._pool&&t._pool.clear()},a.onEnable=function(){this._registerEvent(),this._init(),this._aniDelRuning&&(this._aniDelRuning=!1,this._aniDelItem&&(this._aniDelBeforePos&&(this._aniDelItem.position=this._aniDelBeforePos,delete this._aniDelBeforePos),this._aniDelBeforeScale&&(this._aniDelItem.scale=this._aniDelBeforeScale,delete this._aniDelBeforeScale),delete this._aniDelItem),this._aniDelCB&&(this._aniDelCB(),delete this._aniDelCB))},a.onDisable=function(){this._unregisterEvent()},a._registerEvent=function(){var t=this;t.node.on(h.EventType.TOUCH_START,t._onTouchStart,t),t.node.on("touch-up",t._onTouchUp,t),t.node.on(h.EventType.TOUCH_CANCEL,t._onTouchCancelled,t),t.node.on("scroll-began",t._onScrollBegan,t),t.node.on("scroll-ended",t._onScrollEnded,t),t.node.on("scrolling",t._onScrolling,t),t.node.on(h.EventType.SIZE_CHANGED,t._onSizeChanged,t)},a._unregisterEvent=function(){var t=this;t.node.off(h.EventType.TOUCH_START,t._onTouchStart,t),t.node.off("touch-up",t._onTouchUp,t),t.node.off(h.EventType.TOUCH_CANCEL,t._onTouchCancelled,t),t.node.off("scroll-began",t._onScrollBegan,t),t.node.off("scroll-ended",t._onScrollEnded,t),t.node.off("scrolling",t._onScrolling,t),t.node.off(h.EventType.SIZE_CHANGED,t._onSizeChanged,t)},a._init=function(){var t=this;if(!t._inited)if(t._thisNodeUt=t.node.getComponent(f),t._scrollView=t.node.getComponent(r),t.content=t._scrollView.content,t._contentUt=t.content.getComponent(f),t.content){switch(t._layout=t.content.getComponent(v),t._align=t._layout.type,t._resizeMode=t._layout.resizeMode,t._startAxis=t._layout.startAxis,t._topGap=t._layout.paddingTop,t._rightGap=t._layout.paddingRight,t._bottomGap=t._layout.paddingBottom,t._leftGap=t._layout.paddingLeft,t._columnGap=t._layout.spacingX,t._lineGap=t._layout.spacingY,t._colLineNum,t._verticalDir=t._layout.verticalDirection,t._horizontalDir=t._layout.horizontalDirection,t.setTemplateItem(I(t.templateType==wt.PREFAB?t.tmpPrefab:t.tmpNode)),t._slideMode!=zt.ADHERING&&t._slideMode!=zt.PAGE||(t._scrollView.inertia=!1,t._scrollView._onMouseWheel=function(){}),t.virtual||(t.lackCenter=!1),t._lastDisplayData=[],t.displayData=[],t._pool=new y,t._forceUpdate=!1,t._updateCounter=0,t._updateDone=!0,t.curPageNum=0,t.cyclic&&(t._scrollView._processAutoScrolling=this._processAutoScrolling.bind(t),t._scrollView._startBounceBackIfNeeded=function(){return!1}),t._align){case v.Type.HORIZONTAL:switch(t._horizontalDir){case v.HorizontalDirection.LEFT_TO_RIGHT:t._alignCalcType=1;break;case v.HorizontalDirection.RIGHT_TO_LEFT:t._alignCalcType=2}break;case v.Type.VERTICAL:switch(t._verticalDir){case v.VerticalDirection.TOP_TO_BOTTOM:t._alignCalcType=3;break;case v.VerticalDirection.BOTTOM_TO_TOP:t._alignCalcType=4}break;case v.Type.GRID:switch(t._startAxis){case v.AxisDirection.HORIZONTAL:switch(t._verticalDir){case v.VerticalDirection.TOP_TO_BOTTOM:t._alignCalcType=3;break;case v.VerticalDirection.BOTTOM_TO_TOP:t._alignCalcType=4}break;case v.AxisDirection.VERTICAL:switch(t._horizontalDir){case v.HorizontalDirection.LEFT_TO_RIGHT:t._alignCalcType=1;break;case v.HorizontalDirection.RIGHT_TO_LEFT:t._alignCalcType=2}}}t.content.removeAllChildren(),t._inited=!0}else console.error(t.node.name+"'s ScrollView unset content!")},a._processAutoScrolling=function(t){var e=1e-4,i=new T,s=this._scrollView,o=s._isNecessaryAutoScrollBrake(),n=o?.05:1;s._autoScrollAccumulatedTime+=t*(1/n);var a,l=Math.min(1,s._autoScrollAccumulatedTime/s._autoScrollTotalTime);s._autoScrollAttenuate&&(a=l,l=(a-=1)*a*a*a*a+1);var c=s._autoScrollTargetDelta.clone();c.multiplyScalar(l);var h=s._autoScrollStartPosition.clone();h.add(c);var _=Math.abs(l-1)<=e;if(Math.abs(l-1)<=s.getScrollEndedEventTiming()&&!s._isScrollEndedWithThresholdEventFired&&(s._dispatchEvent(r.EventType.SCROLL_ENG_WITH_THRESHOLD),s._isScrollEndedWithThresholdEventFired=!0),s.elastic){var d=h.clone();d.subtract(s._autoScrollBrakingStartPosition),o&&d.multiplyScalar(n),h.set(s._autoScrollBrakingStartPosition),h.add(d)}else{var u=h.clone();u.subtract(s._getContentPosition());var m=s._getHowMuchOutOfBoundary(u);m.equals(i,e)||(h.add(m),_=!0)}_&&(s._autoScrolling=!1);var p=new T(h);p.subtract(s._getContentPosition()),s._clampDelta(p),s._moveContent(p,_),s._dispatchEvent(r.EventType.SCROLLING),s._autoScrolling||(s._isBouncing=!1,s._scrolling=!1,s._dispatchEvent(r.EventType.SCROLL_ENDED))},a.setTemplateItem=function(t){if(t){var e=this;if(e._itemTmp=t,e._itemTmpUt=t.getComponent(f),e._resizeMode==v.ResizeMode.CHILDREN)e._itemSize=e._layout.cellSize;else{var i=t.getComponent(f);e._itemSize=new S(i.width,i.height)}var s=t.getComponent(b),o=!1;switch(s||(o=!0),o&&(e.selectedMode=Dt.NONE),(s=t.getComponent(w))&&s.enabled&&(e._needUpdateWidget=!0),e.selectedMode==Dt.MULT&&(e.multSelected=[]),e._align){case v.Type.HORIZONTAL:e._colLineNum=1,e._sizeType=!1;break;case v.Type.VERTICAL:e._colLineNum=1,e._sizeType=!0;break;case v.Type.GRID:switch(e._startAxis){case v.AxisDirection.HORIZONTAL:var n=e._contentUt.width-e._leftGap-e._rightGap;e._colLineNum=Math.floor((n+e._columnGap)/(e._itemSize.width+e._columnGap)),e._sizeType=!0;break;case v.AxisDirection.VERTICAL:var a=e._contentUt.height-e._topGap-e._bottomGap;e._colLineNum=Math.floor((a+e._lineGap)/(e._itemSize.height+e._lineGap)),e._sizeType=!1}}}},a.checkInited=function(t){return void 0===t&&(t=!0),!!this._inited||(t&&console.error("List initialization not completed!"),!1)},a._resizeContent=function(){var t,e=this;switch(e._align){case v.Type.HORIZONTAL:if(e._customSize){var i=e._getFixedSize(null);t=e._leftGap+i.val+e._itemSize.width*(e._numItems-i.count)+e._columnGap*(e._numItems-1)+e._rightGap}else t=e._leftGap+e._itemSize.width*e._numItems+e._columnGap*(e._numItems-1)+e._rightGap;break;case v.Type.VERTICAL:if(e._customSize){var s=e._getFixedSize(null);t=e._topGap+s.val+e._itemSize.height*(e._numItems-s.count)+e._lineGap*(e._numItems-1)+e._bottomGap}else t=e._topGap+e._itemSize.height*e._numItems+e._lineGap*(e._numItems-1)+e._bottomGap;break;case v.Type.GRID:switch(e.lackCenter&&(e.lackCenter=!1),e._startAxis){case v.AxisDirection.HORIZONTAL:var o=Math.ceil(e._numItems/e._colLineNum);t=e._topGap+e._itemSize.height*o+e._lineGap*(o-1)+e._bottomGap;break;case v.AxisDirection.VERTICAL:var n=Math.ceil(e._numItems/e._colLineNum);t=e._leftGap+e._itemSize.width*n+e._columnGap*(n-1)+e._rightGap}}var a=e.content.getComponent(v);if(a&&(a.enabled=!1),e._allItemSize=t,e._allItemSizeNoEdge=e._allItemSize-(e._sizeType?e._topGap+e._bottomGap:e._leftGap+e._rightGap),e.cyclic){var l=e._sizeType?e._thisNodeUt.height:e._thisNodeUt.width;e._cyclicPos1=0,l-=e._cyclicPos1,e._cyclicNum=Math.ceil(l/e._allItemSizeNoEdge)+1;var r=e._sizeType?e._lineGap:e._columnGap;e._cyclicPos2=e._cyclicPos1+e._allItemSizeNoEdge+r,e._cyclicAllItemSize=e._allItemSize+e._allItemSizeNoEdge*(e._cyclicNum-1)+r*(e._cyclicNum-1),e._cycilcAllItemSizeNoEdge=e._allItemSizeNoEdge*e._cyclicNum,e._cycilcAllItemSizeNoEdge+=r*(e._cyclicNum-1)}e._lack=!e.cyclic&&e._allItemSize<(e._sizeType?e._thisNodeUt.height:e._thisNodeUt.width);var c=e._lack&&e.lackCenter||!e.lackSlide?.1:0,h=e._lack?(e._sizeType?e._thisNodeUt.height:e._thisNodeUt.width)-c:e.cyclic?e._cyclicAllItemSize:e._allItemSize;h<0&&(h=0),e._sizeType?e._contentUt.height=h:e._contentUt.width=h},a._onScrolling=function(t){if(void 0===t&&(t=null),null==this.frameCount&&(this.frameCount=this._updateRate),!this._forceUpdate&&t&&"scroll-ended"!=t.type&&this.frameCount>0)this.frameCount--;else if(this.frameCount=this._updateRate,!this._aniDelRuning){if(this.cyclic){var e=this.content.getPosition();e=this._sizeType?e.y:e.x;var i=this._allItemSizeNoEdge+(this._sizeType?this._lineGap:this._columnGap),s=this._sizeType?new T(0,i,0):new T(i,0,0),o=this.content.getPosition();switch(this._alignCalcType){case 1:e>-this._cyclicPos1?(o.set(-this._cyclicPos2,o.y,o.z),this.content.setPosition(o),this._scrollView.isAutoScrolling()&&(this._scrollView._autoScrollStartPosition=this._scrollView._autoScrollStartPosition.subtract(s))):e<-this._cyclicPos2&&(o.set(-this._cyclicPos1,o.y,o.z),this.content.setPosition(o),this._scrollView.isAutoScrolling()&&(this._scrollView._autoScrollStartPosition=this._scrollView._autoScrollStartPosition.add(s)));break;case 2:e<this._cyclicPos1?(o.set(this._cyclicPos2,o.y,o.z),this.content.setPosition(o),this._scrollView.isAutoScrolling()&&(this._scrollView._autoScrollStartPosition=this._scrollView._autoScrollStartPosition.add(s))):e>this._cyclicPos2&&(o.set(this._cyclicPos1,o.y,o.z),this.content.setPosition(o),this._scrollView.isAutoScrolling()&&(this._scrollView._autoScrollStartPosition=this._scrollView._autoScrollStartPosition.subtract(s)));break;case 3:e<this._cyclicPos1?(o.set(o.x,this._cyclicPos2,o.z),this.content.setPosition(o),this._scrollView.isAutoScrolling()&&(this._scrollView._autoScrollStartPosition=this._scrollView._autoScrollStartPosition.add(s))):e>this._cyclicPos2&&(o.set(o.x,this._cyclicPos1,o.z),this.content.setPosition(o),this._scrollView.isAutoScrolling()&&(this._scrollView._autoScrollStartPosition=this._scrollView._autoScrollStartPosition.subtract(s)));break;case 4:e>-this._cyclicPos1?(o.set(o.x,-this._cyclicPos2,o.z),this.content.setPosition(o),this._scrollView.isAutoScrolling()&&(this._scrollView._autoScrollStartPosition=this._scrollView._autoScrollStartPosition.subtract(s))):e<-this._cyclicPos2&&(o.set(o.x,-this._cyclicPos1,o.z),this.content.setPosition(o),this._scrollView.isAutoScrolling()&&(this._scrollView._autoScrollStartPosition=this._scrollView._autoScrollStartPosition.add(s)))}}var n,a,l,r;if(this._calcViewPos(),this._sizeType?(n=this.viewTop,l=this.viewBottom):(a=this.viewRight,r=this.viewLeft),this._virtual){var c;this.displayData=[];var h=0,_=this._numItems-1;if(this._customSize)for(var d=!1;h<=_&&!d;h++)switch(c=this._calcItemPos(h),this._align){case v.Type.HORIZONTAL:c.right>=r&&c.left<=a?this.displayData.push(c):0!=h&&this.displayData.length>0&&(d=!0);break;case v.Type.VERTICAL:c.bottom<=n&&c.top>=l?this.displayData.push(c):0!=h&&this.displayData.length>0&&(d=!0);break;case v.Type.GRID:switch(this._startAxis){case v.AxisDirection.HORIZONTAL:c.bottom<=n&&c.top>=l?this.displayData.push(c):0!=h&&this.displayData.length>0&&(d=!0);break;case v.AxisDirection.VERTICAL:c.right>=r&&c.left<=a?this.displayData.push(c):0!=h&&this.displayData.length>0&&(d=!0)}}else{var u=this._itemSize.width+this._columnGap,m=this._itemSize.height+this._lineGap;switch(this._alignCalcType){case 1:h=(r-this._leftGap)/u,_=(a-this._leftGap)/u;break;case 2:h=(-a-this._rightGap)/u,_=(-r-this._rightGap)/u;break;case 3:h=(-n-this._topGap)/m,_=(-l-this._topGap)/m;break;case 4:h=(l-this._bottomGap)/m,_=(n-this._bottomGap)/m}for(h=Math.floor(h)*this._colLineNum,_=Math.ceil(_)*this._colLineNum,h<0&&(h=0),--_>=this._numItems&&(_=this._numItems-1);h<=_;h++)this.displayData.push(this._calcItemPos(h))}if(this._delRedundantItem(),this.displayData.length<=0||!this._numItems)return void(this._lastDisplayData=[]);this.firstListId=this.displayData[0].id,this.displayItemNum=this.displayData.length;var p=this._lastDisplayData.length,g=this.displayItemNum!=p;if(g&&(this.frameByFrameRenderNum>0&&this._lastDisplayData.sort((function(t,e){return t-e})),g=this.firstListId!=this._lastDisplayData[0]||this.displayData[this.displayItemNum-1].id!=this._lastDisplayData[p-1]),this._forceUpdate||g)if(this.frameByFrameRenderNum>0)this._numItems>0?(this._updateDone?this._updateCounter=0:this._doneAfterUpdate=!0,this._updateDone=!1):(this._updateCounter=0,this._updateDone=!0);else{this._lastDisplayData=[];for(var f=0;f<this.displayItemNum;f++)this._createOrUpdateItem(this.displayData[f]);this._forceUpdate=!1}this._calcNearestItem()}}},a._calcViewPos=function(){var t=this.content.getPosition();switch(this._alignCalcType){case 1:this.elasticLeft=t.x>0?t.x:0,this.viewLeft=(t.x<0?-t.x:0)-this.elasticLeft,this.viewRight=this.viewLeft+this._thisNodeUt.width,this.elasticRight=this.viewRight>this._contentUt.width?Math.abs(this.viewRight-this._contentUt.width):0,this.viewRight+=this.elasticRight;break;case 2:this.elasticRight=t.x<0?-t.x:0,this.viewRight=(t.x>0?-t.x:0)+this.elasticRight,this.viewLeft=this.viewRight-this._thisNodeUt.width,this.elasticLeft=this.viewLeft<-this._contentUt.width?Math.abs(this.viewLeft+this._contentUt.width):0,this.viewLeft-=this.elasticLeft;break;case 3:this.elasticTop=t.y<0?Math.abs(t.y):0,this.viewTop=(t.y>0?-t.y:0)+this.elasticTop,this.viewBottom=this.viewTop-this._thisNodeUt.height,this.elasticBottom=this.viewBottom<-this._contentUt.height?Math.abs(this.viewBottom+this._contentUt.height):0,this.viewBottom+=this.elasticBottom;break;case 4:this.elasticBottom=t.y>0?Math.abs(t.y):0,this.viewBottom=(t.y<0?-t.y:0)-this.elasticBottom,this.viewTop=this.viewBottom+this._thisNodeUt.height,this.elasticTop=this.viewTop>this._contentUt.height?Math.abs(this.viewTop-this._contentUt.height):0,this.viewTop-=this.elasticTop}},a._calcItemPos=function(t){var e,i,s,o,n,a,l,r;switch(this._align){case v.Type.HORIZONTAL:switch(this._horizontalDir){case v.HorizontalDirection.LEFT_TO_RIGHT:if(this._customSize){var c=this._getFixedSize(t);n=this._leftGap+(this._itemSize.width+this._columnGap)*(t-c.count)+(c.val+this._columnGap*c.count);var h=this._customSize[t];e=h>0?h:this._itemSize.width}else n=this._leftGap+(this._itemSize.width+this._columnGap)*t,e=this._itemSize.width;if(this.lackCenter)n-=this._leftGap,n+=this._contentUt.width/2-this._allItemSizeNoEdge/2;return{id:t,left:n,right:a=n+e,x:n+this._itemTmpUt.anchorX*e,y:this._itemTmp.y};case v.HorizontalDirection.RIGHT_TO_LEFT:if(this._customSize){var _=this._getFixedSize(t);a=-this._rightGap-(this._itemSize.width+this._columnGap)*(t-_.count)-(_.val+this._columnGap*_.count);var d=this._customSize[t];e=d>0?d:this._itemSize.width}else a=-this._rightGap-(this._itemSize.width+this._columnGap)*t,e=this._itemSize.width;if(this.lackCenter)a+=this._rightGap,a-=this._contentUt.width/2-this._allItemSizeNoEdge/2;return{id:t,right:a,left:n=a-e,x:n+this._itemTmpUt.anchorX*e,y:this._itemTmp.y}}break;case v.Type.VERTICAL:switch(this._verticalDir){case v.VerticalDirection.TOP_TO_BOTTOM:if(this._customSize){var u=this._getFixedSize(t);s=-this._topGap-(this._itemSize.height+this._lineGap)*(t-u.count)-(u.val+this._lineGap*u.count);var m=this._customSize[t];i=m>0?m:this._itemSize.height}else s=-this._topGap-(this._itemSize.height+this._lineGap)*t,i=this._itemSize.height;if(this.lackCenter)s+=this._topGap,s-=this._contentUt.height/2-this._allItemSizeNoEdge/2;return{id:t,top:s,bottom:o=s-i,x:this._itemTmp.x,y:o+this._itemTmpUt.anchorY*i};case v.VerticalDirection.BOTTOM_TO_TOP:if(this._customSize){var p=this._getFixedSize(t);o=this._bottomGap+(this._itemSize.height+this._lineGap)*(t-p.count)+(p.val+this._lineGap*p.count);var g=this._customSize[t];i=g>0?g:this._itemSize.height}else o=this._bottomGap+(this._itemSize.height+this._lineGap)*t,i=this._itemSize.height;if(this.lackCenter)o-=this._bottomGap,o+=this._contentUt.height/2-this._allItemSizeNoEdge/2;return{id:t,top:s=o+i,bottom:o,x:this._itemTmp.x,y:o+this._itemTmpUt.anchorY*i}}case v.Type.GRID:var f=Math.floor(t/this._colLineNum);switch(this._startAxis){case v.AxisDirection.HORIZONTAL:switch(this._verticalDir){case v.VerticalDirection.TOP_TO_BOTTOM:r=(o=(s=-this._topGap-(this._itemSize.height+this._lineGap)*f)-this._itemSize.height)+this._itemTmpUt.anchorY*this._itemSize.height;break;case v.VerticalDirection.BOTTOM_TO_TOP:s=(o=this._bottomGap+(this._itemSize.height+this._lineGap)*f)+this._itemSize.height,r=o+this._itemTmpUt.anchorY*this._itemSize.height}switch(l=this._leftGap+t%this._colLineNum*(this._itemSize.width+this._columnGap),this._horizontalDir){case v.HorizontalDirection.LEFT_TO_RIGHT:l+=this._itemTmpUt.anchorX*this._itemSize.width,l-=this._contentUt.anchorX*this._contentUt.width;break;case v.HorizontalDirection.RIGHT_TO_LEFT:l+=(1-this._itemTmpUt.anchorX)*this._itemSize.width,l-=(1-this._contentUt.anchorX)*this._contentUt.width,l*=-1}return{id:t,top:s,bottom:o,x:l,y:r};case v.AxisDirection.VERTICAL:switch(this._horizontalDir){case v.HorizontalDirection.LEFT_TO_RIGHT:a=(n=this._leftGap+(this._itemSize.width+this._columnGap)*f)+this._itemSize.width,l=n+this._itemTmpUt.anchorX*this._itemSize.width,l-=this._contentUt.anchorX*this._contentUt.width;break;case v.HorizontalDirection.RIGHT_TO_LEFT:l=(n=(a=-this._rightGap-(this._itemSize.width+this._columnGap)*f)-this._itemSize.width)+this._itemTmpUt.anchorX*this._itemSize.width,l+=(1-this._contentUt.anchorX)*this._contentUt.width}switch(r=-this._topGap-t%this._colLineNum*(this._itemSize.height+this._lineGap),this._verticalDir){case v.VerticalDirection.TOP_TO_BOTTOM:r-=(1-this._itemTmpUt.anchorY)*this._itemSize.height,r+=(1-this._contentUt.anchorY)*this._contentUt.height;break;case v.VerticalDirection.BOTTOM_TO_TOP:r-=this._itemTmpUt.anchorY*this._itemSize.height,r+=this._contentUt.anchorY*this._contentUt.height,r*=-1}return{id:t,left:n,right:a,x:l,y:r}}}},a._calcExistItemPos=function(t){var e=this.getItemByListId(t);if(!e)return null;var i=e.getComponent(f),s=e.getPosition(),o={id:t,x:s.x,y:s.y};return this._sizeType?(o.top=s.y+i.height*(1-i.anchorY),o.bottom=s.y-i.height*i.anchorY):(o.left=s.x-i.width*i.anchorX,o.right=s.x+i.width*(1-i.anchorX)),o},a.getItemPos=function(t){return this._virtual||this.frameByFrameRenderNum?this._calcItemPos(t):this._calcExistItemPos(t)},a._getFixedSize=function(t){if(!this._customSize)return null;null==t&&(t=this._numItems);var e=0,i=0;for(var s in this._customSize)parseInt(s)<t&&(e+=this._customSize[s],i++);return{val:e,count:i}},a._onScrollBegan=function(){this._beganPos=this._sizeType?this.viewTop:this.viewLeft},a._onScrollEnded=function(){var t=this;if(t._curScrollIsTouch=!1,null!=t.scrollToListId){var e=t.getItemByListId(t.scrollToListId);t.scrollToListId=null,e&&z(e).to(.1,{scale:1.06}).to(.1,{scale:1}).start()}t._onScrolling(),t._slideMode!=zt.ADHERING||t.adhering?t._slideMode==zt.PAGE&&(null!=t._beganPos&&t._curScrollIsTouch?this._pageAdhere():t.adhere()):t.adhere()},a._onTouchStart=function(t,e){if(!this._scrollView._hasNestedViewGroup(t,e)&&(this._curScrollIsTouch=!0,!(t.eventPhase===Event.AT_TARGET&&t.target===this.node))){for(var i=t.target;null==i._listId&&i.parent;)i=i.parent;this._scrollItem=null!=i._listId?i:t.target}},a._onTouchUp=function(){var t=this;t._scrollPos=null,t._slideMode==zt.ADHERING?(this.adhering&&(this._adheringBarrier=!0),t.adhere()):t._slideMode==zt.PAGE&&(null!=t._beganPos?this._pageAdhere():t.adhere()),this._scrollItem=null},a._onTouchCancelled=function(t,e){var i=this;i._scrollView._hasNestedViewGroup(t,e)||t.simulate||(i._scrollPos=null,i._slideMode==zt.ADHERING?(i.adhering&&(i._adheringBarrier=!0),i.adhere()):i._slideMode==zt.PAGE&&(null!=i._beganPos?i._pageAdhere():i.adhere()),this._scrollItem=null)},a._onSizeChanged=function(){this.checkInited(!1)&&this._onScrolling()},a._onItemAdaptive=function(t){var e=t.getComponent(f);if(!this._sizeType&&e.width!=this._itemSize.width||this._sizeType&&e.height!=this._itemSize.height){this._customSize||(this._customSize={});var i=this._sizeType?e.height:e.width;this._customSize[t._listId]!=i&&(this._customSize[t._listId]=i,this._resizeContent(),this.updateAll(),null!=this._scrollToListId&&(this._scrollPos=null,this.unschedule(this._scrollToSo),this.scrollTo(this._scrollToListId,Math.max(0,this._scrollToEndTime-(new Date).getTime()/1e3))))}},a._pageAdhere=function(){var t=this;if(t.cyclic||!(t.elasticTop>0||t.elasticRight>0||t.elasticBottom>0||t.elasticLeft>0)){var e=t._sizeType?t.viewTop:t.viewLeft,i=(t._sizeType?t._thisNodeUt.height:t._thisNodeUt.width)*t.pageDistance;if(Math.abs(t._beganPos-e)>i){var s=.5;switch(t._alignCalcType){case 1:case 4:t._beganPos>e?t.prePage(s):t.nextPage(s);break;case 2:case 3:t._beganPos<e?t.prePage(s):t.nextPage(s)}}else t.elasticTop<=0&&t.elasticRight<=0&&t.elasticBottom<=0&&t.elasticLeft<=0&&t.adhere();t._beganPos=null}},a.adhere=function(){var t=this;if(t.checkInited()&&!(t.elasticTop>0||t.elasticRight>0||t.elasticBottom>0||t.elasticLeft>0)){t.adhering=!0,t._calcNearestItem();var e=(t._sizeType?t._topGap:t._leftGap)/(t._sizeType?t._thisNodeUt.height:t._thisNodeUt.width);t.scrollTo(t.nearestListId,.7,e)}},a.update=function(){if(!(this.frameByFrameRenderNum<=0||this._updateDone))if(this._virtual){for(var t=this._updateCounter+this.frameByFrameRenderNum>this.displayItemNum?this.displayItemNum:this._updateCounter+this.frameByFrameRenderNum,e=this._updateCounter;e<t;e++){var i=this.displayData[e];i&&this._createOrUpdateItem(i)}this._updateCounter>=this.displayItemNum-1?this._doneAfterUpdate?(this._updateCounter=0,this._updateDone=!1,this._doneAfterUpdate=!1):(this._updateDone=!0,this._delRedundantItem(),this._forceUpdate=!1,this._calcNearestItem(),this.slideMode==zt.PAGE&&(this.curPageNum=this.nearestListId)):this._updateCounter+=this.frameByFrameRenderNum}else if(this._updateCounter<this._numItems){for(var s=this._updateCounter+this.frameByFrameRenderNum>this._numItems?this._numItems:this._updateCounter+this.frameByFrameRenderNum,o=this._updateCounter;o<s;o++)this._createOrUpdateItem2(o);this._updateCounter+=this.frameByFrameRenderNum}else this._updateDone=!0,this._calcNearestItem(),this.slideMode==zt.PAGE&&(this.curPageNum=this.nearestListId)},a._createOrUpdateItem=function(t){var e=this.getItemByListId(t.id);if(e)this._forceUpdate&&this.renderEvent&&(e.setPosition(new T(t.x,t.y,0)),this._resetItemSize(e),this.renderEvent&&u.emitEvents([this.renderEvent],e,t.id%this._actualNumItems));else{var i=this._pool.size()>0;if(e=i?this._pool.get():I(this._itemTmp),i&&g(e)||(e=I(this._itemTmp),i=!1),e._listId!=t.id)e._listId=t.id,e.getComponent(f).setContentSize(this._itemSize);if(e.setPosition(new T(t.x,t.y,0)),this._resetItemSize(e),this.content.addChild(e),i&&this._needUpdateWidget){var s=e.getComponent(w);s&&s.updateAlignment()}e.setSiblingIndex(this.content.children.length-1);var o=e.getComponent(b);e.listItem=o,o&&(o.listId=t.id,o.list=this,o._registerEvent()),this.renderEvent&&u.emitEvents([this.renderEvent],e,t.id%this._actualNumItems)}this._resetItemSize(e),this._updateListItem(e.listItem),this._lastDisplayData.indexOf(t.id)<0&&this._lastDisplayData.push(t.id)},a._createOrUpdateItem2=function(t){var e,i=this.content.children[t];i?this._forceUpdate&&this.renderEvent&&(i._listId=t,e&&(e.listId=t),this.renderEvent&&u.emitEvents([this.renderEvent],i,t%this._actualNumItems)):((i=I(this._itemTmp))._listId=t,this.content.addChild(i),e=i.getComponent(b),i.listItem=e,e&&(e.listId=t,e.list=this,e._registerEvent()),this.renderEvent&&u.emitEvents([this.renderEvent],i,t%this._actualNumItems)),this._updateListItem(e),this._lastDisplayData.indexOf(t)<0&&this._lastDisplayData.push(t)},a._updateListItem=function(t){if(t&&this.selectedMode>Dt.NONE){var e=t.node;switch(this.selectedMode){case Dt.SINGLE:t.selected=this.selectedId==e._listId;break;case Dt.MULT:t.selected=this.multSelected.indexOf(e._listId)>=0}}},a._resetItemSize=function(t){},a._updateItemPos=function(t){var e=isNaN(t)?t:this.getItemByListId(t),i=this.getItemPos(e._listId);e.setPosition(i.x,i.y)},a.setMultSelected=function(t,e){var i=this;if(i.checkInited()){var s,o;if(Array.isArray(t)||(t=[t]),null==e)i.multSelected=t;else if(e)for(var n=t.length-1;n>=0;n--)s=t[n],(o=i.multSelected.indexOf(s))<0&&i.multSelected.push(s);else for(var a=t.length-1;a>=0;a--)s=t[a],(o=i.multSelected.indexOf(s))>=0&&i.multSelected.splice(o,1);i._forceUpdate=!0,i._onScrolling()}},a.getMultSelected=function(){return this.multSelected},a.hasMultSelected=function(t){return this.multSelected&&this.multSelected.indexOf(t)>=0},a.updateItem=function(t){if(this.checkInited()){Array.isArray(t)||(t=[t]);for(var e=0,i=t.length;e<i;e++){var s=t[e],o=this.getItemByListId(s);o&&u.emitEvents([this.renderEvent],o,s%this._actualNumItems)}}},a.updateAll=function(){this.checkInited()&&(this.numItems=this.numItems)},a.getItemByListId=function(t){if(this.content)for(var e=this.content.children.length-1;e>=0;e--){var i=this.content.children[e];if(i._listId==t)return i}},a._getOutsideItem=function(){for(var t,e=[],i=this.content.children.length-1;i>=0;i--)t=this.content.children[i],this.displayData.find((function(e){return e.id==t._listId}))||e.push(t);return e},a._delRedundantItem=function(){if(this._virtual)for(var t=this._getOutsideItem(),e=t.length-1;e>=0;e--){var i=t[e];if(!this._scrollItem||i._listId!=this._scrollItem._listId){i.isCached=!0,this._pool.put(i);for(var s=this._lastDisplayData.length-1;s>=0;s--)if(this._lastDisplayData[s]==i._listId){this._lastDisplayData.splice(s,1);break}}}else for(;this.content.children.length>this._numItems;)this._delSingleItem(this.content.children[this.content.children.length-1])},a._delSingleItem=function(t){t.removeFromParent(),t.destroy&&t.destroy(),t=null},a.aniDelItem=function(t,e,i){var s=this;if(!s.checkInited()||s.cyclic||!s._virtual)return console.error("This function is not allowed to be called!");if(!e)return console.error("CallFunc are not allowed to be NULL, You need to delete the corresponding index in the data array in the CallFunc!");if(s._aniDelRuning)return console.warn("Please wait for the current deletion to finish!");var o,n=s.getItemByListId(t);if(n){o=n.getComponent(b),s._aniDelRuning=!0,s._aniDelCB=e,s._aniDelItem=n,s._aniDelBeforePos=n.position,s._aniDelBeforeScale=n.scale;var a=s.displayData[s.displayData.length-1].id,l=o.selected;o.showAni(i,(function(){var i;if(a<s._numItems-2&&(i=a+1),null!=i){var o=s._calcItemPos(i);s.displayData.push(o),s._virtual?s._createOrUpdateItem(o):s._createOrUpdateItem2(i)}else s._numItems--;if(s.selectedMode==Dt.SINGLE)l?s._selectedId=-1:s._selectedId-1>=0&&s._selectedId--;else if(s.selectedMode==Dt.MULT&&s.multSelected.length){var r=s.multSelected.indexOf(t);r>=0&&s.multSelected.splice(r,1);for(var c=s.multSelected.length-1;c>=0;c--){s.multSelected[c]>=t&&s.multSelected[c]--}}if(s._customSize){s._customSize[t]&&delete s._customSize[t];var h,_={};for(var d in s._customSize){h=s._customSize[d];var u=parseInt(d);_[u-(u>=t?1:0)]=h}s._customSize=_}for(var m,p,g=null!=i?i:a;g>=t+1;g--)if(n=s.getItemByListId(g)){var f=s._calcItemPos(g-1);m=z(n).to(.2333,{position:new T(f.x,f.y,0)}),g<=t+1&&(p=!0,m.call((function(){s._aniDelRuning=!1,e(t),delete s._aniDelCB}))),m.start()}p||(s._aniDelRuning=!1,e(t),s._aniDelCB=null)}),!0)}else e(t)},a.scrollTo=function(t,e,i,s){void 0===e&&(e=.5),void 0===i&&(i=null),void 0===s&&(s=!1);var o=this;if(o.checkInited(!1)){null==e?e=.5:e<0&&(e=0),t<0?t=0:t>=o._numItems&&(t=o._numItems-1),!o._virtual&&o._layout&&o._layout.enabled&&o._layout.updateLayout();var n,a,l=o.getItemPos(t);if(!l)return N;switch(o._alignCalcType){case 1:n=l.left,n-=null!=i?o._thisNodeUt.width*i:o._leftGap,l=new T(n,0,0);break;case 2:n=l.right-o._thisNodeUt.width,n+=null!=i?o._thisNodeUt.width*i:o._rightGap,l=new T(n+o._contentUt.width,0,0);break;case 3:a=l.top,a+=null!=i?o._thisNodeUt.height*i:o._topGap,l=new T(0,-a,0);break;case 4:a=l.bottom+o._thisNodeUt.height,a-=null!=i?o._thisNodeUt.height*i:o._bottomGap,l=new T(0,-a+o._contentUt.height,0)}var r=o.content.getPosition();r=Math.abs(o._sizeType?r.y:r.x);var c=o._sizeType?l.y:l.x;Math.abs((null!=o._scrollPos?o._scrollPos:r)-c)>.5&&(o._scrollView.scrollToOffset(l,e),o._scrollToListId=t,o._scrollToEndTime=(new Date).getTime()/1e3+e,o._scrollToSo=o.scheduleOnce((function(){if(o._adheringBarrier||(o.adhering=o._adheringBarrier=!1),o._scrollPos=o._scrollToListId=o._scrollToEndTime=o._scrollToSo=null,s){var e=o.getItemByListId(t);e&&z(e).to(.1,{scale:1.05}).to(.1,{scale:1}).start()}}),e+.1),e<=0&&o._onScrolling())}},a._calcNearestItem=function(){var t,e,i,s,o,n,a=this;a.nearestListId=null,a._virtual&&a._calcViewPos(),i=a.viewTop,s=a.viewRight,o=a.viewBottom,n=a.viewLeft;for(var l=!1,r=0;r<a.content.children.length&&!l;r+=a._colLineNum)if(t=a._virtual?a.displayData[r]:a._calcExistItemPos(r))switch(e=a._sizeType?(t.top+t.bottom)/2:e=(t.left+t.right)/2,a._alignCalcType){case 1:t.right>=n&&(a.nearestListId=t.id,n>e&&(a.nearestListId+=a._colLineNum),l=!0);break;case 2:t.left<=s&&(a.nearestListId=t.id,s<e&&(a.nearestListId+=a._colLineNum),l=!0);break;case 3:t.bottom<=i&&(a.nearestListId=t.id,i<e&&(a.nearestListId+=a._colLineNum),l=!0);break;case 4:t.top>=o&&(a.nearestListId=t.id,o>e&&(a.nearestListId+=a._colLineNum),l=!0)}if((t=a._virtual?a.displayData[a.displayItemNum-1]:a._calcExistItemPos(a._numItems-1))&&t.id==a._numItems-1)switch(e=a._sizeType?(t.top+t.bottom)/2:e=(t.left+t.right)/2,a._alignCalcType){case 1:s>e&&(a.nearestListId=t.id);break;case 2:n<e&&(a.nearestListId=t.id);break;case 3:o<e&&(a.nearestListId=t.id);break;case 4:i>e&&(a.nearestListId=t.id)}},a.prePage=function(t){void 0===t&&(t=.5),this.checkInited()&&this.skipPage(this.curPageNum-1,t)},a.nextPage=function(t){void 0===t&&(t=.5),this.checkInited()&&this.skipPage(this.curPageNum+1,t)},a.skipPage=function(t,e){var i=this;if(i.checkInited())return i._slideMode!=zt.PAGE?console.error("This function is not allowed to be called, Must SlideMode = PAGE!"):void(t<0||t>=i._numItems||i.curPageNum!=t&&(i.curPageNum=t,i.pageChangeEvent&&u.emitEvents([i.pageChangeEvent],t),i.scrollTo(t,e)))},a.calcCustomSize=function(t){var e=this;if(e.checkInited()){if(!e._itemTmp)return console.error("Unset template item!");if(!e.renderEvent)return console.error("Unset Render-Event!");e._customSize={};var i=I(e._itemTmp),s=i.getComponent(f);e.content.addChild(i);for(var o=0;o<t;o++)u.emitEvents([e.renderEvent],i,o),s.height==e._itemSize.height&&s.width==e._itemSize.width||(e._customSize[o]=e._sizeType?s.height:s.width);return Object.keys(e._customSize).length||(e._customSize=null),i.removeFromParent(),i.destroy&&i.destroy(),e._customSize}},n(e,[{key:"slideMode",get:function(){return this._slideMode},set:function(t){this._slideMode=t}},{key:"virtual",get:function(){return this._virtual},set:function(t){null!=t&&(this._virtual=t),0!=this._numItems&&this._onScrolling()}},{key:"updateRate",get:function(){return this._updateRate},set:function(t){t>=0&&t<=6&&(this._updateRate=t)}},{key:"selectedId",get:function(){return this._selectedId},set:function(t){var e,i=this;switch(i.selectedMode){case Dt.SINGLE:if(!i.repeatEventSingle&&t==i._selectedId)return;if(e=i.getItemByListId(t),i._selectedId>=0?i._lastSelectedId=i._selectedId:i._lastSelectedId=null,i._selectedId=t,e&&(e.getComponent(b).selected=!0),i._lastSelectedId>=0&&i._lastSelectedId!=i._selectedId){var s=i.getItemByListId(i._lastSelectedId);s&&(s.getComponent(b).selected=!1)}i.selectedEvent&&u.emitEvents([i.selectedEvent],e,t%this._actualNumItems,null==i._lastSelectedId?null:i._lastSelectedId%this._actualNumItems);break;case Dt.MULT:if(!(e=i.getItemByListId(t)))return;var o=e.getComponent(b);i._selectedId>=0&&(i._lastSelectedId=i._selectedId),i._selectedId=t;var n=!o.selected;o.selected=n;var a=i.multSelected.indexOf(t);n&&a<0?i.multSelected.push(t):!n&&a>=0&&i.multSelected.splice(a,1),i.selectedEvent&&u.emitEvents([i.selectedEvent],e,t%this._actualNumItems,null==i._lastSelectedId?null:i._lastSelectedId%this._actualNumItems,n)}}},{key:"numItems",get:function(){return this._actualNumItems},set:function(t){var e=this;if(e.checkInited(!1))if(null==t||t<0)console.error("numItems set the wrong::",t);else if(e._actualNumItems=e._numItems=t,e._forceUpdate=!0,e._virtual)e._resizeContent(),e.cyclic&&(e._numItems=e._cyclicNum*e._numItems),e._onScrolling(),e.frameByFrameRenderNum||e.slideMode!=zt.PAGE||(e.curPageNum=e.nearestListId);else{e.cyclic&&(e._resizeContent(),e._numItems=e._cyclicNum*e._numItems);var i=e.content.getComponent(v);if(i&&(i.enabled=!0),e._delRedundantItem(),e.firstListId=0,e.frameByFrameRenderNum>0){for(var s=e.frameByFrameRenderNum>e._numItems?e._numItems:e.frameByFrameRenderNum,o=0;o<s;o++)e._createOrUpdateItem2(o);e.frameByFrameRenderNum<e._numItems&&(e._updateCounter=e.frameByFrameRenderNum,e._updateDone=!1)}else{for(var n=0;n<e._numItems;n++)e._createOrUpdateItem2(n);e.displayItemNum=e._numItems}}}},{key:"scrollView",get:function(){return this._scrollView}}]),e}(D)).prototype,"templateType",[C],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return wt.NODE}}),et=e($.prototype,"tmpNode",[O],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),it=e($.prototype,"tmpPrefab",[R],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),st=e($.prototype,"_slideMode",[A],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return zt.NORMAL}}),e($.prototype,"slideMode",[U],Object.getOwnPropertyDescriptor($.prototype,"slideMode"),$.prototype),ot=e($.prototype,"pageDistance",[B],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return.3}}),nt=e($.prototype,"pageChangeEvent",[k],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return new u}}),at=e($.prototype,"_virtual",[M],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return!0}}),e($.prototype,"virtual",[x],Object.getOwnPropertyDescriptor($.prototype,"virtual"),$.prototype),lt=e($.prototype,"cyclic",[V],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return!1}}),rt=e($.prototype,"lackCenter",[F],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return!1}}),ct=e($.prototype,"lackSlide",[H],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return!1}}),ht=e($.prototype,"_updateRate",[X],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 0}}),e($.prototype,"updateRate",[Y],Object.getOwnPropertyDescriptor($.prototype,"updateRate"),$.prototype),_t=e($.prototype,"frameByFrameRenderNum",[Z],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 0}}),dt=e($.prototype,"renderEvent",[W],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return new u}}),ut=e($.prototype,"selectedMode",[j],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return Dt.NONE}}),mt=e($.prototype,"selectedEvent",[q],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return new u}}),pt=e($.prototype,"repeatEventSingle",[K],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return!1}}),gt=e($.prototype,"_numItems",[J],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 0}}),Q=$))||Q)||Q)||Q)||Q)||Q));a._RF.pop()}}}));

System.register("chunks:///_virtual/List2.ts",["./rollupPluginModLoBabelHelpers.js","cc"],(function(e){var t,n;return{setters:[function(e){t=e.createClass},function(e){n=e.cclegacy}],execute:function(){n._RF.push({},"4a14ci6WrVCupwcmQMUByR/","List",void 0);e("List",function(){function e(e){void 0===e&&(e=!0),this.element=void 0,this.only=!1,this.count=0,this.only=e,this.element=[]}var n=e.prototype;return n.push=function(e){if(this.only&&this.element.indexOf(e)>=0)return!1;return this.element.push(e),this.count=this.element.length,!0},n.unshift=function(e){if(this.only&&this.element.indexOf(e)>=0)return!1;return this.element.unshift(e),this.count=this.element.length,!0},n.pop=function(){if(this.element.length>0){var e=this.element.pop();return this.count=this.element.length,e}return null},n.shift=function(){if(this.element.length>0){var e=this.element.shift();return this.count=this.element.length,e}return null},n.removeAt=function(e){if(e>=this.element.length)throw new Error("删除索引超出范围！");var t=this.element[e];return this.element.splice(e,1),this.count=this.element.length,t},n.remove=function(e){var t=this.element.indexOf(e);if(t<0)throw new Error("要删除的内容不在列表中！"+e);var n=this.element[t];return this.element.splice(t,1),this.count=this.element.length,n},n.clear=function(){this.count=0,this.element.length=0},n.has=function(e){return this.find(e)>=0},n.find=function(e){return this.element.indexOf(e)},n.findIndex=function(e){return this.element.findIndex(e)},n.filter=function(e){return this.element.filter(e)},n.get=function(e){if(e>=this.element.length)throw new Error("超出索引范围:"+e+"/"+this.element.length);return this.element[e]},n.sort=function(e){this.element.sort(e)},t(e,[{key:"elements",get:function(){return this.element}}]),e}());n._RF.pop()}}}));

System.register("chunks:///_virtual/ListItem.ts",["./rollupPluginModLoBabelHelpers.js","cc","./env"],(function(e){var t,i,n,o,r,s,l,a,c,d,u,p,h,f,m,b,g,v;return{setters:[function(e){t=e.applyDecoratedDescriptor,i=e.inheritsLoose,n=e.initializerDefineProperty,o=e.assertThisInitialized,r=e.createClass},function(e){s=e.cclegacy,l=e._decorator,a=e.Sprite,c=e.Node,d=e.Enum,u=e.SpriteFrame,p=e.EventHandler,h=e.UITransform,f=e.tween,m=e.Vec3,b=e.Button,g=e.Component},function(e){v=e.DEV}],execute:function(){var y,C,_,w,S,E,I,F,N,T,z,k,D,L,H,M,O;s._RF.push({},"e7dc6ZAYPVLJKxNcW8aTO1J","ListItem",void 0);var G=l.ccclass,A=l.property,W=l.disallowMultiple,R=l.menu,V=l.executionOrder,x=function(e){return e[e.NONE=0]="NONE",e[e.TOGGLE=1]="TOGGLE",e[e.SWITCH=2]="SWITCH",e}(x||{});e("default",(y=W(),C=R("List Item"),_=V(-5001),w=A({type:a,tooltip:v}),S=A({type:c,tooltip:v}),E=A({type:d(x),tooltip:v}),I=A({type:c,tooltip:v,visible:function(){return this.selectedMode>x.NONE}}),F=A({type:u,tooltip:v,visible:function(){return this.selectedMode==x.SWITCH}}),N=A({tooltip:v}),G(T=y(T=C(T=_((k=t((z=function(e){function t(){for(var t,i=arguments.length,r=new Array(i),s=0;s<i;s++)r[s]=arguments[s];return t=e.call.apply(e,[this].concat(r))||this,n(t,"icon",k,o(t)),n(t,"title",D,o(t)),n(t,"selectedMode",L,o(t)),n(t,"selectedFlag",H,o(t)),n(t,"selectedSpriteFrame",M,o(t)),t._unselectedSpriteFrame=null,n(t,"adaptiveSize",O,o(t)),t._selected=!1,t._btnCom=void 0,t.list=void 0,t._eventReg=!1,t.listId=void 0,t}i(t,e);var s=t.prototype;return s.onLoad=function(){if(this.selectedMode==x.SWITCH){var e=this.selectedFlag.getComponent(a);this._unselectedSpriteFrame=e.spriteFrame}},s.onDestroy=function(){this.node.off(c.EventType.SIZE_CHANGED,this._onSizeChange,this)},s._registerEvent=function(){this._eventReg||(this.btnCom&&this.list.selectedMode>0&&this.btnCom.clickEvents.unshift(this.createEvt(this,"onClickThis")),this.adaptiveSize&&this.node.on(c.EventType.SIZE_CHANGED,this._onSizeChange,this),this._eventReg=!0)},s._onSizeChange=function(){this.list._onItemAdaptive(this.node)},s.createEvt=function(e,t,i){if(void 0===i&&(i=null),e.isValid){e.comName=e.comName||e.name.match(/\<(.*?)\>/g).pop().replace(/\<|>/g,"");var n=new p;return n.target=i||e.node,n.component=e.comName,n.handler=t,n}},s.showAni=function(e,t,i){var n,o=this,r=o.node.getComponent(h);switch(e){case 0:n=f(o.node).to(.2,{scale:new m(.7,.7)}).by(.3,{position:new m(0,2*r.height)});break;case 1:n=f(o.node).to(.2,{scale:new m(.7,.7)}).by(.3,{position:new m(2*r.width,0)});break;case 2:n=f(o.node).to(.2,{scale:new m(.7,.7)}).by(.3,{position:new m(0,-2*r.height)});break;case 3:n=f(o.node).to(.2,{scale:new m(.7,.7)}).by(.3,{position:new m(-2*r.width,0)});break;default:n=f(o.node).to(.3,{scale:new m(.1,.1)})}(t||i)&&n.call((function(){if(i){o.list._delSingleItem(o.node);for(var e=o.list.displayData.length-1;e>=0;e--)if(o.list.displayData[e].id==o.listId){o.list.displayData.splice(e,1);break}}t()})),n.start()},s.onClickThis=function(){this.list.selectedId=this.listId},r(t,[{key:"selected",get:function(){return this._selected},set:function(e){if(this._selected=e,this.selectedFlag)switch(this.selectedMode){case x.TOGGLE:this.selectedFlag.active=e;break;case x.SWITCH:var t=this.selectedFlag.getComponent(a);t&&(t.spriteFrame=e?this.selectedSpriteFrame:this._unselectedSpriteFrame)}}},{key:"btnCom",get:function(){return this._btnCom||(this._btnCom=this.node.getComponent(b)),this._btnCom}}]),t}(g)).prototype,"icon",[w],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),D=t(z.prototype,"title",[S],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),L=t(z.prototype,"selectedMode",[E],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return x.NONE}}),H=t(z.prototype,"selectedFlag",[I],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),M=t(z.prototype,"selectedSpriteFrame",[F],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),O=t(z.prototype,"adaptiveSize",[N],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return!1}}),T=z))||T)||T)||T)||T));s._RF.pop()}}}));

System.register("chunks:///_virtual/Logger.ts",["cc"],(function(t){var n,i;return{setters:[function(t){n=t.cclegacy,i=t.log}],execute:function(){n._RF.push({},"5c09aRrQ9hAroMDCvX+zX+K","Logger",void 0);var e=t("LogType",function(t){return t[t.Net=1]="Net",t[t.Model=2]="Model",t[t.Business=4]="Business",t[t.View=8]="View",t[t.Config=16]="Config",t[t.Trace=32]="Trace",t}({})),o={1:"网络日志",2:"数据日志",4:"业务日志",8:"视图日志",16:"配置日志",32:"标准日志"},s=t("Logger",function(){function t(){}return t.init=function(){this.tags=e.Net|e.Model|e.Business|e.View|e.Config|e.Trace},t.setTags=function(t){void 0===t&&(t=null),t&&(this.tags=t)},t.start=function(t){void 0===t&&(t="Time"),console.time(t)},t.end=function(t){void 0===t&&(t="Time"),console.timeEnd(t)},t.table=function(t,n){this.isOpen(e.Trace)&&console.table(t)},t.trace=function(t,n){(void 0===n&&(n="color:#000000;"),this.isOpen(e.Trace))&&(console.log||i).call(null,"%c%s%s",n,this.getDateString(),t)},t.logNet=function(t,n){this.orange(e.Net,t,n)},t.logModel=function(t,n){this.violet(e.Model,t,n)},t.logBusiness=function(t,n){this.blue(e.Business,t,n)},t.logView=function(t,n){this.green(e.View,t,n)},t.logConfig=function(t,n){this.gray(e.Config,t,n)},t.orange=function(t,n,i){this.print(t,n,"color:#ee7700;",i)},t.violet=function(t,n,i){this.print(t,n,"color:Violet;",i)},t.blue=function(t,n,i){this.print(t,n,"color:#3a5fcd;",i)},t.green=function(t,n,i){this.print(t,n,"color:green;",i)},t.gray=function(t,n,i){this.print(t,n,"color:gray;",i)},t.isOpen=function(t){return 0!=(this.tags&t)},t.print=function(t,n,e,s){if(this.isOpen(t)){var r=console.log||i,c=o[t];s?r.call(null,"%c%s%s%s:%s%o",e,this.getDateString(),"["+c+"]",this.stack(5),s,n):r.call(null,"%c%s%s%s:%o",e,this.getDateString(),"["+c+"]",this.stack(5),n)}},t.stack=function(t){var n=(new Error).stack.split("\n"),i=[];n.forEach((function(t){var n,e=(t=t.substring(7)).split(" ");e.length<2?i.push(e[0]):i.push(((n={})[e[0]]=e[1],n))}));var e,o=[],s=[];if(t<i.length-1)for(var r in i[t]){if(2==(s=r.split(".")).length)o=s.concat();else{var c=(e=i[t][r]).lastIndexOf("/"),l=e.lastIndexOf(".");if(c>-1&&l>-1){var g=e.substring(c+1,l);o.push(g)}else o.push(e)}}return 1==o.length?"["+o[0]+".ts]":2==o.length?"["+o[0]+".ts->"+o[1]+"]":""},t.getDateString=function(){var t=new Date,n=t.getHours().toString(),i="";return i+=(1==n.length?"0"+n:n)+":",i+=(1==(n=t.getMinutes().toString()).length?"0"+n:n)+":",i+=(1==(n=t.getSeconds().toString()).length?"0"+n:n)+":",1==(n=t.getMilliseconds().toString()).length&&(n="00"+n),2==n.length&&(n="0"+n),i="["+(i+=n)+"]"},t}());s.tags=0,s.init(),n._RF.pop()}}}));

System.register("chunks:///_virtual/main",["./ElectronAPI.ts","./Ready.ts","./App.ts","./ConstGlobal.ts","./Data.ts","./Net.ts","./BaseLlistCtr.ts","./EventDispatcher.ts","./EventManager.ts","./GameSocketCtrl.ts","./List.ts","./ListItem.ts","./BroadcastCtrl.ts","./ErrorCode.ts","./GameEvent.ts","./GameSocket.ts","./MessageCtrl.ts","./MessageEvent.ts","./CmdLogin.ts","./ResGameResult.ts","./ResGameStatus.ts","./ResKickoutUser.ts","./ResRoomInfo.ts","./ResUserJoin.ts","./ConfigHelper.ts","./api.ts","./dataTrack.ts","./NetInterface.ts","./NetManager.ts","./NetNode.ts","./NetProtocolPako.ts","./NetProtocolProtobuf.ts","./WebSock.ts","./EventManager2.ts","./ResLoader.ts","./ResManager.ts","./Timer.ts","./TimerManager.ts","./ViewBase.ts","./ViewManager.ts","./ArrayUtil.ts","./AsyncQueue.ts","./Canvas2Image.ts","./Collection.ts","./Effect2DFollow3D.ts","./FixedPointNum.ts","./Guide.ts","./HttpRequest.ts","./ImageUtil.ts","./List2.ts","./Logger.ts","./MathUtil.ts","./ObjectUtil.ts","./Queue.ts","./RegexUtil.ts","./RotateUtil.ts","./ScreenShot.ts","./ScrollViewProCom.ts","./SimpleDelegate.ts","./Singleton.ts","./Sound.ts","./StateMachine.ts","./StorageUtil.ts","./StringUtil.ts","./TimeUtil.ts","./Tool.ts","./Vec3Util.ts","./ViewGroupNesting.ts","./WxWeb.ts","./decorator.ts","./f_Math.ts","./QuadTree.ts","./Agent.ts","./RVOMath.ts","./Simulator.ts","./kdtree.ts","./xcore.ts","./mediaVideo.ts","./ExternalMessage.ts","./room.ts","./user.mjs_cjs=&original=.js","./CCCEventManager.ts","./CCCSctiptSingleton.ts","./cocoscreator-game.cjs.min.ts"],(function(){return{setters:[null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null],execute:function(){}}}));

System.register("chunks:///_virtual/MathUtil.ts",["cc"],(function(t){var n;return{setters:[function(t){n=t.cclegacy}],execute:function(){n._RF.push({},"c8689KLivpLArYwAkSO6wK3","MathUtil",void 0);var r=t("MathUtil",function(){function t(){}return t.sign=function(t){return t>0?1:t<0?-1:0},t.progress=function(t,n,r){return t+(n-t)*r},t.lerp=function(t,n,r){return r>1?r=1:r<0&&(r=0),t*(1-r)+n*r},t.lerpAngle=function(n,r,e){var u=(r%=360)-(n%=360);return u>180?r=n-(360-u):u<-180&&(r=n+(360+u)),(t.lerp(n,r,e)%360+360)%360},t.angleTowards=function(t,n,r){var e=(n%=360)-(t%=360);e>180?n=t-(360-e):e<-180&&(n=t+(360+e));var u=n-t;return r>Math.abs(u)?n:((t+r*Math.sign(u))%360+360)%360},t.clamp=function(t,n,r){return t<n?n:t>r?r:t},t.probability=function(t){return Math.random()<t},t}());r.deg2Rad=Math.PI/180,r.rad2Deg=180/Math.PI,n._RF.pop()}}}));

System.register("chunks:///_virtual/mediaVideo.ts",["./rollupPluginModLoBabelHelpers.js","cc","./env"],(function(t){var e,i,n,r,a,s,o,h,_,u,l,d,c,p,v,E,P,S,A,f,y,T,m;return{setters:[function(t){e=t.applyDecoratedDescriptor,i=t.inheritsLoose,n=t.initializerDefineProperty,r=t.assertThisInitialized,a=t.createClass},function(t){s=t.cclegacy,o=t._decorator,h=t.VideoClip,_=t.VideoPlayer,u=t.RenderableComponent,l=t.Material,d=t.Texture2D,c=t.log,p=t.warn,v=t.game,E=t.Game,P=t.Sprite,S=t.SpriteFrame,A=t.gfx,f=t.UITransform,y=t.view,T=t.Component},function(t){m=t.JSB}],execute:function(){var g,R,D,N,I,b,L,V,G,O,H,M;s._RF.push({},"0f9723VZHVA4pzZSc9Wt3qu","mediaVideo",void 0);var U=o.ccclass,F=o.property,k=(t("EventType",function(t){return t[t.PREPARING=1]="PREPARING",t[t.LOADED=2]="LOADED",t[t.READY=3]="READY",t[t.COMPLETED=4]="COMPLETED",t[t.ERROR=5]="ERROR",t[t.PLAYING=6]="PLAYING",t[t.PAUSED=7]="PAUSED",t[t.STOPPED=8]="STOPPED",t[t.BUFFER_START=9]="BUFFER_START",t[t.BUFFER_UPDATE=10]="BUFFER_UPDATE",t[t.BUFFER_END=11]="BUFFER_END",t}({})),function(t){return t[t.ERROR=-1]="ERROR",t[t.IDLE=0]="IDLE",t[t.PREPARING=1]="PREPARING",t[t.PREPARED=2]="PREPARED",t[t.PLAYING=3]="PLAYING",t[t.PAUSED=4]="PAUSED",t[t.STOP=5]="STOP",t[t.COMPLETED=5]="COMPLETED",t}(k||{})),w=function(t){return t[t.HAVE_NOTHING=0]="HAVE_NOTHING",t[t.HAVE_METADATA=1]="HAVE_METADATA",t[t.HAVE_CURRENT_DATA=2]="HAVE_CURRENT_DATA",t[t.HAVE_FUTURE_DATA=3]="HAVE_FUTURE_DATA",t[t.HAVE_ENOUGH_DATA=4]="HAVE_ENOUGH_DATA",t}(w||{}),B=function(t){return t[t.NONE=-1]="NONE",t[t.I420=0]="I420",t[t.RGB=2]="RGB",t[t.NV12=23]="NV12",t[t.NV21=24]="NV21",t[t.RGBA=26]="RGBA",t}(B||{});t("MediaVideo",(g=U("MediaVideo"),R=F(h),D=F(_),N=F(u),I=F(l),g((V=e((L=function(t){function e(){for(var e,i=arguments.length,a=new Array(i),s=0;s<i;s++)a[s]=arguments[s];return e=t.call.apply(t,[this].concat(a))||this,n(e,"_clip",V,r(e)),e._seekTime=0,e._nativeDuration=0,e._nativeWidth=0,e._nativeHeight=0,e._currentState=k.IDLE,e._targetState=k.IDLE,e._pixelFormat=B.RGBA,e._video=null,e._texture0=new d,e._loaded=!1,e._inBackground=!1,e._lastPlayState=!1,e._volume=-1,e._videoSprites=[],n(e,"videoPlayer",G,r(e)),n(e,"loop",O,r(e)),n(e,"midVideoSp",H,r(e)),n(e,"rgbMat",M,r(e)),e._videoUrl=null,e._cb=null,e._cb2=null,e}i(e,t);var s=e.prototype;return s.setData=function(t,e,i){"string"==typeof t?this._videoUrl=t:t instanceof h&&(this.videoPlayer.clip=t,this._clip=t),this._cb=e,this._cb2=i,c("this._videoUrl ",this._videoUrl,t),this._initialize(),this._video&&this._updateVideoSource()},s.start=function(){},s._initialize=function(){this._initializeBrowser()},s._initializeBrowser=function(){var t=this;this._videoSprites.push(this.midVideoSp),this._video=this.videoPlayer._impl._video,this._video.crossOrigin="anonymous",this._video.autoplay=!1,this._video.loop=!1,this._video.muted=!1,this._video.addEventListener("loadedmetadata",(function(){return t._onMetaLoaded()})),this._video.addEventListener("ended",(function(){return t._onCompleted()})),this._loaded=!1;var e=function(){t._loaded||t._currentState==k.PLAYING||t._video.readyState!==w.HAVE_ENOUGH_DATA&&t._video.readyState!==w.HAVE_METADATA||(c("_initializeBrowser: readyState=",t._video.readyState),t._video.currentTime=0,t._loaded=!0,t._onReadyToPlay())};this._video.addEventListener("canplay",e),this._video.addEventListener("canplaythrough",e),this._video.addEventListener("suspend",e)},s._updateVideoSource=function(){var t=this._videoUrl;!t&&this._clip&&(t=this._clip.nativeUrl),t?(this._loaded=!1,this._video.pause(),this._video.src=t,this.node.emit("preparing",this)):p("video url null",this._clip)},s.onEnable=function(){v.on(E.EVENT_SHOW,this._onShow,this),v.on(E.EVENT_HIDE,this._onHide,this)},s.onDisable=function(){v.off(E.EVENT_SHOW,this._onShow,this),v.off(E.EVENT_HIDE,this._onHide,this),this.stop()},s._onShow=function(){this._inBackground&&(this._inBackground=!1,this._lastPlayState&&this.resume())},s._onHide=function(){this._inBackground||(this._inBackground=!0,this._lastPlayState=this.isPlaying(),this._lastPlayState&&this.pause())},s.update=function(t){this._isInPlaybackState()&&!m&&(this._texture0.uploadData(this._video),this._updateMaterial(this._videoSprites))},s._updateMaterial=function(t){for(var e=0;e<t.length;e++){var i=t[e].getSharedMaterial(0);i&&i.setProperty("texture0",this._texture0)}},s._initRenderTexture=function(t){for(var e=0;e<t.length;e++){var i=t[e];if(i instanceof P){var n=i;null===n.spriteFrame&&(n.spriteFrame=new S);var r=new d;this._resetTexture(r,this.width,this.height),n.spriteFrame.texture=r}}this._resetTexture(this._texture0,this.width,this.height),c("size",this.width,this.height);for(var a=0;a<t.length;a++){var s=t[a],o=null==s?void 0:s.material;null==o||o.setProperty("texture0",this._texture0),null==o||o.setProperty("transFlag",0)}},s._initRenderMaterial=function(t){var e=B.RGB;if(this._pixelFormat!=e){for(var i=0;i<t.length;i++){var n=t[i];switch(c("_initRenderMaterial: i="+i+", pixelFormat="+e),e){case B.RGB:n.setSharedMaterial(this.rgbMat,0)}}this._pixelFormat=e}},s._resetTexture=function(t,e,i,n){t.setFilters(d.Filter.LINEAR,d.Filter.LINEAR),t.setMipFilter(d.Filter.LINEAR),t.setWrapMode(d.WrapMode.CLAMP_TO_EDGE,d.WrapMode.CLAMP_TO_EDGE),t.reset({width:e,height:i,format:n||A.Format.RGB8})},s._onMetaLoaded=function(){this.node.emit("loaded",this)},s._onReadyToPlay=function(){c("_onReadyToPlay"),this._initRenderMaterial(this._videoSprites),this._currentState=k.PREPARED,this._seekTime>.1&&(this.currentTime=this._seekTime),this._initRenderTexture(this._videoSprites),this.node.emit("ready",this),this.play(),this.midVideoSp.getComponent(f).width=y.getVisibleSize().width,this.midVideoSp.getComponent(f).height=y.getVisibleSize().height,this._cb&&this._cb()},s._onCompleted=function(){c("_onCompleted",this.loop),this.loop?this._currentState==k.PLAYING&&(this.currentTime=0,this._video.play()):(this._currentState=k.COMPLETED,this._targetState=k.COMPLETED,this.node.emit("completed",this),this._cb2&&this._cb2())},s.play=function(){this._isInPlaybackState()?(this._currentState==k.COMPLETED&&(this.currentTime=0),this._currentState!=k.PLAYING&&(-1!==this._volume&&(this.setVolume(this._volume),this._volume=-1),this._video.play(),this.node.emit("playing",this),this._currentState=k.PLAYING,this._targetState=k.PLAYING)):this._targetState=k.PLAYING},s.resume=function(){this._isInPlaybackState()&&this._currentState!=k.PLAYING?(this._video.play(),this.node.emit("playing",this),this._currentState=k.PLAYING,this._targetState=k.PLAYING):this._targetState=k.PLAYING},s.pause=function(){this._isInPlaybackState()&&this._currentState!=k.PAUSED?(this._video.pause(),this.node.emit("paused",this),this._currentState=k.PAUSED,this._targetState=k.PAUSED):this._targetState=k.PAUSED},s.stop=function(){this._seekTime=0,this._isInPlaybackState()&&this._currentState!=k.STOP?(this._video.pause(),this._video.currentTime=0,this.node.emit("stopped",this),this._currentState=k.STOP,this._targetState=k.STOP):this._targetState=k.STOP},s.setVolume=function(t){this._isInPlaybackState()?this._video.volume=t:this._volume=t},s.clear=function(){},s.isPlaying=function(){return this._currentState==k.PLAYING||this._targetState==k.PLAYING},s._isInPlaybackState=function(){return!!this._video&&this._currentState!=k.IDLE&&this._currentState!=k.PREPARING&&this._currentState!=k.ERROR},a(e,[{key:"clip",get:function(){return this._clip},set:function(t){this._clip=t}},{key:"currentTime",get:function(){return this._video?this._isInPlaybackState()?this._video.currentTime:this._seekTime:0},set:function(t){this._video&&(this._isInPlaybackState()?this._video.currentTime=t:this._seekTime=t)}},{key:"duration",get:function(){if(!this._video)return 0;if(this._nativeDuration>0)return this._nativeDuration;var t=this._video.duration;return this._nativeDuration=isNaN(t)?0:t,this._nativeDuration}},{key:"width",get:function(){if(!this._isInPlaybackState())return 0;if(this._nativeWidth>0)return this._nativeWidth;var t=this._video.videoWidth;return this._nativeWidth=isNaN(t)?0:t,this._nativeWidth}},{key:"height",get:function(){if(!this._isInPlaybackState())return 0;if(this._nativeHeight>0)return this._nativeHeight;var t=this._video.videoHeight;return this._nativeHeight=isNaN(t)?0:t,this._nativeHeight}},{key:"bufferPercentage",get:function(){return this._video,0}}]),e}(T)).prototype,"_clip",[F],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),e(L.prototype,"clip",[R],Object.getOwnPropertyDescriptor(L.prototype,"clip"),L.prototype),G=e(L.prototype,"videoPlayer",[D],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),O=e(L.prototype,"loop",[F],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return!1}}),H=e(L.prototype,"midVideoSp",[N],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),M=e(L.prototype,"rgbMat",[I],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),b=L))||b));s._RF.pop()}}}));

System.register("chunks:///_virtual/MessageCtrl.ts",["./rollupPluginModLoBabelHelpers.js","cc","./CCCSctiptSingleton.ts","./GameSocket.ts"],(function(e){var t,s,c,o,n,i;return{setters:[function(e){t=e.inheritsLoose,s=e.extends,c=e.createClass},function(e){o=e.cclegacy},function(e){n=e.default},function(e){i=e.GameSocket}],execute:function(){o._RF.push({},"d050du6c19NkY1dAjYEu6WQ","MessageCtrl",void 0);e("default",function(e){function o(){for(var t,s=arguments.length,c=new Array(s),o=0;o<s;o++)c[o]=arguments[o];return(t=e.call.apply(e,[this].concat(c))||this).succFalse={isSucc:!1},t.protocolSwitch=1,t._cmd=void 0,t._subCmd=void 0,t.resolve=void 0,t.reject=void 0,t}t(o,e);var n=o.prototype;return n.addListen=function(){console.log("addListen",this._cmd,this._subCmd),i.onListenMessage((this._cmd<<16)+this._subCmd,this)},n.send=function(e){var t;null==(t=i.mainSocket)||t.send({protocolSwitch:this.protocolSwitch,cmd:this._cmd,subCmd:this._subCmd,data:null==e?new Uint8Array(0):e})},n.sendSync=function(e){var t=this;return new Promise((function(s,c){try{t.scheduleOnce(t.clearResolve,5),t.resolve=s,t.reject=c,t.send(e)}catch(e){c(e)}}))},n.receive=function(e,t){this.reject&&this.unschedule(this.clearResolve),this.resolve?(0===e?this.resolve({isSucc:!0,info:t,code:e}):(console.error(e,t),this.resolve(s({},this.succFalse,{code:e}))),this.resolve=null,this.reject=null):console.warn("接收异常;cmd: "+this.cmd+";subCmd: "+this.subCmd+";code: "+e,t)},n.clearResolve=function(){this.reject&&(console.error("发送超时"),this.reject(this.succFalse)),this.resolve=null,this.reject=null},c(o,[{key:"cmd",set:function(e){e<0&&console.error("未注册消息"),this._cmd=e}},{key:"subCmd",set:function(e){e<0&&console.error("未注册消息"),this._subCmd=e}}]),o}(n));o._RF.pop()}}}));

System.register("chunks:///_virtual/MessageEvent.ts",["cc"],(function(e){var s;return{setters:[function(e){s=e.cclegacy}],execute:function(){s._RF.push({},"b3eaetBgmxBfYoMF/R21FzS","MessageEvent",void 0);e("MessageCmdEvent",{userCmd:1,roomCmd:2,screenUserCmd:9}),e("MessageUserSubCmd",{login:1,joinRoom:8,serverTime:4,userGameHistory:7,UserAwardHistory:10}),e("MessageScreenUserSubCmd",{login:1,joinRoom:2}),e("MessageRoomSubCmd",{updateScore:10,roomStatusPush:6,roomUserPush:7,roomMainInfoPush:4,roomUserMainPush:11,roomGameRankPush:8,rewardPush:9,rule:12,roomConfig:13,rank:14,kickout:15});s._RF.pop()}}}));

System.register("chunks:///_virtual/Net.ts",["cc","./ConstGlobal.ts","./xcore.ts","./TimeUtil.ts","./ArrayUtil.ts"],(function(a){var t,e,n,r,o,p;return{setters:[function(a){t=a.cclegacy},function(a){e=a.E_EVENT,n=a.E_GiftMessageType},function(a){r=a.xcore},function(a){o=a.default},function(a){p=a.ArrayUtil}],execute:function(){t._RF.push({},"25aabaLvWhA1IbWD6LWquOT","Net",void 0);a("default",new(function(){function a(){}var t=a.prototype;return t.login=function(a,t,e,n,o,p,i){return r.http.post("ga/public/api/login",{channel:a,channelId:t,code:e,userName:n,password:o,appId:p,combatId:i}).then((function(a){r.http.defaults.headers.Authorization=a.data}))},t.getServerTimeStamp=function(){return r.http.get("ga/public/api/getServerTimeStamp").then((function(a){return o.initServerTime(a.data)}))},t.relogin=function(a,t,e,n,o,p,i){return r.http.post("ga/refreshLogin",{channel:a,channelId:t,code:e,userName:n,password:o,appId:p,combatId:i}).then((function(a){r.http.defaults.headers.Authorization=a.data}))},t.roleJoin=function(a,t,e){return r.http.post("ga/player/joinGame",{nickName:a,avatarUrl:t,playerId:e,appId:r.gameData.appId,channelId:r.gameData.channelId}).then((function(a){r.gameData.oldRankInfo||(r.gameData.oldRankInfo={});var t=a.data.userIdScoreVo;t&&null!=t.rank&&(t.rank+=1),r.gameData.oldRankInfo[""+e]=t}))},t.getRoleRankInfo=function(a,t,o,p){return void 0===p&&(p=!0),r.http.post("ga/rank/getRankByPlayerIds",{key:a,playerIds:t}).then((function(a){var t=a.data;o&&o(t),p&&t.forEach((function(a){a.rank>=0&&a.rank<=99&&r.event.raiseEvent(e.GiftMessage,{type:n.RankBoss,userId:a.playerId,name:a.nickName,avatar:a.avatarUrl,num:1,lev:a.rank})}))}))},t.getLiverInfo=function(){return r.http.get("ga/anchor/getAnchorInfo").then((function(a){return r.gameData.baseInfo=a.data}))},t.getCombatId=function(){return r.http.post("ga/public/api/combatId").then((function(a){return r.gameData.combatId=a.data}))},t.startFight=function(a,t){return r.http.post("ga/api/combat/tiktok/start",{combatId:a,level:t})},t.settleFight=function(a,t,e,n,o,p,i){return void 0===o&&(o=0),void 0===p&&(p=0),void 0===i&&(i=0),r.http.post("ga/api/combat/tiktok/end",{combatId:a,userList:t,giftList:e,scoreList:n,redScore:o,blueScore:p,maxLevel:i})},t.getRankInfo=function(a,t,e){return r.http.get("ga/rank/getRank",{params:{key:a,start:t,end:e}}).then((function(t){if(-1!=a.indexOf("level"))if(r.gameData.roundRankInfo)if(t.data.length>0){var e=p.noRepeated(r.gameData.roundRankInfo.concat(t.data));r.gameData.roundRankInfo=e}else r.gameData.roundRankInfo=r.gameData.roundRankInfo;else r.gameData.roundRankInfo=t.data;else if(-1!=a.indexOf("gift"))if(r.gameData.giftRankInfo)if(t.data.length>0){var n=p.noRepeated(r.gameData.giftRankInfo.concat(t.data));r.gameData.giftRankInfo=n}else r.gameData.giftRankInfo=r.gameData.giftRankInfo;else r.gameData.giftRankInfo=t.data;else if(r.gameData.scoreRankInfo)if(t.data.length>0){var o=p.noRepeated(r.gameData.scoreRankInfo.concat(t.data));r.gameData.scoreRankInfo=o}else r.gameData.scoreRankInfo=r.gameData.scoreRankInfo;else r.gameData.scoreRankInfo=t.data}))},t.getLiverRankInfo=function(a,t,e){return r.http.get("ga/anchor/rank/getRank",{params:{key:a,start:t,end:e}}).then((function(a){if(r.gameData.liverRankInfo)if(a.data.length>0){var t=p.noRepeated(r.gameData.liverRankInfo.concat(a.data));r.gameData.liverRankInfo=t}else r.gameData.liverRankInfo=r.gameData.liverRankInfo;else r.gameData.liverRankInfo=a.data}))},t.getUserRankInfoByUserIds=function(a,t){return r.http.post("ga/rank/getRankByPlayerIds",{key:a,playerIds:t})},t.updateTokenToIo=function(){return r.http.get("ga/api/loginIoGame")},t.updateAnchorRankGameLev=function(a,t){var e=[t];return r.http.post("ga/anchor/rank/getRankByPlayerIds",{key:a,playerIds:e}).then((function(a){r.gameData.gameLev=a.data[0].score}))},t.updateGiftTopInfo=function(a,t){return r.http.post("ga/api/combat/tiktok/gift/top",{combatId:a,gifts:t}).then((function(a){return r.gameData.giftInfo=a.data}))},t.getDebrisInfo=function(a,t){return r.http.get("ga/player/getPropFragment",{params:{playerIds:a}}).then((function(a){var e=a.data;t&&t(e)}))},t.addDebris=function(a,t,e){return r.http.get("/ga/player/addPropFragment",{params:{playerId:a,num:t,prop:e}})},t.getSKinInfo=function(a,t){return r.http.get("ga/player/getProp",{params:{playerId:a}}).then((function(a){var e=a.data;t&&t(e)}))},t.exchangeSkin=function(a,t,e,n,o){return r.http.get("ga/player/propFragmentToProp",{params:{playerId:a,prop:t,num:e,time:n,fragment:o}})},t.rewardSkin=function(a,t,e){return r.http.post("ga/player/setProp",{playerId:a,type:t,props:e})},t.setSelectSkin=function(a,t){return r.http.get("ga/player/setPropUse",{params:{playerId:a,prop:t}})},t.getDimondInfo=function(a){return r.http.get("ga/player/getBaozhu",{params:{playerId:a}})},t.addDimond=function(a,t,e){return r.http.get("ga/player/addBaozhu",{params:{playerId:a,type:t,num:e}})},t.getPlayerOperate=function(a,t){return r.gameData.playersOperation[a]&&r.gameData.playersOperation[a][t]?r.gameData.playersOperation[a][t]:r.http.get("ga/player/getConfig",{params:{playerId:a,key:t}}).then((function(e){r.gameData.playersOperation[a]||(r.gameData.playersOperation[a]={}),r.gameData.playersOperation[a][t]=e.data,console.log("getPlayerOperate",a,r.gameData.playersOperation[a][t])}))},t.setPlayerOperate=function(a,t,e){return r.http.get("ga/player/setConfig",{params:{playerId:a,key:t,value:e}}).then((function(n){r.gameData.playersOperation[a]||(r.gameData.playersOperation[a]={}),r.gameData.playersOperation[a][t]=e,console.log("setPlayerOperate",a,t,r.gameData.playersOperation[a][t])}))},t.setPlayersOperate=function(a){return r.http.post("ga/player/setConfigs",a)},t.costGold=function(a,t){return r.http.get("ga/rank/reduceGold",{params:{num:a,openId:t}})},a}()));t._RF.pop()}}}));

System.register("chunks:///_virtual/NetInterface.ts",["cc"],(function(){var e;return{setters:[function(t){e=t.cclegacy}],execute:function(){e._RF.push({},"075abIrZ55N9rpJ/Q9qxbwl","NetInterface",void 0),e._RF.pop()}}}));

System.register("chunks:///_virtual/NetManager.ts",["cc"],(function(n){var e;return{setters:[function(n){e=n.cclegacy}],execute:function(){e._RF.push({},"70d77niO4pDzL1uzz2MmVPU","NetManager",void 0),n("NetManager",function(){function n(){this._channels={}}n.getInstance=function(){return this._instance||(this._instance=new n),this._instance};var e=n.prototype;return e.setNetNode=function(n,e){void 0===e&&(e=0),this._channels[e]=n},e.removeNetNode=function(n){delete this._channels[n]},e.connect=function(n,e){return void 0===e&&(e=0),!!this._channels[e]&&this._channels[e].connect(n)},e.send=function(n,e,t){void 0===e&&(e=!1),void 0===t&&(t=0);var i=this._channels[t];return i?i.send(n,e):-1},e.request=function(n,e,t,i,s){void 0===t&&(t=!0),void 0===i&&(i=!1),void 0===s&&(s=0);var c=this._channels[s];c&&c.request(n,e,t,i)},e.requestUnique=function(n,e,t,i,s){void 0===t&&(t=!0),void 0===i&&(i=!1),void 0===s&&(s=0);var c=this._channels[s];return!!c&&c.requestUnique(n,e,t,i)},e.close=function(n,e,t){if(void 0===t&&(t=0),this._channels[t])return this._channels[t].closeSocket(n,e)},n}())._instance=void 0,e._RF.pop()}}}));

System.register("chunks:///_virtual/NetNode.ts",["cc"],(function(e){var t,n,i,s;return{setters:[function(e){t=e.cclegacy,n=e.log,i=e.error,s=e.warn}],execute:function(){t._RF.push({},"03fde1CYKJPxbnxg+22JLZy","NetNode",void 0);var r=["已关闭","连接中","验证中","可传输数据"],o=e("NetTipsType",function(e){return e[e.Connecting=0]="Connecting",e[e.ReConnecting=1]="ReConnecting",e[e.Requesting=2]="Requesting",e}({})),c=e("NetNodeState",function(e){return e[e.Closed=0]="Closed",e[e.Connecting=1]="Connecting",e[e.Checking=2]="Checking",e[e.Working=3]="Working",e}({}));e("NetNode",function(){function e(){this._connectOptions=null,this._autoReconnect=6,this._isSocketInit=!1,this._isSocketOpen=!1,this._state=c.Closed,this._socket=null,this._networkTips=null,this._protocolHelper=null,this._connectedCallback=null,this._disconnectCallback=null,this._callbackExecuter=null,this._keepAliveTimer=null,this._receiveMsgTimer=null,this._reconnectTimer=null,this._heartTime=1e4,this._receiveTime=6e6,this._reconnetTimeOut=8e6,this._requests=Array(),this._listener={}}var t=e.prototype;return t.init=function(e,t,i,s){void 0===i&&(i=null),void 0===s&&(s=null),n("网络初始化"),this._socket=e,this._protocolHelper=t,this._networkTips=i,this._callbackExecuter=s||function(e,t){e.callback.call(e.target,t)}},t.connect=function(e){return!(!this._socket||this._state!=c.Closed)&&(this._isSocketInit||this.initSocket(),this._state=c.Connecting,this._socket.connect(e)?(null==this._connectOptions&&"number"==typeof e.autoReconnect&&(this._autoReconnect=e.autoReconnect),this._connectOptions=e,this.updateNetTips(o.Connecting,!0),!0):(this.updateNetTips(o.Connecting,!1),!1))},t.initSocket=function(){var e=this;this._socket&&(this._socket.onConnected=function(t){e.onConnected(t)},this._socket.onMessage=function(t){e.onMessage(t)},this._socket.onError=function(t){e.onError(t)},this._socket.onClosed=function(t){e.onClosed(t)},this._isSocketInit=!0)},t.updateNetTips=function(e,t){this._networkTips&&(e==o.Requesting?this._networkTips.requestTips(t):e==o.Connecting?this._networkTips.connectTips(t):e==o.ReConnecting&&this._networkTips.reconnectTips(t))},t.onConnected=function(e){var t=this;n("网络已连接"),this._isSocketOpen=!0,null!==this._connectedCallback?(this._state=c.Checking,this._connectedCallback((function(){t.onChecked()}))):this.onChecked(),n("网络已连接当前状态为【"+r[this._state]+"】")},t.onChecked=function(){n("连接验证成功，进入工作状态"),this._state=c.Working,this.updateNetTips(o.Connecting,!1),this.updateNetTips(o.ReConnecting,!1);var e=this._requests.concat();if(e.length>0){n("请求【"+this._requests.length+"】个待发送的信息");for(var t=0;t<e.length;){var i=e[t];this._socket.send(i.buffer),null==i.rspObject||""!=i.rspCmd?e.splice(t,1):++t}this.updateNetTips(o.Requesting,this._requests.length>0)}},t.arrayBufferToJson=function(e){for(var t=new Uint8Array(e),n=new Array,i=0;i<t.length;i++)n.push(String.fromCharCode(t[i]));var s=n.join("");try{return JSON.parse(s)}catch(e){return console.error("Parsing error:",e),null}},t.onMessage=function(e){n("msg",e,this.ArrayBufferUTF8ToStr(e))},t.ArrayBufferUTF8ToStr=function(e){var t,n,i,s,r,o;for(e instanceof ArrayBuffer&&(e=new Uint8Array(e)),t="",i=e.length,n=0;n<i;)switch((s=e[n++])>>4){case 0:case 1:case 2:case 3:case 4:case 5:case 6:case 7:t+=String.fromCharCode(s);break;case 12:case 13:r=e[n++],t+=String.fromCharCode((31&s)<<6|63&r);break;case 14:r=e[n++],o=e[n++],t+=String.fromCharCode((15&s)<<12|(63&r)<<6|(63&o)<<0)}return t},t.onError=function(e){n("err",e),i(e)},t.onClosed=function(e){var t=this;this.clearTimer(),!this._disconnectCallback||this._disconnectCallback()?this.isAutoReconnect()?(this.updateNetTips(o.ReConnecting,!0),this._reconnectTimer=setTimeout((function(){t._socket.close(),t._state=c.Closed,t.connect(t._connectOptions),t._autoReconnect>0&&(t._autoReconnect-=1)}),this._reconnetTimeOut)):this._state=c.Closed:n("断开连接")},t.close=function(e,t){this.clearTimer(),this._listener={},this._requests.length=0,this._networkTips&&(this._networkTips.connectTips(!1),this._networkTips.reconnectTips(!1),this._networkTips.requestTips(!1)),this._socket?this._socket.close(e,t):this._state=c.Closed},t.closeSocket=function(e,t){this._socket&&this._socket.close(e,t)},t.send=function(e,t){return void 0===t&&(t=!1),this._state==c.Working||t?this._socket.send(e):this._state==c.Checking||this._state==c.Connecting?(this._requests.push({buffer:e,rspCmd:"",rspObject:null}),n("当前状态为【"+r[this._state]+"】,繁忙并缓冲发送数据"),0):(i("当前状态为【"+r[this._state]+"】,请求错误"),-1)},t.request=function(e,t,n,i){void 0===n&&(n=!0),void 0===i&&(i=!1);var s=this._protocolHelper.handlerRequestPackage(e);this.base_request(e,s,t,n,i)},t.requestUnique=function(e,t,i,s){void 0===i&&(i=!0),void 0===s&&(s=!1);for(var r=this._protocolHelper.handlerRequestPackage(e),o=0;o<this._requests.length;++o)if(this._requests[o].rspCmd==r)return n("命令【"+r+"】重复请求"),!1;return this.base_request(e,r,t,i,s),!0},t.base_request=function(e,t,i,s,r){void 0===s&&(s=!0),void 0===r&&(r=!1);var h=JSON.stringify(e);(this._state==c.Working||r)&&this._socket.send(h),n("队列命令为【"+t+"】的请求，等待请求数据的回调"),this._requests.push({buffer:h,rspCmd:t,rspObject:i}),s&&this.updateNetTips(o.Requesting,!0)},t.setResponeHandler=function(e,t,n){return null==t?(i("命令为【"+e+"】设置响应处理程序错误"),!1):(this._listener[e]=[{target:n,callback:t}],!0)},t.addResponeHandler=function(e,t,n){if(null==t)return i("命令为【"+e+"】添加响应处理程序错误"),!1;var s={target:n,callback:t};null==this._listener[e]?this._listener[e]=[s]:-1==this.getNetListenersIndex(e,s)&&this._listener[e].push(s);return!0},t.removeResponeHandler=function(e,t,n){if(null!=this._listener[e]&&null!=t){var i=this.getNetListenersIndex(e,{target:n,callback:t});-1!=i&&this._listener[e].splice(i,1)}},t.cleanListeners=function(e){void 0===e&&(e=""),""==e?this._listener={}:delete this._listener[e]},t.getNetListenersIndex=function(e,t){for(var n=-1,i=0;i<this._listener[e].length;i++){var s=this._listener[e][i];if(s.callback==t.callback&&s.target==t.target){n=i;break}}return n},t.resetReceiveMsgTimer=function(){var e=this;null!==this._receiveMsgTimer&&clearTimeout(this._receiveMsgTimer),this._receiveMsgTimer=setTimeout((function(){s("接收消息定时器关闭网络连接"),e._socket.close()}),this._receiveTime)},t.resetHearbeatTimer=function(){var e=this;null!==this._keepAliveTimer&&clearTimeout(this._keepAliveTimer),this._keepAliveTimer=setTimeout((function(){n("网络节点保持活跃发送心跳信息"),e.send(e._protocolHelper.getHearbeat())}),this._heartTime)},t.clearTimer=function(){null!==this._receiveMsgTimer&&clearTimeout(this._receiveMsgTimer),null!==this._keepAliveTimer&&clearTimeout(this._keepAliveTimer),null!==this._reconnectTimer&&clearTimeout(this._reconnectTimer)},t.isAutoReconnect=function(){return 0!=this._autoReconnect},t.rejectReconnect=function(){this._autoReconnect=0,this.clearTimer()},e}());t._RF.pop()}}}));

System.register("chunks:///_virtual/NetProtocolPako.ts",["cc"],(function(t){var e;return{setters:[function(t){e=t.cclegacy}],execute:function(){e._RF.push({},"06708Z0owJNxJFSCQ+3zS6k","NetProtocolPako",void 0);t("NetProtocolPako",function(){function t(){}var e=t.prototype;return e.getHeadlen=function(){return 0},e.getHearbeat=function(){return""},e.getPackageLen=function(t){return t.toString().length},e.checkResponsePackage=function(t){return!0},e.handlerResponsePackage=function(t){return 1==t.code&&(t.isCompress&&(t.data=(e=t.data,n=e.split("").map((function(t){return t.charCodeAt(0)})),a=new Uint8Array(n),pako.inflate(a,{to:"string"}))),t.data=JSON.parse(t.data),!0);var e,n,a},e.handlerRequestPackage=function(t){var e,n=t.cmd;return t.callback=n,t.isCompress&&(t.data=(e=t.data,pako.gzip(e,{to:"string"}))),n},e.getPackageId=function(t){return t.callback},t}());e._RF.pop()}}}));

System.register("chunks:///_virtual/NetProtocolProtobuf.ts",["cc"],(function(e){var t;return{setters:[function(e){t=e.cclegacy}],execute:function(){t._RF.push({},"90cb8aSw5RDOb2lRWuPhHLx","NetProtocolProtobuf",void 0);e("NetProtocolProtobuf",function(){function e(){}var t=e.prototype;return t.getHeadlen=function(){return 0},t.getHearbeat=function(){return""},t.getPackageLen=function(e){return e.toString().length},t.checkResponsePackage=function(e){return!0},t.handlerResponsePackage=function(e){return 1==e.code&&(e.isCompress,e.data=JSON.parse(e.data),!0)},t.handlerRequestPackage=function(e){var t=e.cmd;return e.callback=t,e.isCompress,t},t.getPackageId=function(e){return e.callback},e}());t._RF.pop()}}}));

System.register("chunks:///_virtual/ObjectUtil.ts",["cc"],(function(t){var e;return{setters:[function(t){e=t.cclegacy}],execute:function(){e._RF.push({},"b7061F/jLZOOJAfbMn+aaTV","ObjectUtil",void 0);t("ObjectUtil",function(){function t(){}return t.isObject=function(t){return"[object Object]"===Object.prototype.toString.call(t)},t.deepCopy=function(t){if(null==t||"object"!=typeof t)return t;var e=null;if(t instanceof Date)return(e=new Date).setTime(t.getTime()),e;if(t instanceof Array){e=[];for(var n=0,r=t.length;n<r;n++)e[n]=this.deepCopy(t[n]);return e}if(t instanceof Object){for(var c in e={},t)t.hasOwnProperty(c)&&(e[c]=this.deepCopy(t[c]));return e}console.warn("不支持的类型："+e)},t.copy=function(t){return JSON.parse(JSON.stringify(t))},t}());e._RF.pop()}}}));

System.register("chunks:///_virtual/QuadTree.ts",["cc"],(function(t){var s,e;return{setters:[function(t){s=t.cclegacy,e=t.UITransform}],execute:function(){s._RF.push({},"5d167/3i0ZJ0rmaXs1SQecj","QuadTree",void 0);t("Quadtree",function(){function t(t,s,e,i){this.max_objects=void 0,this.max_levels=void 0,this.level=void 0,this.bounds=void 0,this.objects=void 0,this.nodes=void 0,this.max_objects=s||16,this.max_levels=e||4,this.level=i||0,this.bounds=t,this.objects=[],this.nodes=[]}var s=t.prototype;return s.split=function(){var s=this.level+1,e=this.bounds.width/2,i=this.bounds.height/2,h=this.bounds.x,n=this.bounds.y;this.nodes[0]=new t({x:h+e,y:n,width:e,height:i},this.max_objects,this.max_levels,s),this.nodes[1]=new t({x:h,y:n,width:e,height:i},this.max_objects,this.max_levels,s),this.nodes[2]=new t({x:h,y:n+i,width:e,height:i},this.max_objects,this.max_levels,s),this.nodes[3]=new t({x:h+e,y:n+i,width:e,height:i},this.max_objects,this.max_levels,s)},s.getIndex=function(t){if(t){var s=t.getComponent(e).getBoundingBoxToWorld(),i=t.getPosition(),h=[],n=this.bounds.x+this.bounds.width/2,o=this.bounds.y+this.bounds.height/2,d=i.y<o,r=i.x<n,l=i.x+s.width>n,c=i.y+s.height>o;return d&&l&&h.push(0),r&&d&&h.push(1),r&&c&&h.push(2),l&&c&&h.push(3),h}},s.insert=function(t){var s,e=0;if(this.nodes.length)for(var i=this.getIndex(t),h=0;h<i.length;h++)this.nodes[i[h]].insert(t);else if(this.objects.push(t),this.objects.length>this.max_objects&&this.level<this.max_levels){for(this.nodes.length||this.split(),e=0;e<this.objects.length;e++){s=this.getIndex(this.objects[e]);for(var n=0;n<s.length;n++)this.nodes[s[n]].insert(this.objects[e])}this.objects=[]}},s.retrieve=function(t){var s=this.getIndex(t),e=this.objects;if(this.nodes.length)for(var i=0;i<s.length;i++)e=e.concat(this.nodes[s[i]].retrieve(t));return e=e.filter((function(t,s){return e.indexOf(t)>=s}))},s.clear=function(){this.objects=[];for(var t=0;t<this.nodes.length;t++)this.nodes.length&&this.nodes[t].clear();this.nodes=[]},t}());s._RF.pop()}}}));

System.register("chunks:///_virtual/Queue.ts",["cc"],(function(t){var e;return{setters:[function(t){e=t.cclegacy}],execute:function(){e._RF.push({},"0ccbcm5WjhMgaXJOQNGdyXu","Queue",void 0);t("Queue",function(){function t(t){this.items=[]}var e=t.prototype;return e.enqueue=function(t,e){this.items.push(t),e&&e(this)},e.dequeue=function(){return this.items.shift()},e.isEmpty=function(){return 0===this.items.length},t}());e._RF.pop()}}}));

System.register("chunks:///_virtual/Ready.ts",["./rollupPluginModLoBabelHelpers.js","cc","./App.ts","./xcore.ts","./ConstGlobal.ts","./Tool.ts","./Net.ts","./ConfigHelper.ts"],(function(e){var n,t,a,r,o,i,c,s,u,g,p,f,l,d,h,m,b,y,v,I;return{setters:[function(e){n=e.applyDecoratedDescriptor,t=e.inheritsLoose,a=e.initializerDefineProperty,r=e.assertThisInitialized,o=e.asyncToGenerator,i=e.regeneratorRuntime},function(e){c=e.cclegacy,s=e._decorator,u=e.Button,g=e.Component,p=e.Sprite,f=e.log},function(e){l=e.default},function(e){d=e.xcore},function(e){h=e.C_Bundle,m=e.C_Scene,b=e.E_Channel},function(e){y=e.default},function(e){v=e.default},function(e){I=e.ConfigHelper}],execute:function(){var x,D,L,T,w;c._RF.push({},"55676oUpURLcJ1cSCh8g9Rt","Ready",void 0);var C=s.ccclass,k=s.property;e("Ready",(x=C("Ready"),D=k(u),x((w=n((T=function(e){function n(){for(var n,t=arguments.length,o=new Array(t),i=0;i<t;i++)o[i]=arguments[i];return n=e.call.apply(e,[this].concat(o))||this,a(n,"btnLogin",w,r(n)),n}t(n,e);var c=n.prototype;return c.onLoad=function(){var e=o(i().mark((function e(){return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this.btnLogin.interactable=!1,e.next=3,l.getInstance().init();case 3:this.btnLogin.interactable=!0,this.btnLogin.node.on("click",this.doLogin,this);case 5:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}(),c.doLogin=function(){var e=o(i().mark((function e(){return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,this.btnLogin.interactable=!1,this.btnLogin.getComponent(p).grayscale=!0,e.next=5,this.loadRes();case 5:return e.next=7,this.login();case 7:return e.next=9,this.updateGiftTopInfo();case 9:l.getInstance().initOperation(),d.ui.switchScene(h.abMain,m.Main),e.next=18;break;case 13:e.prev=13,e.t0=e.catch(0),this.btnLogin.interactable=!0,this.btnLogin.getComponent(p).grayscale=!1,d.ui.showToast("登录失败 err:"+e.t0);case 18:case"end":return e.stop()}}),e,this,[[0,13]])})));return function(){return e.apply(this,arguments)}}(),c.loadRes=function(){var e=o(i().mark((function e(){var n;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=d.channel==b.TIKTOK?"https://gz-cdn-1258783731.cos.ap-guangzhou.myqcloud.com/2024/2nantianmen/game_configs/prod/_total.json?t="+(new Date).getTime():"https://gz-cdn-1258783731.cos.ap-guangzhou.myqcloud.com/2024/2nantianmen/game_configs/dev/_total.json?t="+(new Date).getTime(),d.config){e.next=5;break}return e.next=4,d.res.remoteLoadJson(n);case 4:d.config=e.sent;case 5:f("gameConfig:",d.config);case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),c.login=function(){var e=o(i().mark((function e(){var n,t,a,r,o=this;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=y.getCommandLineArgs(),e.next=3,v.getCombatId();case 3:return e.next=5,v.getServerTimeStamp();case 5:return console.log("query",n,n.token),t=I.getInstance().getConstantConfigByKey("channelId"),a=I.getInstance().getConstantConfigByKey("douyinAppID"),d.channel==b.TIKTOK?(d.gameData.token=n.token,d.gameData.appId=a||"ttdb73adce4447fe4a10",d.gameData.channelId=t||"1006"):(d.gameData.appId="merchant-d8748165-38f1-4381-b524-bd84a8e34ff0",d.gameData.channelId="1006",d.channel=b.UHO,d.gameData.userName="grndpagaming",d.gameData.password="123456"),e.next=12,v.login(d.channel,d.gameData.channelId,d.gameData.token,d.gameData.userName,d.gameData.password,d.gameData.appId,d.gameData.combatId);case 12:return e.next=14,v.getLiverInfo();case 14:return r=I.getInstance().getLiverKeyByLev(),e.next=17,v.updateAnchorRankGameLev(r,d.gameData.baseInfo.id);case 17:if(d.channel!=b.TIKTOK){e.next=20;break}return e.next=20,v.updateTokenToIo();case 20:return console.log("liveinfo",d.gameData.baseInfo,d.gameData.gameLev),e.abrupt("return",new Promise((function(e,n){o.scheduleOnce((function(){e(!0)}),.5)})));case 22:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),c.updateGiftTopInfo=function(){var e=o(i().mark((function e(){var n,t,a,r;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(d.channel!=b.TIKTOK){e.next=6;break}for(n=[],t=I.getInstance().getGiftConfigs(),a=0;a<t.length;a++)(r=t[a].douyinGiftId)&&n.push(r);return e.next=6,v.updateGiftTopInfo(d.gameData.combatId,n);case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),n}(g)).prototype,"btnLogin",[D],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),L=T))||L));c._RF.pop()}}}));

System.register("chunks:///_virtual/RegexUtil.ts",["cc"],(function(t){var e;return{setters:[function(t){e=t.cclegacy}],execute:function(){e._RF.push({},"22cd856dXFBc7BN4Td4Q/Yd","RegexUtil",void 0);t("RegexUtil",function(){function t(){}return t.isDoubleWord=function(t){return/[^\x00-\xff]/.test(t)},t.isMobilePhone=function(t){return/^[1][3,4,5,6,7,8,9][0-9]{9}$/.test(t)},t.isIdCard=function(t){return/^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9xX]$/.test(t)},t}());e._RF.pop()}}}));

System.register("chunks:///_virtual/ResGameResult.ts",["./rollupPluginModLoBabelHelpers.js","cc","./room.ts","./BroadcastCtrl.ts","./MessageEvent.ts"],(function(e){var t,s,n,o,r,c,u,a;return{setters:[function(e){t=e.inheritsLoose,s=e.createClass},function(e){n=e.cclegacy,o=e._decorator},function(e){r=e.RoomGameAwardPush},function(e){c=e.default},function(e){u=e.MessageCmdEvent,a=e.MessageRoomSubCmd}],execute:function(){var i;n._RF.push({},"5943eCRnNxGyLdNTUasw20H","ResGameResult",void 0);var l=o.ccclass;e("default",l()(i=function(e){function n(){var t;return(t=e.call(this)||this).cmd=u.roomCmd,t.subCmd=a.rewardPush,t.addListen(),t}return t(n,e),n.prototype.receive=function(t,s){e.prototype.receive.call(this,t,r.decode(s))},s(n,null,[{key:"inst",get:function(){return this.getInst(n)}}]),n}(c))||i);n._RF.pop()}}}));

System.register("chunks:///_virtual/ResGameStatus.ts",["./rollupPluginModLoBabelHelpers.js","cc","./user.mjs_cjs=&original=.js","./BroadcastCtrl.ts","./MessageEvent.ts","./user.js"],(function(e){var t,s,o,u,n,r,a,c;return{setters:[function(e){t=e.inheritsLoose,s=e.createClass},function(e){o=e.cclegacy,u=e._decorator},null,function(e){n=e.default},function(e){r=e.MessageCmdEvent,a=e.MessageRoomSubCmd},function(e){c=e.default}],execute:function(){var i;o._RF.push({},"effbefzoOtPFqikT0zFcVDI","ResGameStatus",void 0);var l=u.ccclass;e("default",l()(i=function(e){function o(){var t;return(t=e.call(this)||this).cmd=r.userCmd,t.subCmd=a.roomStatusPush,t.addListen(),t}return t(o,e),o.prototype.receive=function(t,s){console.log("ResGameStatus:",c.pb.RoomStatusPushPb.decode(s));var o=c.pb.RoomStatusPushPb.decode(s).json;e.prototype.receive.call(this,t,JSON.parse(o))},s(o,null,[{key:"inst",get:function(){return this.getInst(o)}}]),o}(n))||i);o._RF.pop()}}}));

System.register("chunks:///_virtual/ResKickoutUser.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ExternalMessage.ts","./BroadcastCtrl.ts","./MessageEvent.ts"],(function(e){var t,s,n,c,o,r,u,i;return{setters:[function(e){t=e.inheritsLoose,s=e.createClass},function(e){n=e.cclegacy,c=e._decorator},function(e){o=e.StringValue},function(e){r=e.default},function(e){u=e.MessageCmdEvent,i=e.MessageRoomSubCmd}],execute:function(){var a;n._RF.push({},"e57003fyHBDxZwm9WFfZvTD","ResKickoutUser",void 0);var l=c.ccclass;e("default",l()(a=function(e){function n(){var t;return(t=e.call(this)||this).cmd=u.roomCmd,t.subCmd=i.kickout,t.addListen(),t}return t(n,e),n.prototype.receive=function(t,s){e.prototype.receive.call(this,t,o.decode(s))},s(n,null,[{key:"inst",get:function(){return this.getInst(n)}}]),n}(r))||a);n._RF.pop()}}}));

System.register("chunks:///_virtual/ResLoader.ts",["./rollupPluginModLoBabelHelpers.js","cc"],(function(e){var n,r,t,o,u,a,s;return{setters:[function(e){n=e.asyncToGenerator,r=e.regeneratorRuntime},function(e){t=e.cclegacy,o=e.js,u=e.resources,a=e.Asset,s=e.assetManager}],execute:function(){t._RF.push({},"7a97deqQyVA4bM0bepNwfWL","ResLoader",void 0);var i=o.isChildClassOf;i||(i=i);e("default",function(){function e(){}return e.getLoader=function(t){return new Promise(n(r().mark((function n(o,a){var s;return r().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(!t.bundle){n.next=7;break}return n.next=3,e.getBundle(t.bundle);case 3:s=n.sent,o(s),n.next=8;break;case 7:o(u);case 8:case"end":return n.stop()}}),n)}))))},e.formatArgs=function(){do{if(arguments.length<2)break;var e={};if("string"==typeof arguments[1]||arguments[1]instanceof Array){if("string"!=typeof arguments[0])break;e.bundle=arguments[0],e.url=arguments[1],arguments.length>2&&i(arguments[2],a)&&(e.type=arguments[2])}else{if(!("string"==typeof arguments[0]||arguments[0]instanceof Array))break;e.url=arguments[0],i(arguments[1],a)&&(e.type=arguments[1])}return"function"!=typeof arguments[arguments.length-1]||i(arguments[arguments.length-1],a)||(e.onCompleted=arguments[arguments.length-1],"function"!=typeof arguments[arguments.length-2]||i(arguments[arguments.length-2],a)||(e.onProgess=arguments[arguments.length-2])),e}while(0);return console.error("formatArgs error "+arguments),null},e.getBundle=function(){var e=n(r().mark((function e(n){return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise((function(e,r){var t=s.getBundle(n);t?e(t):s.loadBundle(n,(function(n,t){n?(console.log("load bundle fail err:"+n),r(null)):e(t)}))})));case 1:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),e.makeFinishCallback=function(e){return function(n,r){e.onCompleted&&e.onCompleted(n,r),n?e.reject(n):e.resolve(r)}},e.getUuid=function(e,n,r){if("function"==typeof e.getInfoWithPath){var t=e.getInfoWithPath(n,r);return t?t.uuid:null}return null},e.startLoad=function(){var t=n(r().mark((function n(t){return r().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",new Promise((function(n,r){t.resolve=n,t.reject=r;var o=e.makeFinishCallback(t);e.getLoader(t).then((function(n){"string"==typeof t.url?e.getUuid(n,t.url,t.type)?t.type?n.load(t.url,t.type,t.onProgess,o):n.load(t.url,t.onProgess,o):s.loadRemote(t.url,t.onProgess,o):n.load(t.url,t.type,t.onProgess,o)}))})));case 1:case"end":return n.stop()}}),n)})));return function(e){return t.apply(this,arguments)}}(),e.load=function(){var n=e.formatArgs.apply(e,arguments);return e.startLoad(n)},e}());t._RF.pop()}}}));

System.register("chunks:///_virtual/ResManager.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ResLoader.ts","./Singleton.ts"],(function(e){var t,n,r,a,s,i,c,u,o,l,p,h,f,d,m,v,x,g,b,A,w,k,S;return{setters:[function(e){t=e.inheritsLoose,n=e.asyncToGenerator,r=e.regeneratorRuntime},function(e){a=e.cclegacy,s=e.Asset,i=e.sp,c=e.Size,u=e.UITransform,o=e.SpriteAtlas,l=e.log,p=e.Prefab,h=e.SpriteFrame,f=e.Texture2D,d=e.ImageAsset,m=e.VideoClip,v=e.TextAsset,x=e.JsonAsset,g=e.rect,b=e.v2,A=e.size,w=e.assetManager},function(e){k=e.default},function(e){S=e.Singleton}],execute:function(){a._RF.push({},"1eaf3c/N7ZBB4Bw/1hSlQiK","ResManager",void 0);e("ResManager",function(e){function a(){for(var t,n=arguments.length,r=new Array(n),a=0;a<n;a++)r[a]=arguments[a];return(t=e.call.apply(e,[this].concat(r))||this).cacheAssetList=[],t.cacheAtlas=new Map,t.cacheMonsterAtalsNames=[],t.cacheImg2sfr={},t}t(a,e);var S=a.prototype;return S.cacheAsset=function(e,t){void 0===t&&(t=!0),e?e instanceof s?(t&&e.addRef(),this.cacheAssetList.filter((function(t){return t.uuid==e.uuid})).length<=0&&this.cacheAssetList.push(e)):console.log("cacheItem "+e+" is not  Asset"):console.warn("cacheItem error, item is "+e)},S.releaseAssetList=function(e){for(var t=0;t<e.length;t++){var n=e[t];n instanceof s&&this.releaseAsset(n)}e=null},S.releaseAsset=function(e){},S.releaseSpineAsset=function(e){var t=e.getComponent(i.Skeleton);t&&t.skeletonData&&t.skeletonData._depens&&(t.skeletonData.decRef(),this.releaseAssetList(t.skeletonData._depens),t.skeletonData=null,e.destroy(),e=null)},S.bundleLoadPrefab=function(){var e=n(r().mark((function e(t,n){var a;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,t&&"default"!==t&&"resources"!==t){e.next=7;break}return e.next=4,k.load(n,p);case 4:a=e.sent,e.next=10;break;case 7:return e.next=9,k.load(t,n,p);case 9:a=e.sent;case 10:return this.cacheAsset(a),e.abrupt("return",a);case 14:e.prev=14,e.t0=e.catch(0),l("load prefab fail!",e.t0);case 17:case"end":return e.stop()}}),e,this,[[0,14]])})));return function(t,n){return e.apply(this,arguments)}}(),S.bundleLoadSprite=function(){var e=n(r().mark((function e(t,n,a,s){var i,c,u;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=6;break}return e.next=3,k.load(n,d);case 3:i=e.sent,e.next=9;break;case 6:return e.next=8,k.load(t,n,d);case 8:i=e.sent;case 9:return(c=this.cacheImg2sfr[i.nativeUrl])&&c.isValid||(c=new h,(u=new f).image=i,c.texture=u,this.cacheImg2sfr[i.nativeUrl]=c),a&&a.node&&a.node.isValid&&(a.spriteFrame=c),this.cacheAsset(i),a&&a.node&&a.node.isValid&&s&&this.resizeSprite(a,s),e.abrupt("return",c);case 15:case"end":return e.stop()}}),e,this)})));return function(t,n,r,a){return e.apply(this,arguments)}}(),S.bundleLoadAtlas=function(){var e=n(r().mark((function e(t,n){var a;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=6;break}return e.next=3,k.load(n,o);case 3:a=e.sent,e.next=9;break;case 6:return e.next=8,k.load(t,n,o);case 8:a=e.sent;case 9:return e.abrupt("return",a);case 10:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}(),S.bundleLoadSpine=function(){var e=n(r().mark((function e(t,n,a){var s;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=6;break}return e.next=3,k.load(n,i.SkeletonData);case 3:s=e.sent,e.next=9;break;case 6:return e.next=8,k.load(t,n,i.SkeletonData);case 8:s=e.sent;case 9:if(!(a&&a.node&&a.node.isValid)){e.next=14;break}return a.skeletonData=s,a._updateSkeletonData(),this.cacheAsset(s),e.abrupt("return",s);case 14:case"end":return e.stop()}}),e,this)})));return function(t,n,r){return e.apply(this,arguments)}}(),S.bundleLoadClip=function(){var e=n(r().mark((function e(t,n){var a;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=6;break}return e.next=3,k.load(n,m);case 3:a=e.sent,e.next=9;break;case 6:return e.next=8,k.load(t,n,m);case 8:a=e.sent;case 9:return e.abrupt("return",a);case 10:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}(),S.remoteLoadSprite=function(){var e=n(r().mark((function e(t,n,a){var s,i,c;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=3;break}return l("spr path cannot be null"),e.abrupt("return");case 3:return e.next=5,k.load(t,d);case 5:return s=e.sent,(i=this.cacheImg2sfr[s.nativeUrl])&&i.isValid||(i=new h,(c=new f).image=s,i.texture=c,this.cacheImg2sfr[s.nativeUrl]=i),n&&n.node&&n.node.isValid&&(n.spriteFrame=i),this.cacheAsset(s),n&&n.node&&n.node.isValid&&a&&this.resizeSprite(n,a),e.abrupt("return",i);case 12:case"end":return e.stop()}}),e,this)})));return function(t,n,r){return e.apply(this,arguments)}}(),S.resizeSprite=function(e,t){if(e&&t){var n=e.spriteFrame.rect;if(t instanceof c){var r=t,a=Math.min(r.width/n.width,r.height/n.height);e.node.getComponent(u).width=n.width*a,e.node.getComponent(u).height=n.height*a}else if("number"==typeof t){var s=t;a=Math.min(s/n.width,s/n.height);e.node.getComponent(u).width=n.width*a,e.node.getComponent(u).height=n.height*a}}},S.remoteLoadSpine=function(){var e=n(r().mark((function e(t,n,a){var s,c,u,o,l,p,h,m,g,b;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t.endsWith("/")||(t+="/"),e.prev=1,e.next=4,k.load(""+t+n+".json",x);case 4:return s=e.sent,e.next=7,k.load(""+t+n+".atlas",v);case 7:c=e.sent,u=[],o=[],l=c.text.split("\n"),p=0;case 12:if(!(p<l.length)){e.next=26;break}if(!(h=l[p].trim()).endsWith(".png")){e.next=23;break}return u.push(h),m=new f,e.next=19,k.load(""+t+h,d);case 19:g=e.sent,m.image=g,this.cacheAsset(m),o.push(m);case 23:p++,e.next=12;break;case 26:return(b=new i.SkeletonData).skeletonJson=s.json,b.atlasText=c.text,b.textures=o,b.textureNames=u,b._depens=[s,c].concat(o),a.skeletonData=b,a._updateSkeletonData(),this.cacheAsset(s),this.cacheAsset(c),e.abrupt("return",b);case 39:return e.prev=39,e.t0=e.catch(1),e.abrupt("return",Promise.reject(e.t0));case 42:case"end":return e.stop()}}),e,this,[[1,39]])})));return function(t,n,r){return e.apply(this,arguments)}}(),S.remoteLoadAtlas=function(){var e=n(r().mark((function e(t){var n,a,s,i,c,u,l,p,m,v,x,w,S,y,L;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t.endsWith(".plist")){e.next=3;break}return console.warn("请传入以.plist结束的地址"),e.abrupt("return");case 3:return e.prev=3,e.next=6,k.load(t,o);case 6:return a=e.sent,s=t.split("/"),i=null==(n=s[s.length-1])?void 0:n.split(".")[0],s[s.length-1]=a._nativeAsset.metadata.textureFileName,c=s.join("/"),e.next=13,k.load(c,d);case 13:for(m in u=e.sent,(l=new f).image=u,p=new o,a._nativeAsset.frames)v=a._nativeAsset.frames[m],x=v.spriteOffset.replace(/{|}/g,"").split(",").map((function(e){return parseInt(e)})),w=v.spriteSourceSize.replace(/{|}/g,"").split(",").map((function(e){return parseInt(e)})),S=v.textureRect.replace(/{|}/g,"").split(",").map((function(e){return parseInt(e)})),y=m.substring(0,m.length-4),(L=new h).texture=l,L.rect=g(S[0],S[1],S[2],S[3]),L.offset=b(x[0],x[1]),L.originalSize=A(w[0],w[1]),p.spriteFrames[y]=L;return p._depens=[a,u],this.releaseAssetList(p._depens),p.name=i,e.abrupt("return",p);case 24:return e.prev=24,e.t0=e.catch(3),e.abrupt("return",Promise.reject(e.t0));case 27:case"end":return e.stop()}}),e,this,[[3,24]])})));return function(t){return e.apply(this,arguments)}}(),S.remoteLoadJson=function(){var e=n(r().mark((function e(t){var n;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,k.load(""+t,x);case 3:return n=e.sent,e.abrupt("return",n.json);case 7:return e.prev=7,e.t0=e.catch(0),e.abrupt("return",Promise.reject(e.t0));case 10:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(t){return e.apply(this,arguments)}}(),S.remoteLoadAny=function(){var e=n(r().mark((function e(t,n){var a;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,w.loadRemote(t,n);case 3:return a=e.sent,e.abrupt("return",a);case 7:return e.prev=7,e.t0=e.catch(0),e.abrupt("return",Promise.reject(e.t0));case 10:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(t,n){return e.apply(this,arguments)}}(),S.getAtlas=function(e){var t=this.cacheAtlas.get(e);return t||(this.cacheAtlas.set(e,new o),(t=this.cacheAtlas.get(e)).addRef(),this.cacheMonsterAtalsNames||(this.cacheMonsterAtalsNames=[]),this.cacheMonsterAtalsNames.push(e),l("生成图集",e,"总数》",this.cacheAtlas.size)),t},S.addAtlasSprite=function(e,t,n){this.getAtlas(e).spriteFrames[t]=n},S.clearCache=function(){this.cacheAtlas.clear(),this.cacheMonsterAtalsNames=[],this.cacheImg2sfr={}},a}(S));a._RF.pop()}}}));

System.register("chunks:///_virtual/ResRoomInfo.ts",["./rollupPluginModLoBabelHelpers.js","cc","./room.ts","./BroadcastCtrl.ts","./MessageEvent.ts"],(function(e){var t,o,n,s,r,c,i,u;return{setters:[function(e){t=e.inheritsLoose,o=e.createClass},function(e){n=e.cclegacy,s=e._decorator},function(e){r=e.RoomUserMainPb},function(e){c=e.default},function(e){i=e.MessageCmdEvent,u=e.MessageRoomSubCmd}],execute:function(){var a;n._RF.push({},"74721fMWpVCFIcQH1GBzDLe","ResRoomInfo",void 0);var l=s.ccclass;e("default",l()(a=function(e){function n(){var t;return(t=e.call(this)||this).cmd=i.roomCmd,t.subCmd=u.roomUserMainPush,t.addListen(),t}return t(n,e),n.prototype.receive=function(t,o){e.prototype.receive.call(this,t,r.decode(o))},o(n,null,[{key:"inst",get:function(){return this.getInst(n)}}]),n}(c))||a);n._RF.pop()}}}));

System.register("chunks:///_virtual/ResUserJoin.ts",["./rollupPluginModLoBabelHelpers.js","cc","./room.ts","./BroadcastCtrl.ts","./MessageEvent.ts"],(function(e){var t,s,o,n,r,c,u,i;return{setters:[function(e){t=e.inheritsLoose,s=e.createClass},function(e){o=e.cclegacy,n=e._decorator},function(e){r=e.RoomUserPushPb},function(e){c=e.default},function(e){u=e.MessageCmdEvent,i=e.MessageRoomSubCmd}],execute:function(){var a;o._RF.push({},"eb5638fqfJIOJewYrfwcwUk","ResUserJoin",void 0);var l=n.ccclass;e("default",l()(a=function(e){function o(){var t;return(t=e.call(this)||this).cmd=u.roomCmd,t.subCmd=i.roomUserPush,t.addListen(),t}return t(o,e),o.prototype.receive=function(t,s){e.prototype.receive.call(this,t,r.decode(s))},s(o,null,[{key:"inst",get:function(){return this.getInst(o)}}]),o}(c))||a);o._RF.pop()}}}));

System.register("chunks:///_virtual/room.ts",["./rollupPluginModLoBabelHelpers.js","cc","./index.js","./minimal.mjs_cjs=&original=.js","./minimal.js"],(function(e){var t,a,r,i;return{setters:[function(e){t=e.createForOfIteratorHelperLoose},function(e){a=e.cclegacy},function(e){r=e.default},null,function(e){i=e.default}],execute:function(){e({roomStatusEnumFromJSON:o,roomStatusEnumToJSON:u}),a._RF.push({},"aedd6rmR6pIYJjPuvDIICj2","room",void 0);e("protobufPackage","pb");var n=e("RoomStatusEnum",function(e){return e[e.WAITING=0]="WAITING",e[e.GAME_READY=1]="GAME_READY",e[e.GAME_BIG_COUNTDOWN=2]="GAME_BIG_COUNTDOWN",e[e.GAME_COUNTDOWN=3]="GAME_COUNTDOWN",e[e.GAME_START=4]="GAME_START",e[e.GAME_END=5]="GAME_END",e[e.CLOSE=6]="CLOSE",e[e.UN_OPEN=7]="UN_OPEN",e[e.UNRECOGNIZED=-1]="UNRECOGNIZED",e}({}));function o(e){switch(e){case 0:case"WAITING":return n.WAITING;case 1:case"GAME_READY":return n.GAME_READY;case 2:case"GAME_BIG_COUNTDOWN":return n.GAME_BIG_COUNTDOWN;case 3:case"GAME_COUNTDOWN":return n.GAME_COUNTDOWN;case 4:case"GAME_START":return n.GAME_START;case 5:case"GAME_END":return n.GAME_END;case 6:case"CLOSE":return n.CLOSE;case 7:case"UN_OPEN":return n.UN_OPEN;case-1:case"UNRECOGNIZED":default:return n.UNRECOGNIZED}}function u(e){switch(e){case n.WAITING:return"WAITING";case n.GAME_READY:return"GAME_READY";case n.GAME_BIG_COUNTDOWN:return"GAME_BIG_COUNTDOWN";case n.GAME_COUNTDOWN:return"GAME_COUNTDOWN";case n.GAME_START:return"GAME_START";case n.GAME_END:return"GAME_END";case n.CLOSE:return"CLOSE";case n.UN_OPEN:return"UN_OPEN";case n.UNRECOGNIZED:default:return"UNRECOGNIZED"}}var s=e("GameScoreReqPb",{encode:function(e,t){return void 0===t&&(t=i.Writer.create()),0!==e.score&&t.uint32(8).int32(e.score),t},decode:function(e,t){for(var a=e instanceof i.Reader?e:i.Reader.create(e),r=void 0===t?a.len:a.pos+t,n={score:0};a.pos<r;){var o=a.uint32();switch(o>>>3){case 1:if(8!==o)break;n.score=a.int32();continue}if(4==(7&o)||0===o)break;a.skipType(7&o)}return n},fromJSON:function(e){return{score:I(e.score)?Number(e.score):0}},toJSON:function(e){var t={};return void 0!==e.score&&(t.score=Math.round(e.score)),t},create:function(e){return s.fromPartial(null!=e?e:{})},fromPartial:function(e){var t,a={score:0};return a.score=null!=(t=e.score)?t:0,a}});var d=e("RoomBusinessConfigPb",{encode:function(e,t){return void 0===t&&(t=i.Writer.create()),""!==e.bgImg&&t.uint32(10).string(e.bgImg),""!==e.broadcastAudioUrl&&t.uint32(18).string(e.broadcastAudioUrl),""!==e.startAudioUrl&&t.uint32(26).string(e.startAudioUrl),t},decode:function(e,t){for(var a=e instanceof i.Reader?e:i.Reader.create(e),r=void 0===t?a.len:a.pos+t,n={bgImg:"",broadcastAudioUrl:"",startAudioUrl:""};a.pos<r;){var o=a.uint32();switch(o>>>3){case 1:if(10!==o)break;n.bgImg=a.string();continue;case 2:if(18!==o)break;n.broadcastAudioUrl=a.string();continue;case 3:if(26!==o)break;n.startAudioUrl=a.string();continue}if(4==(7&o)||0===o)break;a.skipType(7&o)}return n},fromJSON:function(e){return{bgImg:I(e.bgImg)?String(e.bgImg):"",broadcastAudioUrl:I(e.broadcastAudioUrl)?String(e.broadcastAudioUrl):"",startAudioUrl:I(e.startAudioUrl)?String(e.startAudioUrl):""}},toJSON:function(e){var t={};return void 0!==e.bgImg&&(t.bgImg=e.bgImg),void 0!==e.broadcastAudioUrl&&(t.broadcastAudioUrl=e.broadcastAudioUrl),void 0!==e.startAudioUrl&&(t.startAudioUrl=e.startAudioUrl),t},create:function(e){return d.fromPartial(null!=e?e:{})},fromPartial:function(e){var t,a,r,i={bgImg:"",broadcastAudioUrl:"",startAudioUrl:""};return i.bgImg=null!=(t=e.bgImg)?t:"",i.broadcastAudioUrl=null!=(a=e.broadcastAudioUrl)?a:"",i.startAudioUrl=null!=(r=e.startAudioUrl)?r:"",i}});var c=e("RoomGameAwardPush",{encode:function(e,a){void 0===a&&(a=i.Writer.create());for(var r,n=t(e.ranks);!(r=n()).done;){var o=r.value;N.encode(o,a.uint32(10).fork()).ldelim()}return void 0!==e.my&&N.encode(e.my,a.uint32(18).fork()).ldelim(),0!==e.total&&a.uint32(24).int32(e.total),""!==e.awardId&&a.uint32(34).string(e.awardId),""!==e.awardUrl&&a.uint32(42).string(e.awardUrl),""!==e.awardName&&a.uint32(50).string(e.awardName),""!==e.address&&a.uint32(58).string(e.address),0!==e.maxScore&&a.uint32(64).int32(e.maxScore),0!==e.awardPeopleCount&&a.uint32(72).int32(e.awardPeopleCount),!0===e.emptyAward&&a.uint32(80).bool(e.emptyAward),!0===e.result&&a.uint32(88).bool(e.result),a},decode:function(e,t){for(var a=e instanceof i.Reader?e:i.Reader.create(e),r=void 0===t?a.len:a.pos+t,n={ranks:[],my:void 0,total:0,awardId:"",awardUrl:"",awardName:"",address:"",maxScore:0,awardPeopleCount:0,emptyAward:!1,result:!1};a.pos<r;){var o=a.uint32();switch(o>>>3){case 1:if(10!==o)break;n.ranks.push(N.decode(a,a.uint32()));continue;case 2:if(18!==o)break;n.my=N.decode(a,a.uint32());continue;case 3:if(24!==o)break;n.total=a.int32();continue;case 4:if(34!==o)break;n.awardId=a.string();continue;case 5:if(42!==o)break;n.awardUrl=a.string();continue;case 6:if(50!==o)break;n.awardName=a.string();continue;case 7:if(58!==o)break;n.address=a.string();continue;case 8:if(64!==o)break;n.maxScore=a.int32();continue;case 9:if(72!==o)break;n.awardPeopleCount=a.int32();continue;case 10:if(80!==o)break;n.emptyAward=a.bool();continue;case 11:if(88!==o)break;n.result=a.bool();continue}if(4==(7&o)||0===o)break;a.skipType(7&o)}return n},fromJSON:function(e){return{ranks:Array.isArray(null==e?void 0:e.ranks)?e.ranks.map((function(e){return N.fromJSON(e)})):[],my:I(e.my)?N.fromJSON(e.my):void 0,total:I(e.total)?Number(e.total):0,awardId:I(e.awardId)?String(e.awardId):"",awardUrl:I(e.awardUrl)?String(e.awardUrl):"",awardName:I(e.awardName)?String(e.awardName):"",address:I(e.address)?String(e.address):"",maxScore:I(e.maxScore)?Number(e.maxScore):0,awardPeopleCount:I(e.awardPeopleCount)?Number(e.awardPeopleCount):0,emptyAward:!!I(e.emptyAward)&&Boolean(e.emptyAward),result:!!I(e.result)&&Boolean(e.result)}},toJSON:function(e){var t={};return e.ranks?t.ranks=e.ranks.map((function(e){return e?N.toJSON(e):void 0})):t.ranks=[],void 0!==e.my&&(t.my=e.my?N.toJSON(e.my):void 0),void 0!==e.total&&(t.total=Math.round(e.total)),void 0!==e.awardId&&(t.awardId=e.awardId),void 0!==e.awardUrl&&(t.awardUrl=e.awardUrl),void 0!==e.awardName&&(t.awardName=e.awardName),void 0!==e.address&&(t.address=e.address),void 0!==e.maxScore&&(t.maxScore=Math.round(e.maxScore)),void 0!==e.awardPeopleCount&&(t.awardPeopleCount=Math.round(e.awardPeopleCount)),void 0!==e.emptyAward&&(t.emptyAward=e.emptyAward),void 0!==e.result&&(t.result=e.result),t},create:function(e){return c.fromPartial(null!=e?e:{})},fromPartial:function(e){var t,a,r,i,n,o,u,s,d,c,m={ranks:[],my:void 0,total:0,awardId:"",awardUrl:"",awardName:"",address:"",maxScore:0,awardPeopleCount:0,emptyAward:!1,result:!1};return m.ranks=(null==(t=e.ranks)?void 0:t.map((function(e){return N.fromPartial(e)})))||[],m.my=void 0!==e.my&&null!==e.my?N.fromPartial(e.my):void 0,m.total=null!=(a=e.total)?a:0,m.awardId=null!=(r=e.awardId)?r:"",m.awardUrl=null!=(i=e.awardUrl)?i:"",m.awardName=null!=(n=e.awardName)?n:"",m.address=null!=(o=e.address)?o:"",m.maxScore=null!=(u=e.maxScore)?u:0,m.awardPeopleCount=null!=(s=e.awardPeopleCount)?s:0,m.emptyAward=null!=(d=e.emptyAward)&&d,m.result=null!=(c=e.result)&&c,m}});var m=e("RoomGameEndPush",{encode:function(e,a){void 0===a&&(a=i.Writer.create());for(var r,n=t(e.users);!(r=n()).done;){var o=r.value;N.encode(o,a.uint32(10).fork()).ldelim()}return a},decode:function(e,t){for(var a=e instanceof i.Reader?e:i.Reader.create(e),r=void 0===t?a.len:a.pos+t,n={users:[]};a.pos<r;){var o=a.uint32();switch(o>>>3){case 1:if(10!==o)break;n.users.push(N.decode(a,a.uint32()));continue}if(4==(7&o)||0===o)break;a.skipType(7&o)}return n},fromJSON:function(e){return{users:Array.isArray(null==e?void 0:e.users)?e.users.map((function(e){return N.fromJSON(e)})):[]}},toJSON:function(e){var t={};return e.users?t.users=e.users.map((function(e){return e?N.toJSON(e):void 0})):t.users=[],t},create:function(e){return m.fromPartial(null!=e?e:{})},fromPartial:function(e){var t,a={users:[]};return a.users=(null==(t=e.users)?void 0:t.map((function(e){return N.fromPartial(e)})))||[],a}});var l=e("RoomGamePush",{encode:function(e,a){void 0===a&&(a=i.Writer.create());for(var r,n=t(e.users);!(r=n()).done;){var o=r.value;N.encode(o,a.uint32(10).fork()).ldelim()}return a},decode:function(e,t){for(var a=e instanceof i.Reader?e:i.Reader.create(e),r=void 0===t?a.len:a.pos+t,n={users:[]};a.pos<r;){var o=a.uint32();switch(o>>>3){case 1:if(10!==o)break;n.users.push(N.decode(a,a.uint32()));continue}if(4==(7&o)||0===o)break;a.skipType(7&o)}return n},fromJSON:function(e){return{users:Array.isArray(null==e?void 0:e.users)?e.users.map((function(e){return N.fromJSON(e)})):[]}},toJSON:function(e){var t={};return e.users?t.users=e.users.map((function(e){return e?N.toJSON(e):void 0})):t.users=[],t},create:function(e){return l.fromPartial(null!=e?e:{})},fromPartial:function(e){var t,a={users:[]};return a.users=(null==(t=e.users)?void 0:t.map((function(e){return N.fromPartial(e)})))||[],a}});var f=e("RoomMainInfoPb",{encode:function(e,a){void 0===a&&(a=i.Writer.create()),""!==e.busId&&a.uint32(10).string(e.busId),0!==e.status&&a.uint32(16).int32(e.status);for(var r,n=t(e.userList);!(r=n()).done;){var o=r.value;N.encode(o,a.uint32(26).fork()).ldelim()}return 0!==e.nextStepTime&&a.uint32(32).int64(e.nextStepTime),0!==e.gameStartTime&&a.uint32(40).int64(e.gameStartTime),0!==e.totalPeople&&a.uint32(48).int32(e.totalPeople),!0===e.firstRound&&a.uint32(56).bool(e.firstRound),0!==e.gameTime&&a.uint32(64).int64(e.gameTime),""!==e.busName&&a.uint32(74).string(e.busName),""!==e.activityName&&a.uint32(82).string(e.activityName),0!==e.activityId&&a.uint32(88).int64(e.activityId),a},decode:function(e,t){for(var a=e instanceof i.Reader?e:i.Reader.create(e),r=void 0===t?a.len:a.pos+t,n={busId:"",status:0,userList:[],nextStepTime:0,gameStartTime:0,totalPeople:0,firstRound:!1,gameTime:0,busName:"",activityName:"",activityId:0};a.pos<r;){var o=a.uint32();switch(o>>>3){case 1:if(10!==o)break;n.busId=a.string();continue;case 2:if(16!==o)break;n.status=a.int32();continue;case 3:if(26!==o)break;n.userList.push(N.decode(a,a.uint32()));continue;case 4:if(32!==o)break;n.nextStepTime=p(a.int64());continue;case 5:if(40!==o)break;n.gameStartTime=p(a.int64());continue;case 6:if(48!==o)break;n.totalPeople=a.int32();continue;case 7:if(56!==o)break;n.firstRound=a.bool();continue;case 8:if(64!==o)break;n.gameTime=p(a.int64());continue;case 9:if(74!==o)break;n.busName=a.string();continue;case 10:if(82!==o)break;n.activityName=a.string();continue;case 11:if(88!==o)break;n.activityId=p(a.int64());continue}if(4==(7&o)||0===o)break;a.skipType(7&o)}return n},fromJSON:function(e){return{busId:I(e.busId)?String(e.busId):"",status:I(e.status)?o(e.status):0,userList:Array.isArray(null==e?void 0:e.userList)?e.userList.map((function(e){return N.fromJSON(e)})):[],nextStepTime:I(e.nextStepTime)?Number(e.nextStepTime):0,gameStartTime:I(e.gameStartTime)?Number(e.gameStartTime):0,totalPeople:I(e.totalPeople)?Number(e.totalPeople):0,firstRound:!!I(e.firstRound)&&Boolean(e.firstRound),gameTime:I(e.gameTime)?Number(e.gameTime):0,busName:I(e.busName)?String(e.busName):"",activityName:I(e.activityName)?String(e.activityName):"",activityId:I(e.activityId)?Number(e.activityId):0}},toJSON:function(e){var t={};return void 0!==e.busId&&(t.busId=e.busId),void 0!==e.status&&(t.status=u(e.status)),e.userList?t.userList=e.userList.map((function(e){return e?N.toJSON(e):void 0})):t.userList=[],void 0!==e.nextStepTime&&(t.nextStepTime=Math.round(e.nextStepTime)),void 0!==e.gameStartTime&&(t.gameStartTime=Math.round(e.gameStartTime)),void 0!==e.totalPeople&&(t.totalPeople=Math.round(e.totalPeople)),void 0!==e.firstRound&&(t.firstRound=e.firstRound),void 0!==e.gameTime&&(t.gameTime=Math.round(e.gameTime)),void 0!==e.busName&&(t.busName=e.busName),void 0!==e.activityName&&(t.activityName=e.activityName),void 0!==e.activityId&&(t.activityId=Math.round(e.activityId)),t},create:function(e){return f.fromPartial(null!=e?e:{})},fromPartial:function(e){var t,a,r,i,n,o,u,s,d,c,m,l={busId:"",status:0,userList:[],nextStepTime:0,gameStartTime:0,totalPeople:0,firstRound:!1,gameTime:0,busName:"",activityName:"",activityId:0};return l.busId=null!=(t=e.busId)?t:"",l.status=null!=(a=e.status)?a:0,l.userList=(null==(r=e.userList)?void 0:r.map((function(e){return N.fromPartial(e)})))||[],l.nextStepTime=null!=(i=e.nextStepTime)?i:0,l.gameStartTime=null!=(n=e.gameStartTime)?n:0,l.totalPeople=null!=(o=e.totalPeople)?o:0,l.firstRound=null!=(u=e.firstRound)&&u,l.gameTime=null!=(s=e.gameTime)?s:0,l.busName=null!=(d=e.busName)?d:"",l.activityName=null!=(c=e.activityName)?c:"",l.activityId=null!=(m=e.activityId)?m:0,l}});var v=e("RoomStatusPushPb",{encode:function(e,t){return void 0===t&&(t=i.Writer.create()),0!==e.status&&t.uint32(8).int32(e.status),0!==e.nextStepTime&&t.uint32(16).int64(e.nextStepTime),0!==e.nextGameStartTime&&t.uint32(24).int64(e.nextGameStartTime),0!==e.gameTime&&t.uint32(32).int64(e.gameTime),""!==e.activityName&&t.uint32(42).string(e.activityName),0!==e.activityId&&t.uint32(48).int64(e.activityId),""!==e.roundId&&t.uint32(58).string(e.roundId),t},decode:function(e,t){for(var a=e instanceof i.Reader?e:i.Reader.create(e),r=void 0===t?a.len:a.pos+t,n={status:0,nextStepTime:0,nextGameStartTime:0,gameTime:0,activityName:"",activityId:0,roundId:""};a.pos<r;){var o=a.uint32();switch(o>>>3){case 1:if(8!==o)break;n.status=a.int32();continue;case 2:if(16!==o)break;n.nextStepTime=p(a.int64());continue;case 3:if(24!==o)break;n.nextGameStartTime=p(a.int64());continue;case 4:if(32!==o)break;n.gameTime=p(a.int64());continue;case 5:if(42!==o)break;n.activityName=a.string();continue;case 6:if(48!==o)break;n.activityId=p(a.int64());continue;case 7:if(58!==o)break;n.roundId=a.string();continue}if(4==(7&o)||0===o)break;a.skipType(7&o)}return n},fromJSON:function(e){return{status:I(e.status)?o(e.status):0,nextStepTime:I(e.nextStepTime)?Number(e.nextStepTime):0,nextGameStartTime:I(e.nextGameStartTime)?Number(e.nextGameStartTime):0,gameTime:I(e.gameTime)?Number(e.gameTime):0,activityName:I(e.activityName)?String(e.activityName):"",activityId:I(e.activityId)?Number(e.activityId):0,roundId:I(e.roundId)?String(e.roundId):""}},toJSON:function(e){var t={};return void 0!==e.status&&(t.status=u(e.status)),void 0!==e.nextStepTime&&(t.nextStepTime=Math.round(e.nextStepTime)),void 0!==e.nextGameStartTime&&(t.nextGameStartTime=Math.round(e.nextGameStartTime)),void 0!==e.gameTime&&(t.gameTime=Math.round(e.gameTime)),void 0!==e.activityName&&(t.activityName=e.activityName),void 0!==e.activityId&&(t.activityId=Math.round(e.activityId)),void 0!==e.roundId&&(t.roundId=e.roundId),t},create:function(e){return v.fromPartial(null!=e?e:{})},fromPartial:function(e){var t,a,r,i,n,o,u,s={status:0,nextStepTime:0,nextGameStartTime:0,gameTime:0,activityName:"",activityId:0,roundId:""};return s.status=null!=(t=e.status)?t:0,s.nextStepTime=null!=(a=e.nextStepTime)?a:0,s.nextGameStartTime=null!=(r=e.nextGameStartTime)?r:0,s.gameTime=null!=(i=e.gameTime)?i:0,s.activityName=null!=(n=e.activityName)?n:"",s.activityId=null!=(o=e.activityId)?o:0,s.roundId=null!=(u=e.roundId)?u:"",s}});var N=e("RoomUserInfoPb",{encode:function(e,t){return void 0===t&&(t=i.Writer.create()),""!==e.userId&&t.uint32(10).string(e.userId),""!==e.nickName&&t.uint32(18).string(e.nickName),""!==e.avatarUrl&&t.uint32(26).string(e.avatarUrl),0!==e.score&&t.uint32(32).int64(e.score),0!==e.rank&&t.uint32(40).int32(e.rank),""!==e.awardUrl&&t.uint32(50).string(e.awardUrl),t},decode:function(e,t){for(var a=e instanceof i.Reader?e:i.Reader.create(e),r=void 0===t?a.len:a.pos+t,n={userId:"",nickName:"",avatarUrl:"",score:0,rank:0,awardUrl:""};a.pos<r;){var o=a.uint32();switch(o>>>3){case 1:if(10!==o)break;n.userId=a.string();continue;case 2:if(18!==o)break;n.nickName=a.string();continue;case 3:if(26!==o)break;n.avatarUrl=a.string();continue;case 4:if(32!==o)break;n.score=p(a.int64());continue;case 5:if(40!==o)break;n.rank=a.int32();continue;case 6:if(50!==o)break;n.awardUrl=a.string();continue}if(4==(7&o)||0===o)break;a.skipType(7&o)}return n},fromJSON:function(e){return{userId:I(e.userId)?String(e.userId):"",nickName:I(e.nickName)?String(e.nickName):"",avatarUrl:I(e.avatarUrl)?String(e.avatarUrl):"",score:I(e.score)?Number(e.score):0,rank:I(e.rank)?Number(e.rank):0,awardUrl:I(e.awardUrl)?String(e.awardUrl):""}},toJSON:function(e){var t={};return void 0!==e.userId&&(t.userId=e.userId),void 0!==e.nickName&&(t.nickName=e.nickName),void 0!==e.avatarUrl&&(t.avatarUrl=e.avatarUrl),void 0!==e.score&&(t.score=Math.round(e.score)),void 0!==e.rank&&(t.rank=Math.round(e.rank)),void 0!==e.awardUrl&&(t.awardUrl=e.awardUrl),t},create:function(e){return N.fromPartial(null!=e?e:{})},fromPartial:function(e){var t,a,r,i,n,o,u={userId:"",nickName:"",avatarUrl:"",score:0,rank:0,awardUrl:""};return u.userId=null!=(t=e.userId)?t:"",u.nickName=null!=(a=e.nickName)?a:"",u.avatarUrl=null!=(r=e.avatarUrl)?r:"",u.score=null!=(i=e.score)?i:0,u.rank=null!=(n=e.rank)?n:0,u.awardUrl=null!=(o=e.awardUrl)?o:"",u}});var b=e("RoomUserMainPb",{encode:function(e,t){return void 0===t&&(t=i.Writer.create()),0!==e.status&&t.uint32(8).int32(e.status),0!==e.nextStepTime&&t.uint32(16).int64(e.nextStepTime),0!==e.gameTime&&t.uint32(24).int64(e.gameTime),0!==e.nextGameStartTime&&t.uint32(32).int64(e.nextGameStartTime),""!==e.busName&&t.uint32(42).string(e.busName),0!==e.totalPeople&&t.uint32(48).int32(e.totalPeople),""!==e.activityName&&t.uint32(58).string(e.activityName),0!==e.activityId&&t.uint32(64).int64(e.activityId),t},decode:function(e,t){for(var a=e instanceof i.Reader?e:i.Reader.create(e),r=void 0===t?a.len:a.pos+t,n={status:0,nextStepTime:0,gameTime:0,nextGameStartTime:0,busName:"",totalPeople:0,activityName:"",activityId:0};a.pos<r;){var o=a.uint32();switch(o>>>3){case 1:if(8!==o)break;n.status=a.int32();continue;case 2:if(16!==o)break;n.nextStepTime=p(a.int64());continue;case 3:if(24!==o)break;n.gameTime=p(a.int64());continue;case 4:if(32!==o)break;n.nextGameStartTime=p(a.int64());continue;case 5:if(42!==o)break;n.busName=a.string();continue;case 6:if(48!==o)break;n.totalPeople=a.int32();continue;case 7:if(58!==o)break;n.activityName=a.string();continue;case 8:if(64!==o)break;n.activityId=p(a.int64());continue}if(4==(7&o)||0===o)break;a.skipType(7&o)}return n},fromJSON:function(e){return{status:I(e.status)?o(e.status):0,nextStepTime:I(e.nextStepTime)?Number(e.nextStepTime):0,gameTime:I(e.gameTime)?Number(e.gameTime):0,nextGameStartTime:I(e.nextGameStartTime)?Number(e.nextGameStartTime):0,busName:I(e.busName)?String(e.busName):"",totalPeople:I(e.totalPeople)?Number(e.totalPeople):0,activityName:I(e.activityName)?String(e.activityName):"",activityId:I(e.activityId)?Number(e.activityId):0}},toJSON:function(e){var t={};return void 0!==e.status&&(t.status=u(e.status)),void 0!==e.nextStepTime&&(t.nextStepTime=Math.round(e.nextStepTime)),void 0!==e.gameTime&&(t.gameTime=Math.round(e.gameTime)),void 0!==e.nextGameStartTime&&(t.nextGameStartTime=Math.round(e.nextGameStartTime)),void 0!==e.busName&&(t.busName=e.busName),void 0!==e.totalPeople&&(t.totalPeople=Math.round(e.totalPeople)),void 0!==e.activityName&&(t.activityName=e.activityName),void 0!==e.activityId&&(t.activityId=Math.round(e.activityId)),t},create:function(e){return b.fromPartial(null!=e?e:{})},fromPartial:function(e){var t,a,r,i,n,o,u,s,d={status:0,nextStepTime:0,gameTime:0,nextGameStartTime:0,busName:"",totalPeople:0,activityName:"",activityId:0};return d.status=null!=(t=e.status)?t:0,d.nextStepTime=null!=(a=e.nextStepTime)?a:0,d.gameTime=null!=(r=e.gameTime)?r:0,d.nextGameStartTime=null!=(i=e.nextGameStartTime)?i:0,d.busName=null!=(n=e.busName)?n:"",d.totalPeople=null!=(o=e.totalPeople)?o:0,d.activityName=null!=(u=e.activityName)?u:"",d.activityId=null!=(s=e.activityId)?s:0,d}});var S=e("RoomUserPushPb",{encode:function(e,t){return void 0===t&&(t=i.Writer.create()),void 0!==e.userInfo&&N.encode(e.userInfo,t.uint32(10).fork()).ldelim(),!0===e.add&&t.uint32(16).bool(e.add),0!==e.total&&t.uint32(24).int32(e.total),t},decode:function(e,t){for(var a=e instanceof i.Reader?e:i.Reader.create(e),r=void 0===t?a.len:a.pos+t,n={userInfo:void 0,add:!1,total:0};a.pos<r;){var o=a.uint32();switch(o>>>3){case 1:if(10!==o)break;n.userInfo=N.decode(a,a.uint32());continue;case 2:if(16!==o)break;n.add=a.bool();continue;case 3:if(24!==o)break;n.total=a.int32();continue}if(4==(7&o)||0===o)break;a.skipType(7&o)}return n},fromJSON:function(e){return{userInfo:I(e.userInfo)?N.fromJSON(e.userInfo):void 0,add:!!I(e.add)&&Boolean(e.add),total:I(e.total)?Number(e.total):0}},toJSON:function(e){var t={};return void 0!==e.userInfo&&(t.userInfo=e.userInfo?N.toJSON(e.userInfo):void 0),void 0!==e.add&&(t.add=e.add),void 0!==e.total&&(t.total=Math.round(e.total)),t},create:function(e){return S.fromPartial(null!=e?e:{})},fromPartial:function(e){var t,a,r={userInfo:void 0,add:!1,total:0};return r.userInfo=void 0!==e.userInfo&&null!==e.userInfo?N.fromPartial(e.userInfo):void 0,r.add=null!=(t=e.add)&&t,r.total=null!=(a=e.total)?a:0,r}}),T=function(){if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if("undefined"!=typeof global)return global;throw"Unable to locate global object"}();function p(e){if(e.gt(Number.MAX_SAFE_INTEGER))throw new T.Error("Value is larger than Number.MAX_SAFE_INTEGER");return e.toNumber()}function I(e){return null!=e}i.util.Long!==r&&(i.util.Long=r,i.configure()),a._RF.pop()}}}));

System.register("chunks:///_virtual/RotateUtil.ts",["cc","./Vec3Util.ts"],(function(t){var o,n,r,e,i;return{setters:[function(t){o=t.cclegacy,n=t.Quat,r=t.Vec3,e=t.toRadian},function(t){i=t.Vec3Util}],execute:function(){o._RF.push({},"af950NoZM9JIqoIenvBZZpi","RotateUtil",void 0);t("RotateUtil",function(){function t(){}return t.rotateAround=function(t,o,r){var e=new n;n.rotateAround(e,t.getRotation(),o.normalize(),r),t.setRotation(e)},t.rotateAroundTarget=function(t,o,e,i){var a=t.worldPosition,u=o.worldPosition,c=new n,s=new r;n.fromAxisAngle(c,e,i),r.subtract(s,u,a),r.transformQuat(s,s,c),r.add(s,a,s),o.setWorldPosition(s),n.rotateAround(c,o.worldRotation,e,i),n.normalize(c,c),o.setWorldRotation(c)},t.circularEdgePosition=function(t,o,a){var u=i.z.multiplyScalar(o),c=i.sub(u,t),s=new r,l=new n;return n.fromAxisAngle(l,r.UP,e(a)),r.transformQuat(s,c,l),r.add(s,t,s),s},t}());o._RF.pop()}}}));

System.register("chunks:///_virtual/RVOMath.ts",["cc"],(function(t){var i;return{setters:[function(t){i=t.cclegacy}],execute:function(){i._RF.push({},"411d7N1UQxHkYATyvQa7XjQ","RVOMath",void 0);t("Vector2",function(){function t(t,i){this.x=0,this.y=0,this.x=t,this.y=i}var i=t.prototype;return i.plus=function(i){return new t(this.x+i.x,this.y+i.y)},i.minus=function(i){return new t(this.x-i.x,this.y-i.y)},i.multiply=function(t){return this.x*t.x+this.y*t.y},i.scale=function(i){return new t(this.x*i,this.y*i)},i.copy=function(t){return this.x=t.x,this.y=t.y,this},i.clone=function(){return new t(this.x,this.y)},i.substract=function(t,i){return t.x-=i.x,t.y-=i.y,t},i.lengthSqr=function(){return Math.pow(this.x,2)+Math.pow(this.y,2)},t}()),t("Obstacle",(function(){this.next=void 0,this.previous=void 0,this.direction=void 0,this.point=void 0,this.id=void 0,this.convex=void 0})),t("Line",(function(){this.point=void 0,this.direction=void 0})),t("KeyValuePair",(function(t,i){this.key=void 0,this.value=void 0,this.key=t,this.value=i}));t("RVOMath",function(){function t(){}return t.absSq=function(t){return t.multiply(t)},t.normalize=function(i){return i.scale(1/t.abs(i))},t.distSqPointLineSegment=function(i,n,s){var u=s.minus(i),e=n.minus(i),r=u.multiply(e)/t.absSq(e);return r<0?t.absSq(u):r>1?t.absSq(s.minus(n)):t.absSq(s.minus(i.plus(e.scale(r))))},t.sqr=function(t){return t*t},t.det=function(t,i){return t.x*i.y-t.y*i.x},t.abs=function(i){return Math.sqrt(t.absSq(i))},t.leftOf=function(i,n,s){return t.det(i.minus(s),n.minus(i))},t}()).RVO_EPSILON=.003,i._RF.pop()}}}));

System.register("chunks:///_virtual/ScreenShot.ts",["./rollupPluginModLoBabelHelpers.js","cc"],(function(t){var e,a,i,s,n,r,o,h,c,l,u,g,f,v,d,p,m,_,w,b,C,x;return{setters:[function(t){e=t.applyDecoratedDescriptor,a=t.inheritsLoose,i=t.initializerDefineProperty,s=t.assertThisInitialized},function(t){n=t.cclegacy,r=t._decorator,o=t.Camera,h=t.Node,c=t.RenderTexture,l=t.view,u=t.UITransform,g=t.ImageAsset,f=t.Texture2D,v=t.SpriteFrame,d=t.sys,p=t.assetManager,m=t.instantiate,_=t.Vec3,w=t.Sprite,b=t.log,C=t.error,x=t.Component}],execute:function(){var y,I,S,T,D,N,P;n._RF.push({},"7d50dIdDwFCoYzul0ASRXh5","ScreenShot",void 0);var A=r.ccclass,R=r.property;t("Screenshot2D",(y=A("Screenshot2D"),I=R(o),S=R(h),y((N=e((D=function(t){function e(){for(var e,a=arguments.length,n=new Array(a),r=0;r<a;r++)n[r]=arguments[r];return e=t.call.apply(t,[this].concat(n))||this,i(e,"copyCamera",N,s(e)),i(e,"targetNode",P,s(e)),e._rt=null,e._canvas=null,e._buffer=null,e}a(e,t);var n=e.prototype;return n.start=function(){var t=this;this._rt=new c,this._rt.reset({width:l.getVisibleSize().width,height:l.getVisibleSize().height}),this.copyCamera.targetTexture=this._rt,this.scheduleOnce((function(){t.capture()}),2)},n.bitCheck=function(t,e){return t&1<<e-1},n.bitAdd=function(t,e){return t|1<<e-1},n.bitDel=function(t,e){return t&~(1<<e-1)},n.capture=function(){this.copyRenderTex()},n.copyRenderTex=function(){var t=this.targetNode.getComponent(u).width,e=this.targetNode.getComponent(u).height,a=this.targetNode.getWorldPosition();this._buffer=this._rt.readPixels(Math.round(a.x),Math.round(a.y),t,e),this.showImage(t,e)},n.showImage=function(t,e){var a=this,i=new g;i.reset({_data:this._buffer,width:t,height:e,format:f.PixelFormat.RGBA8888,_compressed:!1});var s=new f;s.image=i;var n=new v;n.texture=s,n.packable=!1,setTimeout((function(){a.savaAsImage(t,e,a._buffer)}),2)},n.savaAsImage=function(t,e,a){var i=this;if(d.isBrowser){this._canvas?this.clearCanvas():(this._canvas=document.createElement("canvas"),this._canvas.width=t,this._canvas.height=e);for(var s=this._canvas.getContext("2d"),n=4*t,r=0;r<e;r++){for(var o=e-1-r,h=s.createImageData(t,1),c=o*t*4,l=0;l<n;l++)h.data[l]=a[c+l];s.putImageData(h,0,r)}this.canvas2image.saveAsPNG(this._canvas,t,e)}else if(d.isNative){var u=jsb.fileUtils.getWritablePath()+"render_to_sprite_image.png";jsb.saveImageData&&jsb.saveImageData(this._buffer,t,e,u).then((function(){p.loadRemote(u,(function(t,e){if(t)console.log("show image error");else{var a=m(i.targetNode);a.setPosition(new _(-a.position.x,a.position.y,a.position.z)),i.targetNode.parent.addChild(a);var s=new v,n=new f;n.image=e,s.texture=n,a.getComponent(w).spriteFrame=s,s.packable=!1,s.flipUVY=!0,!d.isNative||d.os!==d.OS.IOS&&d.os!==d.OS.OSX||(s.flipUVY=!1)}})),b("save image data success, file: "+u)})).catch((function(){C("save image data failed!")}))}else if(d.platform===d.Platform.WECHAT_GAME){this._canvas?this.clearCanvas():(this._canvas=wx.createCanvas(),this._canvas.width=t,this._canvas.height=e);for(var g=this._canvas.getContext("2d"),x=4*t,y=0;y<e;y++){for(var I=e-1-y,S=g.createImageData(t,1),T=I*t*4,D=0;D<x;D++)S.data[D]=a[T+D];g.putImageData(S,0,y)}this._canvas.toTempFilePath({x:0,y:0,width:this._canvas.width,height:this._canvas.height,destWidth:this._canvas.width,destHeight:this._canvas.height,fileType:"png",success:function(t){wx.showToast({title:"截图成功"}),wx.saveImageToPhotosAlbum({filePath:t.tempFilePath,success:function(t){wx.showToast({title:"成功保存到设备相册"})},fail:function(){}})},fail:function(){wx.showToast({title:"截图失败"})}})}},n.clearCanvas=function(){this._canvas.getContext("2d").clearRect(0,0,this._canvas.width,this._canvas.height)},n.onSaveImageBtnClicked=function(){var t=this.targetNode.getComponent(u).width,e=this.targetNode.getComponent(u).height;this.savaAsImage(t,e,this._buffer)},e}(x)).prototype,"copyCamera",[I],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),P=e(D.prototype,"targetNode",[S],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),T=D))||T));n._RF.pop()}}}));

System.register("chunks:///_virtual/ScrollViewProCom.ts",["./rollupPluginModLoBabelHelpers.js","cc"],(function(t){var e,i,n,o,s,h,a,l,c,p,r,g,u,d,m,y,f,C;return{setters:[function(t){e=t.applyDecoratedDescriptor,i=t.inheritsLoose,n=t.initializerDefineProperty,o=t.assertThisInitialized,s=t.createClass},function(t){h=t.cclegacy,a=t._decorator,l=t.Enum,c=t.Node,p=t.Prefab,r=t.ScrollView,g=t.warn,u=t.Layout,d=t.isValid,m=t.instantiate,y=t.UITransform,f=t.v3,C=t.tween}],execute:function(){var w,v,D,T,b,L,S,I,x,P,N;h._RF.push({},"d2bd8Ybyq5JP6Lhg2nKR4ii","ScrollViewProCom",void 0);var X=a.ccclass,R=a.property,A=a.menu,Y=(a.integer,t("default",(w=A("用户脚本组件/滚动视图优化组件"),v=R({displayName:"分帧时间"}),D=R({type:l({"节点":0,"预制体":1}),displayName:"item类型"}),T=R({type:c,visible:function(){return 0==this.type},displayName:"item节点"}),b=R({type:p,visible:function(){return 1==this.type},displayName:"item预制体"}),X(L=w((I=e((S=function(t){function e(){for(var e,i=arguments.length,s=new Array(i),h=0;h<i;h++)s[h]=arguments[h];return e=t.call.apply(t,[this].concat(s))||this,n(e,"intervalT",I,o(e)),n(e,"type",x,o(e)),n(e,"itemNode",P,o(e)),n(e,"itemPrefab",N,o(e)),e.intervalTimer=null,e.layout=null,e._item=null,e.nodePool=null,e.curShowDatas=[],e.allDatas=[],e.func=null,e}i(e,t);var h=e.prototype;return h.onLoad=function(){if(null!=this.node.getComponent(r))if(this.content=this.node.getComponent(r).content,null!=this.content)if(this.layout=this.content.getComponent(u),null!=this.layout){this.layout.enabled=!1,this.node.on(r.EventType.SCROLLING,this.onScrolling,this),this.node.on(c.EventType.SIZE_CHANGED,this.onSizeChanged,this),this.nodePool=[];for(var t=this.content.children.length-1;t>=0;t--)this.nodePool.push(this.content.children[t]),this.content.children[t].setPosition(Number.MAX_VALUE,Number.MAX_VALUE);this.updateContentSize(),this.updateListView()}else g("content lackof layout component");else g("scrollview lack of content");else g("lack of scrollview component")},h.onDestroy=function(){this.node.off(r.EventType.SCROLLING,this.onScrolling,this),this.node.off(c.EventType.SIZE_CHANGED,this.onSizeChanged,this),clearInterval(this.intervalTimer)},h.setView=function(t,e){this.updateAllDatas(t),this.func=e,this.updateContentSize(),this.updateListView(!0)},h.onSizeChanged=function(){this.updateListView()},h.onScrolling=function(){this.updateListView()},h.updateListView=function(t){var e=this;void 0===t&&(t=!1);for(var i=this.getDatasNeedShow(),n=this.curShowDatas.length-1;n>=0;n--){for(var o=0;o<i.length&&i[o][1]!=this.curShowDatas[n].userData;o++);o>=i.length&&(this.nodePool.push(this.curShowDatas[n].n),this.curShowDatas[n].n.setPosition(Number.MAX_VALUE,Number.MAX_VALUE),this.curShowDatas.splice(n,1))}for(var s=[],h=0,a=0;a<i.length;a++){for(o=0;o<this.curShowDatas.length&&this.curShowDatas[o].userData!=i[a][1];o++);if(o<this.curShowDatas.length){var l=this.curShowDatas[o];if(l.index!=i[a][0]){l.index=i[a][0];var c=this.getItemPosByIdx(l.index);l.n.setPosition(c)}1==t&&1==d(this.func)&&this.func(l.n,l.userData,l.index)}else{var p=new Y(i[a][0],i[a][1]);p.n=this.nodePool.pop(),null==p.n?s.push([a,p]):(p.n.setPosition(this.getItemPosByIdx(p.index)),this.curShowDatas.splice(a,0,p),1==d(this.func)&&this.func(p.n,p.userData,p.index))}}clearInterval(this.intervalTimer),this.intervalTimer=setInterval((function(){if(h>=s.length)clearInterval(e.intervalTimer);else{var t=s[h][1],i=s[h][0];t.n=m(e.item),e.content.addChild(t.n),t.n.setPosition(e.getItemPosByIdx(t.index)),e.curShowDatas.splice(i,0,t),1==d(e.func)&&e.func(t.n,t.userData,t.index),h++}}),this.intervalT)},h.updateAllDatas=function(t){this.allDatas=[];for(var e=0;e<t.length;e++)this.allDatas.push([e,t[e]])},h.updateContentSize=function(){if(null!=this.content&&null!=this.layout)switch(this.layout.type){case u.Type.VERTICAL:this.content.getComponent(y).height=this.layout.paddingTop+this.layout.paddingBottom+this.allDatas.length*(this.layout.spacingY+this.item.getComponent(y).height*this.item.scale.y)-this.layout.spacingY;break;case u.Type.HORIZONTAL:this.content.getComponent(y).width=this.layout.paddingLeft+this.layout.paddingRight+this.allDatas.length*(this.layout.spacingX+this.item.getComponent(y).width*this.item.scale.x)-this.layout.spacingX;break;case u.Type.GRID:var t=Math.floor((this.content.getComponent(y).width-this.layout.paddingLeft-this.layout.paddingRight+this.layout.spacingX)/(this.item.getComponent(y).width*this.item.scale.x+this.layout.spacingX)),e=Math.ceil(this.allDatas.length/t);this.content.getComponent(y).height=this.layout.paddingTop+this.layout.paddingBottom+e*(this.layout.spacingY+this.item.getComponent(y).height*this.item.scale.y)-this.layout.spacingY}},h.getDatasNeedShow=function(){if(null==this.content||null==this.layout)return[];switch(this.layout.type){case u.Type.VERTICAL:0>(i=this.content.position.y+(1-this.content.getComponent(y).anchorY)*this.content.getComponent(y).height-(1-this.node.getComponent(y).anchorY)*this.node.getComponent(y).height)&&(i=0),(n=this.node.getComponent(y).height+this.content.position.y)<i&&(n=i),this.content.getComponent(y).height-this.layout.paddingBottom<n&&(n=this.content.getComponent(y).height-this.layout.paddingBottom);var t=Math.floor((i-this.layout.paddingTop)/(this.item.getComponent(y).height+this.layout.spacingY));this.allDatas.length<=t&&(t=this.allDatas.length-1),0>t&&(t=0);var e=Math.ceil((n-this.layout.paddingTop)/(this.item.getComponent(y).height+this.layout.spacingY));this.allDatas.length<=e&&(e=this.allDatas.length-1),e<t&&(e=t);break;case u.Type.HORIZONTAL:0>(i=-(this.content.position.x-this.content.getComponent(y).anchorX*this.content.getComponent(y).width)+this.node.getComponent(y).anchorX*this.node.getComponent(y).width)&&(i=0),(n=this.node.getComponent(y).width-this.content.position.x)<i&&(n=i),this.content.getComponent(y).width-this.layout.paddingRight<n&&(n=this.content.getComponent(y).width-this.layout.paddingRight);t=Math.floor((i-this.layout.paddingLeft)/(this.item.getComponent(y).width+this.layout.spacingX));this.allDatas.length<=t&&(t=this.allDatas.length-1),0>t&&(t=0);e=Math.ceil((n-this.layout.paddingLeft)/(this.item.getComponent(y).width+this.layout.spacingX));this.allDatas.length<=e&&(e=this.allDatas.length-1),e<t&&(e=t);break;case u.Type.GRID:var i,n;0>(i=this.content.position.y+(1-this.content.getComponent(y).anchorY)*this.content.getComponent(y).height-(1-this.node.getComponent(y).anchorY)*this.node.getComponent(y).height)&&(i=0),(n=this.node.getComponent(y).height+this.content.position.y)<i&&(n=i),this.content.getComponent(y).height-this.layout.paddingBottom<n&&(n=this.content.getComponent(y).height-this.layout.paddingBottom);var o=Math.floor((this.content.getComponent(y).width-this.layout.paddingLeft-this.layout.paddingRight+this.layout.spacingX)/(this.item.getComponent(y).width+this.layout.spacingX));t=Math.floor((i-this.layout.paddingTop)/(this.item.getComponent(y).height+this.layout.spacingY))*o;this.allDatas.length<=t&&(t=this.allDatas.length-1),0>t&&(t=0);e=Math.ceil((n-this.layout.paddingTop)/(this.item.getComponent(y).height+this.layout.spacingY))*o-1;this.allDatas.length<=e&&(e=this.allDatas.length-1),e<t&&(e=t)}for(var s=[],h=t;h<Math.min(this.allDatas.length,e+1);h++)s.push(this.allDatas[h]);return s},h.getItemPosByIdx=function(t){if(null==this.content||null==this.layout)return f();switch(this.layout.type){case u.Type.VERTICAL:var e=this.layout.paddingTop+(1-this.content.getComponent(y).anchorY)*this.content.getComponent(y).height+(1-this.item.getComponent(y).anchorY)*this.item.getComponent(y).height*this.item.scale.y+t*(this.item.getComponent(y).height*this.item.scale.y+this.layout.spacingY);return f(0,-e,0);case u.Type.HORIZONTAL:var i=this.layout.paddingLeft-this.content.getComponent(y).anchorX*this.content.getComponent(y).width+this.item.getComponent(y).anchorX*this.item.getComponent(y).width*this.item.scale.x+t*(this.item.getComponent(y).width*this.item.scale.x+this.layout.spacingX);return f(i,0,0);case u.Type.GRID:var n=Math.floor((this.content.getComponent(y).width-this.layout.paddingLeft-this.layout.paddingRight+this.layout.spacingX)/(this.item.getComponent(y).width*this.item.scale.x+this.layout.spacingX));e=this.layout.paddingTop+(1-this.content.getComponent(y).anchorY)*this.content.getComponent(y).height+(1-this.item.getComponent(y).anchorY)*this.item.getComponent(y).height*this.item.scale.y+Math.floor(t/n)*(this.item.getComponent(y).height*this.item.scale.y+this.layout.spacingY),i=this.layout.paddingLeft+this.item.getComponent(y).anchorX*this.item.getComponent(y).width*this.item.scale.x-this.content.getComponent(y).anchorX*this.content.getComponent(y).width+t%n*(this.item.getComponent(y).width*this.item.scale.x+this.layout.spacingX);return f(i,-e,0);default:return f()}},h.scrollToTargetIndex=function(t){var e=this,i=this.getItemPosByIdx(t),n=-this.item.getComponent(y).height/2,o=Math.abs(this.content.position.y+i.y)/3e3;C(this.content).to(o,{position:f(0,-i.y+n)},{onUpdate:function(t,i){e.onScrolling()}}).start()},s(e,[{key:"item",get:function(){return null==this._item&&(0==this.type&&null!=this.itemNode?this._item=m(this.itemNode):1==this.type&&null!=this.itemPrefab&&(this._item=m(this.itemPrefab))),this._item}}]),e}(r)).prototype,"intervalT",[v],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 0}}),x=e(S.prototype,"type",[D],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 0}}),P=e(S.prototype,"itemNode",[T],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),N=e(S.prototype,"itemPrefab",[b],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),L=S))||L)||L)),function(t,e){this.n=void 0,this.userData=void 0,this.index=void 0,this.index=t,this.userData=e});h._RF.pop()}}}));

System.register("chunks:///_virtual/SimpleDelegate.ts",["cc"],(function(e){var n;return{setters:[function(e){n=e.cclegacy}],execute:function(){n._RF.push({},"ec036YRKklJjay7nOvZDKMx","SimpleDelegate",void 0);e("SimpleDelegate",function(){function e(){this._fnQuene=void 0,this._waitingQuene=void 0,this._fnQuene=[],this._waitingQuene=[]}var n=e.prototype;return n.do=function(){for(var e=this._waitingQuene.length,n=0;n<e;n++)this._waitingQuene[n]();this._waitingQuene.length=0,e=this._fnQuene.length;for(var t=0;t<e;t++)this._fnQuene[t]();this._fnQuene.length=0},n.add=function(e){var n=this;this._waitingQuene.push((function(){n._fnQuene.push(e)}))},n.remove=function(e){var n=this;this._waitingQuene.push((function(){n._fnQuene.splice(n._fnQuene.indexOf(e),1)}))},e}());n._RF.pop()}}}));

System.register("chunks:///_virtual/Simulator.ts",["./rollupPluginModLoBabelHelpers.js","cc","./Agent.ts","./RVOMath.ts","./kdtree.ts"],(function(t){var e,n,i,s,a,g;return{setters:[function(t){e=t.createClass},function(t){n=t.cclegacy},function(t){i=t.Agent},function(t){s=t.RVOMath,a=t.Obstacle},function(t){g=t.KdTree}],execute:function(){n._RF.push({},"58866wd9J1BSY7jSuX6ywGx","Simulator",void 0),t("Simulator",function(){function t(){this.agentId=0,this.agentIdLst=[],this.aid2agent=Object.create(null),this.obstacles=[],this.kdTree=new g,this.defaultAgent=void 0,this.time=0}var n=t.prototype;return n.getAgent=function(t){return this.aid2agent[this.agentIdLst[t]]},n.getAgentByAid=function(t){return this.aid2agent[t]},n.getGlobalTime=function(){return this.time},n.getNumAgents=function(){return this.agentIdLst.length},n.getAgentAidByIdx=function(t){return this.agentIdLst[t]},n.setAgentPrefVelocity=function(t,e){var n;null==(n=this.aid2agent[t])||n.prefVelocity_.copy(e)},n.getAgentPosition=function(t){return this.aid2agent[t]?this.aid2agent[t].position_:null},n.getAgentPrefVelocity=function(t){var e;return null==(e=this.aid2agent[t])?void 0:e.prefVelocity_},n.getAgentVelocity=function(t){return this.aid2agent[t].velocity_},n.getAgentRadius=function(t){return this.aid2agent[t].radius_},n.getAgentOrcaLines=function(t){return this.aid2agent[t].orcaLines_},n.addAgent=function(t,e,n,s,a){if(void 0===e&&(e=null),void 0===n&&(n=null),void 0===s&&(s=null),void 0===a&&(a=null),!this.defaultAgent)throw new Error("no default agent");var g=new i;return g.position_.copy(t),g.maxNeighbors_=this.defaultAgent.maxNeighbors_,g.maxSpeed_=n||this.defaultAgent.maxSpeed_,g.neighborDist=this.defaultAgent.neighborDist,g.radius_=e||this.defaultAgent.radius_,g.timeHorizon=this.defaultAgent.timeHorizon,g.timeHorizonObst=this.defaultAgent.timeHorizonObst,g.velocity_.copy(s||this.defaultAgent.velocity_),g.id=this.agentId++,a&&a>=0&&(g.mass=a),this.aid2agent[g.id]=g,this.agentIdLst.push(g.id),g.id},n.removeAgent=function(t){if(this.hasAgent(t)){var e=this.agentIdLst.indexOf(t);e>=0&&(this.agentIdLst[e]=this.agentIdLst[this.agentIdLst.length-1],this.agentIdLst.length--),delete this.aid2agent[t]}},n.hasAgent=function(t){return!!this.aid2agent[t]},n.setAgentMass=function(t,e){this.aid2agent[t].mass=e},n.getAgentMass=function(t){return this.aid2agent[t].mass},n.setAgentRadius=function(t,e){this.aid2agent[t].radius_=e},n.setAgentDefaults=function(t,e,n,s,a,g,u){this.defaultAgent||(this.defaultAgent=new i),this.defaultAgent.maxNeighbors_=e,this.defaultAgent.maxSpeed_=g,this.defaultAgent.neighborDist=t,this.defaultAgent.radius_=a,this.defaultAgent.timeHorizon=n,this.defaultAgent.timeHorizonObst=s,this.defaultAgent.velocity_=u},n.run=function(t){this.kdTree.buildAgentTree(this.getNumAgents());for(var e=this.agentIdLst.length,n=0;n<e;n++)this.aid2agent[this.agentIdLst[n]].computeNeighbors(this),this.aid2agent[this.agentIdLst[n]].computeNewVelocity(t);for(var i=0;i<e;i++)this.aid2agent[this.agentIdLst[i]].update(t);this.time+=t},n.addObstacle=function(t){if(t.length<2)return-1;for(var e=this.obstacles.length,n=0;n<t.length;++n){var i=new a;i.point=t[n],0!=n&&(i.previous=this.obstacles[this.obstacles.length-1],i.previous.next=i),n==t.length-1&&(i.next=this.obstacles[e],i.next.previous=i),i.direction=s.normalize(t[n==t.length-1?0:n+1].minus(t[n])),2==t.length?i.convex=!0:i.convex=s.leftOf(t[0==n?t.length-1:n-1],t[n],t[n==t.length-1?0:n+1])>=0,i.id=this.obstacles.length,this.obstacles.push(i)}return e},n.processObstacles=function(){this.kdTree.buildObstacleTree()},n.queryVisibility=function(t,e,n){return this.kdTree.queryVisibility(t,e,n)},n.getObstacles=function(){return this.obstacles},n.clear=function(){this.agentIdLst.length=0,this.agentId=0,this.aid2agent=Object.create(null),this.defaultAgent=null,this.kdTree=new g,this.obstacles.length=0},e(t,null,[{key:"instance",get:function(){return t._inst||(t._inst=new t),t._inst}}]),t}())._inst=void 0,n._RF.pop()}}}));

System.register("chunks:///_virtual/Singleton.ts",["cc"],(function(n){var t;return{setters:[function(n){t=n.cclegacy}],execute:function(){t._RF.push({},"9ce6d2EKLpGPIIaltnwaiND","Singleton",void 0);var e=n("Singleton",function(){function n(){if(!this.constructor._isGetByInstance)throw new Error("Singleton class can't be instantiated more than once.")}return n.getInstance=function(){var n=this;return n._instance||(n._isGetByInstance=!0,n._instance=new n,n._isGetByInstance=!1),n._instance},n}());e._instance=void 0,e._isGetByInstance=!1,t._RF.pop()}}}));

System.register("chunks:///_virtual/Sound.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ResManager.ts"],(function(o){var u,i,e,t,n,s,c,a;return{setters:[function(o){u=o.inheritsLoose,i=o.createClass},function(o){e=o.cclegacy,t=o.AudioSource,n=o.AudioClip,s=o.assetManager,c=o.Component},function(o){a=o.ResManager}],execute:function(){e._RF.push({},"bd552QMdBFPEKVqu/2R7SN5","Sound",void 0);o("Sound",function(o){function e(){for(var u,i=arguments.length,e=new Array(i),t=0;t<i;t++)e[t]=arguments[t];return(u=o.call.apply(o,[this].concat(e))||this)._soundAble=!0,u._musicAble=!0,u._soundNum=1,u._musicNum=1,u._audioSource=void 0,u}u(e,o);var c=e.prototype;return c.onLoad=function(){this._audioSource=this.node.addComponent(t)},c.initSoundState=function(){},c.playOneShot=function(o,u,i){var e=this;(void 0===u&&(u=1),void 0===i&&(i="resources"),this._soundAble)&&(o instanceof n?this._audioSource.playOneShot(o,u):s.getBundle(i).load(o,(function(o,i){o?console.log(o):e._audioSource.playOneShot(i,u)})))},c.play=function(o,u,i,e){var t=this;(void 0===u&&(u=1),void 0===i&&(i="resources"),void 0===e&&(e=!0),this._musicAble)&&(o instanceof n?(this._audioSource.clip=o,this._audioSource.play(),this.audioSource.volume=u,this._audioSource.loop=e):s.getBundle(i).load(o,(function(o,i){o?console.log(o):(t._audioSource.clip=i,t._audioSource.play(),t.audioSource.volume=u,t._audioSource.loop=e)})))},c.remotePlay=function(o,u,i){var e=this;void 0===u&&(u=1),void 0===i&&(i=!0),this._musicAble&&a.getInstance().remoteLoadAny(o,(function(o,t){o?console.log(o):(e.stop(),e._audioSource.clip=t,e._audioSource.play(),e.audioSource.volume=u,e._audioSource.loop=i)}))},c.remotePlayOneShot=function(o){var u=this;this._musicAble&&a.getInstance().remoteLoadAny(o,(function(o,i){o?console.log(o):u._audioSource.playOneShot(i,u._soundNum)}))},c.switchSound=function(o){this._soundAble=o},c.switchMusic=function(o){this._musicAble=o,this._musicAble?this.resume():this.pause()},c.setSoundNum=function(o){void 0===o&&(o=1),this._soundNum=o},c.setMusicNum=function(o){void 0===o&&(o=1),this._musicNum=o,this._audioSource&&(this.audioSource.volume=this._musicNum)},c.stop=function(){this._audioSource&&this._audioSource.stop()},c.pause=function(){this._audioSource&&this._audioSource.pause()},c.resume=function(){this._audioSource&&this._audioSource.clip&&this._audioSource.play()},i(e,[{key:"audioSource",get:function(){return this._audioSource}}]),e}(c));e._RF.pop()}}}));

System.register("chunks:///_virtual/StateMachine.ts",["./rollupPluginModLoBabelHelpers.js","cc"],(function(t){var a,n,e;return{setters:[function(t){a=t.createClass},function(t){n=t.cclegacy,e=t.warn}],execute:function(){n._RF.push({},"cd2d1XlBo1J2qrragpjZr2i","StateMachine",void 0);var s=t("FSMState",(function(t){this.name=null,this.translationMap=null,this.name=t,this.translationMap=new Map})),i=t("FSMTranslation",(function(t,a,n,e){this.name=null,this.fromState=null,this.toState=null,this.cb=null,this.name=t,this.fromState=a,this.toState=n,e&&(this.cb=e)}));t("FSM",function(){function t(t){this._switchCb=null,this._currState=null,this.stateMap=new Map;for(var a=0;a<t.statenames.length;a++){var n=new s(t.statenames[a]);this.addState(n),0==a&&(this._currState=n)}for(var e=0;e<t.transitions.length;e++){var r=t.transitions[e],h=new i(r.name,this.stateMap.get(r.from),this.stateMap.get(r.to),r.cb);this.addTranslation(h)}this._switchCb=t.switchCb}var n=t.prototype;return n.addState=function(t){this.stateMap.set(t.name,t)},n.addTranslation=function(t){this.stateMap.get(t.fromState.name).translationMap.set(t.name,t)},n.handleEvent=function(t){if(null!=this._currState&&this._currState.translationMap.has(t)){var a=this._currState.translationMap.get(t),n=a.fromState;return this._currState=a.toState,a.cb&&a.cb(),this._switchCb&&this._switchCb(n,this._currState),!0}return e(t+" not in "+this._currState.name+" 's translationMap： "),!1},a(t,[{key:"currState",get:function(){return this._currState}}]),t}());n._RF.pop()}}}));

System.register("chunks:///_virtual/StorageUtil.ts",["./rollupPluginModLoBabelHelpers.js","cc","./EventManager2.ts"],(function(t){var e,a,n,i;return{setters:[function(t){e=t.extends},function(t){a=t.cclegacy,n=t.sys},function(t){i=t.EventManager}],execute:function(){a._RF.push({},"be121jRXGNLVaGNqGOhrG3l","StorageUtil",void 0);var r=t("StorageUtil",function(){function t(){}return t.register=function(){var t=this;i.getInstance().addEventListener("EVENT_HIDE",(function(e){t.intervalId&&(clearTimeout(t.intervalId),t.intervalId=null)})),i.getInstance().addEventListener("EVENT_SHOW",(function(t){}))},t.getAllLocalData=function(t,a){var i=this.gameName+"_firstLogin";for(var r in t[i]=!1,t)"object"==typeof t[r]?this.pureDataCache[r]=e({},t[r]):this.pureDataCache[r]=t[r],this.gameDataRef[r]=t[r];var s=this.getLocalItem(i,!0);if(s)for(var o in this.pureDataCache){var c=this.pureDataCache[o];if(c&&"object"==typeof c)for(var l in c)this.setLocalItemImmediately(o+l,c[l]);else this.setLocalItemImmediately(o,c)}else for(var u in this.pureDataCache){var h=this.pureDataCache[u];if(h&&"object"==typeof h)for(var f in h){var y,g=this.getLocalItem(u+f,h[f]);if(this.pureDataCache[u][f]=g,t[u][f]=g,h[f]==g)if(!n.localStorage.getItem(u+f))this.setLocalItemDefer(u,((y={})[f]=g,y));console.log("childKey:",f,h[f],g)}else{var p=this.getLocalItem(u,h);this.pureDataCache[u]=p,t[u]=p}}a&&a(s)},t.setLocalItemDefer=function(t,a){var n=e({},a);this.pushChangedKey(t,n),this.pureDataCache[t]=n,console.log("cloneValue",n)},t.setLocalItemImmediately=function(t,e){this._setData(t,e)},t.getLocalItem=function(t,e){var a=this._getData(t,e);return"boolean"==typeof e?a=this._toBoolean(a,e):"number"==typeof e?a=this._toNumber(a,e):"object"==typeof e&&(a=this._toJSON(a,e)),a},t.getGameDataItem=function(t){return this.gameDataRef[t]},t.pushChangedKey=function(t,e){if("object"==typeof e)for(var a in e){var n=e[a];console.log("pushChangedKey",n),this.pureDataCache[t]?(console.log("pushChangedKey2",JSON.stringify(n),JSON.stringify(this.pureDataCache[t][a])),JSON.stringify(this.pureDataCache[t][a])!==JSON.stringify(n)&&(this.keyMap[t+a]={key:t,subKey:a},this._syncLocalDataInterval())):(this.keyMap[t+a]={key:t,subKey:a},this._syncLocalDataInterval())}else console.log("pushChangedKeys",JSON.stringify(e),JSON.stringify(this.pureDataCache[t])),JSON.stringify(this.pureDataCache[t])!==JSON.stringify(e)&&(this.keyMap[t]={key:t,subKey:null},this._syncLocalDataInterval())},t._syncLocalDataInterval=function(){var t=this;console.log("_syncLocalDataInterval"),this.intervalId||(this.intervalId=setTimeout((function(){t.intervalId=null,t._syncLocalData()}),this.syncLocalDataInterval))},t._syncLocalData=function(){for(var t in this.keyMap){var e=this.keyMap[t],a=e.key,n=e.subKey;n?this._setData(t,this.pureDataCache[a][n]):this._setData(t,this.pureDataCache[a])}this.keyMap={}},t._setData=function(t,e){"object"==typeof e&&(e=JSON.stringify(e)),n.localStorage.setItem(t,e)},t._getData=function(t,e){var a=n.localStorage.getItem(t);return null!=a&&"null"!=a||null==e||(a=e),a},t._toBoolean=function(t,e){return"boolean"==typeof t?t:null==t||""==t?e:"false"!=t&&("true"==t||void 0)},t._toNumber=function(t,e){var a=Number(t);return isNaN(a)?e:a},t._toJSON=function(t,e){try{var a=JSON.parse(t);return"object"==typeof a&&a?a:e}catch(t){return e}},t}());r.gameName="caishen",r.pureDataCache={},r.gameDataRef={},r.keyMap={},r.intervalId=null,r.syncLocalDataInterval=500,a._RF.pop()}}}));

System.register("chunks:///_virtual/StringUtil.ts",["cc"],(function(t){var r;return{setters:[function(t){r=t.cclegacy}],execute:function(){r._RF.push({},"680eew/wPtKb4j7qqpeJK0j","StringUtil",void 0);t("StringUtil",function(){function t(){}return t.guid=function(){for(var t="",r=1;r<=32;r++){t+=Math.floor(16*Math.random()).toString(16),8!=r&&12!=r&&16!=r&&20!=r||(t+="-")}return t},t.numberTotPermil=function(t){return t.toLocaleString()},t.numberToThousand=function(t,r){void 0===r&&(r=2);var n=1e3;if(t<n)return t.toString();var e=Math.floor(Math.log(t)/Math.log(n));return(t/Math.pow(n,e)).toFixed(r)+["","K","M","G"][e]},t.numberToTenThousand=function(t,r){void 0===r&&(r=2);var n=1e4;if(t<n)return t.toString();var e=Math.floor(Math.log(t)/Math.log(n));return(t/Math.pow(n,e)).toFixed(r)+["","万","亿","万亿"][e]},t.format=function(t,r){var n={"M+":t.getMonth()+1,"d+":t.getDate(),"h+":t.getHours(),"m+":t.getMinutes(),"s+":t.getSeconds(),"q+":Math.floor((t.getMonth()+3)/3),S:t.getMilliseconds()};for(var e in/(y+)/.test(r)&&(r=r.replace(RegExp.$1,(t.getFullYear()+"").substr(4-RegExp.$1.length))),n)new RegExp("("+e+")").test(r)&&(r=r.replace(RegExp.$1,1==RegExp.$1.length?n[e]:("00"+n[e]).substr((""+n[e]).length)));return r},t.stringToArray1=function(t){return""==t?[]:t.split(",")},t.stringToArray2=function(t){return""==t?[]:t.split("|")},t.stringToArray3=function(t){return""==t?[]:t.split(":")},t.stringToArray4=function(t){return""==t?[]:t.split(";")},t.sub=function(t,r,n){if(void 0===n&&(n=!1),!t)return"";var e=/[^\x00-\xff]/g;if(t.replace(e,"mm").length<=r)return t;for(var o=Math.floor(r/2);o<t.length;o++)if(t.substring(0,o).replace(e,"mm").length>=r)return n?t.substring(0,o)+"...":t.substring(0,o);return t},t.stringLen=function(t){for(var r=0,n=t.length,e=-1,o=0;o<n;o++)r+=(e=t.charCodeAt(o))>=0&&e<=128?1:2;return r},t.IsEmpty=function(t){return null==t||null==t||0==t.length},t.substitute=function(t){if(null==t)return"";for(var r=arguments.length,n=new Array(r>1?r-1:0),e=1;e<r;e++)n[e-1]=arguments[e];var o,u=n.length;1==u&&n[0]instanceof Array?u=(o=n[0]).length:o=n;for(var i=0;i<u;i++)t=t.replace(new RegExp("\\{"+i+"\\}","g"),o[i]);return t},t.prototype.mMtoDayHourMin=function(t){var r=parseInt(""+t/1440),n=parseInt(""+t/60%24);return(r>0?r+"天":"")+(n>0?n+"时":"")+parseInt(""+t%60)+"分"},t}());r._RF.pop()}}}));

System.register("chunks:///_virtual/Timer.ts",["./rollupPluginModLoBabelHelpers.js","cc"],(function(e){var t,s;return{setters:[function(e){t=e.createClass},function(e){s=e.cclegacy}],execute:function(){s._RF.push({},"914bcyd6SxEV6fa6lRIsEZb","Timer",void 0);e("Timer",function(){function e(e){void 0===e&&(e=0),this.callback=null,this._elapsedTime=0,this._step=-1,this.step=e}var s=e.prototype;return s.update=function(e){return!(this.step<=0)&&(this._elapsedTime+=e,this._elapsedTime>=this._step&&(this._elapsedTime-=this._step,null==(t=this.callback)||t.call(this),!0));var t},s.reset=function(){this._elapsedTime=0},s.stop=function(){this._elapsedTime=0,this.step=-1},t(e,[{key:"elapsedTime",get:function(){return this._elapsedTime}},{key:"step",get:function(){return this._step},set:function(e){this._step=e,this._elapsedTime=0}},{key:"progress",get:function(){return this._elapsedTime/this._step}}]),e}());s._RF.pop()}}}));

System.register("chunks:///_virtual/TimerManager.ts",["./rollupPluginModLoBabelHelpers.js","cc","./Timer.ts","./StringUtil.ts"],(function(e){var t,i,n,r,o,s;return{setters:[function(e){t=e.inheritsLoose},function(e){i=e.cclegacy,n=e.game,r=e.Component},function(e){o=e.Timer},function(e){s=e.StringUtil}],execute:function(){i._RF.push({},"f9e5bDPuz1L9KWajLP0eePJ","TimerManager",void 0);e("TimerManager",function(e){function i(){for(var t,i=arguments.length,n=new Array(i),r=0;r<i;r++)n[r]=arguments[r];return(t=e.call.apply(e,[this].concat(n))||this).times={},t.serverTime=0,t}t(i,e);var r=i.prototype;return r.update=function(e){for(var t in this.times){var i=this.times[t];i.timer.update(e)&&i.object[i.field]>0&&(i.object[i.field]--,0==i.object[i.field]?this.onTimerComplete(i):i.onSecond&&i.onSecond.call(i.object))}},r.onTimerComplete=function(e){e.onComplete&&e.onComplete.call(e.object),e.event&&this.node.dispatchEvent(e.event),delete this.times[e.id]},r.register=function(e,t,i,n){var r=new o;r.step=1;var m={};return m.id=s.guid(),m.timer=r,m.object=e,m.field=t,m.onSecond=i,m.onComplete=n,this.times[m.id]=m,m.id},r.unRegister=function(e){this.times[e]&&delete this.times[e]},r.setServerTime=function(e){this.serverTime=e},r.getServerTime=function(){return this.serverTime+this.getTime()},r.getLocalTime=function(){return Date.now()},r.getTime=function(){return n.totalTime},r.save=function(){for(var e in this.times)this.times[e].startTime=this.getTime()},r.load=function(){for(var e in this.times){var t=Math.floor((this.getTime()-(this.times[e].startTime||this.getTime()))/1e3),i=this.times[e];i.object[i.field]=i.object[i.field]-t,i.object[i.field]<0&&(i.object[i.field]=0,this.onTimerComplete(i)),this.times[e].startTime=null}},i}(r));i._RF.pop()}}}));

System.register("chunks:///_virtual/TimeUtil.ts",["./rollupPluginModLoBabelHelpers.js","cc"],(function(e){var t,n,r;return{setters:[function(e){t=e.asyncToGenerator,n=e.regeneratorRuntime},function(e){r=e.cclegacy}],execute:function(){r._RF.push({},"47db4lt13lOG7RoVf6UDVFv","TimeUtil",void 0);var a=e("TimeStatusEnum",function(e){return e[e.Before=0]="Before",e[e.Between=1]="Between",e[e.After=2]="After",e}({}));e("default",function(){function e(){}return e.daysBetween=function(e,t){null!=t&&null!=t||(t=+new Date);var n=new Date(e).toLocaleDateString(),r=new Date(t).toLocaleDateString(),a=new Date(n).getTime(),o=new Date(r).getTime();return Math.abs(a-o)/864e5},e.secsBetween=function(e,t){return null!=t&&null!=t||(t=+new Date),Math.abs(t-e)/1e3},e.sleep=function(){var e=t(n().mark((function e(t){return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise((function(e){setTimeout((function(){e()}),t)})));case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),e.initServerTime=function(t){e._timeOffet=Date.now()-t},e.getServerTime=function(){return Date.now()-e._timeOffet},e.convertTodayTime=function(e){return e%864e5-6e4*(new Date).getTimezoneOffset()},e.isInPeroid=function(e,t,n){return e>=t&&e<=n},e.getTimestamp=function(e){return Date.parse(new Date(e))},e.formatTimestamp=function(e,t){var n=new Date(e);return n.getFullYear()+"年"+(n.getMonth()+1)+"月"+n.getDate()+"日"},e.formatTimestampToDate=function(e,t,n,r){void 0===n&&(n=!1),void 0===r&&(r=!1);var a=new Date(e),o=a.getMonth()+1<10?"0"+(a.getMonth()+1):a.getMonth()+1,i=a.getDate()<10?"0"+a.getDate():a.getDate(),u=a.getHours()<10?"0"+a.getHours():a.getHours(),g=a.getMinutes()<10?"0"+a.getMinutes():a.getMinutes(),f=a.getSeconds()<10?"0"+a.getSeconds():a.getSeconds(),s=n?"":""+a.getFullYear()+t;return r?""+s+o+t+i+" "+u+":"+g+":"+f:""+s+o+t+i},e.formatTimeCN=function(e){if(e<0)return"";var t=Math.ceil(e/1e3),n=Math.floor(t/86400),r=Math.floor(t%86400/3600),a=Math.floor(t%3600/60),o=t%60;function i(e,t,n){return void 0===n&&(n=!1),""+(n?e>9?e:"0"+e:e)+t}var u="";return(n>0||u.length>0)&&(u+=i(n,"天")),(r>0||u.length>0)&&(u+=i(r,"小时")),(a>0||u.length>0)&&(u+=i(a,"分")),(o>0||u.length>0)&&(u+=i(o,"秒")),u},e.formatTime=function(e,t,n){if(void 0===t&&(t=3),void 0===n&&(n=":"),e<0)return"";var r=Math.floor(e/1e3),a=Math.floor(r/3600),o=Math.floor(r%3600/60),i=r%60;return 3===t?""+(a>9?a:"0"+a)+n+(o>9?o:"0"+o)+n+(i>9?i:"0"+i):2===t?""+(o>9?o:"0"+o)+n+(i>9?i:"0"+i):1===t?""+(i>9?i:"0"+i):void 0},e.getLastDayByIndex=function(t,n){if(void 0===n&&(n=1),!t||n>7||n<1)return null;var r=new Date(t);if(6==r.getDay())return e.formatTimestampToDate(r.getTime(),"-");for(var a=0;a<7;a++){r.setDate(r.getDate()-1);var o=r.getFullYear(),i=r.getMonth()+1,u=r.getDate(),g=o+"-"+(i>=10?i:"0"+i)+"-"+(u>=10?u:"0"+u);if(e.getDayOfWeek(g)==""+n)return g}return null},e.getFirstDayOfTheWeek=function(e){var t=new Date(e),n=t.getDay(),r=0===n?-6:1-n,a=new Date(t);return a.setDate(t.getDate()+r),a.getFullYear()+"-"+(a.getMonth()+1)+"-"+a.getDate()},e.getFirstDayOfTheMonth=function(){var t=new Date(e.getServerTime()),n=new Date(t.getFullYear(),t.getMonth(),1),r=n.getFullYear(),a=n.getMonth()+1,o=n.getDate();return r+"-"+(a>=10?a:"0"+a)+"-"+(o>=10?o:"0"+o)},e.getTimeUntilNextSaturday=function(){var t=new Date(e.getServerTime()),n=new Date(t);n.setHours(0,0,0,0);var r=(6-n.getDay()+7)%7;return n.setDate(n.getDate()+r),n<=t&&n.setDate(n.getDate()+7),n.getTime()-t.getTime()},e.getMInfo=function(t){void 0===t&&(t=7);var n=(new Date).getDay();n=6-n;var r=[],a={},o=new Date;o.setDate(o.getDate()-1),a.e=o.getFullYear()+"-"+(o.getMonth()+1)+"-"+o.getDate();var i=new Date;i.setDate(i.getDate()-n),a.day=e.getDayOfWeek(a.e),r.push(a);for(var u=new Date,g=0;g<t-1;g++){var f={};0!=g?n=1:n+=1,u.setDate(u.getDate()-n),f.e=u.getFullYear()+"-"+(u.getMonth()+1)+"-"+u.getDate(),f.day=e.getDayOfWeek(f.e),r.push(f)}return r},e.getDayOfWeek=function(e){return["7","1","2","3","4","5","6"][new Date(e).getDay()]},e.fmtSecondsToHMS=function(e){e=e||0;var t=Math.floor(e/3600),n=Math.floor(e%3600/60),r=e%60;return{h:t.toString().padStart(2,"0"),m:n.toString().padStart(2,"0"),s:r.toString().padStart(2,"0")}},e.fmtSecondsToMS=function(e){var t=(e=e||0)%60;return{m:Math.floor(e/60).toString().padStart(2,"0"),s:t.toString().padStart(2,"0")}},e.diff=function(t,n,r){void 0===r&&(r="seconds");var a=e.date(n).getTime()-e.date(t).getTime();switch(r){case"milliseconds":return a;case"seconds":return Math.round(a/1e3);case"minutes":return Math.round(a/6e4);case"hours":return Math.round(a/36e5);case"days":return Math.round(a/864e5);default:throw new Error("Invalid time unit.")}},e.date=function(e){return e instanceof Date?e:new Date(e)},e.checkTimeStatus=function(t,n){var r=+new Date,o=e.diff(r,t);return o>0?a.Before:(o=e.diff(r,n))<0?a.After:a.Between},e.dateFormat=function(t,n){void 0===n&&(n="YYYY-MM-DD hh:mm:ss");var r,a=e.date(t),o={"Y+":a.getFullYear().toString(),"M+":(a.getMonth()+1).toString(),"D+":a.getDate().toString(),"h+":a.getHours().toString(),"m+":a.getMinutes().toString(),"s+":a.getSeconds().toString()};for(var i in o)(r=new RegExp("("+i+")").exec(n))&&(n=n.replace(r[1],1==r[1].length?o[i]:o[i].padStart(r[1].length,"0")));return n},e}())._timeOffet=0,r._RF.pop()}}}));

System.register("chunks:///_virtual/Tool.ts",["./rollupPluginModLoBabelHelpers.js","cc","./xcore.ts","./ConstGlobal.ts","./Collection.ts","./ElectronAPI.ts"],(function(e){var n,t,r,o,i,a,u,c,s,f,l,p,d,m,h,g,v,y,w,b,x;return{setters:[function(e){n=e.regeneratorRuntime,t=e.asyncToGenerator,r=e.createForOfIteratorHelperLoose},function(e){o=e.cclegacy,i=e.v2,a=e.v3,u=e.tween,c=e.log,s=e.warn,f=e.Size,l=e.UITransform,p=e.instantiate,d=e.Component,m=e.AnimationClip},function(e){h=e.xcore},function(e){g=e.C_Runtime,v=e.E_JumpType,y=e.E_TaskType,w=e.C_Bundle},function(e){b=e.Collection},function(e){x=e.default}],execute:function(){o._RF.push({},"23cf4lHfqtOBqjciZFl/5wt","Tool",void 0);var M=e("default",function(){function e(){}return e.simpleV2=function(e,n){return n?(n.set(e,e),n):i(e,e)},e.simpleV3=function(e,n){return n?(n.set(e,e,e),n):a(e,e,e)},e.v2t3=function(e,n){return n?(n.x=e.x,n.y=e.y,n.z=1,n):a(e.x,e.y,1)},e.v3t2=function(e,n){return n?(n.x=e.x,n.y=e.y,n):i(e.x,e.y)},e.randomNumber=function(e,n){var t=Math.min(e,n),r=Math.max(e,n),o=Math.ceil(t),i=Math.floor(r);if(o>i)return null;var a=i-o+1;return o+Math.floor(Math.random()*a)},e.findArrNullIdx=function(e){if(0==e.length)return 0;for(var n=0;n<e.length;n++)if(!e[n])return n;return-1},e.splitNumber=function(e,n){return void 0===n&&(n=","),e?"number"==typeof e?[e]:e.split(n).map((function(e,n){return Number(e)})):[]},e.weightRandomIdx=function(e){if(e.length<=1)return 0;for(var n=0,t=0;t<e.length;t++)n+=e[t];for(var r=Math.random()*n,o=0,i=0,a=0;a<e.length;a++){if(i=o+e[a],o<r&&r<=i)return a;o=i}return 0},e.numMoveZoro=function(e){return e.indexOf(".")<0||"."==(e=e.replace(/0+?$/g,""))[e.length-1]&&(e=e.replace(/[.$]/,"")),e},e.secondFormat=function(e){var n="";e-=3600*Math.floor(e/3600);var t=Math.floor(e/60);return e-=60*t,n+=t<10?"0"+t:t,n+=":",n+=(e=Math.floor(e))<10?"0"+e:e},e.getDateStr=function(e,n,t){void 0===n&&(n=1),void 0===t&&(t=!0);var r=new Date(e),o=r.getFullYear(),i=r.getMonth()+1,a=r.getDate(),u=r.getHours(),c=r.getMinutes(),s=""+c;c<10&&(s="0"+c);return(1==n?o+"/"+i+"/"+a:o+"年"+i+"月"+a+"日")+(t?u+":"+s:"")},e.generatorCallBack=function(e,t){for(var r=n().mark(u),o=arguments.length,i=new Array(o>2?o-2:0),a=2;a<o;a++)i[a-2]=arguments[a];function u(){var o;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:o=0;case 1:if(!(o<e)){n.next=7;break}return n.next=4,t.apply(void 0,[o].concat(i));case 4:o++,n.next=1;break;case 7:case"end":return n.stop()}}),r)}return this.exeGenerator(u(),10)},e.exeGenerator=function(e,n){return new Promise((function(t,r){var o=e;!function e(){for(var r=(new Date).getTime(),i=o.next();;i=o.next()){if(null==i||i.done)return void t(null);if((new Date).getTime()-r>n)return void setTimeout((function(){e()}),n)}}()}))},e.copyObj=function(n,t){if("object"==typeof t)for(var r in t)"object"==typeof t[r]?null!=n[r]&&e.copyObj(n[r],t[r]):"function"!=typeof t[r]&&null!=n[r]&&(t[r]=n[r]);else console.log("can not copy the value type")},e.cloneObj=function(n){var t;if(null==n||n.constructor!==Object&&n.constructor!==Array)t=n;else for(var r in t=new n.constructor,n)t[r]=e.cloneObj(n[r]);return t},e.getAngleByV3=function(e,n){return Math.atan2(n.y-e.y,n.x-e.x)*(180/Math.PI)},e.bezierTo=function(e,n,t,r,o,i){i=i||Object.create(null);return i.onUpdate=function(n,u){var c,s,f,l,p,d;e.position=(f=r,l=o,p=(1-(c=u))*(1-c)*(s=t).x+2*c*(1-c)*f.x+c*c*l.x,d=(1-c)*(1-c)*s.y+2*c*(1-c)*f.y+c*c*l.y,a(p,d,0)),e.angle+=10,1==u&&i.cb&&i.cb()},u(e).to(n,{},i)},e.getComponentPropertys=function(e,n){var t=[];n&&!Array.isArray(n)&&(n=[n]);var r=function(){if(Object.prototype.hasOwnProperty.call(e,o)){var r=e[o];r instanceof d&&"node"!=o&&(n?n.find((function(e){return r instanceof e}))&&t.push(r):t.push(r))}};for(var o in e)r();return t},e.loadSDK=function(e){return new Promise((function(n){var t=document.createElement("script");t.type="text/javascript",t.src=e,t.onload=n,document.body.appendChild(t)}))},e.copyContentH5=function(e){var n=document.createElement("input");document.body.appendChild(n),n.setAttribute("value",e),n.setAttribute("readonly","readonly"),n.select(),n.setSelectionRange(0,999);var t=document.execCommand("copy");return document.body.removeChild(n),t},e.listenThirdJump=function(e,r,o){var i=this;r&&r.off("click");var a=e.taskType;this.formatJumpConfig(e,a);var u=h.runtime==g.other&&e.type==v.h5,c=h.runtime==g.program_wx&&e.type==v.h5,s=h.runtime==g.browser_wx&&e.type==v.h5,f=h.runtime==g.program_wx&&e.type==v.MiniProgram;u||c||s?r.on("click",t(n().mark((function t(){return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:location.href=e.address,o&&o(),console.log("jumpConfig",e.address);case 3:case"end":return n.stop()}}),t)})))):f?r.on("click",t(n().mark((function t(){return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:i.miniProgramOpt("navigate",e.address),console.log("jumpConfig",e.address),o&&o();case 3:case"end":return n.stop()}}),t)}))),this):r.on("click",t(n().mark((function t(){return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:console.warn("平台限制：",decodeURIComponent(JSON.stringify(e.address))),o&&o();case 2:case"end":return n.stop()}}),t)}))))},e.formatJumpConfig=function(n,t){if(t||(t=n.taskType),!n.type&&n.address&&t==y.jumpInside)n.type=n.address.startsWith("http")||n.address.startsWith("www")?v.h5:v.MiniProgram;else{if(!n.address||t!=y.invite&&t!=y.share&&t!=y.jumpOutside)return s("jumpConfig err",n),null;var r;n.type=v.MiniProgram;var o=null==(r=h.config["midpageData.json"])?void 0:r.find((function(e){return e.jsonId==n.address}));if(o){var i={apiParams:n.apiParams||{},apiSign:n.apiSign||{}};for(var a in o)Object.prototype.hasOwnProperty.call(o,a)&&(i[a]=o[a]);if(t==y.invite||t==y.share){var u={};for(var f in n)Object.prototype.hasOwnProperty.call(n,f)&&(u[f]=n[f]);var l=""+location.origin+location.pathname+(e.getSortedQuery(u)?"?"+e.getSortedQuery(u):"");i.path=l,c("卡片入口路径",l)}var p=encodeURIComponent(JSON.stringify(i));return n.address="?config="+p,n}}return n},e.miniProgramOpt=function(n,t){h.runtime==g.program_wx?"navigate"===n?wx.miniProgram.navigateTo({url:t,success:function(r){console.log(r),"invokeMiniProgramAPI:ok"===r.errMsg&&(n="switch",e.miniProgramOpt(n,t))}}):"switch"===n&&wx.miniProgram.switchTab({url:t}):console.log("请在小程序内进行尝试")},e.getRandomPosInCircle=function(e,n){var t=2*Math.random()*Math.PI,r=n*Math.sqrt(Math.random()),o=r*Math.cos(t),i=r*Math.sin(t);return{x:e.x+o,y:e.y+i}},e.parseQuery=function(e){var n={},t=e.match(/[?&]([^=&#]+)=([^&#]*)/g);if(t)for(var r in t){var o=t[r].split("="),i=o[0].substring(1),a=decodeURIComponent(o[1]);n[i]?n[i]=[].concat(n[i],a):n[i]=a}return n},e.asyncDelay=function(e){return new Promise((function(n,t){setTimeout((function(){n("")}),e)}))},e.showConsole=function(){return new Promise((function(n,t){e.loadSDK("https://gz-cdn-1258783731.cos.ap-guangzhou.myqcloud.com/cocos_sdk/vconsole.min.js").then((function(){new VConsole,n("")}))}))},e.resizeSprite=function(e,n){if(e&&n){var t=e.spriteFrame.rect;if(n instanceof f){var r=n,o=Math.min(r.width/t.width,r.height/t.height);e.node.getComponent(l).width=t.width*o,e.node.getComponent(l).height=t.height*o}else if("number"==typeof n){var i=n;o=Math.min(i/t.width,i/t.height);e.node.getComponent(l).width=t.width*o,e.node.getComponent(l).height=t.height*o}}},e.asyncModifyChildren=function(e,n,t,r,o){var i=n.children.filter((function(e){return e.name===t.name})).length,a=i-r;if(a>0)for(var u=0;u<a;u++){var c=n.children.find((function(e){return e.name===t.name}));c&&n.removeChild(c)}var s=n.children.filter((function(e){return e.name===t.name}));s.forEach(o);var f=r-i;if(f>0){var l=0;e.schedule((function(){var e=p(t);e.name=t.name,n.addChild(e),o(e,s.length+l),l++}),0,f-1)}},e.guid=function(){for(var e="",n=1;n<=32;n++){e+=Math.floor(16*Math.random()).toString(16),8!=n&&12!=n&&16!=n&&20!=n||(e+="-")}return e},e.numberTotPermil=function(e){return e.toLocaleString()},e.numberToThousand=function(e,n){void 0===n&&(n=2);var t=1e3;if(e<t)return e.toString();var r=Math.floor(Math.log(e)/Math.log(t));return(e/Math.pow(t,r)).toFixed(n)+["","K","M","G"][r]},e.numberToTenThousand=function(e,n){void 0===n&&(n=2);var t=1e4;if(e<t)return e.toString();var r=Math.floor(Math.log(e)/Math.log(t));return(e/Math.pow(t,r)).toFixed(n)+["","万","亿","万亿"][r]},e.format=function(e,n){var t={"M+":e.getMonth()+1,"d+":e.getDate(),"h+":e.getHours(),"m+":e.getMinutes(),"s+":e.getSeconds(),"q+":Math.floor((e.getMonth()+3)/3),S:e.getMilliseconds()};for(var r in/(y+)/.test(n)&&(n=n.replace(RegExp.$1,(e.getFullYear()+"").substr(4-RegExp.$1.length))),t)new RegExp("("+r+")").test(n)&&(n=n.replace(RegExp.$1,1==RegExp.$1.length?t[r]:("00"+t[r]).substr((""+t[r]).length)));return n},e.stringToArray1=function(e){return""==e?[]:e.split(",")},e.stringToArray2=function(e){return""==e?[]:e.split("|")},e.stringToArray3=function(e){return""==e?[]:e.split(":")},e.stringToArray4=function(e){return""==e?[]:e.split(";")},e.randomSortArray=function(e){return e.sort((function(){return Math.random()-.5})),e},e.selectArrayIdByWeight=function(e){if(e.length<=0)return null;var n=e.reduce((function(e,n){return e+n.weight}),0);if(n<=0)throw new Error("Total weight must be a positive number");var t=Math.random()*n;t<=0&&(t=.1);for(var o,i=0,a=r(e);!(o=a()).done;){var u=o.value;if(t<(i+=u.weight))return u.id}return null},e.sub=function(e,n,t){if(void 0===t&&(t=!1),!e)return"";var r=/[^\x00-\xff]/g;if(e.replace(r,"mm").length<=n)return e;for(var o=Math.floor(n/2);o<e.length;o++)if(e.substr(0,o).replace(r,"mm").length>=n)return t?e.substr(0,o)+"...":e.substr(0,o);return e},e.stringLen=function(e){for(var n=0,t=e.length,r=-1,o=0;o<t;o++)n+=(r=e.charCodeAt(o))>=0&&r<=128?1:2;return n},e.IsEmpty=function(e){return null==e||null==e||0==e.length},e.substitute=function(e){if(null==e)return"";for(var n=arguments.length,t=new Array(n>1?n-1:0),r=1;r<n;r++)t[r-1]=arguments[r];var o,i=t.length;1==i&&t[0]instanceof Array?i=(o=t[0]).length:o=t;for(var a=0;a<i;a++)e=e.replace(new RegExp("\\{"+a+"\\}","g"),o[a]);return e},e.setChildrenNodeSortByPriority=function(e,n){if(e.parent){var t=e.parent.children.concat();e.priority=n,t.sort((function(e,n){return null==e.priority&&(e.priority=0),null==n.priority&&(n.priority=0),e.priority-n.priority}));for(var o,i=t.length,a=r(t);!(o=a()).done;){o.value.setSiblingIndex(i)}}},e.generateNoncestr=function(e){void 0===e&&(e=32);for(var n="",t=["0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"],r=0;r<e;r++){n+=t[Math.round(Math.random()*(t.length-1))]}return n},e.createAnim=function(){var e=t(n().mark((function e(t,r,o,i){var a,u,c,s,f,l,p;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(void 0===i&&(i=!0),t){e.next=3;break}return e.abrupt("return");case 3:if(h.gameData.animationClips||(h.gameData.animationClips=new b),(null==(a=t.clips[0])?void 0:a.name)!=r.name){e.next=8;break}return t.enabled=!0,t.play(r.name),e.abrupt("return");case 8:if(u=h.gameData.animationClips.get(r.name)){e.next=30;break}c=[],s=0;case 12:if(!(s<r.sample)){e.next=27;break}if(f=r.name+"_"+(s<10?"0"+s:s),l="res/anim/"+r.path+"/"+f,!o){e.next=19;break}e.t0=o.getSpriteFrame(f),e.next=22;break;case 19:return e.next=21,h.res.bundleLoadSprite(w.abGame,l);case 21:e.t0=e.sent;case 22:p=e.t0,c.push(p);case 24:s++,e.next=12;break;case 27:u=m.createWithSpriteFrames(c,r.sample),h.gameData.animationClips.set(r.name,u),u.addRef();case 30:t.enabled=!0,t.stop(),u.duration=r.duration,u.speed=r.speed,u.wrapMode=r.wrapMode,u.name=r.name,t.addClip(u,r.name),i&&t.play(r.name);case 38:case"end":return e.stop()}}),e)})));return function(n,t,r,o){return e.apply(this,arguments)}}(),e.getCommandLineArgs=function(){for(var e=x.getQuery(),n={},t=0;t<e.length;t++){var r=e[t];if(r.startsWith("-")){var o=r.indexOf("=");if(o>-1){var i=r.substring(1,o),a=r.substring(o+1,r.length);n[""+i]=a,console.log("命令行参数："+r+"  key:",i+"  val:",a)}}}return n},e.setWindow=function(){x.window()},e.closeDevTools=function(){x.closeDevTools()},e.setScreenResolution=function(e,n){x.setScreenResolution(e,n)},e.setSize=function(e,n){x.setSize(e,n)},e.openDevTool=function(){x.openDevTools()},e.setResolution=function(e,n){x.setResolution(e,n)},e.log=function(e,n){x.log(e,n)},e.listenDataChange=function(e,n){n.forEach((function(n){Object.defineProperty(e,n.key,{get:function(){return e._store=e._store||{},e._store[n.key]},set:function(t){if(e._store=e._store||{},n.modifyFunc){var r=n.modifyFunc(t,e._store[n.key]);r&&(t=r)}e._store[n.key]=t,n.boardcast&&h.event.raiseEvent(n.boardcast,t)}})}))},e}());M.throttle=function(e,n){void 0===n&&(n=1e3);var t=0;return function(){var r=this,o=arguments,i=Date.now();i-t>=n&&(e.apply(r,o),t=Date.now())}},M.debounce=function(e,n,t){var r,o;void 0===n&&(n=1e3),void 0===t&&(t=!1);return function(){var i=this,a=arguments;if(r&&clearTimeout(r),t){var u=!r;r=setTimeout((function(){r=null}),n),u&&(o=e.apply(i,a))}else r=setTimeout((function(){e.apply(i,a)}),n);return o}},M.getSortedQuery=function(e){var n=Object.keys(e).sort(),t="";return n.forEach((function(n){if(e[n]||0===e[n]){var r="";r=e[n]instanceof Object?n+"="+JSON.stringify(e[n]):n+"="+e[n],t&&r&&(t+="&"),t+=r}})),t},o._RF.pop()}}}));

System.register("chunks:///_virtual/user.js",["./cjs-loader.mjs","./minimal.js"],(function(e,t){var n,r;return{setters:[function(e){n=e.default},function(e){r=e.__cjsMetaURL}],execute:function(){e("default",void 0);var i=e("__cjsMetaURL",t.meta.url);n.define(i,(function(t,n,r,i,o){var s,a=n("protobufjs/minimal"),u=a.Reader,c=a.Writer,l=a.util,d=a.roots.default||(a.roots.default={});d.pb=((s={}).BusinessInfoResPb=function(){function e(e){if(e)for(var t=Object.keys(e),n=0;n<t.length;++n)null!=e[t[n]]&&(this[t[n]]=e[t[n]])}return e.prototype.id="",e.prototype.busName="",e.prototype.activityId="",e.prototype.startTime=l.Long?l.Long.fromBits(0,0,!1):0,e.prototype.endTime=l.Long?l.Long.fromBits(0,0,!1):0,e.create=function(t){return new e(t)},e.encode=function(e,t){return t||(t=c.create()),null!=e.id&&Object.hasOwnProperty.call(e,"id")&&t.uint32(10).string(e.id),null!=e.busName&&Object.hasOwnProperty.call(e,"busName")&&t.uint32(18).string(e.busName),null!=e.activityId&&Object.hasOwnProperty.call(e,"activityId")&&t.uint32(26).string(e.activityId),null!=e.startTime&&Object.hasOwnProperty.call(e,"startTime")&&t.uint32(32).int64(e.startTime),null!=e.endTime&&Object.hasOwnProperty.call(e,"endTime")&&t.uint32(40).int64(e.endTime),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof u||(e=u.create(e));for(var n=void 0===t?e.len:e.pos+t,r=new d.pb.BusinessInfoResPb;e.pos<n;){var i=e.uint32();switch(i>>>3){case 1:r.id=e.string();break;case 2:r.busName=e.string();break;case 3:r.activityId=e.string();break;case 4:r.startTime=e.int64();break;case 5:r.endTime=e.int64();break;default:e.skipType(7&i)}}return r},e.decodeDelimited=function(e){return e instanceof u||(e=new u(e)),this.decode(e,e.uint32())},e.verify=function(e){return"object"!=typeof e||null===e?"object expected":null!=e.id&&e.hasOwnProperty("id")&&!l.isString(e.id)?"id: string expected":null!=e.busName&&e.hasOwnProperty("busName")&&!l.isString(e.busName)?"busName: string expected":null!=e.activityId&&e.hasOwnProperty("activityId")&&!l.isString(e.activityId)?"activityId: string expected":null!=e.startTime&&e.hasOwnProperty("startTime")&&!(l.isInteger(e.startTime)||e.startTime&&l.isInteger(e.startTime.low)&&l.isInteger(e.startTime.high))?"startTime: integer|Long expected":null!=e.endTime&&e.hasOwnProperty("endTime")&&!(l.isInteger(e.endTime)||e.endTime&&l.isInteger(e.endTime.low)&&l.isInteger(e.endTime.high))?"endTime: integer|Long expected":null},e.fromObject=function(e){if(e instanceof d.pb.BusinessInfoResPb)return e;var t=new d.pb.BusinessInfoResPb;return null!=e.id&&(t.id=String(e.id)),null!=e.busName&&(t.busName=String(e.busName)),null!=e.activityId&&(t.activityId=String(e.activityId)),null!=e.startTime&&(l.Long?(t.startTime=l.Long.fromValue(e.startTime)).unsigned=!1:"string"==typeof e.startTime?t.startTime=parseInt(e.startTime,10):"number"==typeof e.startTime?t.startTime=e.startTime:"object"==typeof e.startTime&&(t.startTime=new l.LongBits(e.startTime.low>>>0,e.startTime.high>>>0).toNumber())),null!=e.endTime&&(l.Long?(t.endTime=l.Long.fromValue(e.endTime)).unsigned=!1:"string"==typeof e.endTime?t.endTime=parseInt(e.endTime,10):"number"==typeof e.endTime?t.endTime=e.endTime:"object"==typeof e.endTime&&(t.endTime=new l.LongBits(e.endTime.low>>>0,e.endTime.high>>>0).toNumber())),t},e.toObject=function(e,t){t||(t={});var n={};if(t.defaults){if(n.id="",n.busName="",n.activityId="",l.Long){var r=new l.Long(0,0,!1);n.startTime=t.longs===String?r.toString():t.longs===Number?r.toNumber():r}else n.startTime=t.longs===String?"0":0;l.Long?(r=new l.Long(0,0,!1),n.endTime=t.longs===String?r.toString():t.longs===Number?r.toNumber():r):n.endTime=t.longs===String?"0":0}return null!=e.id&&e.hasOwnProperty("id")&&(n.id=e.id),null!=e.busName&&e.hasOwnProperty("busName")&&(n.busName=e.busName),null!=e.activityId&&e.hasOwnProperty("activityId")&&(n.activityId=e.activityId),null!=e.startTime&&e.hasOwnProperty("startTime")&&("number"==typeof e.startTime?n.startTime=t.longs===String?String(e.startTime):e.startTime:n.startTime=t.longs===String?l.Long.prototype.toString.call(e.startTime):t.longs===Number?new l.LongBits(e.startTime.low>>>0,e.startTime.high>>>0).toNumber():e.startTime),null!=e.endTime&&e.hasOwnProperty("endTime")&&("number"==typeof e.endTime?n.endTime=t.longs===String?String(e.endTime):e.endTime:n.endTime=t.longs===String?l.Long.prototype.toString.call(e.endTime):t.longs===Number?new l.LongBits(e.endTime.low>>>0,e.endTime.high>>>0).toNumber():e.endTime),n},e.prototype.toJSON=function(){return this.constructor.toObject(this,a.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/pb.BusinessInfoResPb"},e}(),s.BusinessLoginReqPb=function(){function e(e){if(e)for(var t=Object.keys(e),n=0;n<t.length;++n)null!=e[t[n]]&&(this[t[n]]=e[t[n]])}return e.prototype.account="",e.prototype.password="",e.prototype.token="",e.create=function(t){return new e(t)},e.encode=function(e,t){return t||(t=c.create()),null!=e.account&&Object.hasOwnProperty.call(e,"account")&&t.uint32(10).string(e.account),null!=e.password&&Object.hasOwnProperty.call(e,"password")&&t.uint32(18).string(e.password),null!=e.token&&Object.hasOwnProperty.call(e,"token")&&t.uint32(26).string(e.token),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof u||(e=u.create(e));for(var n=void 0===t?e.len:e.pos+t,r=new d.pb.BusinessLoginReqPb;e.pos<n;){var i=e.uint32();switch(i>>>3){case 1:r.account=e.string();break;case 2:r.password=e.string();break;case 3:r.token=e.string();break;default:e.skipType(7&i)}}return r},e.decodeDelimited=function(e){return e instanceof u||(e=new u(e)),this.decode(e,e.uint32())},e.verify=function(e){return"object"!=typeof e||null===e?"object expected":null!=e.account&&e.hasOwnProperty("account")&&!l.isString(e.account)?"account: string expected":null!=e.password&&e.hasOwnProperty("password")&&!l.isString(e.password)?"password: string expected":null!=e.token&&e.hasOwnProperty("token")&&!l.isString(e.token)?"token: string expected":null},e.fromObject=function(e){if(e instanceof d.pb.BusinessLoginReqPb)return e;var t=new d.pb.BusinessLoginReqPb;return null!=e.account&&(t.account=String(e.account)),null!=e.password&&(t.password=String(e.password)),null!=e.token&&(t.token=String(e.token)),t},e.toObject=function(e,t){t||(t={});var n={};return t.defaults&&(n.account="",n.password="",n.token=""),null!=e.account&&e.hasOwnProperty("account")&&(n.account=e.account),null!=e.password&&e.hasOwnProperty("password")&&(n.password=e.password),null!=e.token&&e.hasOwnProperty("token")&&(n.token=e.token),n},e.prototype.toJSON=function(){return this.constructor.toObject(this,a.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/pb.BusinessLoginReqPb"},e}(),s.BusinessLoginResPb=function(){function e(e){if(e)for(var t=Object.keys(e),n=0;n<t.length;++n)null!=e[t[n]]&&(this[t[n]]=e[t[n]])}return e.prototype.token="",e.prototype.userId="",e.prototype.busId="",e.create=function(t){return new e(t)},e.encode=function(e,t){return t||(t=c.create()),null!=e.token&&Object.hasOwnProperty.call(e,"token")&&t.uint32(10).string(e.token),null!=e.userId&&Object.hasOwnProperty.call(e,"userId")&&t.uint32(18).string(e.userId),null!=e.busId&&Object.hasOwnProperty.call(e,"busId")&&t.uint32(26).string(e.busId),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof u||(e=u.create(e));for(var n=void 0===t?e.len:e.pos+t,r=new d.pb.BusinessLoginResPb;e.pos<n;){var i=e.uint32();switch(i>>>3){case 1:r.token=e.string();break;case 2:r.userId=e.string();break;case 3:r.busId=e.string();break;default:e.skipType(7&i)}}return r},e.decodeDelimited=function(e){return e instanceof u||(e=new u(e)),this.decode(e,e.uint32())},e.verify=function(e){return"object"!=typeof e||null===e?"object expected":null!=e.token&&e.hasOwnProperty("token")&&!l.isString(e.token)?"token: string expected":null!=e.userId&&e.hasOwnProperty("userId")&&!l.isString(e.userId)?"userId: string expected":null!=e.busId&&e.hasOwnProperty("busId")&&!l.isString(e.busId)?"busId: string expected":null},e.fromObject=function(e){if(e instanceof d.pb.BusinessLoginResPb)return e;var t=new d.pb.BusinessLoginResPb;return null!=e.token&&(t.token=String(e.token)),null!=e.userId&&(t.userId=String(e.userId)),null!=e.busId&&(t.busId=String(e.busId)),t},e.toObject=function(e,t){t||(t={});var n={};return t.defaults&&(n.token="",n.userId="",n.busId=""),null!=e.token&&e.hasOwnProperty("token")&&(n.token=e.token),null!=e.userId&&e.hasOwnProperty("userId")&&(n.userId=e.userId),null!=e.busId&&e.hasOwnProperty("busId")&&(n.busId=e.busId),n},e.prototype.toJSON=function(){return this.constructor.toObject(this,a.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/pb.BusinessLoginResPb"},e}(),s.ServerTimeReqPb=function(){function e(e){if(e)for(var t=Object.keys(e),n=0;n<t.length;++n)null!=e[t[n]]&&(this[t[n]]=e[t[n]])}return e.prototype.clientTime="",e.create=function(t){return new e(t)},e.encode=function(e,t){return t||(t=c.create()),null!=e.clientTime&&Object.hasOwnProperty.call(e,"clientTime")&&t.uint32(10).string(e.clientTime),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof u||(e=u.create(e));for(var n=void 0===t?e.len:e.pos+t,r=new d.pb.ServerTimeReqPb;e.pos<n;){var i=e.uint32();switch(i>>>3){case 1:r.clientTime=e.string();break;default:e.skipType(7&i)}}return r},e.decodeDelimited=function(e){return e instanceof u||(e=new u(e)),this.decode(e,e.uint32())},e.verify=function(e){return"object"!=typeof e||null===e?"object expected":null!=e.clientTime&&e.hasOwnProperty("clientTime")&&!l.isString(e.clientTime)?"clientTime: string expected":null},e.fromObject=function(e){if(e instanceof d.pb.ServerTimeReqPb)return e;var t=new d.pb.ServerTimeReqPb;return null!=e.clientTime&&(t.clientTime=String(e.clientTime)),t},e.toObject=function(e,t){t||(t={});var n={};return t.defaults&&(n.clientTime=""),null!=e.clientTime&&e.hasOwnProperty("clientTime")&&(n.clientTime=e.clientTime),n},e.prototype.toJSON=function(){return this.constructor.toObject(this,a.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/pb.ServerTimeReqPb"},e}(),s.ServerTimeResPb=function(){function e(e){if(e)for(var t=Object.keys(e),n=0;n<t.length;++n)null!=e[t[n]]&&(this[t[n]]=e[t[n]])}return e.prototype.clientTime="",e.prototype.serverTransmitTime="",e.prototype.serverSendTime="",e.create=function(t){return new e(t)},e.encode=function(e,t){return t||(t=c.create()),null!=e.clientTime&&Object.hasOwnProperty.call(e,"clientTime")&&t.uint32(10).string(e.clientTime),null!=e.serverTransmitTime&&Object.hasOwnProperty.call(e,"serverTransmitTime")&&t.uint32(18).string(e.serverTransmitTime),null!=e.serverSendTime&&Object.hasOwnProperty.call(e,"serverSendTime")&&t.uint32(26).string(e.serverSendTime),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof u||(e=u.create(e));for(var n=void 0===t?e.len:e.pos+t,r=new d.pb.ServerTimeResPb;e.pos<n;){var i=e.uint32();switch(i>>>3){case 1:r.clientTime=e.string();break;case 2:r.serverTransmitTime=e.string();break;case 3:r.serverSendTime=e.string();break;default:e.skipType(7&i)}}return r},e.decodeDelimited=function(e){return e instanceof u||(e=new u(e)),this.decode(e,e.uint32())},e.verify=function(e){return"object"!=typeof e||null===e?"object expected":null!=e.clientTime&&e.hasOwnProperty("clientTime")&&!l.isString(e.clientTime)?"clientTime: string expected":null!=e.serverTransmitTime&&e.hasOwnProperty("serverTransmitTime")&&!l.isString(e.serverTransmitTime)?"serverTransmitTime: string expected":null!=e.serverSendTime&&e.hasOwnProperty("serverSendTime")&&!l.isString(e.serverSendTime)?"serverSendTime: string expected":null},e.fromObject=function(e){if(e instanceof d.pb.ServerTimeResPb)return e;var t=new d.pb.ServerTimeResPb;return null!=e.clientTime&&(t.clientTime=String(e.clientTime)),null!=e.serverTransmitTime&&(t.serverTransmitTime=String(e.serverTransmitTime)),null!=e.serverSendTime&&(t.serverSendTime=String(e.serverSendTime)),t},e.toObject=function(e,t){t||(t={});var n={};return t.defaults&&(n.clientTime="",n.serverTransmitTime="",n.serverSendTime=""),null!=e.clientTime&&e.hasOwnProperty("clientTime")&&(n.clientTime=e.clientTime),null!=e.serverTransmitTime&&e.hasOwnProperty("serverTransmitTime")&&(n.serverTransmitTime=e.serverTransmitTime),null!=e.serverSendTime&&e.hasOwnProperty("serverSendTime")&&(n.serverSendTime=e.serverSendTime),n},e.prototype.toJSON=function(){return this.constructor.toObject(this,a.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/pb.ServerTimeResPb"},e}(),s.UserAddAwardRes=function(){function e(e){if(e)for(var t=Object.keys(e),n=0;n<t.length;++n)null!=e[t[n]]&&(this[t[n]]=e[t[n]])}return e.prototype.awardId="",e.prototype.awardUrl="",e.prototype.awardName="",e.prototype.address="",e.prototype.code="",e.prototype.createTime="",e.create=function(t){return new e(t)},e.encode=function(e,t){return t||(t=c.create()),null!=e.awardId&&Object.hasOwnProperty.call(e,"awardId")&&t.uint32(10).string(e.awardId),null!=e.awardUrl&&Object.hasOwnProperty.call(e,"awardUrl")&&t.uint32(18).string(e.awardUrl),null!=e.awardName&&Object.hasOwnProperty.call(e,"awardName")&&t.uint32(26).string(e.awardName),null!=e.address&&Object.hasOwnProperty.call(e,"address")&&t.uint32(34).string(e.address),null!=e.code&&Object.hasOwnProperty.call(e,"code")&&t.uint32(42).string(e.code),null!=e.createTime&&Object.hasOwnProperty.call(e,"createTime")&&t.uint32(50).string(e.createTime),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof u||(e=u.create(e));for(var n=void 0===t?e.len:e.pos+t,r=new d.pb.UserAddAwardRes;e.pos<n;){var i=e.uint32();switch(i>>>3){case 1:r.awardId=e.string();break;case 2:r.awardUrl=e.string();break;case 3:r.awardName=e.string();break;case 4:r.address=e.string();break;case 5:r.code=e.string();break;case 6:r.createTime=e.string();break;default:e.skipType(7&i)}}return r},e.decodeDelimited=function(e){return e instanceof u||(e=new u(e)),this.decode(e,e.uint32())},e.verify=function(e){return"object"!=typeof e||null===e?"object expected":null!=e.awardId&&e.hasOwnProperty("awardId")&&!l.isString(e.awardId)?"awardId: string expected":null!=e.awardUrl&&e.hasOwnProperty("awardUrl")&&!l.isString(e.awardUrl)?"awardUrl: string expected":null!=e.awardName&&e.hasOwnProperty("awardName")&&!l.isString(e.awardName)?"awardName: string expected":null!=e.address&&e.hasOwnProperty("address")&&!l.isString(e.address)?"address: string expected":null!=e.code&&e.hasOwnProperty("code")&&!l.isString(e.code)?"code: string expected":null!=e.createTime&&e.hasOwnProperty("createTime")&&!l.isString(e.createTime)?"createTime: string expected":null},e.fromObject=function(e){if(e instanceof d.pb.UserAddAwardRes)return e;var t=new d.pb.UserAddAwardRes;return null!=e.awardId&&(t.awardId=String(e.awardId)),null!=e.awardUrl&&(t.awardUrl=String(e.awardUrl)),null!=e.awardName&&(t.awardName=String(e.awardName)),null!=e.address&&(t.address=String(e.address)),null!=e.code&&(t.code=String(e.code)),null!=e.createTime&&(t.createTime=String(e.createTime)),t},e.toObject=function(e,t){t||(t={});var n={};return t.defaults&&(n.awardId="",n.awardUrl="",n.awardName="",n.address="",n.code="",n.createTime=""),null!=e.awardId&&e.hasOwnProperty("awardId")&&(n.awardId=e.awardId),null!=e.awardUrl&&e.hasOwnProperty("awardUrl")&&(n.awardUrl=e.awardUrl),null!=e.awardName&&e.hasOwnProperty("awardName")&&(n.awardName=e.awardName),null!=e.address&&e.hasOwnProperty("address")&&(n.address=e.address),null!=e.code&&e.hasOwnProperty("code")&&(n.code=e.code),null!=e.createTime&&e.hasOwnProperty("createTime")&&(n.createTime=e.createTime),n},e.prototype.toJSON=function(){return this.constructor.toObject(this,a.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/pb.UserAddAwardRes"},e}(),s.UserAwardHistory=function(){function e(e){if(this.items=[],e)for(var t=Object.keys(e),n=0;n<t.length;++n)null!=e[t[n]]&&(this[t[n]]=e[t[n]])}return e.prototype.items=l.emptyArray,e.create=function(t){return new e(t)},e.encode=function(e,t){if(t||(t=c.create()),null!=e.items&&e.items.length)for(var n=0;n<e.items.length;++n)d.pb.UserAddAwardRes.encode(e.items[n],t.uint32(10).fork()).ldelim();return t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof u||(e=u.create(e));for(var n=void 0===t?e.len:e.pos+t,r=new d.pb.UserAwardHistory;e.pos<n;){var i=e.uint32();switch(i>>>3){case 1:r.items&&r.items.length||(r.items=[]),r.items.push(d.pb.UserAddAwardRes.decode(e,e.uint32()));break;default:e.skipType(7&i)}}return r},e.decodeDelimited=function(e){return e instanceof u||(e=new u(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.items&&e.hasOwnProperty("items")){if(!Array.isArray(e.items))return"items: array expected";for(var t=0;t<e.items.length;++t){var n=d.pb.UserAddAwardRes.verify(e.items[t]);if(n)return"items."+n}}return null},e.fromObject=function(e){if(e instanceof d.pb.UserAwardHistory)return e;var t=new d.pb.UserAwardHistory;if(e.items){if(!Array.isArray(e.items))throw TypeError(".pb.UserAwardHistory.items: array expected");t.items=[];for(var n=0;n<e.items.length;++n){if("object"!=typeof e.items[n])throw TypeError(".pb.UserAwardHistory.items: object expected");t.items[n]=d.pb.UserAddAwardRes.fromObject(e.items[n])}}return t},e.toObject=function(e,t){t||(t={});var n={};if((t.arrays||t.defaults)&&(n.items=[]),e.items&&e.items.length){n.items=[];for(var r=0;r<e.items.length;++r)n.items[r]=d.pb.UserAddAwardRes.toObject(e.items[r],t)}return n},e.prototype.toJSON=function(){return this.constructor.toObject(this,a.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/pb.UserAwardHistory"},e}(),s.UserGameHistory=function(){function e(e){if(this.item=[],e)for(var t=Object.keys(e),n=0;n<t.length;++n)null!=e[t[n]]&&(this[t[n]]=e[t[n]])}return e.prototype.item=l.emptyArray,e.create=function(t){return new e(t)},e.encode=function(e,t){if(t||(t=c.create()),null!=e.item&&e.item.length)for(var n=0;n<e.item.length;++n)d.pb.UserGameHistoryItem.encode(e.item[n],t.uint32(10).fork()).ldelim();return t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof u||(e=u.create(e));for(var n=void 0===t?e.len:e.pos+t,r=new d.pb.UserGameHistory;e.pos<n;){var i=e.uint32();switch(i>>>3){case 1:r.item&&r.item.length||(r.item=[]),r.item.push(d.pb.UserGameHistoryItem.decode(e,e.uint32()));break;default:e.skipType(7&i)}}return r},e.decodeDelimited=function(e){return e instanceof u||(e=new u(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.item&&e.hasOwnProperty("item")){if(!Array.isArray(e.item))return"item: array expected";for(var t=0;t<e.item.length;++t){var n=d.pb.UserGameHistoryItem.verify(e.item[t]);if(n)return"item."+n}}return null},e.fromObject=function(e){if(e instanceof d.pb.UserGameHistory)return e;var t=new d.pb.UserGameHistory;if(e.item){if(!Array.isArray(e.item))throw TypeError(".pb.UserGameHistory.item: array expected");t.item=[];for(var n=0;n<e.item.length;++n){if("object"!=typeof e.item[n])throw TypeError(".pb.UserGameHistory.item: object expected");t.item[n]=d.pb.UserGameHistoryItem.fromObject(e.item[n])}}return t},e.toObject=function(e,t){t||(t={});var n={};if((t.arrays||t.defaults)&&(n.item=[]),e.item&&e.item.length){n.item=[];for(var r=0;r<e.item.length;++r)n.item[r]=d.pb.UserGameHistoryItem.toObject(e.item[r],t)}return n},e.prototype.toJSON=function(){return this.constructor.toObject(this,a.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/pb.UserGameHistory"},e}(),s.UserGameHistoryItem=function(){function e(e){if(e)for(var t=Object.keys(e),n=0;n<t.length;++n)null!=e[t[n]]&&(this[t[n]]=e[t[n]])}return e.prototype.rank="",e.prototype.businessName="",e.prototype.createTime="",e.create=function(t){return new e(t)},e.encode=function(e,t){return t||(t=c.create()),null!=e.rank&&Object.hasOwnProperty.call(e,"rank")&&t.uint32(10).string(e.rank),null!=e.businessName&&Object.hasOwnProperty.call(e,"businessName")&&t.uint32(18).string(e.businessName),null!=e.createTime&&Object.hasOwnProperty.call(e,"createTime")&&t.uint32(26).string(e.createTime),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof u||(e=u.create(e));for(var n=void 0===t?e.len:e.pos+t,r=new d.pb.UserGameHistoryItem;e.pos<n;){var i=e.uint32();switch(i>>>3){case 1:r.rank=e.string();break;case 2:r.businessName=e.string();break;case 3:r.createTime=e.string();break;default:e.skipType(7&i)}}return r},e.decodeDelimited=function(e){return e instanceof u||(e=new u(e)),this.decode(e,e.uint32())},e.verify=function(e){return"object"!=typeof e||null===e?"object expected":null!=e.rank&&e.hasOwnProperty("rank")&&!l.isString(e.rank)?"rank: string expected":null!=e.businessName&&e.hasOwnProperty("businessName")&&!l.isString(e.businessName)?"businessName: string expected":null!=e.createTime&&e.hasOwnProperty("createTime")&&!l.isString(e.createTime)?"createTime: string expected":null},e.fromObject=function(e){if(e instanceof d.pb.UserGameHistoryItem)return e;var t=new d.pb.UserGameHistoryItem;return null!=e.rank&&(t.rank=String(e.rank)),null!=e.businessName&&(t.businessName=String(e.businessName)),null!=e.createTime&&(t.createTime=String(e.createTime)),t},e.toObject=function(e,t){t||(t={});var n={};return t.defaults&&(n.rank="",n.businessName="",n.createTime=""),null!=e.rank&&e.hasOwnProperty("rank")&&(n.rank=e.rank),null!=e.businessName&&e.hasOwnProperty("businessName")&&(n.businessName=e.businessName),null!=e.createTime&&e.hasOwnProperty("createTime")&&(n.createTime=e.createTime),n},e.prototype.toJSON=function(){return this.constructor.toObject(this,a.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/pb.UserGameHistoryItem"},e}(),s.UserLoginReqPb=function(){function e(e){if(e)for(var t=Object.keys(e),n=0;n<t.length;++n)null!=e[t[n]]&&(this[t[n]]=e[t[n]])}return e.prototype.token="",e.prototype.roomId="",e.create=function(t){return new e(t)},e.encode=function(e,t){return t||(t=c.create()),null!=e.token&&Object.hasOwnProperty.call(e,"token")&&t.uint32(10).string(e.token),null!=e.roomId&&Object.hasOwnProperty.call(e,"roomId")&&t.uint32(18).string(e.roomId),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof u||(e=u.create(e));for(var n=void 0===t?e.len:e.pos+t,r=new d.pb.UserLoginReqPb;e.pos<n;){var i=e.uint32();switch(i>>>3){case 1:r.token=e.string();break;case 2:r.roomId=e.string();break;default:e.skipType(7&i)}}return r},e.decodeDelimited=function(e){return e instanceof u||(e=new u(e)),this.decode(e,e.uint32())},e.verify=function(e){return"object"!=typeof e||null===e?"object expected":null!=e.token&&e.hasOwnProperty("token")&&!l.isString(e.token)?"token: string expected":null!=e.roomId&&e.hasOwnProperty("roomId")&&!l.isString(e.roomId)?"roomId: string expected":null},e.fromObject=function(e){if(e instanceof d.pb.UserLoginReqPb)return e;var t=new d.pb.UserLoginReqPb;return null!=e.token&&(t.token=String(e.token)),null!=e.roomId&&(t.roomId=String(e.roomId)),t},e.toObject=function(e,t){t||(t={});var n={};return t.defaults&&(n.token="",n.roomId=""),null!=e.token&&e.hasOwnProperty("token")&&(n.token=e.token),null!=e.roomId&&e.hasOwnProperty("roomId")&&(n.roomId=e.roomId),n},e.prototype.toJSON=function(){return this.constructor.toObject(this,a.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/pb.UserLoginReqPb"},e}(),s.UserLoginResPb=function(){function e(e){if(e)for(var t=Object.keys(e),n=0;n<t.length;++n)null!=e[t[n]]&&(this[t[n]]=e[t[n]])}return e.prototype.result=!1,e.create=function(t){return new e(t)},e.encode=function(e,t){return t||(t=c.create()),null!=e.result&&Object.hasOwnProperty.call(e,"result")&&t.uint32(8).bool(e.result),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof u||(e=u.create(e));for(var n=void 0===t?e.len:e.pos+t,r=new d.pb.UserLoginResPb;e.pos<n;){var i=e.uint32();switch(i>>>3){case 1:r.result=e.bool();break;default:e.skipType(7&i)}}return r},e.decodeDelimited=function(e){return e instanceof u||(e=new u(e)),this.decode(e,e.uint32())},e.verify=function(e){return"object"!=typeof e||null===e?"object expected":null!=e.result&&e.hasOwnProperty("result")&&"boolean"!=typeof e.result?"result: boolean expected":null},e.fromObject=function(e){if(e instanceof d.pb.UserLoginResPb)return e;var t=new d.pb.UserLoginResPb;return null!=e.result&&(t.result=Boolean(e.result)),t},e.toObject=function(e,t){t||(t={});var n={};return t.defaults&&(n.result=!1),null!=e.result&&e.hasOwnProperty("result")&&(n.result=e.result),n},e.prototype.toJSON=function(){return this.constructor.toObject(this,a.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/pb.UserLoginResPb"},e}(),s.RoomStatusPushPb=function(){function e(e){if(e)for(var t=Object.keys(e),n=0;n<t.length;++n)null!=e[t[n]]&&(this[t[n]]=e[t[n]])}return e.prototype.json="",e.create=function(t){return new e(t)},e.encode=function(e,t){return t||(t=c.create()),null!=e.json&&Object.hasOwnProperty.call(e,"json")&&t.uint32(10).string(e.json),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof u||(e=u.create(e));for(var n=void 0===t?e.len:e.pos+t,r=new d.pb.RoomStatusPushPb;e.pos<n;){var i=e.uint32();switch(i>>>3){case 1:r.json=e.string();break;default:e.skipType(7&i)}}return r},e.decodeDelimited=function(e){return e instanceof u||(e=new u(e)),this.decode(e,e.uint32())},e.verify=function(e){return"object"!=typeof e||null===e?"object expected":null!=e.json&&e.hasOwnProperty("json")&&!l.isString(e.json)?"json: string expected":null},e.fromObject=function(e){if(e instanceof d.pb.RoomStatusPushPb)return e;var t=new d.pb.RoomStatusPushPb;return null!=e.json&&(t.json=String(e.json)),t},e.toObject=function(e,t){t||(t={});var n={};return t.defaults&&(n.json=""),null!=e.json&&e.hasOwnProperty("json")&&(n.json=e.json),n},e.prototype.toJSON=function(){return this.constructor.toObject(this,a.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/pb.RoomStatusPushPb"},e}(),s),r.exports=d,e("default",r.exports)}),(function(){return{"protobufjs/minimal":r}}))}}}));

System.register("chunks:///_virtual/user.mjs_cjs=&original=.js",["./user.js","./cjs-loader.mjs"],(function(e,r){var t,s;return{setters:[function(r){t=r.__cjsMetaURL;var s={};s.__cjsMetaURL=r.__cjsMetaURL,s.default=r.default,e(s)},function(e){s=e.default}],execute:function(){t||s.throwInvalidWrapper("./user.js",r.meta.url),s.require(t)}}}));

System.register("chunks:///_virtual/Vec3Util.ts",["./rollupPluginModLoBabelHelpers.js","cc","./MathUtil.ts"],(function(n){var e,r,t,u,a;return{setters:[function(n){e=n.createClass},function(n){r=n.cclegacy,t=n.Vec3,u=n.Mat4},function(n){a=n.MathUtil}],execute:function(){r._RF.push({},"44bdf5CRcpFOYIYhRucHKUx","Vec3Util",void 0);n("Vec3Util",function(){function n(){}return n.progress=function(n,e,r){var u=new t;return u.x=a.progress(n.x,e.x,r),u.y=a.progress(n.y,e.y,r),u.z=a.progress(n.z,e.z,r),u},n.add=function(n,e){var r=new t;return t.add(r,n,e),r},n.sub=function(n,e){var r=new t;return t.subtract(r,n,e),r},n.mul=function(n,e){var r=new t;return t.multiplyScalar(r,n,e),r},n.div=function(n,e){var r=new t;return r.x=n.x/e,r.y=n.y/e,r.z=n.z/e,r},n.equals=function(n,e){return n.x==e.x&&n.y==e.y&&n.z==e.z},n.magnitude=function(n){return n.length()},n.normalize=function(n){return new t(n.x,n.y,n.z).normalize()},n.direction=function(n,e){var r=new t;return t.subtract(r,e,n),r.normalize()},n.distance=function(n,e){return t.distance(n,e)},n.lerp=function(n,e,r){return this.bezierOne(r,n,e)},n.slerp=function(n,e,r){if(r<=0)return n;if(r>=1)return e;var u=this.rotateTo(n,e,t.angle(n,e)/Math.PI*180*r),a=e.length()*r+n.length()*(1-r);return u.normalize().multiplyScalar(a)},n.rotateTo=function(n,e,r){if(0==t.angle(n,e))return e;var a=new t;t.cross(a,n,e),a.normalize();var l=r*Math.PI/180,i=new u;return i.rotate(l,a),new t(n.x*i.m00+n.y*i.m04+n.z*i.m08,n.x*i.m01+n.y*i.m05+n.z*i.m09,n.x*i.m02+n.y*i.m06+n.z*i.m10)},n.bezierOne=function(n,e,r){n>1?n=1:n<0&&(n=0);var t=e.clone(),u=r.clone();return t.multiplyScalar(1-n).add(u.multiplyScalar(n))},n.bezierTwo=function(n,e,r,u){n>1?n=1:n<0&&(n=0);var a=1-n,l=n*n,i=e.clone(),c=new t,o=r.clone(),y=u.clone();return c.add(i.multiplyScalar(a*a)),c.add(o.multiplyScalar(2*a*n)),c.add(y.multiplyScalar(l)),c},n.bezierThree=function(n,e,r,t,u){n>1?n=1:n<0&&(n=0);var a=1-n,l=a*a,i=l*a,c=n*n,o=c*n,y=e.clone(),f=e.clone(),s=r.clone(),d=t.clone(),m=u.clone();return f.add(y.multiplyScalar(i)),f.add(s.multiplyScalar(3*l*n)),f.add(d.multiplyScalar(3*a*c)),f.add(m.multiplyScalar(o)),f},n.dot=function(n,e){var r=n,t=e;return r.x*t.x+r.y*t.y+r.z*t.z},n.cross=function(n,e){var r=new t(1,0,0),u=new t(0,1,0),a=new t(0,0,1),l=new t(n.x,n.y,n.z),i=new t(e.x,e.y,e.z),c=r.multiplyScalar(l.y*i.z-i.y*l.z),o=u.multiplyScalar(i.x*l.z-l.x*i.z),y=a.multiplyScalar(l.x*i.y-i.x*l.y);return c.add(o).add(y)},n.angle=function(n,e){var r=this.dot(n.clone().normalize(),e.clone().normalize());return Math.acos(r)/Math.PI*180*Math.sign(r)},n.dirAngle=function(e,r){var t=n.cross(e,r);return n.angle(e,r)*Math.sign(n.dot(t.normalize(),n.cross(r.normalize(),e.normalize())))},e(n,null,[{key:"x",get:function(){return new t(1,0,0)}},{key:"y",get:function(){return new t(0,1,0)}},{key:"z",get:function(){return new t(0,0,1)}},{key:"left",get:function(){return new t(-1,0,0)}},{key:"right",get:function(){return new t(1,0,0)}},{key:"up",get:function(){return new t(0,1,0)}},{key:"down",get:function(){return new t(0,-1,0)}},{key:"forward",get:function(){return new t(0,0,1)}},{key:"back",get:function(){return new t(0,0,-1)}},{key:"one",get:function(){return new t(1,1,1)}},{key:"zero",get:function(){return new t(0,0,0)}}]),n}());r._RF.pop()}}}));

System.register("chunks:///_virtual/ViewBase.ts",["./rollupPluginModLoBabelHelpers.js","cc","./EventManager2.ts","./Tool.ts","./xcore.ts"],(function(e){var t,n,o,i,r,s,a,c,l,u,h,p,d,f,m,g,v,y,w,B,b,C,S,_,k,D,L;return{setters:[function(e){t=e.applyDecoratedDescriptor,n=e.inheritsLoose,o=e.initializerDefineProperty,i=e.assertThisInitialized,r=e.asyncToGenerator,s=e.regeneratorRuntime},function(e){a=e.cclegacy,c=e.Enum,l=e._decorator,u=e.Node,h=e.Sprite,p=e.Button,d=e.sp,f=e.view,m=e.UITransform,g=e.Graphics,v=e.BlockInputEvents,y=e.Vec3,w=e.Color,B=e.Layers,b=e.log,C=e.v3,S=e.tween,_=e.Component},function(e){k=e.EventManager},function(e){D=e.default},function(e){L=e.xcore}],execute:function(){var R,z,E,F,A,O,V,I,P,U,x,G;a._RF.push({},"98aee6eFudGH6g1TrOqujye","ViewBase",void 0);var M=c({None:0,Scale:1,DrawerBottom:2,Ui:3}),T=l.ccclass,N=l.property;e("ViewBase",(R=T("ViewBase"),z=N({type:M,tooltip:"弹窗打开动画类型"}),E=N(u),F=N(h),A=N(p),R((I=t((V=function(e){function t(){for(var t,n=arguments.length,r=new Array(n),s=0;s<n;s++)r[s]=arguments[s];return t=e.call.apply(e,[this].concat(r))||this,o(t,"maskOpacity",I,i(t)),o(t,"animStyle",P,i(t)),o(t,"ndRoot",U,i(t)),o(t,"sprFrame",x,i(t)),o(t,"btnClose",G,i(t)),t._viewName=null,t._ndBg=null,t._eventList=new Map,t}n(t,e);var a=t.prototype;return a.onLoadCompleted=function(){},a.onOpenCompleted=function(){},a.onDisableCompleted=function(){},a.onDestroyCompleted=function(){},a.onShowCompleted=function(){},a.onBeforeClose=function(){},a.onLoad=function(){this.animStyle!=M.Ui&&this.createBgGraphics(),this.onLoadCompleted()},a.setData=function(e){},a.closeSelf=function(){var e=this;this.onBeforeClose(),this.scheduleOnce((function(){e.node.destroy()}))},a.addBaseListener=function(){var e=this;this.btnClose&&this.btnClose.node.on("click",(function(){e.closeSelf()}),this)},a.releaseAssets=function(){for(var e=D.getComponentPropertys(this),t=0;t<e.length;t++){var n=e[t];n instanceof h&&n.spriteFrame&&n.spriteFrame._resAsset?L.res.releaseAsset(n.spriteFrame._resAsset):n instanceof d.Skeleton&&n.skeletonData&&L.res.releaseSpineAsset(n.node)}},a.addEventListener=function(e,t){var n=function(e,n){t(n)};this._eventList.set(e,n),k.getInstance().addEventListener(e,n)},a.createBgGraphics=function(){var e=f.getVisibleSize();this._ndBg=new u,this._ndBg.name="ndMaskBg",this._ndBg.addComponent(m),this._ndBg.addComponent(g),this._ndBg.addComponent(v),this._ndBg.getComponent(m).width=e.width,this._ndBg.getComponent(m).height=e.height,this._ndBg.position=new y;var t=this._ndBg.getComponent(g);t.rect(-e.width/2,-e.height/2,e.width,e.height),t.fillColor=new w(0,0,0,this.maskOpacity),t.fill(),this._ndBg.layer=B.Enum.UI_2D,this._ndBg.parent=this.node,D.setChildrenNodeSortByPriority(this._ndBg,-1)},a.removeBg=function(){this._ndBg.destroy()},a.addButtonEvent=function(e,t,n){var o;(o=e instanceof u?e.getComponent(p)||e.addComponent(p):e).node.off("click"),o.node.on("click",t,n)},a.onShow=function(){var e=this;b("BaseUI onShow  "+this.node.name),this.onShowCompleted(),this.popAnim((function(){e.addBaseListener(),e.onOpenCompleted()}))},a.onEnable=function(){var e=r(s().mark((function e(){return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),a.onDisable=function(){this.onDisableCompleted()},a.onDestroy=function(){this._eventList.forEach((function(e,t){k.getInstance().removeEventListener(t,e)})),this.onDestroyCompleted()},a.popAnim=function(e){if(this.ndRoot)switch(this.animStyle){case M.Scale:this.ndRoot.setScale(C(0,0,0)),S(this.ndRoot).to(.15,{scale:C(1,1,1)},{easing:"backOut"}).call((function(){e&&e()})).start();break;case M.DrawerBottom:var t=-f.getVisibleSize().height/2-this.sprFrame.node.getComponent(m).height/2,n=-f.getVisibleSize().height/2;this.ndRoot.setPosition(C(0,t)),S(this.ndRoot).to(.15,{position:C(0,n)}).call((function(){e&&e()})).start();break;default:e&&e()}},a.hideAnim=function(){var e=r(s().mark((function e(){var t=this;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise((function(e,n){if(t.ndRoot)switch(t.animStyle){case M.Scale:S(t.ndRoot).to(.15,{scale:C(0,0,0)},{easing:"sineOut"}).call((function(){e("")})).start();break;case M.DrawerBottom:var o=-f.getVisibleSize().height/2-t.sprFrame.node.getComponent(m).height;S(t.ndRoot).to(.15,{position:C(0,o)}).call((function(){e("")})).start();break;case M.Ui:default:e("")}else e("")})));case 1:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),t}(_)).prototype,"maskOpacity",[N],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 200}}),P=t(V.prototype,"animStyle",[z],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return M.Scale}}),U=t(V.prototype,"ndRoot",[E],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),x=t(V.prototype,"sprFrame",[F],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),G=t(V.prototype,"btnClose",[A],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),O=V))||O));a._RF.pop()}}}));

System.register("chunks:///_virtual/ViewGroupNesting.ts",["./rollupPluginModLoBabelHelpers.js","cc"],(function(t){var e,n,o,s,i,h;return{setters:[function(t){e=t.inheritsLoose},function(t){n=t.cclegacy,o=t._decorator,s=t.Node,i=t.EventTouch,h=t.Component}],execute:function(){var a;n._RF.push({},"46a54e/XXVH1r7GbJmUQqL8","ViewGroupNesting",void 0);var r=o.ccclass;o.property,t("default",r(a=function(t){function n(){for(var e,n=arguments.length,o=new Array(n),s=0;s<n;s++)o[s]=arguments[s];return(e=t.call.apply(t,[this].concat(o))||this).events=[],e}e(n,t);var o=n.prototype;return o.onLoad=function(){this.node.on(s.EventType.TOUCH_START,this.onTouchHandle,this,!0),this.node.on(s.EventType.TOUCH_MOVE,this.onTouchHandle,this,!0),this.node.on(s.EventType.TOUCH_END,this.onTouchHandle,this,!0),this.node.on(s.EventType.TOUCH_CANCEL,this.onTouchHandle,this,!0)},o.onTouchHandle=function(t,e){if(!t.sham&&!t.simulate&&t.target!==this.node){var n=t.getDelta();if(!(Math.abs(n.x)>3&&1.2*Math.abs(n.x)>Math.abs(n.y))){var o=new i(t.getTouches(),t.bubbles,e);o.type=t.type,o.touch=t.touch,o.sham=!0,this.events.push(o)}}},o.update=function(){if(0!==this.events.length){for(var t=0;t<this.events.length;t++)this.node.dispatchEvent(this.events[t]);this.events.length=0}},n}(h))||a);n._RF.pop()}}}));

System.register("chunks:///_virtual/ViewManager.ts",["./rollupPluginModLoBabelHelpers.js","cc","./EventManager2.ts","./ResLoader.ts","./Singleton.ts","./Tool.ts","./xcore.ts","./Queue.ts","./ConstGlobal.ts","./UILoading.ts","./UILoadingProgress.ts"],(function(e){var t,n,i,r,a,s,o,c,u,f,p,h,l,v,w,d,g,_,C,m,b,x,k,V;return{setters:[function(e){t=e.inheritsLoose,n=e.assertThisInitialized,i=e.asyncToGenerator,r=e.regeneratorRuntime},function(e){a=e.cclegacy,s=e.JsonAsset,o=e.log,c=e.warn,u=e.instantiate,f=e.director,p=e.RichText,h=e.v3,l=e.TweenSystem,v=e.tween},function(e){w=e.EventManager},function(e){d=e.default},function(e){g=e.Singleton},function(e){_=e.default},function(e){C=e.xcore},function(e){m=e.Queue},function(e){b=e.E_EVENT,x=e.E_UILAYER},function(e){k=e.UILoading},function(e){V=e.UILoadingProgress}],execute:function(){a._RF.push({},"26ad0ntuK1OuqK7PxVrgzQc","ViewManager",void 0);e("ViewManager",function(e){function a(){var t;return(t=e.call(this)||this)._viewconfigs=null,t._sceneName=void 0,t._uiCacheMap=new Map,t._viewQueue=new m(new Function),t._viewCacheList=[],t._activeViewConfigs=[],t._viewConfigGroup=[],t._popTimer=null,w.getInstance().addEventListener(b.UiClose,t.onViewClose,n(t)),t}t(a,e);var g=a.prototype;return g.initViewConfig=function(){var e=this;d.load("viewconfig",s).then((function(t){e._viewconfigs=t.json,o("_viewconfigs",e._viewconfigs)}))},g.onViewClose=function(e,t){o("onViewClose",t),this.checkPop()},g.addQueue=function(){var e=i(r().mark((function e(t){var n=this;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise((function(e,a){n._viewQueue.enqueue(t.bind(n),i(r().mark((function t(n){var i;return r().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(n.isEmpty()){t.next=6;break}return i=n.dequeue(),t.next=4,i();case 4:t.next=0;break;case 6:e("");case 7:case"end":return t.stop()}}),t)}))))})));case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),g.addView=function(){var e=i(r().mark((function e(t,n,i,a){var s;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(void 0===n&&(n=null),void 0===i&&(i=0),s=this._viewconfigs?this._viewconfigs[t]:null){e.next=6;break}return c("UIConfig can not be null ！",t),e.abrupt("return");case 6:if(!(this._viewConfigGroup.filter((function(e){return e.viewName===s.viewName})).length>0)){e.next=9;break}return c("_viewConfigGroup same view ！"+s.viewName),e.abrupt("return");case 9:s.priority=i,s.data=n,a&&(s.openCb=a),!s.priority||s.priority<=0?this.showView(s):(this._viewConfigGroup.push(s),this.checkPop(.01));case 13:case"end":return e.stop()}}),e,this)})));return function(t,n,i,r){return e.apply(this,arguments)}}(),g.checkPop=function(e){var t=this;void 0===e&&(e=0),this._activeViewConfigs.length<=0&&(this._popTimer&&clearTimeout(this._popTimer),this._popTimer=setTimeout((function(){if(t._viewConfigGroup.length>0){t._viewConfigGroup.sort((function(e,t){return t.priority-e.priority}));var e=t._viewConfigGroup.pop();t.showView(e)}}),e))},g.showView=function(){var e=i(r().mark((function e(t){var n,i,a,s,p,h,l,v,w=this;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=2;break}return e.abrupt("return");case 2:if(this._activeViewConfigs.push(t),t.isShowLoading,n=t.data,!(i=this.getView(t.viewName))){e.next=12;break}return i.node.active=!0,i.onShow(),t.openCb&&(t.openCb(),t.openCb=null),null!=n&&null!=n&&(o("setData:",n),i.setData(n)),e.abrupt("return");case 12:return e.prev=12,a=t.bundleName,s=t.path,e.next=17,C.res.bundleLoadPrefab(a,""+s);case 17:p=e.sent,h=u(p),(l=f.getScene().getChildByName("Canvas"))||c("find uiparnet node err => Canvas no find!"),h.parent=l,(v=h.getComponent(h.name))._viewName=t.viewName,v.closeSelf=function(e){void 0===e&&(e=!1),v.onBeforeClose(),w.closeView(t.viewName,e)},v.onShow(),t.openCb&&(t.openCb(),t.openCb=null),this._viewCacheList.push(v),_.setChildrenNodeSortByPriority(h,x.VIEW_LAYER),null!=n&&null!=n&&(o("setData:",n),v.setData(n)),e.next=37;break;case 32:e.prev=32,e.t0=e.catch(12),o(e.t0),this._activeViewConfigs.pop(),this.checkPop();case 37:case"end":return e.stop()}}),e,this,[[12,32]])})));return function(t){return e.apply(this,arguments)}}(),g.releaseAsset=function(e){C.res.releaseAsset(e.node._prefab.asset),e.releaseAssets()},g.closeView=function(){var e=i(r().mark((function e(t,n){var a,s,c=this;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:void 0===n&&(n=!1),a=r().mark((function e(a){var s;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if((s=c._viewCacheList[a])._viewName!==t){e.next=5;break}return e.next=4,c.addQueue(i(r().mark((function e(){var i;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(s&&s.node&&s.node.isValid)){e.next=11;break}if(e.t0=!n&&s.node.active,!e.t0){e.next=5;break}return e.next=5,s.hideAnim();case 5:for(c.releaseAsset(s),s.node.destroy(),c._viewCacheList.splice(a,1),i=0;i<c._activeViewConfigs.length;i++)c._activeViewConfigs[i].viewName===t&&c._activeViewConfigs.splice(i,1);w.getInstance().raiseEvent(b.UiClose,t),o("hideFinish");case 11:case"end":return e.stop()}}),e)}))));case 4:return e.abrupt("return",1);case 5:case"end":return e.stop()}}),e)})),s=0;case 3:if(!(s<this._viewCacheList.length)){e.next=10;break}return e.delegateYield(a(s),"t0",5);case 5:if(!e.t0){e.next=7;break}return e.abrupt("break",10);case 7:++s,e.next=3;break;case 10:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}(),g.hideView=function(){var e=i(r().mark((function e(t){var n,i,a,s=this;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(n=this.getView(t))){e.next=13;break}return e.next=4,n.hideAnim();case 4:n.node.active=!1,w.getInstance().raiseEvent(b.UiClose),i=r().mark((function e(t){return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:s._activeViewConfigs[t].viewName===n._viewName&&s.addQueue((function(){return s._activeViewConfigs.splice(t,1)}));case 1:case"end":return e.stop()}}),e)})),a=0;case 8:if(!(a<this._activeViewConfigs.length)){e.next=13;break}return e.delegateYield(i(a),"t0",10);case 10:a++,e.next=8;break;case 13:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),g.getView=function(e){for(var t=0;t<this._viewCacheList.length;++t)if(this._viewCacheList[t]._viewName===e)return this._viewCacheList[t];return null},g.closeAllView=function(){var e=i(r().mark((function e(t){var n,i,a;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:for(void 0===t&&(t=!1),n=[],i=0;i<this._viewCacheList.length;++i)n.push(this._viewCacheList[i]._viewName);a=0;case 4:if(!(a<n.length)){e.next=10;break}return e.next=7,this.closeView(n[a],t);case 7:a++,e.next=4;break;case 10:n=null,this._viewCacheList=[],this._viewConfigGroup=[],this._activeViewConfigs=[];case 14:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),g.switchScene=function(){var e=i(r().mark((function e(t,n,i){var a=this;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this._popTimer&&clearTimeout(this._popTimer),this._sceneName==n){e.next=5;break}return e.next=4,this.closeAllView();case 4:this._uiCacheMap.forEach((function(e,t){a.closeUI(t)}));case 5:if(this._sceneName=n,t){e.next=9;break}return f.loadScene(n,(function(){i&&i()})),e.abrupt("return");case 9:return e.next=11,d.getBundle(t);case 11:e.sent.loadScene(n,(function(){f.loadScene(n,(function(){i&&i()}))}));case 13:case"end":return e.stop()}}),e,this)})));return function(t,n,i){return e.apply(this,arguments)}}(),g.setUI=function(){var e=i(r().mark((function e(t,n){var i,a,s;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,C.res.bundleLoadPrefab(t,n);case 2:return i=e.sent,a=u(i),(s=f.getScene().getChildByName("Canvas"))||c("find uiparnet node err => Canvas no find!"),a.parent=s,_.setChildrenNodeSortByPriority(a,x.UI_LAYER),this._uiCacheMap.set(a.name,a),e.abrupt("return",a);case 10:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}(),g.closeUI=function(e){var t=this._uiCacheMap.get(e);t&&(this._uiCacheMap.delete(e),t&&(t._prefab&&C.res.releaseAsset(t._prefab.asset),t.destroy(),t=null))},g.showToast=function(){var e=i(r().mark((function e(t){var n,i,a,s,o;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n="ViewToast",i=this._uiCacheMap.get(n)){e.next=15;break}if(a=this._viewconfigs?this._viewconfigs.ViewToast:null){e.next=7;break}return c("ViewToast can not be null ！"),e.abrupt("return");case 7:return e.next=9,C.res.bundleLoadPrefab(a.bundleName,a.path);case 9:s=e.sent,i=u(s),this._uiCacheMap.set(i.name,i),(o=f.getScene().getChildByName("Canvas"))||c("uiparnet Canvas no find!"),i.parent=o;case 15:_.setChildrenNodeSortByPriority(i,x.TOAST_LAVER),i.getChildByName("lbToast").getComponent(p).string=t,i.setPosition(h(0,0)),i.active=!0,l.instance.ActionManager.removeAllActionsFromTarget(i),v(i).to(.7,{position:h(0,120)},{easing:"cubicInOut"}).delay(.35).call((function(){i.active=!1})).start();case 21:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),g.showLoadingUI=function(){var e=i(r().mark((function e(t,n){var i;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,C.ui.setUI("resources","prefab/UI/UILoading").then((function(e){e.getComponent(k).initUI(t.resLoading)}));case 3:return e.next=5,C.ui.setUI("resources","prefab/UI/UILoadingProgress");case 5:return i=e.sent,e.abrupt("return",i.getComponent(V).initUI(["加载中","加载中.","加载中..","加载中..."],n,t.resLoadingPgs));case 9:e.prev=9,e.t0=e.catch(0),C.ui.showToast("ui初始化错误");case 12:case"end":return e.stop()}}),e,null,[[0,9]])})));return function(t,n){return e.apply(this,arguments)}}(),a}(g));a._RF.pop()}}}));

System.register("chunks:///_virtual/WebSock.ts",["cc"],(function(t){var e,n;return{setters:[function(t){e=t.cclegacy,n=t.log}],execute:function(){e._RF.push({},"82d4e53OoZFlrAJBnUUKSuY","WebSock",void 0);t("WebSock",function(){function t(){this._ws=null,this.onConnected=null,this.onMessage=null,this.onError=null,this.onClosed=null}var e=t.prototype;return e.connect=function(t){var e=this;if(this._ws&&this._ws.readyState===WebSocket.CONNECTING)return n("websocket connecting, wait for a moment..."),!1;var s=null;if(t.url)s=t.url;else{var o=t.ip,r=t.port;s=t.protocol+"://"+o+":"+r}return this._ws=new WebSocket(s),this._ws.binaryType=t.binaryType?t.binaryType:"arraybuffer",this._ws.onmessage=function(t){(0,e.onMessage)(t.data)},this._ws.onopen=this.onConnected,this._ws.onerror=this.onError,this._ws.onclose=this.onClosed,!0},e.send=function(t){return this._ws&&this._ws.readyState==WebSocket.OPEN?(this._ws.send(t),1):-1},e.close=function(t,e){this._ws&&this._ws.close(t,e)},t}());e._RF.pop()}}}));

System.register("chunks:///_virtual/WxWeb.ts",["./rollupPluginModLoBabelHelpers.js","cc","./Singleton.ts","./Tool.ts","./ConstGlobal.ts"],(function(n){var o,e,r,t,i,c,a,s,u,g;return{setters:[function(n){o=n.inheritsLoose,e=n.asyncToGenerator,r=n.regeneratorRuntime},function(n){t=n.cclegacy,i=n.log,c=n.game,a=n.Game},function(n){s=n.Singleton},function(n){u=n.default},function(n){g=n.C_Runtime}],execute:function(){t._RF.push({},"1de4dgt3UVG4LNNseG2FVFJ","WxWeb",void 0);n("WxWeb",function(n){function t(){for(var o,e=arguments.length,r=new Array(e),t=0;t<e;t++)r[t]=arguments[t];return(o=n.call.apply(n,[this].concat(r))||this).core=void 0,o}o(t,n);var s=t.prototype;return s.init=function(){var n=e(r().mark((function n(o){var e=this;return r().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,u.loadSDK("//res.wx.qq.com/open/js/jweixin-1.6.0.js");case 2:console.log("wx sdk init"),this.core=o,wx.error((function(n){i("wxerr",n)})),"miniprogram"===window.__wxjs_environment&&(this.core.runtime=g.program_wx),wx.miniProgram.getEnv((function(n){n.miniprogram&&(e.core.runtime=g.program_wx)})),window.WeixinJSBridge&&WeixinJSBridge.on("onPageStateChange",(function(n){"true"===n.active||!0===n.active?c.emit(a.EVENT_SHOW):c.emit(a.EVENT_HIDE)}));case 8:case"end":return n.stop()}}),n,this)})));return function(o){return n.apply(this,arguments)}}(),s.wxJsApiConfig=function(){var n=e(r().mark((function n(o,e,t){var i,c;return r().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:void 0===e&&(e=!1),wx.ready((function(){console.log("wxready"),t&&t()})),i=["scanQRCode"],c={debug:e,appId:o.appId,timestamp:o.timestamp,nonceStr:o.nonceStr,signature:o.signature,jsApiList:i},console.log("wxinitInfo",c),wx.config(c);case 6:case"end":return n.stop()}}),n)})));return function(o,e,r){return n.apply(this,arguments)}}(),s.wxScanQRCode=function(n){var o=this;return void 0===n&&(n=0),new Promise((function(e,r){o.core.runtime!=g.program_wx&&r("请在小程序内进行尝试");try{wx.scanQRCode({needResult:n,scanType:["qrCode","barCode"],success:function(n){e(n.resultStr)}})}catch(n){r(n)}}))},s.navigateTo=function(n){this.core.runtime==g.program_wx?(console.log(n),wx.miniProgram.navigateTo(n)):console.log("请在小程序内进行尝试",n.url)},s.navigateBack=function(){this.core.runtime==g.program_wx?wx.miniProgram.navigateBack():console.log("请在小程序内进行尝试")},s.switchTab=function(n){this.core.runtime==g.program_wx?(console.log(n),wx.miniProgram.switchTab(n)):console.log("请在小程序内进行尝试")},s.reLaunch=function(n){this.core.runtime==g.program_wx?(console.log(n),wx.miniProgram.reLaunch(n)):console.log("请在小程序内进行尝试")},s.redirectTo=function(n){this.core.runtime==g.program_wx?(console.log(n),wx.miniProgram.redirectTo(n)):console.log("请在小程序内进行尝试")},s.postMessage=function(n){this.core.runtime==g.program_wx?wx.miniProgram.postMessage(n):console.log("请在小程序内进行尝试")},s.closeWindow=function(){wx&&wx.closeWindow()},t}(s));t._RF.pop()}}}));

System.register("chunks:///_virtual/xcore.ts",["cc","./EventManager2.ts","./ResManager.ts","./Logger.ts","./HttpRequest.ts","./ViewManager.ts","./WxWeb.ts","./Data.ts"],(function(e){var t,n,o,a,i,c,s,r;return{setters:[function(e){t=e.cclegacy},function(e){n=e.EventManager},function(e){o=e.ResManager},function(e){a=e.Logger},function(e){i=e.HttpRequest},function(e){c=e.ViewManager},function(e){s=e.WxWeb},function(e){r=e.default}],execute:function(){t._RF.push({},"90e73vUG/pJv5bO5RJ2bNOE","xcore",void 0);e("gameVersion","1.2.1");var g=e("xcore",(function(){}));g.env=void 0,g.runtime=void 0,g.log=a,g.ndUI=void 0,g.ndGame=void 0,g.event=n.getInstance(),g.res=o.getInstance(),g.ui=c.getInstance(),g.wx=s.getInstance(),g.http=i.getInstance(),g.channel=void 0,g.sound=void 0,g.timer=void 0,g.storage=void 0,g.gameData=r,g.config=void 0,t._RF.pop()}}}));

(function(r) {
  r('virtual:///prerequisite-imports/main', 'chunks:///_virtual/main'); 
})(function(mid, cid) {
    System.register(mid, [cid], function (_export, _context) {
    return {
        setters: [function(_m) {
            var _exportObj = {};

            for (var _key in _m) {
              if (_key !== "default" && _key !== "__esModule") _exportObj[_key] = _m[_key];
            }
      
            _export(_exportObj);
        }],
        execute: function () { }
    };
    });
});