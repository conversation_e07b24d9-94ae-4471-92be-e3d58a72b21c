export class Singleton {
    // 实例
    private static _instance: Singleton;
    // 是否是通过getInstance实例化
    private static _isGetByInstance: boolean = false;

    /**
     * 获取实例
     */
    public static getInstance<T extends Singleton>(this: (new () => T) | typeof Singleton): T {
        const _class = this as typeof Singleton;
        if (!_class._instance) {
            _class._isGetByInstance = true;
            _class._instance = new _class();
            _class._isGetByInstance = false;
        }
        return _class._instance as T;
    }

    /**
     * 构造函数
     * @protected
     */
    protected constructor() {
        if (!(this.constructor as typeof Singleton)._isGetByInstance) {
            throw new Error("Singleton class can't be instantiated more than once.");
        }
    }
}