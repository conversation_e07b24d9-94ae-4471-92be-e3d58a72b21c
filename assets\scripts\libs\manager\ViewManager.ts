import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Node, Prefab, <PERSON><PERSON><PERSON><PERSON>, Sprite, TweenSystem, _decorator, director, instantiate, log, sp, tween, v3, warn } from "cc";
import { ViewBase } from "./ViewBase";

import { EventManager } from "./EventManager";

import ResLoader from "./ResLoader";


import { Singleton } from "../utils/Singleton";
import Tool from "../utils/Tool";
import { xcore } from "../xcore";
import { Queue } from "../utils/Queue";
import { E_EVENT, E_UILAYER, IUIConfigClass } from "../../ConstGlobal";
import { UILoading } from "../../../resources/prefab/UI/UILoading";
import { UILoadingProgress } from "../../../resources/prefab/UI/UILoadingProgress";





export class ViewManager extends Singleton {

    //界面弹窗配置文件
    private _viewconfigs: [IUIConfigClass] = null;

    private _sceneName: string

    /***************************界面ui******************************/

    private _uiCacheMap: Map<string, Node> = new Map();

    private _viewQueue: Queue<Function> = new Queue(new Function)
    /***************************弹窗*********************************/
    /**缓存 所有实例化后未销毁的弹窗节点 包括显示和隐藏的弹窗*/
    private _viewCacheList: ViewBase[] = [];
    /**激活中的弹窗配置 显示中的弹窗 */
    private _activeViewConfigs: IUIConfigClass[] = [];
    /**弹窗配置队列 还未实例化的弹窗 存储配置信息排队等待实例化 */
    private _viewConfigGroup: IUIConfigClass[] = [];

    private _popTimer: any = null;

    constructor() {
        super();


        EventManager.getInstance().addEventListener(E_EVENT.UiClose, this.onViewClose, this);

    }

    initViewConfig() {
        ResLoader.load('viewconfig', JsonAsset).then(res => {
            this._viewconfigs = res.json;
            log("_viewconfigs", this._viewconfigs)
        })
    }

    //弹窗关闭回调
    onViewClose(event: string, viewName: string) {
        log("onViewClose", viewName)
        this.checkPop();
    }

    //加入队列操作
    async addQueue(qFunc: Function) {
        return new Promise((resolve, reject) => {
            this._viewQueue.enqueue(qFunc.bind(this), async (targetQueue: Queue<Function>) => {
                while (!targetQueue.isEmpty()) {
                    let func = targetQueue.dequeue();
                    await func();
                }
                resolve('')
            })
        })

    }


    /**
     * 添加弹窗队列 
     * @param config 弹窗配置|弹窗名称
     * @param priority //0->马上展示  大于零->优先级显示 1优先级最高
     * @returns 
     */
    async addView(viewname: string, data: any = null, priority: number = 0, openFinishCb?: Function) {
        let UIConfig = this._viewconfigs ? this._viewconfigs[viewname] : null;

        if (!UIConfig) {
            warn('UIConfig can not be null ！', viewname);
            return
        }
        if (this._viewConfigGroup.filter(e => e.viewName === UIConfig.viewName).length > 0) {
            warn('_viewConfigGroup same view ！' + UIConfig.viewName);
            return
        } /* else if (this._activeViewConfigs.filter(e => e.viewName === UIConfig.viewName && UIConfig.viewName != 'ViewCommonTips').length > 0) {
            warn('_activeView same view ！' + UIConfig.viewName);
            return
        } */
        UIConfig.priority = priority;
        UIConfig.data = data;
        if (openFinishCb) {
            UIConfig.openCb = openFinishCb
        }
        //优先弹出弹窗
        if (!UIConfig.priority || UIConfig.priority <= 0) {
            this.showView(UIConfig);
        }
        //排队弹出弹窗
        else {
            this._viewConfigGroup.push(UIConfig);
            this.checkPop(0.01);
        }
    }


    checkPop(delay: number = 0) {
        if (this._activeViewConfigs.length <= 0) {
            this._popTimer && clearTimeout(this._popTimer);
            this._popTimer = setTimeout(() => {
                if (this._viewConfigGroup.length > 0) {
                    this._viewConfigGroup.sort((a, b) => b.priority - a.priority);
                    let uiconfig = this._viewConfigGroup.pop();
                    this.showView(uiconfig);
                }
            }, delay);
        }

    }

    //获取节点弹窗
    async showView(UIConfig: IUIConfigClass) {
        if (!UIConfig) {
            return
        }
        this._activeViewConfigs.push(UIConfig);
        if (UIConfig.isShowLoading) {
            //显示loading转圈效果
            //  await this.showLoading();
        }
        let data = UIConfig.data;


        let ui = this.getView(UIConfig.viewName);
        if (ui) {
            ui.node.active = true;
            ui.onShow();
            if (UIConfig.openCb) {
                UIConfig.openCb();
                UIConfig.openCb = null;
            }
            if (data != null && data != undefined) {
                log('setData:', data)
                ui.setData(data)
            }
            return
        }
        try {
            let bundleName = UIConfig.bundleName;
            let path = UIConfig.path;
            let res = await xcore.res.bundleLoadPrefab(bundleName, `${path}`) as Prefab
            let newNode = instantiate(res);
            let ndParent = director.getScene().getChildByName("Canvas");
            if (!ndParent) {
                warn('find uiparnet node err => Canvas no find!')
            }
            newNode.parent = ndParent;
            let newui = newNode.getComponent(newNode.name) as ViewBase;
            newui._viewName = UIConfig.viewName;
            newui.closeSelf = (isFastClose: boolean = false) => {
                newui.onBeforeClose();
                this.closeView(UIConfig.viewName, isFastClose);
            };
            newui.onShow();
            if (UIConfig.openCb) {
                UIConfig.openCb();
                UIConfig.openCb = null;
            }
            //  this.hideLoading();
            this._viewCacheList.push(newui);
            Tool.setChildrenNodeSortByPriority(newNode, E_UILAYER.VIEW_LAYER);

            if (data != null && data != undefined) {
                log('setData:', data)
                newui.setData(data)

            }
        } catch (err) {

            log(err)
            //  this.hideLoading();
            this._activeViewConfigs.pop();
            this.checkPop();

        }



    }
    releaseAsset(view: ViewBase) {

        xcore.res.releaseAsset(view.node[`_prefab`].asset);
        view.releaseAssets();
    }
    //销毁节点弹窗
    public async closeView(viewName: string, isFastClose: boolean = false) {

        for (let i = 0; i < this._viewCacheList.length; ++i) {
            let view = this._viewCacheList[i]
            if (view._viewName === viewName) {
                await this.addQueue(async () => {
                    if (view && view.node && view.node.isValid) {
                        (!isFastClose && view.node.active) && await view.hideAnim();
                        this.releaseAsset(view);
                        view.node.destroy();
                        this._viewCacheList.splice(i, 1);

                        //清除激活列表中该界面
                        for (let j = 0; j < this._activeViewConfigs.length; j++) {
                            if (this._activeViewConfigs[j].viewName === viewName) {
                                this._activeViewConfigs.splice(j, 1);
                            }
                        }
                        EventManager.getInstance().raiseEvent(E_EVENT.UiClose, viewName);
                        log("hideFinish")
                    }

                })
                break;
            }
        }

        // return new Promise(async (resolve, reject) => {

        //     for (let i = 0; i < this._viewCacheList.length; ++i) {
        //         let view = this._viewCacheList[i]
        //         if (view._viewName === viewName) {
        //             (!isFastClose && view.node.active) && await view.hideAnim();
        //             xcore.res.releaseAsset(view.node[`_prefab`].asset);
        //             view.node.destroy();
        //             this._viewCacheList.splice(i, 1);

        //             //清除激活列表中该界面
        //             for (let j = 0; j < this._activeViewConfigs.length; j++) {
        //                 if (this._activeViewConfigs[j].viewName === viewName) {
        //                     this._activeViewConfigs.splice(j, 1);
        //                 }
        //             }


        //             EventManager.getInstance().raiseEvent(E_EVENT.UiClose, viewName);

        //             break;
        //         }
        //     }

        //     resolve('')
        // })

    }
    //隐藏节点弹窗
    public async hideView(viewName: string) {
        let ui = this.getView(viewName);
        if (ui) {
            await ui.hideAnim()
            ui.node.active = false;
            EventManager.getInstance().raiseEvent(E_EVENT.UiClose);
            for (let i = 0; i < this._activeViewConfigs.length; i++) {
                if (this._activeViewConfigs[i].viewName === ui._viewName) {
                    // this._activeViewConfigs.splice(i, 1);

                    this.addQueue(() => this._activeViewConfigs.splice(i, 1))
                }
            }
        }
    }
    //获取节点弹窗
    public getView(viewName: string): ViewBase {
        for (let i = 0; i < this._viewCacheList.length; ++i) {
            if (this._viewCacheList[i]._viewName === viewName) {
                return this._viewCacheList[i];
            }
        }
        return null;
    }

    //关闭并销毁所有弹窗
    public async closeAllView(isFastClose: boolean = false) {
        let viewnames = [];
        for (let i = 0; i < this._viewCacheList.length; ++i) {
            viewnames.push(this._viewCacheList[i]._viewName);
        }
        for (let j = 0; j < viewnames.length; j++) {
            await this.closeView(viewnames[j], isFastClose);
        }
        viewnames = null;
        this._viewCacheList = [];
        this._viewConfigGroup = [];
        this._activeViewConfigs = [];
    }

    //切换场景
    async switchScene(bundleName: string, sceneName: string, cb?: Function) {
        this._popTimer && clearTimeout(this._popTimer);
        if (this._sceneName != sceneName) {

            await this.closeAllView();
            this._uiCacheMap.forEach((nd, key) => {
                this.closeUI(key);
            })

        }
        this._sceneName = sceneName;
        if (!bundleName) {
            director.loadScene(sceneName, () => {
                cb && cb()
            })
            return
        }

        let bundle = await ResLoader.getBundle(bundleName);
        bundle.loadScene(sceneName, () => {
            director.loadScene(sceneName, () => {
                cb && cb()
            })
        })
    }

    //设置ui
    async setUI(bundleName: string, path: string): Promise<Node> {
        let pfbUI = await xcore.res.bundleLoadPrefab(bundleName, path);
        let ui = instantiate(pfbUI);
        let ndParent = director.getScene().getChildByName("Canvas");
        if (!ndParent) {
            warn('find uiparnet node err => Canvas no find!')
        }
        ui.parent = ndParent;
        Tool.setChildrenNodeSortByPriority(ui, E_UILAYER.UI_LAYER)
        this._uiCacheMap.set(ui.name, ui);

        return ui
    }

    //关闭ui
    closeUI(ui: string) {
        let ndUI = this._uiCacheMap.get(ui);
        if (!ndUI) {
            return
        }
        this._uiCacheMap.delete(ui);
        if (ndUI) {
            if (ndUI[`_prefab`]) {
                xcore.res.releaseAsset(ndUI[`_prefab`].asset);
            }
            ndUI.destroy();
            ndUI = null;
        }




    }


    async showToast(txt: string) {
        let toastName = 'ViewToast';
        let ui = this._uiCacheMap.get(toastName);
        if (!ui) {
            let config = this._viewconfigs ? this._viewconfigs[toastName] : null;
            if (!config) {
                warn('ViewToast can not be null ！');
                return
            }
            let pfbUI = await xcore.res.bundleLoadPrefab(config.bundleName, config.path);
            ui = instantiate(pfbUI);
            this._uiCacheMap.set(ui.name, ui);
            let ndParent = director.getScene().getChildByName("Canvas");
            if (!ndParent) {
                warn('uiparnet Canvas no find!')
            }
            ui.parent = ndParent;
        }
        Tool.setChildrenNodeSortByPriority(ui, E_UILAYER.TOAST_LAVER);
        ui.getChildByName('lbToast').getComponent(RichText).string = txt;
        ui.setPosition(v3(0, 0));
        ui.active = true;
        TweenSystem.instance.ActionManager.removeAllActionsFromTarget(ui);
        tween(ui)
            .to(0.7, { position: v3(0, 120) }, { easing: 'cubicInOut' })
            .delay(0.35)
            .call(() => {
                ui.active = false;
            }).start()

    }


    async showLoadingUI(loadingResConfig: any, onLoginFinish: () => {}): Promise<Function> {

        try {
            await xcore.ui.setUI('resources', 'prefab/UI/UILoading').then((ui) => {
                ui.getComponent(UILoading).initUI(loadingResConfig.resLoading);
            })
            let ui = await xcore.ui.setUI('resources', 'prefab/UI/UILoadingProgress');
            return ui.getComponent(UILoadingProgress).initUI(['加载中', '加载中.', '加载中..', '加载中...'], onLoginFinish, loadingResConfig.resLoadingPgs);

        } catch (err) {
            xcore.ui.showToast('ui初始化错误');
        }
    }
}