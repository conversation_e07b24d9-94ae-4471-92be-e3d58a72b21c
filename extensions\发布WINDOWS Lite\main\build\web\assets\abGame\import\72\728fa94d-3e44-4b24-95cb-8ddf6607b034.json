[1, ["f5UWUIc9VPr5nlR36t4bo/@6c48a", "8d4WDWTyVI1K0wXbMwYN+7@6c48a", "7ej/d4RMtNvqOTzL/TSDlx@6c48a"], 0, [["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3]], [[0, 0, 1, 2, 3, 4, 5]], [[0, "<PERSON><PERSON><PERSON><PERSON>", "\nhunyuansan.png\nsize: 1941,2046\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\n混元伞_00000\n  rotate: true\n  xy: 2, 1653\n  size: 391, 404\n  orig: 540, 540\n  offset: 64, 88\n  index: -1\n混元伞_00004\n  rotate: true\n  xy: 408, 1630\n  size: 414, 479\n  orig: 540, 540\n  offset: 62, 13\n  index: -1\n混元伞_00008\n  rotate: false\n  xy: 1427, 1544\n  size: 512, 500\n  orig: 540, 540\n  offset: 8, 0\n  index: -1\n混元伞_00012\n  rotate: true\n  xy: 2, 79\n  size: 528, 526\n  orig: 540, 540\n  offset: 12, 14\n  index: -1\n混元伞_00064\n  rotate: true\n  xy: 530, 2\n  size: 526, 532\n  orig: 540, 540\n  offset: 14, 0\n  index: -1\n混元伞_00072\n  rotate: false\n  xy: 1080, 1034\n  size: 524, 508\n  orig: 540, 540\n  offset: 16, 24\n  index: -1\n混元伞_00076\n  rotate: true\n  xy: 2, 609\n  size: 513, 526\n  orig: 540, 540\n  offset: 23, 0\n  index: -1\n混元伞_00084\n  rotate: true\n  xy: 1067, 512\n  size: 520, 540\n  orig: 540, 540\n  offset: 20, 0\n  index: -1\n混元伞_00092\n  rotate: false\n  xy: 2, 1124\n  size: 540, 504\n  orig: 540, 540\n  offset: 0, 20\n  index: -1\n混元伞_00096\n  rotate: false\n  xy: 544, 1048\n  size: 534, 506\n  orig: 540, 540\n  offset: 6, 20\n  index: -1\n混元伞_00100\n  rotate: true\n  xy: 530, 530\n  size: 516, 535\n  orig: 540, 540\n  offset: 20, 1\n  index: -1\n混元伞_00124\n  rotate: true\n  xy: 889, 1556\n  size: 488, 536\n  orig: 540, 540\n  offset: 32, 4\n  index: -1\n\nhunyuansan2.png\nsize: 1628,1603\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\n混元伞_00032\n  rotate: true\n  xy: 2, 1076\n  size: 525, 540\n  orig: 540, 540\n  offset: 4, 0\n  index: -1\n混元伞_00036\n  rotate: true\n  xy: 544, 1074\n  size: 527, 540\n  orig: 540, 540\n  offset: 13, 0\n  index: -1\n混元伞_00044\n  rotate: false\n  xy: 544, 540\n  size: 540, 532\n  orig: 540, 540\n  offset: 0, 0\n  index: -1\n混元伞_00048\n  rotate: false\n  xy: 1086, 539\n  size: 540, 532\n  orig: 540, 540\n  offset: 0, 0\n  index: -1\n混元伞_00060\n  rotate: false\n  xy: 2, 12\n  size: 540, 532\n  orig: 540, 540\n  offset: 0, 0\n  index: -1\n混元伞_00068\n  rotate: true\n  xy: 1086, 1073\n  size: 528, 532\n  orig: 540, 540\n  offset: 12, 0\n  index: -1\n混元伞_00088\n  rotate: false\n  xy: 2, 546\n  size: 540, 528\n  orig: 540, 540\n  offset: 0, 0\n  index: -1\n混元伞_00108\n  rotate: true\n  xy: 544, 6\n  size: 532, 540\n  orig: 540, 540\n  offset: 8, 0\n  index: -1\n混元伞_00120\n  rotate: false\n  xy: 1086, 2\n  size: 540, 535\n  orig: 540, 540\n  offset: 0, 5\n  index: -1\n\nhunyuansan3.png\nsize: 1624,1628\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\n混元伞_00020\n  rotate: false\n  xy: 1082, 4\n  size: 536, 538\n  orig: 540, 540\n  offset: 4, 2\n  index: -1\n混元伞_00024\n  rotate: false\n  xy: 2, 1086\n  size: 540, 540\n  orig: 540, 540\n  offset: 0, 0\n  index: -1\n混元伞_00056\n  rotate: false\n  xy: 1086, 1086\n  size: 536, 540\n  orig: 540, 540\n  offset: 0, 0\n  index: -1\n混元伞_00080\n  rotate: false\n  xy: 2, 544\n  size: 540, 540\n  orig: 540, 540\n  offset: 0, 0\n  index: -1\n混元伞_00104\n  rotate: false\n  xy: 544, 2\n  size: 536, 540\n  orig: 540, 540\n  offset: 0, 0\n  index: -1\n混元伞_00112\n  rotate: false\n  xy: 544, 1086\n  size: 540, 540\n  orig: 540, 540\n  offset: 0, 0\n  index: -1\n混元伞_00116\n  rotate: false\n  xy: 2, 2\n  size: 540, 540\n  orig: 540, 540\n  offset: 0, 0\n  index: -1\n混元伞_00128\n  rotate: true\n  xy: 1086, 544\n  size: 540, 536\n  orig: 540, 540\n  offset: 0, 4\n  index: -1\n混元伞_00132\n  rotate: false\n  xy: 544, 544\n  size: 540, 540\n  orig: 540, 540\n  offset: 0, 0\n  index: -1\n", ["hunyuansan.png", "hunyuansan2.png", "hunyuansan3.png"], {"skeleton": {"hash": "tsIE0kniUsPrCCEXdGk4sVjMo0c", "spine": "3.8.97", "x": -800, "y": -960, "width": 1600, "height": 1920, "images": "", "audio": ""}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "scaleX": 2, "scaleY": 2}], "slots": [{"name": "女娲石_00000", "bone": "bone", "attachment": "混元伞_00132"}], "skins": [{"name": "default", "attachments": {"女娲石_00000": {"混元伞_00000": {"width": 540, "height": 540}, "混元伞_00004": {"width": 540, "height": 540}, "混元伞_00008": {"width": 540, "height": 540}, "混元伞_00012": {"width": 540, "height": 540}, "混元伞_00020": {"width": 540, "height": 540}, "混元伞_00024": {"width": 540, "height": 540}, "混元伞_00032": {"width": 540, "height": 540}, "混元伞_00036": {"width": 540, "height": 540}, "混元伞_00044": {"width": 540, "height": 540}, "混元伞_00048": {"width": 540, "height": 540}, "混元伞_00056": {"width": 540, "height": 540}, "混元伞_00060": {"width": 540, "height": 540}, "混元伞_00064": {"width": 540, "height": 540}, "混元伞_00068": {"width": 540, "height": 540}, "混元伞_00072": {"width": 540, "height": 540}, "混元伞_00076": {"width": 540, "height": 540}, "混元伞_00080": {"width": 540, "height": 540}, "混元伞_00084": {"width": 540, "height": 540}, "混元伞_00088": {"width": 540, "height": 540}, "混元伞_00092": {"width": 540, "height": 540}, "混元伞_00096": {"width": 540, "height": 540}, "混元伞_00100": {"width": 540, "height": 540}, "混元伞_00104": {"width": 540, "height": 540}, "混元伞_00108": {"width": 540, "height": 540}, "混元伞_00112": {"width": 540, "height": 540}, "混元伞_00116": {"width": 540, "height": 540}, "混元伞_00120": {"width": 540, "height": 540}, "混元伞_00124": {"width": 540, "height": 540}, "混元伞_00128": {"width": 540, "height": 540}, "混元伞_00132": {"width": 540, "height": 540}}}}], "events": {"event_hit": {}}, "animations": {"animation": {"slots": {"女娲石_00000": {"color": [{"time": 1.4333, "color": "ffffffff"}, {"time": 1.6667, "color": "ffffff00"}], "attachment": [{"name": "混元伞_00000"}, {"time": 0.0667, "name": "混元伞_00004"}, {"time": 0.1, "name": "混元伞_00008"}, {"time": 0.1667, "name": "混元伞_00012"}, {"time": 0.2333, "name": "混元伞_00020"}, {"time": 0.3, "name": "混元伞_00024"}, {"time": 0.3333, "name": "混元伞_00032"}, {"time": 0.4, "name": "混元伞_00036"}, {"time": 0.4667, "name": "混元伞_00044"}, {"time": 0.5333, "name": "混元伞_00048"}, {"time": 0.5667, "name": "混元伞_00056"}, {"time": 0.6333, "name": "混元伞_00060"}, {"time": 0.7, "name": "混元伞_00064"}, {"time": 0.7333, "name": "混元伞_00068"}, {"time": 0.8, "name": "混元伞_00072"}, {"time": 0.8667, "name": "混元伞_00076"}, {"time": 0.9333, "name": "混元伞_00080"}, {"time": 0.9667, "name": "混元伞_00084"}, {"time": 1.0333, "name": "混元伞_00088"}, {"time": 1.1, "name": "混元伞_00092"}, {"time": 1.1333, "name": "混元伞_00096"}, {"time": 1.2, "name": "混元伞_00100"}, {"time": 1.2667, "name": "混元伞_00104"}, {"time": 1.3333, "name": "混元伞_00108"}, {"time": 1.3667, "name": "混元伞_00112"}, {"time": 1.4333, "name": "混元伞_00116"}, {"time": 1.5, "name": "混元伞_00120"}, {"time": 1.5667, "name": "混元伞_00124"}, {"time": 1.6, "name": "混元伞_00128"}, {"time": 1.6667, "name": "混元伞_00132"}]}}}}}, [0, 1, 2]]], 0, 0, [0, 0, 0], [-1, -2, -3], [0, 1, 2]]