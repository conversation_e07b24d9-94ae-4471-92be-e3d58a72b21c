import MessageCtrl from '../MessageCtrl'
import { MessageCmdEvent, MessageUserSubCmd } from '../MessageEvent'

import user from '../../../protos/user.js'
import { _decorator } from 'cc'

const { ccclass } = _decorator
/**
 * 登录
 */
@ccclass()
export default class CmdLogin extends MessageCtrl {
	////////////////////消息单例////////////////////
	public static get inst() {
		return this.getInst(CmdLogin)
	}

	constructor() {
		super()
		this.cmd = MessageCmdEvent.userCmd
		this.subCmd = MessageUserSubCmd.login
		this.addListen()
	}

	////////////////////End////////////////////

	async sendSync(msg: user.pb.UserLoginReqPb) {
		let msgE = user.pb.UserLoginReqPb.encode(msg);
		console.log('decode::::::' , user.pb.UserLoginReqPb.decode(msgE.finish()) )
		return await super.sendSync<user.pb.UserLoginResPb>(msgE.finish())
	}

	receive(code: number, data: Uint8Array) {
		try {
			console.log('cmdlogin receive1:',data)
			let info = user.pb.UserLoginResPb.decode(data)
			console.log('cmdlogin receive2::',info)
			super.receive(code, info)
		} catch (e) {
			super.receive(code, e)
		}
	}
}
