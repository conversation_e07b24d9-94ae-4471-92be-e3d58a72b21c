[1, ["b7MFJ8MjNBwqr3fNq1j5dJ@6c48a", "b7MFJ8MjNBwqr3fNq1j5dJ@f9941"], ["node", "_textureSource", "root", "data", "_spriteFrame"], [["cc.Node", ["_name", "_layer", "_components", "_prefab", "_children", "_parent"], 1, 9, 4, 2, 1], ["cc.UITransform", ["node", "__prefab", "_contentSize"], 3, 1, 4, 5], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.Sprite", ["_type", "_sizeMode", "node", "__prefab", "_color", "_spriteFrame"], 1, 1, 4, 5, 6], ["cc.RichText", ["_lineHeight", "_string", "_horizontalAlign", "_verticalAlign", "_fontSize", "_maxWidth", "node", "__prefab"], -3, 1, 4]], [[4, 0, 2], [5, 0, 1, 2, 3, 4, 5, 5], [0, 0, 1, 5, 2, 3, 3], [1, 0, 1, 2, 1], [3, 0, 2], [0, 0, 1, 4, 2, 3, 3], [1, 0, 1, 1], [6, 0, 1, 2, 3, 4, 5, 3], [7, 0, 1, 2, 3, 4, 5, 6, 7, 7]], [[[{"name": "default_panel", "rect": {"x": 0, "y": 0, "width": 20, "height": 20}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 20, "height": 20}, "rotated": false, "capInsets": [8, 8, 8, 8], "vertices": {"rawPosition": [-10, -10, 0, 10, -10, 0, -10, 10, 0, 10, 10, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 20, 20, 20, 0, 0, 20, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -10, "y": -10, "z": 0}, "maxPos": {"x": 10, "y": 10, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [2], 0, [0], [1], [0]], [[[4, "ViewToast"], [5, "ViewToast", 33554432, [-3, -4], [[6, -2, [0, "f5ZcK32JFH14SMrLbhgLDC"]]], [1, "b1LTNtx9BL86UJzl6w4z0c", null, null, null, -1, 0]], [2, "sprFrame", 33554432, 1, [[3, -5, [0, "d5lfXTckFGVaiute27s9sc"], [5, 500, 80]], [7, 1, 0, -6, [0, "b7eNqg6vpFDZRDq2yIhHm6"], [4, 3577231416], 0]], [1, "b4wXaKnGxOPLR4SAfcuazN", null, null, null, 1, 0]], [2, "lbToast", 33554432, 1, [[3, -7, [0, "1dqf2UY6tBz5Fe6VYzotZJ"], [5, 400, 37.8]], [8, 30, "<color=#FFFFFF>测试提示测试提示</color>", 1, 1, 24, 400, -8, [0, "8dsJUtIUpHT61jJ+j6+sJf"]]], [1, "42/v68S6RNY5ttfuMC78Cm", null, null, null, 1, 0]]], 0, [0, 2, 1, 0, 0, 1, 0, -1, 2, 0, -2, 3, 0, 0, 2, 0, 0, 2, 0, 0, 3, 0, 0, 3, 0, 3, 1, 8], [0], [4], [1]]]]