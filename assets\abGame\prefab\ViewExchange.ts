import { _decorator, Component, Node } from 'cc';
import { ViewBase } from '../../scripts/libs/manager/ViewBase';
import ScrollViewProCom from '../../scripts/libs/utils/ScrollViewProCom';
import { ConfigHelper } from '../../scripts/config/ConfigHelper';
import { UnitExchange } from './Unit/UnitExchange';
const { ccclass, property } = _decorator;

@ccclass('ViewExchange')
export class ViewExchange extends ViewBase {
    @property(ScrollViewProCom)
    private svContent: ScrollViewProCom = null;



    protected onLoadCompleted(): void {
        let jsonId = '470001'// ConfigHelper.getInstance().getExchangeTypeConfigs()[0].jsonId;
        let configs = ConfigHelper.getInstance().getExchangeConfigsByJsonId(jsonId);
        this.svContent.setView(configs, (n: Node, data: any, index: number) => {
            let comp = n.getComponent(UnitExchange);
            if (comp) {
                comp.setData(data);
            }
        })
    }
}


