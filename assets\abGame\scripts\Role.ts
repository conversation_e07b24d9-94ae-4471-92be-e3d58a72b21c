import { instantiate, log, Node, v2, v3, Vec2, Vec3, view, warn } from "cc";
import { Buff } from "./Buff";
import { RoleData } from "./RoleData";
import { Skill } from "./Skill";

import { C_Bundle, C_GiftSkill, C_View, E_BuffType, E_EVENT, E_GiftMessageType, E_MonsterTag, E_MonsterType, E_RoleState, E_RoleType, E_SkillType, E_SkinType, E_TowerPointType } from "../../scripts/ConstGlobal";
import { UnitRoleComp } from "../prefab/Unit/UnitRoleComp";
import { xcore } from "../../scripts/libs/xcore";
import { FightMgr } from "./FightMgr";

import Tool from "../../scripts/libs/utils/Tool";
import { DEBUG } from "cc/env";
import { UnitBuffCoin } from "../prefab/Unit/UnitBuffCoin";

import { UnitTowerPoint } from "../prefab/Unit/UnitTowerPoint";
import { ConfigHelper } from "../../scripts/config/ConfigHelper";
import Net from "../../scripts/Net";
import TimeUtil from "../../scripts/libs/utils/TimeUtil";

/**角色对象 包括塔防 仙族 妖族（及妖族出怪点）
 * 对象逻辑
 * 对象数值
 * 对象技能
 * 对象buff
 * 对象渲染组件
 * 
*/
export class Role {

    /**属性值 */
    data!: RoleData

    /**表现层组件 */
    comp!: UnitRoleComp | UnitTowerPoint


    /**技能  */
    skills: Map<string, Skill> = new Map() //Collection<string, Skill> = new Collection();


    buffCons: Map<string, UnitBuffCoin> = new Map() // Collection<string, UnitBuffCoin> = new Collection();


    /**缓存塔位置 */
    private towerPosY: number



    /**状态切换计时器 */
    private timeoutBackState;

    /**是否加入攻击范围内 */
    private isInAttackDistance: boolean = false;

    private atkTime: number = 0;
    private skillAnimCb: Function = null;

    private needSwitchSkinId: E_SkinType = null;

    private tempTime: number = 1

    private tempAddTowerHp: number = 0


    /**初始化角色 */
    async add(userId: string, type: E_RoleType, monsterType?: E_MonsterType | E_TowerPointType, lev: number = 1, pos?: Vec2, nickName?: string, avatar?: string, monsterJsonId?: string) {
        let width = view.getVisibleSize().width / 2 - 60;
        let height = view.getVisibleSize().height / 2 - 100;
        !this.data && (this.data = new RoleData());
        this.towerPosY = FightMgr.getInstance().towerParentNd.position.y - 160;

        // 初始化属性
        this.data.tickTime = 0;
        this.data.userId = userId;
        this.data.nickName = nickName;
        this.data.iconUrl = avatar;
        this.data.type = type;
        this.setState(E_RoleState.Idle);
        this.data.lev = lev;
        this.data.skillWeakIds = [];
        this.data.isInvincible = false;
        this.isInAttackDistance = false;
        this.data.isLongMonster = false;
        this.data.moveLeft = FightMgr.getInstance().monsterSpace.left;
        this.data.moveRight = FightMgr.getInstance().monsterSpace.right;
        this.tempTime = 0;
        this.data.defenseNum = 0;
        if (!this.data.atkOffsetPos) {
            this.data.atkOffsetPos = v2(0, 0);
        } else {
            this.data.atkOffsetPos.set(0, 0);
        }
        if (!this.data.dimonEffects) {
            this.data.dimonEffects = {
                /**攻击加成 */
                attack: 0,
                attackLev: 0,
                attackDmNum: 0,
                /**攻速加成 */
                atkspeed: 0,
                atkspeedLev: 0,
                atkspeedDmNum: 0,
                /**技能cd加成 */
                skillcd: 0,
                skillcdLev: 0,
                skillcdDmNum: 0,
                /**暴击率 */
                atkr: 0,
                atkrLev: 0,
                /**暴击伤害 */
                atkm: 0,
                atkmLev: 0,
                atkmDmNum: 0,
                /**塔血加成 */
                hp: 0,
                hpLev: 0,
                hpDmNum: 0,
                baseAtkM: 1
            };
        }

        //怪物出生点
        if (type == E_RoleType.MonsterPoint) {
            let xRan = -width + (width * 2 * Math.random());
            this.data.pos = v2(xRan, - height + 80);
            this.comp = await this.createRoleComp(type);
            this.comp.init(this);
            this.comp.setPosAtonce(new Vec2(this.data.pos.x, this.data.pos.y - 200));


        }

        //防御塔
        else if (type == E_RoleType.Tower) {

            this.comp = await this.createRoleComp(type);
            this.comp.init(this);
            this.tempAddTowerHp = 0;
            /*  this.data.maxHp = towerHp;
             this.data.hpNum = this.data.maxHp;
             this.comp.setHp(this.data.hpNum); */
            log('init tower')
        }

        //塔防炮台
        else if (type == E_RoleType.TowerPoint) {
            let config;
            if (lev) {
                config = ConfigHelper.getInstance().getTowerPointByType(monsterType, lev)
            } else if (monsterJsonId) {
                config = ConfigHelper.getInstance().getTowerPointByJsonId(monsterJsonId);
            } else {
                warn('炮台配置错误')
                return
            }
            if (!config) {
                warn('TowerPoint config null', monsterType, lev, monsterJsonId)
                return
            }
            let effectConfig = ConfigHelper.getInstance().getEffectConfigByJsonId(config.attacKeffect);
            let animConfig = ConfigHelper.getInstance().getAnimConfigByJsonId(effectConfig.Animation);
            // log("炮台 config:", monsterType, config, effectConfig, animConfig);
            FightMgr.getInstance().towerPointUpGiftNum = config.douyinGiftNum;
            this.data.jsonId = config.jsonId;
            this.data.monsterType = monsterType;
            let atts = config.attack.split('|');
            this.data.minAtkNum = parseInt(atts[0] || 0);
            this.data.maxAtkNum = parseInt(atts[1] || 0);

            this.data.atkSpeed = config.attackCooldown;
            this.data.maxHp = config.monsterHp;
            this.data.hpNum = config.monsterHp;
            this.data.moveAnimation = config.moveAnimation;
            this.data.attackAnimation = config.attackAnimation;
            this.data.attackEffect = config.attacKeffect;
            this.data.moveSpeed = config.movementSpeed;
            this.data.attacKeffectInterval = config.attacKeffectInterval * 1000;
            this.data.monsterTag = config.monsterRefreshType;
            this.data.atkAnimJsonId = effectConfig.Animation;
            this.data.atkAnimTargetType = animConfig.target;
            this.data.atkStyle = effectConfig.type;
            this.data.monsterName = config.name;
            this.data.hurtEffect = effectConfig.impactAnimation;
            let ran = 1 + -0.2 + (0.4 * Math.random());
            this.data.atkRag = effectConfig.attacDistance * ran;
            this.data.atkPropNum = effectConfig.doubleHit;
            this.data.pos = pos;
            this.data.skillAnimation = config.skillAnimation;
            this.data.monsterScore = config.monsterScore;
            let scale = Number(config.animationScale) || 1;
            this.data.scale = v3(scale, scale, scale);

            this.data.moveLeft = pos.x - 70;
            this.data.moveRight = pos.x + 70;


            this.comp = await this.createRoleComp(type);
            this.comp.setPosAtonce(this.data.pos);
            this.comp.init(this);


            if (config.skill) {
                let skillConfig = ConfigHelper.getInstance().getSkillConfigByJsonId(config.skill);
                if (skillConfig) {
                    this.createSkill(skillConfig.type, 1);
                    log("炮台 skillId:", skillConfig, config.skill)
                } else {
                    warn('skill null', config.skill)
                }

            }


        }
        //仙族or妖族
        else {
            //仙族
            if (type == E_RoleType.Hero) {

                let config = ConfigHelper.getInstance().getHeroConfigByJsonId(this.data.skinId);

                if (!config) {
                    warn('Hero skin config null', this.data.skinId)
                    return
                }

                let effectConfig = ConfigHelper.getInstance().getEffectConfigByJsonId(config.attacKeffect);
                let animConfig = ConfigHelper.getInstance().getAnimConfigByJsonId(effectConfig.Animation)


                log("hero config:", config);
                this.data.jsonId = config.jsonId;
                let atts = config.attack.split('|');
                this.data.minAtkNum = parseInt(atts[0] || 0);
                this.data.maxAtkNum = parseInt(atts[1] || 0);

                this.data.atkSpeed = config.attackCooldown;
                this.data.moveAnimation = config.moveAnimation;
                this.data.attackAnimation = config.attackAnimation;
                this.data.attackEffect = config.attacKeffect;
                this.data.moveSpeed = config.movementSpeed;
                this.data.attacKeffectInterval = config.attacKeffectInterval * 1000;
                this.data.atkOffsetPos.set(Number(config.trajectoryX) || 0, Number(config.trajectoryY) || 0);
                this.data.atkAnimJsonId = effectConfig.Animation;
                this.data.atkAnimTargetType = animConfig.target;
                this.data.atkStyle = effectConfig.type;
                this.data.hurtEffect = effectConfig.impactAnimation;
                let scale = Number(config.animationScale) || 1;
                this.data.scale = v3(scale, scale, scale)
                this.data.dimonEffects.atkr = Number(config.critical || 0);
                this.data.dimonEffects.baseAtkM = Number(config.criticalDamage || 1);
                // log('scale:', this.data.scale, this.data.skinId)
                //获取随机坐标
                if (!pos) {
                    let ranPos = FightMgr.getInstance().getRandomPosByType(type);
                    this.data.pos = ranPos;
                } else {
                    this.data.pos = pos;
                }

                this.data.skillAnimation = config.skillAnimation;


                this.data.atkRag = effectConfig.attacDistance;
                this.data.atkPropNum = effectConfig.doubleHit;


                this.comp = await this.createRoleComp(type);
                this.comp.setPosAtonce(new Vec2(this.data.pos.x, this.data.pos.y + 200)/* this.data.pos */);
                this.comp.init(this);
                if (config.skill) {
                    // let skillConfig = ConfigHelper.getInstance().getSkillConfigByJsonId(config.skill);
                    this.createSkill(null, 1, true, config.skill, true);
                    //log("monster skillId:", skillConfig, config.skill)
                }

            }
            //妖族
            else if (type == E_RoleType.Monster) {
                let config;
                if (monsterJsonId) {
                    config = ConfigHelper.getInstance().getMonsterConfigByJsonId(monsterJsonId);
                } else {
                    config = ConfigHelper.getInstance().getMonsterConfigByType(monsterType, lev);
                }
                if (!config) {
                    warn('monster config null', monsterType, lev, monsterJsonId)
                    return
                }
                let effectConfig = ConfigHelper.getInstance().getEffectConfigByJsonId(config.attacKeffect);
                let animConfig = effectConfig ? ConfigHelper.getInstance().getAnimConfigByJsonId(effectConfig.Animation) : null;
                if (!effectConfig) {
                    log("effectConfig null", config.attacKeffect, monsterType, monsterJsonId)
                }
                let modeNum = [1, 2, 4][xcore.gameData.gameMode] || 1;
                this.data.jsonId = config.jsonId;
                this.data.monsterType = monsterType || config.monsterType;
                let atts = config.attack.split('|');
                this.data.minAtkNum = parseInt(atts[0] || 0) * modeNum;
                this.data.maxAtkNum = parseInt(atts[1] || 0) * modeNum;
                this.data.atkSpeed = config.attackCooldown;
                this.data.maxHp = config.monsterHp * modeNum;
                this.data.hpNum = config.monsterHp * modeNum;
                this.data.moveAnimation = config.moveAnimation;
                this.data.attackAnimation = config.attackAnimation;
                this.data.attackEffect = config.attacKeffect;
                this.data.moveSpeed = config.movementSpeed;
                this.data.attacKeffectInterval = config.attacKeffectInterval * 1000;
                this.data.monsterTag = config.monsterRefreshType;
                this.data.monsterName = config.name;
                this.data.skillAnimation = config.skillAnimation;
                this.data.monsterScore = config.monsterScore;
                this.data.atkOffsetPos.set(Number(config.trajectoryX) || 0, Number(config.trajectoryY) || 0);
                this.data.atkAnimJsonId = effectConfig?.Animation;
                this.data.atkAnimTargetType = animConfig?.target;
                this.data.atkStyle = effectConfig?.type;
                this.data.hurtEffect = effectConfig?.impactAnimation;
                let ran = 1 + -0.2 + (0.4 * Math.random());
                this.data.atkRag = effectConfig?.attacDistance * ran;
                this.data.atkPropNum = effectConfig?.doubleHit;
                let xRan = -width + (width * 2 * Math.random());
                this.data.pos = pos || v2(xRan, - height + 200);
                let scale = Number(config.animationScale) || 1;
                this.data.scale = v3(scale, scale, scale);
                this.comp = await this.createRoleComp(type);

                this.comp.init(this);


                if (config.skill) {
                    // let skillConfig = ConfigHelper.getInstance().getSkillConfigByJsonId(config.skill);
                    this.createSkill(null, 1, null, config.skill);
                    //log("monster skillId:", skillConfig, config.skill)
                }


                if (this.data.monsterType == E_MonsterType.Baize || this.data.monsterType == E_MonsterType.Huodou || this.data.monsterType == E_MonsterType.Jiuwei
                    || this.data.monsterType == E_MonsterType.Qiongqi || this.data.monsterType == E_MonsterType.Suanni || this.data.monsterType == E_MonsterType.Taotie
                ) {
                    this.data.pos.x = 0;
                    this.data.pos.y = -160;
                    this.comp.setPosAtonce(new Vec2(this.data.pos.x, this.data.pos.y))
                } else {
                    this.comp.setPosAtonce(new Vec2(this.data.pos.x, pos ? pos.y : this.data.pos.y - 400));
                }

                //log("monster config:", monsterType, type, config, this.data.atkSpeed, this.data.atkAnimJsonId, this.data.atkSpeedBuff, this.data.attackAnimation);


            }
            if (this.data.atkSpeed <= 0) {
                this.data.atkSpeed = 1
            }
        }
    }
    getAnimNode() {
        return this.comp?.getCompAnimNode();
    }
    towerUpLev(lev: number, isGameStart: boolean = false) {
        let config = ConfigHelper.getInstance().getDoorConfigByLev(lev);
        if (config) {
            let addHp = config.doorHp || 0;
            if (this.data.lev < lev) {
                // xcore.ui.showToast(`城门升级 ${this.data.lev}级 -> ${lev}级`);
                xcore.ui.addView(C_View.ViewTowerLevUp, { level: lev, hp: addHp }, 1);
                this.data.lev = lev;

                this.towerUpHp(addHp, true)

                /*    this.data.maxHp += addHp;
                   this.data.hpNum = this.data.maxHp; */
                if (this.comp && isGameStart) {
                    this.comp.setHp()
                }
            }
            if (this.comp) {
                this.comp.setLev(lev);
            }
        }

    }
    towerUpHp(num: number, isTowerUp: boolean = false) {
        if (!this.data.hpNum) {
            this.tempAddTowerHp += num;
            log('this.tempAddTowerHp', this.tempAddTowerHp)
            return
        }
        this.data.maxHp += num;
        this.data.hpNum += num;
        if (this.tempAddTowerHp) {
            this.data.hpNum += this.tempAddTowerHp;
            this.data.maxHp += this.tempAddTowerHp;
            this.tempAddTowerHp = 0;
        }
        log("塔防增加血量", this.data.hpNum, num)

        if (isTowerUp) {
            this.data.hpNum = this.data.maxHp
        }
        if (this.comp) {
            this.comp.setHp()
        }

    }
    towerPointupLev(lev: number) {

        this.add(this.data.userId, this.data.type, this.data.monsterType, lev, this.data.pos);

    }
    //实例化表现层
    async createRoleComp(type: E_RoleType) {

        if (this.comp && this.comp.node) {
            if (type == E_RoleType.Monster) {
                this.comp.node.parent = FightMgr.getInstance().monsterParentNd;
            } else if (type == E_RoleType.Tower) {
                this.comp.node.parent = FightMgr.getInstance().towerParentNd;
            }
            return this.comp
        };
        if (type == E_RoleType.Tower) {

            const pfbTower = await xcore.res.bundleLoadPrefab(C_Bundle.abGame, './prefab/Unit/UnitTower');
            let comp = instantiate(pfbTower).getComponent(UnitRoleComp);
            comp.node.parent = FightMgr.getInstance().towerParentNd;
            return comp
        } else if (type == E_RoleType.TowerPoint) {
            const pfbTowerPoint = await xcore.res.bundleLoadPrefab(C_Bundle.abGame, './prefab/Unit/UnitTowerPoint');
            let comp = instantiate(pfbTowerPoint).getComponent(UnitTowerPoint);
            comp.node.parent = FightMgr.getInstance().heroParentNd;
            return comp
        }
        else {
            const pfbRole = await xcore.res.bundleLoadPrefab(C_Bundle.abGame, './prefab/Unit/UnitRole');
            let comp = instantiate(pfbRole).getComponent(UnitRoleComp);
            comp.node.parent = (type == E_RoleType.Hero) ? FightMgr.getInstance().heroParentNd : FightMgr.getInstance().monsterParentNd;
            return comp;
        }
    }
    // async createTowerPointComp(): Promise<UnitTowerPoint> {
    //     if (this.comp && this.comp.node) {
    //         //@ts-ignore
    //         return this.comp
    //     }
    //     const pfbTowerPoint = await xcore.res.bundleLoadPrefab(C_Bundle.abGame, './prefab/Unit/UnitTowerPoint');
    //     let comp = instantiate(pfbTowerPoint).getComponent(UnitTowerPoint);
    //     comp.node.parent = FightMgr.getInstance().towerParentNd;
    //     return comp
    // }
    async setTowerHp(num: number) {
        this.data.maxHp = num;
        this.data.hpNum = this.data.maxHp;
        if (this.tempAddTowerHp) {
            this.data.hpNum += this.tempAddTowerHp;
            this.data.maxHp += this.tempAddTowerHp;
            this.tempAddTowerHp = 0;
        }
        log('setTowerHp', !!this.comp)
        if (!this.comp) {
            await this.createRoleComp(this.data.type);
            this.comp.init(this);
        }
        this.comp.setHp();

    }

    getUserRank() {
        return FightMgr.getInstance().getUserRank(this.data.userId)
    }
    getUserWeekScore() {
        return this.data.weekScore
    }
    /**消灭角色 怪物如果有附带buff的消除buff*/
    destroy(isTrueDestroy: boolean = false) {
        this.setState(E_RoleState.Dead);
        if (this.skills) {
            this.skills.forEach(skill => {
                skill.destroy();
            });
            this.skills.clear();
        }
        //this.buffs.clear();
        if (this.comp) {
            this.comp.clear(isTrueDestroy);

        }
        this.data.isLongMonster = false;
        this.data.reliveTime = 0;
        this.data.skinId = E_SkinType.Skin1;
        this.data.skinLev = 1;
        this.clearAllBuff();
    }

    //获取攻击目标
    getNearEnemys(num: number = 1, isNormalAttack: boolean = false): Role[] {
        return FightMgr.getInstance().getAttackNearEnemys(this, num, isNormalAttack);
    }

    //角色是否存活
    isRoleAlive() {

        return this.data.state != E_RoleState.Dead && this.data.state != E_RoleState.None && this.data.state != E_RoleState.WaitRelive
    }
    //瞬时左移动
    moveLeft() {
        this.data.pos.x -= 270;
    }
    //瞬时右移动
    moveRight() {
        this.data.pos.x += 270;
    }

    //判断是否进入塔攻击距离
    checkIfAbleAttackTower() {
        if (!(this.comp?.node)) return null
        let isIn = Math.abs(this.towerPosY - this.comp.node.position.y) <= this.data.atkRag
        this.isInAttackDistance = isIn;
        return isIn;
    }

    checkSkillAnim(cb?: Function) {
        if (this.data.skillAnimation) {
            let config = ConfigHelper.getInstance().getAnimConfigByJsonId(this.data.skillAnimation);
            if (!config) {
                log("skillAnimation config no find", this.data.skillAnimation)
                return
            }

            this.setState(E_RoleState.Skill);
            this.skillAnimCb = cb;
            // this.backTempState(E_RoleState.Idle, time);
            return true

        } else {
            cb && cb()
        }
        return false
    }
    //普通攻击释放 
    releaseAttack(atkNum: number = 1, isFirstAttack: boolean = true) {
        let speed = this.getAtkSpeed();
        //怪物
        if (this.data.type == E_RoleType.Monster) {

            if (this.data.state == E_RoleState.Hurt || this.data.state == E_RoleState.Skill) {
                return
            }
            //进入攻击范围
            if (this.checkIfAbleAttackTower()) {

                this.setState(E_RoleState.Attack);


                this.atkTime = 0;
                this.comp?.doAttackAnim((t) => {
                    let data = {
                        pos0: null,
                        pos1: null,
                        offsetpos: this.data.atkOffsetPos,
                        animId01: this.data.atkAnimJsonId,
                        animId02: this.data.hurtEffect,
                        roledir: this.data.moveDir,
                        atkStyle: this.data.atkStyle,
                        delay: this.data.attacKeffectInterval,
                        formRoleType: this.data.type,
                        checkFunc: null,
                    }
                    if (this.data.atkAnimTargetType == 2) {
                        data.pos1 = this.data.pos
                    }
                    else if (this.data.atkAnimTargetType == 3) {
                        data.pos0 = this.data.pos;
                        data.pos1 = v3(this.data.pos.x, this.towerPosY + 100);


                    }
                    data.checkFunc = this.isRoleAlive.bind(this)
                    //角色普通攻击
                    isFirstAttack && xcore.event.raiseEvent(E_EVENT.MonsterAtkEffect, data);

                    this.backTempState(E_RoleState.Idle, t * 1000, () => {
                        FightMgr.getInstance().attackTower(this);
                        //多重攻击
                        let leftNum = atkNum - 1;
                        if (leftNum > 0) {
                            //log('妖族多重攻击',leftNum)
                            this.releaseAttack(leftNum, false);
                        } else {
                            if (this.checkIfGreateBoss()) {
                                xcore.event.raiseEvent(E_EVENT.ShakeCam)
                            }
                        }
                    });
                }, speed);

            }


        }
        //仙族 or 炮台
        else {
            let roles = this.getNearEnemys(atkNum, true);

            if (!roles || roles.length <= 0) return
            this.setState(E_RoleState.Attack);
            let orgPos = roles[0].data.pos;
            if (orgPos.x > this.data.pos.x) {
                this.data.moveDir = 1;
            } else {
                this.data.moveDir = -1;
            }

            this.comp?.doAttackAnim((t) => {
                this.backTempState(E_RoleState.Idle, t * 1000, () => {
                    //多重攻击
                    // let leftNum = atkNum - 1;
                    // if (leftNum > 0) {
                    //     log('仙族多重攻击')
                    //     this.releaseAttack(leftNum);
                    // }
                });
            }, speed);
            /* if (this.data.type == E_RoleType.Hero) {
                log('speed:', speed)
            } */
            let isBao = false;
            //基础攻击力+攻击加成
            let atkPower = this.data.atkNum * (1 + this.data.atkNumBuff) + this.data.dimonEffects.attack/* + (this.data.dimonEffects.attack || 0) */;
            //暴击
            if (Math.random() < this.data.dimonEffects.atkr) {
                // log('暴击+++' + atkPower + '-->' + atkPower * ( this.data.dimonEffects.atkm + this.data.dimonEffects.baseAtkM), '加成', this.data.dimonEffects.atkm + this.data.dimonEffects.baseAtkM)
                atkPower = atkPower * (this.data.dimonEffects.atkm + this.data.dimonEffects.baseAtkM);
                isBao = true;;
            }
            for (let i = 0; i < atkNum; i++) {
                let role = roles[i] || roles[0];
                let orgPos = role.data.pos;
                //角色普通攻击
                xcore.event.raiseEvent(E_EVENT.HeroAtkEffect, {
                    animId01: this.data.atkAnimJsonId,
                    pos0: this.data.pos,
                    pos1: orgPos,
                    offsetpos: this.data.atkOffsetPos,
                    normalatk: this.data.skinId,
                    animId02: this.data.hurtEffect,
                    formRoleType: this.data.type,
                    delay: this.data.attacKeffectInterval,
                    speed,
                    //攻击表现播放完后回调
                    cb: () => {

                        if (role && role.isRoleAlive()) {
                            role.hurt(atkPower, this.data.userId, null, isBao);
                        }

                    }
                })
            }

        }
    }
    getAtkSpeed() {
        return this.data.atkSpeed * this.data.atkSpeedBuff * (this.data.atkSpeed / (this.data.atkSpeed * (1 + this.data.dimonEffects.atkspeed)));
    }

    //设置塔防无敌状态
    buffTower(time: number) {
        FightMgr.getInstance().buffTower(time);
    }
    public setState(state: E_RoleState) {
        this.timeoutBackState && clearTimeout(this.timeoutBackState);
        if (this.data.state == E_RoleState.Skill) {
            if (this.skillAnimCb) {
                this.skillAnimCb();

            }
        }
        this.data.state = state;
        if (state == E_RoleState.Dead) {
            this.buffCons.forEach(e => e.hide())
            // this.buffCons.clear();
        }
        this.skillAnimCb = null;
    }
    //设置状态切换计时器
    backTempState(state: E_RoleState, delayTime: number = 1000, cb?) {
        if (!this.data.tempState) {
            this.data.tempState = state;

        }
        this.timeoutBackState && clearTimeout(this.timeoutBackState);
        this.timeoutBackState = setTimeout(() => {
            if (!this.data) return
            if (this.data.state != E_RoleState.Dead) {
                this.setState(this.data.tempState);
                this.data.tempState = null;
            }
            cb && cb();
        }, delayTime);
    }
    /**受击 */
    hurt(num: number, fromUserId?: string, skillType?: E_SkillType, isBao?: boolean) {

        if (!this.data) return
        //是否无敌状态或已死亡状态
        if (this.data.invincibleTime > 0 || !this.isRoleAlive()) {
            return
        }
        if (this.data.skillWeakIds.length > 0) {
            // log("看下是否生效：", skillType)
            if (this.data.skillWeakIds.includes(skillType) && this.data.skillWeakNum > 0) {
                //log("弱点生效：", skillType, this.data.skillWeakNum,num);
                num *= this.data.skillWeakNum;
                // log("弱点生效后num：", num);
            }
        }
        if (!num) {
            log("扣血值有问题", num, fromUserId, skillType)
            return
        }
        let orgNum = Math.floor(num)
        num = orgNum;
        if (num > this.data.hpNum) {
            num = this.data.hpNum;
        }
        this.data.hpNum -= num;
        FightMgr.getInstance().addUserAtkPower(fromUserId, num);
        if (this.data.type == E_RoleType.Monster) {

            xcore.event.raiseEvent(E_EVENT.HurtTips, { posx: this.data.pos.x, posy: this.data.pos.y, num: orgNum, isBao })
            this.skills.forEach(skill => skill.checkHpReduceSkill(this.data.hpNum / this.data.maxHp));

        }
        if (!this.data.hpNum || this.data.hpNum <= 0) {
            this.data.hpNum = 0;
            this.setState(E_RoleState.Dead);

            if (this.data.type == E_RoleType.Monster) {
                this.comp?.onDead();
                xcore.event.raiseEvent(E_EVENT.MonsterDead, { pos0: this.data.pos });
                FightMgr.getInstance().addUserScore(fromUserId, this.data.monsterScore, true);
                if (this.data.isLongMonster) {
                    this.setState(E_RoleState.WaitRelive);
                    this.data.reliveTime = 10;
                    this.skills.forEach(skill => skill.hide());
                    log("妖族等待复活");
                } else {

                    FightMgr.getInstance().killMonster(this, fromUserId);
                    this.destroy();
                }
                let fromUser = FightMgr.getInstance().findUser(fromUserId);
                if (!fromUser) {
                    //log("hurt fromuser no find:", fromUserId);
                    return
                }
                //击杀boss提示
                if (this.checkIfBossMonster()) {
                    fromUser.killboss += 1;
                    // xcore.event.raiseEvent(E_EVENT.GiftMessage, {
                    //     type: E_GiftMessageType.Kill,
                    //     userId: fromUserId,
                    //     name: fromUser.nickName,
                    //     avatar: fromUser.iconUrl,
                    //     monsterName: this.data.monsterName
                    // })
                    //大boss

                }
                fromUser.killmonster += 1;

            }

            return 0
        } else {
            // this.setState(E_RoleState.Hurt);
            // this.backTempState(E_RoleState.Idle, 50);

            if (this.comp) {
                this.comp.playHit();
                this.comp.setHp();
            }
            return this.data.hpNum;
        }
    }
    checkIfBossMonster() {
        return this.data.type == E_RoleType.Monster && (this.data.monsterTag == E_MonsterTag.smallboss || this.data.monsterTag == E_MonsterTag.bigboss)
    }

    checkIfSmallBoss() {
        return this.data.type == E_RoleType.Monster && this.data.monsterTag == E_MonsterTag.smallboss
    }
    checkIfBigBoss() {
        return this.data.type == E_RoleType.Monster && this.data.monsterTag == E_MonsterTag.bigboss
    }
    checkIfGreateBoss() {
        return this.data.type == E_RoleType.Monster && this.data.monsterTag == E_MonsterTag.bigboss && this.data.atkAnimTargetType == 3
    }
    saveTower(psent: number, time: number) {
        if (this.data.hpNum >= this.data.maxHp) {
            return 0
        }
        if (this.data.auotSaveHpTime < 0) {
            this.data.auotSaveHpTime = 0;

        }

        this.data.auotSaveHpTime += time;

        if (!this.data.autoSaveHpNum) {
            psent = Number(psent);
            this.data.autoSaveHpNum = Math.ceil(this.data.maxHp * (psent / 100));
            this.data.autoSaveHpEachNum = Math.ceil(this.data.autoSaveHpNum / 5);
        }

        // console.log("加血+time:", this.data.auotSaveHpTime, this.data.autoSaveHpEachNum)
        return this.data.autoSaveHpNum

    }

    addHp(num: number) {
        num = Math.floor(num);
        if (num < 0) {
            return
        }
        this.data.hpNum += num;
        if (this.data.hpNum > this.data.maxHp) {
            this.data.hpNum = this.data.maxHp;
        }
        this.comp.setHp();
        // console.log("补血", num)

    }

    async setBuffIcon(icon: string, time: number) {
        if (!icon) return
        if (!this.isRoleAlive()) return
        let buffcon = this.buffCons.get(icon);
        if (!buffcon) {
            const pfbBuffcon = await xcore.res.bundleLoadPrefab(C_Bundle.abGame, './prefab/Unit/UnitBuffCoin');
            buffcon = instantiate(pfbBuffcon).getComponent(UnitBuffCoin);
            if (!this.isRoleAlive()) {
                buffcon.destroy();
                return
            }
            this.buffCons.set(icon, buffcon);
        }
        buffcon.setData(this, icon, time);
        buffcon.node.parent = FightMgr.getInstance().skillParentNd;
        let index = 0;
        this.buffCons.forEach(e => {
            if (e.node.active) {
                e.setPos(index);
                index += 1;
            }
        })

    }
    async setBuffColor(color: number, time: number) {
        this.comp && this.comp.setColor(color, time);

    }
    /**************************************************角色受buff属性设置*****************************************************/
    //毒火持续伤
    setBuffAutoHurt(hurtNum: number, time: number, lastTime: number, icon?: string, color?: number) {
        if (!this.isRoleAlive()) return
        this.data.autoHurtNum = Number(hurtNum);
        this.data.autoHurtEachTime = time;
        this.data.autoHurtTime = lastTime;
        this.data.autoHurtEffectTime = 0;
        this.setBuffIcon(icon, lastTime);
        this.setBuffColor(color, lastTime);
    }


    //攻速buff
    setBuffAtkSpeed(speed: number, time: number = 1, icon?: string, color?: number) {
        if (!this.isRoleAlive()) return
        this.data.atkSpeedBuff = Number(speed);
        this.data.atkSpeedBuffTime = time;
        this.setBuffIcon(icon, time);
        this.setBuffColor(color, time);
        log("攻速buff", this.data.atkSpeedBuff, this.data.atkSpeedBuffTime)
    }
    //技能速度
    setBuffSkillSpeed(speed: number, time: number = 1, icon?: string, color?: number) {
        if (!this.isRoleAlive()) return
        this.data.skillSpeedBuffTime = Number(time);
        this.data.skillSpeedBuff = speed;
        this.skills.forEach(e => e.refreshSpeed());
        this.setBuffIcon(icon, time);
        this.setBuffColor(color, time);
    }
    //攻击力buff
    setBuffAtk(atk: number, time: number = 1, icon?: string, color?: number) {
        if (!this.isRoleAlive()) return

        this.data.atkNumBuff = Number(atk);
        this.data.atkNumBuffTime = time;
        if (this.data.atkNumBuff < 0) {
            this.data.atkNumBuff = 0;
        }
        this.setBuffIcon(icon, time);
        this.setBuffColor(color, time);

    }
    /**
     * 设置移动速度buff
     * @param speed 速度百分比 
     * @param time 起效时间
     */
    setBuffSpeed(speed: number, time: number = 1, icon?: string, color?: number) {
        if (!this.isRoleAlive()) return
        this.data.moveSpeedBuff = Number(speed);
        this.data.moveSpeedBuffTime = time;
        this.setBuffIcon(icon, time);
        this.setBuffColor(color, time);

    }
    //设置眩晕时间
    setDizzTime(time: number, icon?: string, color?: number) {
        log("setDizzTime")
        if (!this.isRoleAlive()) return
        this.data.dizzinessTime = Number(time);
        this.setState(E_RoleState.Dizziness);
        this.setBuffIcon(icon, time);
        this.setBuffColor(color, time);

    }
    /**设置无敌 time单位秒 */
    setInvincible(time: number) {
        this.data.invincibleTime = time;

    }
    /**设置弱点 */
    setWeakState(ids: string[], num: number) {
        this.data.skillWeakIds = ids;
        this.data.skillWeakNum = Number(num);

        log("设置弱点", this.data.skillWeakIds, this.data.skillWeakNum, this.data.monsterType)
    }
    //角色死亡清楚所有身上buff
    clearAllBuff() {
        this.data.atkNumBuff = 0;
        this.data.atkNumBuffTime = 0;
        this.data.moveSpeedBuff = 1;
        this.data.moveSpeedBuffTime = 0;
        this.data.dizzinessTime = 0;
        this.data.invincibleTime = 0;
        this.data.autoHurtTime = 0;
        this.data.atkSpeedBuff = 1;
        this.data.skillSpeedBuff = 1
        this.data.autoHurtNum = 0;
        this.data.auotSaveHpTime = 0;
        /* this.data.autoSaveHpEachNum = 0;
        this.data.autoSaveHpNum = 0; */
        this.buffCons.forEach(e => e.kill());
        this.buffCons.clear();
    }
    /**移动 */
    moveTo(pos: Vec2) {
        this.data.pos = pos;
    }

    /**添加技能指令 */
    updateSkill(key: E_SkillType, greatkey: E_SkillType, bigNum: number, smallNum: number) {
        if (!this.isRoleAlive()) return
        //log("updateSkill", smallNum, bigNum)
        //小技能
        let smallSkill;
        let bigSkill;
        if (smallNum > 0) {
            smallSkill = this.createSkill(key, smallNum);

        }

        //常驻技能 法宝
        if (bigNum > 0 && greatkey) {
            log('大技能')
            bigSkill = this.createSkill(greatkey, bigNum, true);

        }
        log('skill:', smallSkill, bigSkill, smallSkill == bigSkill)
    }


    /**
     * 创建技能
     * @param key 技能类型
     * @param num 技能数量
     * @param isGreateSkill 是否大技能
     */
    createSkill(key: E_SkillType, num: number, isGreateSkill: boolean = false, jsonId?: string, isLongSkll: boolean = false) {
        if (!this.isRoleAlive()) return
        if (!this.skills) {
            this.skills = new Map();
        }
        let skillkey = key;
        let id = skillkey || jsonId
        let skill = this.skills.get(id);
        log('createskill:', id, skill, this.skills, isGreateSkill);
        if (skill) {
            //大技能升级
            if (isGreateSkill) {

                if (skill.checkIfAbleUpLev(num)) {
                    let curLev = skill.getLev();
                    skill.levelUp(num, skillkey);

                    if (skillkey != E_SkillType.GreatAttack) {

                        xcore.event.raiseEvent(E_EVENT.GiftMessage, {
                            type: E_GiftMessageType.SkillUp,
                            skillType: skillkey,
                            fromLev: curLev,
                            lev: num, avatar: this.data.iconUrl, name: this.data.nickName
                        })
                    }


                } else {

                    return false
                }


            }
            //小技能增加释放次数 
            else {
                skill.addNum(num);
            }
        }

        else {
            skill = FightMgr.getInstance().getSkill();
            skill.init(this, skillkey, isGreateSkill ? num : 1, jsonId, num, isLongSkll);
            this.skills.set(skillkey || jsonId, skill);
            if (isGreateSkill && skillkey != E_SkillType.GreatAttack) {
                if (skillkey) {
                    xcore.event.raiseEvent(E_EVENT.GiftMessage, {
                        type: E_GiftMessageType.SkillUp,
                        skillType: skillkey,

                        lev: num, avatar: this.data.iconUrl, name: this.data.nickName
                    })
                }
            }
        }
        return skill
    }

    /**技能结束 有些技能有次数限制时需要回收技能 有些技能常驻的无需回收 */
    delectSkill(skill: Skill) {
        this.skills.delete(skill.skillType);
        FightMgr.getInstance().killSkill(skill);
    }

    /**更换皮肤指令 */
    switchSkin(skinId: E_SkinType, lev: number = 1) {

        if (!skinId) return
        let fromUser = FightMgr.getInstance().findUser(this.data.userId);
        let haveSkin = fromUser.skins.find(e => e.prop == skinId);
        if (!haveSkin) {
            log('没有这个皮肤', fromUser.skins, skinId)
            return
        }
        //没有选定-》调用切换皮肤
        if (haveSkin.useStatus != 1) {
            Net.setSelectSkin(this.data.userId, skinId);
            // let selectSkin = fromUser.skins.find(e => e.prop == skinId);
            fromUser.skins.forEach(e => {
                e.useStatus = 0;
            })
            haveSkin.useStatus = 1;
        }
        // log('切换皮肤：', skinId)
        if (this.data.weekScore == null || this.data.weekScore == undefined) {
            this.needSwitchSkinId = skinId;
            return
        } else if (this.isRoleAlive()) {
            this.data.skinId = skinId
            this.data.skinLev = lev;
            this.needSwitchSkinId = null;

            // 遍历键
            let values = C_GiftSkill;
            for (let [key, value] of this.skills) {
                //console.log(key);  // 输出 2, 1, 3（按照插入顺序）
                if (!values.includes(key)) {
                    this.skills.delete(key);
                    log('切换皮肤-》删除技能', key)
                }
            }

            this.add(this.data.userId, this.data.type, this.data.monsterType, this.data.lev, this.data.pos, this.data.nickName, this.data.iconUrl);
            this.setState(E_RoleState.Idle);
            let offset = new Vec3(0, 0)
            switch (this.data.skinId) {
                case E_SkinType.Skin1:

                    break;
                case E_SkinType.Skin2:
                    offset.set(0, 20)
                    break;
                case E_SkinType.Skin3:
                    offset.set(0, 60)
                    break;
                case E_SkinType.Skin4:
                    offset.set(0, 60)
                    break;
                case E_SkinType.Skin5:
                    offset.set(0, 60)
                    break;
                case E_SkinType.Skin6:
                    offset.set(0, 80)
                    break;
                case E_SkinType.Skin7:
                    offset.set(0, 80)
                    break;
                default:
                    offset.set(0, 80)
                    break;
            }

            this.comp?.setRankInfo(offset);
            this.skills.forEach(e => e.resetSkillCompPos(null, offset))



        }
    }
    updateDimondInfo(datas: any[]) {
        let configs = ConfigHelper.getInstance().getBeadConfigs();
        for (let i = 0; i < configs.length; i++) {
            let config = configs[i];
            let type = config.jsonId;
            let data = datas.find(e => e.type == type);
            if (data && data.num) {
                let cf = ConfigHelper.getInstance().getBeadConfigByNum(type, data.num);
                if (!cf) {
                    warn("获取 beads 配置失败", type, data.num)
                    break
                }
                switch (type) {
                    /**金珠 攻击加成*/
                    case '260001':
                        this.data.dimonEffects.attackLev = cf.beadLevel;
                        this.data.dimonEffects.attack = Number(cf.attack);
                        this.data.dimonEffects.attackDmNum = data.num;
                        break;
                    /**木珠 法宝cd*/
                    case '260002':
                        this.data.dimonEffects.skillcdLev = cf.beadLevel;
                        this.data.dimonEffects.skillcd = Number(cf.weaponInterval);
                        this.data.dimonEffects.skillcdDmNum = data.num;
                        break;
                    /**水珠 攻速加成*/
                    case '260003':
                        this.data.dimonEffects.atkspeedLev = cf.beadLevel;
                        this.data.dimonEffects.atkspeed = Number(cf.attackCooldown);
                        this.data.dimonEffects.atkspeedDmNum = data.num;
                        break;
                    /**火珠 暴击伤害*/
                    case '260004':
                        this.data.dimonEffects.atkmLev = cf.beadLevel;
                        this.data.dimonEffects.atkm = Number(cf.criticalDamage);
                        this.data.dimonEffects.atkmDmNum = data.num;
                        break;
                    /**土珠 城门血量*/
                    case '260005':

                        this.data.dimonEffects.hpLev = cf.beadLevel;

                        this.data.dimonEffects.hpDmNum = data.num;
                        if (this.data.dimonEffects.hp != Number(cf.gateHp)) {
                            this.data.dimonEffects.hp = Number(cf.gateHp);
                            FightMgr.getInstance().addMaxHpTower(this.data.dimonEffects.hp)
                        }
                        break;

                    default:
                        break;
                }
            }

        }

        log("updateDimondInfo", this.data.dimonEffects)
    }
    //拿到积分 看是否需要切换皮肤 看看是否有可积分-》解锁的皮肤
    checkSwitchSkin() {
        if (this.needSwitchSkinId) {
            this.switchSkin(this.needSwitchSkinId);
        }
        let fromUser = FightMgr.getInstance().findUser(this.data.userId);
        let scoreSkinConfis = ConfigHelper.getInstance().getSkinConfgs().filter(e => e.skin == 1);
        let timeDiff = 60 * 60 * 24 * 30//  Math.floor(TimeUtil.getTimeUntilNextSaturday() / 1000);
        for (let i = 0; i < scoreSkinConfis.length; i++) {
            let config = scoreSkinConfis[i];
            let haveSkin = fromUser.skins.find(e => e.prop == config.jsonId);
            let isNeedUnlock = !haveSkin && this.data.weekScore >= config.openCondition;
            if (isNeedUnlock) {
                log("needUnlock", config.jsonId)
                Net.rewardSkin(fromUser.userId, 4, [
                    {
                        num: 1, prop: config.jsonId,
                        time: timeDiff
                    }
                ])
                let skin = { prop: config.jsonId, time: timeDiff + (config.period * 1000) }
                fromUser.skins.push(skin)
            }
        }

        let selectedSkin = fromUser.skins.find(e => e.useStatus == 1);
        if (!selectedSkin) {
            let defaultSkinId = '170001'
            let defaultSkin = fromUser.skins.find(e => e.prop == defaultSkinId);
            Net.setSelectSkin(this.data.userId, defaultSkinId);
            defaultSkin && (defaultSkin.useStatus = 1);
            selectedSkin = defaultSkin;
        }
    }

    //礼物积分名头
    checkGiftTitle(score: number) {

        let config = ConfigHelper.getInstance().getGiftRankTitleByScore(score);
        log('礼物积分名头', score, config)
        if (config) {
           // this.comp?.setGiftTitleInfo(config)
        }
    }
    //查配置 判断该技能是否有附带buff
    checkIfHaveBuff(jsonId: string) {
        if (!jsonId) return null
        if (!this.isRoleAlive()) return
        return this.createBuff(jsonId);
    }

    createBuff(jsonId: string) {
        let buff = FightMgr.getInstance().getBuff();
        buff.init(jsonId);
        //log('createBuff jsonid:', jsonId);
        return buff;
    }

    tick(dt) {
        if (this.data.state == E_RoleState.Dead) return
        this.data.tickTime += dt;
        /* if(this.data.type==E_RoleType.Hero){
            log(this.data.state)
        } */
        //移动速度buff倒计时
        if (this.data.moveSpeedBuffTime > 0) {
            this.data.moveSpeedBuffTime -= dt;
            if (this.data.moveSpeedBuffTime <= 0) {
                this.data.moveSpeedBuffTime = 0;
                this.data.moveSpeedBuff = 1;

            }
        }
        //攻击力buff倒计时
        if (this.data.atkNumBuffTime > 0) {
            this.data.atkNumBuffTime -= dt;
            if (this.data.atkNumBuffTime <= 0) {
                this.data.atkNumBuffTime = 0;
                this.data.atkNumBuff = 0;
            }
        }
        //攻击速度 间隔buff 倒计时
        if (this.data.atkSpeedBuffTime) {
            this.data.atkSpeedBuffTime -= dt;
            if (this.data.atkSpeedBuffTime <= 0) {
                this.data.atkSpeedBuffTime = 0;
                this.data.atkSpeedBuff = 1;
            }
        }
        //技能释放间隔
        if (this.data.skillSpeedBuffTime > 0) {
            this.data.skillSpeedBuffTime -= dt;
            if (this.data.skillSpeedBuffTime <= 0) {
                this.data.skillSpeedBuffTime = 0;
                this.data.skillSpeedBuff = 1;
                this.skills.forEach(skill => skill.refreshSpeed());
            }
        }
        //眩晕状态buff 妖怪全局释放的眩晕buff导致的状态 仙族则是局部
        if (this.data.dizzinessTime > 0) {
            this.data.dizzinessTime -= dt;
            if (this.data.dizzinessTime <= 0) {
                this.data.dizzinessTime = 0;
                this.setState(E_RoleState.Idle)

            }
            return
        }
        //灼烧buff
        if (this.data.autoHurtTime > 0) {
            this.data.autoHurtTime -= dt;
            this.data.autoHurtEffectTime += dt;
            if (this.data.autoHurtEffectTime >= this.data.autoHurtEachTime) {
                this.data.autoHurtEffectTime = 0;
                this.hurt(this.data.autoHurtNum);
            }

            if (this.data.autoHurtTime <= 0) {
                this.data.autoHurtTime = 0;
                this.data.autoHurtNum = 0;
            }
        }
        //等待复活状态
        if (this.data.state == E_RoleState.WaitRelive) {
            if (this.comp.node.active) {
                this.comp.node.active = false;
            }
            this.data.reliveTime -= dt;
            if (this.data.reliveTime <= 0) {
                this.data.reliveTime = 0;
                this.setState(E_RoleState.Idle);
                this.comp.node.active = true;
                this.skills.forEach(skill => skill.show());
                let pos = v2(this.comp.node.position.x, this.comp.node.position.y);
                this.add(this.data.userId, this.data.type, this.data.monsterType, this.data.lev).then(() => {
                    this.data.pos = pos;
                    this.comp.setPosAtonce(this.data.pos);
                });
                log('怪物原地复活');
            }
            return
        }
        if (this.data.type == E_RoleType.Tower) {
            if (this.data.invincibleTime > 0) {
                this.data.invincibleTime -= dt;
                if (this.data.invincibleTime < 0) {
                    this.data.invincibleTime = 0;
                }

            }
            if (this.data.auotSaveHpTime > 0) {
                this.data.auotSaveHpTime -= dt;
                this.tempTime += dt;
                if (this.tempTime >= 1) {
                    this.tempTime = 0;
                    /*  let addNum = 0;
                     addNum = this.data.autoSaveHpEachNum; */
                    this.addHp(this.data.autoSaveHpEachNum);
                    /* this.data.autoSaveHpNum -= addNum;
                    if (this.data.autoSaveHpNum <= 0) {
                        this.data.autoSaveHpNum = 0;
                    } */
                }
                if (this.data.auotSaveHpTime <= 0) {
                    this.data.auotSaveHpTime = 0;
                }
            }
        } else {
            let speed = this.getAtkSpeed();
            //怪物角色 间隔释放攻击
            if (this.data.type == E_RoleType.Monster) {

                //
                if (this.data.state != E_RoleState.Attack && this.data.tickTime % (speed) <= dt) {
                    this.releaseAttack(this.data.atkPropNum);

                }
                if (this.data.state == E_RoleState.Skill) {

                }
                else if (this.data.state == E_RoleState.Attack) {
                    this.data.pos.x += (-1 + 2 * Math.random());
                    //  this.data.pos.y += (-1 + 2 * Math.random());
                    this.atkTime += dt;
                    if (this.atkTime > 3) {
                        this.atkTime = 0;
                        this.setState(E_RoleState.Idle)
                    }
                }
                else if (this.data.state == E_RoleState.Hurt) {
                    if (this.comp) {
                        this.data.pos.x = this.comp.node.position.x;
                        this.data.pos.y = this.comp.node.position.y;

                        // this.data.pos.y += (-1 + 2 * Math.random());
                    }
                }
                else {
                    //不在攻击范围
                    if (!this.isInAttackDistance) {
                        this.data.pos.x += (-3 + 6 * Math.random());
                        this.data.pos.y += this.data.moveSpeed * this.data.moveSpeedBuff * dt;
                        if (this.data.pos.y > this.towerPosY) {
                            this.data.pos.y = this.towerPosY - 5;
                            this.releaseAttack(this.data.atkPropNum);
                        }
                    }
                    //在攻击范围
                    else {
                        if (this.data.state == E_RoleState.Idle) {

                            this.setState(E_RoleState.Move);
                            this.data.moveDir = Math.random() < 0.5 ? 1 : -1;
                            if (this.data.pos.y > this.towerPosY) {
                                this.data.pos.y = this.towerPosY - 5;
                            }
                        }
                        else if (this.data.state == E_RoleState.Move && !this.checkIfBossMonster()) {
                            this.data.pos.x += (this.data.moveDir);
                        }

                    }
                }

            }
            else if (this.data.type == E_RoleType.Hero) {
                if (this.data.state != E_RoleState.Attack && this.data.tickTime % (speed) <= dt) {
                    this.releaseAttack(this.data.atkPropNum);

                }
                if (this.data.state == E_RoleState.Attack) {
                    /* this.data.pos.x += (-1 + 2 * Math.random());
                    this.data.pos.y += (-1 + 2 * Math.random()); */
                    this.atkTime += dt;
                    if (this.atkTime > 1.5) {
                        this.atkTime = 0;
                        this.setState(E_RoleState.Idle)
                    }
                }

                if (this.data.state == E_RoleState.Idle) {

                    this.setState(E_RoleState.Move);
                    this.data.moveDir = Math.random() < 0.5 ? 1 : -1;
                }
                else if (this.data.state == E_RoleState.Move) {
                    this.data.pos.x += (this.data.moveDir);
                }
            }
            else if (this.data.type == E_RoleType.TowerPoint) {
                //炮塔
                //log("炮塔状态:", this.data.atkSpeed,  this.data.state)
                if (this.data.tickTime % speed <= dt) {
                    this.releaseAttack(this.data.atkPropNum);

                }
                if (this.data.state == E_RoleState.Attack) {
                    this.data.pos.x += (-1 + 2 * Math.random());
                    //this.data.pos.y += (-1 + 2 * Math.random());
                }

                else {
                    if (this.data.state == E_RoleState.Idle) {
                        this.setState(E_RoleState.Move);
                        this.data.moveDir = Math.random() < 0.5 ? 1 : -1;
                    }
                    else if (this.data.state == E_RoleState.Move) {
                        this.data.pos.x += (this.data.moveDir);
                    }
                }
            }

            //移动范围限制
            if (this.data.pos.x < this.data.moveLeft) {
                this.data.pos.x = this.data.moveLeft + 6;
                this.data.moveDir *= -1;
            }
            if (this.data.pos.x > this.data.moveRight) {
                this.data.pos.x = this.data.moveRight - 6;
                this.data.moveDir *= -1;
            }


            //技能tick
            this.skills.forEach(skill => skill.tick(dt));


        }


    }

}