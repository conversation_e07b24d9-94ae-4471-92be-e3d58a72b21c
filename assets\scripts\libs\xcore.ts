import { DEBUG } from "cc/env";
import { EventManager } from "./manager/EventManager";

import { ResManager } from "./manager/ResManager";


import { Logger } from "./utils/Logger";
import { Sound } from "./utils/Sound";
import { HttpRequest } from "./utils/HttpRequest";
import { TimerManager } from "./manager/TimerManager";
import { ViewManager } from "./manager/ViewManager";
import { WxWeb } from "./utils/WxWeb";

import { AnimationClip, Node, SpriteAtlas } from "cc";
import { Collection } from "./utils/Collection";

import Data from '../Data'




export var gameVersion: string = "1.2.1";

/** 框架核心模块访问入口 */
export class xcore {

    /**生产or开发 环境 */
    static env: string;

    /**运行环境 */
    static runtime: string;

    /** ----------核心模块---------- */
    /** 日志管理 */
    static log = Logger;

    static ndUI: Node

    static ndGame: Node

    /** 全局消息管理 */
    static event: EventManager = EventManager.getInstance();

    /** 资源管理 */
    static res: ResManager = ResManager.getInstance();

    /** 界面管理 */
    static ui: ViewManager = ViewManager.getInstance();

    /** 微信 */
    static wx: WxWeb = WxWeb.getInstance();

    /** http */
    static http: HttpRequest = HttpRequest.getInstance();
    /**平台 */
    static channel: string

    /** 游戏音乐管理 */
    static sound: Sound

    /** 游戏时间管理 */
    static timer: TimerManager;


    /** 本地存储 */
    static storage

    static gameData = Data;

    /** 游戏配置 */
    static config: JSON;


}

// 引入xcore全局变量以方便调试
if (DEBUG) {
    //@ts-ignore
    window.xcore = xcore;

}