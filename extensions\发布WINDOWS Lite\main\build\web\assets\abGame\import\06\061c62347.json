[1, ["b6xRHteZtCpKpnBirJw3gC@6c48a", "65uEd7RNdHHrFZKNbLuEMq", "d1NGQ2rJZCcbhjH0/erZWw"], ["node", "mainTexture", "_effectAsset", "root", "ndLight", "_particleSystem", "data", "_cpuMaterial", "_mainTexture"], [["cc.CurveRange", ["mode", "constantMin", "constantMax", "constant", "splineMin", "splineMax"], -1, 4, 4], ["cc.GradientRange", ["_mode", "gradient", "color"], 2, 4, 5], ["cc.PrefabInfo", ["fileId", "instance", "nestedPrefabInstanceRoots", "targetOverrides", "root", "asset"], -1, 1, 1], ["cc.<PERSON><PERSON>", ["time"], 2], ["cc.<PERSON><PERSON><PERSON>", ["alpha", "time"], 1], ["cc.RealKeyframeValue", ["rightTangent", "rightTangentWeight", "leftTangent", "leftTangentWeight", "value"], -2], ["cc.Material", ["_name", "_techIdx", "_states", "_defines", "_props"], -1, 12], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_layer", "_children", "_components", "_prefab", "_lrot", "_euler"], 1, 2, 9, 4, 5, 5], ["cc.Node", ["_name", "_layer", "_parent", "_components", "_prefab", "_lrot", "_lscale", "_euler"], 1, 1, 12, 4, 5, 5, 5], ["cc.UITransform", ["node", "__prefab"], 3, 1, 4], ["cc.CompPrefabInfo", ["fileId"], 2], ["dabfdDKmgNLiYZKZt77GOh7", ["node", "__prefab", "ndLight"], 3, 1, 4, 1], ["cc.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", ["node", "__prefab"], 3, 1, 4], ["cc.ParticleSystem", ["duration", "simulationSpeed", "_aabbHalfX", "_aabbHalfY", "_aabbHalfZ", "_capacity", "node", "__prefab", "_materials", "startColor", "startSizeX", "startSizeY", "startSizeZ", "startSpeed", "startRotationX", "startRotationY", "startRotationZ", "startDelay", "startLifetime", "gravityModifier", "rateOverTime", "rateOverDistance", "_colorOverLifetimeModule", "_shapeModule", "_sizeOvertimeModule", "_velocityOvertimeModule", "_forceOvertimeModule", "_limitVelocityOvertimeModule", "_rotationOvertimeModule", "_textureAnimationModule", "_noiseModule", "_trailModule", "renderer"], -3, 1, 4, 12, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], ["cc.ColorOvertimeModule", ["color"], 3, 4], ["cc.Gradient", ["colorKeys", "alphaKeys"], 3, 9, 9], ["cc.ShapeModule", ["_shapeType", "arcSpeed"], 2, 4], ["cc.SizeOvertimeModule", ["size", "x", "y", "z"], 3, 4, 4, 4, 4], ["cc.VelocityOvertimeModule", ["x", "y", "z", "speedModifier"], 3, 4, 4, 4, 4], ["cc.ForceOvertimeModule", ["x", "y", "z"], 3, 4, 4, 4], ["cc.LimitVelocityOvertimeModule", ["_enable", "dampen", "limitX", "limitY", "limitZ", "limit"], 1, 4, 4, 4, 4], ["cc.RotationOvertimeModule", ["x", "y", "z"], 3, 4, 4, 4], ["cc.TextureAnimationModule", ["_enable", "_numTilesX", "_numTilesY", "cycleCount", "frameOverTime", "startFrame"], -1, 4, 4], ["cc.RealCurve", ["_times", "_values"], 2, 9], ["cc.NoiseModule", [], 3], ["cc.TrailModule", ["lifeTime", "widthRatio", "colorOverTrail", "colorOvertime", "_particleSystem"], 3, 4, 4, 4, 4, 1], ["cc.ParticleSystemRenderer", ["_renderMode", "_alignSpace", "_cpuMaterial", "_mainTexture"], 1, 6, 6]], [[0, 1], [11, 0, 2], [0, 3, 2], [0, 0, 1, 2, 4], [5, 4, 0, 1, 2, 3, 6], [10, 0, 1, 1], [1, 1], [24, 0, 1, 2], [6, 0, 1, 2, 3, 4, 5], [7, 0, 2], [8, 0, 1, 2, 3, 4, 5, 6, 3], [9, 0, 1, 2, 3, 4, 5, 6, 7, 3], [12, 0, 1, 2, 1], [2, 0, 1, 2, 3, 4, 5, 5], [2, 0, 1, 3, 2, 4, 5, 5], [13, 0, 1, 1], [14, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 7], [1, 0, 1, 2], [1, 2, 1], [0, 0, 4, 5, 2], [15, 0, 1], [16, 0, 1, 1], [3, 1], [3, 0, 2], [4, 0, 2], [4, 0, 1, 3], [17, 0, 1, 2], [18, 0, 1, 2, 3, 1], [19, 0, 1, 2, 3, 1], [20, 0, 1, 2, 1], [21, 0, 1, 2, 3, 4, 5, 3], [22, 0, 1, 2, 1], [23, 0, 1, 2, 3, 4, 5, 5], [5, 0, 1, 2, 3, 5], [25, 1], [26, 0, 1, 2, 3, 4, 1], [27, 0, 1, 2, 3, 3]], [[[[8, "lightning_4x1_001", "3", [{"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}], [{}], [[[{}, "mainTexture", 6, 0]], 11]]], 0, 0, [0, 0], [1, 2], [0, 2]], [[[9, "UnitLighting"], [10, "UnitLighting", 33554432, [-5], [[5, -2, [1, "32dRDF3w5IPbCx0RXipxrV"]], [12, -4, [1, "9ePrSKdRVNVbzcqaagwgfJ"], -3]], [13, "54ja70r4tFaKnQKeeQl6ak", null, null, [], -1, 0], [3, 0, 0, 0.7071067811865475, 0.7071067811865476], [1, 0, 0, 90]], [11, "effect", 33554432, 1, [[-6, [15, -7, [1, "c8X72XbEVGZL5aYJZy9+ZS"]], [5, -8, [1, "4btZxw4w5C9KgfKx+zJyN5"]]], 1, 4, 4], [14, "a08+rnwyVPJ7lM78P5+Vn1", null, null, null, 1, 0], [3, 0.5, 0.4999999999999999, 0.5, 0.5000000000000001], [1, 1, 3, 1], [1, 90, 0, 90]], [16, 1, 3, 100.00000000000003, 300.0698950617284, 1, 50, 2, [1, "d707t9sn1PzZ9fLGXXny76"], [[0, null], 6, 0], [6], [3, 3, 100, 100], [0], [0], [3, 3, 0.1, 0.3], [0], [0], [0], [0], [3, 3, 0.5, 0.5], [0], [2, 1.5], [0], [20, [17, 1, [21, [[22], [23, 1]], [[24, 255], [25, 255, 0.5591428571428572]]]]], [26, 0, [2, 1]], [27, [0], [0], [0], [0]], [28, [0], [0], [0], [2, 1]], [29, [0], [0], [0]], [30, true, 0.1, [0], [0], [0], [2, 0.1]], [31, [0], [0], [0]], [32, true, 1, 4, 1, [19, 2, [7, [0, 1], [[33, 1, 1, 1, 1], [4, 1, 1, 1, 1, 1]]], [7, [0, 1], [[4, 0.18518518518518517, 1, 1, 1, 1], [4, 1, 0.8148148148148149, 1, 0.8148148148148149, 1]]]], [0]], [34], [35, [2, 1], [0], [18, [4, 4278190335]], [6], -9], [36, 1, 1, 1, 2]]], 0, [0, 3, 1, 0, 0, 1, 0, 4, 2, 0, 0, 1, 0, -1, 2, 0, -1, 3, 0, 0, 2, 0, 0, 2, 0, 5, 3, 0, 6, 1, 9], [0, 0, 0], [-1, 7, 8], [1, 1, 0]]]]