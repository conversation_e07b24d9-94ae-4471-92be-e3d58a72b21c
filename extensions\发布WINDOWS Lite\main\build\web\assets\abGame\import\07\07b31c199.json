[1, ["4902Mjr3FDjJXGpT+ZFF6a@f9941", "52fHu7D8hGm5vLaoALoXCl", "4902Mjr3FDjJXGpT+ZFF6a@6c48a"], ["node", "root", "lbToast", "btnClose", "sprFrame", "ndRoot", "data", "_spriteFrame", "_font", "_textureSource"], [["cc.Node", ["_name", "_layer", "_children", "_components", "_prefab", "_parent", "_lpos"], 1, 2, 9, 4, 1, 5], ["cc.Node", ["_name", "_layer", "_parent", "_components", "_prefab", "_lpos"], 1, 1, 12, 4, 5], ["cc.UITransform", ["node", "__prefab", "_contentSize"], 3, 1, 4, 5], ["cc.Sprite", ["_type", "_sizeMode", "node", "__prefab"], 1, 1, 4], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["9620c0KEtNP76PS0E3rnZd6", ["maskOpacity", "node", "__prefab", "ndRoot", "sprFrame", "btnClose", "lbToast"], 2, 1, 4, 1, 1, 1, 1], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.<PERSON><PERSON>", ["node", "__prefab", "_normalColor", "_target"], 3, 1, 4, 5, 1], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_lineHeight", "_isSystemFontUsed", "_enableOutline", "node", "__prefab", "_color", "_outlineColor"], -3, 1, 4, 5, 5]], [[6, 0, 2], [8, 0, 1, 2, 3, 4, 5, 5], [2, 0, 1, 2, 1], [1, 0, 1, 2, 3, 4, 3], [5, 0, 2], [0, 0, 1, 2, 3, 4, 3], [0, 0, 1, 5, 2, 3, 4, 6, 3], [1, 0, 1, 2, 3, 4, 5, 3], [2, 0, 1, 1], [7, 0, 1, 2, 3, 4, 5, 6, 2], [3, 0, 1, 2, 3, 3], [3, 2, 3, 1], [9, 0, 1, 2, 3, 1], [10, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 7]], [[[[4, "ViewRoundToast"], [5, "ViewRoundToast", 33554432, [-8], [[2, -2, [0, "7aexdev1xH37+hoeUay14x"], [5, 806, 319]], [9, 0, -7, [0, "9bP2ZVJ2xOyqpXmQ8R9Zyu"], -6, -5, -4, -3]], [1, "a2yX3hAI1Pb7lYU97SulkA", null, null, null, -1, 0]], [6, "ndRoot", 33554432, 1, [-10, -11, -12], [[8, -9, [0, "8dXVEUZIBIL5q3zcnu5QeT"]]], [1, "1dVIdWd7NDVrfwAIiugwlP", null, null, null, 1, 0], [1, 0, 511.4119999999999, 0]], [7, "btnClose", 33554432, 2, [[[2, -13, [0, "85MYhBUUdME6rZu+uzeFlz"], [5, 1000, 2000]], [10, 1, 0, -14, [0, "d449hwjz9OB7kfvDtn6t61"]], -15], 4, 4, 1], [1, "90lGoUpe9BNbJZ2OWWlRjN", null, null, null, 1, 0], [1, 0, -427.24699999999984, 0]], [3, "sprFrame", 33554432, 2, [[[2, -16, [0, "e3d6ovnQVNBqbg0pMqedx0"], [5, 806, 319]], -17], 4, 1], [1, "73FlE1V/RKqqasLZcKOy+k", null, null, null, 1, 0]], [3, "Label", 33554432, 2, [[[2, -18, [0, "92h5FN3QFBVqZlYI7d5E6n"], [5, 104, 67]], -19], 4, 1], [1, "1cbeSMQGVCWZ2U9E0sxL9Y", null, null, null, 1, 0]], [11, 4, [0, "66r3p5uOdGALmmpk9KRL8u"]], [12, 3, [0, "90nNMKD25KVo36uJkOZ9uw"], [4, 4292269782], 3], [13, "关卡", 50, 50, 50, false, true, 5, [0, "46XrEFusxKEKfHgUe9TTLa"], [4, 4282967039], [4, 4282195425]]], 0, [0, 1, 1, 0, 0, 1, 0, 2, 8, 0, 3, 7, 0, 4, 6, 0, 5, 2, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, -1, 4, 0, -2, 3, 0, -3, 5, 0, 0, 3, 0, 0, 3, 0, -3, 7, 0, 0, 4, 0, -2, 6, 0, 0, 5, 0, -2, 8, 0, 6, 1, 19], [6, 8], [7, 8], [0, 1]], [[{"name": "game_giftmessage_frame04", "rect": {"x": 1, "y": 0, "width": 806, "height": 319}, "offset": {"x": 0, "y": 0.5}, "originalSize": {"width": 808, "height": 320}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-403, -159.5, 0, 403, -159.5, 0, -403, 159.5, 0, 403, 159.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [1, 320, 807, 320, 1, 1, 807, 1], "nuv": [0.0012376237623762376, 0.003125, 0.9987623762376238, 0.003125, 0.0012376237623762376, 1, 0.9987623762376238, 1], "minPos": {"x": -403, "y": -159.5, "z": 0}, "maxPos": {"x": 403, "y": 159.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [4], 0, [0], [9], [2]]]]