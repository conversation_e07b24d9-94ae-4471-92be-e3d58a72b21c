import * as $protobuf from "protobufjs";
import Long = require("long");
/** Namespace pb. */
export namespace pb {

    /** Properties of a BusinessInfoResPb. */
    interface IBusinessInfoResPb {

        /** BusinessInfoResPb id */
        id?: (string|null);

        /** BusinessInfoResPb busName */
        busName?: (string|null);

        /** BusinessInfoResPb activityId */
        activityId?: (string|null);

        /** BusinessInfoResPb startTime */
        startTime?: (number|Long|null);

        /** BusinessInfoResPb endTime */
        endTime?: (number|Long|null);
    }

    /** Represents a BusinessInfoResPb. */
    class BusinessInfoResPb implements IBusinessInfoResPb {

        /**
         * Constructs a new BusinessInfoResPb.
         * @param [properties] Properties to set
         */
        constructor(properties?: pb.IBusinessInfoResPb);

        /** BusinessInfoResPb id. */
        public id: string;

        /** BusinessInfoResPb busName. */
        public busName: string;

        /** BusinessInfoResPb activityId. */
        public activityId: string;

        /** BusinessInfoResPb startTime. */
        public startTime: (number|Long);

        /** BusinessInfoResPb endTime. */
        public endTime: (number|Long);

        /**
         * Creates a new BusinessInfoResPb instance using the specified properties.
         * @param [properties] Properties to set
         * @returns BusinessInfoResPb instance
         */
        public static create(properties?: pb.IBusinessInfoResPb): pb.BusinessInfoResPb;

        /**
         * Encodes the specified BusinessInfoResPb message. Does not implicitly {@link pb.BusinessInfoResPb.verify|verify} messages.
         * @param message BusinessInfoResPb message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: pb.IBusinessInfoResPb, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified BusinessInfoResPb message, length delimited. Does not implicitly {@link pb.BusinessInfoResPb.verify|verify} messages.
         * @param message BusinessInfoResPb message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: pb.IBusinessInfoResPb, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a BusinessInfoResPb message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns BusinessInfoResPb
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): pb.BusinessInfoResPb;

        /**
         * Decodes a BusinessInfoResPb message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns BusinessInfoResPb
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): pb.BusinessInfoResPb;

        /**
         * Verifies a BusinessInfoResPb message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a BusinessInfoResPb message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns BusinessInfoResPb
         */
        public static fromObject(object: { [k: string]: any }): pb.BusinessInfoResPb;

        /**
         * Creates a plain object from a BusinessInfoResPb message. Also converts values to other types if specified.
         * @param message BusinessInfoResPb
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: pb.BusinessInfoResPb, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this BusinessInfoResPb to JSON.
         * @returns JSON object
         */
        //public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for BusinessInfoResPb
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a BusinessLoginReqPb. */
    interface IBusinessLoginReqPb {

        /** BusinessLoginReqPb account */
        account?: (string|null);

        /** BusinessLoginReqPb password */
        password?: (string|null);

        /** BusinessLoginReqPb token */
        token?: (string|null);
    }

    /** Represents a BusinessLoginReqPb. */
    class BusinessLoginReqPb implements IBusinessLoginReqPb {

        /**
         * Constructs a new BusinessLoginReqPb.
         * @param [properties] Properties to set
         */
        constructor(properties?: pb.IBusinessLoginReqPb);

        /** BusinessLoginReqPb account. */
        public account: string;

        /** BusinessLoginReqPb password. */
        public password: string;

        /** BusinessLoginReqPb token. */
        public token: string;

        /**
         * Creates a new BusinessLoginReqPb instance using the specified properties.
         * @param [properties] Properties to set
         * @returns BusinessLoginReqPb instance
         */
        public static create(properties?: pb.IBusinessLoginReqPb): pb.BusinessLoginReqPb;

        /**
         * Encodes the specified BusinessLoginReqPb message. Does not implicitly {@link pb.BusinessLoginReqPb.verify|verify} messages.
         * @param message BusinessLoginReqPb message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: pb.IBusinessLoginReqPb, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified BusinessLoginReqPb message, length delimited. Does not implicitly {@link pb.BusinessLoginReqPb.verify|verify} messages.
         * @param message BusinessLoginReqPb message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: pb.IBusinessLoginReqPb, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a BusinessLoginReqPb message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns BusinessLoginReqPb
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): pb.BusinessLoginReqPb;

        /**
         * Decodes a BusinessLoginReqPb message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns BusinessLoginReqPb
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): pb.BusinessLoginReqPb;

        /**
         * Verifies a BusinessLoginReqPb message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a BusinessLoginReqPb message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns BusinessLoginReqPb
         */
        public static fromObject(object: { [k: string]: any }): pb.BusinessLoginReqPb;

        /**
         * Creates a plain object from a BusinessLoginReqPb message. Also converts values to other types if specified.
         * @param message BusinessLoginReqPb
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: pb.BusinessLoginReqPb, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this BusinessLoginReqPb to JSON.
         * @returns JSON object
         */
        //public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for BusinessLoginReqPb
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a BusinessLoginResPb. */
    interface IBusinessLoginResPb {

        /** BusinessLoginResPb token */
        token?: (string|null);

        /** BusinessLoginResPb userId */
        userId?: (string|null);

        /** BusinessLoginResPb busId */
        busId?: (string|null);
    }

    /** Represents a BusinessLoginResPb. */
    class BusinessLoginResPb implements IBusinessLoginResPb {

        /**
         * Constructs a new BusinessLoginResPb.
         * @param [properties] Properties to set
         */
        constructor(properties?: pb.IBusinessLoginResPb);

        /** BusinessLoginResPb token. */
        public token: string;

        /** BusinessLoginResPb userId. */
        public userId: string;

        /** BusinessLoginResPb busId. */
        public busId: string;

        /**
         * Creates a new BusinessLoginResPb instance using the specified properties.
         * @param [properties] Properties to set
         * @returns BusinessLoginResPb instance
         */
        public static create(properties?: pb.IBusinessLoginResPb): pb.BusinessLoginResPb;

        /**
         * Encodes the specified BusinessLoginResPb message. Does not implicitly {@link pb.BusinessLoginResPb.verify|verify} messages.
         * @param message BusinessLoginResPb message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: pb.IBusinessLoginResPb, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified BusinessLoginResPb message, length delimited. Does not implicitly {@link pb.BusinessLoginResPb.verify|verify} messages.
         * @param message BusinessLoginResPb message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: pb.IBusinessLoginResPb, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a BusinessLoginResPb message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns BusinessLoginResPb
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): pb.BusinessLoginResPb;

        /**
         * Decodes a BusinessLoginResPb message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns BusinessLoginResPb
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): pb.BusinessLoginResPb;

        /**
         * Verifies a BusinessLoginResPb message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a BusinessLoginResPb message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns BusinessLoginResPb
         */
        public static fromObject(object: { [k: string]: any }): pb.BusinessLoginResPb;

        /**
         * Creates a plain object from a BusinessLoginResPb message. Also converts values to other types if specified.
         * @param message BusinessLoginResPb
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: pb.BusinessLoginResPb, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this BusinessLoginResPb to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for BusinessLoginResPb
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a ServerTimeReqPb. */
    interface IServerTimeReqPb {

        /** ServerTimeReqPb clientTime */
        clientTime?: (string|null);
    }

    /** Represents a ServerTimeReqPb. */
    class ServerTimeReqPb implements IServerTimeReqPb {

        /**
         * Constructs a new ServerTimeReqPb.
         * @param [properties] Properties to set
         */
        constructor(properties?: pb.IServerTimeReqPb);

        /** ServerTimeReqPb clientTime. */
        public clientTime: string;

        /**
         * Creates a new ServerTimeReqPb instance using the specified properties.
         * @param [properties] Properties to set
         * @returns ServerTimeReqPb instance
         */
        public static create(properties?: pb.IServerTimeReqPb): pb.ServerTimeReqPb;

        /**
         * Encodes the specified ServerTimeReqPb message. Does not implicitly {@link pb.ServerTimeReqPb.verify|verify} messages.
         * @param message ServerTimeReqPb message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: pb.IServerTimeReqPb, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified ServerTimeReqPb message, length delimited. Does not implicitly {@link pb.ServerTimeReqPb.verify|verify} messages.
         * @param message ServerTimeReqPb message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: pb.IServerTimeReqPb, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a ServerTimeReqPb message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns ServerTimeReqPb
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): pb.ServerTimeReqPb;

        /**
         * Decodes a ServerTimeReqPb message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns ServerTimeReqPb
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): pb.ServerTimeReqPb;

        /**
         * Verifies a ServerTimeReqPb message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a ServerTimeReqPb message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns ServerTimeReqPb
         */
        public static fromObject(object: { [k: string]: any }): pb.ServerTimeReqPb;

        /**
         * Creates a plain object from a ServerTimeReqPb message. Also converts values to other types if specified.
         * @param message ServerTimeReqPb
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: pb.ServerTimeReqPb, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this ServerTimeReqPb to JSON.
         * @returns JSON object
         */
        //public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for ServerTimeReqPb
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a ServerTimeResPb. */
    interface IServerTimeResPb {

        /** ServerTimeResPb clientTime */
        clientTime?: (string|null);

        /** ServerTimeResPb serverTransmitTime */
        serverTransmitTime?: (string|null);

        /** ServerTimeResPb serverSendTime */
        serverSendTime?: (string|null);
    }

    /** Represents a ServerTimeResPb. */
    class ServerTimeResPb implements IServerTimeResPb {

        /**
         * Constructs a new ServerTimeResPb.
         * @param [properties] Properties to set
         */
        constructor(properties?: pb.IServerTimeResPb);

        /** ServerTimeResPb clientTime. */
        public clientTime: string;

        /** ServerTimeResPb serverTransmitTime. */
        public serverTransmitTime: string;

        /** ServerTimeResPb serverSendTime. */
        public serverSendTime: string;

        /**
         * Creates a new ServerTimeResPb instance using the specified properties.
         * @param [properties] Properties to set
         * @returns ServerTimeResPb instance
         */
        public static create(properties?: pb.IServerTimeResPb): pb.ServerTimeResPb;

        /**
         * Encodes the specified ServerTimeResPb message. Does not implicitly {@link pb.ServerTimeResPb.verify|verify} messages.
         * @param message ServerTimeResPb message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: pb.IServerTimeResPb, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified ServerTimeResPb message, length delimited. Does not implicitly {@link pb.ServerTimeResPb.verify|verify} messages.
         * @param message ServerTimeResPb message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: pb.IServerTimeResPb, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a ServerTimeResPb message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns ServerTimeResPb
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): pb.ServerTimeResPb;

        /**
         * Decodes a ServerTimeResPb message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns ServerTimeResPb
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): pb.ServerTimeResPb;

        /**
         * Verifies a ServerTimeResPb message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a ServerTimeResPb message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns ServerTimeResPb
         */
        public static fromObject(object: { [k: string]: any }): pb.ServerTimeResPb;

        /**
         * Creates a plain object from a ServerTimeResPb message. Also converts values to other types if specified.
         * @param message ServerTimeResPb
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: pb.ServerTimeResPb, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this ServerTimeResPb to JSON.
         * @returns JSON object
         */
        //public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for ServerTimeResPb
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a UserAddAwardRes. */
    interface IUserAddAwardRes {

        /** UserAddAwardRes awardId */
        awardId?: (string|null);

        /** UserAddAwardRes awardUrl */
        awardUrl?: (string|null);

        /** UserAddAwardRes awardName */
        awardName?: (string|null);

        /** UserAddAwardRes address */
        address?: (string|null);

        /** UserAddAwardRes code */
        code?: (string|null);

        /** UserAddAwardRes createTime */
        createTime?: (string|null);
    }

    /** Represents a UserAddAwardRes. */
    class UserAddAwardRes implements IUserAddAwardRes {

        /**
         * Constructs a new UserAddAwardRes.
         * @param [properties] Properties to set
         */
        constructor(properties?: pb.IUserAddAwardRes);

        /** UserAddAwardRes awardId. */
        public awardId: string;

        /** UserAddAwardRes awardUrl. */
        public awardUrl: string;

        /** UserAddAwardRes awardName. */
        public awardName: string;

        /** UserAddAwardRes address. */
        public address: string;

        /** UserAddAwardRes code. */
        public code: string;

        /** UserAddAwardRes createTime. */
        public createTime: string;

        /**
         * Creates a new UserAddAwardRes instance using the specified properties.
         * @param [properties] Properties to set
         * @returns UserAddAwardRes instance
         */
        public static create(properties?: pb.IUserAddAwardRes): pb.UserAddAwardRes;

        /**
         * Encodes the specified UserAddAwardRes message. Does not implicitly {@link pb.UserAddAwardRes.verify|verify} messages.
         * @param message UserAddAwardRes message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: pb.IUserAddAwardRes, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified UserAddAwardRes message, length delimited. Does not implicitly {@link pb.UserAddAwardRes.verify|verify} messages.
         * @param message UserAddAwardRes message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: pb.IUserAddAwardRes, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a UserAddAwardRes message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns UserAddAwardRes
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): pb.UserAddAwardRes;

        /**
         * Decodes a UserAddAwardRes message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns UserAddAwardRes
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): pb.UserAddAwardRes;

        /**
         * Verifies a UserAddAwardRes message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a UserAddAwardRes message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns UserAddAwardRes
         */
        public static fromObject(object: { [k: string]: any }): pb.UserAddAwardRes;

        /**
         * Creates a plain object from a UserAddAwardRes message. Also converts values to other types if specified.
         * @param message UserAddAwardRes
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: pb.UserAddAwardRes, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this UserAddAwardRes to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for UserAddAwardRes
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a UserAwardHistory. */
    interface IUserAwardHistory {

        /** UserAwardHistory items */
        items?: (pb.IUserAddAwardRes[]|null);
    }

    /** Represents a UserAwardHistory. */
    class UserAwardHistory implements IUserAwardHistory {

        /**
         * Constructs a new UserAwardHistory.
         * @param [properties] Properties to set
         */
        constructor(properties?: pb.IUserAwardHistory);

        /** UserAwardHistory items. */
        public items: pb.IUserAddAwardRes[];

        /**
         * Creates a new UserAwardHistory instance using the specified properties.
         * @param [properties] Properties to set
         * @returns UserAwardHistory instance
         */
        public static create(properties?: pb.IUserAwardHistory): pb.UserAwardHistory;

        /**
         * Encodes the specified UserAwardHistory message. Does not implicitly {@link pb.UserAwardHistory.verify|verify} messages.
         * @param message UserAwardHistory message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: pb.IUserAwardHistory, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified UserAwardHistory message, length delimited. Does not implicitly {@link pb.UserAwardHistory.verify|verify} messages.
         * @param message UserAwardHistory message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: pb.IUserAwardHistory, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a UserAwardHistory message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns UserAwardHistory
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): pb.UserAwardHistory;

        /**
         * Decodes a UserAwardHistory message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns UserAwardHistory
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): pb.UserAwardHistory;

        /**
         * Verifies a UserAwardHistory message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a UserAwardHistory message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns UserAwardHistory
         */
        public static fromObject(object: { [k: string]: any }): pb.UserAwardHistory;

        /**
         * Creates a plain object from a UserAwardHistory message. Also converts values to other types if specified.
         * @param message UserAwardHistory
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: pb.UserAwardHistory, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this UserAwardHistory to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for UserAwardHistory
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a UserGameHistory. */
    interface IUserGameHistory {

        /** UserGameHistory item */
        item?: (pb.IUserGameHistoryItem[]|null);
    }

    /** Represents a UserGameHistory. */
    class UserGameHistory implements IUserGameHistory {

        /**
         * Constructs a new UserGameHistory.
         * @param [properties] Properties to set
         */
        constructor(properties?: pb.IUserGameHistory);

        /** UserGameHistory item. */
        public item: pb.IUserGameHistoryItem[];

        /**
         * Creates a new UserGameHistory instance using the specified properties.
         * @param [properties] Properties to set
         * @returns UserGameHistory instance
         */
        public static create(properties?: pb.IUserGameHistory): pb.UserGameHistory;

        /**
         * Encodes the specified UserGameHistory message. Does not implicitly {@link pb.UserGameHistory.verify|verify} messages.
         * @param message UserGameHistory message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: pb.IUserGameHistory, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified UserGameHistory message, length delimited. Does not implicitly {@link pb.UserGameHistory.verify|verify} messages.
         * @param message UserGameHistory message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: pb.IUserGameHistory, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a UserGameHistory message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns UserGameHistory
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): pb.UserGameHistory;

        /**
         * Decodes a UserGameHistory message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns UserGameHistory
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): pb.UserGameHistory;

        /**
         * Verifies a UserGameHistory message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a UserGameHistory message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns UserGameHistory
         */
        public static fromObject(object: { [k: string]: any }): pb.UserGameHistory;

        /**
         * Creates a plain object from a UserGameHistory message. Also converts values to other types if specified.
         * @param message UserGameHistory
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: pb.UserGameHistory, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this UserGameHistory to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for UserGameHistory
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a UserGameHistoryItem. */
    interface IUserGameHistoryItem {

        /** UserGameHistoryItem rank */
        rank?: (string|null);

        /** UserGameHistoryItem businessName */
        businessName?: (string|null);

        /** UserGameHistoryItem createTime */
        createTime?: (string|null);
    }

    /** Represents a UserGameHistoryItem. */
    class UserGameHistoryItem implements IUserGameHistoryItem {

        /**
         * Constructs a new UserGameHistoryItem.
         * @param [properties] Properties to set
         */
        constructor(properties?: pb.IUserGameHistoryItem);

        /** UserGameHistoryItem rank. */
        public rank: string;

        /** UserGameHistoryItem businessName. */
        public businessName: string;

        /** UserGameHistoryItem createTime. */
        public createTime: string;

        /**
         * Creates a new UserGameHistoryItem instance using the specified properties.
         * @param [properties] Properties to set
         * @returns UserGameHistoryItem instance
         */
        public static create(properties?: pb.IUserGameHistoryItem): pb.UserGameHistoryItem;

        /**
         * Encodes the specified UserGameHistoryItem message. Does not implicitly {@link pb.UserGameHistoryItem.verify|verify} messages.
         * @param message UserGameHistoryItem message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: pb.IUserGameHistoryItem, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified UserGameHistoryItem message, length delimited. Does not implicitly {@link pb.UserGameHistoryItem.verify|verify} messages.
         * @param message UserGameHistoryItem message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: pb.IUserGameHistoryItem, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a UserGameHistoryItem message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns UserGameHistoryItem
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): pb.UserGameHistoryItem;

        /**
         * Decodes a UserGameHistoryItem message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns UserGameHistoryItem
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): pb.UserGameHistoryItem;

        /**
         * Verifies a UserGameHistoryItem message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a UserGameHistoryItem message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns UserGameHistoryItem
         */
        public static fromObject(object: { [k: string]: any }): pb.UserGameHistoryItem;

        /**
         * Creates a plain object from a UserGameHistoryItem message. Also converts values to other types if specified.
         * @param message UserGameHistoryItem
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: pb.UserGameHistoryItem, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this UserGameHistoryItem to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for UserGameHistoryItem
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a UserLoginReqPb. */
    interface IUserLoginReqPb {

        /** UserLoginReqPb token */
        token?: (string|null);

        /** UserLoginReqPb roomId */
        roomId?: (string|null);
    }

    /** Represents a UserLoginReqPb. */
    class UserLoginReqPb implements IUserLoginReqPb {

        /**
         * Constructs a new UserLoginReqPb.
         * @param [properties] Properties to set
         */
        constructor(properties?: pb.IUserLoginReqPb);

        /** UserLoginReqPb token. */
        public token: string;

        /** UserLoginReqPb roomId. */
        public roomId: string;

        /**
         * Creates a new UserLoginReqPb instance using the specified properties.
         * @param [properties] Properties to set
         * @returns UserLoginReqPb instance
         */
        public static create(properties?: pb.IUserLoginReqPb): pb.UserLoginReqPb;

        /**
         * Encodes the specified UserLoginReqPb message. Does not implicitly {@link pb.UserLoginReqPb.verify|verify} messages.
         * @param message UserLoginReqPb message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: pb.IUserLoginReqPb, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified UserLoginReqPb message, length delimited. Does not implicitly {@link pb.UserLoginReqPb.verify|verify} messages.
         * @param message UserLoginReqPb message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: pb.IUserLoginReqPb, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a UserLoginReqPb message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns UserLoginReqPb
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): pb.UserLoginReqPb;

        /**
         * Decodes a UserLoginReqPb message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns UserLoginReqPb
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): pb.UserLoginReqPb;

        /**
         * Verifies a UserLoginReqPb message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a UserLoginReqPb message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns UserLoginReqPb
         */
        public static fromObject(object: { [k: string]: any }): pb.UserLoginReqPb;

        /**
         * Creates a plain object from a UserLoginReqPb message. Also converts values to other types if specified.
         * @param message UserLoginReqPb
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: pb.UserLoginReqPb, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this UserLoginReqPb to JSON.
         * @returns JSON object
         */
       // public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for UserLoginReqPb
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a UserLoginResPb. */
    interface IUserLoginResPb {

        /** UserLoginResPb result */
        result?: (boolean|null);
    }

    /** Represents a UserLoginResPb. */
    class UserLoginResPb implements IUserLoginResPb {

        /**
         * Constructs a new UserLoginResPb.
         * @param [properties] Properties to set
         */
        constructor(properties?: pb.IUserLoginResPb);

        /** UserLoginResPb result. */
        public result: boolean;

        /**
         * Creates a new UserLoginResPb instance using the specified properties.
         * @param [properties] Properties to set
         * @returns UserLoginResPb instance
         */
        public static create(properties?: pb.IUserLoginResPb): pb.UserLoginResPb;

        /**
         * Encodes the specified UserLoginResPb message. Does not implicitly {@link pb.UserLoginResPb.verify|verify} messages.
         * @param message UserLoginResPb message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: pb.IUserLoginResPb, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified UserLoginResPb message, length delimited. Does not implicitly {@link pb.UserLoginResPb.verify|verify} messages.
         * @param message UserLoginResPb message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: pb.IUserLoginResPb, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a UserLoginResPb message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns UserLoginResPb
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): pb.UserLoginResPb;

        /**
         * Decodes a UserLoginResPb message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns UserLoginResPb
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): pb.UserLoginResPb;

        /**
         * Verifies a UserLoginResPb message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a UserLoginResPb message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns UserLoginResPb
         */
        public static fromObject(object: { [k: string]: any }): pb.UserLoginResPb;

        /**
         * Creates a plain object from a UserLoginResPb message. Also converts values to other types if specified.
         * @param message UserLoginResPb
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: pb.UserLoginResPb, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this UserLoginResPb to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for UserLoginResPb
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a RoomStatusPushPb. */
    interface IRoomStatusPushPb {

        /** RoomStatusPushPb json */
        json?: (string|null);
    }

    /** Represents a RoomStatusPushPb. */
    class RoomStatusPushPb implements IRoomStatusPushPb {

        /**
         * Constructs a new RoomStatusPushPb.
         * @param [properties] Properties to set
         */
        constructor(properties?: pb.IRoomStatusPushPb);

        /** RoomStatusPushPb json. */
        public json: string;

        /**
         * Creates a new RoomStatusPushPb instance using the specified properties.
         * @param [properties] Properties to set
         * @returns RoomStatusPushPb instance
         */
        public static create(properties?: pb.IRoomStatusPushPb): pb.RoomStatusPushPb;

        /**
         * Encodes the specified RoomStatusPushPb message. Does not implicitly {@link pb.RoomStatusPushPb.verify|verify} messages.
         * @param message RoomStatusPushPb message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: pb.IRoomStatusPushPb, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified RoomStatusPushPb message, length delimited. Does not implicitly {@link pb.RoomStatusPushPb.verify|verify} messages.
         * @param message RoomStatusPushPb message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: pb.IRoomStatusPushPb, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a RoomStatusPushPb message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns RoomStatusPushPb
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): pb.RoomStatusPushPb;

        /**
         * Decodes a RoomStatusPushPb message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns RoomStatusPushPb
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): pb.RoomStatusPushPb;

        /**
         * Verifies a RoomStatusPushPb message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a RoomStatusPushPb message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns RoomStatusPushPb
         */
        public static fromObject(object: { [k: string]: any }): pb.RoomStatusPushPb;

        /**
         * Creates a plain object from a RoomStatusPushPb message. Also converts values to other types if specified.
         * @param message RoomStatusPushPb
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: pb.RoomStatusPushPb, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this RoomStatusPushPb to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for RoomStatusPushPb
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }
}
