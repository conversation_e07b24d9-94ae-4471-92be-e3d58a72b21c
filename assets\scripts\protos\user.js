/*eslint-disable block-scoped-var, id-length, no-control-regex, no-magic-numbers, no-prototype-builtins, no-redeclare, no-shadow, no-var, sort-vars*/
"use strict";

var $protobuf = require("protobufjs/minimal");

// Common aliases
var $Reader = $protobuf.Reader, $Writer = $protobuf.Writer, $util = $protobuf.util;

// Exported root namespace
var $root = $protobuf.roots["default"] || ($protobuf.roots["default"] = {});

$root.pb = (function() {

    /**
     * Namespace pb.
     * @exports pb
     * @namespace
     */
    var pb = {};

    pb.BusinessInfoResPb = (function() {

        /**
         * Properties of a BusinessInfoResPb.
         * @memberof pb
         * @interface IBusinessInfoResPb
         * @property {string|null} [id] BusinessInfoResPb id
         * @property {string|null} [busName] BusinessInfoResPb busName
         * @property {string|null} [activityId] BusinessInfoResPb activityId
         * @property {number|Long|null} [startTime] BusinessInfoResPb startTime
         * @property {number|Long|null} [endTime] BusinessInfoResPb endTime
         */

        /**
         * Constructs a new BusinessInfoResPb.
         * @memberof pb
         * @classdesc Represents a BusinessInfoResPb.
         * @implements IBusinessInfoResPb
         * @constructor
         * @param {pb.IBusinessInfoResPb=} [properties] Properties to set
         */
        function BusinessInfoResPb(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * BusinessInfoResPb id.
         * @member {string} id
         * @memberof pb.BusinessInfoResPb
         * @instance
         */
        BusinessInfoResPb.prototype.id = "";

        /**
         * BusinessInfoResPb busName.
         * @member {string} busName
         * @memberof pb.BusinessInfoResPb
         * @instance
         */
        BusinessInfoResPb.prototype.busName = "";

        /**
         * BusinessInfoResPb activityId.
         * @member {string} activityId
         * @memberof pb.BusinessInfoResPb
         * @instance
         */
        BusinessInfoResPb.prototype.activityId = "";

        /**
         * BusinessInfoResPb startTime.
         * @member {number|Long} startTime
         * @memberof pb.BusinessInfoResPb
         * @instance
         */
        BusinessInfoResPb.prototype.startTime = $util.Long ? $util.Long.fromBits(0,0,false) : 0;

        /**
         * BusinessInfoResPb endTime.
         * @member {number|Long} endTime
         * @memberof pb.BusinessInfoResPb
         * @instance
         */
        BusinessInfoResPb.prototype.endTime = $util.Long ? $util.Long.fromBits(0,0,false) : 0;

        /**
         * Creates a new BusinessInfoResPb instance using the specified properties.
         * @function create
         * @memberof pb.BusinessInfoResPb
         * @static
         * @param {pb.IBusinessInfoResPb=} [properties] Properties to set
         * @returns {pb.BusinessInfoResPb} BusinessInfoResPb instance
         */
        BusinessInfoResPb.create = function create(properties) {
            return new BusinessInfoResPb(properties);
        };

        /**
         * Encodes the specified BusinessInfoResPb message. Does not implicitly {@link pb.BusinessInfoResPb.verify|verify} messages.
         * @function encode
         * @memberof pb.BusinessInfoResPb
         * @static
         * @param {pb.IBusinessInfoResPb} message BusinessInfoResPb message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        BusinessInfoResPb.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.id != null && Object.hasOwnProperty.call(message, "id"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.id);
            if (message.busName != null && Object.hasOwnProperty.call(message, "busName"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.busName);
            if (message.activityId != null && Object.hasOwnProperty.call(message, "activityId"))
                writer.uint32(/* id 3, wireType 2 =*/26).string(message.activityId);
            if (message.startTime != null && Object.hasOwnProperty.call(message, "startTime"))
                writer.uint32(/* id 4, wireType 0 =*/32).int64(message.startTime);
            if (message.endTime != null && Object.hasOwnProperty.call(message, "endTime"))
                writer.uint32(/* id 5, wireType 0 =*/40).int64(message.endTime);
            return writer;
        };

        /**
         * Encodes the specified BusinessInfoResPb message, length delimited. Does not implicitly {@link pb.BusinessInfoResPb.verify|verify} messages.
         * @function encodeDelimited
         * @memberof pb.BusinessInfoResPb
         * @static
         * @param {pb.IBusinessInfoResPb} message BusinessInfoResPb message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        BusinessInfoResPb.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a BusinessInfoResPb message from the specified reader or buffer.
         * @function decode
         * @memberof pb.BusinessInfoResPb
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {pb.BusinessInfoResPb} BusinessInfoResPb
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        BusinessInfoResPb.decode = function decode(reader, length) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.pb.BusinessInfoResPb();
            while (reader.pos < end) {
                var tag = reader.uint32();
                switch (tag >>> 3) {
                case 1: {
                        message.id = reader.string();
                        break;
                    }
                case 2: {
                        message.busName = reader.string();
                        break;
                    }
                case 3: {
                        message.activityId = reader.string();
                        break;
                    }
                case 4: {
                        message.startTime = reader.int64();
                        break;
                    }
                case 5: {
                        message.endTime = reader.int64();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a BusinessInfoResPb message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof pb.BusinessInfoResPb
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {pb.BusinessInfoResPb} BusinessInfoResPb
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        BusinessInfoResPb.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a BusinessInfoResPb message.
         * @function verify
         * @memberof pb.BusinessInfoResPb
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        BusinessInfoResPb.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.id != null && message.hasOwnProperty("id"))
                if (!$util.isString(message.id))
                    return "id: string expected";
            if (message.busName != null && message.hasOwnProperty("busName"))
                if (!$util.isString(message.busName))
                    return "busName: string expected";
            if (message.activityId != null && message.hasOwnProperty("activityId"))
                if (!$util.isString(message.activityId))
                    return "activityId: string expected";
            if (message.startTime != null && message.hasOwnProperty("startTime"))
                if (!$util.isInteger(message.startTime) && !(message.startTime && $util.isInteger(message.startTime.low) && $util.isInteger(message.startTime.high)))
                    return "startTime: integer|Long expected";
            if (message.endTime != null && message.hasOwnProperty("endTime"))
                if (!$util.isInteger(message.endTime) && !(message.endTime && $util.isInteger(message.endTime.low) && $util.isInteger(message.endTime.high)))
                    return "endTime: integer|Long expected";
            return null;
        };

        /**
         * Creates a BusinessInfoResPb message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof pb.BusinessInfoResPb
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {pb.BusinessInfoResPb} BusinessInfoResPb
         */
        BusinessInfoResPb.fromObject = function fromObject(object) {
            if (object instanceof $root.pb.BusinessInfoResPb)
                return object;
            var message = new $root.pb.BusinessInfoResPb();
            if (object.id != null)
                message.id = String(object.id);
            if (object.busName != null)
                message.busName = String(object.busName);
            if (object.activityId != null)
                message.activityId = String(object.activityId);
            if (object.startTime != null)
                if ($util.Long)
                    (message.startTime = $util.Long.fromValue(object.startTime)).unsigned = false;
                else if (typeof object.startTime === "string")
                    message.startTime = parseInt(object.startTime, 10);
                else if (typeof object.startTime === "number")
                    message.startTime = object.startTime;
                else if (typeof object.startTime === "object")
                    message.startTime = new $util.LongBits(object.startTime.low >>> 0, object.startTime.high >>> 0).toNumber();
            if (object.endTime != null)
                if ($util.Long)
                    (message.endTime = $util.Long.fromValue(object.endTime)).unsigned = false;
                else if (typeof object.endTime === "string")
                    message.endTime = parseInt(object.endTime, 10);
                else if (typeof object.endTime === "number")
                    message.endTime = object.endTime;
                else if (typeof object.endTime === "object")
                    message.endTime = new $util.LongBits(object.endTime.low >>> 0, object.endTime.high >>> 0).toNumber();
            return message;
        };

        /**
         * Creates a plain object from a BusinessInfoResPb message. Also converts values to other types if specified.
         * @function toObject
         * @memberof pb.BusinessInfoResPb
         * @static
         * @param {pb.BusinessInfoResPb} message BusinessInfoResPb
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        BusinessInfoResPb.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.id = "";
                object.busName = "";
                object.activityId = "";
                if ($util.Long) {
                    var long = new $util.Long(0, 0, false);
                    object.startTime = options.longs === String ? long.toString() : options.longs === Number ? long.toNumber() : long;
                } else
                    object.startTime = options.longs === String ? "0" : 0;
                if ($util.Long) {
                    var long = new $util.Long(0, 0, false);
                    object.endTime = options.longs === String ? long.toString() : options.longs === Number ? long.toNumber() : long;
                } else
                    object.endTime = options.longs === String ? "0" : 0;
            }
            if (message.id != null && message.hasOwnProperty("id"))
                object.id = message.id;
            if (message.busName != null && message.hasOwnProperty("busName"))
                object.busName = message.busName;
            if (message.activityId != null && message.hasOwnProperty("activityId"))
                object.activityId = message.activityId;
            if (message.startTime != null && message.hasOwnProperty("startTime"))
                if (typeof message.startTime === "number")
                    object.startTime = options.longs === String ? String(message.startTime) : message.startTime;
                else
                    object.startTime = options.longs === String ? $util.Long.prototype.toString.call(message.startTime) : options.longs === Number ? new $util.LongBits(message.startTime.low >>> 0, message.startTime.high >>> 0).toNumber() : message.startTime;
            if (message.endTime != null && message.hasOwnProperty("endTime"))
                if (typeof message.endTime === "number")
                    object.endTime = options.longs === String ? String(message.endTime) : message.endTime;
                else
                    object.endTime = options.longs === String ? $util.Long.prototype.toString.call(message.endTime) : options.longs === Number ? new $util.LongBits(message.endTime.low >>> 0, message.endTime.high >>> 0).toNumber() : message.endTime;
            return object;
        };

        /**
         * Converts this BusinessInfoResPb to JSON.
         * @function toJSON
         * @memberof pb.BusinessInfoResPb
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        BusinessInfoResPb.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for BusinessInfoResPb
         * @function getTypeUrl
         * @memberof pb.BusinessInfoResPb
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        BusinessInfoResPb.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.BusinessInfoResPb";
        };

        return BusinessInfoResPb;
    })();

    pb.BusinessLoginReqPb = (function() {

        /**
         * Properties of a BusinessLoginReqPb.
         * @memberof pb
         * @interface IBusinessLoginReqPb
         * @property {string|null} [account] BusinessLoginReqPb account
         * @property {string|null} [password] BusinessLoginReqPb password
         * @property {string|null} [token] BusinessLoginReqPb token
         */

        /**
         * Constructs a new BusinessLoginReqPb.
         * @memberof pb
         * @classdesc Represents a BusinessLoginReqPb.
         * @implements IBusinessLoginReqPb
         * @constructor
         * @param {pb.IBusinessLoginReqPb=} [properties] Properties to set
         */
        function BusinessLoginReqPb(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * BusinessLoginReqPb account.
         * @member {string} account
         * @memberof pb.BusinessLoginReqPb
         * @instance
         */
        BusinessLoginReqPb.prototype.account = "";

        /**
         * BusinessLoginReqPb password.
         * @member {string} password
         * @memberof pb.BusinessLoginReqPb
         * @instance
         */
        BusinessLoginReqPb.prototype.password = "";

        /**
         * BusinessLoginReqPb token.
         * @member {string} token
         * @memberof pb.BusinessLoginReqPb
         * @instance
         */
        BusinessLoginReqPb.prototype.token = "";

        /**
         * Creates a new BusinessLoginReqPb instance using the specified properties.
         * @function create
         * @memberof pb.BusinessLoginReqPb
         * @static
         * @param {pb.IBusinessLoginReqPb=} [properties] Properties to set
         * @returns {pb.BusinessLoginReqPb} BusinessLoginReqPb instance
         */
        BusinessLoginReqPb.create = function create(properties) {
            return new BusinessLoginReqPb(properties);
        };

        /**
         * Encodes the specified BusinessLoginReqPb message. Does not implicitly {@link pb.BusinessLoginReqPb.verify|verify} messages.
         * @function encode
         * @memberof pb.BusinessLoginReqPb
         * @static
         * @param {pb.IBusinessLoginReqPb} message BusinessLoginReqPb message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        BusinessLoginReqPb.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.account != null && Object.hasOwnProperty.call(message, "account"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.account);
            if (message.password != null && Object.hasOwnProperty.call(message, "password"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.password);
            if (message.token != null && Object.hasOwnProperty.call(message, "token"))
                writer.uint32(/* id 3, wireType 2 =*/26).string(message.token);
            return writer;
        };

        /**
         * Encodes the specified BusinessLoginReqPb message, length delimited. Does not implicitly {@link pb.BusinessLoginReqPb.verify|verify} messages.
         * @function encodeDelimited
         * @memberof pb.BusinessLoginReqPb
         * @static
         * @param {pb.IBusinessLoginReqPb} message BusinessLoginReqPb message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        BusinessLoginReqPb.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a BusinessLoginReqPb message from the specified reader or buffer.
         * @function decode
         * @memberof pb.BusinessLoginReqPb
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {pb.BusinessLoginReqPb} BusinessLoginReqPb
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        BusinessLoginReqPb.decode = function decode(reader, length) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.pb.BusinessLoginReqPb();
            while (reader.pos < end) {
                var tag = reader.uint32();
                switch (tag >>> 3) {
                case 1: {
                        message.account = reader.string();
                        break;
                    }
                case 2: {
                        message.password = reader.string();
                        break;
                    }
                case 3: {
                        message.token = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a BusinessLoginReqPb message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof pb.BusinessLoginReqPb
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {pb.BusinessLoginReqPb} BusinessLoginReqPb
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        BusinessLoginReqPb.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a BusinessLoginReqPb message.
         * @function verify
         * @memberof pb.BusinessLoginReqPb
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        BusinessLoginReqPb.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.account != null && message.hasOwnProperty("account"))
                if (!$util.isString(message.account))
                    return "account: string expected";
            if (message.password != null && message.hasOwnProperty("password"))
                if (!$util.isString(message.password))
                    return "password: string expected";
            if (message.token != null && message.hasOwnProperty("token"))
                if (!$util.isString(message.token))
                    return "token: string expected";
            return null;
        };

        /**
         * Creates a BusinessLoginReqPb message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof pb.BusinessLoginReqPb
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {pb.BusinessLoginReqPb} BusinessLoginReqPb
         */
        BusinessLoginReqPb.fromObject = function fromObject(object) {
            if (object instanceof $root.pb.BusinessLoginReqPb)
                return object;
            var message = new $root.pb.BusinessLoginReqPb();
            if (object.account != null)
                message.account = String(object.account);
            if (object.password != null)
                message.password = String(object.password);
            if (object.token != null)
                message.token = String(object.token);
            return message;
        };

        /**
         * Creates a plain object from a BusinessLoginReqPb message. Also converts values to other types if specified.
         * @function toObject
         * @memberof pb.BusinessLoginReqPb
         * @static
         * @param {pb.BusinessLoginReqPb} message BusinessLoginReqPb
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        BusinessLoginReqPb.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.account = "";
                object.password = "";
                object.token = "";
            }
            if (message.account != null && message.hasOwnProperty("account"))
                object.account = message.account;
            if (message.password != null && message.hasOwnProperty("password"))
                object.password = message.password;
            if (message.token != null && message.hasOwnProperty("token"))
                object.token = message.token;
            return object;
        };

        /**
         * Converts this BusinessLoginReqPb to JSON.
         * @function toJSON
         * @memberof pb.BusinessLoginReqPb
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        BusinessLoginReqPb.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for BusinessLoginReqPb
         * @function getTypeUrl
         * @memberof pb.BusinessLoginReqPb
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        BusinessLoginReqPb.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.BusinessLoginReqPb";
        };

        return BusinessLoginReqPb;
    })();

    pb.BusinessLoginResPb = (function() {

        /**
         * Properties of a BusinessLoginResPb.
         * @memberof pb
         * @interface IBusinessLoginResPb
         * @property {string|null} [token] BusinessLoginResPb token
         * @property {string|null} [userId] BusinessLoginResPb userId
         * @property {string|null} [busId] BusinessLoginResPb busId
         */

        /**
         * Constructs a new BusinessLoginResPb.
         * @memberof pb
         * @classdesc Represents a BusinessLoginResPb.
         * @implements IBusinessLoginResPb
         * @constructor
         * @param {pb.IBusinessLoginResPb=} [properties] Properties to set
         */
        function BusinessLoginResPb(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * BusinessLoginResPb token.
         * @member {string} token
         * @memberof pb.BusinessLoginResPb
         * @instance
         */
        BusinessLoginResPb.prototype.token = "";

        /**
         * BusinessLoginResPb userId.
         * @member {string} userId
         * @memberof pb.BusinessLoginResPb
         * @instance
         */
        BusinessLoginResPb.prototype.userId = "";

        /**
         * BusinessLoginResPb busId.
         * @member {string} busId
         * @memberof pb.BusinessLoginResPb
         * @instance
         */
        BusinessLoginResPb.prototype.busId = "";

        /**
         * Creates a new BusinessLoginResPb instance using the specified properties.
         * @function create
         * @memberof pb.BusinessLoginResPb
         * @static
         * @param {pb.IBusinessLoginResPb=} [properties] Properties to set
         * @returns {pb.BusinessLoginResPb} BusinessLoginResPb instance
         */
        BusinessLoginResPb.create = function create(properties) {
            return new BusinessLoginResPb(properties);
        };

        /**
         * Encodes the specified BusinessLoginResPb message. Does not implicitly {@link pb.BusinessLoginResPb.verify|verify} messages.
         * @function encode
         * @memberof pb.BusinessLoginResPb
         * @static
         * @param {pb.IBusinessLoginResPb} message BusinessLoginResPb message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        BusinessLoginResPb.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.token != null && Object.hasOwnProperty.call(message, "token"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.token);
            if (message.userId != null && Object.hasOwnProperty.call(message, "userId"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.userId);
            if (message.busId != null && Object.hasOwnProperty.call(message, "busId"))
                writer.uint32(/* id 3, wireType 2 =*/26).string(message.busId);
            return writer;
        };

        /**
         * Encodes the specified BusinessLoginResPb message, length delimited. Does not implicitly {@link pb.BusinessLoginResPb.verify|verify} messages.
         * @function encodeDelimited
         * @memberof pb.BusinessLoginResPb
         * @static
         * @param {pb.IBusinessLoginResPb} message BusinessLoginResPb message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        BusinessLoginResPb.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a BusinessLoginResPb message from the specified reader or buffer.
         * @function decode
         * @memberof pb.BusinessLoginResPb
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {pb.BusinessLoginResPb} BusinessLoginResPb
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        BusinessLoginResPb.decode = function decode(reader, length) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.pb.BusinessLoginResPb();
            while (reader.pos < end) {
                var tag = reader.uint32();
                switch (tag >>> 3) {
                case 1: {
                        message.token = reader.string();
                        break;
                    }
                case 2: {
                        message.userId = reader.string();
                        break;
                    }
                case 3: {
                        message.busId = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a BusinessLoginResPb message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof pb.BusinessLoginResPb
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {pb.BusinessLoginResPb} BusinessLoginResPb
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        BusinessLoginResPb.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a BusinessLoginResPb message.
         * @function verify
         * @memberof pb.BusinessLoginResPb
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        BusinessLoginResPb.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.token != null && message.hasOwnProperty("token"))
                if (!$util.isString(message.token))
                    return "token: string expected";
            if (message.userId != null && message.hasOwnProperty("userId"))
                if (!$util.isString(message.userId))
                    return "userId: string expected";
            if (message.busId != null && message.hasOwnProperty("busId"))
                if (!$util.isString(message.busId))
                    return "busId: string expected";
            return null;
        };

        /**
         * Creates a BusinessLoginResPb message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof pb.BusinessLoginResPb
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {pb.BusinessLoginResPb} BusinessLoginResPb
         */
        BusinessLoginResPb.fromObject = function fromObject(object) {
            if (object instanceof $root.pb.BusinessLoginResPb)
                return object;
            var message = new $root.pb.BusinessLoginResPb();
            if (object.token != null)
                message.token = String(object.token);
            if (object.userId != null)
                message.userId = String(object.userId);
            if (object.busId != null)
                message.busId = String(object.busId);
            return message;
        };

        /**
         * Creates a plain object from a BusinessLoginResPb message. Also converts values to other types if specified.
         * @function toObject
         * @memberof pb.BusinessLoginResPb
         * @static
         * @param {pb.BusinessLoginResPb} message BusinessLoginResPb
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        BusinessLoginResPb.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.token = "";
                object.userId = "";
                object.busId = "";
            }
            if (message.token != null && message.hasOwnProperty("token"))
                object.token = message.token;
            if (message.userId != null && message.hasOwnProperty("userId"))
                object.userId = message.userId;
            if (message.busId != null && message.hasOwnProperty("busId"))
                object.busId = message.busId;
            return object;
        };

        /**
         * Converts this BusinessLoginResPb to JSON.
         * @function toJSON
         * @memberof pb.BusinessLoginResPb
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        BusinessLoginResPb.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for BusinessLoginResPb
         * @function getTypeUrl
         * @memberof pb.BusinessLoginResPb
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        BusinessLoginResPb.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.BusinessLoginResPb";
        };

        return BusinessLoginResPb;
    })();

    pb.ServerTimeReqPb = (function() {

        /**
         * Properties of a ServerTimeReqPb.
         * @memberof pb
         * @interface IServerTimeReqPb
         * @property {string|null} [clientTime] ServerTimeReqPb clientTime
         */

        /**
         * Constructs a new ServerTimeReqPb.
         * @memberof pb
         * @classdesc Represents a ServerTimeReqPb.
         * @implements IServerTimeReqPb
         * @constructor
         * @param {pb.IServerTimeReqPb=} [properties] Properties to set
         */
        function ServerTimeReqPb(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * ServerTimeReqPb clientTime.
         * @member {string} clientTime
         * @memberof pb.ServerTimeReqPb
         * @instance
         */
        ServerTimeReqPb.prototype.clientTime = "";

        /**
         * Creates a new ServerTimeReqPb instance using the specified properties.
         * @function create
         * @memberof pb.ServerTimeReqPb
         * @static
         * @param {pb.IServerTimeReqPb=} [properties] Properties to set
         * @returns {pb.ServerTimeReqPb} ServerTimeReqPb instance
         */
        ServerTimeReqPb.create = function create(properties) {
            return new ServerTimeReqPb(properties);
        };

        /**
         * Encodes the specified ServerTimeReqPb message. Does not implicitly {@link pb.ServerTimeReqPb.verify|verify} messages.
         * @function encode
         * @memberof pb.ServerTimeReqPb
         * @static
         * @param {pb.IServerTimeReqPb} message ServerTimeReqPb message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ServerTimeReqPb.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.clientTime != null && Object.hasOwnProperty.call(message, "clientTime"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.clientTime);
            return writer;
        };

        /**
         * Encodes the specified ServerTimeReqPb message, length delimited. Does not implicitly {@link pb.ServerTimeReqPb.verify|verify} messages.
         * @function encodeDelimited
         * @memberof pb.ServerTimeReqPb
         * @static
         * @param {pb.IServerTimeReqPb} message ServerTimeReqPb message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ServerTimeReqPb.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a ServerTimeReqPb message from the specified reader or buffer.
         * @function decode
         * @memberof pb.ServerTimeReqPb
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {pb.ServerTimeReqPb} ServerTimeReqPb
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ServerTimeReqPb.decode = function decode(reader, length) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.pb.ServerTimeReqPb();
            while (reader.pos < end) {
                var tag = reader.uint32();
                switch (tag >>> 3) {
                case 1: {
                        message.clientTime = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a ServerTimeReqPb message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof pb.ServerTimeReqPb
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {pb.ServerTimeReqPb} ServerTimeReqPb
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ServerTimeReqPb.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a ServerTimeReqPb message.
         * @function verify
         * @memberof pb.ServerTimeReqPb
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        ServerTimeReqPb.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.clientTime != null && message.hasOwnProperty("clientTime"))
                if (!$util.isString(message.clientTime))
                    return "clientTime: string expected";
            return null;
        };

        /**
         * Creates a ServerTimeReqPb message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof pb.ServerTimeReqPb
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {pb.ServerTimeReqPb} ServerTimeReqPb
         */
        ServerTimeReqPb.fromObject = function fromObject(object) {
            if (object instanceof $root.pb.ServerTimeReqPb)
                return object;
            var message = new $root.pb.ServerTimeReqPb();
            if (object.clientTime != null)
                message.clientTime = String(object.clientTime);
            return message;
        };

        /**
         * Creates a plain object from a ServerTimeReqPb message. Also converts values to other types if specified.
         * @function toObject
         * @memberof pb.ServerTimeReqPb
         * @static
         * @param {pb.ServerTimeReqPb} message ServerTimeReqPb
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        ServerTimeReqPb.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults)
                object.clientTime = "";
            if (message.clientTime != null && message.hasOwnProperty("clientTime"))
                object.clientTime = message.clientTime;
            return object;
        };

        /**
         * Converts this ServerTimeReqPb to JSON.
         * @function toJSON
         * @memberof pb.ServerTimeReqPb
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        ServerTimeReqPb.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for ServerTimeReqPb
         * @function getTypeUrl
         * @memberof pb.ServerTimeReqPb
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        ServerTimeReqPb.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.ServerTimeReqPb";
        };

        return ServerTimeReqPb;
    })();

    pb.ServerTimeResPb = (function() {

        /**
         * Properties of a ServerTimeResPb.
         * @memberof pb
         * @interface IServerTimeResPb
         * @property {string|null} [clientTime] ServerTimeResPb clientTime
         * @property {string|null} [serverTransmitTime] ServerTimeResPb serverTransmitTime
         * @property {string|null} [serverSendTime] ServerTimeResPb serverSendTime
         */

        /**
         * Constructs a new ServerTimeResPb.
         * @memberof pb
         * @classdesc Represents a ServerTimeResPb.
         * @implements IServerTimeResPb
         * @constructor
         * @param {pb.IServerTimeResPb=} [properties] Properties to set
         */
        function ServerTimeResPb(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * ServerTimeResPb clientTime.
         * @member {string} clientTime
         * @memberof pb.ServerTimeResPb
         * @instance
         */
        ServerTimeResPb.prototype.clientTime = "";

        /**
         * ServerTimeResPb serverTransmitTime.
         * @member {string} serverTransmitTime
         * @memberof pb.ServerTimeResPb
         * @instance
         */
        ServerTimeResPb.prototype.serverTransmitTime = "";

        /**
         * ServerTimeResPb serverSendTime.
         * @member {string} serverSendTime
         * @memberof pb.ServerTimeResPb
         * @instance
         */
        ServerTimeResPb.prototype.serverSendTime = "";

        /**
         * Creates a new ServerTimeResPb instance using the specified properties.
         * @function create
         * @memberof pb.ServerTimeResPb
         * @static
         * @param {pb.IServerTimeResPb=} [properties] Properties to set
         * @returns {pb.ServerTimeResPb} ServerTimeResPb instance
         */
        ServerTimeResPb.create = function create(properties) {
            return new ServerTimeResPb(properties);
        };

        /**
         * Encodes the specified ServerTimeResPb message. Does not implicitly {@link pb.ServerTimeResPb.verify|verify} messages.
         * @function encode
         * @memberof pb.ServerTimeResPb
         * @static
         * @param {pb.IServerTimeResPb} message ServerTimeResPb message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ServerTimeResPb.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.clientTime != null && Object.hasOwnProperty.call(message, "clientTime"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.clientTime);
            if (message.serverTransmitTime != null && Object.hasOwnProperty.call(message, "serverTransmitTime"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.serverTransmitTime);
            if (message.serverSendTime != null && Object.hasOwnProperty.call(message, "serverSendTime"))
                writer.uint32(/* id 3, wireType 2 =*/26).string(message.serverSendTime);
            return writer;
        };

        /**
         * Encodes the specified ServerTimeResPb message, length delimited. Does not implicitly {@link pb.ServerTimeResPb.verify|verify} messages.
         * @function encodeDelimited
         * @memberof pb.ServerTimeResPb
         * @static
         * @param {pb.IServerTimeResPb} message ServerTimeResPb message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ServerTimeResPb.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a ServerTimeResPb message from the specified reader or buffer.
         * @function decode
         * @memberof pb.ServerTimeResPb
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {pb.ServerTimeResPb} ServerTimeResPb
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ServerTimeResPb.decode = function decode(reader, length) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.pb.ServerTimeResPb();
            while (reader.pos < end) {
                var tag = reader.uint32();
                switch (tag >>> 3) {
                case 1: {
                        message.clientTime = reader.string();
                        break;
                    }
                case 2: {
                        message.serverTransmitTime = reader.string();
                        break;
                    }
                case 3: {
                        message.serverSendTime = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a ServerTimeResPb message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof pb.ServerTimeResPb
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {pb.ServerTimeResPb} ServerTimeResPb
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ServerTimeResPb.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a ServerTimeResPb message.
         * @function verify
         * @memberof pb.ServerTimeResPb
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        ServerTimeResPb.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.clientTime != null && message.hasOwnProperty("clientTime"))
                if (!$util.isString(message.clientTime))
                    return "clientTime: string expected";
            if (message.serverTransmitTime != null && message.hasOwnProperty("serverTransmitTime"))
                if (!$util.isString(message.serverTransmitTime))
                    return "serverTransmitTime: string expected";
            if (message.serverSendTime != null && message.hasOwnProperty("serverSendTime"))
                if (!$util.isString(message.serverSendTime))
                    return "serverSendTime: string expected";
            return null;
        };

        /**
         * Creates a ServerTimeResPb message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof pb.ServerTimeResPb
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {pb.ServerTimeResPb} ServerTimeResPb
         */
        ServerTimeResPb.fromObject = function fromObject(object) {
            if (object instanceof $root.pb.ServerTimeResPb)
                return object;
            var message = new $root.pb.ServerTimeResPb();
            if (object.clientTime != null)
                message.clientTime = String(object.clientTime);
            if (object.serverTransmitTime != null)
                message.serverTransmitTime = String(object.serverTransmitTime);
            if (object.serverSendTime != null)
                message.serverSendTime = String(object.serverSendTime);
            return message;
        };

        /**
         * Creates a plain object from a ServerTimeResPb message. Also converts values to other types if specified.
         * @function toObject
         * @memberof pb.ServerTimeResPb
         * @static
         * @param {pb.ServerTimeResPb} message ServerTimeResPb
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        ServerTimeResPb.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.clientTime = "";
                object.serverTransmitTime = "";
                object.serverSendTime = "";
            }
            if (message.clientTime != null && message.hasOwnProperty("clientTime"))
                object.clientTime = message.clientTime;
            if (message.serverTransmitTime != null && message.hasOwnProperty("serverTransmitTime"))
                object.serverTransmitTime = message.serverTransmitTime;
            if (message.serverSendTime != null && message.hasOwnProperty("serverSendTime"))
                object.serverSendTime = message.serverSendTime;
            return object;
        };

        /**
         * Converts this ServerTimeResPb to JSON.
         * @function toJSON
         * @memberof pb.ServerTimeResPb
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        ServerTimeResPb.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for ServerTimeResPb
         * @function getTypeUrl
         * @memberof pb.ServerTimeResPb
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        ServerTimeResPb.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.ServerTimeResPb";
        };

        return ServerTimeResPb;
    })();

    pb.UserAddAwardRes = (function() {

        /**
         * Properties of a UserAddAwardRes.
         * @memberof pb
         * @interface IUserAddAwardRes
         * @property {string|null} [awardId] UserAddAwardRes awardId
         * @property {string|null} [awardUrl] UserAddAwardRes awardUrl
         * @property {string|null} [awardName] UserAddAwardRes awardName
         * @property {string|null} [address] UserAddAwardRes address
         * @property {string|null} [code] UserAddAwardRes code
         * @property {string|null} [createTime] UserAddAwardRes createTime
         */

        /**
         * Constructs a new UserAddAwardRes.
         * @memberof pb
         * @classdesc Represents a UserAddAwardRes.
         * @implements IUserAddAwardRes
         * @constructor
         * @param {pb.IUserAddAwardRes=} [properties] Properties to set
         */
        function UserAddAwardRes(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * UserAddAwardRes awardId.
         * @member {string} awardId
         * @memberof pb.UserAddAwardRes
         * @instance
         */
        UserAddAwardRes.prototype.awardId = "";

        /**
         * UserAddAwardRes awardUrl.
         * @member {string} awardUrl
         * @memberof pb.UserAddAwardRes
         * @instance
         */
        UserAddAwardRes.prototype.awardUrl = "";

        /**
         * UserAddAwardRes awardName.
         * @member {string} awardName
         * @memberof pb.UserAddAwardRes
         * @instance
         */
        UserAddAwardRes.prototype.awardName = "";

        /**
         * UserAddAwardRes address.
         * @member {string} address
         * @memberof pb.UserAddAwardRes
         * @instance
         */
        UserAddAwardRes.prototype.address = "";

        /**
         * UserAddAwardRes code.
         * @member {string} code
         * @memberof pb.UserAddAwardRes
         * @instance
         */
        UserAddAwardRes.prototype.code = "";

        /**
         * UserAddAwardRes createTime.
         * @member {string} createTime
         * @memberof pb.UserAddAwardRes
         * @instance
         */
        UserAddAwardRes.prototype.createTime = "";

        /**
         * Creates a new UserAddAwardRes instance using the specified properties.
         * @function create
         * @memberof pb.UserAddAwardRes
         * @static
         * @param {pb.IUserAddAwardRes=} [properties] Properties to set
         * @returns {pb.UserAddAwardRes} UserAddAwardRes instance
         */
        UserAddAwardRes.create = function create(properties) {
            return new UserAddAwardRes(properties);
        };

        /**
         * Encodes the specified UserAddAwardRes message. Does not implicitly {@link pb.UserAddAwardRes.verify|verify} messages.
         * @function encode
         * @memberof pb.UserAddAwardRes
         * @static
         * @param {pb.IUserAddAwardRes} message UserAddAwardRes message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        UserAddAwardRes.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.awardId != null && Object.hasOwnProperty.call(message, "awardId"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.awardId);
            if (message.awardUrl != null && Object.hasOwnProperty.call(message, "awardUrl"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.awardUrl);
            if (message.awardName != null && Object.hasOwnProperty.call(message, "awardName"))
                writer.uint32(/* id 3, wireType 2 =*/26).string(message.awardName);
            if (message.address != null && Object.hasOwnProperty.call(message, "address"))
                writer.uint32(/* id 4, wireType 2 =*/34).string(message.address);
            if (message.code != null && Object.hasOwnProperty.call(message, "code"))
                writer.uint32(/* id 5, wireType 2 =*/42).string(message.code);
            if (message.createTime != null && Object.hasOwnProperty.call(message, "createTime"))
                writer.uint32(/* id 6, wireType 2 =*/50).string(message.createTime);
            return writer;
        };

        /**
         * Encodes the specified UserAddAwardRes message, length delimited. Does not implicitly {@link pb.UserAddAwardRes.verify|verify} messages.
         * @function encodeDelimited
         * @memberof pb.UserAddAwardRes
         * @static
         * @param {pb.IUserAddAwardRes} message UserAddAwardRes message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        UserAddAwardRes.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a UserAddAwardRes message from the specified reader or buffer.
         * @function decode
         * @memberof pb.UserAddAwardRes
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {pb.UserAddAwardRes} UserAddAwardRes
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        UserAddAwardRes.decode = function decode(reader, length) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.pb.UserAddAwardRes();
            while (reader.pos < end) {
                var tag = reader.uint32();
                switch (tag >>> 3) {
                case 1: {
                        message.awardId = reader.string();
                        break;
                    }
                case 2: {
                        message.awardUrl = reader.string();
                        break;
                    }
                case 3: {
                        message.awardName = reader.string();
                        break;
                    }
                case 4: {
                        message.address = reader.string();
                        break;
                    }
                case 5: {
                        message.code = reader.string();
                        break;
                    }
                case 6: {
                        message.createTime = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a UserAddAwardRes message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof pb.UserAddAwardRes
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {pb.UserAddAwardRes} UserAddAwardRes
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        UserAddAwardRes.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a UserAddAwardRes message.
         * @function verify
         * @memberof pb.UserAddAwardRes
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        UserAddAwardRes.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.awardId != null && message.hasOwnProperty("awardId"))
                if (!$util.isString(message.awardId))
                    return "awardId: string expected";
            if (message.awardUrl != null && message.hasOwnProperty("awardUrl"))
                if (!$util.isString(message.awardUrl))
                    return "awardUrl: string expected";
            if (message.awardName != null && message.hasOwnProperty("awardName"))
                if (!$util.isString(message.awardName))
                    return "awardName: string expected";
            if (message.address != null && message.hasOwnProperty("address"))
                if (!$util.isString(message.address))
                    return "address: string expected";
            if (message.code != null && message.hasOwnProperty("code"))
                if (!$util.isString(message.code))
                    return "code: string expected";
            if (message.createTime != null && message.hasOwnProperty("createTime"))
                if (!$util.isString(message.createTime))
                    return "createTime: string expected";
            return null;
        };

        /**
         * Creates a UserAddAwardRes message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof pb.UserAddAwardRes
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {pb.UserAddAwardRes} UserAddAwardRes
         */
        UserAddAwardRes.fromObject = function fromObject(object) {
            if (object instanceof $root.pb.UserAddAwardRes)
                return object;
            var message = new $root.pb.UserAddAwardRes();
            if (object.awardId != null)
                message.awardId = String(object.awardId);
            if (object.awardUrl != null)
                message.awardUrl = String(object.awardUrl);
            if (object.awardName != null)
                message.awardName = String(object.awardName);
            if (object.address != null)
                message.address = String(object.address);
            if (object.code != null)
                message.code = String(object.code);
            if (object.createTime != null)
                message.createTime = String(object.createTime);
            return message;
        };

        /**
         * Creates a plain object from a UserAddAwardRes message. Also converts values to other types if specified.
         * @function toObject
         * @memberof pb.UserAddAwardRes
         * @static
         * @param {pb.UserAddAwardRes} message UserAddAwardRes
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        UserAddAwardRes.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.awardId = "";
                object.awardUrl = "";
                object.awardName = "";
                object.address = "";
                object.code = "";
                object.createTime = "";
            }
            if (message.awardId != null && message.hasOwnProperty("awardId"))
                object.awardId = message.awardId;
            if (message.awardUrl != null && message.hasOwnProperty("awardUrl"))
                object.awardUrl = message.awardUrl;
            if (message.awardName != null && message.hasOwnProperty("awardName"))
                object.awardName = message.awardName;
            if (message.address != null && message.hasOwnProperty("address"))
                object.address = message.address;
            if (message.code != null && message.hasOwnProperty("code"))
                object.code = message.code;
            if (message.createTime != null && message.hasOwnProperty("createTime"))
                object.createTime = message.createTime;
            return object;
        };

        /**
         * Converts this UserAddAwardRes to JSON.
         * @function toJSON
         * @memberof pb.UserAddAwardRes
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        UserAddAwardRes.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for UserAddAwardRes
         * @function getTypeUrl
         * @memberof pb.UserAddAwardRes
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        UserAddAwardRes.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.UserAddAwardRes";
        };

        return UserAddAwardRes;
    })();

    pb.UserAwardHistory = (function() {

        /**
         * Properties of a UserAwardHistory.
         * @memberof pb
         * @interface IUserAwardHistory
         * @property {Array.<pb.IUserAddAwardRes>|null} [items] UserAwardHistory items
         */

        /**
         * Constructs a new UserAwardHistory.
         * @memberof pb
         * @classdesc Represents a UserAwardHistory.
         * @implements IUserAwardHistory
         * @constructor
         * @param {pb.IUserAwardHistory=} [properties] Properties to set
         */
        function UserAwardHistory(properties) {
            this.items = [];
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * UserAwardHistory items.
         * @member {Array.<pb.IUserAddAwardRes>} items
         * @memberof pb.UserAwardHistory
         * @instance
         */
        UserAwardHistory.prototype.items = $util.emptyArray;

        /**
         * Creates a new UserAwardHistory instance using the specified properties.
         * @function create
         * @memberof pb.UserAwardHistory
         * @static
         * @param {pb.IUserAwardHistory=} [properties] Properties to set
         * @returns {pb.UserAwardHistory} UserAwardHistory instance
         */
        UserAwardHistory.create = function create(properties) {
            return new UserAwardHistory(properties);
        };

        /**
         * Encodes the specified UserAwardHistory message. Does not implicitly {@link pb.UserAwardHistory.verify|verify} messages.
         * @function encode
         * @memberof pb.UserAwardHistory
         * @static
         * @param {pb.IUserAwardHistory} message UserAwardHistory message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        UserAwardHistory.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.items != null && message.items.length)
                for (var i = 0; i < message.items.length; ++i)
                    $root.pb.UserAddAwardRes.encode(message.items[i], writer.uint32(/* id 1, wireType 2 =*/10).fork()).ldelim();
            return writer;
        };

        /**
         * Encodes the specified UserAwardHistory message, length delimited. Does not implicitly {@link pb.UserAwardHistory.verify|verify} messages.
         * @function encodeDelimited
         * @memberof pb.UserAwardHistory
         * @static
         * @param {pb.IUserAwardHistory} message UserAwardHistory message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        UserAwardHistory.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a UserAwardHistory message from the specified reader or buffer.
         * @function decode
         * @memberof pb.UserAwardHistory
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {pb.UserAwardHistory} UserAwardHistory
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        UserAwardHistory.decode = function decode(reader, length) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.pb.UserAwardHistory();
            while (reader.pos < end) {
                var tag = reader.uint32();
                switch (tag >>> 3) {
                case 1: {
                        if (!(message.items && message.items.length))
                            message.items = [];
                        message.items.push($root.pb.UserAddAwardRes.decode(reader, reader.uint32()));
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a UserAwardHistory message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof pb.UserAwardHistory
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {pb.UserAwardHistory} UserAwardHistory
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        UserAwardHistory.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a UserAwardHistory message.
         * @function verify
         * @memberof pb.UserAwardHistory
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        UserAwardHistory.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.items != null && message.hasOwnProperty("items")) {
                if (!Array.isArray(message.items))
                    return "items: array expected";
                for (var i = 0; i < message.items.length; ++i) {
                    var error = $root.pb.UserAddAwardRes.verify(message.items[i]);
                    if (error)
                        return "items." + error;
                }
            }
            return null;
        };

        /**
         * Creates a UserAwardHistory message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof pb.UserAwardHistory
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {pb.UserAwardHistory} UserAwardHistory
         */
        UserAwardHistory.fromObject = function fromObject(object) {
            if (object instanceof $root.pb.UserAwardHistory)
                return object;
            var message = new $root.pb.UserAwardHistory();
            if (object.items) {
                if (!Array.isArray(object.items))
                    throw TypeError(".pb.UserAwardHistory.items: array expected");
                message.items = [];
                for (var i = 0; i < object.items.length; ++i) {
                    if (typeof object.items[i] !== "object")
                        throw TypeError(".pb.UserAwardHistory.items: object expected");
                    message.items[i] = $root.pb.UserAddAwardRes.fromObject(object.items[i]);
                }
            }
            return message;
        };

        /**
         * Creates a plain object from a UserAwardHistory message. Also converts values to other types if specified.
         * @function toObject
         * @memberof pb.UserAwardHistory
         * @static
         * @param {pb.UserAwardHistory} message UserAwardHistory
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        UserAwardHistory.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.arrays || options.defaults)
                object.items = [];
            if (message.items && message.items.length) {
                object.items = [];
                for (var j = 0; j < message.items.length; ++j)
                    object.items[j] = $root.pb.UserAddAwardRes.toObject(message.items[j], options);
            }
            return object;
        };

        /**
         * Converts this UserAwardHistory to JSON.
         * @function toJSON
         * @memberof pb.UserAwardHistory
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        UserAwardHistory.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for UserAwardHistory
         * @function getTypeUrl
         * @memberof pb.UserAwardHistory
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        UserAwardHistory.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.UserAwardHistory";
        };

        return UserAwardHistory;
    })();

    pb.UserGameHistory = (function() {

        /**
         * Properties of a UserGameHistory.
         * @memberof pb
         * @interface IUserGameHistory
         * @property {Array.<pb.IUserGameHistoryItem>|null} [item] UserGameHistory item
         */

        /**
         * Constructs a new UserGameHistory.
         * @memberof pb
         * @classdesc Represents a UserGameHistory.
         * @implements IUserGameHistory
         * @constructor
         * @param {pb.IUserGameHistory=} [properties] Properties to set
         */
        function UserGameHistory(properties) {
            this.item = [];
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * UserGameHistory item.
         * @member {Array.<pb.IUserGameHistoryItem>} item
         * @memberof pb.UserGameHistory
         * @instance
         */
        UserGameHistory.prototype.item = $util.emptyArray;

        /**
         * Creates a new UserGameHistory instance using the specified properties.
         * @function create
         * @memberof pb.UserGameHistory
         * @static
         * @param {pb.IUserGameHistory=} [properties] Properties to set
         * @returns {pb.UserGameHistory} UserGameHistory instance
         */
        UserGameHistory.create = function create(properties) {
            return new UserGameHistory(properties);
        };

        /**
         * Encodes the specified UserGameHistory message. Does not implicitly {@link pb.UserGameHistory.verify|verify} messages.
         * @function encode
         * @memberof pb.UserGameHistory
         * @static
         * @param {pb.IUserGameHistory} message UserGameHistory message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        UserGameHistory.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.item != null && message.item.length)
                for (var i = 0; i < message.item.length; ++i)
                    $root.pb.UserGameHistoryItem.encode(message.item[i], writer.uint32(/* id 1, wireType 2 =*/10).fork()).ldelim();
            return writer;
        };

        /**
         * Encodes the specified UserGameHistory message, length delimited. Does not implicitly {@link pb.UserGameHistory.verify|verify} messages.
         * @function encodeDelimited
         * @memberof pb.UserGameHistory
         * @static
         * @param {pb.IUserGameHistory} message UserGameHistory message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        UserGameHistory.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a UserGameHistory message from the specified reader or buffer.
         * @function decode
         * @memberof pb.UserGameHistory
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {pb.UserGameHistory} UserGameHistory
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        UserGameHistory.decode = function decode(reader, length) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.pb.UserGameHistory();
            while (reader.pos < end) {
                var tag = reader.uint32();
                switch (tag >>> 3) {
                case 1: {
                        if (!(message.item && message.item.length))
                            message.item = [];
                        message.item.push($root.pb.UserGameHistoryItem.decode(reader, reader.uint32()));
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a UserGameHistory message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof pb.UserGameHistory
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {pb.UserGameHistory} UserGameHistory
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        UserGameHistory.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a UserGameHistory message.
         * @function verify
         * @memberof pb.UserGameHistory
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        UserGameHistory.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.item != null && message.hasOwnProperty("item")) {
                if (!Array.isArray(message.item))
                    return "item: array expected";
                for (var i = 0; i < message.item.length; ++i) {
                    var error = $root.pb.UserGameHistoryItem.verify(message.item[i]);
                    if (error)
                        return "item." + error;
                }
            }
            return null;
        };

        /**
         * Creates a UserGameHistory message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof pb.UserGameHistory
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {pb.UserGameHistory} UserGameHistory
         */
        UserGameHistory.fromObject = function fromObject(object) {
            if (object instanceof $root.pb.UserGameHistory)
                return object;
            var message = new $root.pb.UserGameHistory();
            if (object.item) {
                if (!Array.isArray(object.item))
                    throw TypeError(".pb.UserGameHistory.item: array expected");
                message.item = [];
                for (var i = 0; i < object.item.length; ++i) {
                    if (typeof object.item[i] !== "object")
                        throw TypeError(".pb.UserGameHistory.item: object expected");
                    message.item[i] = $root.pb.UserGameHistoryItem.fromObject(object.item[i]);
                }
            }
            return message;
        };

        /**
         * Creates a plain object from a UserGameHistory message. Also converts values to other types if specified.
         * @function toObject
         * @memberof pb.UserGameHistory
         * @static
         * @param {pb.UserGameHistory} message UserGameHistory
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        UserGameHistory.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.arrays || options.defaults)
                object.item = [];
            if (message.item && message.item.length) {
                object.item = [];
                for (var j = 0; j < message.item.length; ++j)
                    object.item[j] = $root.pb.UserGameHistoryItem.toObject(message.item[j], options);
            }
            return object;
        };

        /**
         * Converts this UserGameHistory to JSON.
         * @function toJSON
         * @memberof pb.UserGameHistory
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        UserGameHistory.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for UserGameHistory
         * @function getTypeUrl
         * @memberof pb.UserGameHistory
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        UserGameHistory.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.UserGameHistory";
        };

        return UserGameHistory;
    })();

    pb.UserGameHistoryItem = (function() {

        /**
         * Properties of a UserGameHistoryItem.
         * @memberof pb
         * @interface IUserGameHistoryItem
         * @property {string|null} [rank] UserGameHistoryItem rank
         * @property {string|null} [businessName] UserGameHistoryItem businessName
         * @property {string|null} [createTime] UserGameHistoryItem createTime
         */

        /**
         * Constructs a new UserGameHistoryItem.
         * @memberof pb
         * @classdesc Represents a UserGameHistoryItem.
         * @implements IUserGameHistoryItem
         * @constructor
         * @param {pb.IUserGameHistoryItem=} [properties] Properties to set
         */
        function UserGameHistoryItem(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * UserGameHistoryItem rank.
         * @member {string} rank
         * @memberof pb.UserGameHistoryItem
         * @instance
         */
        UserGameHistoryItem.prototype.rank = "";

        /**
         * UserGameHistoryItem businessName.
         * @member {string} businessName
         * @memberof pb.UserGameHistoryItem
         * @instance
         */
        UserGameHistoryItem.prototype.businessName = "";

        /**
         * UserGameHistoryItem createTime.
         * @member {string} createTime
         * @memberof pb.UserGameHistoryItem
         * @instance
         */
        UserGameHistoryItem.prototype.createTime = "";

        /**
         * Creates a new UserGameHistoryItem instance using the specified properties.
         * @function create
         * @memberof pb.UserGameHistoryItem
         * @static
         * @param {pb.IUserGameHistoryItem=} [properties] Properties to set
         * @returns {pb.UserGameHistoryItem} UserGameHistoryItem instance
         */
        UserGameHistoryItem.create = function create(properties) {
            return new UserGameHistoryItem(properties);
        };

        /**
         * Encodes the specified UserGameHistoryItem message. Does not implicitly {@link pb.UserGameHistoryItem.verify|verify} messages.
         * @function encode
         * @memberof pb.UserGameHistoryItem
         * @static
         * @param {pb.IUserGameHistoryItem} message UserGameHistoryItem message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        UserGameHistoryItem.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.rank != null && Object.hasOwnProperty.call(message, "rank"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.rank);
            if (message.businessName != null && Object.hasOwnProperty.call(message, "businessName"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.businessName);
            if (message.createTime != null && Object.hasOwnProperty.call(message, "createTime"))
                writer.uint32(/* id 3, wireType 2 =*/26).string(message.createTime);
            return writer;
        };

        /**
         * Encodes the specified UserGameHistoryItem message, length delimited. Does not implicitly {@link pb.UserGameHistoryItem.verify|verify} messages.
         * @function encodeDelimited
         * @memberof pb.UserGameHistoryItem
         * @static
         * @param {pb.IUserGameHistoryItem} message UserGameHistoryItem message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        UserGameHistoryItem.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a UserGameHistoryItem message from the specified reader or buffer.
         * @function decode
         * @memberof pb.UserGameHistoryItem
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {pb.UserGameHistoryItem} UserGameHistoryItem
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        UserGameHistoryItem.decode = function decode(reader, length) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.pb.UserGameHistoryItem();
            while (reader.pos < end) {
                var tag = reader.uint32();
                switch (tag >>> 3) {
                case 1: {
                        message.rank = reader.string();
                        break;
                    }
                case 2: {
                        message.businessName = reader.string();
                        break;
                    }
                case 3: {
                        message.createTime = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a UserGameHistoryItem message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof pb.UserGameHistoryItem
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {pb.UserGameHistoryItem} UserGameHistoryItem
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        UserGameHistoryItem.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a UserGameHistoryItem message.
         * @function verify
         * @memberof pb.UserGameHistoryItem
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        UserGameHistoryItem.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.rank != null && message.hasOwnProperty("rank"))
                if (!$util.isString(message.rank))
                    return "rank: string expected";
            if (message.businessName != null && message.hasOwnProperty("businessName"))
                if (!$util.isString(message.businessName))
                    return "businessName: string expected";
            if (message.createTime != null && message.hasOwnProperty("createTime"))
                if (!$util.isString(message.createTime))
                    return "createTime: string expected";
            return null;
        };

        /**
         * Creates a UserGameHistoryItem message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof pb.UserGameHistoryItem
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {pb.UserGameHistoryItem} UserGameHistoryItem
         */
        UserGameHistoryItem.fromObject = function fromObject(object) {
            if (object instanceof $root.pb.UserGameHistoryItem)
                return object;
            var message = new $root.pb.UserGameHistoryItem();
            if (object.rank != null)
                message.rank = String(object.rank);
            if (object.businessName != null)
                message.businessName = String(object.businessName);
            if (object.createTime != null)
                message.createTime = String(object.createTime);
            return message;
        };

        /**
         * Creates a plain object from a UserGameHistoryItem message. Also converts values to other types if specified.
         * @function toObject
         * @memberof pb.UserGameHistoryItem
         * @static
         * @param {pb.UserGameHistoryItem} message UserGameHistoryItem
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        UserGameHistoryItem.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.rank = "";
                object.businessName = "";
                object.createTime = "";
            }
            if (message.rank != null && message.hasOwnProperty("rank"))
                object.rank = message.rank;
            if (message.businessName != null && message.hasOwnProperty("businessName"))
                object.businessName = message.businessName;
            if (message.createTime != null && message.hasOwnProperty("createTime"))
                object.createTime = message.createTime;
            return object;
        };

        /**
         * Converts this UserGameHistoryItem to JSON.
         * @function toJSON
         * @memberof pb.UserGameHistoryItem
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        UserGameHistoryItem.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for UserGameHistoryItem
         * @function getTypeUrl
         * @memberof pb.UserGameHistoryItem
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        UserGameHistoryItem.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.UserGameHistoryItem";
        };

        return UserGameHistoryItem;
    })();

    pb.UserLoginReqPb = (function() {

        /**
         * Properties of a UserLoginReqPb.
         * @memberof pb
         * @interface IUserLoginReqPb
         * @property {string|null} [token] UserLoginReqPb token
         * @property {string|null} [roomId] UserLoginReqPb roomId
         */

        /**
         * Constructs a new UserLoginReqPb.
         * @memberof pb
         * @classdesc Represents a UserLoginReqPb.
         * @implements IUserLoginReqPb
         * @constructor
         * @param {pb.IUserLoginReqPb=} [properties] Properties to set
         */
        function UserLoginReqPb(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * UserLoginReqPb token.
         * @member {string} token
         * @memberof pb.UserLoginReqPb
         * @instance
         */
        UserLoginReqPb.prototype.token = "";

        /**
         * UserLoginReqPb roomId.
         * @member {string} roomId
         * @memberof pb.UserLoginReqPb
         * @instance
         */
        UserLoginReqPb.prototype.roomId = "";

        /**
         * Creates a new UserLoginReqPb instance using the specified properties.
         * @function create
         * @memberof pb.UserLoginReqPb
         * @static
         * @param {pb.IUserLoginReqPb=} [properties] Properties to set
         * @returns {pb.UserLoginReqPb} UserLoginReqPb instance
         */
        UserLoginReqPb.create = function create(properties) {
            return new UserLoginReqPb(properties);
        };

        /**
         * Encodes the specified UserLoginReqPb message. Does not implicitly {@link pb.UserLoginReqPb.verify|verify} messages.
         * @function encode
         * @memberof pb.UserLoginReqPb
         * @static
         * @param {pb.IUserLoginReqPb} message UserLoginReqPb message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        UserLoginReqPb.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.token != null && Object.hasOwnProperty.call(message, "token"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.token);
            if (message.roomId != null && Object.hasOwnProperty.call(message, "roomId"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.roomId);
            return writer;
        };

        /**
         * Encodes the specified UserLoginReqPb message, length delimited. Does not implicitly {@link pb.UserLoginReqPb.verify|verify} messages.
         * @function encodeDelimited
         * @memberof pb.UserLoginReqPb
         * @static
         * @param {pb.IUserLoginReqPb} message UserLoginReqPb message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        UserLoginReqPb.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a UserLoginReqPb message from the specified reader or buffer.
         * @function decode
         * @memberof pb.UserLoginReqPb
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {pb.UserLoginReqPb} UserLoginReqPb
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        UserLoginReqPb.decode = function decode(reader, length) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.pb.UserLoginReqPb();
            while (reader.pos < end) {
                var tag = reader.uint32();
                switch (tag >>> 3) {
                case 1: {
                        message.token = reader.string();
                        break;
                    }
                case 2: {
                        message.roomId = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a UserLoginReqPb message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof pb.UserLoginReqPb
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {pb.UserLoginReqPb} UserLoginReqPb
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        UserLoginReqPb.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a UserLoginReqPb message.
         * @function verify
         * @memberof pb.UserLoginReqPb
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        UserLoginReqPb.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.token != null && message.hasOwnProperty("token"))
                if (!$util.isString(message.token))
                    return "token: string expected";
            if (message.roomId != null && message.hasOwnProperty("roomId"))
                if (!$util.isString(message.roomId))
                    return "roomId: string expected";
            return null;
        };

        /**
         * Creates a UserLoginReqPb message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof pb.UserLoginReqPb
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {pb.UserLoginReqPb} UserLoginReqPb
         */
        UserLoginReqPb.fromObject = function fromObject(object) {
            if (object instanceof $root.pb.UserLoginReqPb)
                return object;
            var message = new $root.pb.UserLoginReqPb();
            if (object.token != null)
                message.token = String(object.token);
            if (object.roomId != null)
                message.roomId = String(object.roomId);
            return message;
        };

        /**
         * Creates a plain object from a UserLoginReqPb message. Also converts values to other types if specified.
         * @function toObject
         * @memberof pb.UserLoginReqPb
         * @static
         * @param {pb.UserLoginReqPb} message UserLoginReqPb
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        UserLoginReqPb.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.token = "";
                object.roomId = "";
            }
            if (message.token != null && message.hasOwnProperty("token"))
                object.token = message.token;
            if (message.roomId != null && message.hasOwnProperty("roomId"))
                object.roomId = message.roomId;
            return object;
        };

        /**
         * Converts this UserLoginReqPb to JSON.
         * @function toJSON
         * @memberof pb.UserLoginReqPb
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        UserLoginReqPb.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for UserLoginReqPb
         * @function getTypeUrl
         * @memberof pb.UserLoginReqPb
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        UserLoginReqPb.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.UserLoginReqPb";
        };

        return UserLoginReqPb;
    })();

    pb.UserLoginResPb = (function() {

        /**
         * Properties of a UserLoginResPb.
         * @memberof pb
         * @interface IUserLoginResPb
         * @property {boolean|null} [result] UserLoginResPb result
         */

        /**
         * Constructs a new UserLoginResPb.
         * @memberof pb
         * @classdesc Represents a UserLoginResPb.
         * @implements IUserLoginResPb
         * @constructor
         * @param {pb.IUserLoginResPb=} [properties] Properties to set
         */
        function UserLoginResPb(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * UserLoginResPb result.
         * @member {boolean} result
         * @memberof pb.UserLoginResPb
         * @instance
         */
        UserLoginResPb.prototype.result = false;

        /**
         * Creates a new UserLoginResPb instance using the specified properties.
         * @function create
         * @memberof pb.UserLoginResPb
         * @static
         * @param {pb.IUserLoginResPb=} [properties] Properties to set
         * @returns {pb.UserLoginResPb} UserLoginResPb instance
         */
        UserLoginResPb.create = function create(properties) {
            return new UserLoginResPb(properties);
        };

        /**
         * Encodes the specified UserLoginResPb message. Does not implicitly {@link pb.UserLoginResPb.verify|verify} messages.
         * @function encode
         * @memberof pb.UserLoginResPb
         * @static
         * @param {pb.IUserLoginResPb} message UserLoginResPb message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        UserLoginResPb.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.result != null && Object.hasOwnProperty.call(message, "result"))
                writer.uint32(/* id 1, wireType 0 =*/8).bool(message.result);
            return writer;
        };

        /**
         * Encodes the specified UserLoginResPb message, length delimited. Does not implicitly {@link pb.UserLoginResPb.verify|verify} messages.
         * @function encodeDelimited
         * @memberof pb.UserLoginResPb
         * @static
         * @param {pb.IUserLoginResPb} message UserLoginResPb message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        UserLoginResPb.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a UserLoginResPb message from the specified reader or buffer.
         * @function decode
         * @memberof pb.UserLoginResPb
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {pb.UserLoginResPb} UserLoginResPb
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        UserLoginResPb.decode = function decode(reader, length) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.pb.UserLoginResPb();
            while (reader.pos < end) {
                var tag = reader.uint32();
                switch (tag >>> 3) {
                case 1: {
                        message.result = reader.bool();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a UserLoginResPb message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof pb.UserLoginResPb
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {pb.UserLoginResPb} UserLoginResPb
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        UserLoginResPb.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a UserLoginResPb message.
         * @function verify
         * @memberof pb.UserLoginResPb
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        UserLoginResPb.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.result != null && message.hasOwnProperty("result"))
                if (typeof message.result !== "boolean")
                    return "result: boolean expected";
            return null;
        };

        /**
         * Creates a UserLoginResPb message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof pb.UserLoginResPb
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {pb.UserLoginResPb} UserLoginResPb
         */
        UserLoginResPb.fromObject = function fromObject(object) {
            if (object instanceof $root.pb.UserLoginResPb)
                return object;
            var message = new $root.pb.UserLoginResPb();
            if (object.result != null)
                message.result = Boolean(object.result);
            return message;
        };

        /**
         * Creates a plain object from a UserLoginResPb message. Also converts values to other types if specified.
         * @function toObject
         * @memberof pb.UserLoginResPb
         * @static
         * @param {pb.UserLoginResPb} message UserLoginResPb
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        UserLoginResPb.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults)
                object.result = false;
            if (message.result != null && message.hasOwnProperty("result"))
                object.result = message.result;
            return object;
        };

        /**
         * Converts this UserLoginResPb to JSON.
         * @function toJSON
         * @memberof pb.UserLoginResPb
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        UserLoginResPb.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for UserLoginResPb
         * @function getTypeUrl
         * @memberof pb.UserLoginResPb
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        UserLoginResPb.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.UserLoginResPb";
        };

        return UserLoginResPb;
    })();

    pb.RoomStatusPushPb = (function() {

        /**
         * Properties of a RoomStatusPushPb.
         * @memberof pb
         * @interface IRoomStatusPushPb
         * @property {string|null} [json] RoomStatusPushPb json
         */

        /**
         * Constructs a new RoomStatusPushPb.
         * @memberof pb
         * @classdesc Represents a RoomStatusPushPb.
         * @implements IRoomStatusPushPb
         * @constructor
         * @param {pb.IRoomStatusPushPb=} [properties] Properties to set
         */
        function RoomStatusPushPb(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * RoomStatusPushPb json.
         * @member {string} json
         * @memberof pb.RoomStatusPushPb
         * @instance
         */
        RoomStatusPushPb.prototype.json = "";

        /**
         * Creates a new RoomStatusPushPb instance using the specified properties.
         * @function create
         * @memberof pb.RoomStatusPushPb
         * @static
         * @param {pb.IRoomStatusPushPb=} [properties] Properties to set
         * @returns {pb.RoomStatusPushPb} RoomStatusPushPb instance
         */
        RoomStatusPushPb.create = function create(properties) {
            return new RoomStatusPushPb(properties);
        };

        /**
         * Encodes the specified RoomStatusPushPb message. Does not implicitly {@link pb.RoomStatusPushPb.verify|verify} messages.
         * @function encode
         * @memberof pb.RoomStatusPushPb
         * @static
         * @param {pb.IRoomStatusPushPb} message RoomStatusPushPb message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        RoomStatusPushPb.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.json != null && Object.hasOwnProperty.call(message, "json"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.json);
            return writer;
        };

        /**
         * Encodes the specified RoomStatusPushPb message, length delimited. Does not implicitly {@link pb.RoomStatusPushPb.verify|verify} messages.
         * @function encodeDelimited
         * @memberof pb.RoomStatusPushPb
         * @static
         * @param {pb.IRoomStatusPushPb} message RoomStatusPushPb message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        RoomStatusPushPb.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a RoomStatusPushPb message from the specified reader or buffer.
         * @function decode
         * @memberof pb.RoomStatusPushPb
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {pb.RoomStatusPushPb} RoomStatusPushPb
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        RoomStatusPushPb.decode = function decode(reader, length) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.pb.RoomStatusPushPb();
            while (reader.pos < end) {
                var tag = reader.uint32();
                switch (tag >>> 3) {
                case 1: {
                        message.json = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a RoomStatusPushPb message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof pb.RoomStatusPushPb
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {pb.RoomStatusPushPb} RoomStatusPushPb
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        RoomStatusPushPb.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a RoomStatusPushPb message.
         * @function verify
         * @memberof pb.RoomStatusPushPb
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        RoomStatusPushPb.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.json != null && message.hasOwnProperty("json"))
                if (!$util.isString(message.json))
                    return "json: string expected";
            return null;
        };

        /**
         * Creates a RoomStatusPushPb message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof pb.RoomStatusPushPb
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {pb.RoomStatusPushPb} RoomStatusPushPb
         */
        RoomStatusPushPb.fromObject = function fromObject(object) {
            if (object instanceof $root.pb.RoomStatusPushPb)
                return object;
            var message = new $root.pb.RoomStatusPushPb();
            if (object.json != null)
                message.json = String(object.json);
            return message;
        };

        /**
         * Creates a plain object from a RoomStatusPushPb message. Also converts values to other types if specified.
         * @function toObject
         * @memberof pb.RoomStatusPushPb
         * @static
         * @param {pb.RoomStatusPushPb} message RoomStatusPushPb
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        RoomStatusPushPb.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults)
                object.json = "";
            if (message.json != null && message.hasOwnProperty("json"))
                object.json = message.json;
            return object;
        };

        /**
         * Converts this RoomStatusPushPb to JSON.
         * @function toJSON
         * @memberof pb.RoomStatusPushPb
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        RoomStatusPushPb.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for RoomStatusPushPb
         * @function getTypeUrl
         * @memberof pb.RoomStatusPushPb
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        RoomStatusPushPb.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.RoomStatusPushPb";
        };

        return RoomStatusPushPb;
    })();

    return pb;
})();

module.exports = $root;
