[1, 0, 0, [["cc.Json<PERSON>set", ["_name", "json"], 1]], [[0, 0, 1, 3]], [[0, "_total", {"constant.json": {"camp1": 1, "camp2": 2, "giftScoreReservoirSharing": 80, "giftScoreReservoirReserve": 20, "hurtColour": "100|500"}, "monster.json": [{"jsonId": "150011", "name": "蝙蝠精", "camp": 2, "eliteMonster": 0, "monsterType": 1, "monsterLevel": 1, "monsterHp": 5000, "attack": "10|20", "callNum": 1, "animationScale": "1", "attackCooldown": "2", "attacKeffect": "160001", "attacKeffectInterval": "0.5", "movementSpeed": 100, "rebornTime": 0, "monsterScore": 10, "moveAnimation": "340501", "attackAnimation": "340401", "giftId": "300001|300002", "giftName": "点赞|666", "kuaishouGiftNum": 1, "douyinGiftNum": 1}, {"jsonId": "150021", "name": "小钻风", "camp": 2, "eliteMonster": 0, "monsterType": 2, "monsterLevel": 1, "monsterHp": 5000, "attack": "10|20", "callNum": 1, "animationScale": "1", "attackCooldown": "2", "attacKeffect": "160001", "attacKeffectInterval": "0.5", "movementSpeed": 100, "rebornTime": 0, "monsterScore": 10, "moveAnimation": "340502", "attackAnimation": "340402", "giftId": "300003", "giftName": "礼物1", "kuaishouGiftId": "11582", "kuaishouGiftName": "助威火炬", "kuaishouGiftNum": 1, "douyinGiftId": "n1/Dg1905sj1FyoBlQBvmbaDZFBNaKuKZH6zxHkv8Lg5x2cRfrKUTb8gzMs=", "douyinGiftName": "仙女棒", "douyinGiftNum": 1}, {"jsonId": "150031", "name": "伶俐鬼", "camp": 2, "eliteMonster": 0, "monsterType": 3, "monsterLevel": 1, "monsterHp": 5000, "attack": "10|20", "callNum": 1, "animationScale": "1", "attackCooldown": "2", "attacKeffect": "160006", "attacKeffectInterval": "0.5", "movementSpeed": 100, "rebornTime": 0, "monsterScore": 10, "moveAnimation": "340503", "attackAnimation": "340403", "giftId": "300004", "giftName": "礼物2", "kuaishouGiftId": "11582", "kuaishouGiftName": "助威火炬", "kuaishouGiftNum": 1, "douyinGiftId": "28rYzVFNyXEXFC8HI+f/WG+I7a6lfl3OyZZjUS+CVuwCgYZrPrUdytGHu0c=", "douyinGiftName": "能力药丸", "douyinGiftNum": 1}, {"jsonId": "150041", "name": "野猪精", "camp": 2, "eliteMonster": 0, "monsterType": 4, "monsterLevel": 1, "monsterHp": 5000, "attack": "10|20", "callNum": 1, "animationScale": "1", "attackCooldown": "2", "attacKeffect": "160001", "attacKeffectInterval": "0.5", "movementSpeed": 100, "rebornTime": 0, "monsterScore": 10, "moveAnimation": "340504", "attackAnimation": "340404", "giftId": "300005", "giftName": "礼物3", "kuaishouGiftId": "11606", "kuaishouGiftName": "助威火箭", "kuaishouGiftNum": 1, "douyinGiftId": "fJs8HKQ0xlPRixn8JAUiL2gFRiLD9S6IFCFdvZODSnhyo9YN8q7xUuVVyZI=", "douyinGiftName": "魔法镜", "douyinGiftNum": 1}, {"jsonId": "150051", "name": "大鹏", "camp": 2, "eliteMonster": 0, "monsterType": 5, "monsterLevel": 1, "monsterHp": 5000, "attack": "10|20", "callNum": 1, "animationScale": "1", "attackCooldown": "2", "attacKeffect": "160010", "attacKeffectInterval": "0.5", "movementSpeed": 100, "rebornTime": 0, "monsterScore": 10, "moveAnimation": "340505", "attackAnimation": "340405", "giftId": "300006", "giftName": "礼物4", "kuaishouGiftId": "11586", "kuaishouGiftName": "冲鸭", "kuaishouGiftNum": 1, "douyinGiftId": "PJ0FFeaDzXUreuUBZH6Hs+b56Jh0tQjrq0bIrrlZmv13GSAL9Q1hf59fjGk=", "douyinGiftName": "甜甜圈", "douyinGiftNum": 1}, {"jsonId": "150061", "name": "巨力猿", "camp": 2, "eliteMonster": 0, "monsterType": 6, "monsterLevel": 1, "monsterHp": 5000, "attack": "10|20", "callNum": 1, "animationScale": "1", "attackCooldown": "2", "attacKeffect": "160002", "attacKeffectInterval": "0.5", "movementSpeed": 100, "rebornTime": 0, "monsterScore": 10, "moveAnimation": "340506", "attackAnimation": "340406", "giftId": "300007", "giftName": "礼物5", "kuaishouGiftId": "11587", "kuaishouGiftName": "浪漫满屏", "kuaishouGiftNum": 1, "douyinGiftId": "gx7pmjQfhBaDOG2XkWI2peZ66YFWkCWRjZXpTqb23O/epru+sxWyTV/3Ufs=", "douyinGiftName": "恶魔炸弹", "douyinGiftNum": 1}, {"jsonId": "150071", "name": "犀牛精", "camp": 2, "eliteMonster": 0, "monsterType": 7, "monsterLevel": 1, "monsterHp": 5000, "attack": "10|20", "callNum": 1, "animationScale": "1", "attackCooldown": "2", "attacKeffect": "160003", "attacKeffectInterval": "0.5", "movementSpeed": 100, "rebornTime": 0, "monsterScore": 10, "moveAnimation": "340507", "attackAnimation": "340407", "giftId": "300008", "giftName": "礼物6", "kuaishouGiftId": "11587", "kuaishouGiftName": "浪漫满屏", "kuaishouGiftNum": 1, "douyinGiftName": "神秘空投", "douyinGiftNum": 1}, {"jsonId": "151011", "name": "1级蝙蝠王", "camp": 2, "eliteMonster": 0, "monsterType": 1001, "monsterLevel": 1, "monsterHp": 5000, "attack": "10|20", "callNum": 1, "animationScale": "1", "attackCooldown": "2", "attacKeffect": "160002", "attacKeffectInterval": "0.5", "movementSpeed": 100, "rebornTime": 10, "monsterScore": 10, "moveAnimation": "340508", "attackAnimation": "340408", "giftId": "300001|300002", "giftName": "点赞|666", "kuaishouGiftNum": 100, "douyinGiftNum": 100}, {"jsonId": "151021", "name": "1级总钻风", "camp": 2, "eliteMonster": 1, "monsterType": 1002, "monsterLevel": 1, "monsterHp": 5000, "attack": "10|20", "callNum": 1, "animationScale": "1", "attackCooldown": "2", "attacKeffect": "160002", "attacKeffectInterval": "0.5", "movementSpeed": 100, "rebornTime": 10, "monsterScore": 10, "moveAnimation": "340509", "attackAnimation": "340409", "giftId": "300003", "giftName": "礼物1", "kuaishouGiftId": "11582", "kuaishouGiftName": "助威火炬", "kuaishouGiftNum": 10, "douyinGiftId": "n1/Dg1905sj1FyoBlQBvmbaDZFBNaKuKZH6zxHkv8Lg5x2cRfrKUTb8gzMs=", "douyinGiftName": "仙女棒", "douyinGiftNum": 10}, {"jsonId": "151022", "name": "2级总钻风", "camp": 2, "eliteMonster": 1, "monsterType": 1002, "monsterLevel": 2, "monsterHp": 5000, "attack": "10|20", "callNum": 1, "animationScale": "1", "attackCooldown": "2", "attacKeffect": "160002", "attacKeffectInterval": "0.5", "movementSpeed": 100, "rebornTime": 10, "monsterScore": 10, "moveAnimation": "340509", "attackAnimation": "340409", "giftId": "300003", "giftName": "礼物1", "kuaishouGiftId": "11582", "kuaishouGiftName": "助威火炬", "kuaishouGiftNum": 20, "douyinGiftId": "n1/Dg1905sj1FyoBlQBvmbaDZFBNaKuKZH6zxHkv8Lg5x2cRfrKUTb8gzMs=", "douyinGiftName": "仙女棒", "douyinGiftNum": 20}, {"jsonId": "151023", "name": "3级总钻风", "camp": 2, "eliteMonster": 1, "monsterType": 1002, "monsterLevel": 3, "monsterHp": 5000, "attack": "10|20", "callNum": 1, "animationScale": "1", "attackCooldown": "2", "attacKeffect": "160002", "attacKeffectInterval": "0.5", "movementSpeed": 100, "rebornTime": 10, "monsterScore": 10, "moveAnimation": "340509", "attackAnimation": "340409", "giftId": "300003", "giftName": "礼物1", "kuaishouGiftId": "11582", "kuaishouGiftName": "助威火炬", "kuaishouGiftNum": 30, "douyinGiftId": "n1/Dg1905sj1FyoBlQBvmbaDZFBNaKuKZH6zxHkv8Lg5x2cRfrKUTb8gzMs=", "douyinGiftName": "仙女棒", "douyinGiftNum": 30}, {"jsonId": "151024", "name": "4级总钻风", "camp": 2, "eliteMonster": 1, "monsterType": 1002, "monsterLevel": 4, "monsterHp": 5000, "attack": "10|20", "callNum": 1, "animationScale": "1", "attackCooldown": "2", "attacKeffect": "160002", "attacKeffectInterval": "0.5", "movementSpeed": 100, "rebornTime": 10, "monsterScore": 10, "moveAnimation": "340509", "attackAnimation": "340409", "giftId": "300003", "giftName": "礼物1", "kuaishouGiftId": "11582", "kuaishouGiftName": "助威火炬", "kuaishouGiftNum": 40, "douyinGiftId": "n1/Dg1905sj1FyoBlQBvmbaDZFBNaKuKZH6zxHkv8Lg5x2cRfrKUTb8gzMs=", "douyinGiftName": "仙女棒", "douyinGiftNum": 40}, {"jsonId": "151025", "name": "5级总钻风", "camp": 2, "eliteMonster": 1, "monsterType": 1002, "monsterLevel": 5, "monsterHp": 5000, "attack": "10|20", "callNum": 1, "animationScale": "1", "attackCooldown": "2", "attacKeffect": "160002", "attacKeffectInterval": "0.5", "movementSpeed": 100, "rebornTime": 10, "monsterScore": 10, "moveAnimation": "340509", "attackAnimation": "340409", "giftId": "300003", "giftName": "礼物1", "kuaishouGiftId": "11582", "kuaishouGiftName": "助威火炬", "kuaishouGiftNum": 50, "douyinGiftId": "n1/Dg1905sj1FyoBlQBvmbaDZFBNaKuKZH6zxHkv8Lg5x2cRfrKUTb8gzMs=", "douyinGiftName": "仙女棒", "douyinGiftNum": 50}, {"jsonId": "151031", "name": "1级红孩儿", "camp": 2, "eliteMonster": 1, "monsterType": 1003, "monsterLevel": 1, "monsterHp": 5000, "attack": "10|20", "callNum": 1, "animationScale": "1", "attackCooldown": "2", "attacKeffect": "160005", "attacKeffectInterval": "0.5", "movementSpeed": 100, "rebornTime": 10, "monsterScore": 10, "moveAnimation": "340510", "attackAnimation": "340410", "giftId": "300004", "giftName": "礼物2", "kuaishouGiftId": "11582", "kuaishouGiftName": "助威火炬", "kuaishouGiftNum": 10, "douyinGiftId": "28rYzVFNyXEXFC8HI+f/WG+I7a6lfl3OyZZjUS+CVuwCgYZrPrUdytGHu0c=", "douyinGiftName": "能力药丸", "douyinGiftNum": 10}, {"jsonId": "151032", "name": "2级红孩儿", "camp": 2, "eliteMonster": 1, "monsterType": 1003, "monsterLevel": 2, "monsterHp": 5000, "attack": "10|20", "callNum": 1, "animationScale": "1", "attackCooldown": "2", "attacKeffect": "160005", "attacKeffectInterval": "0.5", "movementSpeed": 100, "rebornTime": 10, "monsterScore": 10, "moveAnimation": "340510", "attackAnimation": "340410", "giftId": "300004", "giftName": "礼物2", "kuaishouGiftId": "11582", "kuaishouGiftName": "助威火炬", "kuaishouGiftNum": 20, "douyinGiftId": "28rYzVFNyXEXFC8HI+f/WG+I7a6lfl3OyZZjUS+CVuwCgYZrPrUdytGHu0c=", "douyinGiftName": "能力药丸", "douyinGiftNum": 20}, {"jsonId": "151033", "name": "3级红孩儿", "camp": 2, "eliteMonster": 1, "monsterType": 1003, "monsterLevel": 3, "monsterHp": 5000, "attack": "10|20", "callNum": 1, "animationScale": "1", "attackCooldown": "2", "attacKeffect": "160005", "attacKeffectInterval": "0.5", "movementSpeed": 100, "rebornTime": 10, "monsterScore": 10, "moveAnimation": "340510", "attackAnimation": "340410", "giftId": "300004", "giftName": "礼物2", "kuaishouGiftId": "11582", "kuaishouGiftName": "助威火炬", "kuaishouGiftNum": 30, "douyinGiftId": "28rYzVFNyXEXFC8HI+f/WG+I7a6lfl3OyZZjUS+CVuwCgYZrPrUdytGHu0c=", "douyinGiftName": "能力药丸", "douyinGiftNum": 30}, {"jsonId": "151034", "name": "4级红孩儿", "camp": 2, "eliteMonster": 1, "monsterType": 1003, "monsterLevel": 4, "monsterHp": 5000, "attack": "10|20", "callNum": 1, "animationScale": "1", "attackCooldown": "2", "attacKeffect": "160005", "attacKeffectInterval": "0.5", "movementSpeed": 100, "rebornTime": 10, "monsterScore": 10, "moveAnimation": "340510", "attackAnimation": "340410", "giftId": "300004", "giftName": "礼物2", "kuaishouGiftId": "11582", "kuaishouGiftName": "助威火炬", "kuaishouGiftNum": 40, "douyinGiftId": "28rYzVFNyXEXFC8HI+f/WG+I7a6lfl3OyZZjUS+CVuwCgYZrPrUdytGHu0c=", "douyinGiftName": "能力药丸", "douyinGiftNum": 40}, {"jsonId": "151035", "name": "5级红孩儿", "camp": 2, "eliteMonster": 1, "monsterType": 1003, "monsterLevel": 5, "monsterHp": 5000, "attack": "10|20", "callNum": 1, "animationScale": "1", "attackCooldown": "2", "attacKeffect": "160005", "attacKeffectInterval": "0.5", "movementSpeed": 100, "rebornTime": 10, "monsterScore": 10, "moveAnimation": "340510", "attackAnimation": "340410", "giftId": "300004", "giftName": "礼物2", "kuaishouGiftId": "11582", "kuaishouGiftName": "助威火炬", "kuaishouGiftNum": 50, "douyinGiftId": "28rYzVFNyXEXFC8HI+f/WG+I7a6lfl3OyZZjUS+CVuwCgYZrPrUdytGHu0c=", "douyinGiftName": "能力药丸", "douyinGiftNum": 50}, {"jsonId": "151041", "name": "1级黑熊精", "camp": 2, "eliteMonster": 1, "monsterType": 1004, "monsterLevel": 1, "monsterHp": 5000, "attack": "10|20", "callNum": 1, "animationScale": "1", "attackCooldown": "2", "attacKeffect": "160003", "attacKeffectInterval": "0.5", "movementSpeed": 100, "rebornTime": 10, "monsterScore": 10, "moveAnimation": "340511", "attackAnimation": "340411", "giftId": "300005", "giftName": "礼物3", "kuaishouGiftId": "11606", "kuaishouGiftName": "助威火箭", "kuaishouGiftNum": 10, "douyinGiftId": "fJs8HKQ0xlPRixn8JAUiL2gFRiLD9S6IFCFdvZODSnhyo9YN8q7xUuVVyZI=", "douyinGiftName": "魔法镜", "douyinGiftNum": 10}, {"jsonId": "151042", "name": "2级黑熊精", "camp": 2, "eliteMonster": 1, "monsterType": 1004, "monsterLevel": 2, "monsterHp": 5000, "attack": "10|20", "callNum": 1, "animationScale": "1", "attackCooldown": "2", "attacKeffect": "160003", "attacKeffectInterval": "0.5", "movementSpeed": 100, "rebornTime": 10, "monsterScore": 10, "moveAnimation": "340511", "attackAnimation": "340411", "giftId": "300005", "giftName": "礼物3", "kuaishouGiftId": "11606", "kuaishouGiftName": "助威火箭", "kuaishouGiftNum": 20, "douyinGiftId": "fJs8HKQ0xlPRixn8JAUiL2gFRiLD9S6IFCFdvZODSnhyo9YN8q7xUuVVyZI=", "douyinGiftName": "魔法镜", "douyinGiftNum": 20}, {"jsonId": "151043", "name": "3级黑熊精", "camp": 2, "eliteMonster": 1, "monsterType": 1004, "monsterLevel": 3, "monsterHp": 5000, "attack": "10|20", "callNum": 1, "animationScale": "1", "attackCooldown": "2", "attacKeffect": "160003", "attacKeffectInterval": "0.5", "movementSpeed": 100, "rebornTime": 10, "monsterScore": 10, "moveAnimation": "340511", "attackAnimation": "340411", "giftId": "300005", "giftName": "礼物3", "kuaishouGiftId": "11606", "kuaishouGiftName": "助威火箭", "kuaishouGiftNum": 30, "douyinGiftId": "fJs8HKQ0xlPRixn8JAUiL2gFRiLD9S6IFCFdvZODSnhyo9YN8q7xUuVVyZI=", "douyinGiftName": "魔法镜", "douyinGiftNum": 30}, {"jsonId": "151044", "name": "4级黑熊精", "camp": 2, "eliteMonster": 1, "monsterType": 1004, "monsterLevel": 4, "monsterHp": 5000, "attack": "10|20", "callNum": 1, "animationScale": "1", "attackCooldown": "2", "attacKeffect": "160003", "attacKeffectInterval": "0.5", "movementSpeed": 100, "rebornTime": 10, "monsterScore": 10, "moveAnimation": "340511", "attackAnimation": "340411", "giftId": "300005", "giftName": "礼物3", "kuaishouGiftId": "11606", "kuaishouGiftName": "助威火箭", "kuaishouGiftNum": 40, "douyinGiftId": "fJs8HKQ0xlPRixn8JAUiL2gFRiLD9S6IFCFdvZODSnhyo9YN8q7xUuVVyZI=", "douyinGiftName": "魔法镜", "douyinGiftNum": 40}, {"jsonId": "151045", "name": "5级黑熊精", "camp": 2, "eliteMonster": 1, "monsterType": 1004, "monsterLevel": 5, "monsterHp": 5000, "attack": "10|20", "callNum": 1, "animationScale": "1", "attackCooldown": "2", "attacKeffect": "160003", "attacKeffectInterval": "0.5", "movementSpeed": 100, "rebornTime": 10, "monsterScore": 10, "moveAnimation": "340511", "attackAnimation": "340411", "giftId": "300005", "giftName": "礼物3", "kuaishouGiftId": "11606", "kuaishouGiftName": "助威火箭", "kuaishouGiftNum": 50, "douyinGiftId": "fJs8HKQ0xlPRixn8JAUiL2gFRiLD9S6IFCFdvZODSnhyo9YN8q7xUuVVyZI=", "douyinGiftName": "魔法镜", "douyinGiftNum": 50}, {"jsonId": "151051", "name": "1级金翅大鹏", "camp": 2, "eliteMonster": 1, "monsterType": 1005, "monsterLevel": 1, "monsterHp": 5000, "attack": "10|20", "callNum": 1, "animationScale": "1", "attackCooldown": "2", "attacKeffect": "160010", "attacKeffectInterval": "0.5", "skill": 182001, "movementSpeed": 100, "rebornTime": 10, "monsterScore": 10, "moveAnimation": "340512", "attackAnimation": "340412", "giftId": "300006", "giftName": "礼物4", "kuaishouGiftId": "11586", "kuaishouGiftName": "冲鸭", "kuaishouGiftNum": 10, "douyinGiftId": "PJ0FFeaDzXUreuUBZH6Hs+b56Jh0tQjrq0bIrrlZmv13GSAL9Q1hf59fjGk=", "douyinGiftName": "甜甜圈", "douyinGiftNum": 10}, {"jsonId": "151052", "name": "2级金翅大鹏", "camp": 2, "eliteMonster": 1, "monsterType": 1005, "monsterLevel": 2, "monsterHp": 5000, "attack": "10|20", "callNum": 1, "animationScale": "1", "attackCooldown": "2", "attacKeffect": "160010", "attacKeffectInterval": "0.5", "skill": 182002, "movementSpeed": 100, "rebornTime": 10, "monsterScore": 10, "moveAnimation": "340512", "attackAnimation": "340412", "giftId": "300006", "giftName": "礼物4", "kuaishouGiftId": "11586", "kuaishouGiftName": "冲鸭", "kuaishouGiftNum": 20, "douyinGiftId": "PJ0FFeaDzXUreuUBZH6Hs+b56Jh0tQjrq0bIrrlZmv13GSAL9Q1hf59fjGk=", "douyinGiftName": "甜甜圈", "douyinGiftNum": 20}, {"jsonId": "151053", "name": "3级金翅大鹏", "camp": 2, "eliteMonster": 1, "monsterType": 1005, "monsterLevel": 3, "monsterHp": 5000, "attack": "10|20", "callNum": 1, "animationScale": "1", "attackCooldown": "2", "attacKeffect": "160010", "attacKeffectInterval": "0.5", "skill": 182003, "movementSpeed": 100, "rebornTime": 10, "monsterScore": 10, "moveAnimation": "340512", "attackAnimation": "340412", "giftId": "300006", "giftName": "礼物4", "kuaishouGiftId": "11586", "kuaishouGiftName": "冲鸭", "kuaishouGiftNum": 30, "douyinGiftId": "PJ0FFeaDzXUreuUBZH6Hs+b56Jh0tQjrq0bIrrlZmv13GSAL9Q1hf59fjGk=", "douyinGiftName": "甜甜圈", "douyinGiftNum": 30}, {"jsonId": "151054", "name": "4级金翅大鹏", "camp": 2, "eliteMonster": 1, "monsterType": 1005, "monsterLevel": 4, "monsterHp": 5000, "attack": "10|20", "callNum": 1, "animationScale": "1", "attackCooldown": "2", "attacKeffect": "160010", "attacKeffectInterval": "0.5", "skill": 182004, "movementSpeed": 100, "rebornTime": 10, "monsterScore": 10, "moveAnimation": "340512", "attackAnimation": "340412", "giftId": "300006", "giftName": "礼物4", "kuaishouGiftId": "11586", "kuaishouGiftName": "冲鸭", "kuaishouGiftNum": 40, "douyinGiftId": "PJ0FFeaDzXUreuUBZH6Hs+b56Jh0tQjrq0bIrrlZmv13GSAL9Q1hf59fjGk=", "douyinGiftName": "甜甜圈", "douyinGiftNum": 40}, {"jsonId": "151055", "name": "5级金翅大鹏", "camp": 2, "eliteMonster": 1, "monsterType": 1005, "monsterLevel": 5, "monsterHp": 5000, "attack": "10|20", "callNum": 1, "animationScale": "1", "attackCooldown": "2", "attacKeffect": "160010", "attacKeffectInterval": "0.5", "skill": 182005, "movementSpeed": 100, "rebornTime": 10, "monsterScore": 10, "moveAnimation": "340512", "attackAnimation": "340412", "giftId": "300006", "giftName": "礼物4", "kuaishouGiftId": "11586", "kuaishouGiftName": "冲鸭", "kuaishouGiftNum": 50, "douyinGiftId": "PJ0FFeaDzXUreuUBZH6Hs+b56Jh0tQjrq0bIrrlZmv13GSAL9Q1hf59fjGk=", "douyinGiftName": "甜甜圈", "douyinGiftNum": 50}, {"jsonId": "151061", "name": "1级孙悟空", "camp": 2, "eliteMonster": 1, "monsterType": 1006, "monsterLevel": 1, "monsterHp": 5000, "attack": "10|20", "callNum": 1, "animationScale": "2", "attackCooldown": "2", "attacKeffect": "160004", "attacKeffectInterval": "0.5", "skill": 182011, "movementSpeed": 100, "rebornTime": 10, "monsterScore": 10, "moveAnimation": "340513", "attackAnimation": "340413", "skillAnimation": "340613", "giftId": "300007", "giftName": "礼物5", "kuaishouGiftId": "11587", "kuaishouGiftName": "浪漫满屏", "kuaishouGiftNum": 10, "douyinGiftId": "gx7pmjQfhBaDOG2XkWI2peZ66YFWkCWRjZXpTqb23O/epru+sxWyTV/3Ufs=", "douyinGiftName": "恶魔炸弹", "douyinGiftNum": 10}, {"jsonId": "151062", "name": "2级孙悟空", "camp": 2, "eliteMonster": 1, "monsterType": 1006, "monsterLevel": 2, "monsterHp": 5000, "attack": "10|20", "callNum": 1, "animationScale": "1", "attackCooldown": "2", "attacKeffect": "160004", "attacKeffectInterval": "0.5", "skill": 182012, "movementSpeed": 100, "rebornTime": 10, "monsterScore": 10, "moveAnimation": "340513", "attackAnimation": "340413", "skillAnimation": "340613", "giftId": "300007", "giftName": "礼物5", "kuaishouGiftId": "11587", "kuaishouGiftName": "浪漫满屏", "kuaishouGiftNum": 20, "douyinGiftId": "gx7pmjQfhBaDOG2XkWI2peZ66YFWkCWRjZXpTqb23O/epru+sxWyTV/3Ufs=", "douyinGiftName": "恶魔炸弹", "douyinGiftNum": 20}, {"jsonId": "151063", "name": "3级孙悟空", "camp": 2, "eliteMonster": 1, "monsterType": 1006, "monsterLevel": 3, "monsterHp": 5000, "attack": "10|20", "callNum": 1, "animationScale": "1", "attackCooldown": "2", "attacKeffect": "160004", "attacKeffectInterval": "0.5", "skill": 182013, "movementSpeed": 100, "rebornTime": 10, "monsterScore": 10, "moveAnimation": "340513", "attackAnimation": "340413", "skillAnimation": "340613", "giftId": "300007", "giftName": "礼物5", "kuaishouGiftId": "11587", "kuaishouGiftName": "浪漫满屏", "kuaishouGiftNum": 30, "douyinGiftId": "gx7pmjQfhBaDOG2XkWI2peZ66YFWkCWRjZXpTqb23O/epru+sxWyTV/3Ufs=", "douyinGiftName": "恶魔炸弹", "douyinGiftNum": 30}, {"jsonId": "151064", "name": "4级孙悟空", "camp": 2, "eliteMonster": 1, "monsterType": 1006, "monsterLevel": 4, "monsterHp": 5000, "attack": "10|20", "callNum": 1, "animationScale": "1", "attackCooldown": "2", "attacKeffect": "160004", "attacKeffectInterval": "0.5", "skill": 182014, "movementSpeed": 100, "rebornTime": 10, "monsterScore": 10, "moveAnimation": "340513", "attackAnimation": "340413", "skillAnimation": "340613", "giftId": "300007", "giftName": "礼物5", "kuaishouGiftId": "11587", "kuaishouGiftName": "浪漫满屏", "kuaishouGiftNum": 40, "douyinGiftId": "gx7pmjQfhBaDOG2XkWI2peZ66YFWkCWRjZXpTqb23O/epru+sxWyTV/3Ufs=", "douyinGiftName": "恶魔炸弹", "douyinGiftNum": 40}, {"jsonId": "151065", "name": "5级孙悟空", "camp": 2, "eliteMonster": 1, "monsterType": 1006, "monsterLevel": 5, "monsterHp": 5000, "attack": "10|20", "callNum": 1, "animationScale": "1", "attackCooldown": "2", "attacKeffect": "160004", "attacKeffectInterval": "0.5", "skill": 182015, "movementSpeed": 100, "rebornTime": 10, "monsterScore": 10, "moveAnimation": "340513", "attackAnimation": "340413", "skillAnimation": "340613", "giftId": "300007", "giftName": "礼物5", "kuaishouGiftId": "11587", "kuaishouGiftName": "浪漫满屏", "kuaishouGiftNum": 50, "douyinGiftId": "gx7pmjQfhBaDOG2XkWI2peZ66YFWkCWRjZXpTqb23O/epru+sxWyTV/3Ufs=", "douyinGiftName": "恶魔炸弹", "douyinGiftNum": 50}, {"jsonId": "151071", "name": "1级牛魔王", "camp": 2, "eliteMonster": 1, "monsterType": 1007, "monsterLevel": 1, "monsterHp": 5000, "attack": "10|20", "callNum": 1, "animationScale": "1", "attackCooldown": "2", "attacKeffect": "160004", "attacKeffectInterval": "0.5", "skill": 182025, "movementSpeed": 100, "rebornTime": 10, "monsterScore": 10, "moveAnimation": "340514", "attackAnimation": "340414", "skillAnimation": "340614", "giftId": "300008", "giftName": "礼物6", "kuaishouGiftId": "11587", "kuaishouGiftName": "浪漫满屏", "kuaishouGiftNum": 10, "douyinGiftName": "神秘空投", "douyinGiftNum": 10}, {"jsonId": "151072", "name": "2级牛魔王", "camp": 2, "eliteMonster": 1, "monsterType": 1007, "monsterLevel": 2, "monsterHp": 5000, "attack": "10|20", "callNum": 1, "animationScale": "1", "attackCooldown": "2", "attacKeffect": "160004", "attacKeffectInterval": "0.5", "skill": 182025, "movementSpeed": 100, "rebornTime": 10, "monsterScore": 10, "moveAnimation": "340514", "attackAnimation": "340414", "skillAnimation": "340614", "giftId": "300008", "giftName": "礼物6", "kuaishouGiftId": "11587", "kuaishouGiftName": "浪漫满屏", "kuaishouGiftNum": 20, "douyinGiftName": "神秘空投", "douyinGiftNum": 20}, {"jsonId": "151073", "name": "3级牛魔王", "camp": 2, "eliteMonster": 1, "monsterType": 1007, "monsterLevel": 3, "monsterHp": 5000, "attack": "10|20", "callNum": 1, "animationScale": "1", "attackCooldown": "2", "attacKeffect": "160004", "attacKeffectInterval": "0.5", "skill": 182025, "movementSpeed": 100, "rebornTime": 10, "monsterScore": 10, "moveAnimation": "340514", "attackAnimation": "340414", "skillAnimation": "340614", "giftId": "300008", "giftName": "礼物6", "kuaishouGiftId": "11587", "kuaishouGiftName": "浪漫满屏", "kuaishouGiftNum": 30, "douyinGiftName": "神秘空投", "douyinGiftNum": 30}, {"jsonId": "151074", "name": "4级牛魔王", "camp": 2, "eliteMonster": 1, "monsterType": 1007, "monsterLevel": 4, "monsterHp": 5000, "attack": "10|20", "callNum": 1, "animationScale": "1", "attackCooldown": "2", "attacKeffect": "160004", "attacKeffectInterval": "0.5", "skill": 182025, "movementSpeed": 100, "rebornTime": 10, "monsterScore": 10, "moveAnimation": "340514", "attackAnimation": "340414", "skillAnimation": "340614", "giftId": "300008", "giftName": "礼物6", "kuaishouGiftId": "11587", "kuaishouGiftName": "浪漫满屏", "kuaishouGiftNum": 40, "douyinGiftName": "神秘空投", "douyinGiftNum": 40}, {"jsonId": "151075", "name": "5级牛魔王", "camp": 2, "eliteMonster": 1, "monsterType": 1007, "monsterLevel": 5, "monsterHp": 5000, "attack": "10|20", "callNum": 1, "animationScale": "1", "attackCooldown": "2", "attacKeffect": "160004", "attacKeffectInterval": "0.5", "skill": 182025, "movementSpeed": 100, "rebornTime": 10, "monsterScore": 10, "moveAnimation": "340514", "attackAnimation": "340414", "skillAnimation": "340614", "giftId": "300008", "giftName": "礼物6", "kuaishouGiftId": "11587", "kuaishouGiftName": "浪漫满屏", "kuaishouGiftNum": 50, "douyinGiftName": "神秘空投", "douyinGiftNum": 50}], "attacKeffect.json": [{"jsonId": "160001", "name": "通用刀光", "type": 1, "attacDistance": 20, "Animation": "340001"}, {"jsonId": "160002", "name": "蓝色刀光", "type": 1, "attacDistance": 20, "Animation": "340002"}, {"jsonId": "160003", "name": "紫色刀光", "type": 1, "attacDistance": 20, "Animation": "340003"}, {"jsonId": "160004", "name": "红色刀光", "type": 1, "attacDistance": 20, "Animation": "340004"}, {"jsonId": "160005", "name": "横向红刀光", "type": 1, "attacDistance": 20, "Animation": "340005"}, {"jsonId": "160006", "name": "远程火球", "type": 2, "attacDistance": 500, "Animation": "340006", "impactAnimation": "340101"}, {"jsonId": "160007", "name": "远程剑气", "type": 2, "attacDistance": 500, "Animation": "340007", "impactAnimation": "340102"}, {"jsonId": "160008", "name": "双刀", "type": 5, "attacDistance": 20, "doubleHit": 2, "Animation": "340004"}, {"jsonId": "160009", "name": "三刀", "type": 5, "attacDistance": 20, "doubleHit": 3, "Animation": "340004"}, {"jsonId": "160010", "name": "羽毛", "type": 2, "attacDistance": 400, "Animation": "340008"}], "skin.json": [{"jsonId": "170001", "name": "角色外观1", "camp": 1, "HP": 9999999999, "attack": "10|20", "attackCooldown": "0.3", "attacKeffect": "160007", "moveAnimation": "340801", "attackAnimation": "340701", "InitialIimage": 1, "skinRole": "tianbing01", "openCondition": 0, "barrage": "换1"}, {"jsonId": "170002", "name": "角色外观2", "camp": 1, "HP": 9999999999, "attack": "10|20", "attackCooldown": "0.3", "attacKeffect": "160007", "moveAnimation": "340802", "attackAnimation": "340702", "InitialIimage": 0, "skinRole": "tianbing02", "openCondition": 100, "barrage": "换2"}, {"jsonId": "170003", "name": "角色外观3", "camp": 1, "HP": 9999999999, "attack": "10|20", "attackCooldown": "0.3", "attacKeffect": "160007", "moveAnimation": "340803", "attackAnimation": "340703", "InitialIimage": 0, "skinRole": "tianbing03", "openCondition": 200, "barrage": "换3"}, {"jsonId": "170004", "name": "角色外观4", "camp": 1, "HP": 9999999999, "attack": "10|20", "attackCooldown": "0.3", "attacKeffect": "160007", "moveAnimation": "340804", "attackAnimation": "340704", "InitialIimage": 0, "skinRole": "tianbing04", "openCondition": 300, "barrage": "换4"}, {"jsonId": "170005", "name": "角色外观5", "camp": 1, "HP": 9999999999, "attack": "10|20", "attackCooldown": "0.3", "attacKeffect": "160007", "moveAnimation": "340805", "attackAnimation": "340705", "InitialIimage": 0, "skinRole": "tianbing05", "openCondition": 500, "barrage": "换5"}, {"jsonId": "170006", "name": "角色外观6", "camp": 1, "HP": 9999999999, "attack": "10|20", "attackCooldown": "0.3", "attacKeffect": "160007", "moveAnimation": "340806", "attackAnimation": "340706", "InitialIimage": 0, "skinRole": "tianbing06", "openCondition": 10000, "barrage": "换6"}, {"jsonId": "170007", "name": "角色外观7", "camp": 1, "HP": 9999999999, "attack": "10|20", "attackCooldown": "0.3", "attacKeffect": "160007", "moveAnimation": "340807", "attackAnimation": "340707", "InitialIimage": 0, "skinRole": "tianbing07", "openCondition": 20000, "barrage": "换7"}], "skill.json": [{"jsonId": "180001", "name": "飞剑", "type": 1, "skillDescribe": "发出飞剑攻击敌方", "skillLevel": 1, "skillDamage": 100, "skillCooldown": 3, "skillRange": 0, "skillAnimation": "340301", "skillTarget": 1}, {"jsonId": "180002", "name": "巨型飞剑", "type": 2, "skillDescribe": "发出飞剑攻击敌方", "skillLevel": 1, "skillDamage": 200, "skillCooldown": 3, "skillRange": 0, "skillAnimation": "340303", "impactAnimation": "340304", "skillTarget": 1}, {"jsonId": "180003", "name": "天火", "type": 3, "skillDescribe": "发出火球攻击敌人", "skillLevel": 1, "skillDamage": 300, "skillCooldown": 3, "skillRange": 0, "skillAnimation": "340305", "impactAnimation": "340306", "skillTarget": 1}, {"jsonId": "180004", "name": "闪电", "type": 4, "skillDescribe": "发出连锁闪电攻击敌人", "skillLevel": 1, "skillDamage": 400, "skillCooldown": 3, "lightningNum": 5, "skillRange": 0, "skillTarget": 1}, {"jsonId": "180005", "name": "飞沙走石", "type": 5, "skillDescribe": "召唤一阵旋风攻击敌人并减少敌人移动速度5秒", "skillLevel": 1, "skillDamage": 500, "skillCooldown": 3, "state": "190030", "skillRange": 200, "skillAnimation": "340312", "impactAnimation": "340307", "skillTarget": 1}, {"jsonId": "180006", "name": "护天阵", "type": 6, "skillDescribe": "召唤法阵，南天门无敌20秒", "skillLevel": 1, "skillCooldown": -1, "guardTime": 20, "skillRange": 0, "skillTarget": 1}, {"jsonId": "180007", "name": "三昧真火", "type": 7, "skillDescribe": "召唤三味真火对范围敌人造成伤害，并施加灼烧状态", "skillLevel": 1, "skillDamage": 400, "skillCooldown": 3, "state": "190011", "skillRange": 200, "skillAnimation": "340308", "skillTarget": 1}, {"jsonId": "180008", "name": "天外陨石", "type": 8, "skillDescribe": "召唤陨石砸向敌人，造成发范围伤害并施加晕眩状态", "skillLevel": 1, "skillDamage": 500, "skillCooldown": 3, "state": "190031", "skillRange": 500, "skillAnimation": "340309", "impactAnimation": "340310", "skillTarget": 1}, {"jsonId": "180009", "name": "修复法阵", "type": 9, "skillDescribe": "3秒内回复南天门20%血量", "skillLevel": 1, "skillCooldown": 1, "healing": 20, "healingTime": 3, "skillRange": 0, "skillTarget": 1}, {"jsonId": "181011", "name": "1级天火", "type": 203, "skillDescribe": "发出火球攻击敌人", "skillLevel": 1, "skillDamage": 100, "skillCooldown": 3, "skillRange": 0, "skillAnimation": "340305", "impactAnimation": "340306", "skillTarget": 1}, {"jsonId": "181012", "name": "2级天火", "type": 203, "skillDescribe": "发出火球攻击敌人", "skillLevel": 2, "skillDamage": 200, "skillCooldown": 3, "skillRange": 0, "skillAnimation": "340305", "impactAnimation": "340306", "skillTarget": 1}, {"jsonId": "181013", "name": "3级天火", "type": 203, "skillDescribe": "发出火球攻击敌人", "skillLevel": 3, "skillDamage": 300, "skillCooldown": 3, "skillRange": 0, "skillAnimation": "340305", "impactAnimation": "340306", "skillTarget": 1}, {"jsonId": "181014", "name": "4级天火", "type": 203, "skillDescribe": "发出火球攻击敌人", "skillLevel": 4, "skillDamage": 400, "skillCooldown": 3, "skillRange": 0, "skillAnimation": "340305", "impactAnimation": "340306", "skillTarget": 1}, {"jsonId": "181015", "name": "5级天火", "type": 203, "skillDescribe": "发出火球攻击敌人", "skillLevel": 5, "skillDamage": 500, "skillCooldown": 3, "skillRange": 0, "skillAnimation": "340305", "impactAnimation": "340306", "skillTarget": 1}, {"jsonId": "181021", "name": "1级闪电", "type": 204, "skillDescribe": "发出连锁闪电攻击敌人", "skillLevel": 1, "skillDamage": 50, "skillCooldown": 3, "lightningNum": 5, "skillRange": 0, "skillTarget": 1}, {"jsonId": "181022", "name": "2级闪电", "type": 204, "skillDescribe": "发出连锁闪电攻击敌人", "skillLevel": 2, "skillDamage": 100, "skillCooldown": 3, "lightningNum": 5, "skillRange": 0, "skillTarget": 1}, {"jsonId": "181023", "name": "3级闪电", "type": 204, "skillDescribe": "发出连锁闪电攻击敌人", "skillLevel": 3, "skillDamage": 150, "skillCooldown": 3, "lightningNum": 5, "skillRange": 0, "skillTarget": 1}, {"jsonId": "181024", "name": "4级闪电", "type": 204, "skillDescribe": "发出连锁闪电攻击敌人", "skillLevel": 4, "skillDamage": 200, "skillCooldown": 3, "lightningNum": 5, "skillRange": 0, "skillTarget": 1}, {"jsonId": "181025", "name": "5级闪电", "type": 204, "skillDescribe": "发出连锁闪电攻击敌人", "skillLevel": 5, "skillDamage": 250, "skillCooldown": 3, "lightningNum": 5, "skillRange": 0, "skillTarget": 1}, {"jsonId": "181031", "name": "1级飞沙走石", "type": 205, "skillDescribe": "召唤一阵旋风攻击敌人并减少敌人移动速度5秒", "skillLevel": 1, "skillDamage": 500, "skillCooldown": 3, "state": "190030", "skillRange": 200, "skillAnimation": "340312", "impactAnimation": "340307", "skillTarget": 1}, {"jsonId": "181032", "name": "2级飞沙走石", "type": 205, "skillDescribe": "召唤一阵旋风攻击敌人并减少敌人移动速度5秒", "skillLevel": 2, "skillDamage": 1000, "skillCooldown": 3, "state": "190030", "skillRange": 200, "skillAnimation": "340312", "impactAnimation": "340307", "skillTarget": 1}, {"jsonId": "181033", "name": "3级飞沙走石", "type": 205, "skillDescribe": "召唤一阵旋风攻击敌人并减少敌人移动速度5秒", "skillLevel": 3, "skillDamage": 1500, "skillCooldown": 3, "state": "190030", "skillRange": 200, "skillAnimation": "340312", "impactAnimation": "340307", "skillTarget": 1}, {"jsonId": "181034", "name": "4级飞沙走石", "type": 205, "skillDescribe": "召唤一阵旋风攻击敌人并减少敌人移动速度5秒", "skillLevel": 4, "skillDamage": 2000, "skillCooldown": 3, "state": "190030", "skillRange": 200, "skillAnimation": "340312", "impactAnimation": "340307", "skillTarget": 1}, {"jsonId": "181035", "name": "5级飞沙走石", "type": 205, "skillDescribe": "召唤一阵旋风攻击敌人并减少敌人移动速度5秒", "skillLevel": 5, "skillDamage": 2500, "skillCooldown": 3, "state": "190030", "skillRange": 200, "skillAnimation": "340312", "impactAnimation": "340307", "skillTarget": 1}, {"jsonId": "181051", "name": "1级三昧真火", "type": 207, "skillDescribe": "召唤三味真火对范围敌人造成伤害，并施加灼烧状态", "skillLevel": 1, "skillDamage": 1000, "skillCooldown": 3, "state": "190011", "skillRange": 200, "skillAnimation": "340308", "skillTarget": 1}, {"jsonId": "181052", "name": "2级三昧真火", "type": 207, "skillDescribe": "召唤三味真火对范围敌人造成伤害，并施加灼烧状态", "skillLevel": 2, "skillDamage": 2000, "skillCooldown": 3, "state": "190012", "skillRange": 200, "skillAnimation": "340308", "skillTarget": 1}, {"jsonId": "181053", "name": "3级三昧真火", "type": 207, "skillDescribe": "召唤三味真火对范围敌人造成伤害，并施加灼烧状态", "skillLevel": 3, "skillDamage": 3000, "skillCooldown": 3, "state": "190013", "skillRange": 200, "skillAnimation": "340308", "skillTarget": 1}, {"jsonId": "181054", "name": "4级三昧真火", "type": 207, "skillDescribe": "召唤三味真火对范围敌人造成伤害，并施加灼烧状态", "skillLevel": 4, "skillDamage": 4000, "skillCooldown": 3, "state": "190014", "skillRange": 200, "skillAnimation": "340308", "skillTarget": 1}, {"jsonId": "181055", "name": "5级三昧真火", "type": 207, "skillDescribe": "召唤三味真火对范围敌人造成伤害，并施加灼烧状态", "skillLevel": 5, "skillDamage": 5000, "skillCooldown": 3, "state": "190015", "skillRange": 200, "skillAnimation": "340308", "skillTarget": 1}, {"jsonId": "181061", "name": "1级天外陨石", "type": 208, "skillDescribe": "召唤陨石砸向敌人，造成发范围伤害并施加晕眩状态", "skillLevel": 1, "skillDamage": 2000, "skillCooldown": 3, "state": "190031", "skillRange": 500, "skillAnimation": "340309", "impactAnimation": "340310", "skillTarget": 1}, {"jsonId": "181062", "name": "2级天外陨石", "type": 208, "skillDescribe": "召唤陨石砸向敌人，造成发范围伤害并施加晕眩状态", "skillLevel": 2, "skillDamage": 4000, "skillCooldown": 3, "state": "190032", "skillRange": 500, "skillAnimation": "340309", "impactAnimation": "340310", "skillTarget": 1}, {"jsonId": "181063", "name": "3级天外陨石", "type": 208, "skillDescribe": "召唤陨石砸向敌人，造成发范围伤害并施加晕眩状态", "skillLevel": 3, "skillDamage": 6000, "skillCooldown": 3, "state": "190033", "skillRange": 500, "skillAnimation": "340309", "impactAnimation": "340310", "skillTarget": 1}, {"jsonId": "181064", "name": "4级天外陨石", "type": 208, "skillDescribe": "召唤陨石砸向敌人，造成发范围伤害并施加晕眩状态", "skillLevel": 4, "skillDamage": 8000, "skillCooldown": 3, "state": "190034", "skillRange": 500, "skillAnimation": "340309", "impactAnimation": "340310", "skillTarget": 1}, {"jsonId": "181065", "name": "5级天外陨石", "type": 208, "skillDescribe": "召唤陨石砸向敌人，造成发范围伤害并施加晕眩状态", "skillLevel": 5, "skillDamage": 10000, "skillCooldown": 3, "state": "190035", "skillRange": 500, "skillAnimation": "340309", "impactAnimation": "340310", "skillTarget": 1}, {"jsonId": "182001", "name": "1级金翅大鹏光环", "type": 101, "skillDescribe": "增加附近友方单位10%移速", "skillLevel": 1, "skillCooldown": -1, "state": "190021", "skillRange": 200, "skillAnimation": "340201", "skillTarget": 3}, {"jsonId": "182002", "name": "2级金翅大鹏光环", "type": 101, "skillDescribe": "增加附近友方单位20%移速", "skillLevel": 2, "skillCooldown": -1, "state": "190022", "skillRange": 200, "skillAnimation": "340201", "skillTarget": 3}, {"jsonId": "182003", "name": "3级金翅大鹏光环", "type": 101, "skillDescribe": "增加附近友方单位30%移速", "skillLevel": 3, "skillCooldown": -1, "state": "190023", "skillRange": 200, "skillAnimation": "340201", "skillTarget": 3}, {"jsonId": "182004", "name": "4级金翅大鹏光环", "type": 101, "skillDescribe": "增加附近友方单位40%移速", "skillLevel": 4, "skillCooldown": -1, "state": "190024", "skillRange": 200, "skillAnimation": "340201", "skillTarget": 3}, {"jsonId": "182005", "name": "5级金翅大鹏光环", "type": 101, "skillDescribe": "增加附近友方单位50%移速", "skillLevel": 5, "skillCooldown": -1, "state": "190025", "skillRange": 200, "skillAnimation": "340201", "skillTarget": 3}, {"jsonId": "182011", "name": "1级孙悟空缓速咒", "type": 102, "skillDescribe": "增加技能间隔10%", "skillLevel": 1, "skillCooldown": 3, "state": "190061", "skillRange": 10000, "skillAnimation": "340202", "skillTarget": 1}, {"jsonId": "182012", "name": "2级孙悟空缓速咒", "type": 102, "skillDescribe": "增加技能间隔20%", "skillLevel": 2, "skillCooldown": 3, "state": "190062", "skillRange": 10000, "skillAnimation": "340202", "skillTarget": 1}, {"jsonId": "182013", "name": "3级孙悟空缓速咒", "type": 102, "skillDescribe": "增加技能间隔30%", "skillLevel": 3, "skillCooldown": 3, "state": "190063", "skillRange": 10000, "skillAnimation": "340202", "skillTarget": 1}, {"jsonId": "182014", "name": "4级孙悟空缓速咒", "type": 102, "skillDescribe": "增加技能间隔40%", "skillLevel": 4, "skillCooldown": 3, "state": "190064", "skillRange": 10000, "skillAnimation": "340202", "skillTarget": 1}, {"jsonId": "182015", "name": "5级孙悟空缓速咒", "type": 102, "skillDescribe": "增加技能间隔50%", "skillLevel": 5, "skillCooldown": 3, "state": "190065", "skillRange": 10000, "skillAnimation": "340202", "skillTarget": 1}, {"jsonId": "182021", "name": "1级牛魔王晕眩", "type": 103, "skillDescribe": "晕眩敌方所有单位3秒", "skillLevel": 1, "skillCooldown": 10, "state": "190033", "skillRange": 10000, "skillAnimation": "340203", "skillTarget": 1}, {"jsonId": "182022", "name": "2级牛魔王晕眩", "type": 103, "skillDescribe": "晕眩敌方所有单位3秒", "skillLevel": 2, "skillCooldown": 10, "state": "190033", "skillRange": 10000, "skillAnimation": "340203", "skillTarget": 1}, {"jsonId": "182023", "name": "3级牛魔王晕眩", "type": 103, "skillDescribe": "晕眩敌方所有单位3秒", "skillLevel": 3, "skillCooldown": 10, "state": "190033", "skillRange": 10000, "skillAnimation": "340203", "skillTarget": 1}, {"jsonId": "182024", "name": "4级牛魔王晕眩", "type": 103, "skillDescribe": "晕眩敌方所有单位3秒", "skillLevel": 4, "skillCooldown": 10, "state": "190033", "skillRange": 10000, "skillAnimation": "340203", "skillTarget": 1}, {"jsonId": "182025", "name": "5级牛魔王晕眩", "type": 103, "skillDescribe": "晕眩敌方所有单位3秒", "skillLevel": 5, "skillCooldown": 10, "state": "190033", "skillRange": 10000, "skillAnimation": "340203", "skillTarget": 1}, {"jsonId": "182031", "name": "一级狂暴", "type": 104, "skillDescribe": "生命值下降到50%后获得攻击加速", "skillLevel": 1, "hpDeclinePercentage": "0.5", "state": "190060", "skillTarget": 2}], "state.json": [{"jsonId": "190011", "name": "1级灼烧", "type": 1, "stateLevel": 1, "stateDescribe": "10", "hurt": 5, "hurtInterval": 1, "icon": "icon_zhu<PERSON>ao"}, {"jsonId": "190012", "name": "2级灼烧", "type": 1, "stateLevel": 2, "stateDescribe": "10", "hurt": 10, "hurtInterval": 1, "icon": "icon_zhu<PERSON>ao"}, {"jsonId": "190013", "name": "3级灼烧", "type": 1, "stateLevel": 3, "stateDescribe": "10", "hurt": 15, "hurtInterval": 1, "icon": "icon_zhu<PERSON>ao"}, {"jsonId": "190014", "name": "4级灼烧", "type": 1, "stateLevel": 4, "stateDescribe": "10", "hurt": 20, "hurtInterval": 1, "icon": "icon_zhu<PERSON>ao"}, {"jsonId": "190015", "name": "5级灼烧", "type": 1, "stateLevel": 5, "stateDescribe": "10", "hurt": 25, "hurtInterval": 1, "icon": "icon_zhu<PERSON>ao"}, {"jsonId": "190021", "name": "增加移动速度10%", "type": 2, "stateLevel": 1, "stateDescribe": "10", "percentage": "1.1", "icon": "icon_yisu"}, {"jsonId": "190022", "name": "增加移动速度20%", "type": 2, "stateLevel": 2, "stateDescribe": "10", "percentage": "1.2", "icon": "icon_yisu"}, {"jsonId": "190023", "name": "增加移动速度30%", "type": 2, "stateLevel": 3, "stateDescribe": "10", "percentage": "1.3", "icon": "icon_yisu"}, {"jsonId": "190024", "name": "增加移动速度40%", "type": 2, "stateLevel": 4, "stateDescribe": "10", "percentage": "1.4", "icon": "icon_yisu"}, {"jsonId": "190025", "name": "增加移动速度50%", "type": 2, "stateLevel": 5, "stateDescribe": "10", "percentage": "1.5", "icon": "icon_yisu"}, {"jsonId": "190026", "name": "减少移动速度10%", "type": 2, "stateLevel": 1, "stateDescribe": "10", "percentage": "0.9", "icon": "icon_yisu"}, {"jsonId": "190027", "name": "减少移动速度20%", "type": 2, "stateLevel": 2, "stateDescribe": "10", "percentage": "0.8", "icon": "icon_yisu"}, {"jsonId": "190028", "name": "减少移动速度30%", "type": 2, "stateLevel": 3, "stateDescribe": "10", "percentage": "0.7", "icon": "icon_yisu"}, {"jsonId": "190029", "name": "减少移动速度40%", "type": 2, "stateLevel": 4, "stateDescribe": "10", "percentage": "0.6", "icon": "icon_yisu"}, {"jsonId": "190030", "name": "减少移动速度50%", "type": 2, "stateLevel": 5, "stateDescribe": "10", "percentage": "0.5", "icon": "icon_yisu"}, {"jsonId": "190031", "name": "1级晕眩", "type": 3, "stateLevel": 1, "stateDescribe": "10", "icon": "icon_yunxuan"}, {"jsonId": "190032", "name": "2级晕眩", "type": 3, "stateLevel": 2, "stateDescribe": "10", "icon": "icon_yunxuan"}, {"jsonId": "190033", "name": "3级晕眩", "type": 3, "stateLevel": 3, "stateDescribe": "10", "icon": "icon_yunxuan"}, {"jsonId": "190034", "name": "4级晕眩", "type": 3, "stateLevel": 4, "stateDescribe": "10", "icon": "icon_yunxuan"}, {"jsonId": "190035", "name": "5级晕眩", "type": 3, "stateLevel": 5, "stateDescribe": "10", "icon": "icon_yunxuan"}, {"jsonId": "190041", "name": "增加攻击力10%", "type": 4, "stateLevel": 1, "stateDescribe": "10", "percentage": "1.1", "icon": "icon_gongjiji<PERSON>u"}, {"jsonId": "190042", "name": "增加攻击力20%", "type": 4, "stateLevel": 2, "stateDescribe": "10", "percentage": "1.2", "icon": "icon_gongjiji<PERSON>u"}, {"jsonId": "190043", "name": "增加攻击力30%", "type": 4, "stateLevel": 3, "stateDescribe": "10", "percentage": "1.3", "icon": "icon_gongjiji<PERSON>u"}, {"jsonId": "190044", "name": "增加攻击力40%", "type": 4, "stateLevel": 4, "stateDescribe": "10", "percentage": "1.4", "icon": "icon_gongjiji<PERSON>u"}, {"jsonId": "190045", "name": "增加攻击力50%", "type": 4, "stateLevel": 5, "stateDescribe": "10", "percentage": "1.5", "icon": "icon_gongjiji<PERSON>u"}, {"jsonId": "190046", "name": "减少攻击力10%", "type": 4, "stateLevel": 1, "stateDescribe": "10", "percentage": "0.9", "icon": "icon_gongjiji<PERSON>u"}, {"jsonId": "190047", "name": "减少攻击力20%", "type": 4, "stateLevel": 2, "stateDescribe": "10", "percentage": "0.8", "icon": "icon_gongjiji<PERSON>u"}, {"jsonId": "190048", "name": "减少攻击力30%", "type": 4, "stateLevel": 3, "stateDescribe": "10", "percentage": "0.7", "icon": "icon_gongjiji<PERSON>u"}, {"jsonId": "190049", "name": "减少攻击力40%", "type": 4, "stateLevel": 4, "stateDescribe": "10", "percentage": "0.6", "icon": "icon_gongjiji<PERSON>u"}, {"jsonId": "190050", "name": "减少攻击力50%", "type": 4, "stateLevel": 5, "stateDescribe": "10", "percentage": "0.5", "icon": "icon_gongjiji<PERSON>u"}, {"jsonId": "190051", "name": "增加攻击间隔10%", "type": 5, "stateLevel": 1, "stateDescribe": "10", "percentage": "1.1", "icon": "icon_jiansu"}, {"jsonId": "190052", "name": "增加攻击间隔20%", "type": 5, "stateLevel": 2, "stateDescribe": "10", "percentage": "1.2", "icon": "icon_jiansu"}, {"jsonId": "190053", "name": "增加攻击间隔30%", "type": 5, "stateLevel": 3, "stateDescribe": "10", "percentage": "1.3", "icon": "icon_jiansu"}, {"jsonId": "190054", "name": "增加攻击间隔40%", "type": 5, "stateLevel": 4, "stateDescribe": "10", "percentage": "1.4", "icon": "icon_jiansu"}, {"jsonId": "190055", "name": "增加攻击间隔50%", "type": 5, "stateLevel": 5, "stateDescribe": "10", "percentage": "1.5", "icon": "icon_jiansu"}, {"jsonId": "190056", "name": "减少攻击间隔10%", "type": 5, "stateLevel": 1, "stateDescribe": "10", "percentage": "0.9", "icon": "icon_jiansu"}, {"jsonId": "190057", "name": "减少攻击间隔20%", "type": 5, "stateLevel": 2, "stateDescribe": "10", "percentage": "0.8", "icon": "icon_jiansu"}, {"jsonId": "190058", "name": "减少攻击间隔30%", "type": 5, "stateLevel": 3, "stateDescribe": "10", "percentage": "0.7", "icon": "icon_jiansu"}, {"jsonId": "190059", "name": "减少攻击间隔40%", "type": 5, "stateLevel": 4, "stateDescribe": "10", "percentage": "0.6", "icon": "icon_jiansu"}, {"jsonId": "190060", "name": "减少攻击间隔50%", "type": 5, "stateLevel": 5, "stateDescribe": "10", "percentage": "0.5", "icon": "icon_jiansu"}, {"jsonId": "190061", "name": "增加技能间隔10%", "type": 6, "stateLevel": 1, "stateDescribe": "10", "percentage": "1.1", "icon": "icon_jiansu"}, {"jsonId": "190062", "name": "增加技能间隔20%", "type": 6, "stateLevel": 2, "stateDescribe": "10", "percentage": "1.2", "icon": "icon_jiansu"}, {"jsonId": "190063", "name": "增加技能间隔30%", "type": 6, "stateLevel": 3, "stateDescribe": "10", "percentage": "1.3", "icon": "icon_jiansu"}, {"jsonId": "190064", "name": "增加技能间隔40%", "type": 6, "stateLevel": 4, "stateDescribe": "10", "percentage": "1.4", "icon": "icon_jiansu"}, {"jsonId": "190065", "name": "增加技能间隔50%", "type": 6, "stateLevel": 5, "stateDescribe": "10", "percentage": "1.5", "icon": "icon_jiansu"}, {"jsonId": "190066", "name": "减少技能间隔10%", "type": 6, "stateLevel": 1, "stateDescribe": "10", "percentage": "0.9", "icon": "icon_jiansu"}, {"jsonId": "190067", "name": "减少技能间隔20%", "type": 6, "stateLevel": 2, "stateDescribe": "10", "percentage": "0.8", "icon": "icon_jiansu"}, {"jsonId": "190068", "name": "减少技能间隔30%", "type": 6, "stateLevel": 3, "stateDescribe": "10", "percentage": "0.7", "icon": "icon_jiansu"}, {"jsonId": "190069", "name": "减少技能间隔40%", "type": 6, "stateLevel": 4, "stateDescribe": "10", "percentage": "0.6", "icon": "icon_jiansu"}, {"jsonId": "190070", "name": "减少技能间隔50%", "type": 6, "stateLevel": 5, "stateDescribe": "10", "percentage": "0.5", "icon": "icon_jiansu"}], "weapon.json": [{"jsonId": "200001", "name": "飞剑", "showWeapon": 1, "weaponType": 1, "weaponLevel": 1, "skilId": "180001", "skillNum": 5, "douyinGiftNum": 1}, {"jsonId": "200002", "name": "巨型飞剑", "showWeapon": 1, "weaponType": 2, "weaponLevel": 1, "skilId": "180002", "skillNum": 5, "douyinGiftNum": 10}, {"jsonId": "200003", "name": "天火", "showWeapon": 1, "weaponType": 3, "describe": "单体高攻击", "weaponLevel": 1, "skilId": "180003", "skillNum": 5, "kuaishouGiftId": "11582", "kuaishouGiftName": "助威火炬", "kuaishouGiftNum": 1, "douyinGiftId": "n1/Dg1905sj1FyoBlQBvmbaDZFBNaKuKZH6zxHkv8Lg5x2cRfrKUTb8gzMs=", "douyinGiftName": "仙女棒", "douyinGiftNum": 1}, {"jsonId": "200004", "name": "闪电", "showWeapon": 1, "weaponType": 4, "describe": "群体伤害", "weaponLevel": 1, "skilId": "180004", "skillNum": 5, "kuaishouGiftId": "11582", "kuaishouGiftName": "助威火炬", "kuaishouGiftNum": 1, "douyinGiftId": "28rYzVFNyXEXFC8HI+f/WG+I7a6lfl3OyZZjUS+CVuwCgYZrPrUdytGHu0c=", "douyinGiftName": "能力药丸", "douyinGiftNum": 1}, {"jsonId": "200005", "name": "飞沙走石", "showWeapon": 1, "weaponType": 5, "describe": "群体伤害", "weaponLevel": 1, "skilId": "180005", "skillNum": 5, "kuaishouGiftId": "11606", "kuaishouGiftName": "助威火箭", "kuaishouGiftNum": 1, "douyinGiftId": "fJs8HKQ0xlPRixn8JAUiL2gFRiLD9S6IFCFdvZODSnhyo9YN8q7xUuVVyZI=", "douyinGiftName": "魔法镜", "douyinGiftNum": 1}, {"jsonId": "200007", "name": "三昧真火", "showWeapon": 1, "weaponType": 7, "describe": "持续伤害", "weaponLevel": 1, "skilId": "180007", "skillNum": 5, "kuaishouGiftId": "11587", "kuaishouGiftName": "浪漫满屏", "kuaishouGiftNum": 1, "douyinGiftId": "gx7pmjQfhBaDOG2XkWI2peZ66YFWkCWRjZXpTqb23O/epru+sxWyTV/3Ufs=", "douyinGiftName": "恶魔炸弹", "douyinGiftNum": 1}, {"jsonId": "200008", "name": "天外陨石", "showWeapon": 1, "weaponType": 8, "describe": "全体晕眩", "weaponLevel": 1, "skilId": "180008", "skillNum": 5, "kuaishouGiftId": "11587", "kuaishouGiftName": "浪漫满屏", "kuaishouGiftNum": 1, "douyinGiftId": "pGLo7HKNk1i4djkicmJXf6iWEyd+pfPBjbsHmd3WcX0Ierm2UdnRR7UINvI=", "douyinGiftName": "神秘空投", "douyinGiftNum": 1}, {"jsonId": "200009", "name": "修复法阵", "showWeapon": 1, "weaponType": 9, "describe": "恢复20%血量", "weaponLevel": 1, "skilId": "180009", "skillNum": 1, "kuaishouGiftId": "11587", "kuaishouGiftName": "浪漫满屏", "kuaishouGiftNum": 1, "douyinGiftId": "IkkadLfz7O/a5UR45p/OOCCG6ewAWVbsuzR/Z+v1v76CBU+mTG/wPjqdpfg=", "douyinGiftName": "能量电池", "douyinGiftNum": 1}, {"jsonId": "201001", "name": "1级火灵珠", "showWeapon": 2, "weaponType": 1001, "weaponLevel": 1, "skilId": "181011", "skillNum": -1, "icon": "1zhuqueshan", "kuaishouGiftId": "11582", "kuaishouGiftName": "助威火炬", "kuaishouGiftNum": 10, "douyinGiftId": "n1/Dg1905sj1FyoBlQBvmbaDZFBNaKuKZH6zxHkv8Lg5x2cRfrKUTb8gzMs=", "douyinGiftName": "仙女棒", "douyinGiftNum": 10}, {"jsonId": "201002", "name": "2级火灵珠", "showWeapon": 2, "weaponType": 1001, "weaponLevel": 2, "skilId": "181012", "skillNum": -1, "icon": "1zhuqueshan", "kuaishouGiftId": "11582", "kuaishouGiftName": "助威火炬", "kuaishouGiftNum": 20, "douyinGiftId": "n1/Dg1905sj1FyoBlQBvmbaDZFBNaKuKZH6zxHkv8Lg5x2cRfrKUTb8gzMs=", "douyinGiftName": "仙女棒", "douyinGiftNum": 20}, {"jsonId": "201003", "name": "3级火灵珠", "showWeapon": 2, "weaponType": 1001, "weaponLevel": 3, "skilId": "181013", "skillNum": -1, "icon": "1zhuqueshan", "kuaishouGiftId": "11582", "kuaishouGiftName": "助威火炬", "kuaishouGiftNum": 30, "douyinGiftId": "n1/Dg1905sj1FyoBlQBvmbaDZFBNaKuKZH6zxHkv8Lg5x2cRfrKUTb8gzMs=", "douyinGiftName": "仙女棒", "douyinGiftNum": 30}, {"jsonId": "201004", "name": "4级火灵珠", "showWeapon": 2, "weaponType": 1001, "weaponLevel": 4, "skilId": "181014", "skillNum": -1, "icon": "1zhuqueshan", "kuaishouGiftId": "11582", "kuaishouGiftName": "助威火炬", "kuaishouGiftNum": 40, "douyinGiftId": "n1/Dg1905sj1FyoBlQBvmbaDZFBNaKuKZH6zxHkv8Lg5x2cRfrKUTb8gzMs=", "douyinGiftName": "仙女棒", "douyinGiftNum": 40}, {"jsonId": "201005", "name": "5级火灵珠", "showWeapon": 2, "weaponType": 1001, "weaponLevel": 5, "skilId": "181015", "skillNum": -1, "icon": "1zhuqueshan", "kuaishouGiftId": "11582", "kuaishouGiftName": "助威火炬", "kuaishouGiftNum": 50, "douyinGiftId": "n1/Dg1905sj1FyoBlQBvmbaDZFBNaKuKZH6zxHkv8Lg5x2cRfrKUTb8gzMs=", "douyinGiftName": "仙女棒", "douyinGiftNum": 50}, {"jsonId": "201011", "name": "1级五雷钉", "showWeapon": 2, "weaponType": 1002, "weaponLevel": 1, "skilId": "181021", "skillNum": -1, "icon": "2<PERSON><PERSON><PERSON><PERSON>", "kuaishouGiftId": "11582", "kuaishouGiftName": "助威火炬", "kuaishouGiftNum": 10, "douyinGiftId": "28rYzVFNyXEXFC8HI+f/WG+I7a6lfl3OyZZjUS+CVuwCgYZrPrUdytGHu0c=", "douyinGiftName": "能力药丸", "douyinGiftNum": 10}, {"jsonId": "201012", "name": "2级五雷钉", "showWeapon": 2, "weaponType": 1002, "weaponLevel": 2, "skilId": "181022", "skillNum": -1, "icon": "2<PERSON><PERSON><PERSON><PERSON>", "kuaishouGiftId": "11582", "kuaishouGiftName": "助威火炬", "kuaishouGiftNum": 20, "douyinGiftId": "28rYzVFNyXEXFC8HI+f/WG+I7a6lfl3OyZZjUS+CVuwCgYZrPrUdytGHu0c=", "douyinGiftName": "能力药丸", "douyinGiftNum": 20}, {"jsonId": "201013", "name": "3级五雷钉", "showWeapon": 2, "weaponType": 1002, "weaponLevel": 3, "skilId": "181023", "skillNum": -1, "icon": "2<PERSON><PERSON><PERSON><PERSON>", "kuaishouGiftId": "11582", "kuaishouGiftName": "助威火炬", "kuaishouGiftNum": 30, "douyinGiftId": "28rYzVFNyXEXFC8HI+f/WG+I7a6lfl3OyZZjUS+CVuwCgYZrPrUdytGHu0c=", "douyinGiftName": "能力药丸", "douyinGiftNum": 30}, {"jsonId": "201014", "name": "4级五雷钉", "showWeapon": 2, "weaponType": 1002, "weaponLevel": 4, "skilId": "181024", "skillNum": -1, "icon": "2<PERSON><PERSON><PERSON><PERSON>", "kuaishouGiftId": "11582", "kuaishouGiftName": "助威火炬", "kuaishouGiftNum": 40, "douyinGiftId": "28rYzVFNyXEXFC8HI+f/WG+I7a6lfl3OyZZjUS+CVuwCgYZrPrUdytGHu0c=", "douyinGiftName": "能力药丸", "douyinGiftNum": 40}, {"jsonId": "201015", "name": "5级五雷钉", "showWeapon": 2, "weaponType": 1002, "weaponLevel": 5, "skilId": "181025", "skillNum": -1, "icon": "2<PERSON><PERSON><PERSON><PERSON>", "kuaishouGiftId": "11582", "kuaishouGiftName": "助威火炬", "kuaishouGiftNum": 50, "douyinGiftId": "28rYzVFNyXEXFC8HI+f/WG+I7a6lfl3OyZZjUS+CVuwCgYZrPrUdytGHu0c=", "douyinGiftName": "能力药丸", "douyinGiftNum": 50}, {"jsonId": "201021", "name": "1级混元伞", "showWeapon": 2, "weaponType": 1003, "weaponLevel": 1, "skilId": "181031", "skillNum": -1, "icon": "3hunyuansan", "kuaishouGiftId": "11606", "kuaishouGiftName": "助威火箭", "kuaishouGiftNum": 10, "douyinGiftId": "PJ0FFeaDzXUreuUBZH6Hs+b56Jh0tQjrq0bIrrlZmv13GSAL9Q1hf59fjGk=", "douyinGiftName": "甜甜圈", "douyinGiftNum": 10}, {"jsonId": "201022", "name": "2级混元伞", "showWeapon": 2, "weaponType": 1003, "weaponLevel": 2, "skilId": "181032", "skillNum": -1, "icon": "3hunyuansan", "kuaishouGiftId": "11606", "kuaishouGiftName": "助威火箭", "kuaishouGiftNum": 20, "douyinGiftId": "PJ0FFeaDzXUreuUBZH6Hs+b56Jh0tQjrq0bIrrlZmv13GSAL9Q1hf59fjGk=", "douyinGiftName": "甜甜圈", "douyinGiftNum": 20}, {"jsonId": "201023", "name": "3级混元伞", "showWeapon": 2, "weaponType": 1003, "weaponLevel": 3, "skilId": "181033", "skillNum": -1, "icon": "3hunyuansan", "kuaishouGiftId": "11606", "kuaishouGiftName": "助威火箭", "kuaishouGiftNum": 30, "douyinGiftId": "PJ0FFeaDzXUreuUBZH6Hs+b56Jh0tQjrq0bIrrlZmv13GSAL9Q1hf59fjGk=", "douyinGiftName": "甜甜圈", "douyinGiftNum": 30}, {"jsonId": "201024", "name": "4级混元伞", "showWeapon": 2, "weaponType": 1003, "weaponLevel": 4, "skilId": "181034", "skillNum": -1, "icon": "3hunyuansan", "kuaishouGiftId": "11606", "kuaishouGiftName": "助威火箭", "kuaishouGiftNum": 40, "douyinGiftId": "PJ0FFeaDzXUreuUBZH6Hs+b56Jh0tQjrq0bIrrlZmv13GSAL9Q1hf59fjGk=", "douyinGiftName": "甜甜圈", "douyinGiftNum": 40}, {"jsonId": "201025", "name": "5级混元伞", "showWeapon": 2, "weaponType": 1003, "weaponLevel": 5, "skilId": "181035", "skillNum": -1, "icon": "3hunyuansan", "kuaishouGiftId": "11606", "kuaishouGiftName": "助威火箭", "kuaishouGiftNum": 50, "douyinGiftId": "PJ0FFeaDzXUreuUBZH6Hs+b56Jh0tQjrq0bIrrlZmv13GSAL9Q1hf59fjGk=", "douyinGiftName": "甜甜圈", "douyinGiftNum": 50}, {"jsonId": "201031", "name": "1级八卦炉", "showWeapon": 2, "weaponType": 1004, "weaponLevel": 1, "skilId": "181051", "skillNum": -1, "icon": "4bagualu", "kuaishouGiftId": "11586", "kuaishouGiftName": "冲鸭", "kuaishouGiftNum": 10, "douyinGiftId": "gx7pmjQfhBaDOG2XkWI2peZ66YFWkCWRjZXpTqb23O/epru+sxWyTV/3Ufs=", "douyinGiftName": "恶魔炸弹", "douyinGiftNum": 10}, {"jsonId": "201032", "name": "2级八卦炉", "showWeapon": 2, "weaponType": 1004, "weaponLevel": 2, "skilId": "181052", "skillNum": -1, "icon": "4bagualu", "kuaishouGiftId": "11586", "kuaishouGiftName": "冲鸭", "kuaishouGiftNum": 20, "douyinGiftId": "gx7pmjQfhBaDOG2XkWI2peZ66YFWkCWRjZXpTqb23O/epru+sxWyTV/3Ufs=", "douyinGiftName": "恶魔炸弹", "douyinGiftNum": 20}, {"jsonId": "201033", "name": "3级八卦炉", "showWeapon": 2, "weaponType": 1004, "weaponLevel": 3, "skilId": "181053", "skillNum": -1, "icon": "4bagualu", "kuaishouGiftId": "11586", "kuaishouGiftName": "冲鸭", "kuaishouGiftNum": 30, "douyinGiftId": "gx7pmjQfhBaDOG2XkWI2peZ66YFWkCWRjZXpTqb23O/epru+sxWyTV/3Ufs=", "douyinGiftName": "恶魔炸弹", "douyinGiftNum": 30}, {"jsonId": "201034", "name": "4级八卦炉", "showWeapon": 2, "weaponType": 1004, "weaponLevel": 4, "skilId": "181054", "skillNum": -1, "icon": "4bagualu", "kuaishouGiftId": "11586", "kuaishouGiftName": "冲鸭", "kuaishouGiftNum": 40, "douyinGiftId": "gx7pmjQfhBaDOG2XkWI2peZ66YFWkCWRjZXpTqb23O/epru+sxWyTV/3Ufs=", "douyinGiftName": "恶魔炸弹", "douyinGiftNum": 40}, {"jsonId": "201035", "name": "5级八卦炉", "showWeapon": 2, "weaponType": 1004, "weaponLevel": 5, "skilId": "181055", "skillNum": -1, "icon": "4bagualu", "kuaishouGiftId": "11586", "kuaishouGiftName": "冲鸭", "kuaishouGiftNum": 50, "douyinGiftId": "gx7pmjQfhBaDOG2XkWI2peZ66YFWkCWRjZXpTqb23O/epru+sxWyTV/3Ufs=", "douyinGiftName": "恶魔炸弹", "douyinGiftNum": 50}, {"jsonId": "201041", "name": "1级女娲石", "showWeapon": 2, "weaponType": 1005, "weaponLevel": 1, "skilId": "181061", "skillNum": -1, "icon": "5nvwashi", "kuaishouGiftId": "11587", "kuaishouGiftName": "浪漫满屏", "kuaishouGiftNum": 10, "douyinGiftId": "pGLo7HKNk1i4djkicmJXf6iWEyd+pfPBjbsHmd3WcX0Ierm2UdnRR7UINvI=", "douyinGiftName": "神秘空投", "douyinGiftNum": 10}, {"jsonId": "201042", "name": "2级女娲石", "showWeapon": 2, "weaponType": 1005, "weaponLevel": 2, "skilId": "181062", "skillNum": -1, "icon": "5nvwashi", "kuaishouGiftId": "11587", "kuaishouGiftName": "浪漫满屏", "kuaishouGiftNum": 20, "douyinGiftId": "pGLo7HKNk1i4djkicmJXf6iWEyd+pfPBjbsHmd3WcX0Ierm2UdnRR7UINvI=", "douyinGiftName": "神秘空投", "douyinGiftNum": 20}, {"jsonId": "201043", "name": "3级女娲石", "showWeapon": 2, "weaponType": 1005, "weaponLevel": 3, "skilId": "181063", "skillNum": -1, "icon": "5nvwashi", "kuaishouGiftId": "11587", "kuaishouGiftName": "浪漫满屏", "kuaishouGiftNum": 30, "douyinGiftId": "pGLo7HKNk1i4djkicmJXf6iWEyd+pfPBjbsHmd3WcX0Ierm2UdnRR7UINvI=", "douyinGiftName": "神秘空投", "douyinGiftNum": 30}, {"jsonId": "201044", "name": "4级女娲石", "showWeapon": 2, "weaponType": 1005, "weaponLevel": 4, "skilId": "181064", "skillNum": -1, "icon": "5nvwashi", "kuaishouGiftId": "11587", "kuaishouGiftName": "浪漫满屏", "kuaishouGiftNum": 40, "douyinGiftId": "pGLo7HKNk1i4djkicmJXf6iWEyd+pfPBjbsHmd3WcX0Ierm2UdnRR7UINvI=", "douyinGiftName": "神秘空投", "douyinGiftNum": 40}, {"jsonId": "201045", "name": "5级女娲石", "showWeapon": 2, "weaponType": 1005, "weaponLevel": 5, "skilId": "181065", "skillNum": -1, "icon": "5nvwashi", "kuaishouGiftId": "11587", "kuaishouGiftName": "浪漫满屏", "kuaishouGiftNum": 50, "douyinGiftId": "pGLo7HKNk1i4djkicmJXf6iWEyd+pfPBjbsHmd3WcX0Ierm2UdnRR7UINvI=", "douyinGiftName": "神秘空投", "douyinGiftNum": 50}], "gift.json": [{"jsonId": "300001", "name": "点赞", "giftType": 3, "automationCamp": 0, "integra": 1, "integralUpperLimit": 100}, {"jsonId": "300002", "name": "666", "giftType": 2, "automationCamp": 0, "integra": 1, "integralUpperLimit": 100}, {"jsonId": "300003", "name": "礼物1", "giftType": 1, "kuaishouGiftId": "11582", "kuaishouGiftName": "助威火炬", "douyinGiftId": "n1/Dg1905sj1FyoBlQBvmbaDZFBNaKuKZH6zxHkv8Lg5x2cRfrKUTb8gzMs=", "douyinGiftName": "仙女棒", "automationCamp": 1, "integra": 10, "integralUpperLimit": 1000, "douyinGiftPicture": "101xiannvbang"}, {"jsonId": "300004", "name": "礼物2", "giftType": 1, "kuaishouGiftId": "11582", "kuaishouGiftName": "助威火炬", "douyinGiftId": "28rYzVFNyXEXFC8HI+f/WG+I7a6lfl3OyZZjUS+CVuwCgYZrPrUdytGHu0c=", "douyinGiftName": "能力药丸", "automationCamp": 1, "integra": 100, "integralUpperLimit": 5000, "douyinGiftPicture": "102nengliyaowan"}, {"jsonId": "300005", "name": "礼物3", "giftType": 1, "kuaishouGiftId": "11606", "kuaishouGiftName": "助威火箭", "douyinGiftId": "fJs8HKQ0xlPRixn8JAUiL2gFRiLD9S6IFCFdvZODSnhyo9YN8q7xUuVVyZI=", "douyinGiftName": "魔法镜", "automationCamp": 1, "integra": 500, "integralUpperLimit": 100000, "douyinGiftPicture": "103mofajing"}, {"jsonId": "300006", "name": "礼物4", "giftType": 1, "kuaishouGiftId": "11586", "kuaishouGiftName": "冲鸭", "douyinGiftId": "IkkadLfz7O/a5UR45p/OOCCG6ewAWVbsuzR/Z+v1v76CBU+mTG/wPjqdpfg=", "douyinGiftName": "能量电池", "automationCamp": 1, "integra": 1000, "integralUpperLimit": 200000, "douyinGiftPicture": "104nengliangdianchi"}, {"jsonId": "300007", "name": "礼物5", "giftType": 1, "kuaishouGiftId": "11587", "kuaishouGiftName": "浪漫满屏", "douyinGiftId": "gx7pmjQfhBaDOG2XkWI2peZ66YFWkCWRjZXpTqb23O/epru+sxWyTV/3Ufs=", "douyinGiftName": "恶魔炸弹", "automationCamp": 1, "integra": 5000, "integralUpperLimit": 300000, "douyinGiftPicture": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"jsonId": "300008", "name": "礼物6", "giftType": 1, "douyinGiftId": "pGLo7HKNk1i4djkicmJXf6iWEyd+pfPBjbsHmd3WcX0Ierm2UdnRR7UINvI=", "douyinGiftName": "神秘空投", "automationCamp": 1, "integra": 10000, "integralUpperLimit": 400000, "douyinGiftPicture": "106shenmikongtou"}], "selectDifficulty.json": [{"jsonId": "310001", "name": "防守模式", "Hp": "100000", "time": "900|1200|18000|3600"}], "gameLevel.json": [{"jsonId": "320001", "name": "第1关", "gameLevel": 1}, {"jsonId": "320002", "name": "第2关", "gameLevel": 2}, {"jsonId": "320003", "name": "第3关", "gameLevel": 3}, {"jsonId": "320004", "name": "第4关", "gameLevel": 4}, {"jsonId": "320005", "name": "第5关", "gameLevel": 5}, {"jsonId": "320006", "name": "第6关", "gameLevel": 6}, {"jsonId": "320007", "name": "第7关", "gameLevel": 7}, {"jsonId": "320008", "name": "第8关", "gameLevel": 8}, {"jsonId": "320009", "name": "第9关", "gameLevel": 9}, {"jsonId": "320010", "name": "第10关", "gameLevel": 10}], "monsterRefresh.json": [{"jsonId": "330101", "name": "第1关第1波", "gameLevel": "320001", "refresh": 1, "monsterId": "150011", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 1, "refreshInterval": 2}, {"jsonId": "330102", "name": "第1关第2波", "gameLevel": "320001", "refresh": 2, "monsterId": "150021", "monsterTotal": 1, "monsterInterval": "0.5", "monsterNum": 3, "type": 1, "refreshInterval": 2, "preview": "<PERSON><PERSON>_juren<PERSON>"}, {"jsonId": "330103", "name": "第1关第3波", "gameLevel": "320001", "refresh": 3, "monsterId": "151061", "monsterTotal": 1, "monsterInterval": "0.5", "monsterNum": 3, "type": 3, "refreshInterval": 2, "preview": "Yw_jingshi", "admission": "BOSSwuk"}, {"jsonId": "330201", "name": "第2关第1波", "gameLevel": "320002", "refresh": 4, "monsterId": "151011", "monsterTotal": 1, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2, "preview": "Lz_bianfuwang"}, {"jsonId": "330202", "name": "第2关第2波", "gameLevel": "320002", "refresh": 5, "monsterId": "150051", "monsterTotal": 1, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2, "preview": "Lz_dapeng"}, {"jsonId": "330203", "name": "第2关第3波", "gameLevel": "320002", "refresh": 6, "monsterId": "150061", "monsterTotal": 1, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2, "preview": "Lz_heixiongjing"}, {"jsonId": "330204", "name": "第2关第4波", "gameLevel": "320002", "refresh": 7, "monsterId": "150071", "monsterTotal": 1, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2, "preview": "<PERSON><PERSON>_ho<PERSON><PERSON><PERSON>"}, {"jsonId": "330205", "name": "第2关第5波", "gameLevel": "320002", "refresh": 8, "monsterId": "151011", "monsterTotal": 1, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2, "preview": "Lz_jinzhidapeng"}, {"jsonId": "330301", "name": "第3关第1波", "gameLevel": "320003", "refresh": 9, "monsterId": "151021", "monsterTotal": 1, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2, "preview": "<PERSON><PERSON>_juren<PERSON>"}, {"jsonId": "330302", "name": "第3关第2波", "gameLevel": "320003", "refresh": 10, "monsterId": "151031", "monsterTotal": 1, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2, "preview": "Lz_linngligui"}, {"jsonId": "330303", "name": "第3关第3波", "gameLevel": "320003", "refresh": 11, "monsterId": "151041", "monsterTotal": 1, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2, "preview": "Lz_niumowang"}, {"jsonId": "330304", "name": "第3关第4波", "gameLevel": "320003", "refresh": 12, "monsterId": "151051", "monsterTotal": 1, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2, "preview": "Lz_sunwukong"}, {"jsonId": "330305", "name": "第3关第5波", "gameLevel": "320003", "refresh": 13, "monsterId": "151061", "monsterTotal": 1, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2, "preview": "Lz_xiniujing"}, {"jsonId": "330306", "name": "第3关第6波", "gameLevel": "320003", "refresh": 14, "monsterId": "151071", "monsterTotal": 1, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2, "preview": "Lz_ye<PERSON>hujing"}, {"jsonId": "330307", "name": "第3关第7波", "gameLevel": "320003", "refresh": 15, "monsterId": "150011", "monsterTotal": 1, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2, "preview": "Lz_zongzuanfeng"}, {"jsonId": "330308", "name": "第3关第8波", "gameLevel": "320003", "refresh": 16, "monsterId": "150021", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330309", "name": "第3关第9波", "gameLevel": "320003", "refresh": 17, "monsterId": "150031", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330310", "name": "第3关第10波", "gameLevel": "320003", "refresh": 18, "monsterId": "150041", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330401", "name": "第4关第1波", "gameLevel": "320004", "refresh": 19, "monsterId": "150051", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330402", "name": "第4关第2波", "gameLevel": "320004", "refresh": 20, "monsterId": "150061", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330403", "name": "第4关第3波", "gameLevel": "320004", "refresh": 21, "monsterId": "150071", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330404", "name": "第4关第4波", "gameLevel": "320004", "refresh": 22, "monsterId": "151011", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330405", "name": "第4关第5波", "gameLevel": "320004", "refresh": 23, "monsterId": "151021", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330406", "name": "第4关第6波", "gameLevel": "320004", "refresh": 24, "monsterId": "151031", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330407", "name": "第4关第7波", "gameLevel": "320004", "refresh": 25, "monsterId": "151041", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330408", "name": "第4关第8波", "gameLevel": "320004", "refresh": 26, "monsterId": "151051", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330409", "name": "第4关第9波", "gameLevel": "320004", "refresh": 27, "monsterId": "151061", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330410", "name": "第4关第10波", "gameLevel": "320004", "refresh": 28, "monsterId": "151071", "monsterTotal": 1, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330501", "name": "第5关第1波", "gameLevel": "320005", "refresh": 29, "monsterId": "150011", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330502", "name": "第5关第2波", "gameLevel": "320005", "refresh": 30, "monsterId": "150021", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330503", "name": "第5关第3波", "gameLevel": "320005", "refresh": 31, "monsterId": "150031", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330504", "name": "第5关第4波", "gameLevel": "320005", "refresh": 32, "monsterId": "150041", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330505", "name": "第5关第5波", "gameLevel": "320005", "refresh": 33, "monsterId": "150051", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330506", "name": "第5关第6波", "gameLevel": "320005", "refresh": 34, "monsterId": "150061", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330507", "name": "第5关第7波", "gameLevel": "320005", "refresh": 35, "monsterId": "150071", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330508", "name": "第5关第8波", "gameLevel": "320005", "refresh": 36, "monsterId": "151011", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330509", "name": "第5关第9波", "gameLevel": "320005", "refresh": 37, "monsterId": "151021", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330510", "name": "第5关第10波", "gameLevel": "320005", "refresh": 38, "monsterId": "151031", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330601", "name": "第6关第1波", "gameLevel": "320006", "refresh": 39, "monsterId": "151041", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330602", "name": "第6关第2波", "gameLevel": "320006", "refresh": 40, "monsterId": "151051", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330603", "name": "第6关第3波", "gameLevel": "320006", "refresh": 41, "monsterId": "151061", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330604", "name": "第6关第4波", "gameLevel": "320006", "refresh": 42, "monsterId": "151071", "monsterTotal": 1, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330605", "name": "第6关第5波", "gameLevel": "320006", "refresh": 43, "monsterId": "150011", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330606", "name": "第6关第6波", "gameLevel": "320006", "refresh": 44, "monsterId": "150021", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330607", "name": "第6关第7波", "gameLevel": "320006", "refresh": 45, "monsterId": "150031", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330608", "name": "第6关第8波", "gameLevel": "320006", "refresh": 46, "monsterId": "150041", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330609", "name": "第6关第9波", "gameLevel": "320006", "refresh": 47, "monsterId": "150051", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330610", "name": "第6关第10波", "gameLevel": "320006", "refresh": 48, "monsterId": "150061", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330701", "name": "第7关第1波", "gameLevel": "320007", "refresh": 49, "monsterId": "150071", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330702", "name": "第7关第2波", "gameLevel": "320007", "refresh": 50, "monsterId": "151011", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330703", "name": "第7关第3波", "gameLevel": "320007", "refresh": 51, "monsterId": "151021", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330704", "name": "第7关第4波", "gameLevel": "320007", "refresh": 52, "monsterId": "151031", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330705", "name": "第7关第5波", "gameLevel": "320007", "refresh": 53, "monsterId": "151041", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330706", "name": "第7关第6波", "gameLevel": "320007", "refresh": 54, "monsterId": "151051", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330707", "name": "第7关第7波", "gameLevel": "320007", "refresh": 55, "monsterId": "151061", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330708", "name": "第7关第8波", "gameLevel": "320007", "refresh": 56, "monsterId": "151071", "monsterTotal": 1, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330709", "name": "第7关第9波", "gameLevel": "320007", "refresh": 57, "monsterId": "150011", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330710", "name": "第7关第10波", "gameLevel": "320007", "refresh": 58, "monsterId": "150021", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330801", "name": "第8关第1波", "gameLevel": "320008", "refresh": 59, "monsterId": "150031", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330802", "name": "第8关第2波", "gameLevel": "320008", "refresh": 60, "monsterId": "150041", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330803", "name": "第8关第3波", "gameLevel": "320008", "refresh": 61, "monsterId": "150051", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330804", "name": "第8关第4波", "gameLevel": "320008", "refresh": 62, "monsterId": "150061", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330805", "name": "第8关第5波", "gameLevel": "320008", "refresh": 63, "monsterId": "150071", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330806", "name": "第8关第6波", "gameLevel": "320008", "refresh": 64, "monsterId": "151011", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330807", "name": "第8关第7波", "gameLevel": "320008", "refresh": 65, "monsterId": "151021", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330808", "name": "第8关第8波", "gameLevel": "320008", "refresh": 66, "monsterId": "151031", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330809", "name": "第8关第9波", "gameLevel": "320008", "refresh": 67, "monsterId": "151041", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330810", "name": "第8关第10波", "gameLevel": "320008", "refresh": 68, "monsterId": "151051", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330901", "name": "第9关第1波", "gameLevel": "320009", "refresh": 69, "monsterId": "151061", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330902", "name": "第9关第2波", "gameLevel": "320009", "refresh": 70, "monsterId": "151071", "monsterTotal": 1, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330903", "name": "第9关第3波", "gameLevel": "320009", "refresh": 71, "monsterId": "150011", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330904", "name": "第9关第4波", "gameLevel": "320009", "refresh": 72, "monsterId": "150021", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330905", "name": "第9关第5波", "gameLevel": "320009", "refresh": 73, "monsterId": "150031", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330906", "name": "第9关第6波", "gameLevel": "320009", "refresh": 74, "monsterId": "150041", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330907", "name": "第9关第7波", "gameLevel": "320009", "refresh": 75, "monsterId": "150051", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330908", "name": "第9关第8波", "gameLevel": "320009", "refresh": 76, "monsterId": "150061", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330909", "name": "第9关第9波", "gameLevel": "320009", "refresh": 77, "monsterId": "150071", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "330910", "name": "第9关第10波", "gameLevel": "320009", "refresh": 78, "monsterId": "151011", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "331001", "name": "第10关第1波", "gameLevel": "320010", "refresh": 79, "monsterId": "151021", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "331002", "name": "第10关第2波", "gameLevel": "320010", "refresh": 80, "monsterId": "151031", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "331003", "name": "第10关第3波", "gameLevel": "320010", "refresh": 81, "monsterId": "151041", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "331004", "name": "第10关第4波", "gameLevel": "320010", "refresh": 82, "monsterId": "151051", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "331005", "name": "第10关第5波", "gameLevel": "320010", "refresh": 83, "monsterId": "151061", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "331006", "name": "第10关第6波", "gameLevel": "320010", "refresh": 84, "monsterId": "151071", "monsterTotal": 1, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "331007", "name": "第10关第7波", "gameLevel": "320010", "refresh": 85, "monsterId": "150011", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "331008", "name": "第10关第8波", "gameLevel": "320010", "refresh": 86, "monsterId": "150021", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "331009", "name": "第10关第9波", "gameLevel": "320010", "refresh": 87, "monsterId": "150031", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}, {"jsonId": "331010", "name": "第10关第10波", "gameLevel": "320010", "refresh": 88, "monsterId": "150041", "monsterTotal": 100, "monsterInterval": "0.5", "monsterNum": 3, "type": 2, "refreshInterval": 2}], "animation.json": [{"jsonId": "340001", "sample": 7, "duration": 1, "speed": 1, "wrapMode": 1, "path": "atkeffect", "name": "1", "target": 2, "XAxisOffset": "", "YAxisOffset": "", "notes": "160001通用刀光"}, {"jsonId": "340002", "sample": 7, "duration": 1, "speed": 1, "wrapMode": 1, "path": "atkeffect", "name": "2", "target": 2, "XAxisOffset": "", "YAxisOffset": "", "notes": "160002蓝色刀光"}, {"jsonId": "340003", "sample": 10, "duration": 1, "speed": 1, "wrapMode": 1, "path": "atkeffect", "name": "3", "target": 2, "XAxisOffset": "", "YAxisOffset": "", "notes": "160003紫色刀光"}, {"jsonId": "340004", "sample": 10, "duration": 1, "speed": 1, "wrapMode": 1, "path": "atkeffect", "name": "4", "target": 2, "XAxisOffset": "", "YAxisOffset": "", "notes": "160004红色刀光"}, {"jsonId": "340005", "sample": 10, "duration": 1, "speed": 1, "wrapMode": 1, "path": "atkeffect", "name": "5", "target": 2, "XAxisOffset": "", "YAxisOffset": "", "notes": "160005横向红色刀光"}, {"jsonId": "340006", "sample": 11, "duration": 1, "speed": 1, "wrapMode": 2, "path": "atkeffect", "name": "6", "target": 3, "XAxisOffset": "", "YAxisOffset": "", "notes": "160006火红火球"}, {"jsonId": "340007", "sample": 7, "duration": 1, "speed": 1, "wrapMode": 2, "path": "atkeffect", "name": "7", "target": 3, "XAxisOffset": "-50", "YAxisOffset": "", "notes": "160007蓝色光球"}, {"jsonId": "340008", "sample": 1, "duration": 1, "speed": 1, "wrapMode": 2, "path": "atkeffect", "name": "yumao", "target": 3, "XAxisOffset": "", "YAxisOffset": "", "notes": "羽毛攻击"}, {"jsonId": "340101", "sample": 5, "duration": 1, "speed": 1, "wrapMode": 2, "path": "atkeffect", "name": "25", "target": 2, "XAxisOffset": "", "YAxisOffset": "", "notes": "火球受击"}, {"jsonId": "340102", "sample": 8, "duration": 1, "speed": 1, "wrapMode": 2, "path": "atkeffect", "name": "26", "target": 2, "XAxisOffset": "", "YAxisOffset": "", "notes": "蓝色光球受击"}, {"jsonId": "340201", "sample": 12, "duration": 1, "speed": 1, "wrapMode": 1, "path": "skilleffect", "name": "20", "target": 1, "XAxisOffset": "", "YAxisOffset": "", "notes": "大鹏加速光环"}, {"jsonId": "340202", "sample": 15, "duration": 1, "speed": 1, "wrapMode": 1, "path": "skilleffect", "name": "41", "target": 1, "XAxisOffset": "", "YAxisOffset": "", "notes": "孙悟空迟缓术"}, {"jsonId": "340203", "sample": 19, "duration": 1, "speed": 1, "wrapMode": 1, "path": "skilleffect", "name": "38", "target": 1, "XAxisOffset": "", "YAxisOffset": "", "notes": "牛魔王踩地板"}, {"jsonId": "340301", "sample": 10, "duration": 1, "speed": 1, "wrapMode": 2, "path": "skilleffect", "name": "31", "target": 3, "XAxisOffset": "-100", "YAxisOffset": "", "notes": "点赞小飞剑"}, {"jsonId": "340302", "sample": 5, "duration": 1, "speed": 1, "wrapMode": 1, "path": "skilleffect", "name": "44", "target": 2, "XAxisOffset": "", "YAxisOffset": "", "notes": "点赞小飞剑受击"}, {"jsonId": "340303", "sample": 12, "duration": 1, "speed": 1, "wrapMode": 2, "path": "skilleffect", "name": "30", "target": 3, "XAxisOffset": "", "YAxisOffset": "", "notes": "点赞大飞剑"}, {"jsonId": "340304", "sample": 9, "duration": 1, "speed": 1, "wrapMode": 1, "path": "skilleffect", "name": "35", "target": 2, "XAxisOffset": "", "YAxisOffset": "", "notes": "点赞大飞剑受击"}, {"jsonId": "340305", "sample": 14, "duration": 1, "speed": 1, "wrapMode": 2, "path": "skilleffect", "name": "36", "target": 3, "XAxisOffset": "", "YAxisOffset": "", "notes": "礼物天火技能"}, {"jsonId": "340306", "sample": 13, "duration": 1, "speed": 1, "wrapMode": 1, "path": "skilleffect", "name": "40", "target": 2, "XAxisOffset": "", "YAxisOffset": "", "notes": "礼物天火技能命中"}, {"jsonId": "340307", "sample": 12, "duration": 1, "speed": 1, "wrapMode": 1, "path": "skilleffect", "name": "37", "target": 2, "XAxisOffset": "", "YAxisOffset": "", "notes": "礼物飞沙技能"}, {"jsonId": "340308", "sample": 20, "duration": 1, "speed": 1, "wrapMode": 1, "path": "skilleffect", "name": "39", "target": 2, "XAxisOffset": "", "YAxisOffset": "160", "notes": "礼物三味真火技能"}, {"jsonId": "340309", "sample": 7, "duration": 1, "speed": 1, "wrapMode": 2, "path": "skilleffect", "name": "34", "target": 3, "XAxisOffset": "-150", "YAxisOffset": "", "notes": "礼物技能天外陨石"}, {"jsonId": "340310", "sample": 18, "duration": 1, "speed": 1, "wrapMode": 1, "path": "skilleffect", "name": "33", "target": 2, "XAxisOffset": "", "YAxisOffset": "20", "notes": "礼物技能天外陨石受击"}, {"jsonId": "340311", "sample": 18, "duration": 1, "speed": 1, "wrapMode": 1, "path": "skilleffect", "name": "42", "target": 2, "XAxisOffset": "", "YAxisOffset": "", "notes": "闪电动画"}, {"jsonId": "340312", "sample": 12, "duration": 1, "speed": 1, "wrapMode": 1, "path": "skilleffect", "name": "43", "target": 3, "XAxisOffset": "", "YAxisOffset": "", "notes": "飞沙走石弹道"}, {"jsonId": "340401", "sample": 7, "duration": 1, "speed": 1, "wrapMode": 1, "path": "<PERSON><PERSON><PERSON>", "name": "bianfujing-attack", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "蝙蝠怪攻击"}, {"jsonId": "340402", "sample": 8, "duration": 1, "speed": 1, "wrapMode": 1, "path": "<PERSON><PERSON><PERSON>", "name": "xiaozuanfeng-attack", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "小钻风"}, {"jsonId": "340403", "sample": 7, "duration": 1, "speed": 1, "wrapMode": 1, "path": "<PERSON><PERSON><PERSON>", "name": "lingligui-attack", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "伶俐鬼"}, {"jsonId": "340404", "sample": 7, "duration": 1, "speed": 1, "wrapMode": 1, "path": "<PERSON><PERSON><PERSON>", "name": "zhuyao-attack", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "野猪精"}, {"jsonId": "340405", "sample": 6, "duration": 1, "speed": 1, "wrapMode": 1, "path": "<PERSON><PERSON><PERSON>", "name": "dapeng-attack", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "大鹏"}, {"jsonId": "340406", "sample": 8, "duration": 1, "speed": 1, "wrapMode": 1, "path": "<PERSON><PERSON><PERSON>", "name": "jurenyuan-attack", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "巨力猿"}, {"jsonId": "340407", "sample": 8, "duration": 1, "speed": 1, "wrapMode": 1, "path": "<PERSON><PERSON><PERSON>", "name": "xiniujing-attack", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "犀牛精"}, {"jsonId": "340408", "sample": 7, "duration": 1, "speed": 1, "wrapMode": 1, "path": "<PERSON><PERSON><PERSON>", "name": "bian<PERSON>wang-attack", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "蝙蝠王"}, {"jsonId": "340409", "sample": 6, "duration": 1, "speed": 1, "wrapMode": 1, "path": "<PERSON><PERSON><PERSON>", "name": "zongzuanfeng-attack", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "总钻风"}, {"jsonId": "340410", "sample": 7, "duration": 1, "speed": 1, "wrapMode": 1, "path": "<PERSON><PERSON><PERSON>", "name": "honghaier-attack", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "红孩儿"}, {"jsonId": "340411", "sample": 8, "duration": 1, "speed": 1, "wrapMode": 1, "path": "<PERSON><PERSON><PERSON>", "name": "heixiongjing-attack", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "黑熊精"}, {"jsonId": "340412", "sample": 7, "duration": 1, "speed": 1, "wrapMode": 1, "path": "<PERSON><PERSON><PERSON>", "name": "jinchidapeng-attack", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "金翅大鹏"}, {"jsonId": "340413", "sample": 9, "duration": 1, "speed": 1, "wrapMode": 1, "path": "<PERSON><PERSON><PERSON>", "name": "sunwukong-attack", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "孙悟空"}, {"jsonId": "340414", "sample": 7, "duration": 1, "speed": 1, "wrapMode": 1, "path": "<PERSON><PERSON><PERSON>", "name": "niumowang-attack", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "牛魔王"}, {"jsonId": "340501", "sample": 5, "duration": 1, "speed": 1, "wrapMode": 2, "path": "<PERSON><PERSON><PERSON>", "name": "bianfujing-move", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "蝙蝠怪移动"}, {"jsonId": "340502", "sample": 8, "duration": 1, "speed": 1, "wrapMode": 2, "path": "<PERSON><PERSON><PERSON>", "name": "xia<PERSON><PERSON><PERSON>-move", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "小钻风"}, {"jsonId": "340503", "sample": 7, "duration": 1, "speed": 1, "wrapMode": 2, "path": "<PERSON><PERSON><PERSON>", "name": "lingligui-move", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "伶俐鬼"}, {"jsonId": "340504", "sample": 8, "duration": 1, "speed": 1, "wrapMode": 2, "path": "<PERSON><PERSON><PERSON>", "name": "zhuyao-move", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "野猪精"}, {"jsonId": "340505", "sample": 7, "duration": 1, "speed": 1, "wrapMode": 2, "path": "<PERSON><PERSON><PERSON>", "name": "dapeng-move", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "大鹏"}, {"jsonId": "340506", "sample": 8, "duration": 1, "speed": 1, "wrapMode": 2, "path": "<PERSON><PERSON><PERSON>", "name": "juren<PERSON>-move", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "巨力猿"}, {"jsonId": "340507", "sample": 8, "duration": 1, "speed": 1, "wrapMode": 2, "path": "<PERSON><PERSON><PERSON>", "name": "xiniujing-move", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "犀牛精"}, {"jsonId": "340508", "sample": 5, "duration": 1, "speed": 1, "wrapMode": 2, "path": "<PERSON><PERSON><PERSON>", "name": "bian<PERSON>wang-move", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "蝙蝠王"}, {"jsonId": "340509", "sample": 6, "duration": 1, "speed": 1, "wrapMode": 2, "path": "<PERSON><PERSON><PERSON>", "name": "zongzuanfeng-move", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "总钻风"}, {"jsonId": "340510", "sample": 8, "duration": 1, "speed": 1, "wrapMode": 2, "path": "<PERSON><PERSON><PERSON>", "name": "honghaier-move", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "红孩儿"}, {"jsonId": "340511", "sample": 8, "duration": 1, "speed": 1, "wrapMode": 2, "path": "<PERSON><PERSON><PERSON>", "name": "heixiongjing-move", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "黑熊精"}, {"jsonId": "340512", "sample": 7, "duration": 1, "speed": 1, "wrapMode": 2, "path": "<PERSON><PERSON><PERSON>", "name": "jinchidapeng-move", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "金翅大鹏"}, {"jsonId": "340513", "sample": 8, "duration": 1, "speed": 1, "wrapMode": 2, "path": "<PERSON><PERSON><PERSON>", "name": "sunwukong-move", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "孙悟空"}, {"jsonId": "340514", "sample": 8, "duration": 1, "speed": 1, "wrapMode": 2, "path": "<PERSON><PERSON><PERSON>", "name": "niumowang-move", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "牛魔王"}, {"jsonId": "340613", "sample": 20, "duration": 1, "speed": 1, "wrapMode": 1, "path": "<PERSON><PERSON><PERSON>", "name": "sunwukong-skill", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "孙悟空技能动画"}, {"jsonId": "340614", "sample": 16, "duration": 1, "speed": 1, "wrapMode": 1, "path": "<PERSON><PERSON><PERSON>", "name": "niumowang-skill", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "牛魔王技能动画"}, {"jsonId": "340701", "sample": 6, "duration": 1, "speed": 1, "wrapMode": 1, "path": "<PERSON><PERSON><PERSON>", "name": "bin01-attack", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "天兵01攻击"}, {"jsonId": "340702", "sample": 6, "duration": 1, "speed": 1, "wrapMode": 1, "path": "<PERSON><PERSON><PERSON>", "name": "bin02-attack", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "天兵02攻击"}, {"jsonId": "340703", "sample": 6, "duration": 1, "speed": 1, "wrapMode": 1, "path": "<PERSON><PERSON><PERSON>", "name": "bin03-attack", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "天兵03攻击"}, {"jsonId": "340704", "sample": 6, "duration": 1, "speed": 1, "wrapMode": 1, "path": "<PERSON><PERSON><PERSON>", "name": "bin04-attack", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "天兵04攻击"}, {"jsonId": "340705", "sample": 7, "duration": 1, "speed": 1, "wrapMode": 1, "path": "<PERSON><PERSON><PERSON>", "name": "bin05-attack", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "天兵05攻击"}, {"jsonId": "340706", "sample": 10, "duration": 1, "speed": 1, "wrapMode": 1, "path": "<PERSON><PERSON><PERSON>", "name": "bin06-attack", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "天兵06攻击"}, {"jsonId": "340707", "sample": 10, "duration": 1, "speed": 1, "wrapMode": 1, "path": "<PERSON><PERSON><PERSON>", "name": "bin07-attack", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "天兵07攻击"}, {"jsonId": "340801", "sample": 7, "duration": 1, "speed": 1, "wrapMode": 2, "path": "<PERSON><PERSON><PERSON>", "name": "bin01-move", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "天兵01移动"}, {"jsonId": "340802", "sample": 7, "duration": 1, "speed": 1, "wrapMode": 2, "path": "<PERSON><PERSON><PERSON>", "name": "bin02-move", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "天兵02移动"}, {"jsonId": "340803", "sample": 7, "duration": 1, "speed": 1, "wrapMode": 2, "path": "<PERSON><PERSON><PERSON>", "name": "bin03-move", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "天兵03移动"}, {"jsonId": "340804", "sample": 7, "duration": 1, "speed": 1, "wrapMode": 2, "path": "<PERSON><PERSON><PERSON>", "name": "bin04-move", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "天兵04移动"}, {"jsonId": "340805", "sample": 8, "duration": 1, "speed": 1, "wrapMode": 2, "path": "<PERSON><PERSON><PERSON>", "name": "bin05-move", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "天兵05移动"}, {"jsonId": "340806", "sample": 10, "duration": 1, "speed": 1, "wrapMode": 2, "path": "<PERSON><PERSON><PERSON>", "name": "bin06-move", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "天兵06移动"}, {"jsonId": "340807", "sample": 6, "duration": 1, "speed": 1, "wrapMode": 2, "path": "<PERSON><PERSON><PERSON>", "name": "bin07-move", "target": 0, "XAxisOffset": "", "YAxisOffset": "", "notes": "天兵07移动"}]}]], 0, 0, [], [], []]