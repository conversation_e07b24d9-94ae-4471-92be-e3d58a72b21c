{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "f1ebedb6-dbf8-4030-a3e3-ef6d240c67e5", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "f1ebedb6-dbf8-4030-a3e3-ef6d240c67e5@6c48a", "displayName": "xiaolonglv-attack_16", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "f1ebedb6-dbf8-4030-a3e3-ef6d240c67e5", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "f1ebedb6-dbf8-4030-a3e3-ef6d240c67e5@f9941", "displayName": "xiaolonglv-attack_16", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": -25.5, "offsetY": -20.5, "trimX": 13, "trimY": 49, "width": 123, "height": 143, "rawWidth": 200, "rawHeight": 200, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-61.5, -71.5, 0, 61.5, -71.5, 0, -61.5, 71.5, 0, 61.5, 71.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [13, 151, 136, 151, 13, 8, 136, 8], "nuv": [0.065, 0.04, 0.68, 0.04, 0.065, 0.755, 0.68, 0.755], "minPos": [-61.5, -71.5, 0], "maxPos": [61.5, 71.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "f1ebedb6-dbf8-4030-a3e3-ef6d240c67e5@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "f1ebedb6-dbf8-4030-a3e3-ef6d240c67e5@6c48a"}}