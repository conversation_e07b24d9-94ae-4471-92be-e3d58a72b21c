// @ts-nocheck
import { sys } from 'cc'

var sa = {
	is_first_launch: !1,
	launched: !1,
	_queue: [],
	mpshow_time: null,
	sa_referrer: '直接打开',
	query_share_depth: 0,
	share_distinct_id: '',
	share_method: '',
	current_scene: '',
	inited: !1,
	para: {
		server_url: '',
		send_timeout: 1e3,
		show_log: !1,
		allow_amend_share_path: !0,
		max_string_length: 500,
		datasend_timeout: 3e3,
		source_channel: [],
		batch_send: { send_timeout: 6e3, max_length: 6 },
		preset_properties: {}
	},
	platform: '',
	lib: { version: '0.6.2', name: 'MiniGame', method: 'code' },
	properties: { $lib: 'MiniGame', $lib_version: '0.6.2' },
	source_channel_standard: 'utm_source utm_medium utm_campaign utm_content utm_term',
	latest_source_channel: [
		'$latest_utm_source',
		'$latest_utm_medium',
		'$latest_utm_campaign',
		'$latest_utm_content',
		'$latest_utm_term',
		'$latest_sa_utm'
	],
	latest_share_info: [
		'$latest_share_distinct_id',
		'$latest_share_url_path',
		'$latest_share_depth',
		'$latest_share_method'
	],
	currentProps: {}
}
const _toString = Object.prototype.toString,
	_hasOwnProperty = Object.prototype.hasOwnProperty,
	indexOf = Array.prototype.indexOf,
	slice = Array.prototype.slice,
	_isArray = Array.isArray,
	forEach = Array.prototype.forEach,
	bind = Function.prototype.bind
function isUndefined(t) {
	return void 0 === t
}
function isString(t) {
	return '[object String]' == _toString.call(t)
}
function isDate(t) {
	return '[object Date]' == _toString.call(t)
}
function isBoolean(t) {
	return '[object Boolean]' == _toString.call(t)
}
function isNumber(t) {
	return '[object Number]' == _toString.call(t) && /[\d\.]+/.test(String(t))
}
var isArray =
	_isArray ||
	function (t) {
		return '[object Array]' === Object.prototype.toString.call(t)
	}
function isJSONString(t) {
	try {
		JSON.parse(t)
	} catch (t) {
		return !1
	}
	return !0
}
function isObject(t) {
	return null != t && '[object Object]' === _toString.call(t)
}
function isPlainObject(t) {
	return '[object Object]' === _toString.call(t)
}
function isFuction(t) {
	try {
		return /^\s*\bfunction\b/.test(t)
	} catch (t) {
		return !1
	}
}
function isArguments(t) {
	return !(!t || !_hasOwnProperty.call(t, 'callee'))
}
function toString(t) {
	return null == t
		? ''
		: isArray(t) || (isPlainObject(t) && t.toString === _toString)
		? JSON.stringify(t, null, 2)
		: String(t)
}
function each(t, e, a) {
	if (null == t) return !1
	if (forEach && t.forEach === forEach) t.forEach(e, a)
	else if (t.length === +t.length) {
		for (var r = 0, n = t.length; r < n; r++)
			if (r in t && e.call(a, t[r], r, t) === {}) return !1
	} else
		for (var s in t) if (_hasOwnProperty.call(t, s) && e.call(a, t[s], s, t) === {}) return !1
}
function toArray(t, e) {
	if (!t) return []
	var a = []
	return (
		t.toArray && (a = t.toArray()),
		isArray(t) && (a = slice.call(t)),
		isArguments(t) && (a = slice.call(t)),
		(a = values(t)),
		(a = e && isNumber(e) ? a.slice(e) : a)
	)
}
function values(t) {
	var e = []
	return (
		null == t ||
			each(t, function (t) {
				e[e.length] = t
			}),
		e
	)
}
function include(t, e) {
	var a = !1
	return null == t
		? a
		: indexOf && t.indexOf === indexOf
		? -1 != t.indexOf(e)
		: (each(t, function (t) {
				if ((a = a || t === e)) return {}
		  }),
		  a)
}
function unique(t) {
	for (var e, a = [], r = {}, n = 0; n < t.length; n++) r[(e = t[n])] || ((r[e] = !0), a.push(e))
	return a
}
function formatDate(t) {
	function e(t) {
		return t < 10 ? '0' + t : t
	}
	return (
		t.getFullYear() +
		'-' +
		e(t.getMonth() + 1) +
		'-' +
		e(t.getDate()) +
		' ' +
		e(t.getHours()) +
		':' +
		e(t.getMinutes()) +
		':' +
		e(t.getSeconds()) +
		'.' +
		e(t.getMilliseconds())
	)
}
function searchObjDate(a) {
	isObject(a) &&
		each(a, function (t, e) {
			isObject(t) ? searchObjDate(a[e]) : isDate(t) && (a[e] = formatDate(t))
		})
}
function trim(t) {
	return t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g, '')
}
function isFunction(t) {
	if (!t) return !1
	t = Object.prototype.toString.call(t)
	return (
		'[object Function]' == t ||
		'[object AsyncFunction]' == t ||
		'[object GeneratorFunction]' == t
	)
}
function extend(a) {
	return (
		each(slice.call(arguments, 1), function (t) {
			for (var e in t) void 0 !== t[e] && (a[e] = t[e])
		}),
		a
	)
}
function extend2Lev(a) {
	return (
		each(slice.call(arguments, 1), function (t) {
			for (var e in t)
				void 0 !== t[e] &&
					null !== t[e] &&
					(isObject(t[e]) && isObject(a[e]) ? extend(a[e], t[e]) : (a[e] = t[e]))
		}),
		a
	)
}
function isEmptyObject(t) {
	if (isObject(t)) {
		for (var e in t) if (_hasOwnProperty.call(t, e)) return !1
		return !0
	}
	return !1
}
function formatString(t) {
	return t.length > sa.para.max_string_length
		? (sa.log('字符串长度超过限制，已经做截取--' + t), t.slice(0, sa.para.max_string_length))
		: t
}
function searchObjString(a) {
	isObject(a) &&
		each(a, function (t, e) {
			isObject(t) ? searchObjString(a[e]) : isString(t) && (a[e] = formatString(t))
		})
}
function decodeURIComponent(e) {
	var a = ''
	try {
		a = decodeURIComponent(e)
	} catch (t) {
		a = e
	}
	return a
}
function encodeDates(a) {
	return (
		each(a, function (t, e) {
			isDate(t) ? (a[e] = formatDate(t)) : isObject(t) && (a[e] = encodeDates(t))
		}),
		a
	)
}
function utf8Encode(t) {
	for (
		var e,
			a = '',
			r = (e = 0),
			n = (t = (t + '').replace(/\r\n/g, '\n').replace(/\r/g, '\n')).length,
			s = 0;
		s < n;
		s++
	) {
		var i = t.charCodeAt(s),
			o = null
		i < 128
			? e++
			: (o =
					127 < i && i < 2048
						? String.fromCharCode((i >> 6) | 192, (63 & i) | 128)
						: String.fromCharCode(
								(i >> 12) | 224,
								((i >> 6) & 63) | 128,
								(63 & i) | 128
						  )),
			null !== o && (r < e && (a += t.substring(r, e)), (a += o), (r = e = s + 1))
	}
	return r < e && (a += t.substring(r, t.length)), a
}
function base64Encode(t) {
	var e,
		a,
		r,
		n,
		s = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=',
		i = 0,
		o = 0,
		c = '',
		l = []
	if (!t) return t
	for (
		t = utf8Encode(t);
		(e =
			((n = (t.charCodeAt(i++) << 16) | (t.charCodeAt(i++) << 8) | t.charCodeAt(i++)) >> 12) &
			63),
			(a = (n >> 6) & 63),
			(r = 63 & n),
			(l[o++] = s.charAt((n >> 18) & 63) + s.charAt(e) + s.charAt(a) + s.charAt(r)),
			i < t.length;

	);
	switch (((c = l.join('')), t.length % 3)) {
		case 1:
			c = c.slice(0, -2) + '=='
			break
		case 2:
			c = c.slice(0, -1) + '='
	}
	return c
}
function formatSystem(t) {
	var e = t.toLowerCase()
	return 'ios' === e ? 'iOS' : 'android' === e ? 'Android' : t
}
var getRandomBasic = (function () {
	var e = new Date().getTime()
	return function (t) {
		return Math.ceil(((e = (9301 * e + 49297) % 233280) / 233280) * t)
	}
})()
function getRandom() {
	if ('function' == typeof Uint32Array) {
		var t = ''
		if (
			('undefined' != typeof crypto
				? (t = crypto)
				: 'undefined' != typeof msCrypto && (t = msCrypto),
			isObject(t) && t.getRandomValues)
		) {
			var e = new Uint32Array(1)
			return t.getRandomValues(e)[0] / Math.pow(2, 32)
		}
	}
	return getRandomBasic(1e19) / 1e19
}
function getUUID() {
	return (
		Date.now() +
		'-' +
		Math.floor(1e7 * getRandom()) +
		'-' +
		getRandom().toString(16).replace('.', '') +
		'-' +
		String(31242 * getRandom())
			.replace('.', '')
			.slice(0, 8)
	)
}
const _ = {
	getUUID: getUUID,
	formatSystem: formatSystem,
	indexOf: indexOf,
	slice: slice,
	forEach: forEach,
	bind: bind,
	_hasOwnProperty: _hasOwnProperty,
	_toString: _toString,
	isUndefined: isUndefined,
	isString: isString,
	isDate: isDate,
	isBoolean: isBoolean,
	isNumber: isNumber,
	isJSONString: isJSONString,
	isObject: isObject,
	isPlainObject: isPlainObject,
	isArray: isArray,
	isFuction: isFuction,
	isArguments: isArguments,
	toString: toString,
	unique: unique,
	include: include,
	values: values,
	toArray: toArray,
	each: each,
	formatDate: formatDate,
	searchObjDate: searchObjDate,
	utf8Encode: utf8Encode,
	decodeURIComponent: decodeURIComponent,
	encodeDates: encodeDates,
	base64Encode: base64Encode,
	trim: trim,
	isFunction: isFunction,
	extend: extend,
	extend2Lev: extend2Lev,
	isEmptyObject: isEmptyObject,
	searchObjString: searchObjString,
	formatString: formatString
}
function stripProperties(r) {
	return (
		isObject(r) &&
			each(r, function (e, t) {
				var a
				isArray(e) &&
					((a = []),
					each(e, function (t) {
						isString(t)
							? a.push(t)
							: sa.log('您的数据-', e, '的数组里的值必须是字符串,已经将其删除')
					}),
					0 !== a.length ? (r[t] = a) : (delete r[t], sa.log('已经删除空的数组'))),
					isString(e) ||
						isNumber(e) ||
						isDate(e) ||
						isBoolean(e) ||
						isArray(e) ||
						(sa.log('您的数据-', e, '-格式不满足要求，我们已经将其删除'), delete r[t])
			}),
		r
	)
}
function batchSend() {
	var t, e
	!sa.batch_state.sended ||
		(0 < (e = (t = sa.batch_state.mem).length) &&
			((sa.batch_state.sended = !1),
			batchRequest({ data: t, len: e, success: batchRemove, fail: sendFail })))
}
function batchRequest(e) {
	if (isArray(e.data) && 0 < e.data.length) {
		var a = Date.now(),
			r = sa.para.datasend_timeout
		e.data.forEach(function (t) {
			t._flush_time = a
		}),
			(e.data = JSON.stringify(e.data))
		let t = {
			url: sa.para.server_url,
			method: 'POST',
			dataType: 'text',
			data: 'data_list=' + encodeURIComponent(base64Encode(e.data)),
			timeout: r,
			success: function () {
				e.success(e.len)
			},
			fail: function () {
				e.fail()
			}
		}
		sa.lib &&
			'KuaishouMini' === sa.lib.name &&
			(t.header = { 'Content-Type': 'application/x-www-form-urlencoded' }),
			sa.system_api.request(t)
	} else e.success(e.len)
}
function sendFail() {
	;(sa.batch_state.sended = !0), sa.batch_state.failTime++
}
function batchRemove(t) {
	sa.batch_state.clear(t),
		(sa.batch_state.sended = !0),
		(sa.batch_state.changed = !0),
		batchWrite(),
		(sa.batch_state.failTime = 0)
}
function batchWrite() {
	sa.batch_state.changed &&
		(sa.batch_state.is_first_batch_write &&
			((sa.batch_state.is_first_batch_write = !1),
			setTimeout(function () {
				batchSend()
			}, 1e3)),
		sa.batch_state.syncStorage &&
			(sa.system_api.setStorageSync('sensors_prepare_data', sa.batch_state.mem),
			(sa.batch_state.changed = !1)))
}
function batchInterval() {
	!(function t() {
		setTimeout(function () {
			batchWrite(), t()
		}, 1e3)
	})(),
		(function t() {
			setTimeout(function () {
				batchSend(), t()
			}, sa.para.batch_send.send_timeout * Math.pow(2, sa.batch_state.failTime))
		})()
}
function singleSend(t) {
	t._flush_time = Date.now()
	var e = '',
		t = JSON.stringify(t),
		e =
			-1 !== sa.para.server_url.indexOf('?')
				? sa.para.server_url + '&data=' + encodeURIComponent(base64Encode(t))
				: sa.para.server_url + '?data=' + encodeURIComponent(base64Encode(t)),
		t = sa.para.datasend_timeout
	sa.system_api.request({ url: e, dataType: 'text', method: 'GET', timeout: t })
}
function reportEvent(t) {
	var e = 'sensors_',
		a = ''
	;(t._flush_time = Date.now()),
		(a = t.event ? e + t.event : e + t.type),
		(t.dataSource = 'sensors'),
		sa.log('report_event, name: ', a, '-- key: ', t),
		__mp_private_api__.reportEvent(a, t)
}
;(sa.batch_state = {
	mem: [],
	changed: !1,
	sended: !0,
	is_first_batch_write: !0,
	sync_storage: !1,
	failTime: 0,
	getLength: function () {
		return this.mem.length
	},
	add: function (t) {
		this.mem.push(t)
	},
	clear: function (t) {
		this.mem.splice(0, t)
	}
}),
	(sa.prepareData = function (t, e) {
		var a = extend(
			(a = {
				distinct_id: sa.store.getDistinctId(),
				lib: {
					$lib: sa.lib.name,
					$lib_method: sa.lib.method,
					$lib_version: String(sa.lib.version)
				},
				properties: {}
			}),
			t
		)
		if (
			(isObject(t.properties) &&
				!isEmptyObject(t.properties) &&
				(a.properties = extend(a.properties, t.properties)),
			(t.type && 'profile' === t.type.slice(0, 7)) ||
				(sa.para.batch_send &&
					(a._track_id = Number(
						String(getRandom()).slice(2, 5) +
							String(getRandom()).slice(2, 4) +
							String(Date.now()).slice(-4)
					)),
				(a.properties = extend({}, sa.properties, sa.currentProps, a.properties)),
				'object' == typeof sa.store._state &&
				'number' == typeof sa.store._state.first_visit_day_time &&
				sa.store._state.first_visit_day_time > new Date().getTime()
					? (a.properties.$is_first_day = !0)
					: (a.properties.$is_first_day = !1)),
			a.properties.$time && isDate(a.properties.$time)
				? ((a.time = +a.properties.$time), delete a.properties.$time)
				: (a.time = +new Date()),
			stripProperties(a.properties),
			searchObjDate(a),
			searchObjString(a),
			!sa.para.server_url)
		)
			return !1
		sa.log(a), sa.send(a)
	}),
	(sa.send = function (t) {
		if (
			((t._nocache = (String(getRandom()) + String(getRandom()) + String(getRandom())).slice(
				2,
				15
			)),
			'sensorsdata2015_binance' === sa.storageName && 'native' === sa.para.data_report_type)
		)
			return reportEvent(t), !1
		if (sa.para.batch_send) {
			if (300 <= sa.batch_state.getLength()) return sa.log('数据量存储过大，有异常'), !1
			sa.batch_state.add(t),
				(sa.batch_state.changed = !0),
				sa.batch_state.getLength() >= sa.para.batch_send.max_length && batchSend()
		} else singleSend(t)
	}),
	(sa.log = function () {
		if (sa.para.show_log && 'object' == typeof console && console.log)
			try {
				var t = Array.prototype.slice.call(arguments)
				return console.log.apply(console, t)
			} catch (t) {
				console.log(arguments[0])
			}
	}),
	(sa.track = function (t, e, a) {
		sa.prepareData({ type: 'track', event: t, properties: e }, a)
	}),
	(sa.setProfile = function (t) {
		sa.prepareData({ type: 'profile_set', properties: t })
	}),
	(sa.setOnceProfile = function (t, e) {
		sa.prepareData({ type: 'profile_set_once', properties: t }, e)
	}),
	(sa.login = function (t) {
		var e = sa.store.getFirstId(),
			a = sa.store.getDistinctId()
		t !== a && (e || sa.store.set('first_id', a), sa.trackSignup(t, '$SignUp'))
	}),
	(sa.logout = function (t) {
		var e = sa.store.getFirstId()
		e
			? (sa.store.set('first_id', ''),
			  !0 === t ? sa.store.set('distinct_id', getUUID()) : sa.store.set('distinct_id', e))
			: sa.log('没有first_id，logout失败')
	}),
	(sa.identify = function (t) {
		if ('number' == typeof t) t = String(t)
		else if ('string' != typeof t) return !1
		sa.store.getFirstId() ? sa.store.set('first_id', t) : sa.store.set('distinct_id', t)
	}),
	(sa.trackSignup = function (t, e, a) {
		sa.prepareData({
			original_id: sa.store.getFirstId() || sa.store.getDistinctId(),
			distinct_id: t,
			type: 'track_signup',
			event: e,
			properties: a
		}),
			sa.store.set('distinct_id', t)
	}),
	(sa.registerApp = function (t) {
		isObject(t) && !isEmptyObject(t) && (sa.currentProps = extend(sa.currentProps, t))
	}),
	(sa.clearAppRegister = function (a) {
		isArray(a) &&
			each(sa.currentProps, function (t, e) {
				include(a, e) && delete sa.currentProps[e]
			})
	}),
	(sa.use = function (t) {
		const e = this._installedPlugins || (this._installedPlugins = [])
		if (-1 < e.indexOf(t)) return this
		const a = toArray(arguments, 1)
		return (
			a.unshift(this),
			'function' == typeof t.init
				? t.init.apply(t, a)
				: 'function' == typeof t && t.apply(null, a),
			e.push(t),
			this
		)
	}),
	(sa.init = function () {
		if (!0 === sa.hasExeInit) return !1
		;(this.hasExeInit = !0),
			sa.store.init(),
			sa.system.init(),
			sa.para.batch_send &&
				(sa.system_api.getStorage('sensors_prepare_data', function (t) {
					var e = []
					t &&
						t.data &&
						isArray(t.data) &&
						((e = t.data), (sa.batch_state.mem = e.concat(sa.batch_state.mem))),
						(sa.batch_state.syncStorage = !0)
				}),
				batchInterval())
	}),
	(sa.setPara = function (t) {
		sa.para = extend2Lev(sa.para, t)
		var e = []
		if (isArray(sa.para.source_channel))
			for (var a = sa.para.source_channel.length, r = 0; r < a; r++)
				-1 ===
					' utm_source utm_medium utm_campaign utm_content utm_term sa_utm '.indexOf(
						' ' + sa.para.source_channel[r] + ' '
					) && e.push(sa.para.source_channel[r])
		;(sa.para.source_channel = e),
			'number' != typeof sa.para.send_timeout && (sa.para.send_timeout = 1e3)
		var n = { send_timeout: 6e3, max_length: 6 }
		;(t && t.datasend_timeout) || (sa.para.batch_send && (sa.para.datasend_timeout = 1e4)),
			!0 === sa.para.batch_send
				? (sa.para.batch_send = extend({}, n))
				: isObject(sa.para.batch_send) &&
				  (sa.para.batch_send = extend({}, n, sa.para.batch_send)),
			sa.para.server_url
				? (sa.para.preset_properties = isObject(sa.para.preset_properties)
						? sa.para.preset_properties
						: {})
				: sa.log('请使用 setPara() 方法设置 server_url 数据接收地址')
	}),
	(sa.checkInit = function () {
		!0 === sa.system.inited &&
			!0 === sa.store.inited &&
			((sa.inited = !0),
			0 < sa._queue.length &&
				(each(sa._queue, function (t) {
					sa[t[0]].apply(sa, slice.call(t[1]))
				}),
				(sa._queue = [])))
	}),
	each(
		[
			'setProfile',
			'setOnceProfile',
			'track',
			'login',
			'logout',
			'identify',
			'registerApp',
			'clearAppRegister'
		],
		function (t) {
			var e = sa[t]
			sa[t] = function () {
				sa.inited ? e.apply(sa, arguments) : sa._queue.push([t, arguments])
			}
		}
	)
var store = {
	inited: !0,
	storageInfo: null,
	_state: {},
	toState: function (t) {
		isObject(t) && t.distinct_id ? (this._state = t) : this.set('distinct_id', getUUID())
	},
	getFirstId: function () {
		return this._state.first_id
	},
	getDistinctId: function () {
		return this._state.distinct_id
	},
	getUnionId: function () {
		var t = {},
			e = this._state.first_id,
			a = this._state.distinct_id
		return e && a ? ((t.login_id = a), (t.anonymous_id = e)) : (t.anonymous_id = a), t
	},
	getProps: function () {
		return this._state.props || {}
	},
	setProps: function (t, e) {
		var a = this._state.props || {}
		e ? this.set('props', t) : (extend(a, t), this.set('props', a))
	},
	set: function (t, e) {
		var a,
			r = {}
		for (a in ('string' == typeof t ? (r[t] = e) : 'object' == typeof t && (r = t),
		(this._state = this._state || {}),
		r))
			this._state[a] = r[a]
		this.save()
	},
	save: function () {
		sa.system_api.setStorageSync(sa.storageName, this._state)
	},
	init: function () {
		var t,
			e = sa.system_api.getStorageSync(sa.storageName)
		e
			? this.toState(e)
			: ((sa.is_first_launch = !0),
			  (e = (t = new Date()).getTime()),
			  t.setHours(23),
			  t.setMinutes(59),
			  t.setSeconds(60),
			  sa.setOnceProfile({ $first_visit_time: new Date() }),
			  this.set({
					distinct_id: getUUID(),
					first_visit_time: e,
					first_visit_day_time: t.getTime()
			  }))
	}
}
function request(t) {
	var e
	t.timeout && ((e = t.timeout), delete t.timeout)
	var a = sa.platform_obj.request(t)
	setTimeout(function () {
		try {
			isObject(a) && isFunction(a.abort) && a.abort()
		} catch (t) {
			sa.log(t)
		}
	}, e)
}
function getStorage(e, a) {
	try {
		sa.platform_obj.getStorage({ key: e, success: r, fail: r })
	} catch (t) {
		try {
			sa.platform_obj.getStorage({ key: e, success: r, fail: r })
		} catch (t) {
			sa.log('获取 storage 失败！', t)
		}
	}
	function r(t) {
		if (t && t.data && isJSONString(t.data))
			try {
				var e = JSON.parse(t.data)
				t.data = e
			} catch (t) {
				sa.log('parse res.data 失败！', t)
			}
		a(t)
	}
}
function setStorage(e, t) {
	var a
	try {
		a = JSON.stringify(t)
	} catch (t) {
		sa.log('序列化缓存对象失败！', t)
	}
	try {
		sa.platform_obj.setStorage({ key: e, data: a })
	} catch (t) {
		try {
			sa.platform_obj.setStorage({ key: e, data: a })
		} catch (t) {
			sa.log('设置 storage 失败: ', t)
		}
	}
}
function getStorageSync(e) {
	var a = ''
	try {
		a = sa.platform_obj.getStorageSync(e)
	} catch (t) {
		try {
			a = sa.platform_obj.getStorageSync(e)
		} catch (t) {
			sa.log('获取 storage 失败！')
		}
	}
	return (a = isJSONString(a) ? JSON.parse(a) : a)
}
function setStorageSync(t, e) {
	var a
	try {
		a = JSON.stringify(e)
	} catch (t) {
		sa.log('序列化缓存对象失败！', t)
	}
	e = function () {
		sa.platform_obj.setStorageSync(t, a)
	}
	try {
		e()
	} catch (t) {
		sa.log('set Storage fail --', t)
		try {
			e()
		} catch (t) {
			sa.log('set Storage fail again --', t)
		}
	}
}
sa.store = store
var compose = {
	request: request,
	getStorage: getStorage,
	setStorage: setStorage,
	getStorageSync: getStorageSync,
	setStorageSync: setStorageSync
}
function getNetwork() {
	return new Promise(function (t) {
		sa.platform_obj.getNetworkType({
			success(t) {
				sa.properties.$network_type = t.networkType
			},
			fail(t) {
				sa.log('获取网络状态信息失败： ', t)
			},
			complete() {
				t()
			}
		})
	})
}
function getSystemInfo() {
	return new Promise((t, e) => {
		sa.platform_obj.getSystemInfo({
			success(t) {
				var e = sa.properties
				isObject(t) &&
					((e.$manufacturer = t.brand),
					(e.$model = t.model),
					(e.$screen_width = Number(t.screenWidth)),
					(e.$screen_height = Number(t.screenHeight)),
					(e.$os = formatSystem(t.platform || 'android')),
					(e.$os_version =
						-1 < t.system.indexOf(' ') ? t.system.split(' ')[1] : t.system))
			},
			fail(t) {
				sa.log('获取系统信息失败: ', t)
			},
			complete() {
				t()
			}
		})
	})
}
function getAppId() {
	var t
	if (
		isObject(
			(t = sa.platform_obj.getAccountInfoSync ? sa.platform_obj.getAccountInfoSync() : t)
		) &&
		isObject(t.miniProgram)
	)
		return t.miniProgram.appId
}
var system = {
		inited: !1,
		init: function () {
			var t = new Date().getTimezoneOffset()
			isNumber(t) && (sa.properties.$timezone_offset = t)
			var e = getAppId() || sa.para.app_id || sa.para.appid
			e && (sa.properties.$app_id = e)
			;(t = getNetwork()), (e = getSystemInfo())
			Promise.all([t, e]).then(function () {
				;(sa.system.inited = !0), sa.checkInit()
			})
		}
	},
	system$1 = {
		inited: !1,
		init: function () {
			var t = new Date().getTimezoneOffset()
			isNumber(t) && (sa.properties.$timezone_offset = t)
			t = sa.para.app_id || sa.para.appid
			t && (sa.properties.$app_id = t)
			try {
				;(sa.properties.$screen_width = Number(window.screen.width)),
					(sa.properties.$screen_height = Number(window.screen.height))
			} catch (t) {
				sa.log('获取screen异常')
			}
			;(sa.system.inited = !0), sa.checkInit()
		}
	},
	localStorage = {
		get: function (t) {
			return window.localStorage.getItem(t)
		},
		parse: function (t) {
			var e
			try {
				e = JSON.parse(this.get(t)) || null
			} catch (t) {
				sa.log(t)
			}
			return e
		},
		set: function (t, e) {
			window.localStorage.setItem(t, e)
		},
		remove: function (t) {
			window.localStorage.removeItem(t)
		},
		isSupport: function () {
			var e = !0
			try {
				var t = '__sensorsdatasupport__',
					a = 'testIsSupportStorage'
				this.set(t, a), this.get(t) !== a && (e = !1), this.remove(t)
			} catch (t) {
				e = !1
			}
			return e
		}
	}
function getStorageSync$1(t) {
	return localStorage.parse(t)
}
function setStorageSync$1(t, e) {
	var a
	try {
		a = JSON.stringify(e)
	} catch (t) {
		sa.log('序列化缓存对象失败！')
	}
	try {
		localStorage.set(t, a)
	} catch (t) {
		sa.log('set Storage fail!')
	}
}
var compose$1 = {
		request: function (t) {
			if ((((t = t || {}).timeout = t.timeout || 3e4), !t.method || !t.url)) return !1
			try {
				new Image().src = t.url
			} catch (t) {
				sa.log('创建AJAX请求失败' + t)
			}
		},
		getStorageSync: getStorageSync$1,
		setStorageSync: setStorageSync$1
	},
	android_api = {
		init: function (t) {
			jsb.reflection.callStaticMethod(
				'com/cocos/game/CCSensorsDataAPI',
				'initSA',
				'(Ljava/lang/String;)V',
				t
			)
		},
		track: function (t, e) {
			jsb.reflection.callStaticMethod(
				'com/cocos/game/CCSensorsDataAPI',
				'track',
				'(Ljava/lang/String;Ljava/lang/String;)V',
				t,
				e
			)
		},
		login: function (t) {
			jsb.reflection.callStaticMethod(
				'com/cocos/game/CCSensorsDataAPI',
				'login',
				'(Ljava/lang/String;)V',
				t
			)
		},
		logout: function () {
			jsb.reflection.callStaticMethod('com/cocos/game/CCSensorsDataAPI', 'logout', '()V')
		},
		identify: function (t) {
			jsb.reflection.callStaticMethod(
				'com/cocos/game/CCSensorsDataAPI',
				'identify',
				'(Ljava/lang/String;)V',
				t
			)
		},
		registerApp: function (t) {
			jsb.reflection.callStaticMethod(
				'com/cocos/game/CCSensorsDataAPI',
				'registerApp',
				'(Ljava/lang/String;)V',
				t
			)
		},
		clearAppRegister: function (t) {
			jsb.reflection.callStaticMethod(
				'com/cocos/game/CCSensorsDataAPI',
				'clearAppRegister',
				'(Ljava/lang/String;)V',
				t
			)
		},
		setProfile: function (t) {
			jsb.reflection.callStaticMethod(
				'com/cocos/game/CCSensorsDataAPI',
				'setProfile',
				'(Ljava/lang/String;)V',
				t
			)
		},
		setOnceProfile: function (t) {
			jsb.reflection.callStaticMethod(
				'com/cocos/game/CCSensorsDataAPI',
				'setOnceProfile',
				'(Ljava/lang/String;)V',
				t
			)
		},
		setIncrementProfile: function (t, e) {
			jsb.reflection.callStaticMethod(
				'com/cocos/game/CCSensorsDataAPI',
				'setIncrementProfile',
				'(Ljava/lang/String;F)V',
				t,
				e
			)
		},
		setAppendProfile: function (t) {
			jsb.reflection.callStaticMethod(
				'com/cocos/game/CCSensorsDataAPI',
				'setAppendProfile',
				'(Ljava/lang/String;)V',
				t
			)
		},
		profileUnset: function (t) {
			jsb.reflection.callStaticMethod(
				'com/cocos/game/CCSensorsDataAPI',
				'profileUnset',
				'(Ljava/lang/String;)V',
				t
			)
		},
		profileDelete: function () {
			jsb.reflection.callStaticMethod(
				'com/cocos/game/CCSensorsDataAPI',
				'profileDelete',
				'()V'
			)
		},
		flush: function () {
			jsb.reflection.callStaticMethod('com/cocos/game/CCSensorsDataAPI', 'flush', '()V')
		},
		trackTimerStart: function (t) {
			return jsb.reflection.callStaticMethod(
				'com/cocos/game/CCSensorsDataAPI',
				'trackTimerStart',
				'(Ljava/lang/String;)Ljava/lang/String;',
				t
			)
		},
		trackTimerPause: function (t) {
			jsb.reflection.callStaticMethod(
				'com/cocos/game/CCSensorsDataAPI',
				'trackTimerPause',
				'(Ljava/lang/String;)V',
				t
			)
		},
		trackTimerResume: function (t) {
			jsb.reflection.callStaticMethod(
				'com/cocos/game/CCSensorsDataAPI',
				'trackTimerResume',
				'(Ljava/lang/String;)V',
				t
			)
		},
		trackTimerEnd: function (t, e) {
			jsb.reflection.callStaticMethod(
				'com/cocos/game/CCSensorsDataAPI',
				'trackTimerEnd',
				'(Ljava/lang/String;Ljava/lang/String;)V',
				t,
				e
			)
		},
		trackAppInstall: function (t) {
			jsb.reflection.callStaticMethod(
				'com/cocos/game/CCSensorsDataAPI',
				'trackAppInstall',
				'(Ljava/lang/String;)V',
				t
			)
		},
		trackAppViewScreen: function (t, e) {
			jsb.reflection.callStaticMethod(
				'com/cocos/game/CCSensorsDataAPI',
				'trackAppViewScreen',
				'(Ljava/lang/String;Ljava/lang/String;)V',
				t,
				e
			)
		},
		getDistinctId: function () {
			return jsb.reflection.callStaticMethod(
				'com/cocos/game/CCSensorsDataAPI',
				'getDistinctId',
				'()Ljava/lang/String;'
			)
		},
		getAnonymousId: function () {
			return jsb.reflection.callStaticMethod(
				'com/cocos/game/CCSensorsDataAPI',
				'getAnonymousId',
				'()Ljava/lang/String;'
			)
		},
		getLoginId: function () {
			return jsb.reflection.callStaticMethod(
				'com/cocos/game/CCSensorsDataAPI',
				'getLoginId',
				'()Ljava/lang/String;'
			)
		},
		getPresetProperties: function () {
			return jsb.reflection.callStaticMethod(
				'com/cocos/game/CCSensorsDataAPI',
				'getPresetProperties',
				'()Ljava/lang/String;'
			)
		}
	},
	ios_api = {
		init: function (t) {
			jsb.reflection.callStaticMethod('CCSensorsDataAPI', 'initSA:', t)
		},
		track: function (t, e) {
			jsb.reflection.callStaticMethod('CCSensorsDataAPI', 'track:properties:', t, e)
		},
		login: function (t) {
			jsb.reflection.callStaticMethod('CCSensorsDataAPI', 'login:', t)
		},
		logout: function () {
			jsb.reflection.callStaticMethod('CCSensorsDataAPI', 'logout')
		},
		identify: function (t) {
			jsb.reflection.callStaticMethod('CCSensorsDataAPI', 'identify:', t)
		},
		registerApp: function (t) {
			jsb.reflection.callStaticMethod('CCSensorsDataAPI', 'registerSuperProperties:', t)
		},
		clearAppRegister: function (t) {
			jsb.reflection.callStaticMethod('CCSensorsDataAPI', 'unregisterSuperProperty:', t)
		},
		setProfile: function (t) {
			jsb.reflection.callStaticMethod('CCSensorsDataAPI', 'setProfile:', t)
		},
		setOnceProfile: function (t) {
			jsb.reflection.callStaticMethod('CCSensorsDataAPI', 'setOnceProfile:', t)
		},
		setIncrementProfile: function (t, e) {
			jsb.reflection.callStaticMethod('CCSensorsDataAPI', 'increment:by:', t, e)
		},
		setAppendProfile: function (t) {
			jsb.reflection.callStaticMethod('CCSensorsDataAPI', 'profileAppend:', t)
		},
		profileUnset: function (t) {
			jsb.reflection.callStaticMethod('CCSensorsDataAPI', 'profileUnset:', t)
		},
		profileDelete: function () {
			jsb.reflection.callStaticMethod('CCSensorsDataAPI', 'profileDelete')
		},
		flush: function () {
			jsb.reflection.callStaticMethod('CCSensorsDataAPI', 'flush')
		},
		trackTimerStart: function (t) {
			return jsb.reflection.callStaticMethod('CCSensorsDataAPI', 'trackTimerStart:', t)
		},
		trackTimerPause: function (t) {
			jsb.reflection.callStaticMethod('CCSensorsDataAPI', 'trackTimerPause:', t)
		},
		trackTimerResume: function (t) {
			jsb.reflection.callStaticMethod('CCSensorsDataAPI', 'trackTimerResume:', t)
		},
		trackTimerEnd: function (t, e) {
			jsb.reflection.callStaticMethod(
				'CCSensorsDataAPI',
				'trackTimerEnd:withProperties:',
				t,
				e
			)
		},
		trackAppInstall: function (t) {
			jsb.reflection.callStaticMethod('CCSensorsDataAPI', 'trackAppInstall:', t)
		},
		trackAppViewScreen: function (t, e) {
			jsb.reflection.callStaticMethod(
				'CCSensorsDataAPI',
				'trackAppViewScreen:withProperties:',
				t,
				e
			)
		},
		getDistinctId: function () {
			return jsb.reflection.callStaticMethod('CCSensorsDataAPI', 'getDistinctId')
		},
		getAnonymousId: function () {
			return jsb.reflection.callStaticMethod('CCSensorsDataAPI', 'getAnonymousId')
		},
		getLoginId: function () {
			return jsb.reflection.callStaticMethod('CCSensorsDataAPI', 'getLoginId')
		},
		getPresetProperties: function () {
			return jsb.reflection.callStaticMethod('CCSensorsDataAPI', 'getPresetProperties')
		}
	}
function _isNativePlatform() {
	return !(!sys.isNative || ('iOS' != sys.os && 'Android' != sys.os))
}
function isString$1(t) {
	return '[object String]' == Object.prototype.toString.call(t)
}
function isBoolean$1(t) {
	return '[object Boolean]' == Object.prototype.toString.call(t)
}
var isArray$1 =
	Array.isArray ||
	function (t) {
		return '[object Array]' === Object.prototype.toString.call(t)
	}
function isNumber$1(t) {
	return '[object Number]' == Object.prototype.toString.call(t) && /[\d\.]+/.test(String(t))
}
var COMMON_API_LIST = [
		'track',
		'login',
		'logout',
		'identify',
		'registerApp',
		'clearAppRegister',
		'setProfile',
		'setOnceProfile'
	],
	NATIVE_API_LIST = [
		'setIncrementProfile',
		'setAppendProfile',
		'profileUnset',
		'profileDelete',
		'flush',
		'trackTimerStart',
		'trackTimerPause',
		'trackTimerResume',
		'trackTimerEnd',
		'trackAppInstall',
		'trackAppViewScreen',
		'getDistinctId',
		'getAnonymousId',
		'getLoginId',
		'getPresetProperties'
	]
function initNative(e) {
	if (
		(NATIVE_API_LIST.forEach(function (t) {
			e[t] = function () {
				return console.log('该方法未定义！'), !1
			}
		}),
		!_isNativePlatform())
	)
		return !1
	COMMON_API_LIST.forEach(function (t) {
		e[t] = function () {
			return console.log('该方法未定义！'), !1
		}
	}),
		(e.para.app_start = !1),
		(e.para.app_end = !1),
		(e.para.enable_encrypt = !1),
		(e.para.enable_native = !1)
	var a = { iOS: ios_api, Android: android_api }[sys.os]
	e.init = function () {
		if (!isBoolean$1(e.para.enable_native) || !e.para.enable_native) return !1
		isBoolean$1(e.para.app_start) || (e.para.app_start = !1),
			isBoolean$1(e.para.app_end) || (e.para.app_end = !1),
			isBoolean$1(e.para.enable_encrypt) || (e.para.enable_encrypt = !1),
			isBoolean$1(e.para.show_log) || (e.para.show_log = !1)
		var t = {
			serverUrl: e.para.server_url,
			appStart: e.para.app_start,
			appEnd: e.para.app_end,
			enableLog: e.para.show_log,
			enableEncrypt: e.para.enable_encrypt
		}
		initApi(e, a)
		t = JSON.stringify(t)
		a.init(t)
	}
}
function initApi(t, r) {
	;(t.track = function (t, e) {
		if (!isString$1(t)) return console.log('传入的事件名称不是一个合法的字符串！'), !1
		e = JSON.stringify(e)
		r.track(t, e)
	}),
		(t.login = function (t) {
			isString$1(t) ? r.login(t) : console.log('传入的 id 不是一个合法的字符串！')
		}),
		(t.logout = function () {
			r.logout()
		}),
		(t.identify = function (t) {
			r.identify(t)
		}),
		(t.registerApp = function (t) {
			t = JSON.stringify(t)
			r.registerApp(t)
		}),
		(t.clearAppRegister = function (t) {
			if (!isArray$1(t)) return console.log('参数类型错误！'), !1
			for (var e in t)
				isString$1(t[e]) ? r.clearAppRegister(t[e]) : console.log('参数类型错误！')
		}),
		(t.setProfile = function (t) {
			t = JSON.stringify(t)
			r.setProfile(t)
		}),
		(t.setOnceProfile = function (t) {
			t = JSON.stringify(t)
			r.setOnceProfile(t)
		}),
		(t.setIncrementProfile = function (t, e) {
			t && isNumber$1(e) ? r.setIncrementProfile(t, e) : console.log('输入参数错误！')
		}),
		(t.setAppendProfile = function (t, e) {
			var a = {}
			a[t] = e
			a = JSON.stringify(a)
			r.setAppendProfile(a)
		}),
		(t.profileUnset = function (t) {
			r.profileUnset(t)
		}),
		(t.profileDelete = function () {
			r.profileDelete()
		}),
		(t.flush = function () {
			r.flush()
		}),
		(t.trackTimerStart = function (t) {
			return r.trackTimerStart(t)
		}),
		(t.trackTimerPause = function (t) {
			r.trackTimerPause(t)
		}),
		(t.trackTimerResume = function (t) {
			r.trackTimerResume(t)
		}),
		(t.trackTimerEnd = function (t, e) {
			e = JSON.stringify(e)
			r.trackTimerEnd(t, e)
		}),
		(t.trackAppInstall = function (t) {
			t = JSON.stringify(t)
			r.trackAppInstall(t)
		}),
		(t.trackAppViewScreen = function (t, e) {
			e = JSON.stringify(e)
			r.trackAppViewScreen(t, e)
		}),
		(t.getDistinctId = function () {
			return r.getDistinctId()
		}),
		(t.getAnonymousId = function () {
			return r.getAnonymousId()
		}),
		(t.getLoginId = function () {
			return r.getLoginId()
		}),
		(t.getPresetProperties = function () {
			return r.getPresetProperties()
		})
}
switch (((sa._ = _), (sa.system_api = compose), (sa.system = system), getPlatform())) {
	case 'WECHAT_GAME':
		;(sa.platform_obj = wx), (sa.storageName = 'sd2015-wechat-game')
		break
	case 'QQ_GAME':
		;(sa.platform_obj = qq), (sa.storageName = 'sd2015-qq-game')
		break
	case 'BYTEDANCE_GAME':
		;(sa.platform_obj = tt), (sa.storageName = 'sd2015-bytedance-game')
		break
	case 'VIVO_MINI_GAME':
		;(sa.platform_obj = qg), (sa.storageName = 'sd2015-vivo-game')
		break
	case 'OPPO_MINI_GAME':
		;(sa.platform_obj = qg), (sa.storageName = 'sd2015-oppo-game')
		break
	default:
		;(sa.para.batch_send = !1),
			(sa.system_api = compose$1),
			(sa.system = system$1),
			(sa.storageName = 'sd2015-h5-game')
}
function getPlatform() {
	var t = sys.platform,
		e = sys.Platform.WECHAT_GAME,
		a = sys.Platform.QQ_PLAY || '',
		r = sys.Platform.BYTEDANCE_GAME
	if (t === sys.Platform.OPPO_MINI_GAME) {
		return 'OPPO_MINI_GAME'
	} else if (t === sys.Platform.VIVO_MINI_GAME) {
		return 'VIVO_MINI_GAME'
	}
	return t === e ? 'WECHAT_GAME' : t === a ? 'QQ_GAME' : t === r ? 'BYTEDANCE_GAME' : 'DEFAULT'
}
initNative(sa)

export default sa
