import { _decorator, Component, log, Node, sp, v3 } from 'cc';
import { EffectMgr } from '../../scripts/EffectMgr';
import { xcore } from '../../../scripts/libs/xcore';
import { E_EVENT } from '../../../scripts/ConstGlobal';

const { ccclass, property } = _decorator;

@ccclass('UnitNewsEffect')
export class UnitNewsEffect extends Component {

    @property(sp.Skeleton)
    private anim: sp.Skeleton = null;

    async setData(data: any) {

        if (data.anim) {

            // await xcore.res.remoteLoadSpine(xcore.gameData.cospath + `image/newseffect/`, data.anim, this.anim)
            await xcore.res.bundleLoadSpine('resources', `./res/image/newseffect/${data.anim}`, this.anim)
            if (data.anim == 'baize' || data.anim == 'huodou' || data.anim == 'tianhu' || data.anim == 'qiongqi' || data.anim == 'suanni' || data.anim == 'taotie') {
                this.anim.setAnimation(0, 'chuchang', false);
                this.anim.node.setPosition(v3(0, -200))
            } else {
                this.anim.setAnimation(0, 'animation', false);
            }


            this.anim.setCompleteListener(() => {
                this.killNews();
                if (data.anim02) {
                    xcore.event.raiseEvent(E_EVENT.NewsEffect, { anim: data.anim02 })
                }
            })
        }
    }


    killNews() {
        this.anim.skeletonData = null;
        EffectMgr.getInstance().killNewsEffect(this);
    }
}


