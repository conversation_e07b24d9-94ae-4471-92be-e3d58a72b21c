import { _decorator, Color, color, Component, Label, Node, Sprite } from 'cc';
import { xcore } from '../../../scripts/libs/xcore';
const { ccclass, property } = _decorator;

@ccclass('UnitSelectGameTime')
export class UnitSelectGameTime extends Component {

    @property(Sprite)
    private sprFrame: Sprite = null;

    @property(Label)
    private lbTime: Label = null;

    private _index: number


    start() {

    }
    setData(time: any, index: number, selectIndex: number) {
        this._index = index;
        this.lbTime.string = time+'秒';
        xcore.res.bundleLoadSprite('resources', index != selectIndex ? './img_unpack/common_btn_blue' : './img_unpack/common_btn_yellow02', this.sprFrame);
        this.lbTime.color = index == selectIndex ? new Color(245, 245, 245, 255) : new Color(52, 36, 24, 255);
    }
    update(deltaTime: number) {

    }
}


