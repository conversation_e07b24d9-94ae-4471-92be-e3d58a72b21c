import { Director, Game, Node, director, game, log, sys, warn } from "cc";

import { DEBUG } from "cc/env";
import { Singleton } from "./libs/utils/Singleton";
import { xcore } from "./libs/xcore";
import { Sound } from "./libs/utils/Sound";
import { TimerManager } from "./libs/manager/TimerManager";
import { E_Channel, E_EVENT } from "./ConstGlobal";
import Tool from "./libs/utils/Tool";
import ElectronAPI from "../ElectronAPI";
import TimeUtil from "./libs/utils/TimeUtil";


export default class App extends Singleton {

    private persistRootNode: Node = null;

    async initServerInfo() {

        this.initRuntime();
        this.initEnv();
        //await xcore.wx.init(xcore);

        xcore.http.addHeader('content-type', 'application/json');
        console.log('sEnv', xcore.channel);

        xcore.http.defaults.baseURL = xcore.channel == E_Channel.TIKTOK ? 'https://apig.xiaoyisz.com/live-game-ntm-v1/' : 'https://dev-api.xiaoyisz.com/live-game-service/'

        xcore.http.setRequestInterceptor(
            request => {
                if (DEBUG) {
                    console.log(`send: path:${request.path} pamars:${request.data || `none`}`)
                }
                return request
            }
        )

        xcore.http.setResponseInterceptor(
            response => {
                if (DEBUG) {
                    console.log('recv:' + response.path, response.data);
                }
                const data = response.data
                if (data.code === 0) {
                    return data;
                }

                else if (data.code == 903 || data.code == 904 || data.code == 802 || data.code == 907) {
                    xcore.ui.showToast('网络异常，请手动刷新再试！')

                    return new Error(`网络异常，请手动刷新再试！`);
                }
                else if (data.code == 2026 || data.code == 2027) {
                    return new Error(data.code);
                }
                else {
                    if (DEBUG) {
                        warn(`code:${data.code} - ${data.message}`)
                        xcore.ui.showToast(`code:${data.code} - ${data.message}`)
                    } else {
                        warn(`${data.message}`)
                        xcore.ui.showToast(`${data.message}`)
                    }
                    Tool.log('error', `http req:${response.path} code:${data.code} message:${data.message}`)
                    return new Error(data.code);
                }
            }
        )
    }

    public async init() {

        await this.initServerInfo();
        await this.initGameConfig();

        // 创建持久根节点
        this.persistRootNode = new Node("PersistRootNode");
        director.addPersistRootNode(this.persistRootNode);

        // 创建音频模块
        xcore.sound = this.persistRootNode.addComponent(Sound);

        // 创建时间模块 
        xcore.timer = this.persistRootNode.addComponent(TimerManager);

        //初始化界面配置文件 IUIConfigClass
        xcore.ui.initViewConfig();
        this.initOperation()
        // 游戏显示事件
        game.on(Game.EVENT_SHOW, () => {
            //xcore.timer.load();     // 平台不需要在退出时精准计算时间，直接暂时游戏时间
            //xcore.sound.resume();
            //xcore.event.raiseEvent(E_EVENT.GameShow);
        });

        // 游戏隐藏事件
        game.on(Game.EVENT_HIDE, () => {
            //xcore.timer.save();     // 平台不需要在退出时精准计算时间，直接暂时游戏时间
            //xcore.sound.pause();
            //xcore.event.raiseEvent(E_EVENT.GameHide);
        });

        // 场景切换
        director.on(Director.EVENT_BEFORE_SCENE_LOADING, function () {
            xcore.ui.closeAllView();
        }, this);
        // window.addEventListener("unhandledrejection", (err) => {

        // })
        // window.onerror = function (message, url, line, ceil, error) {

        // }
    }

    /**配置表初始化 */
    async initGameConfig() {

    }

    // initEnvByHostname() {
    //     let devHost = ['dev.xiaoyisz.com', 'localhost', 'gz-cdn-1258783731.cos.ap-guangzhou.myqcloud.com', '************'];
    //     if (devHost.includes(location.hostname) || DEBUG) {
    //         xcore.env = 'dev'
    //     }
    //     else {
    //         xcore.env = 'prod'
    //     }
    // }

    initEnv() {

        let query = Tool.getCommandLineArgs();
        if (query.token) {
            xcore.channel = E_Channel.TIKTOK;
        } else {
            xcore.channel = E_Channel.GAME560;
        }
    }

    initRuntime() {
        // xcore.runtime = C_Runtime.other;
        xcore.gameData.query = Tool.parseQuery((location === null || location === void 0 ? void 0 : location.search) || '')
        // var ua = window.navigator.userAgent.toLowerCase(); // @ts-ignore

        // if (/micromessenger/i.test(ua)) {
        //     xcore.runtime = C_Runtime.browser_wx;

        //     if (/miniprogram/i.test(ua)) {
        //         xcore.runtime = C_Runtime.browser_wx;
        //     }
        // }
    }

    initOperation() {
        let needUpdate = false;
        let read = ElectronAPI.readFile('sound.json');
        if (read) {
            let readConfig = JSON.parse(read);
            xcore.gameData.soundData = readConfig;
            if (xcore.gameData.soundData.sound == null || xcore.gameData.soundData.sound == undefined) {
                xcore.gameData.soundData.sound = 1;
                needUpdate = true;
            }
            if (xcore.gameData.soundData.music == null || xcore.gameData.soundData.music == undefined) {
                xcore.gameData.soundData.music = 1;
                needUpdate = true;
            }
        } else {
            xcore.gameData.soundData = { sound: 1, music: 1 }
            needUpdate = true;
        }
        xcore.sound.setSoundNum(xcore.gameData.soundData.sound);
        xcore.sound.setMusicNum(xcore.gameData.soundData.music);
        xcore.gameData.gameJoinTime = TimeUtil.formatTimestampToDate(Math.floor(TimeUtil.getServerTime()), "_")
        let giftData = ElectronAPI.readFile(`gift_${xcore.gameData.gameJoinTime}.json`);
        if (giftData) {
            let giftDataConfig = JSON.parse(giftData);
            xcore.gameData.giftData = giftDataConfig;
        } else {
            xcore.gameData.giftData = {}
            needUpdate = true;
        }
        if (needUpdate) {
            this.updateOperation()
        }
    }
    updateOperation() {
        if (!xcore.gameData) return
        if (xcore.gameData.soundData) {
            ElectronAPI.writeFile('sound.json', { sound: xcore.gameData.soundData.sound, music: xcore.gameData.soundData.music });
        }
        if (xcore.gameData.giftData) {
            ElectronAPI.writeFile(`gift_${xcore.gameData.gameJoinTime}.json`, xcore.gameData.giftData);
        }
    }



}

