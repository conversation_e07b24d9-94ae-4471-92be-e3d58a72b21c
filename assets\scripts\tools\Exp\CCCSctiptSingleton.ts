import { Node, director, js } from 'cc'
import { EventManager } from '../../components/EventManager'
 

export default class CCCSctiptSingleton extends EventManager {
	private static singletonHome: Node
	private static _insts = new Map<string, CCCSctiptSingleton>()

	protected static getInst<T extends CCCSctiptSingleton>(si: new () => T, guid = ''): T {
		let name = js.getClassName(si)

		if (guid != '') name += guid

		if (!this._insts.has(name)) {
			let inst = new Node().addComponent(si)
			inst.node.name = name
			if (CCCSctiptSingleton.singletonHome == null) {
				CCCSctiptSingleton.singletonHome = new Node()
				CCCSctiptSingleton.singletonHome.name = 'SingletonHome'
				director.addPersistRootNode(this.singletonHome)
			}
			this.singletonHome.addChild(inst.node)
			this._insts.set(name, inst)
			console.log('创建单例', name)
		}
		return this._insts.get(name) as T
	}
}
