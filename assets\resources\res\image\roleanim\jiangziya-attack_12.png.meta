{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "bc2f21f0-8656-43e8-be73-0b50a4f33d83", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "bc2f21f0-8656-43e8-be73-0b50a4f33d83@6c48a", "displayName": "jiangziya-attack_12", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "bc2f21f0-8656-43e8-be73-0b50a4f33d83", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "bc2f21f0-8656-43e8-be73-0b50a4f33d83@f9941", "displayName": "jiangziya-attack_12", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 7.5, "offsetY": -24, "trimX": 50, "trimY": 60, "width": 115, "height": 128, "rawWidth": 200, "rawHeight": 200, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-57.5, -64, 0, 57.5, -64, 0, -57.5, 64, 0, 57.5, 64, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [50, 140, 165, 140, 50, 12, 165, 12], "nuv": [0.25, 0.06, 0.825, 0.06, 0.25, 0.7, 0.825, 0.7], "minPos": [-57.5, -64, 0], "maxPos": [57.5, 64, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "bc2f21f0-8656-43e8-be73-0b50a4f33d83@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "bc2f21f0-8656-43e8-be73-0b50a4f33d83@6c48a"}}