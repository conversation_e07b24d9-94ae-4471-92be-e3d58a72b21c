[1, ["6aGJJ6+wRK3Lo3PjpNq0yx@6c48a", "108Fz5iTtKmYd3y2VBaPGm@6c48a"], 0, [["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3]], [[0, 0, 1, 2, 3, 4, 5]], [[0, "q<PERSON><PERSON><PERSON>", "\nqiongqi.png\nsize: 2046,2005\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\njianjia01\n  rotate: true\n  xy: 1019, 723\n  size: 216, 221\n  orig: 219, 223\n  offset: 1, 1\n  index: -1\njiao01\n  rotate: true\n  xy: 1670, 418\n  size: 260, 349\n  orig: 262, 351\n  offset: 1, 1\n  index: -1\njiao02\n  rotate: true\n  xy: 2, 288\n  size: 288, 304\n  orig: 290, 306\n  offset: 1, 1\n  index: -1\nmaofa01\n  rotate: false\n  xy: 1747, 1405\n  size: 210, 110\n  orig: 210, 110\n  offset: 0, 0\n  index: -1\nmaofa02\n  rotate: false\n  xy: 335, 1752\n  size: 191, 78\n  orig: 193, 82\n  offset: 1, 3\n  index: -1\nmaofa03\n  rotate: false\n  xy: 1129, 1301\n  size: 218, 128\n  orig: 221, 130\n  offset: 2, 1\n  index: -1\nmaofa04\n  rotate: false\n  xy: 413, 806\n  size: 366, 182\n  orig: 366, 182\n  offset: 0, 0\n  index: -1\nmaofa05\n  rotate: false\n  xy: 2, 1044\n  size: 275, 166\n  orig: 275, 166\n  offset: 0, 0\n  index: -1\nmaofa06\n  rotate: true\n  xy: 1767, 907\n  size: 183, 195\n  orig: 183, 195\n  offset: 0, 0\n  index: -1\nmaofa07\n  rotate: true\n  xy: 232, 1977\n  size: 26, 57\n  orig: 28, 59\n  offset: 1, 1\n  index: -1\nmaofa08\n  rotate: true\n  xy: 729, 1961\n  size: 42, 63\n  orig: 42, 63\n  offset: 0, 0\n  index: -1\nneihoutui01\n  rotate: true\n  xy: 1812, 1092\n  size: 174, 200\n  orig: 176, 202\n  offset: 1, 1\n  index: -1\nneihoutui02\n  rotate: true\n  xy: 2, 1224\n  size: 138, 191\n  orig: 138, 191\n  offset: 0, 0\n  index: -1\nneihoutui03\n  rotate: false\n  xy: 797, 1450\n  size: 207, 104\n  orig: 209, 106\n  offset: 1, 1\n  index: -1\nneiqiantui01\n  rotate: true\n  xy: 1498, 701\n  size: 222, 246\n  orig: 224, 248\n  offset: 1, 1\n  index: -1\nneiqiantui02\n  rotate: true\n  xy: 195, 840\n  size: 188, 216\n  orig: 190, 219\n  offset: 1, 2\n  index: -1\nneiqiantui03\n  rotate: false\n  xy: 1349, 1292\n  size: 242, 130\n  orig: 244, 132\n  offset: 1, 1\n  index: -1\nshent01\n  rotate: true\n  xy: 654, 147\n  size: 327, 409\n  orig: 327, 409\n  offset: 0, 0\n  index: -1\nshenti02\n  rotate: false\n  xy: 308, 262\n  size: 344, 308\n  orig: 344, 308\n  offset: 0, 0\n  index: -1\ntou02\n  rotate: true\n  xy: 958, 1160\n  size: 156, 166\n  orig: 158, 168\n  offset: 1, 1\n  index: -1\ntou03\n  rotate: true\n  xy: 783, 1895\n  size: 64, 90\n  orig: 64, 90\n  offset: 0, 0\n  index: -1\ntou04\n  rotate: true\n  xy: 1677, 1533\n  size: 92, 128\n  orig: 92, 128\n  offset: 0, 0\n  index: -1\ntou05\n  rotate: true\n  xy: 644, 1332\n  size: 133, 151\n  orig: 133, 151\n  offset: 0, 0\n  index: -1\ntx/01_00001\n  rotate: true\n  xy: 1593, 1272\n  size: 131, 169\n  orig: 288, 279\n  offset: 81, 72\n  index: -1\ntx/01_00002\n  rotate: true\n  xy: 538, 1002\n  size: 175, 194\n  orig: 288, 279\n  offset: 59, 43\n  index: -1\ntx/01_00003\n  rotate: true\n  xy: 1171, 941\n  size: 179, 187\n  orig: 288, 279\n  offset: 62, 42\n  index: -1\ntx/01_00004\n  rotate: false\n  xy: 2, 858\n  size: 191, 184\n  orig: 288, 279\n  offset: 60, 38\n  index: -1\ntx/01_00005\n  rotate: false\n  xy: 1360, 940\n  size: 197, 180\n  orig: 288, 279\n  offset: 60, 34\n  index: -1\ntx/01_00006\n  rotate: false\n  xy: 1559, 925\n  size: 206, 180\n  orig: 288, 279\n  offset: 57, 28\n  index: -1\ntx/01_00007\n  rotate: false\n  xy: 956, 955\n  size: 213, 177\n  orig: 288, 279\n  offset: 52, 27\n  index: -1\ntx/01_00008\n  rotate: false\n  xy: 734, 990\n  size: 220, 176\n  orig: 288, 279\n  offset: 46, 22\n  index: -1\ntx/01_00009\n  rotate: false\n  xy: 740, 1168\n  size: 216, 148\n  orig: 288, 279\n  offset: 46, 20\n  index: -1\ntx/01_00010\n  rotate: false\n  xy: 367, 1201\n  size: 214, 144\n  orig: 288, 279\n  offset: 44, 18\n  index: -1\ntx/01_00011\n  rotate: false\n  xy: 367, 1201\n  size: 214, 144\n  orig: 288, 279\n  offset: 44, 18\n  index: -1\ntx/Fire-Loop_1\n  rotate: true\n  xy: 952, 1889\n  size: 64, 96\n  orig: 84, 122\n  offset: 11, 3\n  index: -1\ntx/Fire-Loop_11\n  rotate: true\n  xy: 921, 1815\n  size: 72, 106\n  orig: 84, 122\n  offset: 10, 4\n  index: -1\ntx/Fire-Loop_13\n  rotate: true\n  xy: 1029, 1814\n  size: 72, 111\n  orig: 84, 122\n  offset: 7, 3\n  index: -1\ntx/Fire-Loop_15\n  rotate: true\n  xy: 1858, 1876\n  size: 69, 109\n  orig: 84, 122\n  offset: 8, 3\n  index: -1\ntx/Fire-Loop_17\n  rotate: true\n  xy: 1563, 1880\n  size: 67, 96\n  orig: 84, 122\n  offset: 8, 3\n  index: -1\ntx/Fire-Loop_19\n  rotate: true\n  xy: 1378, 1885\n  size: 66, 107\n  orig: 84, 122\n  offset: 10, 2\n  index: -1\ntx/Fire-Loop_21\n  rotate: true\n  xy: 2, 1849\n  size: 69, 116\n  orig: 84, 122\n  offset: 2, 2\n  index: -1\ntx/Fire-Loop_23\n  rotate: true\n  xy: 420, 1832\n  size: 71, 114\n  orig: 84, 122\n  offset: 1, 2\n  index: -1\ntx/Fire-Loop_25\n  rotate: true\n  xy: 1945, 1788\n  size: 77, 94\n  orig: 84, 122\n  offset: 4, 2\n  index: -1\ntx/Fire-Loop_27\n  rotate: true\n  xy: 1262, 1810\n  size: 73, 105\n  orig: 84, 122\n  offset: 8, 2\n  index: -1\ntx/Fire-Loop_29\n  rotate: true\n  xy: 1369, 1810\n  size: 73, 114\n  orig: 84, 122\n  offset: 7, 2\n  index: -1\ntx/Fire-Loop_3\n  rotate: true\n  xy: 1269, 1885\n  size: 66, 107\n  orig: 84, 122\n  offset: 6, 3\n  index: -1\ntx/Fire-Loop_31\n  rotate: true\n  xy: 120, 1846\n  size: 69, 112\n  orig: 84, 122\n  offset: 5, 2\n  index: -1\ntx/Fire-Loop_33\n  rotate: true\n  xy: 536, 1825\n  size: 71, 92\n  orig: 84, 122\n  offset: 6, 2\n  index: -1\ntx/Fire-Loop_5\n  rotate: true\n  xy: 1050, 1888\n  size: 65, 114\n  orig: 84, 122\n  offset: 4, 3\n  index: -1\ntx/Fire-Loop_7\n  rotate: true\n  xy: 630, 1823\n  size: 72, 113\n  orig: 84, 122\n  offset: 4, 3\n  index: -1\ntx/Fire-Loop_9\n  rotate: true\n  xy: 320, 1837\n  size: 70, 98\n  orig: 84, 122\n  offset: 7, 3\n  index: -1\ntx/Smoke_1\n  rotate: false\n  xy: 291, 1975\n  size: 41, 28\n  orig: 153, 97\n  offset: 8, 2\n  index: -1\ntx/Smoke_10\n  rotate: false\n  xy: 2, 2002\n  size: 1, 1\n  orig: 1, 1\n  offset: 0, 0\n  index: -1\ntx/Smoke_2\n  rotate: false\n  xy: 1661, 1879\n  size: 90, 67\n  orig: 153, 97\n  offset: 17, 3\n  index: -1\ntx/Smoke_3\n  rotate: false\n  xy: 989, 1732\n  size: 118, 80\n  orig: 153, 97\n  offset: 15, 4\n  index: -1\ntx/Smoke_4\n  rotate: false\n  xy: 181, 1583\n  size: 134, 87\n  orig: 153, 97\n  offset: 13, 4\n  index: -1\ntx/Smoke_5\n  rotate: false\n  xy: 181, 1672\n  size: 135, 84\n  orig: 153, 97\n  offset: 14, 9\n  index: -1\ntx/Smoke_6\n  rotate: false\n  xy: 1319, 1727\n  size: 133, 81\n  orig: 153, 97\n  offset: 16, 12\n  index: -1\ntx/Smoke_7\n  rotate: false\n  xy: 1485, 1805\n  size: 94, 73\n  orig: 153, 97\n  offset: 53, 18\n  index: -1\ntx/Smoke_8\n  rotate: false\n  xy: 71, 1983\n  size: 23, 20\n  orig: 153, 97\n  offset: 109, 70\n  index: -1\ntx/Smoke_9\n  rotate: false\n  xy: 5, 1996\n  size: 11, 7\n  orig: 153, 97\n  offset: 115, 78\n  index: -1\ntx/fire_0\n  rotate: true\n  xy: 1565, 1718\n  size: 83, 177\n  orig: 83, 177\n  offset: 0, 0\n  index: -1\ntx/fire_1\n  rotate: true\n  xy: 1744, 1715\n  size: 83, 177\n  orig: 83, 177\n  offset: 0, 0\n  index: -1\ntx/fire_10\n  rotate: true\n  xy: 1571, 1630\n  size: 83, 177\n  orig: 83, 177\n  offset: 0, 0\n  index: -1\ntx/fire_11\n  rotate: true\n  xy: 1866, 1615\n  size: 83, 177\n  orig: 83, 177\n  offset: 0, 0\n  index: -1\ntx/fire_12\n  rotate: true\n  xy: 2, 1598\n  size: 83, 177\n  orig: 83, 177\n  offset: 0, 0\n  index: -1\ntx/fire_13\n  rotate: true\n  xy: 317, 1582\n  size: 83, 177\n  orig: 83, 177\n  offset: 0, 0\n  index: -1\ntx/fire_14\n  rotate: true\n  xy: 496, 1573\n  size: 83, 177\n  orig: 83, 177\n  offset: 0, 0\n  index: -1\ntx/fire_15\n  rotate: true\n  xy: 675, 1572\n  size: 83, 177\n  orig: 83, 177\n  offset: 0, 0\n  index: -1\ntx/fire_2\n  rotate: true\n  xy: 2, 1683\n  size: 83, 177\n  orig: 83, 177\n  offset: 0, 0\n  index: -1\ntx/fire_3\n  rotate: true\n  xy: 318, 1667\n  size: 83, 177\n  orig: 83, 177\n  offset: 0, 0\n  index: -1\ntx/fire_4\n  rotate: true\n  xy: 497, 1658\n  size: 83, 177\n  orig: 83, 177\n  offset: 0, 0\n  index: -1\ntx/fire_5\n  rotate: true\n  xy: 676, 1657\n  size: 83, 177\n  orig: 83, 177\n  offset: 0, 0\n  index: -1\ntx/fire_6\n  rotate: true\n  xy: 855, 1647\n  size: 83, 177\n  orig: 83, 177\n  offset: 0, 0\n  index: -1\ntx/fire_7\n  rotate: true\n  xy: 1034, 1645\n  size: 83, 177\n  orig: 83, 177\n  offset: 0, 0\n  index: -1\ntx/fire_8\n  rotate: true\n  xy: 1213, 1642\n  size: 83, 177\n  orig: 83, 177\n  offset: 0, 0\n  index: -1\ntx/fire_9\n  rotate: true\n  xy: 1392, 1633\n  size: 83, 177\n  orig: 83, 177\n  offset: 0, 0\n  index: -1\ntx/gh_0\n  rotate: false\n  xy: 1127, 1440\n  size: 118, 109\n  orig: 200, 180\n  offset: 42, 35\n  index: -1\ntx/gh_1\n  rotate: true\n  xy: 120, 1364\n  size: 116, 124\n  orig: 200, 180\n  offset: 43, 31\n  index: -1\ntx/gh_10\n  rotate: true\n  xy: 2, 1771\n  size: 76, 104\n  orig: 200, 180\n  offset: 78, 41\n  index: -1\ntx/gh_11\n  rotate: true\n  xy: 1166, 1886\n  size: 65, 101\n  orig: 200, 180\n  offset: 77, 44\n  index: -1\ntx/gh_12\n  rotate: true\n  xy: 609, 1898\n  size: 62, 97\n  orig: 200, 180\n  offset: 78, 47\n  index: -1\ntx/gh_13\n  rotate: true\n  xy: 1872, 1947\n  size: 56, 58\n  orig: 200, 180\n  offset: 78, 83\n  index: -1\ntx/gh_2\n  rotate: true\n  xy: 1481, 1424\n  size: 111, 129\n  orig: 200, 180\n  offset: 46, 26\n  index: -1\ntx/gh_3\n  rotate: true\n  xy: 1612, 1418\n  size: 113, 133\n  orig: 200, 180\n  offset: 46, 23\n  index: -1\ntx/gh_4\n  rotate: true\n  xy: 521, 1347\n  size: 120, 121\n  orig: 200, 180\n  offset: 45, 30\n  index: -1\ntx/gh_5\n  rotate: false\n  xy: 1006, 1445\n  size: 119, 108\n  orig: 200, 180\n  offset: 47, 36\n  index: -1\ntx/gh_6\n  rotate: true\n  xy: 559, 1469\n  size: 102, 107\n  orig: 200, 180\n  offset: 60, 38\n  index: -1\ntx/gh_7\n  rotate: true\n  xy: 336, 1480\n  size: 100, 110\n  orig: 200, 180\n  offset: 61, 38\n  index: -1\ntx/gh_8\n  rotate: true\n  xy: 224, 1482\n  size: 98, 110\n  orig: 200, 180\n  offset: 61, 38\n  index: -1\ntx/gh_9\n  rotate: true\n  xy: 2, 1500\n  size: 96, 108\n  orig: 200, 180\n  offset: 64, 39\n  index: -1\ntx/huo_0\n  rotate: true\n  xy: 1454, 1720\n  size: 83, 109\n  orig: 105, 133\n  offset: 12, 12\n  index: -1\ntx/huo_1\n  rotate: true\n  xy: 1923, 1700\n  size: 86, 108\n  orig: 105, 133\n  offset: 8, 17\n  index: -1\ntx/huo_2\n  rotate: true\n  xy: 112, 1485\n  size: 96, 110\n  orig: 105, 133\n  offset: 3, 12\n  index: -1\ntx/huo_3\n  rotate: true\n  xy: 854, 1558\n  size: 87, 112\n  orig: 105, 133\n  offset: 12, 12\n  index: -1\ntx/huo_4\n  rotate: true\n  xy: 1179, 1551\n  size: 89, 114\n  orig: 105, 133\n  offset: 10, 12\n  index: -1\ntx/huo_5\n  rotate: true\n  xy: 1295, 1541\n  size: 90, 116\n  orig: 105, 133\n  offset: 12, 11\n  index: -1\ntx/huo_6\n  rotate: true\n  xy: 1750, 1627\n  size: 86, 114\n  orig: 105, 133\n  offset: 13, 12\n  index: -1\ntx/huo_7\n  rotate: true\n  xy: 1675, 1803\n  size: 74, 121\n  orig: 105, 133\n  offset: 12, 6\n  index: -1\ntx/huo_8\n  rotate: true\n  xy: 1109, 1730\n  size: 80, 121\n  orig: 105, 133\n  offset: 12, 9\n  index: -1\ntx/huo_9\n  rotate: true\n  xy: 1142, 1812\n  size: 72, 118\n  orig: 105, 133\n  offset: 13, 13\n  index: -1\ntx/j_0\n  rotate: false\n  xy: 668, 1467\n  size: 127, 103\n  orig: 300, 300\n  offset: 164, 158\n  index: -1\ntx/j_10\n  rotate: false\n  xy: 255, 578\n  size: 240, 226\n  orig: 300, 300\n  offset: 16, 48\n  index: -1\ntx/j_12\n  rotate: false\n  xy: 497, 572\n  size: 239, 232\n  orig: 300, 300\n  offset: 13, 42\n  index: -1\ntx/j_14\n  rotate: false\n  xy: 738, 499\n  size: 237, 237\n  orig: 300, 300\n  offset: 11, 37\n  index: -1\ntx/j_16\n  rotate: false\n  xy: 1746, 680\n  size: 239, 225\n  orig: 300, 300\n  offset: 11, 38\n  index: -1\ntx/j_18\n  rotate: false\n  xy: 781, 738\n  size: 236, 215\n  orig: 300, 300\n  offset: 15, 37\n  index: -1\ntx/j_2\n  rotate: false\n  xy: 1247, 1431\n  size: 232, 107\n  orig: 300, 300\n  offset: 57, 160\n  index: -1\ntx/j_20\n  rotate: false\n  xy: 1126, 1134\n  size: 219, 165\n  orig: 300, 300\n  offset: 26, 44\n  index: -1\ntx/j_22\n  rotate: false\n  xy: 195, 1212\n  size: 170, 146\n  orig: 300, 300\n  offset: 64, 55\n  index: -1\ntx/j_24\n  rotate: false\n  xy: 968, 1556\n  size: 114, 87\n  orig: 300, 300\n  offset: 109, 91\n  index: -1\ntx/j_26\n  rotate: true\n  xy: 65, 1920\n  size: 57, 70\n  orig: 300, 300\n  offset: 124, 100\n  index: -1\ntx/j_4\n  rotate: false\n  xy: 1561, 1107\n  size: 249, 159\n  orig: 300, 300\n  offset: 39, 113\n  index: -1\ntx/j_6\n  rotate: false\n  xy: 1242, 722\n  size: 254, 216\n  orig: 300, 300\n  offset: 31, 57\n  index: -1\ntx/j_8\n  rotate: false\n  xy: 2, 612\n  size: 251, 226\n  orig: 300, 300\n  offset: 22, 51\n  index: -1\ntx/jiao01_g_0\n  rotate: true\n  xy: 959, 1955\n  size: 48, 50\n  orig: 155, 93\n  offset: 20, 13\n  index: -1\ntx/jiao01_g_1\n  rotate: true\n  xy: 1199, 1953\n  size: 50, 57\n  orig: 155, 93\n  offset: 21, 10\n  index: -1\ntx/jiao01_g_10\n  rotate: true\n  xy: 794, 1961\n  size: 42, 60\n  orig: 155, 93\n  offset: 94, 22\n  index: -1\ntx/jiao01_g_11\n  rotate: true\n  xy: 488, 1969\n  size: 34, 42\n  orig: 155, 93\n  offset: 101, 39\n  index: -1\ntx/jiao01_g_12\n  rotate: true\n  xy: 154, 1978\n  size: 25, 39\n  orig: 155, 93\n  offset: 108, 40\n  index: -1\ntx/jiao01_g_2\n  rotate: false\n  xy: 137, 1918\n  size: 62, 58\n  orig: 155, 93\n  offset: 23, 9\n  index: -1\ntx/jiao01_g_3\n  rotate: false\n  xy: 289, 1909\n  size: 67, 61\n  orig: 155, 93\n  offset: 25, 9\n  index: -1\ntx/jiao01_g_4\n  rotate: false\n  xy: 470, 1905\n  size: 72, 61\n  orig: 155, 93\n  offset: 30, 10\n  index: -1\ntx/jiao01_g_5\n  rotate: false\n  xy: 201, 1917\n  size: 86, 58\n  orig: 155, 93\n  offset: 31, 11\n  index: -1\ntx/jiao01_g_6\n  rotate: false\n  xy: 1676, 1948\n  size: 75, 55\n  orig: 155, 93\n  offset: 45, 13\n  index: -1\ntx/jiao01_g_7\n  rotate: false\n  xy: 708, 1897\n  size: 73, 62\n  orig: 155, 93\n  offset: 59, 16\n  index: -1\ntx/jiao01_g_8\n  rotate: true\n  xy: 2, 1925\n  size: 57, 61\n  orig: 155, 93\n  offset: 78, 19\n  index: -1\ntx/jiao01_g_9\n  rotate: true\n  xy: 1932, 1947\n  size: 56, 61\n  orig: 155, 93\n  offset: 80, 20\n  index: -1\ntx/jm1_0\n  rotate: true\n  xy: 18, 1994\n  size: 9, 13\n  orig: 210, 166\n  offset: 35, 93\n  index: -1\ntx/jm1_1\n  rotate: false\n  xy: 1507, 1950\n  size: 58, 53\n  orig: 210, 166\n  offset: 29, 73\n  index: -1\ntx/jm1_10\n  rotate: false\n  xy: 1551, 1537\n  size: 124, 91\n  orig: 210, 166\n  offset: 58, 20\n  index: -1\ntx/jm1_11\n  rotate: false\n  xy: 1084, 1555\n  size: 93, 88\n  orig: 210, 166\n  offset: 88, 22\n  index: -1\ntx/jm1_12\n  rotate: false\n  xy: 1232, 1728\n  size: 85, 80\n  orig: 210, 166\n  offset: 93, 25\n  index: -1\ntx/jm1_2\n  rotate: false\n  xy: 528, 1744\n  size: 91, 79\n  orig: 210, 166\n  offset: 27, 61\n  index: -1\ntx/jm1_3\n  rotate: false\n  xy: 1941, 1517\n  size: 99, 96\n  orig: 210, 166\n  offset: 26, 48\n  index: -1\ntx/jm1_4\n  rotate: true\n  xy: 448, 1470\n  size: 101, 109\n  orig: 210, 166\n  offset: 26, 37\n  index: -1\ntx/jm1_5\n  rotate: true\n  xy: 2, 1369\n  size: 114, 116\n  orig: 210, 166\n  offset: 27, 30\n  index: -1\ntx/jm1_6\n  rotate: false\n  xy: 389, 1349\n  size: 130, 119\n  orig: 210, 166\n  offset: 29, 26\n  index: -1\ntx/jm1_7\n  rotate: false\n  xy: 246, 1360\n  size: 141, 118\n  orig: 210, 166\n  offset: 35, 24\n  index: -1\ntx/jm1_8\n  rotate: false\n  xy: 1413, 1540\n  size: 136, 91\n  orig: 210, 166\n  offset: 43, 22\n  index: -1\ntx/jm1_9\n  rotate: false\n  xy: 1807, 1520\n  size: 132, 93\n  orig: 210, 166\n  offset: 49, 20\n  index: -1\ntx/ks01\n  rotate: true\n  xy: 1594, 2\n  size: 414, 432\n  orig: 592, 556\n  offset: 89, 65\n  index: -1\ntx/lz\n  rotate: true\n  xy: 49, 1984\n  size: 19, 20\n  orig: 22, 22\n  offset: 1, 1\n  index: -1\ntx/q (1)\n  rotate: false\n  xy: 1753, 1948\n  size: 117, 55\n  orig: 121, 56\n  offset: 2, 0\n  index: -1\ntx/q (2)\n  rotate: false\n  xy: 1798, 1800\n  size: 145, 74\n  orig: 147, 76\n  offset: 1, 0\n  index: -1\ntx/q (3)\n  rotate: false\n  xy: 737, 1742\n  size: 131, 78\n  orig: 134, 79\n  offset: 1, 0\n  index: -1\ntx/q (4)\n  rotate: false\n  xy: 216, 1758\n  size: 117, 77\n  orig: 120, 79\n  offset: 2, 1\n  index: -1\ntx/q (5)\n  rotate: false\n  xy: 870, 1734\n  size: 117, 79\n  orig: 119, 80\n  offset: 1, 0\n  index: -1\ntx/q (6)\n  rotate: false\n  xy: 621, 1743\n  size: 114, 78\n  orig: 117, 79\n  offset: 2, 1\n  index: -1\ntx/q (7)\n  rotate: false\n  xy: 108, 1768\n  size: 106, 76\n  orig: 109, 77\n  offset: 2, 0\n  index: -1\ntx/q (8)\n  rotate: true\n  xy: 1969, 1867\n  size: 78, 75\n  orig: 81, 76\n  offset: 1, 1\n  index: -1\ntx/q (9)\n  rotate: false\n  xy: 1487, 1882\n  size: 74, 66\n  orig: 77, 68\n  offset: 1, 1\n  index: -1\ntx/waihoutui01_zg_0\n  rotate: false\n  xy: 96, 1981\n  size: 22, 22\n  orig: 208, 150\n  offset: 157, 108\n  index: -1\ntx/waihoutui01_zg_1\n  rotate: false\n  xy: 600, 1967\n  size: 38, 36\n  orig: 208, 150\n  offset: 142, 95\n  index: -1\ntx/waihoutui01_zg_10\n  rotate: false\n  xy: 856, 1961\n  size: 101, 42\n  orig: 208, 150\n  offset: 31, 22\n  index: -1\ntx/waihoutui01_zg_2\n  rotate: false\n  xy: 1258, 1953\n  size: 52, 50\n  orig: 208, 150\n  offset: 127, 80\n  index: -1\ntx/waihoutui01_zg_3\n  rotate: false\n  xy: 544, 1904\n  size: 63, 61\n  orig: 208, 150\n  offset: 113, 66\n  index: -1\ntx/waihoutui01_zg_4\n  rotate: false\n  xy: 875, 1895\n  size: 75, 64\n  orig: 208, 150\n  offset: 92, 53\n  index: -1\ntx/waihoutui01_zg_5\n  rotate: false\n  xy: 234, 1838\n  size: 84, 69\n  orig: 208, 150\n  offset: 76, 38\n  index: -1\ntx/waihoutui01_zg_6\n  rotate: false\n  xy: 1581, 1804\n  size: 92, 73\n  orig: 208, 150\n  offset: 62, 25\n  index: -1\ntx/waihoutui01_zg_7\n  rotate: false\n  xy: 1753, 1879\n  size: 103, 67\n  orig: 208, 150\n  offset: 47, 21\n  index: -1\ntx/waihoutui01_zg_8\n  rotate: false\n  xy: 358, 1909\n  size: 110, 60\n  orig: 208, 150\n  offset: 32, 20\n  index: -1\ntx/waihoutui01_zg_9\n  rotate: false\n  xy: 1567, 1949\n  size: 107, 54\n  orig: 208, 150\n  offset: 32, 20\n  index: -1\ntx/waiqiantui02_zg_0\n  rotate: false\n  xy: 33, 1993\n  size: 14, 10\n  orig: 65, 167\n  offset: 27, 134\n  index: -1\ntx/waiqiantui02_zg_1\n  rotate: true\n  xy: 120, 1979\n  size: 24, 32\n  orig: 65, 167\n  offset: 25, 113\n  index: -1\ntx/waiqiantui02_zg_2\n  rotate: true\n  xy: 334, 1972\n  size: 31, 52\n  orig: 65, 167\n  offset: 23, 94\n  index: -1\ntx/waiqiantui02_zg_3\n  rotate: true\n  xy: 532, 1968\n  size: 35, 66\n  orig: 65, 167\n  offset: 22, 79\n  index: -1\ntx/waiqiantui02_zg_4\n  rotate: true\n  xy: 640, 1962\n  size: 41, 87\n  orig: 65, 167\n  offset: 17, 57\n  index: -1\ntx/waiqiantui02_zg_5\n  rotate: true\n  xy: 1011, 1955\n  size: 48, 112\n  orig: 65, 167\n  offset: 11, 26\n  index: -1\ntx/waiqiantui02_zg_6\n  rotate: true\n  xy: 1312, 1953\n  size: 50, 103\n  orig: 65, 167\n  offset: 9, 24\n  index: -1\ntx/waiqiantui02_zg_7\n  rotate: true\n  xy: 1417, 1953\n  size: 50, 88\n  orig: 65, 167\n  offset: 8, 23\n  index: -1\ntx/waiqiantui02_zg_8\n  rotate: true\n  xy: 1125, 1955\n  size: 48, 72\n  orig: 65, 167\n  offset: 8, 23\n  index: -1\ntx/waiqiantui02_zg_9\n  rotate: true\n  xy: 195, 1978\n  size: 25, 35\n  orig: 65, 167\n  offset: 10, 25\n  index: -1\ntx/xz\n  rotate: false\n  xy: 583, 1179\n  size: 155, 151\n  orig: 258, 258\n  offset: 58, 48\n  index: -1\nwaihoutui01\n  rotate: true\n  xy: 977, 476\n  size: 244, 394\n  orig: 247, 396\n  offset: 2, 1\n  index: -1\nwaihoutui02\n  rotate: false\n  xy: 745, 1822\n  size: 174, 71\n  orig: 176, 73\n  offset: 1, 1\n  index: -1\nwaihoutui03\n  rotate: true\n  xy: 279, 1030\n  size: 169, 257\n  orig: 169, 257\n  offset: 0, 0\n  index: -1\nwaiqiantui01\n  rotate: false\n  xy: 1764, 1268\n  size: 280, 135\n  orig: 282, 137\n  offset: 1, 1\n  index: -1\nwaiqiantui02\n  rotate: true\n  xy: 1373, 446\n  size: 253, 295\n  orig: 255, 297\n  offset: 1, 1\n  index: -1\nwaiqiantui03\n  rotate: false\n  xy: 1347, 1122\n  size: 212, 168\n  orig: 215, 170\n  offset: 1, 1\n  index: -1\nweiba01\n  rotate: false\n  xy: 1065, 108\n  size: 527, 336\n  orig: 527, 336\n  offset: 0, 0\n  index: -1\nweiba02\n  rotate: true\n  xy: 797, 1318\n  size: 120, 330\n  orig: 120, 330\n  offset: 0, 0\n  index: -1\nyingying\n  rotate: false\n  xy: 388, 1971\n  size: 98, 32\n  orig: 100, 34\n  offset: 1, 1\n  index: -1\n\nqiongqi2.png\nsize: 1585,1295\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nchibang01\n  rotate: true\n  xy: 2, 373\n  size: 920, 783\n  orig: 920, 783\n  offset: 0, 0\n  index: -1\nchibang02\n  rotate: false\n  xy: 787, 511\n  size: 796, 782\n  orig: 796, 782\n  offset: 0, 0\n  index: -1\ntx/x\n  rotate: false\n  xy: 787, 2\n  size: 507, 507\n  orig: 523, 523\n  offset: 8, 8\n  index: -1\n", ["qiongqi.png", "qiongqi2.png"], {"skeleton": {"hash": "eEzzJ+begdCylb6GgtHB9q2tqGs", "spine": "3.8.97", "x": -275.27, "y": -23.44, "width": 674.44, "height": 547.41, "images": "./images/", "audio": "C:\\Users\\<USER>\\Desktop\\角色\\qiongqi"}, "bones": [{"name": "root"}, {"name": "zong", "parent": "root", "scaleX": 0.45, "scaleY": 0.45}, {"name": "bone", "parent": "zong", "x": 1060.15, "y": 443.48}, {"name": "shenti02", "parent": "bone", "length": 248.28, "rotation": 176.75, "x": -865.27, "y": -62.56}, {"name": "shent01", "parent": "shenti02", "length": 262.23, "rotation": -20.05, "x": 240.96, "y": 4.87}, {"name": "tou03", "parent": "shent01", "length": 163.14, "rotation": 60.42, "x": 258.31, "y": 0.22}, {"name": "tou04", "parent": "tou03", "length": 73.18, "rotation": 28.78, "x": 75.74, "y": 33.08}, {"name": "jianjia01", "parent": "shent01", "length": 225.42, "rotation": 167.5, "x": 121.16, "y": 16.45}, {"name": "waiqiantui02", "parent": "jianjia01", "length": 217.95, "rotation": -78.49, "x": 228.5, "y": -4.28}, {"name": "waiqiantui01", "parent": "waiqiantui02", "length": 193.68, "rotation": -156.48, "x": 214.9, "y": 2.48, "transform": "noRotationOrReflection"}, {"name": "neiqiantui02", "parent": "shent01", "length": 171.7, "rotation": 103.61, "x": 110.85, "y": 9.02}, {"name": "neiqiantui01", "parent": "neiqiantui02", "length": 200.77, "rotation": -27.48, "x": 170.65, "y": -1.29}, {"name": "neiqiantui03", "parent": "neiqiantui01", "length": 189.58, "rotation": -161.33, "x": 196.18, "y": 3.14, "transform": "noRotationOrReflection"}, {"name": "waihoutui01", "parent": "shenti02", "length": 201.71, "rotation": 123.61, "x": -0.23, "y": 4.1}, {"name": "waihoutui03", "parent": "waihoutui01", "length": 80.06, "rotation": 50.79, "x": 200.19, "y": 0.89}, {"name": "waihoutui3", "parent": "waihoutui03", "length": 154.58, "rotation": -63.96, "x": 80.06}, {"name": "waihoutui02", "parent": "waihoutui3", "length": 107.71, "rotation": -81.05, "x": 156.26, "y": -0.52}, {"name": "neih<PERSON>ui<PERSON>", "parent": "shenti02", "length": 174.85, "rotation": 78.09, "x": 31.72, "y": 30.57}, {"name": "neihoutui02", "parent": "neih<PERSON>ui<PERSON>", "length": 60.13, "rotation": 67.28, "x": 177.78, "y": 2.62}, {"name": "neihoutui2", "parent": "neihoutui02", "length": 98.59, "rotation": -83.26, "x": 60.13}, {"name": "neihoutui03", "parent": "neihoutui2", "length": 150.71, "rotation": -41.91, "x": 100.09, "y": 0.91}, {"name": "weiba01", "parent": "shenti02", "length": 186.46, "rotation": 138.25, "x": -83.11, "y": 48.7}, {"name": "weiba1", "parent": "weiba01", "length": 121.51, "rotation": 13.61, "x": 186.46}, {"name": "weiba2", "parent": "weiba1", "length": 130.09, "rotation": 12.47, "x": 121.51}, {"name": "weiba3", "parent": "weiba2", "length": 114.28, "rotation": 18.04, "x": 130.09}, {"name": "weiba02", "parent": "weiba3", "length": 90.05, "rotation": 39.54, "x": 116.01, "y": 1.78}, {"name": "weiba4", "parent": "weiba02", "length": 105.2, "rotation": 41.72, "x": 90.05}, {"name": "weiba5", "parent": "weiba4", "length": 124.52, "rotation": 40.17, "x": 105.2}, {"name": "weiba6", "parent": "weiba5", "length": 144.39, "rotation": 11.49, "x": 124.52}, {"name": "chibang01", "parent": "shenti02", "length": 213.89, "rotation": -98.37, "x": 182.95, "y": -13.13}, {"name": "chibang1", "parent": "chibang01", "length": 174.79, "rotation": 21.29, "x": 213.89}, {"name": "chibang2", "parent": "chibang1", "length": 703.88, "rotation": -88.77, "x": 167.02, "y": -10.59}, {"name": "chibang02", "parent": "shenti02", "length": 200.49, "rotation": -91.8, "x": 363.02, "y": -6.84}, {"name": "chibang3", "parent": "chibang02", "length": 162.99, "rotation": 21.08, "x": 200.49}, {"name": "chibang4", "parent": "chibang3", "length": 657.75, "rotation": -91.38, "x": 151.95, "y": -2.94}, {"name": "1", "parent": "bone", "x": -1130.49, "y": -356.28, "transform": "noScale", "color": "ff3f00ff"}, {"name": "3", "parent": "bone", "x": -1362.45, "y": -345.8, "transform": "noScale", "color": "ff3f00ff"}, {"name": "2", "parent": "bone", "x": -946.04, "y": -384.69, "color": "ff3f00ff"}, {"name": "4", "parent": "2", "x": 50.8, "y": 84.23, "color": "ff3f00ff"}, {"name": "6", "parent": "bone", "x": -638.56, "y": -398.52, "color": "ff3f00ff"}, {"name": "5", "parent": "6", "x": -45.84, "y": 147.26, "color": "ff3f00ff"}, {"name": "shent1", "parent": "shent01", "length": 76.59, "rotation": 146.11, "x": 114.85, "y": 91.57}, {"name": "shent2", "parent": "shent1", "length": 58.99, "rotation": -11.77, "x": 76.59}, {"name": "shent3", "parent": "shent2", "length": 57.82, "rotation": -26.08, "x": 58.99}, {"name": "tou5", "parent": "tou04", "length": 38.38, "rotation": 8.64, "x": 37.64, "y": 13.56}, {"name": "tou6", "parent": "tou5", "length": 28.02, "rotation": -22.7, "x": 38.38}, {"name": "tou7", "parent": "tou6", "length": 24.44, "rotation": 53.09, "x": 28.02}, {"name": "tou3", "parent": "tou04", "length": 40.81, "rotation": 74.58, "x": 11.93, "y": 5.5}, {"name": "tou4", "parent": "tou3", "length": 33.1, "rotation": -32.47, "x": 40.81}, {"name": "tou05", "parent": "tou03", "length": 41.96, "rotation": 166.08, "x": 41.83, "y": 44.61}, {"name": "tou8", "parent": "tou05", "length": 42.31, "rotation": 23.31, "x": 41.96}, {"name": "maofa06", "parent": "tou03", "length": 67.75, "rotation": -146.82, "x": 22.76, "y": -39.08}, {"name": "maofa6", "parent": "maofa06", "length": 67.11, "rotation": -38.63, "x": 67.75}, {"name": "maofa7", "parent": "maofa6", "length": 68.15, "rotation": 9.37, "x": 67.11}, {"name": "maofa01", "parent": "tou03", "length": 56.63, "rotation": 169.88, "x": -19.56, "y": -8.24}, {"name": "maofa1", "parent": "maofa01", "length": 61.65, "rotation": -40.39, "x": 56.63}, {"name": "maofa2", "parent": "maofa1", "length": 63.96, "rotation": 28.05, "x": 61.65}, {"name": "maofa07", "parent": "maofa6", "length": 38.47, "rotation": 36.54, "x": -7.25, "y": 32}, {"name": "maofa08", "parent": "maofa6", "length": 27.16, "rotation": 67.8, "x": 47.03, "y": 24.75}, {"name": "maofa8", "parent": "maofa08", "length": 32.93, "rotation": -50.06, "x": 27.16}, {"name": "maofa05", "parent": "shent01", "length": 113.03, "rotation": -164.3, "x": 241.34, "y": -117.72}, {"name": "maofa5", "parent": "maofa05", "length": 55.48, "rotation": -15.03, "x": 113.03}, {"name": "maofa9", "parent": "maofa5", "length": 63.53, "rotation": 14.9, "x": 55.48}, {"name": "maofa10", "parent": "maofa05", "length": 43.9, "rotation": 123.54, "x": 99, "y": 48.72}, {"name": "maofa11", "parent": "maofa10", "length": 35.47, "rotation": -32.85, "x": 43.9}, {"name": "maofa04", "parent": "shent01", "length": 75.05, "rotation": -141.87, "x": 20.25, "y": -114.19}, {"name": "maofa4", "parent": "maofa04", "length": 58.68, "rotation": 75.17, "x": 75.05}, {"name": "maofa12", "parent": "maofa4", "length": 59.65, "rotation": 63.43, "x": 58.68}, {"name": "maofa13", "parent": "maofa12", "length": 46.37, "rotation": 49.59, "x": 59.65}, {"name": "fire1", "parent": "weiba4", "length": 108.86, "rotation": 48.74, "x": 171.45, "y": 45.54, "color": "6553ffff"}, {"name": "fire2", "parent": "fire1", "length": 101.69, "rotation": 2.51, "x": 107.79, "y": -1.31, "color": "6553ffff"}, {"name": "tou_yhuo", "parent": "tou03", "rotation": 56.3, "x": 96.2, "y": -15.87, "scaleX": 0.5287, "scaleY": 0.7079}, {"name": "tx", "parent": "zong", "x": -514.56, "y": 403.31}, {"name": "huo", "parent": "waiqiantui01", "rotation": 156.48, "x": 285.12, "y": -466.91}, {"name": "huo2", "parent": "neiqiantui03", "rotation": 161.33, "x": 108.85, "y": -357.55}, {"name": "huo3", "parent": "waihoutui02", "rotation": 153.76, "x": 55.65, "y": 7.67}, {"name": "huo4", "parent": "neihoutui03", "rotation": 163, "x": 66.56, "y": 1.89, "scaleX": 1.7986, "scaleY": 1.7986}, {"name": "huo5", "parent": "waihoutui01", "rotation": 59.71, "x": 198.78, "y": -3.81}, {"name": "tx2", "parent": "zong", "x": -722.05, "y": 310.69}, {"name": "tx3", "parent": "zong", "x": -722.05, "y": 310.69}, {"name": "tx4", "parent": "zong", "x": -722.05, "y": 310.69}, {"name": "tx5", "parent": "zong", "x": -722.05, "y": 310.69}, {"name": "tx6", "parent": "zong", "x": -722.05, "y": 310.69}, {"name": "tx7", "parent": "zong", "x": -722.05, "y": 310.69}, {"name": "tx8", "parent": "zong", "x": -722.05, "y": 310.69}, {"name": "tx9", "parent": "zong", "x": -722.05, "y": 310.69}], "slots": [{"name": "A参考", "bone": "root"}, {"name": "yinying", "bone": "zong", "attachment": "yingying"}, {"name": "chibang02", "bone": "chibang02", "attachment": "chibang02"}, {"name": "fire1", "bone": "fire1", "attachment": "tx/fire_0"}, {"name": "weiba02", "bone": "weiba02", "attachment": "weiba02"}, {"name": "fire2", "bone": "fire1", "attachment": "tx/fire_0"}, {"name": "weiba01", "bone": "weiba01", "attachment": "weiba01"}, {"name": "neihoutui03", "bone": "neihoutui03", "attachment": "neihoutui03"}, {"name": "neihoutui02", "bone": "neihoutui02", "attachment": "neihoutui02"}, {"name": "neih<PERSON>ui<PERSON>", "bone": "neih<PERSON>ui<PERSON>", "attachment": "neih<PERSON>ui<PERSON>"}, {"name": "neiqiantui03", "bone": "neiqiantui03", "attachment": "neiqiantui03"}, {"name": "neiqiantui02", "bone": "neiqiantui02", "attachment": "neiqiantui02"}, {"name": "neiqiantui01", "bone": "neiqiantui01", "attachment": "neiqiantui01"}, {"name": "shenti02", "bone": "shenti02", "attachment": "shenti02"}, {"name": "waihoutui03", "bone": "waihoutui03", "attachment": "waihoutui03"}, {"name": "waihoutui02", "bone": "waihoutui02", "attachment": "waihoutui02"}, {"name": "waihoutui01", "bone": "waihoutui01", "attachment": "waihoutui01"}, {"name": "shent01", "bone": "shent01", "attachment": "shent01"}, {"name": "jianjia01", "bone": "jianjia01", "attachment": "jianjia01"}, {"name": "jiao02", "bone": "tou03", "attachment": "jiao02"}, {"name": "tou05", "bone": "tou05", "attachment": "tou05"}, {"name": "tou04", "bone": "tou5", "attachment": "tou04"}, {"name": "tou03", "bone": "tou3", "attachment": "tou03"}, {"name": "tou02", "bone": "tou03", "attachment": "tou02"}, {"name": "fire", "bone": "tou_yhuo", "blend": "additive"}, {"name": "maofa08", "bone": "maofa08", "attachment": "maofa08"}, {"name": "maofa07", "bone": "maofa07", "attachment": "maofa07"}, {"name": "maofa06", "bone": "maofa06", "attachment": "maofa06"}, {"name": "maofa05", "bone": "maofa05", "attachment": "maofa05"}, {"name": "maofa04", "bone": "maofa04", "attachment": "maofa04"}, {"name": "maofa03", "bone": "shent01", "attachment": "maofa03"}, {"name": "maofa02", "bone": "shent01", "attachment": "maofa02"}, {"name": "maofa01", "bone": "maofa01", "attachment": "maofa01"}, {"name": "jiao01", "bone": "tou03", "attachment": "jiao01"}, {"name": "chibang01", "bone": "chibang01", "attachment": "chibang01"}, {"name": "waiqiantui03", "bone": "jianjia01", "attachment": "waiqiantui03"}, {"name": "waiqiantui02", "bone": "waiqiantui02", "attachment": "waiqiantui02"}, {"name": "waiqiantui01", "bone": "waiqiantui01", "attachment": "waiqiantui01"}, {"name": "smoke", "bone": "tx"}, {"name": "huo", "bone": "huo", "blend": "additive"}, {"name": "huo2", "bone": "huo2", "blend": "additive"}, {"name": "gh2", "bone": "tx8", "blend": "additive"}, {"name": "huo3", "bone": "huo3", "blend": "additive"}, {"name": "huo5", "bone": "huo5", "blend": "additive"}, {"name": "huo4", "bone": "huo4", "blend": "additive"}, {"name": "waihoutui01_zg", "bone": "waihoutui01", "blend": "additive"}, {"name": "waiqiantui02_zg", "bone": "waiqiantui02", "blend": "additive"}, {"name": "jm", "bone": "jianjia01", "blend": "additive"}, {"name": "jiao1_g", "bone": "tou03", "blend": "additive"}, {"name": "jiao2_g", "bone": "tou03", "blend": "additive"}, {"name": "lz", "bone": "tx2"}, {"name": "lz4", "bone": "tx5"}, {"name": "lz5", "bone": "tx6"}, {"name": "lz3", "bone": "tx4"}, {"name": "lz2", "bone": "tx3"}, {"name": "gh", "bone": "tx7"}, {"name": "zk1", "bone": "tx3"}, {"name": "zk2", "bone": "tx4"}, {"name": "bo", "bone": "tx5", "blend": "additive"}, {"name": "bo2", "bone": "tx5", "blend": "additive"}, {"name": "xz", "bone": "tx", "blend": "additive"}, {"name": "hjs", "bone": "tx6"}, {"name": "hjs2", "bone": "tx7"}, {"name": "hjs3", "bone": "tx8"}, {"name": "hjs4", "bone": "tx9"}, {"name": "ju", "bone": "tx9", "blend": "additive"}, {"name": "ju2", "bone": "tx8", "blend": "additive"}, {"name": "qq", "bone": "tx2"}], "ik": [{"name": "1", "target": "1", "bendPositive": false, "stretch": true, "bones": ["jianjia01", "waiqiantui02"]}, {"name": "2", "order": 3, "target": "2", "bones": ["neihoutui2"]}, {"name": "3", "order": 1, "target": "3", "bendPositive": false, "stretch": true, "bones": ["neiqiantui02", "neiqiantui01"]}, {"name": "4", "order": 2, "target": "4", "bones": ["neih<PERSON>ui<PERSON>", "neihoutui02"]}, {"name": "5", "order": 4, "target": "5", "stretch": true, "bones": ["waihoutui01", "waihoutui03"]}, {"name": "6", "order": 5, "target": "6", "bones": ["waihoutui3"]}], "skins": [{"name": "default", "attachments": {"bo": {"tx/ks01": {"width": 592, "height": 556}}, "bo2": {"tx/ks01": {"scaleX": 2.0013, "scaleY": 2.0013, "rotation": -56.04, "width": 592, "height": 556}}, "chibang01": {"chibang01": {"type": "mesh", "hull": 24, "width": 920, "height": 783, "uvs": [0.96999, 0.26675, 0.88314, 0.43643, 0.72187, 0.64997, 0.58821, 0.66555, 0.50857, 0.77437, 0.41898, 0.82149, 0.27478, 0.83151, 0.34219, 0.90269, 0.29782, 0.97087, 0.17921, 1, 0.09389, 0.9839, 0.03635, 0.93249, 0.05871, 0.83977, 0.07844, 0.76405, 0.08383, 0.6907, 0.06128, 0.60111, 0.03508, 0.51616, 0, 0.43367, 0, 0.37089, 0.1011, 0.2884, 0.24465, 0.26993, 0.50958, 0.1768, 0.73638, 0.1197, 1, 0, 0.25074, 0.70069, 0.25513, 0.61342, 0.25827, 0.48661, 0.49608, 0.47554, 0.72963, 0.42108], "triangles": [24, 14, 25, 14, 15, 25, 25, 15, 26, 15, 16, 26, 26, 16, 19, 16, 17, 19, 17, 18, 19, 8, 9, 6, 11, 12, 10, 9, 10, 12, 6, 12, 13, 6, 9, 12, 24, 13, 14, 13, 24, 6, 8, 6, 7, 6, 24, 5, 24, 25, 5, 4, 5, 25, 4, 25, 27, 27, 25, 26, 4, 27, 3, 2, 3, 28, 2, 28, 1, 28, 3, 27, 19, 20, 26, 26, 20, 27, 27, 21, 28, 27, 20, 21, 1, 28, 0, 28, 22, 0, 28, 21, 22, 0, 22, 23], "vertices": [1, 31, 791.12, 18.89, 1, 1, 31, 687.52, -96.46, 1, 3, 31, 510.21, -232.58, 0.98627, 29, 353.95, -509.61, 0.01181, 30, -54.55, -525.68, 0.00192, 3, 31, 387.16, -221.3, 0.9073, 29, 317.23, -391.62, 0.06826, 30, -45.91, -402.41, 0.02445, 3, 31, 299.09, -291.1, 0.72333, 29, 219.02, -337.01, 0.20396, 30, -117.59, -315.87, 0.07271, 3, 31, 211.18, -311.74, 0.56105, 29, 166.28, -263.7, 0.33642, 30, -140.11, -228.42, 0.10253, 3, 31, 79.42, -294.35, 0.12411, 29, 131.88, -135.34, 0.82911, 30, -125.56, -96.32, 0.04678, 3, 31, 129.78, -360.81, 0.01863, 29, 89.77, -207.3, 0.98095, 30, -190.92, -148.09, 0.00042, 2, 31, 79.6, -405.51, 0.00754, 29, 29.27, -178.07, 0.99246, 1, 29, -15.05, -75.79, 1, 1, 29, -18.51, 3.64, 1, 1, 29, 10.25, 63.6, 1, 1, 29, 85.51, 58.07, 1, 2, 29, 147.24, 52.23, 0.95295, 30, -43.13, 72.87, 0.04705, 2, 29, 204.49, 58.94, 0.4134, 30, 12.65, 58.33, 0.5866, 1, 30, 85.28, 67, 1, 1, 30, 154.9, 79.58, 1, 2, 31, -109.89, 59.35, 0.0003, 30, 224, 100.54, 0.9997, 2, 31, -100.59, 107.62, 0.03177, 30, 272.46, 92.28, 0.96823, 2, 31, 2.96, 153.45, 0.45969, 30, 320.5, -10.26, 0.54031, 2, 31, 135.38, 142.67, 0.95417, 30, 312.57, -142.88, 0.04583, 1, 31, 388.51, 168.16, 1, 1, 31, 601.85, 172.6, 1, 1, 31, 857.74, 218.76, 1, 3, 31, 77.08, -189.59, 0.24462, 29, 227.76, -93.04, 0.43325, 30, -20.86, -91.73, 0.32213, 3, 31, 93.97, -123.26, 0.46926, 29, 295.5, -83.24, 0.10904, 30, 45.81, -107.19, 0.42169, 3, 31, 115.59, -26.3, 0.94796, 29, 393.34, -66.07, 0.00334, 30, 143.21, -126.72, 0.0487, 3, 31, 332.07, -59.17, 0.9784, 29, 445.89, -278.63, 0.01439, 30, 114.99, -343.85, 0.00721, 2, 31, 551.12, -57.94, 0.99961, 29, 530.94, -480.5, 0.00039], "edges": [24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 0, 46, 28, 26, 26, 24, 30, 28, 30, 32, 32, 34, 40, 38, 34, 36, 38, 36, 40, 42, 42, 44, 44, 46]}}, "chibang02": {"chibang02": {"type": "mesh", "hull": 22, "width": 796, "height": 782, "uvs": [1, 0.2356, 0.92251, 0.39895, 0.82814, 0.57833, 0.64389, 0.6491, 0.50859, 0.80997, 0.33692, 0.82732, 0.39717, 0.8956, 0.31304, 1, 0.16183, 1, 0.08793, 0.94074, 0.10498, 0.85857, 0.11976, 0.77872, 0.11863, 0.71622, 0.08452, 0.64447, 0.047, 0.57388, 0, 0.49055, 0, 0.44274, 0.09722, 0.3652, 0.25465, 0.32122, 0.46157, 0.23095, 0.66019, 0.16472, 1, 0, 0.28121, 0.70234, 0.27398, 0.61749, 0.27056, 0.53416, 0.47393, 0.4836, 0.68105, 0.42625, 0.4789, 0.59661, 0.48387, 0.70287], "triangles": [22, 12, 23, 12, 13, 23, 13, 14, 24, 24, 14, 17, 14, 15, 17, 15, 16, 17, 6, 7, 5, 9, 10, 8, 7, 8, 5, 8, 10, 5, 10, 11, 5, 11, 22, 5, 5, 22, 28, 11, 12, 22, 5, 28, 4, 4, 28, 3, 22, 27, 28, 28, 27, 3, 22, 23, 27, 3, 26, 2, 26, 3, 25, 23, 13, 24, 23, 24, 27, 24, 25, 27, 3, 27, 25, 2, 26, 1, 17, 18, 24, 24, 18, 25, 18, 19, 25, 25, 19, 26, 19, 20, 26, 26, 20, 1, 1, 20, 0, 20, 21, 0], "vertices": [1, 34, 717.77, 45.12, 1, 2, 34, 625.78, -62.85, 0.99819, 32, 495.09, -558.42, 0.00181, 3, 34, 517.61, -179.56, 0.96738, 32, 348.76, -495.93, 0.03093, 33, -40.05, -516.07, 0.00169, 3, 34, 361.72, -195.99, 0.83309, 32, 280.74, -354.7, 0.15343, 33, -52.71, -359.82, 0.01348, 3, 34, 225.7, -290.44, 0.5505, 32, 145.97, -258.47, 0.42883, 33, -143.85, -221.56, 0.02067, 3, 34, 90.05, -269, 0.25749, 32, 120.43, -123.54, 0.72353, 33, -119.14, -86.47, 0.01898, 3, 34, 122.95, -332.79, 0.05887, 32, 71.46, -176.01, 0.93394, 33, -183.71, -117.82, 0.00719, 2, 34, 37.5, -394.83, 0.00994, 32, -15.75, -116.48, 0.99006, 2, 34, -78.95, -364.37, 0.00043, 32, -26.33, 3.42, 0.99957, 1, 32, 14.67, 66.09, 1, 2, 32, 79.86, 58.22, 0.9723, 33, -91.61, 97.71, 0.0277, 2, 32, 143.1, 51.99, 0.7816, 33, -34.85, 69.15, 0.2184, 2, 32, 191.7, 57.18, 0.45673, 33, 12.37, 56.52, 0.54327, 2, 32, 245.21, 89.16, 0.1511, 33, 73.8, 67.11, 0.8489, 2, 32, 297.58, 123.76, 0.00847, 33, 135.1, 80.55, 0.99153, 1, 33, 208.06, 98.5, 1, 2, 34, -93.31, 89.82, 0.06438, 33, 244, 88.17, 0.93562, 2, 34, -3.1, 128.9, 0.35441, 33, 280.89, -2.95, 0.64559, 2, 34, 126.84, 130.47, 0.68775, 33, 279.32, -132.89, 0.31225, 2, 34, 304.05, 157.08, 0.9567, 33, 301.65, -310.69, 0.0433, 1, 34, 470.12, 167.19, 1, 1, 34, 764.39, 223.37, 1, 3, 34, 71.88, -163.22, 0.24062, 32, 213.9, -70.78, 0.45504, 33, -12.95, -70.86, 0.30434, 3, 34, 83.1, -97.57, 0.5162, 32, 279.49, -59.21, 0.08473, 33, 52.41, -83.66, 0.39907, 3, 34, 96.96, -33.84, 0.91924, 32, 344.16, -50.78, 0.00747, 33, 115.78, -99.06, 0.07328, 3, 34, 263.57, -36.55, 0.98184, 32, 397.77, -208.55, 0.01477, 33, 109.06, -265.56, 0.00339, 2, 34, 434.43, -34.88, 0.99815, 32, 456.93, -368.85, 0.00185, 3, 34, 245.04, -123.04, 0.84674, 32, 310.09, -220.26, 0.12172, 33, 23.03, -244.94, 0.03153, 3, 34, 227.84, -204.44, 0.67797, 32, 227.66, -231.51, 0.27961, 33, -57.93, -225.78, 0.04242], "edges": [28, 30, 28, 26, 26, 24, 20, 18, 18, 16, 14, 16, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 0, 42, 2, 0, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 24, 22, 22, 20]}}, "fire": {"tx/Fire-Loop_1": {"x": -0.34, "y": 36.81, "width": 84, "height": 122}, "tx/Fire-Loop_3": {"x": -0.34, "y": 36.81, "width": 84, "height": 122}, "tx/Fire-Loop_5": {"x": -0.34, "y": 36.81, "width": 84, "height": 122}, "tx/Fire-Loop_7": {"x": -0.34, "y": 36.81, "width": 84, "height": 122}, "tx/Fire-Loop_9": {"x": -0.34, "y": 36.81, "width": 84, "height": 122}, "tx/Fire-Loop_11": {"x": -0.34, "y": 36.81, "width": 84, "height": 122}, "tx/Fire-Loop_13": {"x": -0.34, "y": 36.81, "width": 84, "height": 122}, "tx/Fire-Loop_15": {"x": -0.34, "y": 36.81, "width": 84, "height": 122}, "tx/Fire-Loop_17": {"x": -0.34, "y": 36.81, "width": 84, "height": 122}, "tx/Fire-Loop_19": {"x": -0.34, "y": 36.81, "width": 84, "height": 122}, "tx/Fire-Loop_21": {"x": -0.34, "y": 36.81, "width": 84, "height": 122}, "tx/Fire-Loop_23": {"x": -0.34, "y": 36.81, "width": 84, "height": 122}, "tx/Fire-Loop_25": {"x": -0.34, "y": 36.81, "width": 84, "height": 122}, "tx/Fire-Loop_27": {"x": -0.34, "y": 36.81, "width": 84, "height": 122}, "tx/Fire-Loop_29": {"x": -0.34, "y": 36.81, "width": 84, "height": 122}, "tx/Fire-Loop_31": {"x": -0.34, "y": 36.81, "width": 84, "height": 122}, "tx/Fire-Loop_33": {"x": -0.34, "y": 36.81, "width": 84, "height": 122}}, "fire1": {"tx/fire_0": {"type": "mesh", "hull": 12, "width": 83, "height": 177, "uvs": [1, 0.18967, 1, 0.36599, 1, 0.54954, 1, 0.75011, 1, 1, 0, 1, 0, 0.69054, 0, 0.50938, 0, 0.33892, 0, 0.18301, 0, 0, 1, 0], "triangles": [5, 3, 4, 5, 6, 3, 6, 2, 3, 6, 7, 2, 7, 1, 2, 7, 8, 1, 8, 0, 1, 8, 9, 0, 9, 11, 0, 9, 10, 11], "vertices": [1, 70, 98.35, -84.09, 1, 2, 70, 46.22, -82.21, 0.99661, 69, 157.57, -81.42, 0.00339, 2, 70, -8.05, -80.26, 0.76672, 69, 103.27, -81.84, 0.23328, 2, 70, -67.35, -78.12, 0.22571, 69, 43.93, -82.3, 0.77429, 2, 70, -141.23, -75.46, 0.01543, 69, -29.99, -82.88, 0.98457, 1, 69, -31.31, 85.79, 1, 1, 69, 60.24, 86.5, 1, 2, 70, 9.9, 87.88, 0.13826, 69, 113.83, 86.92, 0.86174, 2, 70, 60.3, 86.07, 0.5978, 69, 164.26, 87.31, 0.4022, 2, 70, 106.4, 84.4, 0.88785, 69, 210.39, 87.67, 0.11215, 2, 70, 160.5, 82.45, 0.98158, 69, 264.53, 88.1, 0.01842, 1, 70, 154.43, -86.11, 1], "edges": [8, 10, 20, 22, 16, 2, 2, 4, 14, 16, 4, 14, 10, 12, 12, 14, 4, 6, 6, 8, 12, 6, 16, 18, 18, 20, 2, 0, 0, 22, 18, 0]}, "tx/fire_1": {"type": "mesh", "hull": 12, "width": 83, "height": 177, "uvs": [1, 0.18967, 1, 0.36599, 1, 0.54954, 1, 0.75011, 1, 1, 0, 1, 0, 0.69054, 0, 0.50938, 0, 0.33892, 0, 0.18301, 0, 0, 1, 0], "triangles": [5, 3, 4, 5, 6, 3, 6, 2, 3, 6, 7, 2, 7, 1, 2, 7, 8, 1, 8, 0, 1, 8, 9, 0, 9, 11, 0, 9, 10, 11], "vertices": [1, 70, 98.35, -84.09, 1, 2, 70, 46.22, -82.21, 0.99661, 69, 157.57, -81.42, 0.00339, 2, 70, -8.05, -80.26, 0.76672, 69, 103.27, -81.84, 0.23328, 2, 70, -67.35, -78.12, 0.22571, 69, 43.93, -82.3, 0.77429, 2, 70, -141.23, -75.46, 0.01543, 69, -29.99, -82.88, 0.98457, 1, 69, -31.31, 85.79, 1, 1, 69, 60.24, 86.5, 1, 2, 70, 9.9, 87.88, 0.13826, 69, 113.83, 86.92, 0.86174, 2, 70, 60.3, 86.07, 0.5978, 69, 164.26, 87.31, 0.4022, 2, 70, 106.4, 84.4, 0.88785, 69, 210.39, 87.67, 0.11215, 2, 70, 160.5, 82.45, 0.98158, 69, 264.53, 88.1, 0.01842, 1, 70, 154.43, -86.11, 1], "edges": [8, 10, 20, 22, 16, 2, 2, 4, 14, 16, 4, 14, 10, 12, 12, 14, 4, 6, 6, 8, 12, 6, 16, 18, 18, 20, 2, 0, 0, 22, 18, 0]}, "tx/fire_2": {"type": "mesh", "hull": 12, "width": 83, "height": 177, "uvs": [1, 0.18967, 1, 0.36599, 1, 0.54954, 1, 0.75011, 1, 1, 0, 1, 0, 0.69054, 0, 0.50938, 0, 0.33892, 0, 0.18301, 0, 0, 1, 0], "triangles": [5, 3, 4, 5, 6, 3, 6, 2, 3, 6, 7, 2, 7, 1, 2, 7, 8, 1, 8, 0, 1, 8, 9, 0, 9, 11, 0, 9, 10, 11], "vertices": [1, 70, 98.35, -84.09, 1, 2, 70, 46.22, -82.21, 0.99661, 69, 157.57, -81.42, 0.00339, 2, 70, -8.05, -80.26, 0.76672, 69, 103.27, -81.84, 0.23328, 2, 70, -67.35, -78.12, 0.22571, 69, 43.93, -82.3, 0.77429, 2, 70, -141.23, -75.46, 0.01543, 69, -29.99, -82.88, 0.98457, 1, 69, -31.31, 85.79, 1, 1, 69, 60.24, 86.5, 1, 2, 70, 9.9, 87.88, 0.13826, 69, 113.83, 86.92, 0.86174, 2, 70, 60.3, 86.07, 0.5978, 69, 164.26, 87.31, 0.4022, 2, 70, 106.4, 84.4, 0.88785, 69, 210.39, 87.67, 0.11215, 2, 70, 160.5, 82.45, 0.98158, 69, 264.53, 88.1, 0.01842, 1, 70, 154.43, -86.11, 1], "edges": [8, 10, 20, 22, 16, 2, 2, 4, 14, 16, 4, 14, 10, 12, 12, 14, 4, 6, 6, 8, 12, 6, 16, 18, 18, 20, 2, 0, 0, 22, 18, 0]}, "tx/fire_3": {"type": "mesh", "hull": 12, "width": 83, "height": 177, "uvs": [1, 0.18967, 1, 0.36599, 1, 0.54954, 1, 0.75011, 1, 1, 0, 1, 0, 0.69054, 0, 0.50938, 0, 0.33892, 0, 0.18301, 0, 0, 1, 0], "triangles": [5, 3, 4, 5, 6, 3, 6, 2, 3, 6, 7, 2, 7, 1, 2, 7, 8, 1, 8, 0, 1, 8, 9, 0, 9, 11, 0, 9, 10, 11], "vertices": [1, 70, 98.35, -84.09, 1, 2, 70, 46.22, -82.21, 0.99661, 69, 157.57, -81.42, 0.00339, 2, 70, -8.05, -80.26, 0.76672, 69, 103.27, -81.84, 0.23328, 2, 70, -67.35, -78.12, 0.22571, 69, 43.93, -82.3, 0.77429, 2, 70, -141.23, -75.46, 0.01543, 69, -29.99, -82.88, 0.98457, 1, 69, -31.31, 85.79, 1, 1, 69, 60.24, 86.5, 1, 2, 70, 9.9, 87.88, 0.13826, 69, 113.83, 86.92, 0.86174, 2, 70, 60.3, 86.07, 0.5978, 69, 164.26, 87.31, 0.4022, 2, 70, 106.4, 84.4, 0.88785, 69, 210.39, 87.67, 0.11215, 2, 70, 160.5, 82.45, 0.98158, 69, 264.53, 88.1, 0.01842, 1, 70, 154.43, -86.11, 1], "edges": [8, 10, 20, 22, 16, 2, 2, 4, 14, 16, 4, 14, 10, 12, 12, 14, 4, 6, 6, 8, 12, 6, 16, 18, 18, 20, 2, 0, 0, 22, 18, 0]}, "tx/fire_4": {"type": "mesh", "hull": 12, "width": 83, "height": 177, "uvs": [1, 0.18967, 1, 0.36599, 1, 0.54954, 1, 0.75011, 1, 1, 0, 1, 0, 0.69054, 0, 0.50938, 0, 0.33892, 0, 0.18301, 0, 0, 1, 0], "triangles": [5, 3, 4, 5, 6, 3, 6, 2, 3, 6, 7, 2, 7, 1, 2, 7, 8, 1, 8, 0, 1, 8, 9, 0, 9, 11, 0, 9, 10, 11], "vertices": [1, 70, 98.35, -84.09, 1, 2, 70, 46.22, -82.21, 0.99661, 69, 157.57, -81.42, 0.00339, 2, 70, -8.05, -80.26, 0.76672, 69, 103.27, -81.84, 0.23328, 2, 70, -67.35, -78.12, 0.22571, 69, 43.93, -82.3, 0.77429, 2, 70, -141.23, -75.46, 0.01543, 69, -29.99, -82.88, 0.98457, 1, 69, -31.31, 85.79, 1, 1, 69, 60.24, 86.5, 1, 2, 70, 9.9, 87.88, 0.13826, 69, 113.83, 86.92, 0.86174, 2, 70, 60.3, 86.07, 0.5978, 69, 164.26, 87.31, 0.4022, 2, 70, 106.4, 84.4, 0.88785, 69, 210.39, 87.67, 0.11215, 2, 70, 160.5, 82.45, 0.98158, 69, 264.53, 88.1, 0.01842, 1, 70, 154.43, -86.11, 1], "edges": [8, 10, 20, 22, 16, 2, 2, 4, 14, 16, 4, 14, 10, 12, 12, 14, 4, 6, 6, 8, 12, 6, 16, 18, 18, 20, 2, 0, 0, 22, 18, 0]}, "tx/fire_5": {"type": "mesh", "hull": 12, "width": 83, "height": 177, "uvs": [1, 0.18967, 1, 0.36599, 1, 0.54954, 1, 0.75011, 1, 1, 0, 1, 0, 0.69054, 0, 0.50938, 0, 0.33892, 0, 0.18301, 0, 0, 1, 0], "triangles": [5, 3, 4, 5, 6, 3, 6, 2, 3, 6, 7, 2, 7, 1, 2, 7, 8, 1, 8, 0, 1, 8, 9, 0, 9, 11, 0, 9, 10, 11], "vertices": [1, 70, 98.35, -84.09, 1, 2, 70, 46.22, -82.21, 0.99661, 69, 157.57, -81.42, 0.00339, 2, 70, -8.05, -80.26, 0.76672, 69, 103.27, -81.84, 0.23328, 2, 70, -67.35, -78.12, 0.22571, 69, 43.93, -82.3, 0.77429, 2, 70, -141.23, -75.46, 0.01543, 69, -29.99, -82.88, 0.98457, 1, 69, -31.31, 85.79, 1, 1, 69, 60.24, 86.5, 1, 2, 70, 9.9, 87.88, 0.13826, 69, 113.83, 86.92, 0.86174, 2, 70, 60.3, 86.07, 0.5978, 69, 164.26, 87.31, 0.4022, 2, 70, 106.4, 84.4, 0.88785, 69, 210.39, 87.67, 0.11215, 2, 70, 160.5, 82.45, 0.98158, 69, 264.53, 88.1, 0.01842, 1, 70, 154.43, -86.11, 1], "edges": [8, 10, 20, 22, 16, 2, 2, 4, 14, 16, 4, 14, 10, 12, 12, 14, 4, 6, 6, 8, 12, 6, 16, 18, 18, 20, 2, 0, 0, 22, 18, 0]}, "tx/fire_6": {"type": "mesh", "hull": 12, "width": 83, "height": 177, "uvs": [1, 0.18967, 1, 0.36599, 1, 0.54954, 1, 0.75011, 1, 1, 0, 1, 0, 0.69054, 0, 0.50938, 0, 0.33892, 0, 0.18301, 0, 0, 1, 0], "triangles": [5, 3, 4, 5, 6, 3, 6, 2, 3, 6, 7, 2, 7, 1, 2, 7, 8, 1, 8, 0, 1, 8, 9, 0, 9, 11, 0, 9, 10, 11], "vertices": [1, 70, 98.35, -84.09, 1, 2, 70, 46.22, -82.21, 0.99661, 69, 157.57, -81.42, 0.00339, 2, 70, -8.05, -80.26, 0.76672, 69, 103.27, -81.84, 0.23328, 2, 70, -67.35, -78.12, 0.22571, 69, 43.93, -82.3, 0.77429, 2, 70, -141.23, -75.46, 0.01543, 69, -29.99, -82.88, 0.98457, 1, 69, -31.31, 85.79, 1, 1, 69, 60.24, 86.5, 1, 2, 70, 9.9, 87.88, 0.13826, 69, 113.83, 86.92, 0.86174, 2, 70, 60.3, 86.07, 0.5978, 69, 164.26, 87.31, 0.4022, 2, 70, 106.4, 84.4, 0.88785, 69, 210.39, 87.67, 0.11215, 2, 70, 160.5, 82.45, 0.98158, 69, 264.53, 88.1, 0.01842, 1, 70, 154.43, -86.11, 1], "edges": [8, 10, 20, 22, 16, 2, 2, 4, 14, 16, 4, 14, 10, 12, 12, 14, 4, 6, 6, 8, 12, 6, 16, 18, 18, 20, 2, 0, 0, 22, 18, 0]}, "tx/fire_7": {"type": "mesh", "hull": 12, "width": 83, "height": 177, "uvs": [1, 0.18967, 1, 0.36599, 1, 0.54954, 1, 0.75011, 1, 1, 0, 1, 0, 0.69054, 0, 0.50938, 0, 0.33892, 0, 0.18301, 0, 0, 1, 0], "triangles": [5, 3, 4, 5, 6, 3, 6, 2, 3, 6, 7, 2, 7, 1, 2, 7, 8, 1, 8, 0, 1, 8, 9, 0, 9, 11, 0, 9, 10, 11], "vertices": [1, 70, 98.35, -84.09, 1, 2, 70, 46.22, -82.21, 0.99661, 69, 157.57, -81.42, 0.00339, 2, 70, -8.05, -80.26, 0.76672, 69, 103.27, -81.84, 0.23328, 2, 70, -67.35, -78.12, 0.22571, 69, 43.93, -82.3, 0.77429, 2, 70, -141.23, -75.46, 0.01543, 69, -29.99, -82.88, 0.98457, 1, 69, -31.31, 85.79, 1, 1, 69, 60.24, 86.5, 1, 2, 70, 9.9, 87.88, 0.13826, 69, 113.83, 86.92, 0.86174, 2, 70, 60.3, 86.07, 0.5978, 69, 164.26, 87.31, 0.4022, 2, 70, 106.4, 84.4, 0.88785, 69, 210.39, 87.67, 0.11215, 2, 70, 160.5, 82.45, 0.98158, 69, 264.53, 88.1, 0.01842, 1, 70, 154.43, -86.11, 1], "edges": [8, 10, 20, 22, 16, 2, 2, 4, 14, 16, 4, 14, 10, 12, 12, 14, 4, 6, 6, 8, 12, 6, 16, 18, 18, 20, 2, 0, 0, 22, 18, 0]}, "tx/fire_8": {"type": "mesh", "hull": 12, "width": 83, "height": 177, "uvs": [1, 0.18967, 1, 0.36599, 1, 0.54954, 1, 0.75011, 1, 1, 0, 1, 0, 0.69054, 0, 0.50938, 0, 0.33892, 0, 0.18301, 0, 0, 1, 0], "triangles": [5, 3, 4, 5, 6, 3, 6, 2, 3, 6, 7, 2, 7, 1, 2, 7, 8, 1, 8, 0, 1, 8, 9, 0, 9, 11, 0, 9, 10, 11], "vertices": [1, 70, 98.35, -84.09, 1, 2, 70, 46.22, -82.21, 0.99661, 69, 157.57, -81.42, 0.00339, 2, 70, -8.05, -80.26, 0.76672, 69, 103.27, -81.84, 0.23328, 2, 70, -67.35, -78.12, 0.22571, 69, 43.93, -82.3, 0.77429, 2, 70, -141.23, -75.46, 0.01543, 69, -29.99, -82.88, 0.98457, 1, 69, -31.31, 85.79, 1, 1, 69, 60.24, 86.5, 1, 2, 70, 9.9, 87.88, 0.13826, 69, 113.83, 86.92, 0.86174, 2, 70, 60.3, 86.07, 0.5978, 69, 164.26, 87.31, 0.4022, 2, 70, 106.4, 84.4, 0.88785, 69, 210.39, 87.67, 0.11215, 2, 70, 160.5, 82.45, 0.98158, 69, 264.53, 88.1, 0.01842, 1, 70, 154.43, -86.11, 1], "edges": [8, 10, 20, 22, 16, 2, 2, 4, 14, 16, 4, 14, 10, 12, 12, 14, 4, 6, 6, 8, 12, 6, 16, 18, 18, 20, 2, 0, 0, 22, 18, 0]}, "tx/fire_9": {"type": "mesh", "hull": 12, "width": 83, "height": 177, "uvs": [1, 0.18967, 1, 0.36599, 1, 0.54954, 1, 0.75011, 1, 1, 0, 1, 0, 0.69054, 0, 0.50938, 0, 0.33892, 0, 0.18301, 0, 0, 1, 0], "triangles": [5, 3, 4, 5, 6, 3, 6, 2, 3, 6, 7, 2, 7, 1, 2, 7, 8, 1, 8, 0, 1, 8, 9, 0, 9, 11, 0, 9, 10, 11], "vertices": [1, 70, 98.35, -84.09, 1, 2, 70, 46.22, -82.21, 0.99661, 69, 157.57, -81.42, 0.00339, 2, 70, -8.05, -80.26, 0.76672, 69, 103.27, -81.84, 0.23328, 2, 70, -67.35, -78.12, 0.22571, 69, 43.93, -82.3, 0.77429, 2, 70, -141.23, -75.46, 0.01543, 69, -29.99, -82.88, 0.98457, 1, 69, -31.31, 85.79, 1, 1, 69, 60.24, 86.5, 1, 2, 70, 9.9, 87.88, 0.13826, 69, 113.83, 86.92, 0.86174, 2, 70, 60.3, 86.07, 0.5978, 69, 164.26, 87.31, 0.4022, 2, 70, 106.4, 84.4, 0.88785, 69, 210.39, 87.67, 0.11215, 2, 70, 160.5, 82.45, 0.98158, 69, 264.53, 88.1, 0.01842, 1, 70, 154.43, -86.11, 1], "edges": [8, 10, 20, 22, 16, 2, 2, 4, 14, 16, 4, 14, 10, 12, 12, 14, 4, 6, 6, 8, 12, 6, 16, 18, 18, 20, 2, 0, 0, 22, 18, 0]}, "tx/fire_10": {"type": "mesh", "hull": 12, "width": 83, "height": 177, "uvs": [1, 0.18967, 1, 0.36599, 1, 0.54954, 1, 0.75011, 1, 1, 0, 1, 0, 0.69054, 0, 0.50938, 0, 0.33892, 0, 0.18301, 0, 0, 1, 0], "triangles": [5, 3, 4, 5, 6, 3, 6, 2, 3, 6, 7, 2, 7, 1, 2, 7, 8, 1, 8, 0, 1, 8, 9, 0, 9, 11, 0, 9, 10, 11], "vertices": [1, 70, 98.35, -84.09, 1, 2, 70, 46.22, -82.21, 0.99661, 69, 157.57, -81.42, 0.00339, 2, 70, -8.05, -80.26, 0.76672, 69, 103.27, -81.84, 0.23328, 2, 70, -67.35, -78.12, 0.22571, 69, 43.93, -82.3, 0.77429, 2, 70, -141.23, -75.46, 0.01543, 69, -29.99, -82.88, 0.98457, 1, 69, -31.31, 85.79, 1, 1, 69, 60.24, 86.5, 1, 2, 70, 9.9, 87.88, 0.13826, 69, 113.83, 86.92, 0.86174, 2, 70, 60.3, 86.07, 0.5978, 69, 164.26, 87.31, 0.4022, 2, 70, 106.4, 84.4, 0.88785, 69, 210.39, 87.67, 0.11215, 2, 70, 160.5, 82.45, 0.98158, 69, 264.53, 88.1, 0.01842, 1, 70, 154.43, -86.11, 1], "edges": [8, 10, 20, 22, 16, 2, 2, 4, 14, 16, 4, 14, 10, 12, 12, 14, 4, 6, 6, 8, 12, 6, 16, 18, 18, 20, 2, 0, 0, 22, 18, 0]}, "tx/fire_11": {"type": "mesh", "hull": 12, "width": 83, "height": 177, "uvs": [1, 0.18967, 1, 0.36599, 1, 0.54954, 1, 0.75011, 1, 1, 0, 1, 0, 0.69054, 0, 0.50938, 0, 0.33892, 0, 0.18301, 0, 0, 1, 0], "triangles": [5, 3, 4, 5, 6, 3, 6, 2, 3, 6, 7, 2, 7, 1, 2, 7, 8, 1, 8, 0, 1, 8, 9, 0, 9, 11, 0, 9, 10, 11], "vertices": [1, 70, 98.35, -84.09, 1, 2, 70, 46.22, -82.21, 0.99661, 69, 157.57, -81.42, 0.00339, 2, 70, -8.05, -80.26, 0.76672, 69, 103.27, -81.84, 0.23328, 2, 70, -67.35, -78.12, 0.22571, 69, 43.93, -82.3, 0.77429, 2, 70, -141.23, -75.46, 0.01543, 69, -29.99, -82.88, 0.98457, 1, 69, -31.31, 85.79, 1, 1, 69, 60.24, 86.5, 1, 2, 70, 9.9, 87.88, 0.13826, 69, 113.83, 86.92, 0.86174, 2, 70, 60.3, 86.07, 0.5978, 69, 164.26, 87.31, 0.4022, 2, 70, 106.4, 84.4, 0.88785, 69, 210.39, 87.67, 0.11215, 2, 70, 160.5, 82.45, 0.98158, 69, 264.53, 88.1, 0.01842, 1, 70, 154.43, -86.11, 1], "edges": [8, 10, 20, 22, 16, 2, 2, 4, 14, 16, 4, 14, 10, 12, 12, 14, 4, 6, 6, 8, 12, 6, 16, 18, 18, 20, 2, 0, 0, 22, 18, 0]}, "tx/fire_12": {"type": "mesh", "hull": 12, "width": 83, "height": 177, "uvs": [1, 0.18967, 1, 0.36599, 1, 0.54954, 1, 0.75011, 1, 1, 0, 1, 0, 0.69054, 0, 0.50938, 0, 0.33892, 0, 0.18301, 0, 0, 1, 0], "triangles": [7, 1, 2, 7, 8, 1, 8, 0, 1, 8, 9, 0, 9, 11, 0, 9, 10, 11, 5, 3, 4, 5, 6, 3, 6, 2, 3, 6, 7, 2], "vertices": [2, 69, 209.73, -81.01, 0.02888, 70, 98.35, -84.09, 0.97112, 2, 69, 157.57, -81.42, 0.15972, 70, 46.22, -82.21, 0.84028, 2, 69, 103.27, -81.84, 0.46818, 70, -8.05, -80.26, 0.53182, 2, 69, 43.93, -82.3, 0.74803, 70, -67.35, -78.12, 0.25197, 2, 69, -29.99, -82.88, 0.91962, 70, -141.23, -75.46, 0.08038, 2, 69, -31.31, 85.79, 0.99486, 70, -135.15, 93.11, 0.00514, 2, 69, 60.24, 86.5, 0.90901, 70, -43.66, 89.81, 0.09099, 2, 69, 113.83, 86.92, 0.62431, 70, 9.9, 87.88, 0.37569, 2, 69, 164.26, 87.31, 0.34487, 70, 60.3, 86.07, 0.65513, 2, 69, 210.39, 87.67, 0.13319, 70, 106.4, 84.4, 0.86681, 2, 69, 264.53, 88.1, 0.04352, 70, 160.5, 82.45, 0.95648, 2, 69, 265.84, -80.57, 0.00614, 70, 154.43, -86.11, 0.99386], "edges": [8, 10, 20, 22, 16, 2, 2, 4, 14, 16, 4, 14, 10, 12, 12, 14, 4, 6, 6, 8, 12, 6, 16, 18, 18, 20, 2, 0, 0, 22, 18, 0]}, "tx/fire_13": {"type": "mesh", "hull": 12, "width": 83, "height": 177, "uvs": [1, 0.18967, 1, 0.36599, 1, 0.54954, 1, 0.75011, 1, 1, 0, 1, 0, 0.69054, 0, 0.50938, 0, 0.33892, 0, 0.18301, 0, 0, 1, 0], "triangles": [5, 3, 4, 5, 6, 3, 6, 2, 3, 6, 7, 2, 7, 1, 2, 7, 8, 1, 8, 0, 1, 8, 9, 0, 9, 11, 0, 9, 10, 11], "vertices": [1, 70, 98.35, -84.09, 1, 2, 70, 46.22, -82.21, 0.99661, 69, 157.57, -81.42, 0.00339, 2, 70, -8.05, -80.26, 0.76672, 69, 103.27, -81.84, 0.23328, 2, 70, -67.35, -78.12, 0.22571, 69, 43.93, -82.3, 0.77429, 2, 70, -141.23, -75.46, 0.01543, 69, -29.99, -82.88, 0.98457, 1, 69, -31.31, 85.79, 1, 1, 69, 60.24, 86.5, 1, 2, 70, 9.9, 87.88, 0.13826, 69, 113.83, 86.92, 0.86174, 2, 70, 60.3, 86.07, 0.5978, 69, 164.26, 87.31, 0.4022, 2, 70, 106.4, 84.4, 0.88785, 69, 210.39, 87.67, 0.11215, 2, 70, 160.5, 82.45, 0.98158, 69, 264.53, 88.1, 0.01842, 1, 70, 154.43, -86.11, 1], "edges": [8, 10, 20, 22, 16, 2, 2, 4, 14, 16, 4, 14, 10, 12, 12, 14, 4, 6, 6, 8, 12, 6, 16, 18, 18, 20, 2, 0, 0, 22, 18, 0]}, "tx/fire_14": {"type": "mesh", "hull": 12, "width": 83, "height": 177, "uvs": [1, 0.18967, 1, 0.36599, 1, 0.54954, 1, 0.75011, 1, 1, 0, 1, 0, 0.69054, 0, 0.50938, 0, 0.33892, 0, 0.18301, 0, 0, 1, 0], "triangles": [5, 3, 4, 5, 6, 3, 6, 2, 3, 6, 7, 2, 7, 1, 2, 7, 8, 1, 8, 0, 1, 8, 9, 0, 9, 11, 0, 9, 10, 11], "vertices": [1, 70, 98.35, -84.09, 1, 2, 70, 46.22, -82.21, 0.99661, 69, 157.57, -81.42, 0.00339, 2, 70, -8.05, -80.26, 0.76672, 69, 103.27, -81.84, 0.23328, 2, 70, -67.35, -78.12, 0.22571, 69, 43.93, -82.3, 0.77429, 2, 70, -141.23, -75.46, 0.01543, 69, -29.99, -82.88, 0.98457, 1, 69, -31.31, 85.79, 1, 1, 69, 60.24, 86.5, 1, 2, 70, 9.9, 87.88, 0.13826, 69, 113.83, 86.92, 0.86174, 2, 70, 60.3, 86.07, 0.5978, 69, 164.26, 87.31, 0.4022, 2, 70, 106.4, 84.4, 0.88785, 69, 210.39, 87.67, 0.11215, 2, 70, 160.5, 82.45, 0.98158, 69, 264.53, 88.1, 0.01842, 1, 70, 154.43, -86.11, 1], "edges": [8, 10, 20, 22, 16, 2, 2, 4, 14, 16, 4, 14, 10, 12, 12, 14, 4, 6, 6, 8, 12, 6, 16, 18, 18, 20, 2, 0, 0, 22, 18, 0]}, "tx/fire_15": {"type": "mesh", "hull": 12, "width": 83, "height": 177, "uvs": [1, 0.18967, 1, 0.36599, 1, 0.54954, 1, 0.75011, 1, 1, 0, 1, 0, 0.69054, 0, 0.50938, 0, 0.33892, 0, 0.18301, 0, 0, 1, 0], "triangles": [5, 3, 4, 5, 6, 3, 6, 2, 3, 6, 7, 2, 7, 1, 2, 7, 8, 1, 8, 0, 1, 8, 9, 0, 9, 11, 0, 9, 10, 11], "vertices": [1, 70, 98.35, -84.09, 1, 2, 70, 46.22, -82.21, 0.99661, 69, 157.57, -81.42, 0.00339, 2, 70, -8.05, -80.26, 0.76672, 69, 103.27, -81.84, 0.23328, 2, 70, -67.35, -78.12, 0.22571, 69, 43.93, -82.3, 0.77429, 2, 70, -141.23, -75.46, 0.01543, 69, -29.99, -82.88, 0.98457, 1, 69, -31.31, 85.79, 1, 1, 69, 60.24, 86.5, 1, 2, 70, 9.9, 87.88, 0.13826, 69, 113.83, 86.92, 0.86174, 2, 70, 60.3, 86.07, 0.5978, 69, 164.26, 87.31, 0.4022, 2, 70, 106.4, 84.4, 0.88785, 69, 210.39, 87.67, 0.11215, 2, 70, 160.5, 82.45, 0.98158, 69, 264.53, 88.1, 0.01842, 1, 70, 154.43, -86.11, 1], "edges": [8, 10, 20, 22, 16, 2, 2, 4, 14, 16, 4, 14, 10, 12, 12, 14, 4, 6, 6, 8, 12, 6, 16, 18, 18, 20, 2, 0, 0, 22, 18, 0]}}, "fire2": {"tx/fire_0": {"type": "mesh", "hull": 12, "width": 83, "height": 177, "uvs": [1, 0.18967, 1, 0.36599, 1, 0.54954, 1, 0.75011, 1, 1, 0, 1, 0, 0.69054, 0, 0.50938, 0, 0.33892, 0, 0.18301, 0, 0, 1, 0], "triangles": [5, 3, 4, 5, 6, 3, 6, 2, 3, 6, 7, 2, 7, 1, 2, 7, 8, 1, 8, 0, 1, 8, 9, 0, 9, 11, 0, 9, 10, 11], "vertices": [1, 70, 98.35, -84.09, 1, 2, 70, 46.22, -82.21, 0.99661, 69, 157.57, -81.42, 0.00339, 2, 70, -8.05, -80.26, 0.76672, 69, 103.27, -81.84, 0.23328, 2, 70, -67.35, -78.12, 0.22571, 69, 43.93, -82.3, 0.77429, 2, 70, -141.23, -75.46, 0.01543, 69, -29.99, -82.88, 0.98457, 1, 69, -31.31, 85.79, 1, 1, 69, 60.24, 86.5, 1, 2, 70, 9.9, 87.88, 0.13826, 69, 113.83, 86.92, 0.86174, 2, 70, 60.3, 86.07, 0.5978, 69, 164.26, 87.31, 0.4022, 2, 70, 106.4, 84.4, 0.88785, 69, 210.39, 87.67, 0.11215, 2, 70, 160.5, 82.45, 0.98158, 69, 264.53, 88.1, 0.01842, 1, 70, 154.43, -86.11, 1], "edges": [8, 10, 20, 22, 16, 2, 2, 4, 14, 16, 4, 14, 10, 12, 12, 14, 4, 6, 6, 8, 12, 6, 16, 18, 18, 20, 2, 0, 0, 22, 18, 0]}, "tx/fire_1": {"type": "mesh", "hull": 12, "width": 83, "height": 177, "uvs": [1, 0.18967, 1, 0.36599, 1, 0.54954, 1, 0.75011, 1, 1, 0, 1, 0, 0.69054, 0, 0.50938, 0, 0.33892, 0, 0.18301, 0, 0, 1, 0], "triangles": [5, 3, 4, 5, 6, 3, 6, 2, 3, 6, 7, 2, 7, 1, 2, 7, 8, 1, 8, 0, 1, 8, 9, 0, 9, 11, 0, 9, 10, 11], "vertices": [1, 70, 98.35, -84.09, 1, 2, 70, 46.22, -82.21, 0.99661, 69, 157.57, -81.42, 0.00339, 2, 70, -8.05, -80.26, 0.76672, 69, 103.27, -81.84, 0.23328, 2, 70, -67.35, -78.12, 0.22571, 69, 43.93, -82.3, 0.77429, 2, 70, -141.23, -75.46, 0.01543, 69, -29.99, -82.88, 0.98457, 1, 69, -31.31, 85.79, 1, 1, 69, 60.24, 86.5, 1, 2, 70, 9.9, 87.88, 0.13826, 69, 113.83, 86.92, 0.86174, 2, 70, 60.3, 86.07, 0.5978, 69, 164.26, 87.31, 0.4022, 2, 70, 106.4, 84.4, 0.88785, 69, 210.39, 87.67, 0.11215, 2, 70, 160.5, 82.45, 0.98158, 69, 264.53, 88.1, 0.01842, 1, 70, 154.43, -86.11, 1], "edges": [8, 10, 20, 22, 16, 2, 2, 4, 14, 16, 4, 14, 10, 12, 12, 14, 4, 6, 6, 8, 12, 6, 16, 18, 18, 20, 2, 0, 0, 22, 18, 0]}, "tx/fire_2": {"type": "mesh", "hull": 12, "width": 83, "height": 177, "uvs": [1, 0.18967, 1, 0.36599, 1, 0.54954, 1, 0.75011, 1, 1, 0, 1, 0, 0.69054, 0, 0.50938, 0, 0.33892, 0, 0.18301, 0, 0, 1, 0], "triangles": [5, 3, 4, 5, 6, 3, 6, 2, 3, 6, 7, 2, 7, 1, 2, 7, 8, 1, 8, 0, 1, 8, 9, 0, 9, 11, 0, 9, 10, 11], "vertices": [1, 70, 98.35, -84.09, 1, 2, 70, 46.22, -82.21, 0.99661, 69, 157.57, -81.42, 0.00339, 2, 70, -8.05, -80.26, 0.76672, 69, 103.27, -81.84, 0.23328, 2, 70, -67.35, -78.12, 0.22571, 69, 43.93, -82.3, 0.77429, 2, 70, -141.23, -75.46, 0.01543, 69, -29.99, -82.88, 0.98457, 1, 69, -31.31, 85.79, 1, 1, 69, 60.24, 86.5, 1, 2, 70, 9.9, 87.88, 0.13826, 69, 113.83, 86.92, 0.86174, 2, 70, 60.3, 86.07, 0.5978, 69, 164.26, 87.31, 0.4022, 2, 70, 106.4, 84.4, 0.88785, 69, 210.39, 87.67, 0.11215, 2, 70, 160.5, 82.45, 0.98158, 69, 264.53, 88.1, 0.01842, 1, 70, 154.43, -86.11, 1], "edges": [8, 10, 20, 22, 16, 2, 2, 4, 14, 16, 4, 14, 10, 12, 12, 14, 4, 6, 6, 8, 12, 6, 16, 18, 18, 20, 2, 0, 0, 22, 18, 0]}, "tx/fire_3": {"type": "mesh", "hull": 12, "width": 83, "height": 177, "uvs": [1, 0.18967, 1, 0.36599, 1, 0.54954, 1, 0.75011, 1, 1, 0, 1, 0, 0.69054, 0, 0.50938, 0, 0.33892, 0, 0.18301, 0, 0, 1, 0], "triangles": [5, 3, 4, 5, 6, 3, 6, 2, 3, 6, 7, 2, 7, 1, 2, 7, 8, 1, 8, 0, 1, 8, 9, 0, 9, 11, 0, 9, 10, 11], "vertices": [1, 70, 98.35, -84.09, 1, 2, 70, 46.22, -82.21, 0.99661, 69, 157.57, -81.42, 0.00339, 2, 70, -8.05, -80.26, 0.76672, 69, 103.27, -81.84, 0.23328, 2, 70, -67.35, -78.12, 0.22571, 69, 43.93, -82.3, 0.77429, 2, 70, -141.23, -75.46, 0.01543, 69, -29.99, -82.88, 0.98457, 1, 69, -31.31, 85.79, 1, 1, 69, 60.24, 86.5, 1, 2, 70, 9.9, 87.88, 0.13826, 69, 113.83, 86.92, 0.86174, 2, 70, 60.3, 86.07, 0.5978, 69, 164.26, 87.31, 0.4022, 2, 70, 106.4, 84.4, 0.88785, 69, 210.39, 87.67, 0.11215, 2, 70, 160.5, 82.45, 0.98158, 69, 264.53, 88.1, 0.01842, 1, 70, 154.43, -86.11, 1], "edges": [8, 10, 20, 22, 16, 2, 2, 4, 14, 16, 4, 14, 10, 12, 12, 14, 4, 6, 6, 8, 12, 6, 16, 18, 18, 20, 2, 0, 0, 22, 18, 0]}, "tx/fire_4": {"type": "mesh", "hull": 12, "width": 83, "height": 177, "uvs": [1, 0.18967, 1, 0.36599, 1, 0.54954, 1, 0.75011, 1, 1, 0, 1, 0, 0.69054, 0, 0.50938, 0, 0.33892, 0, 0.18301, 0, 0, 1, 0], "triangles": [5, 3, 4, 5, 6, 3, 6, 2, 3, 6, 7, 2, 7, 1, 2, 7, 8, 1, 8, 0, 1, 8, 9, 0, 9, 11, 0, 9, 10, 11], "vertices": [1, 70, 98.35, -84.09, 1, 2, 70, 46.22, -82.21, 0.99661, 69, 157.57, -81.42, 0.00339, 2, 70, -8.05, -80.26, 0.76672, 69, 103.27, -81.84, 0.23328, 2, 70, -67.35, -78.12, 0.22571, 69, 43.93, -82.3, 0.77429, 2, 70, -141.23, -75.46, 0.01543, 69, -29.99, -82.88, 0.98457, 1, 69, -31.31, 85.79, 1, 1, 69, 60.24, 86.5, 1, 2, 70, 9.9, 87.88, 0.13826, 69, 113.83, 86.92, 0.86174, 2, 70, 60.3, 86.07, 0.5978, 69, 164.26, 87.31, 0.4022, 2, 70, 106.4, 84.4, 0.88785, 69, 210.39, 87.67, 0.11215, 2, 70, 160.5, 82.45, 0.98158, 69, 264.53, 88.1, 0.01842, 1, 70, 154.43, -86.11, 1], "edges": [8, 10, 20, 22, 16, 2, 2, 4, 14, 16, 4, 14, 10, 12, 12, 14, 4, 6, 6, 8, 12, 6, 16, 18, 18, 20, 2, 0, 0, 22, 18, 0]}, "tx/fire_5": {"type": "mesh", "hull": 12, "width": 83, "height": 177, "uvs": [1, 0.18967, 1, 0.36599, 1, 0.54954, 1, 0.75011, 1, 1, 0, 1, 0, 0.69054, 0, 0.50938, 0, 0.33892, 0, 0.18301, 0, 0, 1, 0], "triangles": [5, 3, 4, 5, 6, 3, 6, 2, 3, 6, 7, 2, 7, 1, 2, 7, 8, 1, 8, 0, 1, 8, 9, 0, 9, 11, 0, 9, 10, 11], "vertices": [1, 70, 98.35, -84.09, 1, 2, 70, 46.22, -82.21, 0.99661, 69, 157.57, -81.42, 0.00339, 2, 70, -8.05, -80.26, 0.76672, 69, 103.27, -81.84, 0.23328, 2, 70, -67.35, -78.12, 0.22571, 69, 43.93, -82.3, 0.77429, 2, 70, -141.23, -75.46, 0.01543, 69, -29.99, -82.88, 0.98457, 1, 69, -31.31, 85.79, 1, 1, 69, 60.24, 86.5, 1, 2, 70, 9.9, 87.88, 0.13826, 69, 113.83, 86.92, 0.86174, 2, 70, 60.3, 86.07, 0.5978, 69, 164.26, 87.31, 0.4022, 2, 70, 106.4, 84.4, 0.88785, 69, 210.39, 87.67, 0.11215, 2, 70, 160.5, 82.45, 0.98158, 69, 264.53, 88.1, 0.01842, 1, 70, 154.43, -86.11, 1], "edges": [8, 10, 20, 22, 16, 2, 2, 4, 14, 16, 4, 14, 10, 12, 12, 14, 4, 6, 6, 8, 12, 6, 16, 18, 18, 20, 2, 0, 0, 22, 18, 0]}, "tx/fire_6": {"type": "mesh", "hull": 12, "width": 83, "height": 177, "uvs": [1, 0.18967, 1, 0.36599, 1, 0.54954, 1, 0.75011, 1, 1, 0, 1, 0, 0.69054, 0, 0.50938, 0, 0.33892, 0, 0.18301, 0, 0, 1, 0], "triangles": [5, 3, 4, 5, 6, 3, 6, 2, 3, 6, 7, 2, 7, 1, 2, 7, 8, 1, 8, 0, 1, 8, 9, 0, 9, 11, 0, 9, 10, 11], "vertices": [1, 70, 98.35, -84.09, 1, 2, 70, 46.22, -82.21, 0.99661, 69, 157.57, -81.42, 0.00339, 2, 70, -8.05, -80.26, 0.76672, 69, 103.27, -81.84, 0.23328, 2, 70, -67.35, -78.12, 0.22571, 69, 43.93, -82.3, 0.77429, 2, 70, -141.23, -75.46, 0.01543, 69, -29.99, -82.88, 0.98457, 1, 69, -31.31, 85.79, 1, 1, 69, 60.24, 86.5, 1, 2, 70, 9.9, 87.88, 0.13826, 69, 113.83, 86.92, 0.86174, 2, 70, 60.3, 86.07, 0.5978, 69, 164.26, 87.31, 0.4022, 2, 70, 106.4, 84.4, 0.88785, 69, 210.39, 87.67, 0.11215, 2, 70, 160.5, 82.45, 0.98158, 69, 264.53, 88.1, 0.01842, 1, 70, 154.43, -86.11, 1], "edges": [8, 10, 20, 22, 16, 2, 2, 4, 14, 16, 4, 14, 10, 12, 12, 14, 4, 6, 6, 8, 12, 6, 16, 18, 18, 20, 2, 0, 0, 22, 18, 0]}, "tx/fire_7": {"type": "mesh", "hull": 12, "width": 83, "height": 177, "uvs": [1, 0.18967, 1, 0.36599, 1, 0.54954, 1, 0.75011, 1, 1, 0, 1, 0, 0.69054, 0, 0.50938, 0, 0.33892, 0, 0.18301, 0, 0, 1, 0], "triangles": [5, 3, 4, 5, 6, 3, 6, 2, 3, 6, 7, 2, 7, 1, 2, 7, 8, 1, 8, 0, 1, 8, 9, 0, 9, 11, 0, 9, 10, 11], "vertices": [1, 70, 98.35, -84.09, 1, 2, 70, 46.22, -82.21, 0.99661, 69, 157.57, -81.42, 0.00339, 2, 70, -8.05, -80.26, 0.76672, 69, 103.27, -81.84, 0.23328, 2, 70, -67.35, -78.12, 0.22571, 69, 43.93, -82.3, 0.77429, 2, 70, -141.23, -75.46, 0.01543, 69, -29.99, -82.88, 0.98457, 1, 69, -31.31, 85.79, 1, 1, 69, 60.24, 86.5, 1, 2, 70, 9.9, 87.88, 0.13826, 69, 113.83, 86.92, 0.86174, 2, 70, 60.3, 86.07, 0.5978, 69, 164.26, 87.31, 0.4022, 2, 70, 106.4, 84.4, 0.88785, 69, 210.39, 87.67, 0.11215, 2, 70, 160.5, 82.45, 0.98158, 69, 264.53, 88.1, 0.01842, 1, 70, 154.43, -86.11, 1], "edges": [8, 10, 20, 22, 16, 2, 2, 4, 14, 16, 4, 14, 10, 12, 12, 14, 4, 6, 6, 8, 12, 6, 16, 18, 18, 20, 2, 0, 0, 22, 18, 0]}, "tx/fire_8": {"type": "mesh", "hull": 12, "width": 83, "height": 177, "uvs": [1, 0.18967, 1, 0.36599, 1, 0.54954, 1, 0.75011, 1, 1, 0, 1, 0, 0.69054, 0, 0.50938, 0, 0.33892, 0, 0.18301, 0, 0, 1, 0], "triangles": [5, 3, 4, 5, 6, 3, 6, 2, 3, 6, 7, 2, 7, 1, 2, 7, 8, 1, 8, 0, 1, 8, 9, 0, 9, 11, 0, 9, 10, 11], "vertices": [1, 70, 98.35, -84.09, 1, 2, 70, 46.22, -82.21, 0.99661, 69, 157.57, -81.42, 0.00339, 2, 70, -8.05, -80.26, 0.76672, 69, 103.27, -81.84, 0.23328, 2, 70, -67.35, -78.12, 0.22571, 69, 43.93, -82.3, 0.77429, 2, 70, -141.23, -75.46, 0.01543, 69, -29.99, -82.88, 0.98457, 1, 69, -31.31, 85.79, 1, 1, 69, 60.24, 86.5, 1, 2, 70, 9.9, 87.88, 0.13826, 69, 113.83, 86.92, 0.86174, 2, 70, 60.3, 86.07, 0.5978, 69, 164.26, 87.31, 0.4022, 2, 70, 106.4, 84.4, 0.88785, 69, 210.39, 87.67, 0.11215, 2, 70, 160.5, 82.45, 0.98158, 69, 264.53, 88.1, 0.01842, 1, 70, 154.43, -86.11, 1], "edges": [8, 10, 20, 22, 16, 2, 2, 4, 14, 16, 4, 14, 10, 12, 12, 14, 4, 6, 6, 8, 12, 6, 16, 18, 18, 20, 2, 0, 0, 22, 18, 0]}, "tx/fire_9": {"type": "mesh", "hull": 12, "width": 83, "height": 177, "uvs": [1, 0.18967, 1, 0.36599, 1, 0.54954, 1, 0.75011, 1, 1, 0, 1, 0, 0.69054, 0, 0.50938, 0, 0.33892, 0, 0.18301, 0, 0, 1, 0], "triangles": [5, 3, 4, 5, 6, 3, 6, 2, 3, 6, 7, 2, 7, 1, 2, 7, 8, 1, 8, 0, 1, 8, 9, 0, 9, 11, 0, 9, 10, 11], "vertices": [1, 70, 98.35, -84.09, 1, 2, 70, 46.22, -82.21, 0.99661, 69, 157.57, -81.42, 0.00339, 2, 70, -8.05, -80.26, 0.76672, 69, 103.27, -81.84, 0.23328, 2, 70, -67.35, -78.12, 0.22571, 69, 43.93, -82.3, 0.77429, 2, 70, -141.23, -75.46, 0.01543, 69, -29.99, -82.88, 0.98457, 1, 69, -31.31, 85.79, 1, 1, 69, 60.24, 86.5, 1, 2, 70, 9.9, 87.88, 0.13826, 69, 113.83, 86.92, 0.86174, 2, 70, 60.3, 86.07, 0.5978, 69, 164.26, 87.31, 0.4022, 2, 70, 106.4, 84.4, 0.88785, 69, 210.39, 87.67, 0.11215, 2, 70, 160.5, 82.45, 0.98158, 69, 264.53, 88.1, 0.01842, 1, 70, 154.43, -86.11, 1], "edges": [8, 10, 20, 22, 16, 2, 2, 4, 14, 16, 4, 14, 10, 12, 12, 14, 4, 6, 6, 8, 12, 6, 16, 18, 18, 20, 2, 0, 0, 22, 18, 0]}, "tx/fire_10": {"type": "mesh", "hull": 12, "width": 83, "height": 177, "uvs": [1, 0.18967, 1, 0.36599, 1, 0.54954, 1, 0.75011, 1, 1, 0, 1, 0, 0.69054, 0, 0.50938, 0, 0.33892, 0, 0.18301, 0, 0, 1, 0], "triangles": [5, 3, 4, 5, 6, 3, 6, 2, 3, 6, 7, 2, 7, 1, 2, 7, 8, 1, 8, 0, 1, 8, 9, 0, 9, 11, 0, 9, 10, 11], "vertices": [1, 70, 98.35, -84.09, 1, 2, 70, 46.22, -82.21, 0.99661, 69, 157.57, -81.42, 0.00339, 2, 70, -8.05, -80.26, 0.76672, 69, 103.27, -81.84, 0.23328, 2, 70, -67.35, -78.12, 0.22571, 69, 43.93, -82.3, 0.77429, 2, 70, -141.23, -75.46, 0.01543, 69, -29.99, -82.88, 0.98457, 1, 69, -31.31, 85.79, 1, 1, 69, 60.24, 86.5, 1, 2, 70, 9.9, 87.88, 0.13826, 69, 113.83, 86.92, 0.86174, 2, 70, 60.3, 86.07, 0.5978, 69, 164.26, 87.31, 0.4022, 2, 70, 106.4, 84.4, 0.88785, 69, 210.39, 87.67, 0.11215, 2, 70, 160.5, 82.45, 0.98158, 69, 264.53, 88.1, 0.01842, 1, 70, 154.43, -86.11, 1], "edges": [8, 10, 20, 22, 16, 2, 2, 4, 14, 16, 4, 14, 10, 12, 12, 14, 4, 6, 6, 8, 12, 6, 16, 18, 18, 20, 2, 0, 0, 22, 18, 0]}, "tx/fire_11": {"type": "mesh", "hull": 12, "width": 83, "height": 177, "uvs": [1, 0.18967, 1, 0.36599, 1, 0.54954, 1, 0.75011, 1, 1, 0, 1, 0, 0.69054, 0, 0.50938, 0, 0.33892, 0, 0.18301, 0, 0, 1, 0], "triangles": [5, 3, 4, 5, 6, 3, 6, 2, 3, 6, 7, 2, 7, 1, 2, 7, 8, 1, 8, 0, 1, 8, 9, 0, 9, 11, 0, 9, 10, 11], "vertices": [1, 70, 98.35, -84.09, 1, 2, 70, 46.22, -82.21, 0.99661, 69, 157.57, -81.42, 0.00339, 2, 70, -8.05, -80.26, 0.76672, 69, 103.27, -81.84, 0.23328, 2, 70, -67.35, -78.12, 0.22571, 69, 43.93, -82.3, 0.77429, 2, 70, -141.23, -75.46, 0.01543, 69, -29.99, -82.88, 0.98457, 1, 69, -31.31, 85.79, 1, 1, 69, 60.24, 86.5, 1, 2, 70, 9.9, 87.88, 0.13826, 69, 113.83, 86.92, 0.86174, 2, 70, 60.3, 86.07, 0.5978, 69, 164.26, 87.31, 0.4022, 2, 70, 106.4, 84.4, 0.88785, 69, 210.39, 87.67, 0.11215, 2, 70, 160.5, 82.45, 0.98158, 69, 264.53, 88.1, 0.01842, 1, 70, 154.43, -86.11, 1], "edges": [8, 10, 20, 22, 16, 2, 2, 4, 14, 16, 4, 14, 10, 12, 12, 14, 4, 6, 6, 8, 12, 6, 16, 18, 18, 20, 2, 0, 0, 22, 18, 0]}, "tx/fire_12": {"type": "mesh", "hull": 12, "width": 83, "height": 177, "uvs": [1, 0.18967, 1, 0.36599, 1, 0.54954, 1, 0.75011, 1, 1, 0, 1, 0, 0.69054, 0, 0.50938, 0, 0.33892, 0, 0.18301, 0, 0, 1, 0], "triangles": [7, 1, 2, 7, 8, 1, 8, 0, 1, 8, 9, 0, 9, 11, 0, 9, 10, 11, 5, 3, 4, 5, 6, 3, 6, 2, 3, 6, 7, 2], "vertices": [2, 69, 209.73, -81.01, 0.02888, 70, 98.35, -84.09, 0.97112, 2, 69, 157.57, -81.42, 0.15972, 70, 46.22, -82.21, 0.84028, 2, 69, 103.27, -81.84, 0.46818, 70, -8.05, -80.26, 0.53182, 2, 69, 43.93, -82.3, 0.74803, 70, -67.35, -78.12, 0.25197, 2, 69, -29.99, -82.88, 0.91962, 70, -141.23, -75.46, 0.08038, 2, 69, -31.31, 85.79, 0.99486, 70, -135.15, 93.11, 0.00514, 2, 69, 60.24, 86.5, 0.90901, 70, -43.66, 89.81, 0.09099, 2, 69, 113.83, 86.92, 0.62431, 70, 9.9, 87.88, 0.37569, 2, 69, 164.26, 87.31, 0.34487, 70, 60.3, 86.07, 0.65513, 2, 69, 210.39, 87.67, 0.13319, 70, 106.4, 84.4, 0.86681, 2, 69, 264.53, 88.1, 0.04352, 70, 160.5, 82.45, 0.95648, 2, 69, 265.84, -80.57, 0.00614, 70, 154.43, -86.11, 0.99386], "edges": [8, 10, 20, 22, 16, 2, 2, 4, 14, 16, 4, 14, 10, 12, 12, 14, 4, 6, 6, 8, 12, 6, 16, 18, 18, 20, 2, 0, 0, 22, 18, 0]}, "tx/fire_13": {"type": "mesh", "hull": 12, "width": 83, "height": 177, "uvs": [1, 0.18967, 1, 0.36599, 1, 0.54954, 1, 0.75011, 1, 1, 0, 1, 0, 0.69054, 0, 0.50938, 0, 0.33892, 0, 0.18301, 0, 0, 1, 0], "triangles": [5, 3, 4, 5, 6, 3, 6, 2, 3, 6, 7, 2, 7, 1, 2, 7, 8, 1, 8, 0, 1, 8, 9, 0, 9, 11, 0, 9, 10, 11], "vertices": [1, 70, 98.35, -84.09, 1, 2, 70, 46.22, -82.21, 0.99661, 69, 157.57, -81.42, 0.00339, 2, 70, -8.05, -80.26, 0.76672, 69, 103.27, -81.84, 0.23328, 2, 70, -67.35, -78.12, 0.22571, 69, 43.93, -82.3, 0.77429, 2, 70, -141.23, -75.46, 0.01543, 69, -29.99, -82.88, 0.98457, 1, 69, -31.31, 85.79, 1, 1, 69, 60.24, 86.5, 1, 2, 70, 9.9, 87.88, 0.13826, 69, 113.83, 86.92, 0.86174, 2, 70, 60.3, 86.07, 0.5978, 69, 164.26, 87.31, 0.4022, 2, 70, 106.4, 84.4, 0.88785, 69, 210.39, 87.67, 0.11215, 2, 70, 160.5, 82.45, 0.98158, 69, 264.53, 88.1, 0.01842, 1, 70, 154.43, -86.11, 1], "edges": [8, 10, 20, 22, 16, 2, 2, 4, 14, 16, 4, 14, 10, 12, 12, 14, 4, 6, 6, 8, 12, 6, 16, 18, 18, 20, 2, 0, 0, 22, 18, 0]}, "tx/fire_14": {"type": "mesh", "hull": 12, "width": 83, "height": 177, "uvs": [1, 0.18967, 1, 0.36599, 1, 0.54954, 1, 0.75011, 1, 1, 0, 1, 0, 0.69054, 0, 0.50938, 0, 0.33892, 0, 0.18301, 0, 0, 1, 0], "triangles": [5, 3, 4, 5, 6, 3, 6, 2, 3, 6, 7, 2, 7, 1, 2, 7, 8, 1, 8, 0, 1, 8, 9, 0, 9, 11, 0, 9, 10, 11], "vertices": [1, 70, 98.35, -84.09, 1, 2, 70, 46.22, -82.21, 0.99661, 69, 157.57, -81.42, 0.00339, 2, 70, -8.05, -80.26, 0.76672, 69, 103.27, -81.84, 0.23328, 2, 70, -67.35, -78.12, 0.22571, 69, 43.93, -82.3, 0.77429, 2, 70, -141.23, -75.46, 0.01543, 69, -29.99, -82.88, 0.98457, 1, 69, -31.31, 85.79, 1, 1, 69, 60.24, 86.5, 1, 2, 70, 9.9, 87.88, 0.13826, 69, 113.83, 86.92, 0.86174, 2, 70, 60.3, 86.07, 0.5978, 69, 164.26, 87.31, 0.4022, 2, 70, 106.4, 84.4, 0.88785, 69, 210.39, 87.67, 0.11215, 2, 70, 160.5, 82.45, 0.98158, 69, 264.53, 88.1, 0.01842, 1, 70, 154.43, -86.11, 1], "edges": [8, 10, 20, 22, 16, 2, 2, 4, 14, 16, 4, 14, 10, 12, 12, 14, 4, 6, 6, 8, 12, 6, 16, 18, 18, 20, 2, 0, 0, 22, 18, 0]}, "tx/fire_15": {"type": "mesh", "hull": 12, "width": 83, "height": 177, "uvs": [1, 0.18967, 1, 0.36599, 1, 0.54954, 1, 0.75011, 1, 1, 0, 1, 0, 0.69054, 0, 0.50938, 0, 0.33892, 0, 0.18301, 0, 0, 1, 0], "triangles": [5, 3, 4, 5, 6, 3, 6, 2, 3, 6, 7, 2, 7, 1, 2, 7, 8, 1, 8, 0, 1, 8, 9, 0, 9, 11, 0, 9, 10, 11], "vertices": [1, 70, 98.35, -84.09, 1, 2, 70, 46.22, -82.21, 0.99661, 69, 157.57, -81.42, 0.00339, 2, 70, -8.05, -80.26, 0.76672, 69, 103.27, -81.84, 0.23328, 2, 70, -67.35, -78.12, 0.22571, 69, 43.93, -82.3, 0.77429, 2, 70, -141.23, -75.46, 0.01543, 69, -29.99, -82.88, 0.98457, 1, 69, -31.31, 85.79, 1, 1, 69, 60.24, 86.5, 1, 2, 70, 9.9, 87.88, 0.13826, 69, 113.83, 86.92, 0.86174, 2, 70, 60.3, 86.07, 0.5978, 69, 164.26, 87.31, 0.4022, 2, 70, 106.4, 84.4, 0.88785, 69, 210.39, 87.67, 0.11215, 2, 70, 160.5, 82.45, 0.98158, 69, 264.53, 88.1, 0.01842, 1, 70, 154.43, -86.11, 1], "edges": [8, 10, 20, 22, 16, 2, 2, 4, 14, 16, 4, 14, 10, 12, 12, 14, 4, 6, 6, 8, 12, 6, 16, 18, 18, 20, 2, 0, 0, 22, 18, 0]}}, "gh": {"tx/gh_0": {"x": -8.78, "y": 35.11, "width": 200, "height": 180}, "tx/gh_1": {"x": -8.78, "y": 35.11, "width": 200, "height": 180}, "tx/gh_2": {"x": -8.78, "y": 35.11, "width": 200, "height": 180}, "tx/gh_3": {"x": -8.78, "y": 35.11, "width": 200, "height": 180}, "tx/gh_4": {"x": -8.78, "y": 35.11, "width": 200, "height": 180}, "tx/gh_5": {"x": -8.78, "y": 35.11, "width": 200, "height": 180}, "tx/gh_6": {"x": -8.78, "y": 35.11, "width": 200, "height": 180}, "tx/gh_7": {"x": -8.78, "y": 35.11, "width": 200, "height": 180}, "tx/gh_8": {"x": -8.78, "y": 35.11, "width": 200, "height": 180}, "tx/gh_9": {"x": -8.78, "y": 35.11, "width": 200, "height": 180}, "tx/gh_10": {"x": -8.78, "y": 35.11, "width": 200, "height": 180}, "tx/gh_11": {"x": -8.78, "y": 35.11, "width": 200, "height": 180}, "tx/gh_12": {"x": -8.78, "y": 35.11, "width": 200, "height": 180}, "tx/gh_13": {"x": -8.78, "y": 35.11, "width": 200, "height": 180}}, "gh2": {"tx/gh_0": {"x": -8.78, "y": 35.11, "width": 200, "height": 180}, "tx/gh_1": {"x": -8.78, "y": 35.11, "width": 200, "height": 180}, "tx/gh_2": {"x": -8.78, "y": 35.11, "width": 200, "height": 180}, "tx/gh_3": {"x": -8.78, "y": 35.11, "width": 200, "height": 180}, "tx/gh_4": {"x": -8.78, "y": 35.11, "width": 200, "height": 180}, "tx/gh_5": {"x": -8.78, "y": 35.11, "width": 200, "height": 180}, "tx/gh_6": {"x": -8.78, "y": 35.11, "width": 200, "height": 180}, "tx/gh_7": {"x": -8.78, "y": 35.11, "width": 200, "height": 180}, "tx/gh_8": {"x": -8.78, "y": 35.11, "width": 200, "height": 180}, "tx/gh_9": {"x": -8.78, "y": 35.11, "width": 200, "height": 180}, "tx/gh_10": {"x": -8.78, "y": 35.11, "width": 200, "height": 180}, "tx/gh_11": {"x": -8.78, "y": 35.11, "width": 200, "height": 180}, "tx/gh_12": {"x": -8.78, "y": 35.11, "width": 200, "height": 180}, "tx/gh_13": {"x": -8.78, "y": 35.11, "width": 200, "height": 180}}, "hjs": {"tx/01_00001": {"x": 32.36, "y": -90.71, "width": 288, "height": 279}, "tx/01_00002": {"x": 32.36, "y": -90.71, "width": 288, "height": 279}, "tx/01_00003": {"x": 32.36, "y": -90.71, "width": 288, "height": 279}, "tx/01_00004": {"x": 32.36, "y": -90.71, "width": 288, "height": 279}, "tx/01_00005": {"x": 32.36, "y": -90.71, "width": 288, "height": 279}, "tx/01_00006": {"x": 32.36, "y": -90.71, "width": 288, "height": 279}, "tx/01_00007": {"x": 32.36, "y": -90.71, "width": 288, "height": 279}, "tx/01_00008": {"x": 32.36, "y": -90.71, "width": 288, "height": 279}, "tx/01_00009": {"x": 32.36, "y": -90.71, "width": 288, "height": 279}, "tx/01_00010": {"x": 32.36, "y": -90.71, "width": 288, "height": 279}, "tx/01_00011": {"x": 32.36, "y": -90.71, "width": 288, "height": 279}}, "hjs2": {"tx/01_00001": {"x": 32.36, "y": -90.71, "width": 288, "height": 279}, "tx/01_00002": {"x": 32.36, "y": -90.71, "width": 288, "height": 279}, "tx/01_00003": {"x": 32.36, "y": -90.71, "width": 288, "height": 279}, "tx/01_00004": {"x": 32.36, "y": -90.71, "width": 288, "height": 279}, "tx/01_00005": {"x": 32.36, "y": -90.71, "width": 288, "height": 279}, "tx/01_00006": {"x": 32.36, "y": -90.71, "width": 288, "height": 279}, "tx/01_00007": {"x": 32.36, "y": -90.71, "width": 288, "height": 279}, "tx/01_00008": {"x": 32.36, "y": -90.71, "width": 288, "height": 279}, "tx/01_00009": {"x": 32.36, "y": -90.71, "width": 288, "height": 279}, "tx/01_00010": {"x": 32.36, "y": -90.71, "width": 288, "height": 279}, "tx/01_00011": {"x": 32.36, "y": -90.71, "width": 288, "height": 279}}, "hjs3": {"tx/01_00001": {"x": 32.36, "y": -90.71, "width": 288, "height": 279}, "tx/01_00002": {"x": 32.36, "y": -90.71, "width": 288, "height": 279}, "tx/01_00003": {"x": 32.36, "y": -90.71, "width": 288, "height": 279}, "tx/01_00004": {"x": 32.36, "y": -90.71, "width": 288, "height": 279}, "tx/01_00005": {"x": 32.36, "y": -90.71, "width": 288, "height": 279}, "tx/01_00006": {"x": 32.36, "y": -90.71, "width": 288, "height": 279}, "tx/01_00007": {"x": 32.36, "y": -90.71, "width": 288, "height": 279}, "tx/01_00008": {"x": 32.36, "y": -90.71, "width": 288, "height": 279}, "tx/01_00009": {"x": 32.36, "y": -90.71, "width": 288, "height": 279}, "tx/01_00010": {"x": 32.36, "y": -90.71, "width": 288, "height": 279}, "tx/01_00011": {"x": 32.36, "y": -90.71, "width": 288, "height": 279}}, "hjs4": {"tx/01_00001": {"x": 32.36, "y": -90.71, "width": 288, "height": 279}, "tx/01_00002": {"x": 32.36, "y": -90.71, "width": 288, "height": 279}, "tx/01_00003": {"x": 32.36, "y": -90.71, "width": 288, "height": 279}, "tx/01_00004": {"x": 32.36, "y": -90.71, "width": 288, "height": 279}, "tx/01_00005": {"x": 32.36, "y": -90.71, "width": 288, "height": 279}, "tx/01_00006": {"x": 32.36, "y": -90.71, "width": 288, "height": 279}, "tx/01_00007": {"x": 32.36, "y": -90.71, "width": 288, "height": 279}, "tx/01_00008": {"x": 32.36, "y": -90.71, "width": 288, "height": 279}, "tx/01_00009": {"x": 32.36, "y": -90.71, "width": 288, "height": 279}, "tx/01_00010": {"x": 32.36, "y": -90.71, "width": 288, "height": 279}, "tx/01_00011": {"x": 32.36, "y": -90.71, "width": 288, "height": 279}}, "huo": {"tx/huo_0": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}, "tx/huo_1": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}, "tx/huo_2": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}, "tx/huo_3": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}, "tx/huo_4": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}, "tx/huo_5": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}, "tx/huo_6": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}, "tx/huo_7": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}, "tx/huo_8": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}, "tx/huo_9": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}}, "huo2": {"tx/huo_0": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}, "tx/huo_1": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}, "tx/huo_2": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}, "tx/huo_3": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}, "tx/huo_4": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}, "tx/huo_5": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}, "tx/huo_6": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}, "tx/huo_7": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}, "tx/huo_8": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}, "tx/huo_9": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}}, "huo3": {"tx/huo_0": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}, "tx/huo_1": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}, "tx/huo_2": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}, "tx/huo_3": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}, "tx/huo_4": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}, "tx/huo_5": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}, "tx/huo_6": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}, "tx/huo_7": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}, "tx/huo_8": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}, "tx/huo_9": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}}, "huo4": {"tx/huo_0": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}, "tx/huo_1": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}, "tx/huo_2": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}, "tx/huo_3": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}, "tx/huo_4": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}, "tx/huo_5": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}, "tx/huo_6": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}, "tx/huo_7": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}, "tx/huo_8": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}, "tx/huo_9": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}}, "huo5": {"tx/huo_0": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}, "tx/huo_1": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}, "tx/huo_2": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}, "tx/huo_3": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}, "tx/huo_4": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}, "tx/huo_5": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}, "tx/huo_6": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}, "tx/huo_7": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}, "tx/huo_8": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}, "tx/huo_9": {"x": 3.77, "y": 49.55, "width": 105, "height": 133}}, "jianjia01": {"jianjia01": {"x": 29.51, "y": 4.47, "rotation": 35.8, "width": 219, "height": 223}}, "jiao01": {"jiao01": {"x": 15.44, "y": -177.48, "rotation": 142.88, "width": 262, "height": 351}}, "jiao1_g": {"tx/jiao01_g_0": {"x": 54.06, "y": -45.08, "scaleX": 1.42, "scaleY": 1.42, "rotation": 142.88, "width": 155, "height": 93}, "tx/jiao01_g_1": {"x": 54.06, "y": -45.08, "scaleX": 1.42, "scaleY": 1.42, "rotation": 142.88, "width": 155, "height": 93}, "tx/jiao01_g_2": {"x": 54.06, "y": -45.08, "scaleX": 1.42, "scaleY": 1.42, "rotation": 142.88, "width": 155, "height": 93}, "tx/jiao01_g_3": {"x": 54.06, "y": -45.08, "scaleX": 1.42, "scaleY": 1.42, "rotation": 142.88, "width": 155, "height": 93}, "tx/jiao01_g_4": {"x": 54.06, "y": -45.08, "scaleX": 1.42, "scaleY": 1.42, "rotation": 142.88, "width": 155, "height": 93}, "tx/jiao01_g_5": {"x": 54.06, "y": -45.08, "scaleX": 1.42, "scaleY": 1.42, "rotation": 142.88, "width": 155, "height": 93}, "tx/jiao01_g_6": {"x": 54.06, "y": -45.08, "scaleX": 1.42, "scaleY": 1.42, "rotation": 142.88, "width": 155, "height": 93}, "tx/jiao01_g_7": {"x": 54.06, "y": -45.08, "scaleX": 1.42, "scaleY": 1.42, "rotation": 142.88, "width": 155, "height": 93}, "tx/jiao01_g_8": {"x": 54.06, "y": -45.08, "scaleX": 1.42, "scaleY": 1.42, "rotation": 142.88, "width": 155, "height": 93}, "tx/jiao01_g_9": {"x": 54.06, "y": -45.08, "scaleX": 1.42, "scaleY": 1.42, "rotation": 142.88, "width": 155, "height": 93}, "tx/jiao01_g_10": {"x": 54.06, "y": -45.08, "scaleX": 1.42, "scaleY": 1.42, "rotation": 142.88, "width": 155, "height": 93}, "tx/jiao01_g_11": {"x": 54.06, "y": -45.08, "scaleX": 1.42, "scaleY": 1.42, "rotation": 142.88, "width": 155, "height": 93}, "tx/jiao01_g_12": {"x": 54.06, "y": -45.08, "scaleX": 1.42, "scaleY": 1.42, "rotation": 142.88, "width": 155, "height": 93}}, "jiao02": {"jiao02": {"x": 89.1, "y": -186.2, "rotation": 142.88, "width": 290, "height": 306}}, "jiao2_g": {"tx/jiao01_g_0": {"x": 112.89, "y": -61.07, "scaleX": 1.1515, "scaleY": 1.1515, "rotation": 138.71, "width": 155, "height": 93}, "tx/jiao01_g_1": {"x": 112.89, "y": -61.07, "scaleX": 1.1515, "scaleY": 1.1515, "rotation": 138.71, "width": 155, "height": 93}, "tx/jiao01_g_2": {"x": 112.89, "y": -61.07, "scaleX": 1.1515, "scaleY": 1.1515, "rotation": 138.71, "width": 155, "height": 93}, "tx/jiao01_g_3": {"x": 112.89, "y": -61.07, "scaleX": 1.1515, "scaleY": 1.1515, "rotation": 138.71, "width": 155, "height": 93}, "tx/jiao01_g_4": {"x": 112.89, "y": -61.07, "scaleX": 1.1515, "scaleY": 1.1515, "rotation": 138.71, "width": 155, "height": 93}, "tx/jiao01_g_5": {"x": 112.89, "y": -61.07, "scaleX": 1.1515, "scaleY": 1.1515, "rotation": 138.71, "width": 155, "height": 93}, "tx/jiao01_g_6": {"x": 112.89, "y": -61.07, "scaleX": 1.1515, "scaleY": 1.1515, "rotation": 138.71, "width": 155, "height": 93}, "tx/jiao01_g_7": {"x": 112.89, "y": -61.07, "scaleX": 1.1515, "scaleY": 1.1515, "rotation": 138.71, "width": 155, "height": 93}, "tx/jiao01_g_8": {"x": 112.89, "y": -61.07, "scaleX": 1.1515, "scaleY": 1.1515, "rotation": 138.71, "width": 155, "height": 93}, "tx/jiao01_g_9": {"x": 112.89, "y": -61.07, "scaleX": 1.1515, "scaleY": 1.1515, "rotation": 138.71, "width": 155, "height": 93}, "tx/jiao01_g_10": {"x": 112.89, "y": -61.07, "scaleX": 1.1515, "scaleY": 1.1515, "rotation": 138.71, "width": 155, "height": 93}, "tx/jiao01_g_11": {"x": 112.89, "y": -61.07, "scaleX": 1.1515, "scaleY": 1.1515, "rotation": 138.71, "width": 155, "height": 93}, "tx/jiao01_g_12": {"x": 112.89, "y": -61.07, "scaleX": 1.1515, "scaleY": 1.1515, "rotation": 138.71, "width": 155, "height": 93}}, "jm": {"tx/jm1_0": {"x": 41.4, "y": -15.77, "scaleX": 1.42, "scaleY": 1.42, "rotation": 35.67, "width": 210, "height": 166}, "tx/jm1_1": {"x": 41.4, "y": -15.77, "scaleX": 1.42, "scaleY": 1.42, "rotation": 35.67, "width": 210, "height": 166}, "tx/jm1_2": {"x": 41.4, "y": -15.77, "scaleX": 1.42, "scaleY": 1.42, "rotation": 35.67, "width": 210, "height": 166}, "tx/jm1_3": {"x": 41.4, "y": -15.77, "scaleX": 1.42, "scaleY": 1.42, "rotation": 35.67, "width": 210, "height": 166}, "tx/jm1_4": {"x": 41.4, "y": -15.77, "scaleX": 1.42, "scaleY": 1.42, "rotation": 35.67, "width": 210, "height": 166}, "tx/jm1_5": {"x": 41.4, "y": -15.77, "scaleX": 1.42, "scaleY": 1.42, "rotation": 35.67, "width": 210, "height": 166}, "tx/jm1_6": {"x": 41.4, "y": -15.77, "scaleX": 1.42, "scaleY": 1.42, "rotation": 35.67, "width": 210, "height": 166}, "tx/jm1_7": {"x": 41.4, "y": -15.77, "scaleX": 1.42, "scaleY": 1.42, "rotation": 35.67, "width": 210, "height": 166}, "tx/jm1_8": {"x": 41.4, "y": -15.77, "scaleX": 1.42, "scaleY": 1.42, "rotation": 35.67, "width": 210, "height": 166}, "tx/jm1_9": {"x": 41.4, "y": -15.77, "scaleX": 1.42, "scaleY": 1.42, "rotation": 35.67, "width": 210, "height": 166}, "tx/jm1_10": {"x": 41.4, "y": -15.77, "scaleX": 1.42, "scaleY": 1.42, "rotation": 35.67, "width": 210, "height": 166}, "tx/jm1_11": {"x": 41.4, "y": -15.77, "scaleX": 1.42, "scaleY": 1.42, "rotation": 35.67, "width": 210, "height": 166}, "tx/jm1_12": {"x": 41.4, "y": -15.77, "scaleX": 1.42, "scaleY": 1.42, "rotation": 35.67, "width": 210, "height": 166}}, "ju": {"tx/j_0": {"x": -11.98, "y": 13.48, "width": 300, "height": 300}, "tx/j_2": {"x": -11.98, "y": 13.48, "width": 300, "height": 300}, "tx/j_4": {"x": -11.98, "y": 13.48, "width": 300, "height": 300}, "tx/j_6": {"x": -11.98, "y": 13.48, "width": 300, "height": 300}, "tx/j_8": {"x": -11.98, "y": 13.48, "width": 300, "height": 300}, "tx/j_10": {"x": -11.98, "y": 13.48, "width": 300, "height": 300}, "tx/j_12": {"x": -11.98, "y": 13.48, "width": 300, "height": 300}, "tx/j_14": {"x": -11.98, "y": 13.48, "width": 300, "height": 300}, "tx/j_16": {"x": -11.98, "y": 13.48, "width": 300, "height": 300}, "tx/j_18": {"x": -11.98, "y": 13.48, "width": 300, "height": 300}, "tx/j_20": {"x": -11.98, "y": 13.48, "width": 300, "height": 300}, "tx/j_22": {"x": -11.98, "y": 13.48, "width": 300, "height": 300}, "tx/j_24": {"x": -11.98, "y": 13.48, "width": 300, "height": 300}, "tx/j_26": {"x": -11.98, "y": 13.48, "width": 300, "height": 300}}, "ju2": {"tx/j_0": {"x": -11.98, "y": 13.48, "width": 300, "height": 300}, "tx/j_2": {"x": -11.98, "y": 13.48, "width": 300, "height": 300}, "tx/j_4": {"x": -11.98, "y": 13.48, "width": 300, "height": 300}, "tx/j_6": {"x": -11.98, "y": 13.48, "width": 300, "height": 300}, "tx/j_8": {"x": -11.98, "y": 13.48, "width": 300, "height": 300}, "tx/j_10": {"x": -11.98, "y": 13.48, "width": 300, "height": 300}, "tx/j_12": {"x": -11.98, "y": 13.48, "width": 300, "height": 300}, "tx/j_14": {"x": -11.98, "y": 13.48, "width": 300, "height": 300}, "tx/j_16": {"x": -11.98, "y": 13.48, "width": 300, "height": 300}, "tx/j_18": {"x": -11.98, "y": 13.48, "width": 300, "height": 300}, "tx/j_20": {"x": -11.98, "y": 13.48, "width": 300, "height": 300}, "tx/j_22": {"x": -11.98, "y": 13.48, "width": 300, "height": 300}, "tx/j_24": {"x": -11.98, "y": 13.48, "width": 300, "height": 300}, "tx/j_26": {"x": -11.98, "y": 13.48, "width": 300, "height": 300}}, "lz": {"tx/lz": {"width": 22, "height": 22}}, "lz2": {"tx/lz": {"width": 22, "height": 22}}, "lz3": {"tx/lz": {"width": 22, "height": 22}}, "lz4": {"tx/lz": {"width": 22, "height": 22}}, "lz5": {"tx/lz": {"width": 22, "height": 22}}, "maofa01": {"maofa01": {"type": "mesh", "hull": 24, "width": 210, "height": 110, "uvs": [0.62495, 0.35581, 0.7359, 0.38446, 0.82753, 0.39377, 0.9021, 0.35581, 1, 0.24666, 1, 0.36292, 0.94685, 0.548, 0.8064, 0.66375, 0.702, 1, 0.65015, 0.85426, 0.55783, 0.76103, 0.44259, 0.74511, 0.34813, 0.8068, 0.2574, 0.86849, 0.13828, 0.94047, 0.08484, 1, 0, 1, 0, 0.88116, 0.04382, 0.66998, 0.1008, 0.46276, 0.21763, 0.18515, 0.39412, 0.13295, 0.55907, 0.08006, 0.68336, 0], "triangles": [14, 17, 18, 13, 14, 18, 15, 16, 17, 14, 15, 17, 12, 20, 21, 12, 19, 20, 21, 11, 12, 13, 18, 19, 12, 13, 19, 3, 4, 5, 6, 3, 5, 7, 1, 2, 2, 3, 6, 7, 2, 6, 9, 1, 7, 8, 9, 7, 0, 22, 23, 0, 11, 21, 0, 21, 22, 10, 11, 0, 9, 0, 1, 10, 0, 9], "vertices": [2, 55, 41.29, 15.47, 0.72737, 56, -10.7, 23.23, 0.27263, 2, 55, 64.69, 17.8, 0.39403, 56, 11.05, 14.28, 0.60597, 2, 55, 83.64, 21.26, 0.06603, 56, 29.4, 8.43, 0.93397, 1, 56, 45.61, 8.5, 1, 1, 56, 68.54, 14.92, 1, 1, 56, 65.3, 2.54, 1, 2, 55, 111.95, 10.56, 0.01534, 56, 49.35, -14.33, 0.98466, 2, 55, 86.2, -8.66, 0.20924, 56, 17.6, -19.18, 0.79076, 3, 55, 73.44, -49.71, 0.44126, 56, -12.97, -49.42, 0.5586, 54, 80.35, -85.45, 0.00014, 3, 55, 59.14, -36.64, 0.73494, 56, -19.45, -31.15, 0.24826, 54, 77.93, -66.23, 0.0168, 4, 55, 37.9, -31.16, 0.72379, 56, -35.61, -16.33, 0.10883, 54, 65.31, -48.29, 0.16649, 5, -75.37, 50.77, 0.00089, 4, 55, 13.95, -35.06, 0.53398, 56, -58.58, -8.51, 0.00766, 54, 44.54, -35.74, 0.43614, 5, -57.13, 34.77, 0.02222, 3, 55, -3.77, -46.25, 0.22861, 54, 23.78, -32.79, 0.62958, 5, -37.22, 28.21, 0.14181, 3, 55, -20.73, -57.27, 0.04586, 54, 3.73, -30.18, 0.48331, 5, -17.93, 22.13, 0.47083, 3, 55, -43.24, -70.76, 0.00365, 54, -22.16, -25.88, 0.21352, 5, 6.8, 13.34, 0.78283, 2, 54, -35.13, -26.62, 0.00342, 5, 19.7, 11.79, 0.99658, 2, 54, -51.01, -18.54, 0.00047, 5, 33.9, 1.04, 0.99953, 2, 54, -45.07, -6.89, 0.1079, 5, 26.02, -9.38, 0.8921, 2, 54, -26.33, 9.63, 0.42554, 5, 4.66, -22.35, 0.57446, 3, 55, -63.06, -21.47, 0.00046, 54, -5.32, 24.51, 0.75795, 5, -18.64, -33.31, 0.2416, 3, 55, -46.27, 13.92, 0.16989, 54, 30.4, 40.58, 0.81442, 5, -56.63, -42.85, 0.01569, 2, 55, -11.55, 28.09, 0.50237, 54, 66.03, 28.88, 0.49763, 2, 55, 20.81, 41.77, 0.83525, 54, 99.54, 18.34, 0.16475, 3, 55, 44.16, 56.39, 0.99382, 56, 11.07, 57.99, 0.00533, 54, 126.8, 14.34, 0.00085], "edges": [30, 32, 28, 30, 32, 34, 36, 34, 44, 46, 16, 14, 46, 0, 0, 2, 2, 4, 12, 14, 12, 10, 10, 8, 6, 8, 6, 4, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 36, 38, 38, 40, 40, 42, 42, 44]}}, "maofa02": {"maofa02": {"x": 80.42, "y": -59.73, "rotation": -156.7, "width": 193, "height": 82}}, "maofa03": {"maofa03": {"x": -29.36, "y": -130.96, "rotation": -156.7, "width": 221, "height": 130}}, "maofa04": {"maofa04": {"type": "mesh", "hull": 39, "width": 366, "height": 182, "uvs": [0.9655, 0.23897, 0.96659, 0.37399, 1, 0.54422, 1, 0.73065, 0.97915, 0.84125, 0.92271, 0.92547, 0.83476, 1, 0.71896, 1, 0.52385, 0.95254, 0.40788, 0.91815, 0.27794, 0.87414, 0.16463, 0.82315, 0.05687, 0.75145, 0, 0.71639, 0, 0.59628, 0.10283, 0.36046, 0.22802, 0.30787, 0.34766, 0.33655, 0.47286, 0.52458, 0.5595, 0.55419, 0.653, 0.59562, 0.73169, 0.67777, 0.78484, 0.70239, 0.8411, 0.68805, 0.87204, 0.64047, 0.8823, 0.55378, 0.86741, 0.47903, 0.82921, 0.40808, 0.77286, 0.35936, 0.72224, 0.35369, 0.67613, 0.40412, 0.62859, 0.47582, 0.65632, 0.29577, 0.72383, 0.20551, 0.77881, 0.18517, 0.83793, 0.20093, 0.89498, 0.26786, 0.95757, 0, 1, 0, 0.62685, 0.97326], "triangles": [29, 32, 33, 28, 33, 34, 29, 33, 28, 30, 32, 29, 31, 32, 30, 8, 19, 20, 12, 13, 14, 15, 12, 14, 11, 15, 16, 12, 15, 11, 10, 16, 17, 10, 17, 18, 11, 16, 10, 9, 10, 18, 8, 18, 19, 9, 18, 8, 39, 8, 20, 8, 39, 7, 5, 24, 4, 23, 24, 5, 39, 20, 21, 7, 39, 21, 7, 21, 22, 6, 22, 23, 6, 23, 5, 7, 22, 6, 0, 37, 38, 0, 36, 37, 28, 34, 35, 36, 0, 1, 27, 28, 35, 27, 35, 36, 26, 27, 36, 26, 36, 1, 25, 26, 1, 2, 25, 1, 24, 25, 2, 3, 24, 2, 4, 24, 3], "vertices": [2, 66, 91.09, -15.51, 0.16492, 67, 0.62, -35.92, 0.83508, 2, 66, 66.51, -15.91, 0.49825, 67, -10.73, -14.12, 0.50175, 3, 66, 35.53, -28.14, 0.78568, 67, -35.52, 8.13, 0.17193, 65, 111.35, 27.15, 0.04239, 2, 66, 1.6, -28.14, 0.75889, 65, 102.67, -5.65, 0.24111, 2, 66, -18.53, -20.51, 0.43241, 65, 90.14, -23.16, 0.56759, 2, 66, -33.86, 0.15, 0.14147, 65, 66.25, -32.69, 0.85853, 3, 66, -47.42, 32.34, 0.05016, 65, 31.66, -37.57, 0.79552, 4, -27.85, -104.18, 0.15432, 3, 66, -47.42, 74.72, 0.00214, 65, -9.31, -26.72, 0.53489, 4, 11.08, -87.42, 0.46296, 1, 4, 80.08, -67.11, 1, 1, 4, 121.54, -56.07, 1, 1, 4, 168.39, -44.61, 1, 1, 4, 210.15, -36.73, 1, 1, 4, 251.53, -33.12, 1, 1, 4, 273.18, -30.75, 1, 1, 4, 281.82, -50.82, 1, 1, 4, 264.23, -105.13, 1, 1, 4, 225.93, -132.04, 1, 1, 4, 183.65, -144.57, 1, 1, 4, 128.03, -131.26, 1, 1, 4, 96.77, -138.86, 1, 2, 65, -13.82, 50.6, 0.51852, 4, 62.36, -145.47, 0.48148, 3, 66, 11.22, 70.06, 0.01638, 65, 10.2, 28.78, 0.76962, 4, 30, -143.13, 0.21399, 4, 66, 6.74, 50.61, 0.09791, 67, 22.04, 69.09, 0.0001, 65, 27.86, 19.47, 0.83067, 4, 10.36, -146.7, 0.07133, 3, 66, 9.35, 30.02, 0.24047, 67, 4.79, 57.55, 0.00029, 65, 48.43, 16.72, 0.75924, 3, 66, 18.01, 18.69, 0.53933, 67, -1.47, 44.74, 0.0253, 65, 61.59, 22.2, 0.43537, 3, 66, 33.79, 14.94, 0.64741, 67, 2.23, 28.95, 0.21366, 65, 69.26, 36.49, 0.13893, 4, 66, 47.39, 20.39, 0.44925, 67, 13.19, 19.22, 0.54035, 65, 67.47, 51.03, 0.00949, 68, -15.49, 47.83, 0.00091, 4, 66, 60.31, 34.37, 0.15038, 67, 31.47, 13.92, 0.68294, 65, 57.26, 67.09, 3e-05, 68, -7.67, 30.48, 0.16665, 3, 66, 69.17, 54.99, 0.00543, 67, 53.88, 15.21, 0.49502, 68, 7.84, 14.25, 0.49954, 2, 67, 70.91, 22.57, 0.16803, 68, 24.49, 6.06, 0.83197, 2, 67, 81.9, 38.33, 0.00044, 68, 43.61, 7.9, 0.99956, 1, 68, 64.73, 13.11, 1, 1, 68, 42.57, -13.08, 1, 2, 67, 82.45, -1.81, 0.17357, 68, 13.41, -18.54, 0.82643, 2, 67, 66.11, -14.12, 0.5069, 68, -6.56, -14.07, 0.4931, 2, 67, 45.47, -21.23, 0.84024, 68, -25.35, -2.97, 0.15976, 1, 67, 21.35, -19.67, 1, 1, 67, 22.66, -73.52, 1, 2, 66, 134.58, -28.14, 0.00351, 67, 8.77, -80.47, 0.99649, 2, 65, -40.66, -13.39, 0.51852, 4, 43.97, -78.56, 0.48148], "edges": [36, 34, 34, 32, 32, 30, 30, 28, 26, 28, 24, 26, 24, 22, 22, 20, 20, 18, 18, 16, 16, 78, 78, 14, 36, 38, 38, 40, 40, 42, 12, 14, 8, 10, 10, 12, 42, 44, 44, 46, 46, 48, 6, 4, 4, 2, 52, 50, 50, 48, 0, 76, 74, 76, 74, 72, 72, 70, 0, 2, 70, 68, 56, 54, 54, 52, 58, 60, 60, 62, 64, 62, 64, 66, 66, 68, 58, 56, 14, 16, 6, 8]}}, "maofa05": {"maofa05": {"type": "mesh", "hull": 38, "width": 275, "height": 166, "uvs": [0.48786, 0.08022, 0.44986, 0.06511, 0.43922, 0.12051, 0.45366, 0.2036, 0.4757, 0.27285, 0.49774, 0.34839, 0.53429, 0.39827, 0.59543, 0.45615, 0.66775, 0.57302, 0.72309, 0.75222, 0.81897, 0.7228, 0.8598, 0.88757, 0.93726, 0.87345, 1, 0.81828, 1, 0.85421, 0.96282, 0.92478, 0.85361, 1, 0.77011, 1, 0.69024, 0.96485, 0.58487, 0.94034, 0.47979, 0.91891, 0.39942, 0.87615, 0.30686, 0.81196, 0.19492, 0.74598, 0.09697, 0.74242, 0, 0.75311, 0, 0.67109, 0.09589, 0.58372, 0.23259, 0.37152, 0.34561, 0.32873, 0.4385, 0.38936, 0.4301, 0.34713, 0.39742, 0.29803, 0.3579, 0.22123, 0.35334, 0.12554, 0.37994, 0.0563, 0.41262, 0, 0.46582, 0, 0.48908, 0.44107], "triangles": [14, 12, 13, 15, 12, 14, 11, 17, 9, 11, 9, 10, 18, 9, 17, 16, 17, 11, 16, 11, 12, 16, 12, 15, 38, 6, 7, 20, 38, 7, 28, 23, 27, 22, 28, 29, 22, 29, 30, 22, 30, 38, 23, 24, 27, 26, 27, 24, 25, 26, 24, 23, 28, 22, 21, 22, 38, 20, 21, 38, 38, 30, 5, 38, 5, 6, 30, 31, 5, 20, 7, 8, 19, 20, 8, 19, 8, 9, 18, 19, 9, 32, 33, 3, 32, 3, 4, 31, 32, 4, 31, 4, 5, 1, 36, 37, 35, 36, 1, 1, 37, 0, 2, 35, 1, 34, 35, 2, 33, 34, 2, 33, 2, 3], "vertices": [2, 64, 24.23, -16.36, 0.99998, 63, 55.38, -26.89, 2e-05, 2, 64, 25.46, -5.69, 0.99933, 63, 62.21, -18.59, 0.00067, 2, 64, 15.98, -3.89, 0.82327, 63, 55.22, -11.94, 0.17673, 2, 64, 2.76, -9.49, 0.49147, 63, 41.07, -9.47, 0.50853, 3, 64, -7.92, -16.89, 0.15879, 63, 28.09, -9.89, 0.84075, 61, -40.69, 69.42, 0.00047, 4, 64, -19.64, -24.42, 0.05344, 63, 14.16, -9.86, 0.60571, 61, -30.27, 60.18, 0.00752, 60, 99.39, 65.97, 0.33333, 1, 60, 110.45, 59.09, 1, 3, 61, 1.41, 54, 0.4496, 60, 128.39, 51.79, 0.55, 62, -38.37, 66.09, 0.0004, 2, 61, 27.23, 43.74, 0.77953, 62, -16.06, 49.54, 0.22047, 2, 61, 52.71, 22.14, 0.46651, 62, 3.02, 22.1, 0.53349, 3, 63, -80.38, -62.11, 0.00659, 61, 75.18, 36.79, 0.17786, 62, 28.49, 30.49, 0.81554, 2, 61, 96.06, 15.86, 0.00172, 62, 43.29, 4.89, 0.99828, 1, 62, 64.08, 10.08, 1, 1, 62, 79.95, 21.47, 1, 1, 62, 80.75, 15.56, 1, 1, 62, 72.19, 2.58, 1, 1, 62, 44.11, -13.83, 1, 2, 61, 80.47, -10.86, 0.1501, 62, 21.36, -16.92, 0.8499, 3, 61, 57.95, -13.92, 0.47823, 60, 165.39, -28.47, 0.0052, 62, -1.19, -14.09, 0.51657, 3, 61, 29.64, -21.31, 0.6143, 60, 136.13, -28.26, 0.20247, 62, -30.45, -13.95, 0.18324, 2, 61, 1.6, -29.14, 0.47075, 60, 107.02, -28.56, 0.52925, 2, 61, -21.53, -31.09, 0.14262, 60, 84.17, -24.44, 0.85738, 2, 61, -49.13, -31.04, 0.00655, 60, 57.53, -17.24, 0.99345, 1, 60, 25.57, -10.46, 1, 1, 60, -1.21, -13.43, 1, 1, 60, -27.41, -18.71, 1, 2, 63, 25.87, 136.66, 0.00114, 60, -29.21, -5.22, 0.99886, 1, 60, -4.99, 12.64, 1, 1, 60, 27.62, 52.53, 1, 1, 60, 57.49, 63.68, 1, 1, 60, 84.14, 57.08, 1, 3, 64, -21.67, -5.93, 0.07902, 63, 22.48, 6.78, 0.57709, 60, 80.93, 63.72, 0.34389, 3, 64, -14.66, 3.98, 0.23159, 63, 33.75, 11.29, 0.76667, 60, 70.94, 70.61, 0.00174, 2, 64, -3.31, 16.3, 0.56345, 63, 49.96, 15.49, 0.43655, 2, 64, 12.31, 19.45, 0.8913, 63, 64.8, 9.67, 0.1087, 2, 64, 24.6, 13.58, 0.99853, 63, 71.93, -1.94, 0.00147, 1, 64, 34.96, 5.78, 1, 2, 64, 36.72, -8.74, 0.99999, 63, 70, -27.27, 1e-05, 1, 60, 99.07, 50.41, 1], "edges": [46, 48, 48, 50, 60, 58, 58, 56, 56, 54, 50, 52, 54, 52, 46, 44, 44, 42, 42, 40, 32, 34, 30, 32, 30, 28, 28, 26, 24, 26, 20, 22, 22, 24, 18, 20, 16, 18, 12, 14, 14, 16, 40, 38, 38, 36, 36, 34, 8, 10, 10, 12, 62, 60, 68, 66, 66, 64, 64, 62, 4, 6, 6, 8, 4, 2, 2, 0, 0, 74, 72, 74, 72, 70, 70, 68]}}, "maofa06": {"maofa06": {"type": "mesh", "hull": 20, "width": 183, "height": 195, "uvs": [1, 0.07365, 1, 0.22858, 0.92856, 0.36991, 0.83367, 0.48503, 0.72945, 0.59965, 0.60895, 0.72802, 0.50147, 0.84202, 0.3712, 1, 0.29629, 1, 0, 0.82521, 0, 0.63113, 0.03249, 0.48137, 0.16113, 0.38876, 0.31909, 0.29401, 0.52264, 0.22982, 0.69831, 0.18509, 0.83009, 0.1416, 0.85615, 0.06958, 0.77072, 0, 0.9387, 0], "triangles": [7, 8, 6, 8, 9, 6, 12, 6, 10, 10, 11, 12, 6, 12, 5, 6, 9, 10, 12, 13, 5, 5, 13, 4, 4, 13, 14, 4, 14, 3, 3, 14, 15, 3, 15, 2, 2, 15, 16, 2, 16, 1, 1, 16, 0, 16, 17, 0, 17, 19, 0, 17, 18, 19], "vertices": [1, 53, 81.77, -1.03, 1, 2, 53, 61.94, -23.82, 0.98893, 52, 132.1, -13.41, 0.01107, 3, 53, 33.99, -36.02, 0.8608, 52, 106.51, -30, 0.13563, 51, 132.22, -89.93, 0.00357, 3, 53, 6.15, -41.55, 0.57246, 52, 79.94, -40, 0.38422, 51, 105.23, -81.15, 0.04332, 3, 53, -22.91, -45.89, 0.25115, 52, 51.98, -49.01, 0.54156, 51, 77.76, -70.74, 0.20729, 3, 53, -55.98, -50.3, 0.04594, 52, 20.07, -58.74, 0.46683, 51, 46.76, -58.42, 0.48723, 3, 53, -85.41, -54.15, 0.00095, 52, -8.34, -67.34, 0.21964, 51, 19.2, -47.4, 0.77941, 2, 52, -44.8, -81.05, 0.05134, 51, -17.84, -35.34, 0.94866, 2, 52, -56.47, -73.85, 0.00151, 51, -22.47, -22.44, 0.99849, 2, 52, -84.73, -16.38, 0.0004, 51, -8.67, 40.1, 0.9996, 2, 52, -64.87, 15.83, 0.02241, 51, 26.96, 52.86, 0.97759, 2, 52, -44.48, 37.57, 0.15486, 51, 56.46, 57.11, 0.84514, 2, 52, -14.96, 40.58, 0.46707, 51, 81.4, 41.04, 0.53293, 3, 53, -40.43, 48.37, 0.06638, 52, 19.34, 41.14, 0.71191, 51, 108.54, 20.06, 0.22172, 3, 53, -4.11, 33.35, 0.38367, 52, 57.62, 32.24, 0.5955, 51, 132.89, -10.79, 0.02083, 2, 53, 25.86, 18.83, 0.71701, 52, 89.56, 22.79, 0.28299, 2, 53, 49.62, 9.39, 0.98396, 52, 114.54, 17.35, 0.01604, 1, 53, 62.44, 16.85, 1, 1, 53, 59.55, 37.35, 1, 1, 53, 82.74, 17.17, 1], "edges": [22, 20, 10, 12, 12, 14, 14, 16, 18, 20, 16, 18, 22, 24, 24, 26, 26, 28, 6, 8, 8, 10, 4, 2, 32, 34, 34, 36, 36, 38, 2, 0, 38, 0, 32, 30, 30, 28, 4, 6]}}, "maofa07": {"maofa07": {"x": 19.45, "y": 4.07, "rotation": -68.2, "width": 28, "height": 59}}, "maofa08": {"maofa08": {"type": "mesh", "hull": 17, "width": 42, "height": 63, "uvs": [1, 0.03651, 0.87822, 0.09937, 0.74831, 0.19117, 0.59047, 0.34359, 0.5339, 0.4707, 0.52332, 0.59075, 0.58698, 0.73473, 0.6666, 0.90235, 0.11555, 1, 0.03593, 0.85905, 0, 0.70679, 0.03513, 0.54605, 0.10647, 0.39667, 0.24475, 0.2528, 0.4445, 0.12412, 0.69803, 0.03093, 1, 0], "triangles": [4, 11, 12, 5, 11, 4, 5, 10, 11, 6, 10, 5, 6, 9, 10, 8, 9, 6, 7, 8, 6, 1, 15, 16, 0, 1, 16, 2, 15, 1, 14, 15, 2, 3, 14, 2, 13, 14, 3, 4, 13, 3, 12, 13, 4], "vertices": [1, 59, 38.18, -6.66, 1, 2, 59, 31.84, -5.35, 0.99813, 58, 43.49, -27.85, 0.00187, 2, 59, 23.9, -4.98, 0.94621, 58, 38.69, -21.52, 0.05379, 2, 59, 12.29, -6.19, 0.79038, 58, 30.3, -13.4, 0.20962, 2, 59, 4.67, -9.6, 0.52719, 58, 22.79, -9.74, 0.4728, 2, 59, -1.36, -14.18, 0.25299, 58, 15.41, -8.06, 0.74701, 2, 59, -6.51, -22.12, 0.07548, 58, 6.02, -9.2, 0.92452, 2, 59, -12.35, -31.53, 0.00721, 58, -4.95, -10.77, 0.99279, 1, 58, -7.21, 13.07, 1, 2, 59, -27.52, -9.64, 0.00014, 58, 2.1, 14.91, 0.99986, 2, 59, -21.22, -2.25, 0.04823, 58, 11.81, 14.82, 0.95177, 2, 59, -12.57, 3.22, 0.20743, 58, 21.56, 11.7, 0.79257, 2, 59, -3.48, 7.07, 0.47746, 58, 30.35, 7.2, 0.52254, 2, 59, 7.19, 8.55, 0.7627, 58, 38.33, -0.02, 0.2373, 2, 59, 18.8, 7.46, 0.93684, 58, 44.95, -9.63, 0.06316, 1, 59, 30.19, 3.2, 1, 1, 59, 39.92, -5.16, 1], "edges": [18, 16, 10, 12, 12, 14, 14, 16, 22, 20, 20, 18, 8, 10, 6, 8, 26, 24, 24, 22, 2, 4, 4, 6, 0, 32, 2, 0, 30, 28, 28, 26, 30, 32]}}, "neihoutui01": {"neihoutui01": {"x": 115.4, "y": 9.31, "rotation": 105.15, "width": 176, "height": 202}}, "neihoutui02": {"neihoutui02": {"type": "mesh", "hull": 18, "width": 138, "height": 191, "uvs": [0.72095, 0.19042, 1, 0.16415, 1, 0.21866, 0.95505, 0.32539, 0.87956, 0.46634, 0.79628, 0.59414, 0.63493, 0.81027, 0.43717, 1, 0.38514, 1, 0.23431, 0.81021, 0, 0.72184, 0.07829, 0.56021, 0.10173, 0.46059, 0, 0.34029, 0, 0.24106, 0.15647, 0.10012, 0.42444, 0, 0.67158, 0, 0.5622, 0.41179], "triangles": [6, 7, 9, 7, 8, 9, 5, 6, 18, 10, 11, 9, 9, 11, 18, 6, 9, 18, 18, 11, 12, 5, 18, 4, 4, 18, 3, 18, 12, 15, 12, 13, 15, 15, 16, 18, 18, 0, 3, 0, 16, 17, 0, 18, 16, 13, 14, 15, 3, 0, 2, 0, 1, 2], "vertices": [2, 18, 51.54, 46.55, 0.80966, 19, -47.23, -3.05, 0.19034, 2, 18, 78.86, 74.15, 0.67017, 19, -71.42, 27.33, 0.32983, 2, 18, 85.25, 65.93, 0.54893, 19, -62.51, 32.71, 0.45107, 2, 18, 92.87, 46.03, 0.40346, 19, -41.85, 37.93, 0.59654, 2, 18, 101.17, 18.38, 0.21056, 19, -13.42, 42.92, 0.78943, 2, 18, 107.09, -7.94, 0.05218, 19, 13.42, 45.69, 0.94782, 2, 18, 114.86, -54.2, 0.00074, 19, 60.27, 47.95, 0.99926, 1, 19, 105.39, 43.31, 1, 1, 19, 109.1, 37.16, 1, 2, 18, 71.21, -88.13, 0.0051, 19, 88.82, 0.61, 0.9949, 2, 18, 35.32, -94.66, 0.0679, 19, 91.07, -35.79, 0.9321, 2, 18, 24.9, -63.65, 0.25064, 19, 59.06, -42.49, 0.74936, 2, 18, 15.77, -46.65, 0.5476, 19, 41.1, -49.55, 0.4524, 2, 18, -9.42, -37.13, 0.81013, 19, 28.67, -73.44, 0.18987, 2, 18, -21.05, -22.17, 0.96072, 19, 12.45, -83.24, 0.03928, 2, 18, -20.54, 12.34, 0.99199, 19, -21.76, -78.65, 0.00801, 2, 18, -3.09, 50.13, 0.99978, 19, -57.24, -56.87, 0.00022, 2, 18, 23.83, 71.07, 0.94608, 19, -74.85, -27.67, 0.05392, 2, 18, 60.21, -0.28, 0.45251, 19, 0.29, 0.04, 0.54749], "edges": [10, 12, 12, 14, 22, 20, 20, 18, 14, 16, 18, 16, 26, 24, 24, 22, 6, 8, 8, 10, 6, 4, 34, 0, 4, 2, 0, 2, 32, 34, 32, 30, 26, 28, 30, 28]}}, "neihoutui03": {"neihoutui03": {"x": 70.88, "y": -11.71, "rotation": 163.04, "width": 209, "height": 106}}, "neiqiantui01": {"neiqiantui01": {"x": 86.04, "y": 17.09, "rotation": 127.17, "width": 224, "height": 248}}, "neiqiantui02": {"neiqiantui02": {"x": 63, "y": 19.52, "rotation": 99.69, "width": 190, "height": 219}}, "neiqiantui03": {"neiqiantui03": {"x": 96.35, "y": -0.64, "rotation": 161.06, "width": 244, "height": 132}}, "qq": {"tx/q (1)": {"x": -35.63, "y": 18.9, "width": 121, "height": 56}, "tx/q (2)": {"x": -55.94, "y": 29.27, "width": 147, "height": 76}, "tx/q (3)": {"x": -64.65, "y": 28.9, "width": 134, "height": 79}, "tx/q (4)": {"x": -70.26, "y": 30.63, "width": 120, "height": 79}, "tx/q (5)": {"x": -73.65, "y": 32.29, "width": 119, "height": 80}, "tx/q (6)": {"x": -75.81, "y": 32.36, "width": 117, "height": 79}, "tx/q (7)": {"x": -78.4, "y": 35.38, "width": 109, "height": 77}, "tx/q (8)": {"x": -93.09, "y": 35.75, "width": 81, "height": 76}, "tx/q (9)": {"x": -91.36, "y": 40.07, "width": 77, "height": 68}}, "shent01": {"shent01": {"type": "mesh", "hull": 25, "width": 327, "height": 409, "uvs": [0.80777, 0.08823, 0.92821, 0.23267, 1, 0.37578, 1, 0.5234, 1, 0.59422, 1, 0.66848, 1, 0.74133, 0.97307, 0.81623, 0.88608, 0.89246, 0.76229, 0.96067, 0.68033, 1, 0.65524, 1, 0.59836, 0.94997, 0.52476, 0.87641, 0.38694, 0.82388, 0.23704, 0.76675, 0.24708, 0.71529, 0.16511, 0.64638, 0.05313, 0.57929, 0, 0.48946, 0, 0.34514, 0.05775, 0.18866, 0.19324, 0.06027, 0.40736, 0, 0.62878, 0, 0.49394, 0.53582, 0.62011, 0.69122, 0.68535, 0.82827, 0.42869, 0.25164], "triangles": [9, 10, 12, 10, 11, 12, 12, 27, 9, 9, 27, 8, 12, 13, 27, 8, 27, 7, 13, 14, 27, 14, 26, 27, 7, 27, 6, 6, 27, 26, 26, 5, 6, 5, 26, 4, 15, 16, 14, 14, 16, 26, 16, 25, 26, 16, 17, 25, 17, 18, 25, 4, 26, 3, 3, 25, 2, 1, 28, 0, 0, 28, 24, 28, 1, 25, 25, 1, 2, 28, 20, 21, 28, 19, 20, 21, 22, 28, 22, 23, 28, 28, 23, 24, 19, 28, 25, 18, 19, 25, 26, 25, 3], "vertices": [1, 4, 93.16, -116.21, 1, 1, 4, 33.62, -77.53, 1, 1, 4, -11.09, -33.06, 1, 4, 4, -34.97, 22.39, 0.81229, 41, 85.8, 140.96, 0.04085, 42, -19.75, 139.88, 0.14429, 43, -132.21, 91.02, 0.00257, 4, 4, -46.43, 48.99, 0.5874, 41, 110.14, 125.27, 0.06709, 42, 7.28, 129.48, 0.32619, 43, -103.36, 93.57, 0.01932, 4, 4, -58.44, 76.89, 0.3739, 41, 135.67, 108.81, 0.05342, 42, 35.63, 118.58, 0.50227, 43, -73.11, 96.24, 0.07041, 4, 4, -70.23, 104.26, 0.23027, 41, 160.71, 92.66, 0.0232, 42, 63.44, 107.88, 0.58205, 43, -43.42, 98.86, 0.16447, 4, 4, -74.25, 135.87, 0.13276, 41, 181.69, 68.66, 0.00381, 42, 88.87, 88.66, 0.55493, 43, -12.14, 92.78, 0.30851, 3, 4, -60.46, 175.76, 0.04482, 42, 107.76, 50.92, 0.33699, 43, 21.42, 67.18, 0.61819, 3, 4, -34.32, 217.39, 0.00198, 42, 119.27, 3.13, 0.01469, 43, 52.77, 29.31, 0.98334, 1, 43, 71.15, 4.03, 1, 2, 42, 121.72, -35.32, 3e-05, 43, 71.87, -4.15, 0.99997, 3, 41, 161.26, -63.96, 0.0032, 42, 95.94, -45.33, 0.06247, 43, 53.12, -24.47, 0.93433, 3, 41, 122.94, -67.88, 0.10496, 42, 59.22, -57, 0.48333, 43, 25.27, -51.09, 0.41171, 3, 41, 80.46, -94.12, 0.52603, 42, 22.99, -91.35, 0.43748, 43, 7.83, -97.87, 0.03649, 4, 4, 154.8, 212.48, 0.01035, 41, 34.26, -122.65, 0.7591, 42, -16.42, -128.71, 0.23053, 43, -11.14, -148.75, 3e-05, 4, 4, 160.11, 191.86, 0.13298, 41, 18.35, -108.49, 0.64921, 42, -34.88, -118.09, 0.2178, 43, -32.39, -147.33, 1e-05, 2, 4, 195.88, 176.57, 0.49, 41, -19.86, -115.74, 0.51, 1, 4, 240.36, 165.86, 1, 1, 4, 270.85, 138.98, 1, 1, 4, 294.2, 84.77, 1, 1, 4, 302.17, 18.52, 1, 1, 4, 282.25, -47.24, 1, 1, 4, 227.69, -97.57, 1, 1, 4, 161.19, -126.21, 1, 1, 4, 115.01, 92.51, 1, 2, 41, 76.17, -0.63, 0.53212, 42, -0.28, -0.71, 0.46788, 3, 41, 134.84, -13.08, 5e-05, 42, 59.69, -0.92, 0.40481, 43, 1.04, -0.51, 0.59514, 1, 4, 180.58, -5.8, 1], "edges": [6, 4, 4, 2, 2, 0, 0, 48, 46, 48, 46, 44, 44, 42, 42, 40, 38, 40, 36, 38, 6, 8, 8, 10, 32, 30, 10, 12, 14, 12, 14, 16, 16, 18, 18, 20, 26, 24, 20, 22, 24, 22, 52, 32, 30, 28, 28, 26, 28, 54, 36, 34, 34, 32]}}, "shenti02": {"shenti02": {"type": "mesh", "hull": 18, "width": 344, "height": 308, "uvs": [0.86722, 0.19278, 0.78119, 0.30131, 0.93403, 0.35706, 1, 0.45384, 1, 0.77637, 0.83387, 0.85599, 0.7077, 1, 0.33069, 1, 0.10926, 0.92071, 0.06146, 0.76236, 0, 0.65027, 0, 0.28731, 0.0981, 0.15209, 0.19687, 0.19301, 0.39887, 0.23192, 0.47693, 0.19456, 0.57569, 0, 0.73181, 0, 0.11563, 0.47413], "triangles": [18, 11, 13, 10, 11, 18, 9, 10, 18, 7, 18, 14, 7, 9, 18, 1, 17, 0, 13, 11, 12, 18, 13, 14, 3, 1, 2, 3, 5, 1, 4, 5, 3, 7, 8, 9, 16, 17, 1, 15, 16, 1, 14, 15, 1, 5, 7, 14, 5, 14, 1, 6, 7, 5], "vertices": [2, 3, -9.5, -92.77, 0.99433, 4, -201.8, -177.6, 0.00567, 2, 3, 18.15, -57.72, 0.98867, 4, -187.84, -135.19, 0.01133, 2, 3, -35.31, -43.56, 0.97141, 4, -242.92, -140.22, 0.02859, 2, 3, -59.66, -15.09, 0.9359, 4, -275.55, -121.82, 0.0641, 2, 3, -65.29, 84.1, 0.8749, 4, -314.84, -30.58, 0.1251, 2, 3, -9.62, 111.82, 0.78592, 4, -272.05, 14.55, 0.21408, 2, 3, 31.2, 158.56, 0.67666, 4, -249.74, 72.46, 0.32334, 2, 3, 160.69, 165.91, 0.56698, 4, -130.62, 123.75, 0.43302, 2, 3, 238.12, 145.84, 0.48443, 4, -51, 131.45, 0.51557, 2, 3, 257.3, 98.08, 0.45365, 4, -16.61, 93.16, 0.54635, 2, 3, 280.36, 64.81, 0.48502, 4, 16.46, 69.82, 0.51498, 2, 3, 286.7, -46.81, 0.5692, 4, 60.68, -32.86, 0.4308, 2, 3, 255.36, -90.3, 0.68146, 4, 46.16, -84.46, 0.31854, 2, 3, 220.73, -79.64, 0.79309, 4, 9.97, -86.32, 0.20691, 2, 3, 150.67, -71.61, 0.88297, 4, -58.59, -102.8, 0.11703, 2, 3, 124.51, -84.62, 0.94303, 4, -78.7, -123.99, 0.05697, 2, 3, 93.99, -146.37, 0.97637, 4, -86.2, -192.47, 0.02363, 2, 3, 40.37, -149.42, 0.99111, 4, -135.53, -213.71, 0.00889, 1, 4, 1.39, 4.26, 1], "edges": [20, 18, 24, 26, 20, 22, 24, 22, 18, 16, 16, 14, 26, 28, 28, 30, 30, 32, 32, 34, 34, 0, 0, 2, 2, 4, 4, 6, 6, 8, 10, 8, 12, 14, 10, 12]}}, "smoke": {"tx/Smoke_1": {"x": -57.62, "y": 40.12, "scaleX": -1, "width": 153, "height": 97}, "tx/Smoke_2": {"x": -57.62, "y": 40.12, "scaleX": -1, "width": 153, "height": 97}, "tx/Smoke_3": {"x": -57.62, "y": 40.12, "scaleX": -1, "width": 153, "height": 97}, "tx/Smoke_4": {"x": -57.62, "y": 40.12, "scaleX": -1, "width": 153, "height": 97}, "tx/Smoke_5": {"x": -57.62, "y": 40.12, "scaleX": -1, "width": 153, "height": 97}, "tx/Smoke_6": {"x": -57.62, "y": 40.12, "scaleX": -1, "width": 153, "height": 97}, "tx/Smoke_7": {"x": -57.62, "y": 40.12, "scaleX": -1, "width": 153, "height": 97}, "tx/Smoke_8": {"x": -57.62, "y": 40.12, "scaleX": -1, "width": 153, "height": 97}, "tx/Smoke_9": {"x": -57.62, "y": 40.12, "scaleX": -1, "width": 153, "height": 97}, "tx/Smoke_10": {"x": -57.62, "y": 40.12, "scaleX": -1, "width": 1, "height": 1}}, "tou02": {"tou02": {"x": 76.28, "y": -5.94, "rotation": 142.88, "width": 158, "height": 168}}, "tou03": {"tou03": {"type": "mesh", "hull": 15, "width": 64, "height": 90, "uvs": [0.45407, 0.1112, 0.62376, 0.21175, 0.85001, 0.31633, 1, 0.44705, 1, 0.65419, 0.84718, 0.83922, 0.63037, 1, 0.6515, 0.82086, 0.66618, 0.69643, 0.54174, 0.59587, 0.31266, 0.51543, 0.10055, 0.41688, 0, 0.27812, 0.0949, 0, 0.19671, 0], "triangles": [9, 2, 3, 8, 9, 3, 4, 8, 3, 5, 8, 4, 7, 8, 5, 6, 7, 5, 14, 12, 13, 0, 12, 14, 10, 11, 0, 0, 11, 12, 1, 10, 0, 9, 10, 1, 9, 1, 2], "vertices": [1, 47, 8.66, 16.11, 1, 2, 47, 22.8, 16.04, 0.91283, 48, -23.81, 3.86, 0.08717, 2, 47, 39.96, 17.99, 0.62465, 48, -10.38, 14.72, 0.37535, 2, 47, 54.85, 15.03, 0.29132, 48, 3.78, 20.22, 0.70868, 2, 47, 66.71, 0.65, 0.04515, 48, 21.51, 14.45, 0.95485, 1, 48, 34.32, 0.01, 1, 1, 48, 43.79, -17.66, 1, 2, 47, 59.06, -25.12, 0.00287, 48, 28.88, -11.39, 0.99713, 2, 47, 52.65, -15.88, 0.13518, 48, 18.52, -7.04, 0.86482, 2, 47, 40.75, -13.97, 0.45703, 48, 7.45, -11.82, 0.54297, 2, 47, 24.83, -17.71, 0.78749, 48, -3.97, -23.52, 0.21251, 2, 47, 8.72, -19.51, 0.98851, 48, -16.6, -33.69, 0.01149, 1, 47, -4.19, -13.97, 1, 1, 47, -15.44, 9.2, 1, 1, 47, -10.41, 13.35, 1], "edges": [20, 22, 22, 24, 28, 0, 0, 2, 26, 28, 24, 26, 2, 4, 4, 6, 20, 18, 18, 16, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16]}}, "tou04": {"tou04": {"type": "mesh", "hull": 21, "width": 92, "height": 128, "uvs": [0.77104, 0.10804, 0.90483, 0.24944, 1, 0.38738, 1, 0.55141, 0.82416, 0.65605, 0.60775, 0.70272, 0.52315, 0.81443, 0.3874, 0.86109, 0.36379, 0.91658, 0.45036, 1, 0.33625, 1, 0.23394, 0.91658, 0.22411, 0.82715, 0.28116, 0.72817, 0.35396, 0.66171, 0.28707, 0.56619, 0.11394, 0.51246, 0, 0.4262, 0.23395, 0.18864, 0.40511, 0, 0.62939, 0, 0.60185, 0.38095], "triangles": [10, 8, 9, 10, 11, 8, 8, 11, 7, 7, 11, 12, 6, 7, 13, 7, 12, 13, 13, 14, 6, 6, 14, 5, 4, 5, 21, 21, 5, 14, 4, 21, 3, 21, 2, 3, 21, 1, 2, 21, 0, 1, 21, 18, 0, 0, 19, 20, 21, 15, 18, 18, 15, 16, 16, 17, 18, 19, 0, 18, 14, 15, 21], "vertices": [1, 6, -0.28, 12.98, 1, 2, 6, 11.21, 31.6, 0.4059, 44, -23.41, 21.81, 0.5941, 2, 6, 23.75, 46.81, 0.11298, 44, -8.73, 34.96, 0.88702, 2, 6, 42.92, 55.38, 0.01043, 44, 11.51, 40.56, 0.98957, 2, 44, 28.73, 28.54, 0.87481, 45, -19.91, 22.6, 0.12519, 3, 44, 39.79, 10.94, 0.54148, 45, -2.92, 10.64, 0.45821, 46, -10.08, 31.13, 0.00031, 3, 44, 55.65, 7.25, 0.20814, 45, 13.14, 13.35, 0.64821, 46, 1.73, 19.92, 0.14365, 2, 45, 25.55, 7.22, 0.52899, 46, 4.29, 6.32, 0.47101, 2, 45, 32.48, 9.9, 0.19597, 46, 10.59, 2.39, 0.80403, 2, 45, 35.95, 22.76, 0.00598, 46, 22.96, 7.33, 0.99402, 2, 45, 42.44, 14.51, 0.00192, 46, 20.26, -2.81, 0.99808, 2, 45, 39.86, 0.51, 0.23255, 46, 7.51, -9.16, 0.76745, 3, 6, 104.29, 4.64, 0.01008, 45, 31.41, -7.27, 0.55581, 46, -3.78, -7.08, 0.43412, 4, 6, 90.58, 4.25, 0.11676, 44, 50.94, -17.15, 0.0149, 45, 18.21, -10.98, 0.76563, 46, -14.67, 1.26, 0.10271, 3, 6, 80.08, 6.89, 0.4501, 44, 40.96, -12.96, 0.0149, 45, 7.38, -10.97, 0.535, 1, 6, 71.43, -3.72, 1, 1, 6, 71.66, -21.07, 1, 1, 6, 65.86, -35.14, 1, 1, 6, 29.31, -27.91, 1, 1, 6, 0.84, -23.4, 1, 1, 6, -7.58, -4.56, 1, 2, 6, 37.96, 13.03, 0.02111, 44, 0.24, -0.56, 0.97889], "edges": [0, 40, 38, 40, 38, 36, 36, 34, 34, 32, 32, 30, 6, 4, 16, 18, 18, 20, 22, 20, 30, 28, 28, 26, 26, 24, 24, 22, 14, 16, 14, 12, 12, 10, 10, 8, 8, 6, 0, 2, 2, 4]}}, "tou05": {"tou05": {"type": "mesh", "hull": 20, "width": 133, "height": 151, "uvs": [0.79836, 0.09864, 1, 0.11028, 1, 0.26221, 0.96255, 0.40949, 0.83233, 0.57239, 0.70732, 0.65118, 0.81112, 0.69107, 0.89227, 0.83403, 0.54502, 1, 0.44688, 0.82073, 0.30561, 0.75652, 0.14666, 0.68095, 0, 0.57724, 0, 0.53433, 0.17406, 0.31976, 0.35495, 0.16716, 0.45645, 0.1162, 0.60021, 0.05044, 0.71533, 0, 0.84177, 0, 0.4288, 0.49204, 0.72288, 0.37624], "triangles": [14, 15, 20, 11, 13, 14, 11, 12, 13, 0, 18, 19, 0, 1, 2, 0, 17, 18, 21, 0, 2, 21, 17, 0, 16, 17, 21, 3, 21, 2, 4, 21, 3, 21, 15, 16, 21, 20, 15, 5, 20, 21, 5, 21, 4, 14, 20, 11, 10, 11, 20, 9, 10, 20, 5, 9, 20, 8, 9, 5, 7, 8, 5, 7, 5, 6], "vertices": [2, 49, 68.33, 34.5, 0.01779, 50, 37.87, 21.25, 0.98221, 1, 50, 55.06, 0.58, 1, 2, 49, 83.25, 1.24, 5e-05, 50, 38.41, -15.21, 0.99995, 2, 49, 69.91, -17.24, 0.13073, 50, 18.85, -26.9, 0.86927, 2, 49, 44.3, -33.03, 0.85151, 50, -10.91, -31.26, 0.14849, 2, 49, 24.34, -37.42, 0.99916, 50, -30.99, -27.39, 0.00084, 1, 49, 34.65, -48.39, 1, 1, 49, 36.07, -72.48, 1, 1, 49, -16.25, -77.33, 1, 2, 49, -17.59, -47.3, 0.88128, 5, 70.28, 86.29, 0.11872, 2, 49, -31.04, -30.99, 0.75343, 5, 79.41, 67.22, 0.24657, 2, 49, -45.97, -12.17, 0.50069, 5, 89.39, 45.37, 0.49931, 1, 5, 95.49, 21.11, 1, 1, 5, 91.58, 15.94, 1, 1, 5, 53.57, 4.07, 1, 1, 5, 20.48, 0.22, 1, 2, 49, 25.49, 49.98, 0.5646, 50, 4.65, 52.42, 0.4354, 2, 49, 46.97, 51.57, 0.2056, 50, 25.02, 45.38, 0.7944, 2, 49, 64.05, 52.54, 0.0464, 50, 41.08, 39.51, 0.9536, 2, 49, 79.5, 45.92, 0.00677, 50, 52.65, 27.31, 0.99323, 1, 49, -0.25, -0.74, 1, 2, 49, 42.59, -0.07, 0.34673, 50, 0.56, -0.32, 0.65327], "edges": [32, 30, 30, 28, 28, 26, 24, 26, 24, 22, 22, 20, 34, 36, 34, 32, 36, 38, 38, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20]}}, "waihoutui01": {"waihoutui01": {"x": 32.9, "y": 17.72, "rotation": 59.64, "width": 247, "height": 396}}, "waihoutui01_zg": {"tx/waihoutui01_zg_0": {"x": -63.37, "y": 95.26, "scaleX": 1.42, "scaleY": 1.42, "rotation": 59.71, "width": 208, "height": 150}, "tx/waihoutui01_zg_1": {"x": -63.37, "y": 95.26, "scaleX": 1.42, "scaleY": 1.42, "rotation": 59.71, "width": 208, "height": 150}, "tx/waihoutui01_zg_2": {"x": -63.37, "y": 95.26, "scaleX": 1.42, "scaleY": 1.42, "rotation": 59.71, "width": 208, "height": 150}, "tx/waihoutui01_zg_3": {"x": -63.37, "y": 95.26, "scaleX": 1.42, "scaleY": 1.42, "rotation": 59.71, "width": 208, "height": 150}, "tx/waihoutui01_zg_4": {"x": -63.37, "y": 95.26, "scaleX": 1.42, "scaleY": 1.42, "rotation": 59.71, "width": 208, "height": 150}, "tx/waihoutui01_zg_5": {"x": -63.37, "y": 95.26, "scaleX": 1.42, "scaleY": 1.42, "rotation": 59.71, "width": 208, "height": 150}, "tx/waihoutui01_zg_6": {"x": -63.37, "y": 95.26, "scaleX": 1.42, "scaleY": 1.42, "rotation": 59.71, "width": 208, "height": 150}, "tx/waihoutui01_zg_7": {"x": -63.37, "y": 95.26, "scaleX": 1.42, "scaleY": 1.42, "rotation": 59.71, "width": 208, "height": 150}, "tx/waihoutui01_zg_8": {"x": -63.37, "y": 95.26, "scaleX": 1.42, "scaleY": 1.42, "rotation": 59.71, "width": 208, "height": 150}, "tx/waihoutui01_zg_9": {"x": -63.37, "y": 95.26, "scaleX": 1.42, "scaleY": 1.42, "rotation": 59.71, "width": 208, "height": 150}, "tx/waihoutui01_zg_10": {"x": -63.37, "y": 95.26, "scaleX": 1.42, "scaleY": 1.42, "rotation": 59.71, "width": 208, "height": 150}}, "waihoutui02": {"waihoutui02": {"x": 49.41, "y": 9.06, "rotation": 153.85, "width": 176, "height": 73}}, "waihoutui03": {"waihoutui03": {"type": "mesh", "hull": 14, "width": 169, "height": 257, "uvs": [0.65423, 0.19214, 0.79721, 0.30994, 1, 0.49893, 1, 0.85995, 1, 1, 0.69681, 1, 0.3982, 0.95749, 0.34566, 0.88617, 0.46537, 0.63892, 0.23133, 0.48504, 0, 0.39541, 0, 0, 0.33557, 0, 0.55175, 0, 0.42749, 0.2799, 0.70895, 0.85191], "triangles": [1, 8, 14, 1, 14, 0, 8, 1, 2, 9, 14, 8, 15, 8, 2, 15, 2, 3, 7, 8, 15, 6, 7, 15, 5, 6, 15, 15, 3, 4, 5, 15, 4, 14, 12, 13, 14, 13, 0, 14, 10, 11, 14, 11, 12, 9, 10, 14], "vertices": [2, 14, 112.04, 28.03, 0.42813, 15, -11.15, 41.04, 0.57187, 2, 14, 140.57, 1.83, 0.11402, 15, 24.91, 55.17, 0.88598, 2, 14, 181.9, -40.9, 0.00093, 15, 81.44, 73.56, 0.99907, 1, 15, 170.08, 46.13, 1, 1, 15, 204.46, 35.5, 1, 1, 15, 189.32, -13.45, 1, 1, 15, 163.97, -58.44, 1, 2, 14, 87.93, -156.23, 0.00112, 15, 143.83, -61.5, 0.99888, 2, 14, 98.15, -90.33, 0.14435, 15, 89.11, -23.39, 0.85565, 2, 14, 52.99, -57.34, 0.44952, 15, 39.64, -49.49, 0.55048, 2, 14, 10.82, -40.58, 0.78173, 15, 6.08, -80.03, 0.21827, 2, 14, -4.8, 59.83, 0.97104, 15, -91, -49.99, 0.02896, 2, 14, 51.24, 68.55, 0.97999, 15, -74.24, 4.18, 0.02001, 2, 14, 87.34, 74.16, 0.75974, 15, -63.44, 39.09, 0.24026, 2, 14, 77.64, -0.14, 0.88895, 15, -0.93, -2.24, 0.11105, 1, 15, 153.57, -0.24, 1], "edges": [22, 24, 24, 26, 20, 22, 2, 4, 18, 16, 16, 14, 14, 12, 8, 10, 12, 10, 4, 6, 6, 8, 26, 0, 0, 2, 20, 18]}}, "waiqiantui01": {"waiqiantui01": {"x": 101.6, "y": 8.77, "rotation": 156.46, "width": 282, "height": 137}}, "waiqiantui02": {"waiqiantui02": {"x": 103.34, "y": 17.8, "rotation": 114.29, "width": 255, "height": 297}}, "waiqiantui02_zg": {"tx/waiqiantui02_zg_0": {"x": 4.6, "y": 77.74, "scaleX": 1.42, "scaleY": 1.42, "rotation": 114.31, "width": 65, "height": 167}, "tx/waiqiantui02_zg_1": {"x": 4.6, "y": 77.74, "scaleX": 1.42, "scaleY": 1.42, "rotation": 114.31, "width": 65, "height": 167}, "tx/waiqiantui02_zg_2": {"x": 4.6, "y": 77.74, "scaleX": 1.42, "scaleY": 1.42, "rotation": 114.31, "width": 65, "height": 167}, "tx/waiqiantui02_zg_3": {"x": 4.6, "y": 77.74, "scaleX": 1.42, "scaleY": 1.42, "rotation": 114.31, "width": 65, "height": 167}, "tx/waiqiantui02_zg_4": {"x": 4.6, "y": 77.74, "scaleX": 1.42, "scaleY": 1.42, "rotation": 114.31, "width": 65, "height": 167}, "tx/waiqiantui02_zg_5": {"x": 4.6, "y": 77.74, "scaleX": 1.42, "scaleY": 1.42, "rotation": 114.31, "width": 65, "height": 167}, "tx/waiqiantui02_zg_6": {"x": 4.6, "y": 77.74, "scaleX": 1.42, "scaleY": 1.42, "rotation": 114.31, "width": 65, "height": 167}, "tx/waiqiantui02_zg_7": {"x": 4.6, "y": 77.74, "scaleX": 1.42, "scaleY": 1.42, "rotation": 114.31, "width": 65, "height": 167}, "tx/waiqiantui02_zg_8": {"x": 4.6, "y": 77.74, "scaleX": 1.42, "scaleY": 1.42, "rotation": 114.31, "width": 65, "height": 167}, "tx/waiqiantui02_zg_9": {"x": 4.6, "y": 77.74, "scaleX": 1.42, "scaleY": 1.42, "rotation": 114.31, "width": 65, "height": 167}}, "waiqiantui03": {"waiqiantui03": {"x": 161.81, "y": 14.19, "rotation": 35.8, "width": 215, "height": 170}}, "weiba01": {"weiba01": {"type": "mesh", "hull": 32, "width": 527, "height": 336, "uvs": [0.21711, 0.16621, 0.29105, 0.29923, 0.3798, 0.43057, 0.42534, 0.40502, 0.4361, 0.49108, 0.48236, 0.59233, 0.55159, 0.64715, 0.61665, 0.69042, 0.66466, 0.77256, 0.74839, 0.79211, 0.80443, 0.83056, 0.87077, 0.83878, 0.93649, 0.83303, 0.96652, 0.83147, 1, 0.85961, 1, 0.90428, 0.98551, 0.95144, 0.96258, 0.96946, 0.88386, 1, 0.80269, 1, 0.72447, 1, 0.62713, 0.97381, 0.54508, 0.92617, 0.46849, 0.88432, 0.39042, 0.83382, 0.31731, 0.78169, 0.24147, 0.72147, 0.13557, 0.62154, 0.03642, 0.45425, 0, 0.28218, 0, 0, 0.08555, 0], "triangles": [29, 30, 31, 29, 31, 0, 13, 14, 15, 16, 13, 15, 17, 12, 13, 17, 13, 16, 20, 9, 10, 19, 20, 10, 19, 10, 11, 18, 11, 12, 18, 12, 17, 19, 11, 18, 22, 23, 6, 22, 6, 7, 22, 7, 8, 21, 22, 8, 20, 8, 9, 21, 8, 20, 2, 3, 4, 25, 26, 2, 25, 2, 4, 25, 4, 5, 24, 25, 5, 23, 24, 5, 23, 5, 6, 28, 29, 0, 28, 0, 1, 27, 28, 1, 27, 1, 2, 26, 27, 2], "vertices": [2, 21, 52.09, 70.4, 0.90454, 22, -114.03, 100.04, 0.09546, 2, 21, 111.25, 66.35, 0.75633, 22, -57.49, 82.19, 0.24366, 3, 21, 175.52, 68.22, 0.5574, 22, 5.42, 68.88, 0.43969, 23, -98.48, 92.31, 0.00291, 3, 21, 186.42, 91.26, 0.35123, 22, 21.43, 88.71, 0.6174, 23, -78.57, 108.22, 0.03137, 3, 21, 210.88, 74.82, 0.19043, 22, 41.33, 66.98, 0.68853, 23, -63.82, 82.7, 0.12103, 4, 21, 252.17, 68.01, 0.08562, 22, 79.87, 50.63, 0.61415, 23, -29.73, 58.43, 0.29939, 24, -133.86, 105.06, 0.00085, 4, 21, 291, 80.78, 0.03031, 22, 120.6, 53.91, 0.4316, 23, 10.76, 52.84, 0.51374, 24, -97.1, 87.2, 0.02435, 5, 21, 325.52, 94.75, 0.00698, 22, 157.45, 59.36, 0.22839, 23, 47.91, 50.21, 0.65776, 24, -62.59, 73.19, 0.10681, 25, -92.28, 168.77, 6e-05, 5, 21, 362.93, 93.12, 0.00027, 22, 193.42, 48.98, 0.08524, 23, 80.79, 32.3, 0.6316, 24, -36.87, 45.98, 0.27827, 25, -89.76, 131.42, 0.00462, 4, 22, 234.51, 66.36, 0.01849, 23, 124.66, 40.4, 0.45746, 24, 7.35, 40.1, 0.47834, 25, -59.41, 98.72, 0.04571, 4, 22, 266.45, 70.71, 0.00136, 23, 156.79, 37.76, 0.24035, 24, 37.08, 27.63, 0.5981, 25, -44.42, 70.18, 0.16018, 3, 23, 190.76, 46.48, 0.08593, 24, 72.08, 25.41, 0.54233, 25, -18.84, 46.19, 0.37174, 3, 23, 222.89, 59.54, 0.01725, 24, 106.68, 27.87, 0.36949, 25, 9.41, 26.06, 0.61325, 3, 23, 237.69, 65.17, 0.00067, 24, 122.49, 28.64, 0.21909, 25, 22.09, 16.58, 0.78024, 2, 24, 140.28, 19.46, 0.22509, 25, 29.97, -1.82, 0.77491, 2, 24, 140.51, 4.45, 0.39344, 25, 20.59, -13.54, 0.60656, 2, 24, 133.12, -11.51, 0.63312, 25, 4.73, -21.15, 0.36688, 3, 23, 250.77, 20.64, 0.01825, 24, 121.13, -17.75, 0.81922, 25, -8.49, -18.33, 0.16254, 3, 23, 214.85, -2.52, 0.09178, 24, 79.81, -28.65, 0.85994, 25, -47.29, -0.42, 0.04829, 4, 22, 295.32, 21.63, 9e-05, 23, 174.38, -16.4, 0.25754, 24, 37.04, -29.31, 0.73592, 25, -80.7, 26.3, 0.00645, 3, 22, 260.13, 0.16, 0.01911, 23, 135.39, -29.76, 0.47898, 24, -4.18, -29.94, 0.50191, 3, 22, 211.76, -19.05, 0.09398, 23, 84.01, -38.08, 0.64513, 24, -55.61, -21.93, 0.26089, 4, 21, 354.86, 12.06, 0.00077, 22, 166.51, -27.9, 0.26068, 23, 37.92, -36.96, 0.64514, 24, -99.09, -6.59, 0.09341, 4, 21, 316.38, -6.54, 0.02141, 22, 124.73, -36.92, 0.48062, 23, -4.83, -36.75, 0.47917, 24, -139.66, 6.85, 0.01879, 3, 21, 275.29, -23.63, 0.09885, 22, 80.77, -43.87, 0.6432, 23, -49.25, -34.04, 0.25795, 3, 21, 235.66, -38.49, 0.26779, 22, 38.76, -48.98, 0.63989, 23, -91.37, -29.97, 0.09231, 3, 21, 193.09, -52.44, 0.50801, 22, -5.9, -52.53, 0.4734, 23, -135.74, -23.79, 0.01859, 3, 21, 129.88, -68.17, 0.74575, 22, -71.03, -52.94, 0.25412, 23, -199.43, -10.13, 0.00013, 2, 21, 53.19, -65.37, 0.90925, 22, -144.91, -32.17, 0.09075, 1, 3, -56.83, 76.25, 1, 1, 3, -51.46, -18.41, 1, 1, 3, -96.47, -20.96, 1], "edges": [58, 60, 56, 58, 60, 62, 0, 62, 56, 54, 0, 2, 2, 4, 54, 52, 8, 6, 6, 4, 38, 40, 36, 38, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24]}}, "weiba02": {"weiba02": {"type": "mesh", "hull": 35, "width": 120, "height": 330, "uvs": [0.12557, 0.0243, 0.29893, 0.07471, 0.44237, 0.12898, 0.57744, 0.1798, 0.69249, 0.24212, 0.82509, 0.31383, 0.93899, 0.39234, 1, 0.47269, 1, 0.56052, 0.97835, 0.65701, 0.91715, 0.73799, 0.83002, 0.81232, 0.70698, 0.87586, 0.56928, 0.93463, 0.38738, 0.981, 0.25138, 1, 0.15448, 0.98471, 0.10008, 0.94205, 0.14428, 0.89136, 0.24458, 0.87714, 0.37548, 0.85427, 0.50808, 0.81837, 0.61132, 0.76784, 0.69615, 0.70843, 0.72335, 0.64476, 0.74035, 0.57057, 0.72982, 0.48272, 0.68569, 0.42016, 0.60069, 0.34474, 0.49699, 0.27179, 0.40234, 0.21627, 0.30779, 0.16404, 0.17313, 0.09882, 0.05247, 0.04532, 0, 0], "triangles": [12, 22, 11, 21, 22, 12, 13, 21, 12, 20, 21, 13, 14, 20, 13, 19, 20, 14, 19, 17, 18, 14, 17, 19, 17, 15, 16, 14, 15, 17, 26, 7, 8, 25, 26, 8, 9, 25, 8, 24, 25, 9, 9, 23, 24, 10, 23, 9, 11, 23, 10, 22, 23, 11, 33, 34, 0, 32, 0, 1, 33, 0, 32, 31, 1, 2, 32, 1, 31, 30, 31, 2, 30, 2, 3, 29, 30, 3, 29, 3, 4, 28, 29, 4, 28, 4, 5, 27, 28, 5, 27, 5, 6, 26, 27, 6, 7, 26, 6], "vertices": [2, 27, 174.83, 2.83, 0.06144, 28, 49.87, -7.25, 0.93856, 2, 27, 149.93, -6.63, 0.23399, 28, 23.58, -11.56, 0.76601, 2, 27, 125.76, -12.35, 0.51765, 28, -1.25, -12.35, 0.48235, 2, 27, 103.08, -17.79, 0.78954, 28, -24.55, -13.16, 0.21046, 2, 27, 78.35, -19.23, 0.95033, 28, -49.07, -9.64, 0.04967, 2, 27, 49.88, -20.9, 0.93396, 26, 156.8, 16.2, 0.06604, 2, 27, 20.63, -19.51, 0.7568, 26, 133.54, -1.6, 0.2432, 2, 27, -5.93, -12.33, 0.46853, 26, 108.62, -13.25, 0.53147, 2, 27, -30.89, 2.4, 0.20124, 26, 80.05, -18.09, 0.79876, 3, 27, -57, 20.82, 0.04507, 26, 48.22, -20.85, 0.89526, 25, 139.92, 16.52, 0.05967, 2, 26, 20.64, -18.08, 0.76955, 25, 117.49, 0.24, 0.23045, 2, 26, -5.29, -11.87, 0.48766, 25, 94.01, -12.38, 0.51234, 2, 26, -28.43, -0.82, 0.21399, 25, 69.38, -19.53, 0.78601, 2, 26, -50.31, 12.23, 0.05144, 25, 44.36, -24.35, 0.94856, 1, 25, 17.76, -22.66, 1, 1, 25, 1.1, -17.36, 1, 1, 25, -4.83, -6.16, 1, 1, 25, -1.14, 8.91, 1, 1, 25, 13.45, 18.66, 1, 1, 25, 25.78, 14.81, 1, 2, 26, -28.05, 39.6, 0.04471, 25, 42.77, 10.89, 0.95529, 2, 26, -13.71, 25.89, 0.20039, 25, 62.59, 10.2, 0.79961, 2, 26, 4.8, 16.46, 0.46718, 25, 82.68, 15.48, 0.53282, 2, 26, 25.83, 9.7, 0.75566, 25, 102.88, 24.43, 0.24434, 3, 27, -37.96, 45.12, 0.06506, 26, 47.09, 9.99, 0.86826, 25, 118.55, 38.8, 0.06668, 3, 27, -17.91, 30.92, 0.24117, 26, 71.57, 12.07, 0.75869, 25, 135.44, 56.64, 0.00014, 2, 27, 7.7, 17.27, 0.5284, 26, 99.94, 18.16, 0.4716, 2, 27, 28.17, 11.34, 0.79662, 26, 119.41, 26.83, 0.20338, 2, 27, 54.79, 7.47, 0.95384, 26, 142.25, 41.05, 0.04616, 3, 27, 81.84, 5.95, 0.95453, 28, -40.64, 14.34, 0.04542, 26, 163.9, 57.34, 5e-05, 2, 27, 103.39, 6.42, 0.79806, 28, -19.42, 10.51, 0.20194, 2, 27, 124, 7.44, 0.53042, 28, 0.97, 7.39, 0.46958, 2, 27, 150.75, 10.41, 0.2425, 28, 27.78, 4.98, 0.7575, 2, 27, 173.32, 13.91, 0.0657, 28, 50.59, 3.91, 0.9343, 1, 28, 65.91, -1.43, 1], "edges": [38, 36, 36, 34, 34, 32, 32, 30, 30, 28, 22, 24, 24, 26, 26, 28, 38, 40, 40, 42, 42, 44, 14, 16, 52, 50, 50, 48, 48, 46, 46, 44, 16, 18, 18, 20, 20, 22, 68, 66, 68, 0, 0, 2, 2, 4, 66, 64, 64, 62, 62, 60, 4, 6, 6, 8, 8, 10, 60, 58, 58, 56, 56, 54, 10, 12, 12, 14, 54, 52]}}, "xz": {"tx/xz": {"width": 258, "height": 258}}, "yinying": {"yingying": {"scaleX": 8, "scaleY": 3, "rotation": 0.15, "width": 100, "height": 34}}, "zk1": {"tx/x": {"y": 0.5, "width": 523, "height": 523}}, "zk2": {"tx/x": {"y": 0.5, "width": 523, "height": 523}}}}], "events": {"attack": {}}, "animations": {"chuchang": {"slots": {"bo": {"color": [{"time": 1.6, "color": "ffffffff"}, {"time": 1.8, "color": "ffffff02"}, {"time": 1.8667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.9667, "color": "ffffffff"}, {"time": 2.1667, "color": "ffffff02"}, {"time": 2.6, "color": "ffffffff"}], "attachment": [{"time": 1.5, "name": "tx/ks01"}, {"time": 1.8, "name": null}, {"time": 1.8667, "name": "tx/ks01"}, {"time": 2.1667, "name": null}]}, "bo2": {"color": [{"time": 1.5667, "color": "ffffffff"}, {"time": 1.7, "color": "ffffff02"}, {"time": 1.8667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.9333, "color": "ffffffff"}, {"time": 2.0667, "color": "ffffff02"}, {"time": 2.6, "color": "ffffffff"}], "attachment": [{"time": 1.5, "name": "tx/ks01"}, {"time": 1.7, "name": null}, {"time": 1.8667, "name": "tx/ks01"}, {"time": 2.0667, "name": null}]}, "chibang01": {"color": [{"color": "ffffff00"}, {"time": 0.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4667, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}], "attachment": [{"time": 2.6, "name": null}]}, "chibang02": {"color": [{"color": "ffffff00"}, {"time": 0.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4667, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}], "attachment": [{"time": 2.6, "name": null}]}, "fire": {"color": [{"time": 2.4667, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}], "attachment": [{"name": "tx/Fire-Loop_1"}, {"time": 0.0333, "name": "tx/Fire-Loop_3"}, {"time": 0.1, "name": "tx/Fire-Loop_5"}, {"time": 0.1667, "name": "tx/Fire-Loop_7"}, {"time": 0.2, "name": "tx/Fire-Loop_9"}, {"time": 0.2667, "name": "tx/Fire-Loop_11"}, {"time": 0.3333, "name": "tx/Fire-Loop_13"}, {"time": 0.3667, "name": "tx/Fire-Loop_15"}, {"time": 0.4333, "name": "tx/Fire-Loop_17"}, {"time": 0.4667, "name": "tx/Fire-Loop_19"}, {"time": 0.5333, "name": "tx/Fire-Loop_21"}, {"time": 0.5667, "name": "tx/Fire-Loop_23"}, {"time": 0.6333, "name": "tx/Fire-Loop_25"}, {"time": 0.7, "name": "tx/Fire-Loop_27"}, {"time": 0.7333, "name": "tx/Fire-Loop_29"}, {"time": 0.8, "name": "tx/Fire-Loop_31"}, {"time": 0.8333, "name": "tx/Fire-Loop_33"}, {"time": 0.9, "name": "tx/Fire-Loop_1"}, {"time": 0.9667, "name": "tx/Fire-Loop_3"}, {"time": 1, "name": "tx/Fire-Loop_5"}, {"time": 1.0667, "name": "tx/Fire-Loop_7"}, {"time": 1.1, "name": "tx/Fire-Loop_9"}, {"time": 1.1667, "name": "tx/Fire-Loop_11"}, {"time": 1.2333, "name": "tx/Fire-Loop_13"}, {"time": 1.2667, "name": "tx/Fire-Loop_15"}, {"time": 1.3333, "name": "tx/Fire-Loop_17"}, {"time": 1.3667, "name": "tx/Fire-Loop_19"}, {"time": 1.4333, "name": "tx/Fire-Loop_21"}, {"time": 1.4667, "name": "tx/Fire-Loop_23"}, {"time": 1.5333, "name": "tx/Fire-Loop_25"}, {"time": 1.6, "name": "tx/Fire-Loop_27"}, {"time": 1.6333, "name": "tx/Fire-Loop_29"}, {"time": 1.7, "name": "tx/Fire-Loop_31"}, {"time": 1.7333, "name": "tx/Fire-Loop_33"}, {"time": 1.8, "name": "tx/Fire-Loop_1"}, {"time": 1.8667, "name": "tx/Fire-Loop_3"}, {"time": 1.9, "name": "tx/Fire-Loop_5"}, {"time": 1.9667, "name": "tx/Fire-Loop_7"}, {"time": 2, "name": "tx/Fire-Loop_9"}, {"time": 2.0667, "name": "tx/Fire-Loop_11"}, {"time": 2.1333, "name": "tx/Fire-Loop_13"}, {"time": 2.1667, "name": "tx/Fire-Loop_15"}, {"time": 2.2333, "name": "tx/Fire-Loop_17"}, {"time": 2.2667, "name": "tx/Fire-Loop_19"}, {"time": 2.3333, "name": "tx/Fire-Loop_21"}, {"time": 2.3667, "name": "tx/Fire-Loop_23"}, {"time": 2.4333, "name": "tx/Fire-Loop_25"}, {"time": 2.5, "name": "tx/Fire-Loop_27"}, {"time": 2.5333, "name": "tx/Fire-Loop_29"}, {"time": 2.6, "name": null}]}, "fire1": {"color": [{"time": 2.4667, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}], "attachment": [{"time": 0.0667, "name": "tx/fire_1"}, {"time": 0.1333, "name": "tx/fire_2"}, {"time": 0.2, "name": "tx/fire_3"}, {"time": 0.2667, "name": "tx/fire_4"}, {"time": 0.3333, "name": "tx/fire_5"}, {"time": 0.4, "name": "tx/fire_6"}, {"time": 0.4667, "name": "tx/fire_7"}, {"time": 0.5333, "name": "tx/fire_8"}, {"time": 0.6, "name": "tx/fire_9"}, {"time": 0.6667, "name": "tx/fire_10"}, {"time": 0.7333, "name": "tx/fire_11"}, {"time": 0.8, "name": "tx/fire_12"}, {"time": 0.8667, "name": "tx/fire_13"}, {"time": 0.9333, "name": "tx/fire_14"}, {"time": 1, "name": "tx/fire_15"}, {"time": 1.0667, "name": "tx/fire_0"}, {"time": 1.1333, "name": "tx/fire_1"}, {"time": 1.2, "name": "tx/fire_2"}, {"time": 1.2667, "name": "tx/fire_3"}, {"time": 1.3333, "name": "tx/fire_4"}, {"time": 1.4, "name": "tx/fire_5"}, {"time": 1.4667, "name": "tx/fire_6"}, {"time": 1.5333, "name": "tx/fire_7"}, {"time": 1.6, "name": "tx/fire_8"}, {"time": 1.6333, "name": "tx/fire_9"}, {"time": 1.7, "name": "tx/fire_5"}, {"time": 1.7333, "name": "tx/fire_11"}, {"time": 1.7667, "name": "tx/fire_12"}, {"time": 1.8, "name": "tx/fire_13"}, {"time": 1.8667, "name": "tx/fire_14"}, {"time": 1.9, "name": "tx/fire_15"}, {"time": 1.9333, "name": "tx/fire_0"}, {"time": 2, "name": "tx/fire_1"}, {"time": 2.0333, "name": "tx/fire_2"}, {"time": 2.0667, "name": "tx/fire_3"}, {"time": 2.1333, "name": "tx/fire_4"}, {"time": 2.1667, "name": "tx/fire_5"}, {"time": 2.2, "name": "tx/fire_6"}, {"time": 2.2333, "name": "tx/fire_7"}, {"time": 2.3, "name": "tx/fire_8"}, {"time": 2.3333, "name": "tx/fire_9"}, {"time": 2.3667, "name": "tx/fire_5"}, {"time": 2.4, "name": "tx/fire_11"}, {"time": 2.4667, "name": "tx/fire_12"}, {"time": 2.5, "name": "tx/fire_13"}, {"time": 2.5333, "name": "tx/fire_14"}, {"time": 2.5667, "name": "tx/fire_15"}, {"time": 2.6, "name": null}]}, "fire2": {"color": [{"color": "ffffffdd", "curve": "stepped"}, {"time": 2.5667, "color": "ffffffdd"}, {"time": 2.6, "color": "ffffff00"}], "attachment": [{"time": 0.0667, "name": "tx/fire_1"}, {"time": 0.1333, "name": "tx/fire_2"}, {"time": 0.2, "name": "tx/fire_3"}, {"time": 0.2667, "name": "tx/fire_4"}, {"time": 0.3333, "name": "tx/fire_5"}, {"time": 0.4, "name": "tx/fire_6"}, {"time": 0.4667, "name": "tx/fire_7"}, {"time": 0.5333, "name": "tx/fire_8"}, {"time": 0.6, "name": "tx/fire_9"}, {"time": 0.6667, "name": "tx/fire_10"}, {"time": 0.7333, "name": "tx/fire_11"}, {"time": 0.8, "name": "tx/fire_12"}, {"time": 0.8667, "name": "tx/fire_13"}, {"time": 0.9333, "name": "tx/fire_14"}, {"time": 1, "name": "tx/fire_15"}, {"time": 1.0667, "name": "tx/fire_0"}, {"time": 1.1333, "name": "tx/fire_1"}, {"time": 1.2, "name": "tx/fire_2"}, {"time": 1.2667, "name": "tx/fire_3"}, {"time": 1.3333, "name": "tx/fire_4"}, {"time": 1.4, "name": "tx/fire_5"}, {"time": 1.4667, "name": "tx/fire_6"}, {"time": 1.5333, "name": "tx/fire_7"}, {"time": 1.6, "name": "tx/fire_8"}, {"time": 1.6333, "name": "tx/fire_9"}, {"time": 1.7, "name": "tx/fire_10"}, {"time": 1.7333, "name": "tx/fire_11"}, {"time": 1.7667, "name": "tx/fire_12"}, {"time": 1.8, "name": "tx/fire_13"}, {"time": 1.8667, "name": "tx/fire_14"}, {"time": 1.9, "name": "tx/fire_15"}, {"time": 1.9333, "name": "tx/fire_0"}, {"time": 2, "name": "tx/fire_1"}, {"time": 2.0333, "name": "tx/fire_2"}, {"time": 2.0667, "name": "tx/fire_3"}, {"time": 2.1333, "name": "tx/fire_4"}, {"time": 2.1667, "name": "tx/fire_5"}, {"time": 2.2, "name": "tx/fire_6"}, {"time": 2.2333, "name": "tx/fire_7"}, {"time": 2.3, "name": "tx/fire_8"}, {"time": 2.3333, "name": "tx/fire_9"}, {"time": 2.3667, "name": "tx/fire_10"}, {"time": 2.4, "name": "tx/fire_11"}, {"time": 2.4667, "name": "tx/fire_12"}, {"time": 2.5, "name": "tx/fire_13"}, {"time": 2.5333, "name": "tx/fire_14"}, {"time": 2.5667, "name": "tx/fire_15"}, {"time": 2.6, "name": null}]}, "hjs": {"attachment": [{"time": 1.4667, "name": "tx/01_00001"}, {"time": 1.5, "name": "tx/01_00002"}, {"time": 1.5333, "name": "tx/01_00003"}, {"time": 1.5667, "name": "tx/01_00004"}, {"time": 1.6, "name": "tx/01_00005"}, {"time": 1.6333, "name": "tx/01_00006"}, {"time": 1.6667, "name": "tx/01_00007"}, {"time": 1.7, "name": "tx/01_00008"}, {"time": 1.7333, "name": "tx/01_00009"}, {"time": 1.7667, "name": "tx/01_00010"}, {"time": 1.8, "name": "tx/01_00011"}, {"time": 1.8333, "name": null}]}, "hjs2": {"attachment": [{"time": 1.5, "name": "tx/01_00001"}, {"time": 1.5333, "name": "tx/01_00002"}, {"time": 1.6, "name": "tx/01_00003"}, {"time": 1.6333, "name": "tx/01_00004"}, {"time": 1.6667, "name": "tx/01_00005"}, {"time": 1.7333, "name": "tx/01_00006"}, {"time": 1.7667, "name": "tx/01_00007"}, {"time": 1.8, "name": "tx/01_00008"}, {"time": 1.8333, "name": "tx/01_00009"}, {"time": 1.9, "name": "tx/01_00010"}, {"time": 1.9333, "name": "tx/01_00011"}, {"time": 1.9667, "name": null}, {"time": 2, "name": "tx/01_00001"}, {"time": 2.0333, "name": "tx/01_00002"}, {"time": 2.0667, "name": "tx/01_00003"}, {"time": 2.1, "name": "tx/01_00004"}, {"time": 2.1333, "name": "tx/01_00005"}, {"time": 2.1667, "name": "tx/01_00006"}, {"time": 2.2, "name": "tx/01_00007"}, {"time": 2.2333, "name": "tx/01_00008"}, {"time": 2.2667, "name": "tx/01_00009"}, {"time": 2.3, "name": "tx/01_00010"}, {"time": 2.3333, "name": "tx/01_00011"}, {"time": 2.4, "name": null}]}, "hjs3": {"attachment": [{"time": 1.5667, "name": "tx/01_00001"}, {"time": 1.6, "name": "tx/01_00002"}, {"time": 1.6333, "name": "tx/01_00003"}, {"time": 1.6667, "name": "tx/01_00004"}, {"time": 1.7, "name": "tx/01_00005"}, {"time": 1.7333, "name": "tx/01_00006"}, {"time": 1.7667, "name": "tx/01_00007"}, {"time": 1.8, "name": "tx/01_00008"}, {"time": 1.8333, "name": "tx/01_00009"}, {"time": 1.8667, "name": "tx/01_00010"}, {"time": 1.9, "name": "tx/01_00011"}, {"time": 1.9333, "name": null}]}, "hjs4": {"attachment": [{"time": 1.5667, "name": "tx/01_00001"}, {"time": 1.6, "name": "tx/01_00002"}, {"time": 1.6333, "name": "tx/01_00003"}, {"time": 1.6667, "name": "tx/01_00004"}, {"time": 1.7, "name": "tx/01_00005"}, {"time": 1.7333, "name": "tx/01_00006"}, {"time": 1.7667, "name": "tx/01_00007"}, {"time": 1.8, "name": "tx/01_00008"}, {"time": 1.8333, "name": "tx/01_00009"}, {"time": 1.8667, "name": "tx/01_00010"}, {"time": 1.9, "name": "tx/01_00011"}, {"time": 1.9333, "name": null}, {"time": 1.9667, "name": "tx/01_00001"}, {"time": 2, "name": "tx/01_00002"}, {"time": 2.0333, "name": "tx/01_00003"}, {"time": 2.0667, "name": "tx/01_00004"}, {"time": 2.1, "name": "tx/01_00005"}, {"time": 2.1333, "name": "tx/01_00006"}, {"time": 2.1667, "name": "tx/01_00007"}, {"time": 2.2, "name": "tx/01_00008"}, {"time": 2.2333, "name": "tx/01_00009"}, {"time": 2.2667, "name": "tx/01_00010"}, {"time": 2.3, "name": "tx/01_00011"}, {"time": 2.3333, "name": null}]}, "huo2": {"color": [{"time": 2.4667, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}]}, "huo3": {"color": [{"time": 2.4667, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}]}, "huo4": {"color": [{"time": 2.4667, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}]}, "jianjia01": {"color": [{"color": "ffffff00"}, {"time": 0.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4667, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}], "attachment": [{"time": 2.6, "name": null}]}, "jiao01": {"color": [{"color": "ffffff00"}, {"time": 0.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4667, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}], "attachment": [{"time": 2.6, "name": null}]}, "jiao1_g": {"color": [{"time": 2.4667, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}], "attachment": [{"time": 1.4, "name": "tx/jiao01_g_12"}, {"time": 1.4333, "name": "tx/jiao01_g_11"}, {"time": 1.4667, "name": "tx/jiao01_g_10"}, {"time": 1.5, "name": "tx/jiao01_g_9"}, {"time": 1.5333, "name": "tx/jiao01_g_8"}, {"time": 1.5667, "name": "tx/jiao01_g_7"}, {"time": 1.6, "name": "tx/jiao01_g_6"}, {"time": 1.6333, "name": "tx/jiao01_g_5"}, {"time": 1.6667, "name": "tx/jiao01_g_4"}, {"time": 1.7, "name": "tx/jiao01_g_3"}, {"time": 1.7333, "name": "tx/jiao01_g_2"}, {"time": 1.7667, "name": "tx/jiao01_g_1"}, {"time": 1.8, "name": "tx/jiao01_g_0"}, {"time": 1.8333, "name": "tx/jiao01_g_12"}, {"time": 1.8667, "name": "tx/jiao01_g_11"}, {"time": 1.9, "name": "tx/jiao01_g_10"}, {"time": 1.9333, "name": "tx/jiao01_g_9"}, {"time": 1.9667, "name": "tx/jiao01_g_8"}, {"time": 2, "name": "tx/jiao01_g_7"}, {"time": 2.0333, "name": "tx/jiao01_g_6"}, {"time": 2.0667, "name": "tx/jiao01_g_5"}, {"time": 2.1, "name": "tx/jiao01_g_4"}, {"time": 2.1333, "name": "tx/jiao01_g_3"}, {"time": 2.1667, "name": "tx/jiao01_g_2"}, {"time": 2.2, "name": "tx/jiao01_g_1"}, {"time": 2.2333, "name": "tx/jiao01_g_0"}, {"time": 2.2667, "name": null}]}, "jiao02": {"color": [{"color": "ffffff00"}, {"time": 0.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4667, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}], "attachment": [{"time": 2.6, "name": null}]}, "jiao2_g": {"color": [{"time": 2.4667, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}], "attachment": [{"time": 1.4, "name": "tx/jiao01_g_12"}, {"time": 1.4333, "name": "tx/jiao01_g_11"}, {"time": 1.4667, "name": "tx/jiao01_g_10"}, {"time": 1.5, "name": "tx/jiao01_g_9"}, {"time": 1.5333, "name": "tx/jiao01_g_8"}, {"time": 1.5667, "name": "tx/jiao01_g_7"}, {"time": 1.6, "name": "tx/jiao01_g_6"}, {"time": 1.6333, "name": "tx/jiao01_g_5"}, {"time": 1.6667, "name": "tx/jiao01_g_4"}, {"time": 1.7, "name": "tx/jiao01_g_3"}, {"time": 1.7333, "name": "tx/jiao01_g_2"}, {"time": 1.7667, "name": "tx/jiao01_g_1"}, {"time": 1.8, "name": "tx/jiao01_g_0"}, {"time": 1.8333, "name": "tx/jiao01_g_12"}, {"time": 1.8667, "name": "tx/jiao01_g_11"}, {"time": 1.9, "name": "tx/jiao01_g_10"}, {"time": 1.9333, "name": "tx/jiao01_g_9"}, {"time": 1.9667, "name": "tx/jiao01_g_8"}, {"time": 2, "name": "tx/jiao01_g_7"}, {"time": 2.0333, "name": "tx/jiao01_g_6"}, {"time": 2.0667, "name": "tx/jiao01_g_5"}, {"time": 2.1, "name": "tx/jiao01_g_4"}, {"time": 2.1333, "name": "tx/jiao01_g_3"}, {"time": 2.1667, "name": "tx/jiao01_g_2"}, {"time": 2.2, "name": "tx/jiao01_g_1"}, {"time": 2.2333, "name": "tx/jiao01_g_0"}, {"time": 2.2667, "name": null}]}, "jm": {"color": [{"time": 2.4667, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}], "attachment": [{"time": 1.4, "name": "tx/jm1_12"}, {"time": 1.4333, "name": "tx/jm1_11"}, {"time": 1.4667, "name": "tx/jm1_10"}, {"time": 1.5, "name": "tx/jm1_9"}, {"time": 1.5333, "name": "tx/jm1_8"}, {"time": 1.5667, "name": "tx/jm1_7"}, {"time": 1.6, "name": "tx/jm1_6"}, {"time": 1.6333, "name": "tx/jm1_5"}, {"time": 1.6667, "name": "tx/jm1_4"}, {"time": 1.7, "name": "tx/jm1_3"}, {"time": 1.7333, "name": "tx/jm1_2"}, {"time": 1.7667, "name": "tx/jm1_1"}, {"time": 1.8, "name": "tx/jm1_0"}, {"time": 1.8333, "name": "tx/jm1_12"}, {"time": 1.8667, "name": "tx/jm1_11"}, {"time": 1.9, "name": "tx/jm1_10"}, {"time": 1.9333, "name": "tx/jm1_9"}, {"time": 1.9667, "name": "tx/jm1_8"}, {"time": 2, "name": "tx/jm1_7"}, {"time": 2.0333, "name": "tx/jm1_6"}, {"time": 2.0667, "name": "tx/jm1_5"}, {"time": 2.1, "name": "tx/jm1_4"}, {"time": 2.1333, "name": "tx/jm1_3"}, {"time": 2.1667, "name": "tx/jm1_2"}, {"time": 2.2, "name": "tx/jm1_1"}, {"time": 2.2333, "name": "tx/jm1_0"}, {"time": 2.2667, "name": null}]}, "ju": {"attachment": [{"time": 0.5, "name": "tx/j_0"}, {"time": 0.5333, "name": "tx/j_2"}, {"time": 0.6, "name": "tx/j_4"}, {"time": 0.6667, "name": "tx/j_6"}, {"time": 0.7333, "name": "tx/j_8"}, {"time": 0.8, "name": "tx/j_10"}, {"time": 0.8667, "name": "tx/j_12"}, {"time": 0.9333, "name": "tx/j_14"}, {"time": 1, "name": "tx/j_16"}, {"time": 1.0667, "name": "tx/j_18"}, {"time": 1.1333, "name": "tx/j_20"}, {"time": 1.2, "name": "tx/j_22"}, {"time": 1.2667, "name": "tx/j_24"}, {"time": 1.3333, "name": "tx/j_26"}, {"time": 1.4, "name": null}]}, "ju2": {"attachment": [{"time": 0.5, "name": "tx/j_0"}, {"time": 0.5333, "name": "tx/j_2"}, {"time": 0.6, "name": "tx/j_4"}, {"time": 0.6667, "name": "tx/j_6"}, {"time": 0.7333, "name": "tx/j_8"}, {"time": 0.8, "name": "tx/j_10"}, {"time": 0.8667, "name": "tx/j_12"}, {"time": 0.9333, "name": "tx/j_14"}, {"time": 1, "name": "tx/j_16"}, {"time": 1.0667, "name": "tx/j_18"}, {"time": 1.1333, "name": "tx/j_20"}, {"time": 1.2, "name": "tx/j_22"}, {"time": 1.2667, "name": "tx/j_24"}, {"time": 1.3333, "name": "tx/j_26"}, {"time": 1.4, "name": null}]}, "lz": {"color": [{"color": "ffffffff"}, {"time": 1.4333, "color": "000000ff", "curve": "stepped"}, {"time": 2, "color": "000000ff"}, {"time": 2.5667, "color": "00000000"}, {"time": 2.6, "color": "ffffffff"}], "attachment": [{"time": 1.4333, "name": "tx/lz"}, {"time": 2.5667, "name": null}]}, "maofa01": {"color": [{"color": "ffffff00"}, {"time": 0.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4667, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}], "attachment": [{"time": 2.6, "name": null}]}, "maofa02": {"color": [{"color": "ffffff00"}, {"time": 0.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4667, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}], "attachment": [{"time": 2.6, "name": null}]}, "maofa03": {"color": [{"color": "ffffff00"}, {"time": 0.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4667, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}], "attachment": [{"time": 2.6, "name": null}]}, "maofa04": {"color": [{"color": "ffffff00"}, {"time": 0.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4667, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}], "attachment": [{"time": 2.6, "name": null}]}, "maofa05": {"color": [{"color": "ffffff00"}, {"time": 0.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4667, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}], "attachment": [{"time": 2.6, "name": null}]}, "maofa06": {"color": [{"color": "ffffff00"}, {"time": 0.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4667, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}], "attachment": [{"time": 2.6, "name": null}]}, "maofa07": {"color": [{"color": "ffffff00"}, {"time": 0.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4667, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}], "attachment": [{"time": 2.6, "name": null}]}, "maofa08": {"color": [{"color": "ffffff00"}, {"time": 0.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4667, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}], "attachment": [{"time": 2.6, "name": null}]}, "neihoutui01": {"color": [{"color": "ffffff00"}, {"time": 0.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4667, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}], "attachment": [{"time": 2.6, "name": null}]}, "neihoutui02": {"color": [{"color": "ffffff00"}, {"time": 0.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4667, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}], "attachment": [{"time": 2.6, "name": null}]}, "neihoutui03": {"color": [{"color": "ffffff00"}, {"time": 0.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4667, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}], "attachment": [{"time": 2.6, "name": null}]}, "neiqiantui01": {"color": [{"color": "ffffff00"}, {"time": 0.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4667, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}], "attachment": [{"time": 2.6, "name": null}]}, "neiqiantui02": {"color": [{"color": "ffffff00"}, {"time": 0.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4667, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}], "attachment": [{"time": 2.6, "name": null}]}, "neiqiantui03": {"color": [{"color": "ffffff00"}, {"time": 0.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4667, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}], "attachment": [{"time": 2.6, "name": null}]}, "qq": {"color": [{"color": "b1b1b1c7", "curve": "stepped"}, {"time": 0.3667, "color": "b1b1b1c7"}, {"time": 0.7, "color": "b1b1b100"}, {"time": 2.6, "color": "b1b1b1c7"}], "attachment": [{"time": 0.1667, "name": "tx/q (1)"}, {"time": 0.2, "name": "tx/q (2)"}, {"time": 0.2333, "name": "tx/q (3)"}, {"time": 0.2667, "name": "tx/q (4)"}, {"time": 0.3333, "name": "tx/q (5)"}, {"time": 0.4, "name": "tx/q (6)"}, {"time": 0.4667, "name": "tx/q (7)"}, {"time": 0.5333, "name": "tx/q (8)"}, {"time": 0.6333, "name": "tx/q (9)"}, {"time": 0.7, "name": null}]}, "shent01": {"color": [{"color": "ffffff00"}, {"time": 0.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4667, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}], "attachment": [{"time": 2.6, "name": null}]}, "shenti02": {"color": [{"color": "ffffff00"}, {"time": 0.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4667, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}], "attachment": [{"time": 2.6, "name": null}]}, "tou02": {"color": [{"color": "ffffff00"}, {"time": 0.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4667, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}], "attachment": [{"time": 2.6, "name": null}]}, "tou03": {"color": [{"color": "ffffff00"}, {"time": 0.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4667, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}], "attachment": [{"time": 2.6, "name": null}]}, "tou04": {"color": [{"color": "ffffff00"}, {"time": 0.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4667, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}], "attachment": [{"time": 2.6, "name": null}]}, "tou05": {"color": [{"color": "ffffff00"}, {"time": 0.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4667, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}], "attachment": [{"time": 2.6, "name": null}]}, "waihoutui01": {"color": [{"color": "ffffff00"}, {"time": 0.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4667, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}], "attachment": [{"time": 2.6, "name": null}]}, "waihoutui01_zg": {"color": [{"time": 2.4667, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}]}, "waihoutui02": {"color": [{"color": "ffffff00"}, {"time": 0.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4667, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}], "attachment": [{"time": 2.6, "name": null}]}, "waihoutui03": {"color": [{"color": "ffffff00"}, {"time": 0.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4667, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}], "attachment": [{"time": 2.6, "name": null}]}, "waiqiantui01": {"color": [{"color": "ffffff00"}, {"time": 0.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4667, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}], "attachment": [{"time": 2.6, "name": null}]}, "waiqiantui02": {"color": [{"color": "ffffff00"}, {"time": 0.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4667, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}], "attachment": [{"time": 2.6, "name": null}]}, "waiqiantui02_zg": {"color": [{"time": 2.4667, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}]}, "waiqiantui03": {"color": [{"color": "ffffff00"}, {"time": 0.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4667, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}], "attachment": [{"time": 2.6, "name": null}]}, "weiba01": {"color": [{"color": "ffffff00"}, {"time": 0.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4667, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}], "attachment": [{"time": 2.6, "name": null}]}, "weiba02": {"color": [{"color": "ffffff00"}, {"time": 0.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4667, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}], "attachment": [{"time": 2.6, "name": null}]}, "xz": {"color": [{"time": 1.9667, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffff00"}, {"time": 2.6, "color": "ffffffff"}], "attachment": [{"time": 1.5333, "name": "tx/xz"}, {"time": 2.3333, "name": null}]}, "yinying": {"color": [{"color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4667, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}], "attachment": [{"time": 2.6, "name": null}]}, "zk1": {"color": [{"color": "ffffffff"}, {"time": 1.4667, "color": "ffcf6fff", "curve": "stepped"}, {"time": 1.5333, "color": "ffcf6fff"}, {"time": 1.6667, "color": "ffcf6f00"}, {"time": 1.7333, "color": "ffcf6fff", "curve": "stepped"}, {"time": 1.8, "color": "ffcf6fff"}, {"time": 1.9333, "color": "ffcf6f00"}, {"time": 2, "color": "ffcf6fff", "curve": "stepped"}, {"time": 2.0667, "color": "ffcf6fff"}, {"time": 2.2, "color": "ffcf6f00"}, {"time": 2.6, "color": "ffffffff"}], "attachment": [{"time": 1.4667, "name": "tx/x"}, {"time": 1.6667, "name": null}, {"time": 1.7333, "name": "tx/x"}, {"time": 1.9333, "name": null}, {"time": 2, "name": "tx/x"}, {"time": 2.2, "name": null}]}, "zk2": {"color": [{"color": "ffffffff"}, {"time": 1.4667, "color": "fff4c2ff", "curve": "stepped"}, {"time": 1.5667, "color": "fff4c2ff"}, {"time": 1.7, "color": "fff4c207"}, {"time": 1.7333, "color": "fff4c2ff", "curve": "stepped"}, {"time": 1.8333, "color": "fff4c2ff"}, {"time": 1.9667, "color": "fff4c207"}, {"time": 2, "color": "fff4c2ff", "curve": "stepped"}, {"time": 2.1, "color": "fff4c2ff"}, {"time": 2.2333, "color": "fff4c207"}, {"time": 2.6, "color": "ffffffff"}], "attachment": [{"time": 1.4667, "name": "tx/x"}, {"time": 1.7, "name": null}, {"time": 1.7333, "name": "tx/x"}, {"time": 1.9667, "name": null}, {"time": 2, "name": "tx/x"}, {"time": 2.2333, "name": null}]}}, "bones": {"1": {"translate": [{"x": -277.82, "y": -111.8, "curve": 0.25, "c3": 0.75}, {"time": 0.1667}]}, "2": {"translate": [{"x": 342.67, "y": 501.03, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 439.16, "y": 642.4, "curve": 0.25, "c3": 0.75}, {"time": 0.2667}]}, "3": {"translate": [{"x": -126.29, "y": -95.09, "curve": 0.25, "c3": 0.75}, {"time": 0.1667}]}, "4": {"translate": [{"x": -173.93, "y": -140.18, "curve": "stepped"}, {"time": 0.1667, "x": -173.93, "y": -140.18, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "x": 13.1, "curve": "stepped"}, {"time": 2.2667, "x": 13.1, "curve": 0.25, "c3": 0.75}, {"time": 2.6}]}, "5": {"translate": [{"x": -132.4, "y": -132.4, "curve": "stepped"}, {"time": 0.1667, "x": -132.4, "y": -132.4, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 77.48, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "6": {"translate": [{"x": 197.3, "y": 379.02, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 293.79, "y": 520.39, "curve": 0.25, "c3": 0.75}, {"time": 0.2667}]}, "tx5": {"rotate": [{"time": 1.8}, {"time": 1.8667, "angle": -50.66, "curve": "stepped"}, {"time": 2.1667, "angle": -50.66}, {"time": 2.6}], "translate": [{}, {"time": 1.5, "x": 284.69, "y": 380.56, "curve": "stepped"}, {"time": 2.1667, "x": 284.69, "y": 380.56}, {"time": 2.6}], "scale": [{"time": 1.5}, {"time": 1.6, "x": 4.297, "y": 4.297}, {"time": 1.8, "x": 5.418, "y": 5.418}, {"time": 1.8667}, {"time": 1.9667, "x": 3.234, "y": 3.234}, {"time": 2.1667, "x": 4.355, "y": 4.355}, {"time": 2.6}]}, "chibang01": {"rotate": [{"angle": 23.87, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 26.06, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 5.8, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 19.45, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 12.83, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": -3.64, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": -4.39, "curve": 0.25, "c3": 0.75}, {"time": 2.6}], "scale": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 0.534, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "curve": "stepped"}, {"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "x": 0.756, "curve": "stepped"}, {"time": 2.2667, "x": 0.756, "curve": 0.25, "c3": 0.75}, {"time": 2.6}]}, "chibang02": {"rotate": [{"angle": 12.85, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 14.99, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -6.99, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 7.36, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 12.85, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": -8.21, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": -5.95, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": -8.21, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": -5.95, "curve": 0.25, "c3": 0.75}, {"time": 2.1, "angle": -8.21, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": -5.58, "curve": 0.25, "c3": 0.75}, {"time": 2.6}], "scale": [{"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 0.694, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "curve": "stepped"}, {"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "x": 0.925, "curve": "stepped"}, {"time": 2.2667, "x": 0.925, "curve": 0.25, "c3": 0.75}, {"time": 2.6}]}, "tou_yhuo": {"rotate": [{"angle": -12.36}, {"time": 1.3333, "angle": -0.46}, {"time": 1.9333, "angle": 16.21}, {"time": 2.6}], "translate": [{"x": -0.69, "y": 2.12}, {"time": 1.9333, "x": 0.38, "y": -0.61}, {"time": 2.6}], "scale": [{"x": 1.332, "y": 1.392}, {"time": 2.6}]}, "fire1": {"rotate": [{"angle": -17.63, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5, "angle": -9.33, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -30.2, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -12.56, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": -30.24, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": -14.12, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "angle": -28.54, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": -17.63}], "translate": [{"x": -117.46, "y": -54.73}, {"time": 0.4, "x": -96.35, "y": -67.51, "curve": "stepped"}, {"time": 1.9667, "x": -96.35, "y": -67.51}, {"time": 2.6, "x": -117.46, "y": -54.73}], "scale": [{"x": 1.641, "y": 1.874, "curve": 0.324, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 0.4, "x": 1.169, "y": 1.335, "curve": "stepped"}, {"time": 1.9667, "x": 1.169, "y": 1.335, "curve": 0.324, "c2": 0.3, "c3": 0.674, "c4": 0.69}, {"time": 2.6, "x": 1.641, "y": 1.874}]}, "tx7": {"rotate": [{}, {"time": 1.5, "angle": -90.76, "curve": "stepped"}, {"time": 1.9667, "angle": -90.76}, {"time": 2, "angle": -145.69, "curve": "stepped"}, {"time": 2.4, "angle": -145.69}, {"time": 2.6}], "translate": [{}, {"time": 1.5, "x": 107.08, "y": 244.23, "curve": "stepped"}, {"time": 1.9667, "x": 107.08, "y": 244.22}, {"time": 2, "x": 190.11, "y": 323.31, "curve": "stepped"}, {"time": 2.4, "x": 190.11, "y": 323.31}, {"time": 2.6}], "scale": [{}, {"time": 1.5, "x": 4.079, "y": 4.079}, {"time": 1.9667, "x": 6.21, "y": 6.21}, {"time": 2, "x": 4.079, "y": 4.079}, {"time": 2.4, "x": 6.21, "y": 6.21}, {"time": 2.6}]}, "tx8": {"rotate": [{}, {"time": 0.5, "angle": -179.75}, {"time": 0.6667, "angle": -142.64}, {"time": 1.4, "angle": -96.26}, {"time": 1.5667, "angle": -124.69, "curve": "stepped"}, {"time": 1.9333, "angle": -124.69}, {"time": 2.6}], "translate": [{}, {"time": 0.5, "x": 759.54, "y": -62.17, "curve": "stepped"}, {"time": 1.4, "x": 759.54, "y": -62.17}, {"time": 1.5667, "x": 65.07, "y": 694.22, "curve": "stepped"}, {"time": 1.9333, "x": 65.07, "y": 694.22}, {"time": 2.6}], "scale": [{}, {"time": 0.5, "x": 9.981, "y": 9.981}, {"time": 0.6667, "x": 7.571, "y": 7.571}, {"time": 1.4, "x": 4.558, "y": 4.558}, {"time": 1.5667, "x": -2.297, "y": 2.297}, {"time": 1.9333, "x": -4.428, "y": 4.428}, {"time": 2.6}]}, "tx6": {"rotate": [{}, {"time": 1.4667, "angle": 124.29, "curve": "stepped"}, {"time": 1.8333, "angle": 124.29}, {"time": 2.6}], "translate": [{}, {"time": 1.4667, "x": 581.08, "y": 472.22, "curve": "stepped"}, {"time": 1.8333, "x": 581.08, "y": 472.22}, {"time": 2.6}], "scale": [{}, {"time": 1.4667, "x": 3.508, "y": 3.508}, {"time": 1.8333, "x": 5.639, "y": 5.639}, {"time": 2.6}]}, "tx9": {"rotate": [{"time": 0.5}, {"time": 0.6667, "angle": 17.29}, {"time": 1.4, "angle": 38.91}, {"time": 1.5667, "angle": 104.48, "curve": "stepped"}, {"time": 1.9333, "angle": 104.48}, {"time": 1.9667, "angle": 28.07, "curve": "stepped"}, {"time": 2.3333, "angle": 28.07}, {"time": 2.6}], "translate": [{}, {"time": 0.5, "x": 759.54, "y": -62.17, "curve": "stepped"}, {"time": 1.4, "x": 759.54, "y": -62.17}, {"time": 1.5667, "x": 515.08, "y": 376.22, "curve": "stepped"}, {"time": 1.9333, "x": 515.08, "y": 376.22}, {"time": 1.9667, "x": 219.23, "y": -77.02, "curve": "stepped"}, {"time": 2.3333, "x": 219.23, "y": -77.02}, {"time": 2.6}], "scale": [{}, {"time": 0.5, "x": 9.981, "y": 9.981}, {"time": 0.6667, "x": 8.197, "y": 8.197}, {"time": 1.4, "x": 5.967, "y": 5.967}, {"time": 1.5667, "x": -2.297, "y": 2.297}, {"time": 1.9333, "x": -4.428, "y": 4.428}, {"time": 1.9667, "x": -2.297, "y": 2.297}, {"time": 2.3333, "x": -4.428, "y": 4.428}, {"time": 2.6}]}, "jianjia01": {"rotate": [{"angle": -79.57, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": -2.41, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 20.31, "curve": "stepped"}, {"time": 2.2667, "angle": 20.31, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": 0.13}]}, "tou03": {"rotate": [{"angle": -26.36, "curve": "stepped"}, {"time": 0.4333, "angle": -26.36, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 5.96, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": -60.41, "curve": "stepped"}, {"time": 2.2667, "angle": -60.41, "curve": 0.25, "c3": 0.75}, {"time": 2.6}], "scale": [{"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "x": 1.085, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "x": 1.085, "y": 1.108, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "x": 1.085, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 1.085, "y": 1.108, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "x": 1.085, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "x": 1.085, "y": 1.108, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "x": 1.085, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "x": 1.085, "y": 1.108, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 1.085, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "x": 1.085, "y": 1.108, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "x": 1.085, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "x": 1.085, "y": 1.108, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "x": 1.085, "curve": 0.25, "c3": 0.75}, {"time": 2.6}]}, "tx2": {"translate": [{}, {"time": 0.1667, "x": 964.6, "y": -310.69}, {"time": 0.2667, "x": 686.77, "y": -310.69}, {"time": 0.7, "x": 564.03, "y": -310.69}, {"time": 1.4333, "x": 356.1, "y": 388.48, "curve": "stepped"}, {"time": 2.5667, "x": 356.1, "y": 388.48}, {"time": 2.6}], "scale": [{}, {"time": 0.1667, "x": 6.649, "y": 6.649}, {"time": 0.2667, "x": 9.086, "y": 9.086}, {"time": 0.7, "x": 8.848, "y": 8.848}, {"time": 1.4333, "x": 13.242, "y": 13.242}, {"time": 1.5333, "x": 135.637, "y": 135.637}, {"time": 2.5667, "x": 184.467, "y": 184.467}, {"time": 2.6}]}, "tx3": {"translate": [{}, {"time": 1.4667, "x": 300.59, "y": 383.95, "curve": "stepped"}, {"time": 2.2, "x": 300.59, "y": 383.95}, {"time": 2.6}], "scale": [{}, {"time": 1.4667, "x": 0.568, "y": 0.568}, {"time": 1.5, "x": 2.066, "y": 2.066}, {"time": 1.6667, "x": 2.948, "y": 2.948}, {"time": 1.7333, "x": 0.568, "y": 0.568}, {"time": 1.9333, "x": 2.948, "y": 2.948}, {"time": 2, "x": 0.568, "y": 0.568}, {"time": 2.2, "x": 2.948, "y": 2.948}, {"time": 2.6}]}, "tx4": {"translate": [{}, {"time": 1.4667, "x": 300.59, "y": 383.95, "curve": "stepped"}, {"time": 2.2333, "x": 300.59, "y": 383.95}, {"time": 2.6}], "scale": [{}, {"time": 1.4667, "x": 0.291, "y": 0.291}, {"time": 1.7, "x": 6.475, "y": 6.475}, {"time": 1.7333, "x": 0.291, "y": 0.291}, {"time": 1.9667, "x": 6.475, "y": 6.475}, {"time": 2, "x": 0.291, "y": 0.291}, {"time": 2.2333, "x": 6.475, "y": 6.475}, {"time": 2.6}]}, "maofa01": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 14.06, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 8.79, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 20.92, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 24.87, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 20.44, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 25.73, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "angle": 17.69, "curve": 0.25, "c3": 0.75}, {"time": 2.6}]}, "shent01": {"rotate": [{"angle": 6.17, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -2.43, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 49.66, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -3.27, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 29.92, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": -12.84, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": -13.5, "curve": 0.25, "c3": 0.75}, {"time": 2.6}], "translate": [{"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "x": 94.43, "y": -1.91, "curve": "stepped"}, {"time": 2.2667, "x": 94.43, "y": -1.91, "curve": 0.25, "c3": 0.75}, {"time": 2.6}], "scale": [{"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "x": 1.085, "curve": "stepped"}, {"time": 2.2667, "x": 1.085, "curve": 0.25, "c3": 0.75}, {"time": 2.6}]}, "maofa04": {"rotate": [{"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": -22.6, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": 7.51, "curve": 0.25, "c3": 0.75}, {"time": 2.6}]}, "maofa05": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 14.06, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 8.79, "curve": 0.25, "c3": 0.75}, {"time": 1.4}]}, "maofa06": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 14.06, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 8.79, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 22.02, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": 12.67, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": 13.48, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": 4.46, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": 6.52, "curve": 0.25, "c3": 0.75}, {"time": 2.6}]}, "neihoutui01": {"rotate": [{"angle": 110.53, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": -2.72, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 36.08, "curve": "stepped"}, {"time": 2.2667, "angle": 36.08, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": -0.03}]}, "neihoutui02": {"rotate": [{"angle": -2.73, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 36.47, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 24.46, "curve": "stepped"}, {"time": 2.2667, "angle": 24.46, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": 0.04}]}, "neihoutui03": {"rotate": [{"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 6.45, "curve": "stepped"}, {"time": 2.2667, "angle": 6.45, "curve": 0.25, "c3": 0.75}, {"time": 2.6}]}, "neiqiantui01": {"rotate": [{"angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": -97.7, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 2.91, "curve": "stepped"}, {"time": 2.2667, "angle": 2.91, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": -0.65}]}, "neiqiantui02": {"rotate": [{"angle": -44.1, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 16.51, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 49.21, "curve": "stepped"}, {"time": 2.2667, "angle": 49.21, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": 0.37}], "translate": [{"x": 49.63, "y": 34.71, "curve": "stepped"}, {"time": 1.4, "x": 49.63, "y": 34.71, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "x": -22.46, "y": 20.87, "curve": "stepped"}, {"time": 2.2667, "x": -22.46, "y": 20.87, "curve": 0.25, "c3": 0.75}, {"time": 2.6}]}, "shenti02": {"rotate": [{"angle": 19.57, "curve": "stepped"}, {"time": 0.1667, "angle": 19.57, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -10.46, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 6.39, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": -13.29, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "angle": -13.95, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": -13.29, "curve": 0.25, "c3": 0.75}, {"time": 2.6}], "translate": [{"y": 23.72, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 96.49, "y": 165.09, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 96.49, "y": -110.85, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "x": 112.34, "y": -33.26, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "x": -63.32, "y": -51.55, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "x": -60.24, "y": -51.55, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "x": -63.32, "y": -51.55, "curve": 0.25, "c3": 0.75}, {"time": 2.6}]}, "tx": {"rotate": [{"time": 1.5333}, {"time": 1.9, "angle": -151.75}, {"time": 2.1, "angle": 130.09}, {"time": 2.3333, "angle": 8.12}, {"time": 2.6}], "translate": [{}, {"time": 1.5333, "x": 117.45, "y": 310.72, "curve": "stepped"}, {"time": 2.3333, "x": 117.45, "y": 310.72}, {"time": 2.6}], "scale": [{}, {"time": 1.5333, "x": 11.143, "y": 11.143}, {"time": 2.3333, "x": 14.79, "y": 14.79}, {"time": 2.5, "x": 15.854, "y": 15.854}, {"time": 2.6}]}, "tou3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 40.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -5.66, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 34.85, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 13.88, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": 1.87, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": 6.51, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": -8.47, "curve": 0.25, "c3": 0.75}, {"time": 2.6}]}, "tou5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 40.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -5.66, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 34.85, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 39.53, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": 0.79, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": 23.69, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": 0.79, "curve": 0.25, "c3": 0.75}, {"time": 2.1, "angle": 23.69, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": 1.01, "curve": 0.25, "c3": 0.75}, {"time": 2.6}]}, "tou05": {"rotate": [{"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 13.88, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": 1.87, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": 6.51, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": -8.47, "curve": 0.25, "c3": 0.75}, {"time": 2.6}]}, "waihoutui01": {"rotate": [{"angle": 31.95, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 2.59, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 40.67, "curve": "stepped"}, {"time": 2.2667, "angle": 40.67, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": -0.08}], "scale": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 0.75, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "waihoutui02": {"rotate": [{"angle": 38.92, "curve": "stepped"}, {"time": 0.1667, "angle": 38.92, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 29.43, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "waihoutui03": {"rotate": [{"angle": 12.19, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 1.93, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": -38.3, "curve": "stepped"}, {"time": 2.2667, "angle": -38.3, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": 0.18}]}, "waiqiantui02": {"rotate": [{"angle": 17.44, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": -40.4, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 42.07, "curve": "stepped"}, {"time": 2.2667, "angle": 42.07, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": -0.15}]}, "weiba01": {"rotate": [{"angle": 55.99, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 54.95, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 60.74, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 55.99, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 55.39, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": 50.45, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": 51.89, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": 48.58, "curve": 0.25, "c3": 0.75}, {"time": 2.6}], "translate": [{"x": 37.29, "y": -77.09, "curve": "stepped"}, {"time": 1.4, "x": 37.29, "y": -77.09, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "x": 9.8, "y": -33.02, "curve": "stepped"}, {"time": 2.2667, "x": 9.8, "y": -33.02, "curve": 0.25, "c3": 0.75}, {"time": 2.6}]}, "weiba02": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -30.23, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 3.33, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 1.53, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": -6.29, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 0.79, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": -5.29, "curve": 0.25, "c3": 0.75}, {"time": 2.6}]}, "tou04": {"rotate": [{"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 55.32, "curve": "stepped"}, {"time": 2.2667, "angle": 55.32, "curve": 0.25, "c3": 0.75}, {"time": 2.6}]}, "waihoutui3": {"rotate": [{"angle": 4.33, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": -10.82, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 11.01, "curve": "stepped"}, {"time": 2.2667, "angle": 11.01, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": -0.01}]}, "neihoutui2": {"rotate": [{"angle": 18.21, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": -40.1, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": -53.29, "curve": "stepped"}, {"time": 2.2667, "angle": -53.29, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": 0.03}]}, "weiba1": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -11.55, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -37.04, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 7.62, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 2.18, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": 4.58, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": 0.81, "curve": 0.25, "c3": 0.75}, {"time": 2.6}]}, "weiba2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -11.55, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -40.29, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 1.53, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": -4.9, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": -0.87, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": -5.29, "curve": 0.25, "c3": 0.75}, {"time": 2.6}]}, "weiba3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -11.55, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -27.26, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 1.53, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": -5.52, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": -0.05, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": -5.29, "curve": 0.25, "c3": 0.75}, {"time": 2.6}]}, "weiba5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -30.23, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 3.33, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 1.53, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -11.71, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": 4.83, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -5.29, "curve": 0.25, "c3": 0.75}, {"time": 2.6}]}, "weiba6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -30.23, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 3.33, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 1.53, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -14.19, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": 8.64, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "angle": -5.29, "curve": 0.25, "c3": 0.75}, {"time": 2.6}]}, "chibang1": {"rotate": [{"angle": 12.9, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 15.09, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -5.17, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 8.48, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 1.86, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": -3.64, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": -5.59, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": -2.5, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "angle": -5.59, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": -2.5, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": -4.39, "curve": 0.25, "c3": 0.75}, {"time": 2.6}]}, "chibang2": {"rotate": [{"angle": 12.9, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 15.09, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -5.17, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 8.48, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 1.86, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": -3.64, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -5.59, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 1.71, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -5.59, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": 1.71, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "angle": -4.39, "curve": 0.25, "c3": 0.75}, {"time": 2.6}]}, "chibang3": {"rotate": [{"angle": 12.85, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 14.99, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -6.99, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 7.36, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 12.85, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": -8.21, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": -5.95, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": -8.21, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": -5.95, "curve": 0.25, "c3": 0.75}, {"time": 2.1, "angle": -8.21, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": -5.58, "curve": 0.25, "c3": 0.75}, {"time": 2.6}]}, "chibang4": {"rotate": [{"angle": 12.85, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 14.99, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -6.99, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 7.36, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 12.85, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": -8.21, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": -5.95, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": -8.21, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": -5.95, "curve": 0.25, "c3": 0.75}, {"time": 2.1, "angle": -8.21, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": -5.58, "curve": 0.25, "c3": 0.75}, {"time": 2.6}]}, "shent1": {"rotate": [{"angle": 19.12, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 19.44, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -23.14, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 12.85, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 6.63, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": -0.19, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": 9.33, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -0.19, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": 6.63, "curve": 0.25, "c3": 0.75}, {"time": 2.6}]}, "shent2": {"rotate": [{"angle": 19.12, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 19.44, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -23.14, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 12.85, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 13.45, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -4.38, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": 13.4, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": -6.09, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 10.77, "curve": 0.25, "c3": 0.75}, {"time": 2.6}]}, "shent3": {"rotate": [{"angle": 19.12, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 19.44, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -23.14, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 12.85, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 30.9, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": -15.74, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": 24.31, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": -13, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "angle": 20.27, "curve": 0.25, "c3": 0.75}, {"time": 2.6}]}, "tou6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 40.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -5.66, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 34.85, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 39.53, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 0.79, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 23.69, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 0.79, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": 23.69, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -32.98, "curve": 0.25, "c3": 0.75}, {"time": 2.6}]}, "tou7": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 40.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -5.66, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 34.85, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 39.53, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": 0.79, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": 23.69, "curve": 0.25, "c3": 0.75}, {"time": 2.1, "angle": 0.79, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": 23.69, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "angle": -32.98, "curve": 0.25, "c3": 0.75}, {"time": 2.6}]}, "tou4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 40.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -5.66, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 34.85, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 13.88, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 1.87, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": 6.51, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "angle": -8.47, "curve": 0.25, "c3": 0.75}, {"time": 2.6}]}, "tou8": {"rotate": [{"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 13.88, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": 1.87, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": 6.51, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": -8.47, "curve": 0.25, "c3": 0.75}, {"time": 2.6}]}, "maofa6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 14.06, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 8.79, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 22.02, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": 12.67, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": 13.48, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": 4.46, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": 6.52, "curve": 0.25, "c3": 0.75}, {"time": 2.6}]}, "maofa7": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 14.06, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 8.79, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": -28.46, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": 12.67, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": 13.48, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": 4.46, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 6.52, "curve": 0.25, "c3": 0.75}, {"time": 2.6}]}, "maofa1": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 14.06, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 8.79, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 18.33, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 28.54, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 17.59, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 28.24, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "angle": 14.72, "curve": 0.25, "c3": 0.75}, {"time": 2.6}]}, "maofa2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 14.06, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 8.79, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 13.99, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 31.88, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 13.66, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 32.58, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "angle": 9.76, "curve": 0.25, "c3": 0.75}, {"time": 2.6}]}, "maofa5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 14.06, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 8.79, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "curve": "stepped"}, {"time": 1.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": 7.32, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -5.8, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": 7.49, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": -4.86, "curve": 0.25, "c3": 0.75}, {"time": 2.6}]}, "maofa9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 14.06, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 8.79, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "curve": "stepped"}, {"time": 1.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": 7.32, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -5.8, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": 7.49, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": -4.86, "curve": 0.25, "c3": 0.75}, {"time": 2.6}]}, "maofa10": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 14.06, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 8.79, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": -70.38, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -45.41, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": -49.69, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": -16.97, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": -40.93, "curve": 0.25, "c3": 0.75}, {"time": 2.6}]}, "maofa11": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 14.06, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 8.79, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": -26.14, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -12.23, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": -27.58, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": -5.91, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": -31.68, "curve": 0.25, "c3": 0.75}, {"time": 2.6}]}, "maofa4": {"rotate": [{"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": -22.6, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": 7.51, "curve": 0.25, "c3": 0.75}, {"time": 2.6}]}, "maofa12": {"rotate": [{"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": -22.6, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": 7.51, "curve": 0.25, "c3": 0.75}, {"time": 2.6}]}, "maofa13": {"rotate": [{"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": -22.6, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": 7.51, "curve": 0.25, "c3": 0.75}, {"time": 2.6}]}, "weiba4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -30.23, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 3.33, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 1.53, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": -8.1, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": 2.07, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": -5.29, "curve": 0.25, "c3": 0.75}, {"time": 2.6}]}, "bone": {"translate": [{"x": 1676.09, "y": 1981.93, "curve": 0.25, "c3": 0.75}, {"time": 0.1667}]}, "fire2": {"rotate": [{"angle": -0.36}, {"time": 0.2667, "angle": 12.23}, {"time": 1, "angle": 19.01}, {"time": 1.6667, "angle": -23.6}, {"time": 1.9, "angle": 19.67}, {"time": 2.1333, "angle": -14.11}, {"time": 2.6, "angle": -0.36}]}}, "drawOrder": [{"offsets": [{"slot": "smoke", "offset": 11}, {"slot": "huo2", "offset": -26}, {"slot": "huo3", "offset": -24}, {"slot": "huo4", "offset": -34}, {"slot": "waihoutui01_zg", "offset": -25}, {"slot": "waiqiantui02_zg", "offset": -2}, {"slot": "jm", "offset": -24}, {"slot": "jiao1_g", "offset": -8}, {"slot": "jiao2_g", "offset": -24}]}, {"time": 0.1333, "offsets": [{"slot": "smoke", "offset": 11}, {"slot": "huo2", "offset": -26}, {"slot": "huo3", "offset": -24}, {"slot": "huo4", "offset": -34}, {"slot": "waihoutui01_zg", "offset": -25}, {"slot": "waiqiantui02_zg", "offset": -2}, {"slot": "jm", "offset": -24}, {"slot": "jiao1_g", "offset": -8}, {"slot": "jiao2_g", "offset": -24}, {"slot": "qq", "offset": -16}]}, {"time": 0.7, "offsets": [{"slot": "smoke", "offset": 12}, {"slot": "huo2", "offset": -25}, {"slot": "huo3", "offset": -23}, {"slot": "huo4", "offset": -33}, {"slot": "waihoutui01_zg", "offset": -24}, {"slot": "waiqiantui02_zg", "offset": -1}, {"slot": "jm", "offset": -23}, {"slot": "jiao1_g", "offset": -7}, {"slot": "jiao2_g", "offset": -23}, {"slot": "lz", "offset": -50}, {"slot": "qq", "offset": -14}]}, {"time": 1, "offsets": [{"slot": "smoke", "offset": 13}, {"slot": "huo2", "offset": -24}, {"slot": "huo3", "offset": -22}, {"slot": "huo4", "offset": -32}, {"slot": "waihoutui01_zg", "offset": -23}, {"slot": "waiqiantui02_zg", "offset": 0}, {"slot": "jm", "offset": -22}, {"slot": "jiao1_g", "offset": -6}, {"slot": "jiao2_g", "offset": -22}, {"slot": "lz", "offset": -50}, {"slot": "xz", "offset": -59}]}], "events": [{"time": 1.3333, "name": "attack", "int": 101}, {"time": 1.4667, "name": "attack", "int": 1}, {"time": 1.5, "name": "attack", "int": 103}, {"time": 1.6333, "name": "attack", "int": 3}, {"time": 1.6667, "name": "attack", "int": 105}, {"time": 1.8, "name": "attack", "int": 5}, {"time": 1.8333, "name": "attack", "int": 107}, {"time": 1.9667, "name": "attack", "int": 7}, {"time": 2, "name": "attack", "int": 109}, {"time": 2.1333, "name": "attack", "int": 9}, {"time": 2.1667, "name": "attack", "int": 111}, {"time": 2.3, "name": "attack", "int": 11}]}}}, [0, 1]]], 0, 0, [0, 0], [-1, -2], [0, 1]]