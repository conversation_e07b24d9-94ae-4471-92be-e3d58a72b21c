System.register([], function(_export, _context) { return { execute: function () {
System.register("chunks:///_virtual/cjs-loader.mjs",[],(function(e){return{execute:function(){e("default",new(function(){function e(){this._registry={},this._moduleCache={}}var r=e.prototype;return r.define=function(e,r,t){this._registry[e]={factory:r,resolveMap:t}},r.require=function(e){return this._require(e)},r.throwInvalidWrapper=function(e,r){throw new Error("Module '"+e+"' imported from '"+r+"' is expected be an ESM-wrapped CommonJS module but it doesn't.")},r._require=function(e,r){var t=this._moduleCache[e];if(t)return t.exports;var o={id:e,exports:{}};return this._moduleCache[e]=o,this._tryModuleLoad(o,e),o.exports},r._resolve=function(e,r){return this._resolveFromInfos(e,r)||this._throwUnresolved(e,r)},r._resolveFromInfos=function(e,r){var t,o;return e in cjsInfos?e:r&&null!=(t=null==(o=cjsInfos[r])?void 0:o.resolveCache[e])?t:void 0},r._tryModuleLoad=function(e,r){var t=!0;try{this._load(e,r),t=!1}finally{t&&delete this._moduleCache[r]}},r._load=function(e,r){var t=this._loadWrapper(r),o=t.factory,n=t.resolveMap,i=this._createRequire(e),u=n?this._createRequireWithResolveMap("function"==typeof n?n():n,i):i;o(e.exports,u,e)},r._loadWrapper=function(e){return e in this._registry?this._registry[e]:this._loadHostProvidedModules(e)},r._loadHostProvidedModules=function(e){return{factory:function(r,t,o){if("undefined"==typeof require)throw new Error("Current environment does not provide a require() for requiring '"+e+"'.");try{o.exports=require(e)}catch(r){throw new Error("Exception thrown when calling host defined require('"+e+"').",{cause:r})}}}},r._createRequire=function(e){var r=this;return function(t){return r._require(t,e)}},r._createRequireWithResolveMap=function(e,r){return function(t){var o=e[t];if(o)return r(o);throw new Error("Unresolved specifier "+t)}},r._throwUnresolved=function(e,r){throw new Error("Unable to resolve "+e+" from "+parent+".")},e}()))}}}));

System.register("chunks:///_virtual/env",[],(function(e){return{execute:function(){e("DEV",!1),e("JSB",!1)}}}));

System.register("chunks:///_virtual/index-minimal.js",["./cjs-loader.mjs","./writer.js","./writer_buffer.js","./reader.js","./reader_buffer.js","./minimal2.js","./rpc.js","./roots.js"],(function(r,e){var t,i,n,f,u,c,a,s;return{setters:[function(r){t=r.default},function(r){i=r.__cjsMetaURL},function(r){n=r.__cjsMetaURL},function(r){f=r.__cjsMetaURL},function(r){u=r.__cjsMetaURL},function(r){c=r.__cjsMetaURL},function(r){a=r.__cjsMetaURL},function(r){s=r.__cjsMetaURL}],execute:function(){var o=r("__cjsMetaURL",e.meta.url);t.define(o,(function(r,e,t,i,n){var f=r;function u(){f.util._configure(),f.Writer._configure(f.BufferWriter),f.Reader._configure(f.BufferReader)}f.build="minimal",f.Writer=e("./writer"),f.BufferWriter=e("./writer_buffer"),f.Reader=e("./reader"),f.BufferReader=e("./reader_buffer"),f.util=e("./util/minimal"),f.rpc=e("./rpc"),f.roots=e("./roots"),f.configure=u,u(),t.exports}),(function(){return{"./writer":i,"./writer_buffer":n,"./reader":f,"./reader_buffer":u,"./util/minimal":c,"./rpc":a,"./roots":s}}))}}}));

System.register("chunks:///_virtual/index.js",[],(function(i){return{execute:function(){i("default",n);
/**
       * @license
       * Copyright 2009 The Closure Library Authors
       * Copyright 2020 Daniel Wirtz / The long.js Authors.
       *
       * Licensed under the Apache License, Version 2.0 (the "License");
       * you may not use this file except in compliance with the License.
       * You may obtain a copy of the License at
       *
       *     http://www.apache.org/licenses/LICENSE-2.0
       *
       * Unless required by applicable law or agreed to in writing, software
       * distributed under the License is distributed on an "AS IS" BASIS,
       * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
       * See the License for the specific language governing permissions and
       * limitations under the License.
       *
       * SPDX-License-Identifier: Apache-2.0
       */
var t=null;try{t=new WebAssembly.Instance(new WebAssembly.Module(new Uint8Array([0,97,115,109,1,0,0,0,1,13,2,96,0,1,127,96,4,127,127,127,127,1,127,3,7,6,0,1,1,1,1,1,6,6,1,127,1,65,0,11,7,50,6,3,109,117,108,0,1,5,100,105,118,95,115,0,2,5,100,105,118,95,117,0,3,5,114,101,109,95,115,0,4,5,114,101,109,95,117,0,5,8,103,101,116,95,104,105,103,104,0,0,10,191,1,6,4,0,35,0,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,126,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,127,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,128,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,129,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,130,34,4,66,32,135,167,36,0,32,4,167,11])),{}).exports}catch(i){}function n(i,t,n){this.low=0|i,this.high=0|t,this.unsigned=!!n}function h(i){return!0===(i&&i.__isLong__)}function s(i){var t=Math.clz32(i&-i);return i?31-t:t}n.prototype.__isLong__,Object.defineProperty(n.prototype,"__isLong__",{value:!0}),n.isLong=h;var e={},r={};function u(i,t){var n,h,s;return t?(s=0<=(i>>>=0)&&i<256)&&(h=r[i])?h:(n=g(i,0,!0),s&&(r[i]=n),n):(s=-128<=(i|=0)&&i<128)&&(h=e[i])?h:(n=g(i,i<0?-1:0,!1),s&&(e[i]=n),n)}function o(i,t){if(isNaN(i))return t?N:m;if(t){if(i<0)return N;if(i>=c)return _}else{if(i<=-w)return p;if(i+1>=w)return q}return i<0?o(-i,t).neg():g(i%d|0,i/d|0,t)}function g(i,t,h){return new n(i,t,h)}n.fromInt=u,n.fromNumber=o,n.fromBits=g;var f=Math.pow;function l(i,t,n){if(0===i.length)throw Error("empty string");if("number"==typeof t?(n=t,t=!1):t=!!t,"NaN"===i||"Infinity"===i||"+Infinity"===i||"-Infinity"===i)return t?N:m;if((n=n||10)<2||36<n)throw RangeError("radix");var h;if((h=i.indexOf("-"))>0)throw Error("interior hyphen");if(0===h)return l(i.substring(1),t,n).neg();for(var s=o(f(n,8)),e=m,r=0;r<i.length;r+=8){var u=Math.min(8,i.length-r),g=parseInt(i.substring(r,r+u),n);if(u<8){var a=o(f(n,u));e=e.mul(a).add(o(g))}else e=(e=e.mul(s)).add(o(g))}return e.unsigned=t,e}function a(i,t){return"number"==typeof i?o(i,t):"string"==typeof i?l(i,t):g(i.low,i.high,"boolean"==typeof t?t:i.unsigned)}n.fromString=l,n.fromValue=a;var d=4294967296,c=d*d,w=c/2,v=u(1<<24),m=u(0);n.ZERO=m;var N=u(0,!0);n.UZERO=N;var E=u(1);n.ONE=E;var y=u(1,!0);n.UONE=y;var b=u(-1);n.NEG_ONE=b;var q=g(-1,2147483647,!1);n.MAX_VALUE=q;var _=g(-1,-1,!0);n.MAX_UNSIGNED_VALUE=_;var p=g(0,-2147483648,!1);n.MIN_VALUE=p;var B=n.prototype;B.toInt=function(){return this.unsigned?this.low>>>0:this.low},B.toNumber=function(){return this.unsigned?(this.high>>>0)*d+(this.low>>>0):this.high*d+(this.low>>>0)},B.toString=function(i){if((i=i||10)<2||36<i)throw RangeError("radix");if(this.isZero())return"0";if(this.isNegative()){if(this.eq(p)){var t=o(i),n=this.div(t),h=n.mul(t).sub(this);return n.toString(i)+h.toInt().toString(i)}return"-"+this.neg().toString(i)}for(var s=o(f(i,6),this.unsigned),e=this,r="";;){var u=e.div(s),g=(e.sub(u.mul(s)).toInt()>>>0).toString(i);if((e=u).isZero())return g+r;for(;g.length<6;)g="0"+g;r=""+g+r}},B.getHighBits=function(){return this.high},B.getHighBitsUnsigned=function(){return this.high>>>0},B.getLowBits=function(){return this.low},B.getLowBitsUnsigned=function(){return this.low>>>0},B.getNumBitsAbs=function(){if(this.isNegative())return this.eq(p)?64:this.neg().getNumBitsAbs();for(var i=0!=this.high?this.high:this.low,t=31;t>0&&0==(i&1<<t);t--);return 0!=this.high?t+33:t+1},B.isZero=function(){return 0===this.high&&0===this.low},B.eqz=B.isZero,B.isNegative=function(){return!this.unsigned&&this.high<0},B.isPositive=function(){return this.unsigned||this.high>=0},B.isOdd=function(){return 1==(1&this.low)},B.isEven=function(){return 0==(1&this.low)},B.equals=function(i){return h(i)||(i=a(i)),(this.unsigned===i.unsigned||this.high>>>31!=1||i.high>>>31!=1)&&(this.high===i.high&&this.low===i.low)},B.eq=B.equals,B.notEquals=function(i){return!this.eq(i)},B.neq=B.notEquals,B.ne=B.notEquals,B.lessThan=function(i){return this.comp(i)<0},B.lt=B.lessThan,B.lessThanOrEqual=function(i){return this.comp(i)<=0},B.lte=B.lessThanOrEqual,B.le=B.lessThanOrEqual,B.greaterThan=function(i){return this.comp(i)>0},B.gt=B.greaterThan,B.greaterThanOrEqual=function(i){return this.comp(i)>=0},B.gte=B.greaterThanOrEqual,B.ge=B.greaterThanOrEqual,B.compare=function(i){if(h(i)||(i=a(i)),this.eq(i))return 0;var t=this.isNegative(),n=i.isNegative();return t&&!n?-1:!t&&n?1:this.unsigned?i.high>>>0>this.high>>>0||i.high===this.high&&i.low>>>0>this.low>>>0?-1:1:this.sub(i).isNegative()?-1:1},B.comp=B.compare,B.negate=function(){return!this.unsigned&&this.eq(p)?p:this.not().add(E)},B.neg=B.negate,B.add=function(i){h(i)||(i=a(i));var t=this.high>>>16,n=65535&this.high,s=this.low>>>16,e=65535&this.low,r=i.high>>>16,u=65535&i.high,o=i.low>>>16,f=0,l=0,d=0,c=0;return d+=(c+=e+(65535&i.low))>>>16,l+=(d+=s+o)>>>16,f+=(l+=n+u)>>>16,f+=t+r,g((d&=65535)<<16|(c&=65535),(f&=65535)<<16|(l&=65535),this.unsigned)},B.subtract=function(i){return h(i)||(i=a(i)),this.add(i.neg())},B.sub=B.subtract,B.multiply=function(i){if(this.isZero())return this;if(h(i)||(i=a(i)),t)return g(t.mul(this.low,this.high,i.low,i.high),t.get_high(),this.unsigned);if(i.isZero())return this.unsigned?N:m;if(this.eq(p))return i.isOdd()?p:m;if(i.eq(p))return this.isOdd()?p:m;if(this.isNegative())return i.isNegative()?this.neg().mul(i.neg()):this.neg().mul(i).neg();if(i.isNegative())return this.mul(i.neg()).neg();if(this.lt(v)&&i.lt(v))return o(this.toNumber()*i.toNumber(),this.unsigned);var n=this.high>>>16,s=65535&this.high,e=this.low>>>16,r=65535&this.low,u=i.high>>>16,f=65535&i.high,l=i.low>>>16,d=65535&i.low,c=0,w=0,E=0,y=0;return E+=(y+=r*d)>>>16,w+=(E+=e*d)>>>16,E&=65535,w+=(E+=r*l)>>>16,c+=(w+=s*d)>>>16,w&=65535,c+=(w+=e*l)>>>16,w&=65535,c+=(w+=r*f)>>>16,c+=n*d+s*l+e*f+r*u,g((E&=65535)<<16|(y&=65535),(c&=65535)<<16|(w&=65535),this.unsigned)},B.mul=B.multiply,B.divide=function(i){if(h(i)||(i=a(i)),i.isZero())throw Error("division by zero");var n,s,e;if(t)return this.unsigned||-2147483648!==this.high||-1!==i.low||-1!==i.high?g((this.unsigned?t.div_u:t.div_s)(this.low,this.high,i.low,i.high),t.get_high(),this.unsigned):this;if(this.isZero())return this.unsigned?N:m;if(this.unsigned){if(i.unsigned||(i=i.toUnsigned()),i.gt(this))return N;if(i.gt(this.shru(1)))return y;e=N}else{if(this.eq(p))return i.eq(E)||i.eq(b)?p:i.eq(p)?E:(n=this.shr(1).div(i).shl(1)).eq(m)?i.isNegative()?E:b:(s=this.sub(i.mul(n)),e=n.add(s.div(i)));if(i.eq(p))return this.unsigned?N:m;if(this.isNegative())return i.isNegative()?this.neg().div(i.neg()):this.neg().div(i).neg();if(i.isNegative())return this.div(i.neg()).neg();e=m}for(s=this;s.gte(i);){n=Math.max(1,Math.floor(s.toNumber()/i.toNumber()));for(var r=Math.ceil(Math.log(n)/Math.LN2),u=r<=48?1:f(2,r-48),l=o(n),d=l.mul(i);d.isNegative()||d.gt(s);)d=(l=o(n-=u,this.unsigned)).mul(i);l.isZero()&&(l=E),e=e.add(l),s=s.sub(d)}return e},B.div=B.divide,B.modulo=function(i){return h(i)||(i=a(i)),t?g((this.unsigned?t.rem_u:t.rem_s)(this.low,this.high,i.low,i.high),t.get_high(),this.unsigned):this.sub(this.div(i).mul(i))},B.mod=B.modulo,B.rem=B.modulo,B.not=function(){return g(~this.low,~this.high,this.unsigned)},B.countLeadingZeros=function(){return this.high?Math.clz32(this.high):Math.clz32(this.low)+32},B.clz=B.countLeadingZeros,B.countTrailingZeros=function(){return this.low?s(this.low):s(this.high)+32},B.ctz=B.countTrailingZeros,B.and=function(i){return h(i)||(i=a(i)),g(this.low&i.low,this.high&i.high,this.unsigned)},B.or=function(i){return h(i)||(i=a(i)),g(this.low|i.low,this.high|i.high,this.unsigned)},B.xor=function(i){return h(i)||(i=a(i)),g(this.low^i.low,this.high^i.high,this.unsigned)},B.shiftLeft=function(i){return h(i)&&(i=i.toInt()),0==(i&=63)?this:i<32?g(this.low<<i,this.high<<i|this.low>>>32-i,this.unsigned):g(0,this.low<<i-32,this.unsigned)},B.shl=B.shiftLeft,B.shiftRight=function(i){return h(i)&&(i=i.toInt()),0==(i&=63)?this:i<32?g(this.low>>>i|this.high<<32-i,this.high>>i,this.unsigned):g(this.high>>i-32,this.high>=0?0:-1,this.unsigned)},B.shr=B.shiftRight,B.shiftRightUnsigned=function(i){return h(i)&&(i=i.toInt()),0==(i&=63)?this:i<32?g(this.low>>>i|this.high<<32-i,this.high>>>i,this.unsigned):g(32===i?this.high:this.high>>>i-32,0,this.unsigned)},B.shru=B.shiftRightUnsigned,B.shr_u=B.shiftRightUnsigned,B.rotateLeft=function(i){var t;return h(i)&&(i=i.toInt()),0==(i&=63)?this:32===i?g(this.high,this.low,this.unsigned):i<32?(t=32-i,g(this.low<<i|this.high>>>t,this.high<<i|this.low>>>t,this.unsigned)):(t=32-(i-=32),g(this.high<<i|this.low>>>t,this.low<<i|this.high>>>t,this.unsigned))},B.rotl=B.rotateLeft,B.rotateRight=function(i){var t;return h(i)&&(i=i.toInt()),0==(i&=63)?this:32===i?g(this.high,this.low,this.unsigned):i<32?(t=32-i,g(this.high<<t|this.low>>>i,this.low<<t|this.high>>>i,this.unsigned)):(t=32-(i-=32),g(this.low<<t|this.high>>>i,this.high<<t|this.low>>>i,this.unsigned))},B.rotr=B.rotateRight,B.toSigned=function(){return this.unsigned?g(this.low,this.high,!1):this},B.toUnsigned=function(){return this.unsigned?this:g(this.low,this.high,!0)},B.toBytes=function(i){return i?this.toBytesLE():this.toBytesBE()},B.toBytesLE=function(){var i=this.high,t=this.low;return[255&t,t>>>8&255,t>>>16&255,t>>>24,255&i,i>>>8&255,i>>>16&255,i>>>24]},B.toBytesBE=function(){var i=this.high,t=this.low;return[i>>>24,i>>>16&255,i>>>8&255,255&i,t>>>24,t>>>16&255,t>>>8&255,255&t]},n.fromBytes=function(i,t,h){return h?n.fromBytesLE(i,t):n.fromBytesBE(i,t)},n.fromBytesLE=function(i,t){return new n(i[0]|i[1]<<8|i[2]<<16|i[3]<<24,i[4]|i[5]<<8|i[6]<<16|i[7]<<24,t)},n.fromBytesBE=function(i,t){return new n(i[4]<<24|i[5]<<16|i[6]<<8|i[7],i[0]<<24|i[1]<<16|i[2]<<8|i[3],t)}}}}));

System.register("chunks:///_virtual/index2.js",["./cjs-loader.mjs"],(function(e,t){var n;return{setters:[function(e){n=e.default}],execute:function(){var r=e("__cjsMetaURL",t.meta.url);n.define(r,(function(e,t,n,r,o){function a(e){return"undefined"!=typeof Float32Array?function(){var t=new Float32Array([-0]),n=new Uint8Array(t.buffer),r=128===n[3];function o(e,r,o){t[0]=e,r[o]=n[0],r[o+1]=n[1],r[o+2]=n[2],r[o+3]=n[3]}function a(e,r,o){t[0]=e,r[o]=n[3],r[o+1]=n[2],r[o+2]=n[1],r[o+3]=n[0]}function u(e,r){return n[0]=e[r],n[1]=e[r+1],n[2]=e[r+2],n[3]=e[r+3],t[0]}function i(e,r){return n[3]=e[r],n[2]=e[r+1],n[1]=e[r+2],n[0]=e[r+3],t[0]}e.writeFloatLE=r?o:a,e.writeFloatBE=r?a:o,e.readFloatLE=r?u:i,e.readFloatBE=r?i:u}():function(){function t(e,t,n,r){var o=t<0?1:0;if(o&&(t=-t),0===t)e(1/t>0?0:2147483648,n,r);else if(isNaN(t))e(2143289344,n,r);else if(t>34028234663852886e22)e((o<<31|2139095040)>>>0,n,r);else if(t<11754943508222875e-54)e((o<<31|Math.round(t/1401298464324817e-60))>>>0,n,r);else{var a=Math.floor(Math.log(t)/Math.LN2);e((o<<31|a+127<<23|8388607&Math.round(t*Math.pow(2,-a)*8388608))>>>0,n,r)}}function n(e,t,n){var r=e(t,n),o=2*(r>>31)+1,a=r>>>23&255,u=8388607&r;return 255===a?u?NaN:o*(1/0):0===a?1401298464324817e-60*o*u:o*Math.pow(2,a-150)*(u+8388608)}e.writeFloatLE=t.bind(null,u),e.writeFloatBE=t.bind(null,i),e.readFloatLE=n.bind(null,l),e.readFloatBE=n.bind(null,f)}(),"undefined"!=typeof Float64Array?function(){var t=new Float64Array([-0]),n=new Uint8Array(t.buffer),r=128===n[7];function o(e,r,o){t[0]=e,r[o]=n[0],r[o+1]=n[1],r[o+2]=n[2],r[o+3]=n[3],r[o+4]=n[4],r[o+5]=n[5],r[o+6]=n[6],r[o+7]=n[7]}function a(e,r,o){t[0]=e,r[o]=n[7],r[o+1]=n[6],r[o+2]=n[5],r[o+3]=n[4],r[o+4]=n[3],r[o+5]=n[2],r[o+6]=n[1],r[o+7]=n[0]}function u(e,r){return n[0]=e[r],n[1]=e[r+1],n[2]=e[r+2],n[3]=e[r+3],n[4]=e[r+4],n[5]=e[r+5],n[6]=e[r+6],n[7]=e[r+7],t[0]}function i(e,r){return n[7]=e[r],n[6]=e[r+1],n[5]=e[r+2],n[4]=e[r+3],n[3]=e[r+4],n[2]=e[r+5],n[1]=e[r+6],n[0]=e[r+7],t[0]}e.writeDoubleLE=r?o:a,e.writeDoubleBE=r?a:o,e.readDoubleLE=r?u:i,e.readDoubleBE=r?i:u}():function(){function t(e,t,n,r,o,a){var u=r<0?1:0;if(u&&(r=-r),0===r)e(0,o,a+t),e(1/r>0?0:2147483648,o,a+n);else if(isNaN(r))e(0,o,a+t),e(2146959360,o,a+n);else if(r>17976931348623157e292)e(0,o,a+t),e((u<<31|2146435072)>>>0,o,a+n);else{var i;if(r<22250738585072014e-324)e((i=r/5e-324)>>>0,o,a+t),e((u<<31|i/4294967296)>>>0,o,a+n);else{var l=Math.floor(Math.log(r)/Math.LN2);1024===l&&(l=1023),e(4503599627370496*(i=r*Math.pow(2,-l))>>>0,o,a+t),e((u<<31|l+1023<<20|1048576*i&1048575)>>>0,o,a+n)}}}function n(e,t,n,r,o){var a=e(r,o+t),u=e(r,o+n),i=2*(u>>31)+1,l=u>>>20&2047,f=4294967296*(1048575&u)+a;return 2047===l?f?NaN:i*(1/0):0===l?5e-324*i*f:i*Math.pow(2,l-1075)*(f+4503599627370496)}e.writeDoubleLE=t.bind(null,u,0,4),e.writeDoubleBE=t.bind(null,i,4,0),e.readDoubleLE=n.bind(null,l,0,4),e.readDoubleBE=n.bind(null,f,4,0)}(),e}function u(e,t,n){t[n]=255&e,t[n+1]=e>>>8&255,t[n+2]=e>>>16&255,t[n+3]=e>>>24}function i(e,t,n){t[n]=e>>>24,t[n+1]=e>>>16&255,t[n+2]=e>>>8&255,t[n+3]=255&e}function l(e,t){return(e[t]|e[t+1]<<8|e[t+2]<<16|e[t+3]<<24)>>>0}function f(e,t){return(e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3])>>>0}n.exports=a(a),n.exports,n.exports.writeFloatLE,n.exports.writeFloatBE,n.exports.readFloatLE,n.exports.readFloatBE,n.exports.writeDoubleLE,n.exports.writeDoubleBE,n.exports.readDoubleLE,n.exports.readDoubleBE}),{})}}}));

System.register("chunks:///_virtual/index3.js",["./cjs-loader.mjs"],(function(t,e){var s;return{setters:[function(t){s=t.default}],execute:function(){var i=t("__cjsMetaURL",e.meta.url);s.define(i,(function(t,e,s,i,n){function r(){this._listeners={}}s.exports=r,r.prototype.on=function(t,e,s){return(this._listeners[t]||(this._listeners[t]=[])).push({fn:e,ctx:s||this}),this},r.prototype.off=function(t,e){if(void 0===t)this._listeners={};else if(void 0===e)this._listeners[t]=[];else for(var s=this._listeners[t],i=0;i<s.length;)s[i].fn===e?s.splice(i,1):++i;return this},r.prototype.emit=function(t){var e=this._listeners[t];if(e){for(var s=[],i=1;i<arguments.length;)s.push(arguments[i++]);for(i=0;i<e.length;)e[i].fn.apply(e[i++].ctx,s)}return this},s.exports}),{})}}}));

System.register("chunks:///_virtual/index4.js",["./cjs-loader.mjs"],(function(r,e){var t;return{setters:[function(r){t=r.default}],execute:function(){var a=r("__cjsMetaURL",e.meta.url);t.define(a,(function(r,e,t,a,n){var i=r;i.length=function(r){var e=r.length;if(!e)return 0;for(var t=0;--e%4>1&&"="===r.charAt(e);)++t;return Math.ceil(3*r.length)/4-t};for(var o=new Array(64),c=new Array(123),s=0;s<64;)c[o[s]=s<26?s+65:s<52?s+71:s<62?s-4:s-59|43]=s++;i.encode=function(r,e,t){for(var a,n=null,i=[],c=0,s=0;e<t;){var u=r[e++];switch(s){case 0:i[c++]=o[u>>2],a=(3&u)<<4,s=1;break;case 1:i[c++]=o[a|u>>4],a=(15&u)<<2,s=2;break;case 2:i[c++]=o[a|u>>6],i[c++]=o[63&u],s=0}c>8191&&((n||(n=[])).push(String.fromCharCode.apply(String,i)),c=0)}return s&&(i[c++]=o[a],i[c++]=61,1===s&&(i[c++]=61)),n?(c&&n.push(String.fromCharCode.apply(String,i.slice(0,c))),n.join("")):String.fromCharCode.apply(String,i.slice(0,c))};var u="invalid encoding";i.decode=function(r,e,t){for(var a,n=t,i=0,o=0;o<r.length;){var s=r.charCodeAt(o++);if(61===s&&i>1)break;if(void 0===(s=c[s]))throw Error(u);switch(i){case 0:a=s,i=1;break;case 1:e[t++]=a<<2|(48&s)>>4,a=s,i=2;break;case 2:e[t++]=(15&a)<<4|(60&s)>>2,a=s,i=3;break;case 3:e[t++]=(3&a)<<6|s,i=0}}if(1===i)throw Error(u);return t-n},i.test=function(r){return/^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/.test(r)},t.exports}),{})}}}));

System.register("chunks:///_virtual/index5.js",["./cjs-loader.mjs"],(function(r,t){var e;return{setters:[function(r){e=r.default}],execute:function(){var n=r("__cjsMetaURL",t.meta.url);e.define(n,(function(r,t,e,n,o){var a=r;a.length=function(r){for(var t=0,e=0,n=0;n<r.length;++n)(e=r.charCodeAt(n))<128?t+=1:e<2048?t+=2:55296==(64512&e)&&56320==(64512&r.charCodeAt(n+1))?(++n,t+=4):t+=3;return t},a.read=function(r,t,e){if(e-t<1)return"";for(var n,o=null,a=[],i=0;t<e;)(n=r[t++])<128?a[i++]=n:n>191&&n<224?a[i++]=(31&n)<<6|63&r[t++]:n>239&&n<365?(n=((7&n)<<18|(63&r[t++])<<12|(63&r[t++])<<6|63&r[t++])-65536,a[i++]=55296+(n>>10),a[i++]=56320+(1023&n)):a[i++]=(15&n)<<12|(63&r[t++])<<6|63&r[t++],i>8191&&((o||(o=[])).push(String.fromCharCode.apply(String,a)),i=0);return o?(i&&o.push(String.fromCharCode.apply(String,a.slice(0,i))),o.join("")):String.fromCharCode.apply(String,a.slice(0,i))},a.write=function(r,t,e){for(var n,o,a=e,i=0;i<r.length;++i)(n=r.charCodeAt(i))<128?t[e++]=n:n<2048?(t[e++]=n>>6|192,t[e++]=63&n|128):55296==(64512&n)&&56320==(64512&(o=r.charCodeAt(i+1)))?(n=65536+((1023&n)<<10)+(1023&o),++i,t[e++]=n>>18|240,t[e++]=n>>12&63|128,t[e++]=n>>6&63|128,t[e++]=63&n|128):(t[e++]=n>>12|224,t[e++]=n>>6&63|128,t[e++]=63&n|128);return e-a},e.exports}),{})}}}));

System.register("chunks:///_virtual/index6.js",["./cjs-loader.mjs"],(function(exports,module){var loader;return{setters:[function(e){loader=e.default}],execute:function(){var __cjsMetaURL=exports("__cjsMetaURL",module.meta.url);loader.define(__cjsMetaURL,(function(exports,require,module,__filename,__dirname){function inquire(moduleName){try{var mod=eval("quire".replace(/^/,"re"))(moduleName);if(mod&&(mod.length||Object.keys(mod).length))return mod}catch(e){}return null}module.exports=inquire,module.exports}),{})}}}));

System.register("chunks:///_virtual/index7.js",["./cjs-loader.mjs"],(function(e,t){var n;return{setters:[function(e){n=e.default}],execute:function(){var r=e("__cjsMetaURL",t.meta.url);n.define(r,(function(e,t,n,r,u){n.exports=function(e,t,n){var r=n||8192,u=r>>>1,c=null,i=r;return function(n){if(n<1||n>u)return e(n);i+n>r&&(c=e(r),i=0);var s=t.call(c,i,i+=n);return 7&i&&(i=1+(7|i)),s}},n.exports}),{})}}}));

System.register("chunks:///_virtual/index8.js",["./cjs-loader.mjs"],(function(e,n){var t;return{setters:[function(e){t=e.default}],execute:function(){var r=e("__cjsMetaURL",n.meta.url);t.define(r,(function(e,n,t,r,u){t.exports=function(e,n){var t=new Array(arguments.length-1),r=0,u=2,l=!0;for(;u<arguments.length;)t[r++]=arguments[u++];return new Promise((function(u,a){t[r]=function(e){if(l)if(l=!1,e)a(e);else{for(var n=new Array(arguments.length-1),t=0;t<n.length;)n[t++]=arguments[t];u.apply(null,n)}};try{e.apply(n||null,t)}catch(e){l&&(l=!1,a(e))}}))},t.exports}),{})}}}));

System.register("chunks:///_virtual/longbits.js",["./cjs-loader.mjs","./minimal2.js"],(function(t,i){var n,o;return{setters:[function(t){n=t.default},function(t){o=t.__cjsMetaURL}],execute:function(){var r=t("__cjsMetaURL",i.meta.url);n.define(r,(function(t,i,n,o,r){n.exports=h;var e=i("../util/minimal");function h(t,i){this.lo=t>>>0,this.hi=i>>>0}var s=h.zero=new h(0,0);s.toNumber=function(){return 0},s.zzEncode=s.zzDecode=function(){return this},s.length=function(){return 1};var u=h.zeroHash="\0\0\0\0\0\0\0\0";h.fromNumber=function(t){if(0===t)return s;var i=t<0;i&&(t=-t);var n=t>>>0,o=(t-n)/4294967296>>>0;return i&&(o=~o>>>0,n=~n>>>0,++n>4294967295&&(n=0,++o>4294967295&&(o=0))),new h(n,o)},h.from=function(t){if("number"==typeof t)return h.fromNumber(t);if(e.isString(t)){if(!e.Long)return h.fromNumber(parseInt(t,10));t=e.Long.fromString(t)}return t.low||t.high?new h(t.low>>>0,t.high>>>0):s},h.prototype.toNumber=function(t){if(!t&&this.hi>>>31){var i=1+~this.lo>>>0,n=~this.hi>>>0;return i||(n=n+1>>>0),-(i+4294967296*n)}return this.lo+4294967296*this.hi},h.prototype.toLong=function(t){return e.Long?new e.Long(0|this.lo,0|this.hi,Boolean(t)):{low:0|this.lo,high:0|this.hi,unsigned:Boolean(t)}};var l=String.prototype.charCodeAt;h.fromHash=function(t){return t===u?s:new h((l.call(t,0)|l.call(t,1)<<8|l.call(t,2)<<16|l.call(t,3)<<24)>>>0,(l.call(t,4)|l.call(t,5)<<8|l.call(t,6)<<16|l.call(t,7)<<24)>>>0)},h.prototype.toHash=function(){return String.fromCharCode(255&this.lo,this.lo>>>8&255,this.lo>>>16&255,this.lo>>>24,255&this.hi,this.hi>>>8&255,this.hi>>>16&255,this.hi>>>24)},h.prototype.zzEncode=function(){var t=this.hi>>31;return this.hi=((this.hi<<1|this.lo>>>31)^t)>>>0,this.lo=(this.lo<<1^t)>>>0,this},h.prototype.zzDecode=function(){var t=-(1&this.lo);return this.lo=((this.lo>>>1|this.hi<<31)^t)>>>0,this.hi=(this.hi>>>1^t)>>>0,this},h.prototype.length=function(){var t=this.lo,i=(this.lo>>>28|this.hi<<4)>>>0,n=this.hi>>>24;return 0===n?0===i?t<16384?t<128?1:2:t<2097152?3:4:i<16384?i<128?5:6:i<2097152?7:8:n<128?9:10},n.exports}),(function(){return{"../util/minimal":o}}))}}}));

System.register("chunks:///_virtual/minimal.js",["./cjs-loader.mjs","./index-minimal.js"],(function(e,n){var t,i;return{setters:[function(e){t=e.default},function(e){i=e.__cjsMetaURL}],execute:function(){e("default",void 0);var r=e("__cjsMetaURL",n.meta.url);t.define(r,(function(n,t,i,r,s){i.exports=t("./src/index-minimal"),e("default",i.exports)}),(function(){return{"./src/index-minimal":i}}))}}}));

System.register("chunks:///_virtual/minimal.mjs_cjs=&original=.js",["./minimal.js","./cjs-loader.mjs"],(function(e,t){var a,i;return{setters:[function(t){a=t.__cjsMetaURL;var i={};i.__cjsMetaURL=t.__cjsMetaURL,i.default=t.default,e(i)},function(e){i=e.default}],execute:function(){a||i.throwInvalidWrapper("./minimal.js",t.meta.url),i.require(a)}}}));

System.register("chunks:///_virtual/minimal2.js",["./cjs-loader.mjs","./index8.js","./index4.js","./index3.js","./index2.js","./index6.js","./index5.js","./index7.js","./longbits.js"],(function(e,r){var t,n,o,f,i,u,s,a,c;return{setters:[function(e){t=e.default},function(e){n=e.__cjsMetaURL},function(e){o=e.__cjsMetaURL},function(e){f=e.__cjsMetaURL},function(e){i=e.__cjsMetaURL},function(e){u=e.__cjsMetaURL},function(e){s=e.__cjsMetaURL},function(e){a=e.__cjsMetaURL},function(e){c=e.__cjsMetaURL}],execute:function(){var l=e("__cjsMetaURL",r.meta.url);t.define(l,(function(e,r,t,n,o){var f=e;function i(e,r,t){for(var n=Object.keys(r),o=0;o<n.length;++o)void 0!==e[n[o]]&&t||(e[n[o]]=r[n[o]]);return e}function u(e){function r(e,t){if(!(this instanceof r))return new r(e,t);Object.defineProperty(this,"message",{get:function(){return e}}),Error.captureStackTrace?Error.captureStackTrace(this,r):Object.defineProperty(this,"stack",{value:(new Error).stack||""}),t&&i(this,t)}return r.prototype=Object.create(Error.prototype,{constructor:{value:r,writable:!0,enumerable:!1,configurable:!0},name:{get:function(){return e},set:void 0,enumerable:!1,configurable:!0},toString:{value:function(){return this.name+": "+this.message},writable:!0,enumerable:!1,configurable:!0}}),r}f.asPromise=r("@protobufjs/aspromise"),f.base64=r("@protobufjs/base64"),f.EventEmitter=r("@protobufjs/eventemitter"),f.float=r("@protobufjs/float"),f.inquire=r("@protobufjs/inquire"),f.utf8=r("@protobufjs/utf8"),f.pool=r("@protobufjs/pool"),f.LongBits=r("./longbits"),f.isNode=Boolean("undefined"!=typeof global&&global&&global.process&&global.process.versions&&global.process.versions.node),f.global=f.isNode&&global||"undefined"!=typeof window&&window||"undefined"!=typeof self&&self||this,f.emptyArray=Object.freeze?Object.freeze([]):[],f.emptyObject=Object.freeze?Object.freeze({}):{},f.isInteger=Number.isInteger||function(e){return"number"==typeof e&&isFinite(e)&&Math.floor(e)===e},f.isString=function(e){return"string"==typeof e||e instanceof String},f.isObject=function(e){return e&&"object"==typeof e},f.isset=f.isSet=function(e,r){var t=e[r];return!(null==t||!e.hasOwnProperty(r))&&("object"!=typeof t||(Array.isArray(t)?t.length:Object.keys(t).length)>0)},f.Buffer=function(){try{var e=f.inquire("buffer").Buffer;return e.prototype.utf8Write?e:null}catch(e){return null}}(),f._Buffer_from=null,f._Buffer_allocUnsafe=null,f.newBuffer=function(e){return"number"==typeof e?f.Buffer?f._Buffer_allocUnsafe(e):new f.Array(e):f.Buffer?f._Buffer_from(e):"undefined"==typeof Uint8Array?e:new Uint8Array(e)},f.Array="undefined"!=typeof Uint8Array?Uint8Array:Array,f.Long=f.global.dcodeIO&&f.global.dcodeIO.Long||f.global.Long||f.inquire("long"),f.key2Re=/^true|false|0|1$/,f.key32Re=/^-?(?:0|[1-9][0-9]*)$/,f.key64Re=/^(?:[\\x00-\\xff]{8}|-?(?:0|[1-9][0-9]*))$/,f.longToHash=function(e){return e?f.LongBits.from(e).toHash():f.LongBits.zeroHash},f.longFromHash=function(e,r){var t=f.LongBits.fromHash(e);return f.Long?f.Long.fromBits(t.lo,t.hi,r):t.toNumber(Boolean(r))},f.merge=i,f.lcFirst=function(e){return e.charAt(0).toLowerCase()+e.substring(1)},f.newError=u,f.ProtocolError=u("ProtocolError"),f.oneOfGetter=function(e){for(var r={},t=0;t<e.length;++t)r[e[t]]=1;return function(){for(var e=Object.keys(this),t=e.length-1;t>-1;--t)if(1===r[e[t]]&&void 0!==this[e[t]]&&null!==this[e[t]])return e[t]}},f.oneOfSetter=function(e){return function(r){for(var t=0;t<e.length;++t)e[t]!==r&&delete this[e[t]]}},f.toJSONOptions={longs:String,enums:String,bytes:String,json:!0},f._configure=function(){var e=f.Buffer;e?(f._Buffer_from=e.from!==Uint8Array.from&&e.from||function(r,t){return new e(r,t)},f._Buffer_allocUnsafe=e.allocUnsafe||function(r){return new e(r)}):f._Buffer_from=f._Buffer_allocUnsafe=null},t.exports}),(function(){return{"@protobufjs/aspromise":n,"@protobufjs/base64":o,"@protobufjs/eventemitter":f,"@protobufjs/float":i,"@protobufjs/inquire":u,"@protobufjs/utf8":s,"@protobufjs/pool":a,"./longbits":c}}))}}}));

System.register("chunks:///_virtual/reader_buffer.js",["./cjs-loader.mjs","./reader.js","./minimal2.js"],(function(t,e){var i,r,n;return{setters:[function(t){i=t.default},function(t){r=t.__cjsMetaURL},function(t){n=t.__cjsMetaURL}],execute:function(){var s=t("__cjsMetaURL",e.meta.url);i.define(s,(function(t,e,i,r,n){i.exports=u;var s=e("./reader");(u.prototype=Object.create(s.prototype)).constructor=u;var o=e("./util/minimal");function u(t){s.call(this,t)}u._configure=function(){o.Buffer&&(u.prototype._slice=o.Buffer.prototype.slice)},u.prototype.string=function(){var t=this.uint32();return this.buf.utf8Slice?this.buf.utf8Slice(this.pos,this.pos=Math.min(this.pos+t,this.len)):this.buf.toString("utf-8",this.pos,this.pos=Math.min(this.pos+t,this.len))},u._configure(),i.exports}),(function(){return{"./reader":r,"./util/minimal":n}}))}}}));

System.register("chunks:///_virtual/reader.js",["./cjs-loader.mjs","./minimal2.js"],(function(t,i){var s,r;return{setters:[function(t){s=t.default},function(t){r=t.__cjsMetaURL}],execute:function(){var o=t("__cjsMetaURL",i.meta.url);s.define(o,(function(t,i,s,r,o){s.exports=p;var n,e=i("./util/minimal"),h=e.LongBits,u=e.utf8;function f(t,i){return RangeError("index out of range: "+t.pos+" + "+(i||1)+" > "+t.len)}function p(t){this.buf=t,this.pos=0,this.len=t.length}var l,a="undefined"!=typeof Uint8Array?function(t){if(t instanceof Uint8Array||Array.isArray(t))return new p(t);throw Error("illegal buffer")}:function(t){if(Array.isArray(t))return new p(t);throw Error("illegal buffer")},c=function(){return e.Buffer?function(t){return(p.create=function(t){return e.Buffer.isBuffer(t)?new n(t):a(t)})(t)}:a};function b(){var t=new h(0,0),i=0;if(!(this.len-this.pos>4)){for(;i<3;++i){if(this.pos>=this.len)throw f(this);if(t.lo=(t.lo|(127&this.buf[this.pos])<<7*i)>>>0,this.buf[this.pos++]<128)return t}return t.lo=(t.lo|(127&this.buf[this.pos++])<<7*i)>>>0,t}for(;i<4;++i)if(t.lo=(t.lo|(127&this.buf[this.pos])<<7*i)>>>0,this.buf[this.pos++]<128)return t;if(t.lo=(t.lo|(127&this.buf[this.pos])<<28)>>>0,t.hi=(t.hi|(127&this.buf[this.pos])>>4)>>>0,this.buf[this.pos++]<128)return t;if(i=0,this.len-this.pos>4){for(;i<5;++i)if(t.hi=(t.hi|(127&this.buf[this.pos])<<7*i+3)>>>0,this.buf[this.pos++]<128)return t}else for(;i<5;++i){if(this.pos>=this.len)throw f(this);if(t.hi=(t.hi|(127&this.buf[this.pos])<<7*i+3)>>>0,this.buf[this.pos++]<128)return t}throw Error("invalid varint encoding")}function y(t,i){return(t[i-4]|t[i-3]<<8|t[i-2]<<16|t[i-1]<<24)>>>0}function w(){if(this.pos+8>this.len)throw f(this,8);return new h(y(this.buf,this.pos+=4),y(this.buf,this.pos+=4))}p.create=c(),p.prototype._slice=e.Array.prototype.subarray||e.Array.prototype.slice,p.prototype.uint32=(l=4294967295,function(){if(l=(127&this.buf[this.pos])>>>0,this.buf[this.pos++]<128)return l;if(l=(l|(127&this.buf[this.pos])<<7)>>>0,this.buf[this.pos++]<128)return l;if(l=(l|(127&this.buf[this.pos])<<14)>>>0,this.buf[this.pos++]<128)return l;if(l=(l|(127&this.buf[this.pos])<<21)>>>0,this.buf[this.pos++]<128)return l;if(l=(l|(15&this.buf[this.pos])<<28)>>>0,this.buf[this.pos++]<128)return l;if((this.pos+=5)>this.len)throw this.pos=this.len,f(this,10);return l}),p.prototype.int32=function(){return 0|this.uint32()},p.prototype.sint32=function(){var t=this.uint32();return t>>>1^-(1&t)|0},p.prototype.bool=function(){return 0!==this.uint32()},p.prototype.fixed32=function(){if(this.pos+4>this.len)throw f(this,4);return y(this.buf,this.pos+=4)},p.prototype.sfixed32=function(){if(this.pos+4>this.len)throw f(this,4);return 0|y(this.buf,this.pos+=4)},p.prototype.float=function(){if(this.pos+4>this.len)throw f(this,4);var t=e.float.readFloatLE(this.buf,this.pos);return this.pos+=4,t},p.prototype.double=function(){if(this.pos+8>this.len)throw f(this,4);var t=e.float.readDoubleLE(this.buf,this.pos);return this.pos+=8,t},p.prototype.bytes=function(){var t=this.uint32(),i=this.pos,s=this.pos+t;if(s>this.len)throw f(this,t);if(this.pos+=t,Array.isArray(this.buf))return this.buf.slice(i,s);if(i===s){var r=e.Buffer;return r?r.alloc(0):new this.buf.constructor(0)}return this._slice.call(this.buf,i,s)},p.prototype.string=function(){var t=this.bytes();return u.read(t,0,t.length)},p.prototype.skip=function(t){if("number"==typeof t){if(this.pos+t>this.len)throw f(this,t);this.pos+=t}else do{if(this.pos>=this.len)throw f(this)}while(128&this.buf[this.pos++]);return this},p.prototype.skipType=function(t){switch(t){case 0:this.skip();break;case 1:this.skip(8);break;case 2:this.skip(this.uint32());break;case 3:for(;4!=(t=7&this.uint32());)this.skipType(t);break;case 5:this.skip(4);break;default:throw Error("invalid wire type "+t+" at offset "+this.pos)}return this},p._configure=function(t){n=t,p.create=c(),n._configure();var i=e.Long?"toLong":"toNumber";e.merge(p.prototype,{int64:function(){return b.call(this)[i](!1)},uint64:function(){return b.call(this)[i](!0)},sint64:function(){return b.call(this).zzDecode()[i](!1)},fixed64:function(){return w.call(this)[i](!0)},sfixed64:function(){return w.call(this)[i](!1)}})},s.exports}),(function(){return{"./util/minimal":r}}))}}}));

System.register("chunks:///_virtual/rollupPluginModLoBabelHelpers.js",[],(function(t){return{execute:function(){function e(t,e,r,n,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void r(t)}u.done?e(c):Promise.resolve(c).then(n,o)}function r(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,h(n.key),n)}}function n(){return(n=t("extends",Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t})).apply(this,arguments)}function o(e){return(o=t("getPrototypeOf",Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)}))(e)}function i(e,r){return(i=t("setPrototypeOf",Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t}))(e,r)}function a(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}function u(e,r,n){return(u=a()?t("construct",Reflect.construct.bind()):t("construct",(function(t,e,r){var n=[null];n.push.apply(n,e);var o=new(Function.bind.apply(t,n));return r&&i(o,r.prototype),o}))).apply(null,arguments)}function c(t){return-1!==Function.toString.call(t).indexOf("[native code]")}function l(e){var r="function"==typeof Map?new Map:void 0;return(l=t("wrapNativeSuper",(function(t){if(null===t||!c(t))return t;if("function"!=typeof t)throw new TypeError("Super expression must either be null or a function");if(void 0!==r){if(r.has(t))return r.get(t);r.set(t,e)}function e(){return u(t,arguments,o(this).constructor)}return e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),i(e,t)})))(e)}function f(t,e){if(t){if("string"==typeof t)return s(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?s(t,e):void 0}}function s(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function p(t,e){if("object"!=typeof t||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function h(t){var e=p(t,"string");return"symbol"==typeof e?e:String(e)}t({applyDecoratedDescriptor:function(t,e,r,n,o){var i={};Object.keys(n).forEach((function(t){i[t]=n[t]})),i.enumerable=!!i.enumerable,i.configurable=!!i.configurable,("value"in i||i.initializer)&&(i.writable=!0);i=r.slice().reverse().reduce((function(r,n){return n(t,e,r)||r}),i),o&&void 0!==i.initializer&&(i.value=i.initializer?i.initializer.call(o):void 0,i.initializer=void 0);void 0===i.initializer&&(Object.defineProperty(t,e,i),i=null);return i},arrayLikeToArray:s,assertThisInitialized:function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t},asyncToGenerator:function(t){return function(){var r=this,n=arguments;return new Promise((function(o,i){var a=t.apply(r,n);function u(t){e(a,o,i,u,c,"next",t)}function c(t){e(a,o,i,u,c,"throw",t)}u(void 0)}))}},construct:u,createClass:function(t,e,n){e&&r(t.prototype,e);n&&r(t,n);return Object.defineProperty(t,"prototype",{writable:!1}),t},createForOfIteratorHelperLoose:function(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(r)return(r=r.call(t)).next.bind(r);if(Array.isArray(t)||(r=f(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0;return function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},extends:n,getPrototypeOf:o,inheritsLoose:function(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t,i(t,e)},initializerDefineProperty:function(t,e,r,n){if(!r)return;Object.defineProperty(t,e,{enumerable:r.enumerable,configurable:r.configurable,writable:r.writable,value:r.initializer?r.initializer.call(n):void 0})},isNativeFunction:c,isNativeReflectConstruct:a,regeneratorRuntime:function(){
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
t("regeneratorRuntime",(function(){return r}));var e,r={},n=Object.prototype,o=n.hasOwnProperty,i=Object.defineProperty||function(t,e,r){t[e]=r.value},a="function"==typeof Symbol?Symbol:{},u=a.iterator||"@@iterator",c=a.asyncIterator||"@@asyncIterator",l=a.toStringTag||"@@toStringTag";function f(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(e){f=function(t,e,r){return t[e]=r}}function s(t,e,r,n){var o=e&&e.prototype instanceof g?e:g,a=Object.create(o.prototype),u=new T(n||[]);return i(a,"_invoke",{value:E(t,r,u)}),a}function p(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}r.wrap=s;var h="suspendedStart",y="executing",v="completed",d={};function g(){}function b(){}function m(){}var w={};f(w,u,(function(){return this}));var O=Object.getPrototypeOf,j=O&&O(O(k([])));j&&j!==n&&o.call(j,u)&&(w=j);var x=m.prototype=g.prototype=Object.create(w);function L(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function P(t,e){function r(n,i,a,u){var c=p(t[n],t,i);if("throw"!==c.type){var l=c.arg,f=l.value;return f&&"object"==typeof f&&o.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,a,u)}),(function(t){r("throw",t,a,u)})):e.resolve(f).then((function(t){l.value=t,a(l)}),(function(t){return r("throw",t,a,u)}))}u(c.arg)}var n;i(this,"_invoke",{value:function(t,o){function i(){return new e((function(e,n){r(t,o,e,n)}))}return n=n?n.then(i,i):i()}})}function E(t,r,n){var o=h;return function(i,a){if(o===y)throw new Error("Generator is already running");if(o===v){if("throw"===i)throw a;return{value:e,done:!0}}for(n.method=i,n.arg=a;;){var u=n.delegate;if(u){var c=_(u,n);if(c){if(c===d)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===h)throw o=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=y;var l=p(t,r,n);if("normal"===l.type){if(o=n.done?v:"suspendedYield",l.arg===d)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(o=v,n.method="throw",n.arg=l.arg)}}}function _(t,r){var n=r.method,o=t.iterator[n];if(o===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,_(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),d;var i=p(o,t.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,d;var a=i.arg;return a?a.done?(r[t.resultName]=a.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,d):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,d)}function S(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function N(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(S,this),this.reset(!0)}function k(t){if(t||""===t){var r=t[u];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,i=function r(){for(;++n<t.length;)if(o.call(t,n))return r.value=t[n],r.done=!1,r;return r.value=e,r.done=!0,r};return i.next=i}}throw new TypeError(typeof t+" is not iterable")}return b.prototype=m,i(x,"constructor",{value:m,configurable:!0}),i(m,"constructor",{value:b,configurable:!0}),b.displayName=f(m,l,"GeneratorFunction"),r.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===b||"GeneratorFunction"===(e.displayName||e.name))},r.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,f(t,l,"GeneratorFunction")),t.prototype=Object.create(x),t},r.awrap=function(t){return{__await:t}},L(P.prototype),f(P.prototype,c,(function(){return this})),r.AsyncIterator=P,r.async=function(t,e,n,o,i){void 0===i&&(i=Promise);var a=new P(s(t,e,n,o),i);return r.isGeneratorFunction(e)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},L(x),f(x,l,"Generator"),f(x,u,(function(){return this})),f(x,"toString",(function(){return"[object Generator]"})),r.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},r.values=k,T.prototype={constructor:T,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(N),!t)for(var r in this)"t"===r.charAt(0)&&o.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function n(n,o){return u.type="throw",u.arg=t,r.next=n,o&&(r.method="next",r.arg=e),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return n("end");if(a.tryLoc<=this.prev){var c=o.call(a,"catchLoc"),l=o.call(a,"finallyLoc");if(c&&l){if(this.prev<a.catchLoc)return n(a.catchLoc,!0);if(this.prev<a.finallyLoc)return n(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return n(a.catchLoc,!0)}else{if(!l)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return n(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&o.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var i=n;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,d):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),d},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),N(r),d}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;N(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:k(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),d}},r},setPrototypeOf:i,toPrimitive:p,toPropertyKey:h,unsupportedIterableToArray:f,wrapNativeSuper:l})}}}));

System.register("chunks:///_virtual/roots.js",["./cjs-loader.mjs"],(function(e,t){var r;return{setters:[function(e){r=e.default}],execute:function(){var s=e("__cjsMetaURL",t.meta.url);r.define(s,(function(e,t,r,s,n){r.exports={},r.exports}),{})}}}));

System.register("chunks:///_virtual/rpc.js",["./cjs-loader.mjs","./service.js"],(function(e,r){var t,c;return{setters:[function(e){t=e.default},function(e){c=e.__cjsMetaURL}],execute:function(){var n=e("__cjsMetaURL",r.meta.url);t.define(n,(function(e,r,t,c,n){e.Service=r("./rpc/service"),t.exports}),(function(){return{"./rpc/service":c}}))}}}));

System.register("chunks:///_virtual/service.js",["./cjs-loader.mjs","./minimal2.js"],(function(e,t){var r,i;return{setters:[function(e){r=e.default},function(e){i=e.__cjsMetaURL}],execute:function(){var n=e("__cjsMetaURL",t.meta.url);r.define(n,(function(e,t,r,i,n){r.exports=u;var o=t("../util/minimal");function u(e,t,r){if("function"!=typeof e)throw TypeError("rpcImpl must be a function");o.EventEmitter.call(this),this.rpcImpl=e,this.requestDelimited=Boolean(t),this.responseDelimited=Boolean(r)}(u.prototype=Object.create(o.EventEmitter.prototype)).constructor=u,u.prototype.rpcCall=function e(t,r,i,n,u){if(!n)throw TypeError("request must be specified");var c=this;if(!u)return o.asPromise(e,c,t,r,i,n);if(c.rpcImpl)try{return c.rpcImpl(t,r[c.requestDelimited?"encodeDelimited":"encode"](n).finish(),(function(e,r){if(e)return c.emit("error",e,t),u(e);if(null!==r){if(!(r instanceof i))try{r=i[c.responseDelimited?"decodeDelimited":"decode"](r)}catch(e){return c.emit("error",e,t),u(e)}return c.emit("data",r,t),u(null,r)}c.end(!0)}))}catch(e){return c.emit("error",e,t),void setTimeout((function(){u(e)}),0)}else setTimeout((function(){u(Error("already ended"))}),0)},u.prototype.end=function(e){return this.rpcImpl&&(e||this.rpcImpl(null,null,null),this.rpcImpl=null,this.emit("end").off()),this},r.exports}),(function(){return{"../util/minimal":i}}))}}}));

System.register("chunks:///_virtual/writer_buffer.js",["./cjs-loader.mjs","./writer.js","./minimal2.js"],(function(t,e){var r,n,i;return{setters:[function(t){r=t.default},function(t){n=t.__cjsMetaURL},function(t){i=t.__cjsMetaURL}],execute:function(){var f=t("__cjsMetaURL",e.meta.url);r.define(f,(function(t,e,r,n,i){r.exports=o;var f=e("./writer");(o.prototype=Object.create(f.prototype)).constructor=o;var u=e("./util/minimal");function o(){f.call(this)}function s(t,e,r){t.length<40?u.utf8.write(t,e,r):e.utf8Write?e.utf8Write(t,r):e.write(t,r)}o._configure=function(){o.alloc=u._Buffer_allocUnsafe,o.writeBytesBuffer=u.Buffer&&u.Buffer.prototype instanceof Uint8Array&&"set"===u.Buffer.prototype.set.name?function(t,e,r){e.set(t,r)}:function(t,e,r){if(t.copy)t.copy(e,r,0,t.length);else for(var n=0;n<t.length;)e[r++]=t[n++]}},o.prototype.bytes=function(t){u.isString(t)&&(t=u._Buffer_from(t,"base64"));var e=t.length>>>0;return this.uint32(e),e&&this._push(o.writeBytesBuffer,e,t),this},o.prototype.string=function(t){var e=u.Buffer.byteLength(t);return this.uint32(e),e&&this._push(s,e,t),this},o._configure(),r.exports}),(function(){return{"./writer":n,"./util/minimal":i}}))}}}));

System.register("chunks:///_virtual/writer.js",["./cjs-loader.mjs","./minimal2.js"],(function(t,n){var i,e;return{setters:[function(t){i=t.default},function(t){e=t.__cjsMetaURL}],execute:function(){var o=t("__cjsMetaURL",n.meta.url);i.define(o,(function(t,n,i,e,o){i.exports=c;var r,s=n("./util/minimal"),h=s.LongBits,u=s.base64,a=s.utf8;function l(t,n,i){this.fn=t,this.len=n,this.next=void 0,this.val=i}function p(){}function f(t){this.head=t.head,this.tail=t.tail,this.len=t.len,this.next=t.states}function c(){this.len=0,this.head=new l(p,0,0),this.tail=this.head,this.states=null}var y=function(){return s.Buffer?function(){return(c.create=function(){return new r})()}:function(){return new c}};function d(t,n,i){n[i]=255&t}function _(t,n){this.len=t,this.next=void 0,this.val=n}function v(t,n,i){for(;t.hi;)n[i++]=127&t.lo|128,t.lo=(t.lo>>>7|t.hi<<25)>>>0,t.hi>>>=7;for(;t.lo>127;)n[i++]=127&t.lo|128,t.lo=t.lo>>>7;n[i++]=t.lo}function x(t,n,i){n[i]=255&t,n[i+1]=t>>>8&255,n[i+2]=t>>>16&255,n[i+3]=t>>>24}c.create=y(),c.alloc=function(t){return new s.Array(t)},s.Array!==Array&&(c.alloc=s.pool(c.alloc,s.Array.prototype.subarray)),c.prototype._push=function(t,n,i){return this.tail=this.tail.next=new l(t,n,i),this.len+=n,this},_.prototype=Object.create(l.prototype),_.prototype.fn=function(t,n,i){for(;t>127;)n[i++]=127&t|128,t>>>=7;n[i]=t},c.prototype.uint32=function(t){return this.len+=(this.tail=this.tail.next=new _((t>>>=0)<128?1:t<16384?2:t<2097152?3:t<268435456?4:5,t)).len,this},c.prototype.int32=function(t){return t<0?this._push(v,10,h.fromNumber(t)):this.uint32(t)},c.prototype.sint32=function(t){return this.uint32((t<<1^t>>31)>>>0)},c.prototype.uint64=function(t){var n=h.from(t);return this._push(v,n.length(),n)},c.prototype.int64=c.prototype.uint64,c.prototype.sint64=function(t){var n=h.from(t).zzEncode();return this._push(v,n.length(),n)},c.prototype.bool=function(t){return this._push(d,1,t?1:0)},c.prototype.fixed32=function(t){return this._push(x,4,t>>>0)},c.prototype.sfixed32=c.prototype.fixed32,c.prototype.fixed64=function(t){var n=h.from(t);return this._push(x,4,n.lo)._push(x,4,n.hi)},c.prototype.sfixed64=c.prototype.fixed64,c.prototype.float=function(t){return this._push(s.float.writeFloatLE,4,t)},c.prototype.double=function(t){return this._push(s.float.writeDoubleLE,8,t)};var m=s.Array.prototype.set?function(t,n,i){n.set(t,i)}:function(t,n,i){for(var e=0;e<t.length;++e)n[i+e]=t[e]};c.prototype.bytes=function(t){var n=t.length>>>0;if(!n)return this._push(d,1,0);if(s.isString(t)){var i=c.alloc(n=u.length(t));u.decode(t,i,0),t=i}return this.uint32(n)._push(m,n,t)},c.prototype.string=function(t){var n=a.length(t);return n?this.uint32(n)._push(a.write,n,t):this._push(d,1,0)},c.prototype.fork=function(){return this.states=new f(this),this.head=this.tail=new l(p,0,0),this.len=0,this},c.prototype.reset=function(){return this.states?(this.head=this.states.head,this.tail=this.states.tail,this.len=this.states.len,this.states=this.states.next):(this.head=this.tail=new l(p,0,0),this.len=0),this},c.prototype.ldelim=function(){var t=this.head,n=this.tail,i=this.len;return this.reset().uint32(i),i&&(this.tail.next=t.next,this.tail=n,this.len+=i),this},c.prototype.finish=function(){for(var t=this.head.next,n=this.constructor.alloc(this.len),i=0;t;)t.fn(t.val,n,i),i+=t.len,t=t.next;return n},c._configure=function(t){r=t,c.create=y(),r._configure()},i.exports}),(function(){return{"./util/minimal":e}}))}}}));

} }; });