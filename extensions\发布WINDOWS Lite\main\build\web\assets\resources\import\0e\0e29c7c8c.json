[1, ["1feRGgvc1Dub1Hkeoh6HT5"], ["_effectAsset"], [["cc.EffectAsset", ["_name", "shaders", "techniques"], 0], ["cc.Material", ["_name", "_props", "_states", "_defines"], -1]], [[0, 0, 1, 2, 4], [1, 0, 1, 2, 3, 5]], [[[[0, "../resources/res/shader/videoRGB", [{"hash": 2996630752, "name": "../resources/res/shader/videoRGB|unlit-vs:vert|unlit-fs:frag", "blocks": [{"name": "Constant", "stageFlags": 16, "binding": 0, "members": [{"name": "mainColor", "type": 16, "count": 1}], "defines": []}, {"name": "ARGS", "stageFlags": 16, "binding": 1, "members": [{"name": "transFlag", "type": 13, "count": 1}, {"name": "offset", "type": 13, "count": 1}], "defines": []}], "samplerTextures": [{"name": "texture0", "type": 28, "count": 1, "stageFlags": 16, "binding": 2, "defines": []}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "format": 32, "location": 0, "defines": []}, {"name": "a_normal", "format": 32, "location": 1, "defines": []}, {"name": "a_texCoord", "format": 21, "location": 2, "defines": []}, {"name": "a_tangent", "format": 44, "location": 3, "defines": []}, {"name": "a_joints", "location": 4, "defines": ["CC_USE_SKINNING"]}, {"name": "a_weights", "format": 44, "location": 5, "defines": ["CC_USE_SKINNING"]}, {"name": "a_jointAnimInfo", "format": 44, "isInstanced": true, "location": 6, "defines": ["USE_INSTANCING", "CC_USE_BAKED_ANIMATION"]}, {"name": "a_matWorld0", "format": 44, "isInstanced": true, "location": 7, "defines": ["USE_INSTANCING"]}, {"name": "a_matWorld1", "format": 44, "isInstanced": true, "location": 8, "defines": ["USE_INSTANCING"]}, {"name": "a_matWorld2", "format": 44, "isInstanced": true, "location": 9, "defines": ["USE_INSTANCING"]}, {"name": "a_lightingMapUVParam", "format": 44, "isInstanced": true, "location": 10, "defines": ["USE_INSTANCING", "CC_USE_LIGHTMAP"]}, {"name": "a_localShadowBiasAndProbeId", "format": 44, "isInstanced": true, "location": 11, "defines": ["USE_INSTANCING"]}, {"name": "a_reflectionProbeData", "format": 44, "isInstanced": true, "location": 12, "defines": ["USE_INSTANCING", "CC_USE_REFLECTION_PROBE"]}, {"name": "a_sh_linear_const_r", "format": 44, "isInstanced": true, "location": 13, "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"]}, {"name": "a_sh_linear_const_g", "format": 44, "isInstanced": true, "location": 14, "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"]}, {"name": "a_sh_linear_const_b", "format": 44, "isInstanced": true, "location": 15, "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"]}, {"name": "a_vertexId", "format": 11, "location": 16, "defines": ["CC_USE_MORPH"]}, {"name": "a_color", "format": 44, "location": 17, "defines": []}, {"name": "a_texCoord1", "format": 21, "location": 18, "defines": ["HAS_SECOND_UV"]}], "fragColors": [{"name": "cc_FragColor", "typename": "vec4", "type": 16, "count": 1, "stageFlags": 16, "location": 0, "defines": []}], "descriptors": [{"rate": 0, "blocks": [{"name": "CCMorph", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_displacementWeights", "typename": "vec4", "type": 16, "count": 15, "isArray": true}, {"name": "cc_displacementTextureInfo", "typename": "vec4", "type": 16, "count": 1}], "defines": ["CC_USE_MORPH"]}, {"name": "CCSkinningTexture", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_jointTextureInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "CCSkinningAnimation", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_jointAnimInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "CCSkinning", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_joints", "typename": "vec4", "type": 16, "count": 0, "precision": "highp ", "isArray": true}], "defines": ["CC_USE_SKINNING", "!CC_USE_BAKED_ANIMATION", "!CC_USE_REAL_TIME_JOINT_TEXTURE"]}], "samplerTextures": [{"name": "cc_PositionDisplacements", "typename": "sampler2D", "type": 28, "count": 1, "stageFlags": 1, "tags": {"builtin": "local"}, "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_POSITION"]}, {"name": "cc_NormalDisplacements", "typename": "sampler2D", "type": 28, "count": 1, "stageFlags": 1, "tags": {"builtin": "local"}, "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_NORMAL"]}, {"name": "cc_TangentDisplacements", "typename": "sampler2D", "type": 28, "count": 1, "stageFlags": 1, "tags": {"builtin": "local"}, "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_TANGENT"]}, {"name": "cc_jointTexture", "typename": "sampler2D", "type": 28, "count": 1, "precision": "highp ", "stageFlags": 1, "tags": {"builtin": "local"}, "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "cc_realtimeJoint", "typename": "sampler2D", "type": 28, "count": 1, "precision": "highp ", "stageFlags": 1, "tags": {"builtin": "local"}, "defines": ["CC_USE_SKINNING", "!CC_USE_BAKED_ANIMATION", "CC_USE_REAL_TIME_JOINT_TEXTURE"]}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [{"name": "Constant", "stageFlags": 16, "binding": 0, "members": [{"name": "mainColor", "type": 16, "count": 1}], "defines": []}, {"name": "ARGS", "stageFlags": 16, "binding": 1, "members": [{"name": "transFlag", "type": 13, "count": 1}, {"name": "offset", "type": 13, "count": 1}], "defines": []}], "samplerTextures": [{"name": "texture0", "type": 28, "count": 1, "stageFlags": 16, "binding": 2, "defines": []}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [{"name": "CCGlobal", "stageFlags": 17, "tags": {"builtin": "global"}, "members": [{"name": "cc_time", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_screenSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nativeSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_probeInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_debug_view_mode", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}, {"name": "CCCamera", "stageFlags": 17, "tags": {"builtin": "global"}, "members": [{"name": "cc_matView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_cameraPos", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_surfaceTransform", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_screenScale", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_exposure", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitDir", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientSky", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientGround", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogBase", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogAdd", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nearFar", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_viewPort", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "glsl1": {"vert": "\nprecision highp float;\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nstruct StandardVertInput {\n  highp vec4 position;\n  vec3 normal;\n  vec4 tangent;\n};\nattribute vec3 a_position;\nattribute vec3 a_normal;\nattribute vec2 a_texCoord;\nattribute vec4 a_tangent;\n#if CC_USE_SKINNING\n    attribute vec4 a_joints;\n  attribute vec4 a_weights;\n#endif\n#if USE_INSTANCING\n  #if CC_USE_BAKED_ANIMATION\n    attribute highp vec4 a_jointAnimInfo;\n  #endif\n  attribute vec4 a_matWorld0;\n  attribute vec4 a_matWorld1;\n  attribute vec4 a_matWorld2;\n  #if CC_USE_LIGHTMAP\n    attribute vec4 a_lightingMapUVParam;\n  #endif\n  #if CC_USE_REFLECTION_PROBE || CC_RECEIVE_SHADOW\n    #if CC_RECEIVE_SHADOW\n    #endif\n    attribute vec4 a_localShadowBiasAndProbeId;\n  #endif\n  #if CC_USE_REFLECTION_PROBE\n    attribute vec4 a_reflectionProbeData;\n  #endif\n  #if CC_USE_LIGHT_PROBE\n    attribute vec4 a_sh_linear_const_r;\n    attribute vec4 a_sh_linear_const_g;\n    attribute vec4 a_sh_linear_const_b;\n  #endif\n#endif\n#if CC_USE_MORPH\n    attribute float a_vertexId;\n    int getVertexId() {\n      return int(a_vertexId);\n    }\n#endif\nhighp float decode32 (highp vec4 rgba) {\n  rgba = rgba * 255.0;\n  highp float Sign = 1.0 - (step(128.0, (rgba[3]) + 0.5)) * 2.0;\n  highp float Exponent = 2.0 * (mod(float(int((rgba[3]) + 0.5)), 128.0)) + (step(128.0, (rgba[2]) + 0.5)) - 127.0;\n  highp float Mantissa = (mod(float(int((rgba[2]) + 0.5)), 128.0)) * 65536.0 + rgba[1] * 256.0 + rgba[0] + 8388608.0;\n  return Sign * exp2(Exponent - 23.0) * Mantissa;\n}\n#if CC_USE_MORPH\n  uniform vec4 cc_displacementWeights[15];\n  uniform vec4 cc_displacementTextureInfo;\n  #if CC_MORPH_TARGET_HAS_POSITION\n    uniform sampler2D cc_PositionDisplacements;\n  #endif\n  #if CC_MORPH_TARGET_HAS_NORMAL\n    uniform sampler2D cc_NormalDisplacements;\n  #endif\n  #if CC_MORPH_TARGET_HAS_TANGENT\n    uniform sampler2D cc_TangentDisplacements;\n  #endif\n  vec2 getPixelLocation(vec2 textureResolution, int pixelIndex) {\n    float pixelIndexF = float(pixelIndex);\n    float x = mod(pixelIndexF, textureResolution.x);\n    float y = floor(pixelIndexF / textureResolution.x);\n    return vec2(x, y);\n  }\n  vec2 getPixelCoordFromLocation(vec2 location, vec2 textureResolution) {\n    return (vec2(location.x, location.y) + .5) / textureResolution;\n  }\n  #if CC_DEVICE_SUPPORT_FLOAT_TEXTURE\n      vec4 fetchVec3ArrayFromTexture(sampler2D tex, int elementIndex) {\n        int pixelIndex = elementIndex;\n        vec2 location = getPixelLocation(cc_displacementTextureInfo.xy, pixelIndex);\n        vec2 uv = getPixelCoordFromLocation(location, cc_displacementTextureInfo.xy);\n        return texture2D(tex, uv);\n      }\n  #else\n    vec4 fetchVec3ArrayFromTexture(sampler2D tex, int elementIndex) {\n      int pixelIndex = elementIndex * 4;\n      vec2 location = getPixelLocation(cc_displacementTextureInfo.xy, pixelIndex);\n      vec2 x = getPixelCoordFromLocation(location + vec2(0.0, 0.0), cc_displacementTextureInfo.xy);\n      vec2 y = getPixelCoordFromLocation(location + vec2(1.0, 0.0), cc_displacementTextureInfo.xy);\n      vec2 z = getPixelCoordFromLocation(location + vec2(2.0, 0.0), cc_displacementTextureInfo.xy);\n      return vec4(\n        decode32(texture2D(tex, x)),\n        decode32(texture2D(tex, y)),\n        decode32(texture2D(tex, z)),\n        1.0\n      );\n    }\n  #endif\n  float getDisplacementWeight(int index) {\n    int quot = index / 4;\n    int remainder = index - quot * 4;\n    if (remainder == 0) {\n      return cc_displacementWeights[quot].x;\n    } else if (remainder == 1) {\n      return cc_displacementWeights[quot].y;\n    } else if (remainder == 2) {\n      return cc_displacementWeights[quot].z;\n    } else {\n      return cc_displacementWeights[quot].w;\n    }\n  }\n  vec3 getVec3DisplacementFromTexture(sampler2D tex, int vertexIndex) {\n  #if CC_MORPH_PRECOMPUTED\n    return fetchVec3ArrayFromTexture(tex, vertexIndex).rgb;\n  #else\n    vec3 result = vec3(0, 0, 0);\n    int nVertices = int(cc_displacementTextureInfo.z);\n    for (int iTarget = 0; iTarget < CC_MORPH_TARGET_COUNT; ++iTarget) {\n      result += (fetchVec3ArrayFromTexture(tex, nVertices * iTarget + vertexIndex).rgb * getDisplacementWeight(iTarget));\n    }\n    return result;\n  #endif\n  }\n  #if CC_MORPH_TARGET_HAS_POSITION\n  vec3 getPositionDisplacement(int vertexId) {\n      return getVec3DisplacementFromTexture(cc_PositionDisplacements, vertexId);\n  }\n  #endif\n  #if CC_MORPH_TARGET_HAS_NORMAL\n  vec3 getNormalDisplacement(int vertexId) {\n      return getVec3DisplacementFromTexture(cc_NormalDisplacements, vertexId);\n  }\n  #endif\n  #if CC_MORPH_TARGET_HAS_TANGENT\n  vec3 getTangentDisplacement(int vertexId) {\n      return getVec3DisplacementFromTexture(cc_TangentDisplacements, vertexId);\n  }\n  #endif\n  void applyMorph (inout vec4 position, inout vec3 normal, inout vec4 tangent) {\n    int vertexId = getVertexId();\n  #if CC_MORPH_TARGET_HAS_POSITION\n    position.xyz = position.xyz + getPositionDisplacement(vertexId);\n  #endif\n  #if CC_MORPH_TARGET_HAS_NORMAL\n    normal.xyz = normal.xyz + getNormalDisplacement(vertexId);\n  #endif\n  #if CC_MORPH_TARGET_HAS_TANGENT\n    tangent.xyz = tangent.xyz + getTangentDisplacement(vertexId);\n  #endif\n  }\n  void applyMorph (inout vec4 position) {\n  #if CC_MORPH_TARGET_HAS_POSITION\n    position.xyz = position.xyz + getPositionDisplacement(getVertexId());\n  #endif\n  }\n#endif\n#if CC_USE_SKINNING\n  #if CC_USE_BAKED_ANIMATION\n    uniform highp vec4 cc_jointTextureInfo;\n    uniform highp vec4 cc_jointAnimInfo;\n    uniform highp sampler2D cc_jointTexture;\n    void CCGetJointTextureCoords(float pixelsPerJoint, float jointIdx, out highp float x, out highp float y, out highp float invSize)\n    {\n      #if USE_INSTANCING\n        highp float temp = pixelsPerJoint * (a_jointAnimInfo.x * a_jointAnimInfo.y + jointIdx) + a_jointAnimInfo.z;\n      #else\n        highp float temp = pixelsPerJoint * (cc_jointAnimInfo.x * cc_jointTextureInfo.y + jointIdx) + cc_jointTextureInfo.z;\n      #endif\n      invSize = cc_jointTextureInfo.w;\n      highp float tempY = floor(temp * invSize);\n      x = floor(temp - tempY * cc_jointTextureInfo.x);\n      y = (tempY + 0.5) * invSize;\n    }\n  #else\n    #if CC_USE_REAL_TIME_JOINT_TEXTURE\n      uniform highp sampler2D cc_realtimeJoint;\n    #else\n      uniform highp vec4 cc_joints[CC_JOINT_UNIFORM_CAPACITY * 3];\n    #endif\n  #endif\n  #if CC_USE_BAKED_ANIMATION\n    #if CC_DEVICE_SUPPORT_FLOAT_TEXTURE\n      mat4 getJointMatrix (float i) {\n        highp float x, y, invSize;\n        CCGetJointTextureCoords(3.0, i, x, y, invSize);\n        vec4 v1 = texture2D(cc_jointTexture, vec2((x + 0.5) * invSize, y));\n        vec4 v2 = texture2D(cc_jointTexture, vec2((x + 1.5) * invSize, y));\n        vec4 v3 = texture2D(cc_jointTexture, vec2((x + 2.5) * invSize, y));\n        return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n      }\n    #else\n      mat4 getJointMatrix (float i) {\n        highp float x, y, invSize;\n        CCGetJointTextureCoords(12.0, i, x, y, invSize);\n        vec4 v1 = vec4(\n          decode32(texture2D(cc_jointTexture, vec2((x + 0.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 1.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 2.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 3.5) * invSize, y)))\n        );\n        vec4 v2 = vec4(\n          decode32(texture2D(cc_jointTexture, vec2((x + 4.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 5.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 6.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 7.5) * invSize, y)))\n        );\n        vec4 v3 = vec4(\n          decode32(texture2D(cc_jointTexture, vec2((x + 8.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 9.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 10.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 11.5) * invSize, y)))\n        );\n        return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n      }\n    #endif\n  #else\n    #if CC_USE_REAL_TIME_JOINT_TEXTURE\n      #if CC_DEVICE_SUPPORT_FLOAT_TEXTURE\n        mat4 getJointMatrix (float i) {\n          float x = i;\n          vec4 v1 = texture2D(cc_realtimeJoint, vec2( x / 256.0, 0.5 / 3.0));\n          vec4 v2 = texture2D(cc_realtimeJoint, vec2( x / 256.0, 1.5 / 3.0));\n          vec4 v3 = texture2D(cc_realtimeJoint, vec2( x / 256.0, 2.5 / 3.0));\n          return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n        }\n      #else\n        mat4 getJointMatrix (float i) {\n         float x = 4.0 * i;\n          vec4 v1 = vec4(\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 0.5)/ 1024.0, 0.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 1.5)/ 1024.0, 0.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 2.5)/ 1024.0, 0.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 3.5)/ 1024.0, 0.5 / 3.0)))\n          );\n          vec4 v2 = vec4(\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 0.5)/ 1024.0, 1.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 1.5)/ 1024.0, 1.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 2.5)/ 1024.0, 1.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 3.5)/ 1024.0, 1.5 / 3.0)))\n          );\n          vec4 v3 = vec4(\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 0.5)/ 1024.0, 2.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 1.5)/ 1024.0, 2.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 2.5)/ 1024.0, 2.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 3.5)/ 1024.0, 2.5 / 3.0)))\n          );\n          return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n        }\n      #endif\n    #else\n      mat4 getJointMatrix (float i) {\n        int idx = int(i);\n        vec4 v1 = cc_joints[idx * 3];\n        vec4 v2 = cc_joints[idx * 3 + 1];\n        vec4 v3 = cc_joints[idx * 3 + 2];\n        return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n      }\n    #endif\n  #endif\n  mat4 skinMatrix () {\n    vec4 joints = vec4(a_joints);\n    return getJointMatrix(joints.x) * a_weights.x\n         + getJointMatrix(joints.y) * a_weights.y\n         + getJointMatrix(joints.z) * a_weights.z\n         + getJointMatrix(joints.w) * a_weights.w;\n  }\n  void CCSkin (inout vec4 position) {\n    mat4 m = skinMatrix();\n    position = m * position;\n  }\n  void CCSkin (inout vec4 position, inout vec3 normal, inout vec4 tangent) {\n    mat4 m = skinMatrix();\n    position = m * position;\n    normal = (m * vec4(normal, 0.0)).xyz;\n    tangent.xyz = (m * vec4(tangent.xyz, 0.0)).xyz;\n  }\n#endif\nvoid CCVertInput(inout StandardVertInput In)\n{\n    In.position = vec4(a_position, 1.0);\n    In.normal = a_normal;\n    In.tangent = a_tangent;\n  #if CC_USE_MORPH\n    applyMorph(In.position, In.normal, In.tangent);\n  #endif\n  #if CC_USE_SKINNING\n    CCSkin(In.position, In.normal, In.tangent);\n  #endif\n}\nuniform highp mat4 cc_matViewProj;\nattribute vec4 a_color;\n#if HAS_SECOND_UV\n  attribute vec2 a_texCoord1;\n#endif\nvarying vec3 v_position;\nvarying vec3 v_normal;\nvarying vec3 v_tangent;\nvarying vec3 v_bitangent;\nvarying vec2 v_uv;\nvarying vec2 v_uv1;\nvarying vec4 v_color;\nvec4 vert () {\n  StandardVertInput In;\n  CCVertInput(In);\n  v_uv = a_texCoord;\n  #if HAS_SECOND_UV\n    v_uv1 = a_texCoord1;\n  #endif\n  v_color = a_color;\n  return cc_matViewProj * In.position;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision highp float;\nvec4 CCFragOutput (vec4 color) {\n  return color;\n}\nvarying vec2 v_uv;\nuniform sampler2D texture0;\n    uniform vec4 mainColor;\n    uniform float transFlag;\n    uniform float offset;\nvec4 frag () {\n  vec4 col = vec4(1, 1, 1, 1);\n  vec2 uv = v_uv;\n  if (transFlag > 0.0) {\n    uv.y = uv.y + (uv.y - 0.5) * uv.x * offset;\n  } else if (transFlag < 0.0) {\n    uv.y = uv.y + (uv.y - 0.5) * (1.0 - uv.x) * offset;\n  }\n  col.rgb = texture2D(texture0, uv).rgb;\n  if (uv.y < 0.0 || uv.y > 1.0) {\n    col.a = 0.0;\n  }\n  col = mainColor * col;\n  return CCFragOutput(col);\n}\nvoid main() { gl_FragColor = frag(); }"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}, {"name": "CCCamera", "defines": []}], "samplerTextures": [], "buffers": [], "images": []}, "locals": {"blocks": [{"name": "CCMorph", "defines": ["CC_USE_MORPH"]}, {"name": "CCSkinningTexture", "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "CCSkinningAnimation", "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "CCSkinning", "defines": ["CC_USE_SKINNING", "!CC_USE_BAKED_ANIMATION", "!CC_USE_REAL_TIME_JOINT_TEXTURE"]}], "samplerTextures": [{"name": "cc_PositionDisplacements", "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_POSITION"]}, {"name": "cc_NormalDisplacements", "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_NORMAL"]}, {"name": "cc_TangentDisplacements", "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_TANGENT"]}, {"name": "cc_jointTexture", "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "cc_realtimeJoint", "defines": ["CC_USE_SKINNING", "!CC_USE_BAKED_ANIMATION", "CC_USE_REAL_TIME_JOINT_TEXTURE"]}], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 60, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 45}}, "defines": [{"name": "USE_INSTANCING", "type": "boolean"}, {"name": "CC_USE_SKINNING", "type": "boolean"}, {"name": "CC_USE_BAKED_ANIMATION", "type": "boolean"}, {"name": "CC_USE_LIGHTMAP", "type": "boolean"}, {"name": "CC_USE_REFLECTION_PROBE", "type": "boolean"}, {"name": "CC_RECEIVE_SHADOW", "type": "boolean"}, {"name": "CC_USE_LIGHT_PROBE", "type": "boolean"}, {"name": "CC_USE_MORPH", "type": "boolean"}, {"name": "CC_MORPH_TARGET_COUNT", "type": "number", "range": [2, 8]}, {"name": "CC_MORPH_TARGET_HAS_POSITION", "type": "boolean"}, {"name": "CC_MORPH_TARGET_HAS_NORMAL", "type": "boolean"}, {"name": "CC_MORPH_TARGET_HAS_TANGENT", "type": "boolean"}, {"name": "CC_MORPH_PRECOMPUTED", "type": "boolean"}, {"name": "CC_USE_REAL_TIME_JOINT_TEXTURE", "type": "boolean"}, {"name": "HAS_SECOND_UV", "type": "boolean"}]}, {"hash": 364228726, "name": "../resources/res/shader/videoRGB|legacy/main-functions/general-vs:vert|unlit-fs:frag", "blocks": [{"name": "Constant", "stageFlags": 16, "binding": 0, "members": [{"name": "mainColor", "type": 16, "count": 1}], "defines": []}, {"name": "ARGS", "stageFlags": 16, "binding": 1, "members": [{"name": "transFlag", "type": 13, "count": 1}, {"name": "offset", "type": 13, "count": 1}], "defines": []}], "samplerTextures": [{"name": "texture0", "type": 28, "count": 1, "stageFlags": 16, "binding": 2, "defines": []}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "format": 32, "location": 0, "defines": []}, {"name": "a_normal", "format": 32, "location": 1, "defines": []}, {"name": "a_texCoord", "format": 21, "location": 2, "defines": []}, {"name": "a_tangent", "format": 44, "location": 3, "defines": []}, {"name": "a_joints", "location": 4, "defines": ["CC_USE_SKINNING"]}, {"name": "a_weights", "format": 44, "location": 5, "defines": ["CC_USE_SKINNING"]}, {"name": "a_jointAnimInfo", "format": 44, "isInstanced": true, "location": 6, "defines": ["USE_INSTANCING", "CC_USE_BAKED_ANIMATION"]}, {"name": "a_matWorld0", "format": 44, "isInstanced": true, "location": 7, "defines": ["USE_INSTANCING"]}, {"name": "a_matWorld1", "format": 44, "isInstanced": true, "location": 8, "defines": ["USE_INSTANCING"]}, {"name": "a_matWorld2", "format": 44, "isInstanced": true, "location": 9, "defines": ["USE_INSTANCING"]}, {"name": "a_lightingMapUVParam", "format": 44, "isInstanced": true, "location": 10, "defines": ["USE_INSTANCING", "CC_USE_LIGHTMAP"]}, {"name": "a_localShadowBiasAndProbeId", "format": 44, "isInstanced": true, "location": 11, "defines": ["USE_INSTANCING"]}, {"name": "a_reflectionProbeData", "format": 44, "isInstanced": true, "location": 12, "defines": ["USE_INSTANCING", "CC_USE_REFLECTION_PROBE"]}, {"name": "a_sh_linear_const_r", "format": 44, "isInstanced": true, "location": 13, "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"]}, {"name": "a_sh_linear_const_g", "format": 44, "isInstanced": true, "location": 14, "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"]}, {"name": "a_sh_linear_const_b", "format": 44, "isInstanced": true, "location": 15, "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"]}, {"name": "a_vertexId", "format": 11, "location": 16, "defines": ["CC_USE_MORPH"]}, {"name": "a_color", "format": 44, "location": 17, "defines": []}, {"name": "a_texCoord1", "format": 21, "location": 18, "defines": ["HAS_SECOND_UV"]}], "fragColors": [{"name": "cc_FragColor", "typename": "vec4", "type": 16, "count": 1, "stageFlags": 16, "location": 0, "defines": []}], "descriptors": [{"rate": 0, "blocks": [{"name": "CCMorph", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_displacementWeights", "typename": "vec4", "type": 16, "count": 15, "isArray": true}, {"name": "cc_displacementTextureInfo", "typename": "vec4", "type": 16, "count": 1}], "defines": ["CC_USE_MORPH"]}, {"name": "CCSkinningTexture", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_jointTextureInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "CCSkinningAnimation", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_jointAnimInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "CCSkinning", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_joints", "typename": "vec4", "type": 16, "count": 0, "precision": "highp ", "isArray": true}], "defines": ["CC_USE_SKINNING", "!CC_USE_BAKED_ANIMATION", "!CC_USE_REAL_TIME_JOINT_TEXTURE"]}, {"name": "CCLocal", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_matWorld", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matWorldIT", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_lightingMapUVParam", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_localShadowBias", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": ["!USE_INSTANCING"]}], "samplerTextures": [{"name": "cc_PositionDisplacements", "typename": "sampler2D", "type": 28, "count": 1, "stageFlags": 1, "tags": {"builtin": "local"}, "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_POSITION"]}, {"name": "cc_NormalDisplacements", "typename": "sampler2D", "type": 28, "count": 1, "stageFlags": 1, "tags": {"builtin": "local"}, "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_NORMAL"]}, {"name": "cc_TangentDisplacements", "typename": "sampler2D", "type": 28, "count": 1, "stageFlags": 1, "tags": {"builtin": "local"}, "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_TANGENT"]}, {"name": "cc_jointTexture", "typename": "sampler2D", "type": 28, "count": 1, "precision": "highp ", "stageFlags": 1, "tags": {"builtin": "local"}, "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "cc_realtimeJoint", "typename": "sampler2D", "type": 28, "count": 1, "precision": "highp ", "stageFlags": 1, "tags": {"builtin": "local"}, "defines": ["CC_USE_SKINNING", "!CC_USE_BAKED_ANIMATION", "CC_USE_REAL_TIME_JOINT_TEXTURE"]}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [{"name": "Constant", "stageFlags": 16, "binding": 0, "members": [{"name": "mainColor", "type": 16, "count": 1}], "defines": []}, {"name": "ARGS", "stageFlags": 16, "binding": 1, "members": [{"name": "transFlag", "type": 13, "count": 1}, {"name": "offset", "type": 13, "count": 1}], "defines": []}], "samplerTextures": [{"name": "texture0", "type": 28, "count": 1, "stageFlags": 16, "binding": 2, "defines": []}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [{"name": "CCGlobal", "stageFlags": 17, "tags": {"builtin": "global"}, "members": [{"name": "cc_time", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_screenSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nativeSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_probeInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_debug_view_mode", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}, {"name": "CCCamera", "stageFlags": 17, "tags": {"builtin": "global"}, "members": [{"name": "cc_matView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_cameraPos", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_surfaceTransform", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_screenScale", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_exposure", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitDir", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientSky", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientGround", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogBase", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogAdd", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nearFar", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_viewPort", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}, {"name": "CCShadow", "stageFlags": 1, "tags": {"builtin": "global"}, "members": [{"name": "cc_matLightView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matLightViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_shadowInvProjDepthInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_shadowProjDepthInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_shadowProjInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_shadowNFLSInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_shadowWHPBInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_shadowLPNNInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_shadowColor", "typename": "vec4", "type": 16, "count": 1, "precision": "lowp "}, {"name": "cc_planarNDInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}, {"name": "CCCSM", "stageFlags": 1, "tags": {"builtin": "global"}, "members": [{"name": "cc_csmViewDir0", "typename": "vec4", "type": 16, "count": 4, "precision": "highp ", "isArray": true}, {"name": "cc_csmViewDir1", "typename": "vec4", "type": 16, "count": 4, "precision": "highp ", "isArray": true}, {"name": "cc_csmViewDir2", "typename": "vec4", "type": 16, "count": 4, "precision": "highp ", "isArray": true}, {"name": "cc_csmAtlas", "typename": "vec4", "type": 16, "count": 4, "precision": "highp ", "isArray": true}, {"name": "cc_matCSMViewProj", "typename": "mat4", "type": 25, "count": 4, "precision": "highp ", "isArray": true}, {"name": "cc_csmProjDepthInfo", "typename": "vec4", "type": 16, "count": 4, "precision": "highp ", "isArray": true}, {"name": "cc_csmProjInfo", "typename": "vec4", "type": 16, "count": 4, "precision": "highp ", "isArray": true}, {"name": "cc_csmSplitsInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": ["CC_SUPPORT_CASCADED_SHADOW_MAP"]}], "samplerTextures": [{"name": "cc_shadowMap", "typename": "sampler2D", "type": 28, "count": 1, "precision": "highp ", "stageFlags": 1, "tags": {"builtin": "global"}, "defines": ["CC_RECEIVE_SHADOW"]}, {"name": "cc_spotShadowMap", "typename": "sampler2D", "type": 28, "count": 1, "precision": "highp ", "stageFlags": 1, "tags": {"builtin": "global"}, "defines": ["CC_RECEIVE_SHADOW"]}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "glsl1": {"vert": "\n#ifdef GL_EXT_shader_texture_lod\n#extension GL_EXT_shader_texture_lod: enable\n#endif\nprecision highp float;\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nstruct StandardVertInput {\n  highp vec4 position;\n  vec3 normal;\n  vec4 tangent;\n};\nattribute vec3 a_position;\nattribute vec3 a_normal;\nattribute vec2 a_texCoord;\nattribute vec4 a_tangent;\n#if CC_USE_SKINNING\n    attribute vec4 a_joints;\n  attribute vec4 a_weights;\n#endif\n#if USE_INSTANCING\n  #if CC_USE_BAKED_ANIMATION\n    attribute highp vec4 a_jointAnimInfo;\n  #endif\n  attribute vec4 a_matWorld0;\n  attribute vec4 a_matWorld1;\n  attribute vec4 a_matWorld2;\n  #if CC_USE_LIGHTMAP\n    attribute vec4 a_lightingMapUVParam;\n  #endif\n  #if CC_USE_REFLECTION_PROBE || CC_RECEIVE_SHADOW\n    #if CC_RECEIVE_SHADOW\n    #endif\n    attribute vec4 a_localShadowBiasAndProbeId;\n  #endif\n  #if CC_USE_REFLECTION_PROBE\n    attribute vec4 a_reflectionProbeData;\n  #endif\n  #if CC_USE_LIGHT_PROBE\n    attribute vec4 a_sh_linear_const_r;\n    attribute vec4 a_sh_linear_const_g;\n    attribute vec4 a_sh_linear_const_b;\n  #endif\n#endif\n#if CC_USE_MORPH\n    attribute float a_vertexId;\n    int getVertexId() {\n      return int(a_vertexId);\n    }\n#endif\nhighp float decode32 (highp vec4 rgba) {\n  rgba = rgba * 255.0;\n  highp float Sign = 1.0 - (step(128.0, (rgba[3]) + 0.5)) * 2.0;\n  highp float Exponent = 2.0 * (mod(float(int((rgba[3]) + 0.5)), 128.0)) + (step(128.0, (rgba[2]) + 0.5)) - 127.0;\n  highp float Mantissa = (mod(float(int((rgba[2]) + 0.5)), 128.0)) * 65536.0 + rgba[1] * 256.0 + rgba[0] + 8388608.0;\n  return Sign * exp2(Exponent - 23.0) * Mantissa;\n}\n#if CC_USE_MORPH\n  uniform vec4 cc_displacementWeights[15];\n  uniform vec4 cc_displacementTextureInfo;\n  #if CC_MORPH_TARGET_HAS_POSITION\n    uniform sampler2D cc_PositionDisplacements;\n  #endif\n  #if CC_MORPH_TARGET_HAS_NORMAL\n    uniform sampler2D cc_NormalDisplacements;\n  #endif\n  #if CC_MORPH_TARGET_HAS_TANGENT\n    uniform sampler2D cc_TangentDisplacements;\n  #endif\n  vec2 getPixelLocation(vec2 textureResolution, int pixelIndex) {\n    float pixelIndexF = float(pixelIndex);\n    float x = mod(pixelIndexF, textureResolution.x);\n    float y = floor(pixelIndexF / textureResolution.x);\n    return vec2(x, y);\n  }\n  vec2 getPixelCoordFromLocation(vec2 location, vec2 textureResolution) {\n    return (vec2(location.x, location.y) + .5) / textureResolution;\n  }\n  #if CC_DEVICE_SUPPORT_FLOAT_TEXTURE\n      vec4 fetchVec3ArrayFromTexture(sampler2D tex, int elementIndex) {\n        int pixelIndex = elementIndex;\n        vec2 location = getPixelLocation(cc_displacementTextureInfo.xy, pixelIndex);\n        vec2 uv = getPixelCoordFromLocation(location, cc_displacementTextureInfo.xy);\n        return texture2D(tex, uv);\n      }\n  #else\n    vec4 fetchVec3ArrayFromTexture(sampler2D tex, int elementIndex) {\n      int pixelIndex = elementIndex * 4;\n      vec2 location = getPixelLocation(cc_displacementTextureInfo.xy, pixelIndex);\n      vec2 x = getPixelCoordFromLocation(location + vec2(0.0, 0.0), cc_displacementTextureInfo.xy);\n      vec2 y = getPixelCoordFromLocation(location + vec2(1.0, 0.0), cc_displacementTextureInfo.xy);\n      vec2 z = getPixelCoordFromLocation(location + vec2(2.0, 0.0), cc_displacementTextureInfo.xy);\n      return vec4(\n        decode32(texture2D(tex, x)),\n        decode32(texture2D(tex, y)),\n        decode32(texture2D(tex, z)),\n        1.0\n      );\n    }\n  #endif\n  float getDisplacementWeight(int index) {\n    int quot = index / 4;\n    int remainder = index - quot * 4;\n    if (remainder == 0) {\n      return cc_displacementWeights[quot].x;\n    } else if (remainder == 1) {\n      return cc_displacementWeights[quot].y;\n    } else if (remainder == 2) {\n      return cc_displacementWeights[quot].z;\n    } else {\n      return cc_displacementWeights[quot].w;\n    }\n  }\n  vec3 getVec3DisplacementFromTexture(sampler2D tex, int vertexIndex) {\n  #if CC_MORPH_PRECOMPUTED\n    return fetchVec3ArrayFromTexture(tex, vertexIndex).rgb;\n  #else\n    vec3 result = vec3(0, 0, 0);\n    int nVertices = int(cc_displacementTextureInfo.z);\n    for (int iTarget = 0; iTarget < CC_MORPH_TARGET_COUNT; ++iTarget) {\n      result += (fetchVec3ArrayFromTexture(tex, nVertices * iTarget + vertexIndex).rgb * getDisplacementWeight(iTarget));\n    }\n    return result;\n  #endif\n  }\n  #if CC_MORPH_TARGET_HAS_POSITION\n  vec3 getPositionDisplacement(int vertexId) {\n      return getVec3DisplacementFromTexture(cc_PositionDisplacements, vertexId);\n  }\n  #endif\n  #if CC_MORPH_TARGET_HAS_NORMAL\n  vec3 getNormalDisplacement(int vertexId) {\n      return getVec3DisplacementFromTexture(cc_NormalDisplacements, vertexId);\n  }\n  #endif\n  #if CC_MORPH_TARGET_HAS_TANGENT\n  vec3 getTangentDisplacement(int vertexId) {\n      return getVec3DisplacementFromTexture(cc_TangentDisplacements, vertexId);\n  }\n  #endif\n  void applyMorph (inout vec4 position, inout vec3 normal, inout vec4 tangent) {\n    int vertexId = getVertexId();\n  #if CC_MORPH_TARGET_HAS_POSITION\n    position.xyz = position.xyz + getPositionDisplacement(vertexId);\n  #endif\n  #if CC_MORPH_TARGET_HAS_NORMAL\n    normal.xyz = normal.xyz + getNormalDisplacement(vertexId);\n  #endif\n  #if CC_MORPH_TARGET_HAS_TANGENT\n    tangent.xyz = tangent.xyz + getTangentDisplacement(vertexId);\n  #endif\n  }\n  void applyMorph (inout vec4 position) {\n  #if CC_MORPH_TARGET_HAS_POSITION\n    position.xyz = position.xyz + getPositionDisplacement(getVertexId());\n  #endif\n  }\n#endif\n#if CC_USE_SKINNING\n  #if CC_USE_BAKED_ANIMATION\n    uniform highp vec4 cc_jointTextureInfo;\n    uniform highp vec4 cc_jointAnimInfo;\n    uniform highp sampler2D cc_jointTexture;\n    void CCGetJointTextureCoords(float pixelsPerJoint, float jointIdx, out highp float x, out highp float y, out highp float invSize)\n    {\n      #if USE_INSTANCING\n        highp float temp = pixelsPerJoint * (a_jointAnimInfo.x * a_jointAnimInfo.y + jointIdx) + a_jointAnimInfo.z;\n      #else\n        highp float temp = pixelsPerJoint * (cc_jointAnimInfo.x * cc_jointTextureInfo.y + jointIdx) + cc_jointTextureInfo.z;\n      #endif\n      invSize = cc_jointTextureInfo.w;\n      highp float tempY = floor(temp * invSize);\n      x = floor(temp - tempY * cc_jointTextureInfo.x);\n      y = (tempY + 0.5) * invSize;\n    }\n  #else\n    #if CC_USE_REAL_TIME_JOINT_TEXTURE\n      uniform highp sampler2D cc_realtimeJoint;\n    #else\n      uniform highp vec4 cc_joints[CC_JOINT_UNIFORM_CAPACITY * 3];\n    #endif\n  #endif\n  #if CC_USE_BAKED_ANIMATION\n    #if CC_DEVICE_SUPPORT_FLOAT_TEXTURE\n      mat4 getJointMatrix (float i) {\n        highp float x, y, invSize;\n        CCGetJointTextureCoords(3.0, i, x, y, invSize);\n        vec4 v1 = texture2D(cc_jointTexture, vec2((x + 0.5) * invSize, y));\n        vec4 v2 = texture2D(cc_jointTexture, vec2((x + 1.5) * invSize, y));\n        vec4 v3 = texture2D(cc_jointTexture, vec2((x + 2.5) * invSize, y));\n        return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n      }\n    #else\n      mat4 getJointMatrix (float i) {\n        highp float x, y, invSize;\n        CCGetJointTextureCoords(12.0, i, x, y, invSize);\n        vec4 v1 = vec4(\n          decode32(texture2D(cc_jointTexture, vec2((x + 0.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 1.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 2.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 3.5) * invSize, y)))\n        );\n        vec4 v2 = vec4(\n          decode32(texture2D(cc_jointTexture, vec2((x + 4.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 5.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 6.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 7.5) * invSize, y)))\n        );\n        vec4 v3 = vec4(\n          decode32(texture2D(cc_jointTexture, vec2((x + 8.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 9.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 10.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 11.5) * invSize, y)))\n        );\n        return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n      }\n    #endif\n  #else\n    #if CC_USE_REAL_TIME_JOINT_TEXTURE\n      #if CC_DEVICE_SUPPORT_FLOAT_TEXTURE\n        mat4 getJointMatrix (float i) {\n          float x = i;\n          vec4 v1 = texture2D(cc_realtimeJoint, vec2( x / 256.0, 0.5 / 3.0));\n          vec4 v2 = texture2D(cc_realtimeJoint, vec2( x / 256.0, 1.5 / 3.0));\n          vec4 v3 = texture2D(cc_realtimeJoint, vec2( x / 256.0, 2.5 / 3.0));\n          return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n        }\n      #else\n        mat4 getJointMatrix (float i) {\n         float x = 4.0 * i;\n          vec4 v1 = vec4(\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 0.5)/ 1024.0, 0.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 1.5)/ 1024.0, 0.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 2.5)/ 1024.0, 0.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 3.5)/ 1024.0, 0.5 / 3.0)))\n          );\n          vec4 v2 = vec4(\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 0.5)/ 1024.0, 1.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 1.5)/ 1024.0, 1.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 2.5)/ 1024.0, 1.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 3.5)/ 1024.0, 1.5 / 3.0)))\n          );\n          vec4 v3 = vec4(\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 0.5)/ 1024.0, 2.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 1.5)/ 1024.0, 2.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 2.5)/ 1024.0, 2.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 3.5)/ 1024.0, 2.5 / 3.0)))\n          );\n          return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n        }\n      #endif\n    #else\n      mat4 getJointMatrix (float i) {\n        int idx = int(i);\n        vec4 v1 = cc_joints[idx * 3];\n        vec4 v2 = cc_joints[idx * 3 + 1];\n        vec4 v3 = cc_joints[idx * 3 + 2];\n        return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n      }\n    #endif\n  #endif\n  mat4 skinMatrix () {\n    vec4 joints = vec4(a_joints);\n    return getJointMatrix(joints.x) * a_weights.x\n         + getJointMatrix(joints.y) * a_weights.y\n         + getJointMatrix(joints.z) * a_weights.z\n         + getJointMatrix(joints.w) * a_weights.w;\n  }\n  void CCSkin (inout vec4 position) {\n    mat4 m = skinMatrix();\n    position = m * position;\n  }\n  void CCSkin (inout vec4 position, inout vec3 normal, inout vec4 tangent) {\n    mat4 m = skinMatrix();\n    position = m * position;\n    normal = (m * vec4(normal, 0.0)).xyz;\n    tangent.xyz = (m * vec4(tangent.xyz, 0.0)).xyz;\n  }\n#endif\nvoid CCVertInput(inout StandardVertInput In)\n{\n    In.position = vec4(a_position, 1.0);\n    In.normal = a_normal;\n    In.tangent = a_tangent;\n  #if CC_USE_MORPH\n    applyMorph(In.position, In.normal, In.tangent);\n  #endif\n  #if CC_USE_SKINNING\n    CCSkin(In.position, In.normal, In.tangent);\n  #endif\n}\nuniform highp mat4 cc_matView;\n  uniform highp mat4 cc_matProj;\n  uniform highp vec4 cc_cameraPos;\n  uniform mediump vec4 cc_fogBase;\n  uniform mediump vec4 cc_fogAdd;\n#if !USE_INSTANCING\n  uniform highp mat4 cc_matWorld;\n  uniform highp mat4 cc_matWorldIT;\n#endif\nvoid CCGetWorldMatrixFull(out mat4 matWorld, out mat4 matWorldIT)\n{\n  #if USE_INSTANCING\n    matWorld = mat4(\n      vec4(a_matWorld0.xyz, 0.0),\n      vec4(a_matWorld1.xyz, 0.0),\n      vec4(a_matWorld2.xyz, 0.0),\n      vec4(a_matWorld0.w, a_matWorld1.w, a_matWorld2.w, 1.0)\n    );\n    vec3 scale = 1.0 / vec3(length(a_matWorld0.xyz), length(a_matWorld1.xyz), length(a_matWorld2.xyz));\n    vec3 scale2 = scale * scale;\n    matWorldIT = mat4(\n      vec4(a_matWorld0.xyz * scale2.x, 0.0),\n      vec4(a_matWorld1.xyz * scale2.y, 0.0),\n      vec4(a_matWorld2.xyz * scale2.z, 0.0),\n      vec4(0.0, 0.0, 0.0, 1.0)\n    );\n  #else\n    matWorld = cc_matWorld;\n    matWorldIT = cc_matWorldIT;\n  #endif\n}\n#if CC_USE_FOG != 4\n  float LinearFog(vec4 pos, vec3 cameraPos, float fogStart, float fogEnd) {\n      vec4 wPos = pos;\n      float cam_dis = distance(cameraPos, wPos.xyz);\n      return clamp((fogEnd - cam_dis) / (fogEnd - fogStart), 0., 1.);\n  }\n  float ExpFog(vec4 pos, vec3 cameraPos, float fogStart, float fogDensity, float fogAtten) {\n      vec4 wPos = pos;\n      float cam_dis = max(distance(cameraPos, wPos.xyz) - fogStart, 0.0) / fogAtten * 4.;\n      float f = exp(-cam_dis * fogDensity);\n      return f;\n  }\n  float ExpSquaredFog(vec4 pos, vec3 cameraPos, float fogStart, float fogDensity, float fogAtten) {\n      vec4 wPos = pos;\n      float cam_dis = max(distance(cameraPos, wPos.xyz) - fogStart, 0.0) / fogAtten * 4.;\n      float f = exp(-cam_dis * cam_dis * fogDensity * fogDensity);\n      return f;\n  }\n  float LayeredFog(vec4 pos, vec3 cameraPos, float fogTop, float fogRange, float fogAtten) {\n      vec4 wPos = pos;\n      vec3 camWorldProj = cameraPos.xyz;\n      camWorldProj.y = 0.;\n      vec3 worldPosProj = wPos.xyz;\n      worldPosProj.y = 0.;\n      float fDeltaD = distance(worldPosProj, camWorldProj) / fogAtten * 2.0;\n      float fDeltaY, fDensityIntegral;\n      if (cameraPos.y > fogTop) {\n          if (wPos.y < fogTop) {\n              fDeltaY = (fogTop - wPos.y) / fogRange * 2.0;\n              fDensityIntegral = fDeltaY * fDeltaY * 0.5;\n          }\n          else {\n              fDeltaY = 0.;\n              fDensityIntegral = 0.;\n          }\n      }\n      else {\n          if (wPos.y < fogTop) {\n              float fDeltaA = (fogTop - cameraPos.y) / fogRange * 2.;\n              float fDeltaB = (fogTop - wPos.y) / fogRange * 2.;\n              fDeltaY = abs(fDeltaA - fDeltaB);\n              fDensityIntegral = abs((fDeltaA * fDeltaA * 0.5) - (fDeltaB * fDeltaB * 0.5));\n          }\n          else {\n              fDeltaY = abs(fogTop - cameraPos.y) / fogRange * 2.;\n              fDensityIntegral = abs(fDeltaY * fDeltaY * 0.5);\n          }\n      }\n      float fDensity;\n      if (fDeltaY != 0.) {\n          fDensity = (sqrt(1.0 + ((fDeltaD / fDeltaY) * (fDeltaD / fDeltaY)))) * fDensityIntegral;\n      }\n      else {\n          fDensity = 0.;\n      }\n      float f = exp(-fDensity);\n      return f;\n  }\n#endif\nvoid CC_TRANSFER_FOG_BASE(vec4 pos, out float factor)\n{\n#if CC_USE_FOG == 0\n\tfactor = LinearFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.y);\n#elif CC_USE_FOG == 1\n\tfactor = ExpFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.z, cc_fogAdd.z);\n#elif CC_USE_FOG == 2\n\tfactor = ExpSquaredFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.z, cc_fogAdd.z);\n#elif CC_USE_FOG == 3\n\tfactor = LayeredFog(pos, cc_cameraPos.xyz, cc_fogAdd.x, cc_fogAdd.y, cc_fogAdd.z);\n#else\n\tfactor = 1.0;\n#endif\n}\n#if !CC_USE_ACCURATE_FOG\nvarying mediump float v_fog_factor;\n#endif\nvoid CC_TRANSFER_FOG(vec4 pos) {\n#if !CC_USE_ACCURATE_FOG\n    CC_TRANSFER_FOG_BASE(pos, v_fog_factor);\n#endif\n}\nvarying highp vec4 v_shadowPos;\nuniform highp mat4 cc_matLightViewProj;\n#if CC_SUPPORT_CASCADED_SHADOW_MAP\n  #endif\n#if defined(CC_USE_METAL) || defined(CC_USE_WGPU)\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y) y = -y\n#else\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y)\n#endif\n#if CC_RECEIVE_SHADOW\n  uniform highp sampler2D cc_shadowMap;\n  uniform highp sampler2D cc_spotShadowMap;\n    #define UnpackBitFromFloat(value, bit) (mod(floor(value / pow(10.0, float(bit))), 10.0) > 0.0)\n  #if CC_SUPPORT_CASCADED_SHADOW_MAP\n  #else\n  #endif\n#endif\n#if CC_RECEIVE_SHADOW\n#endif\nattribute vec4 a_color;\n#if HAS_SECOND_UV\n  attribute vec2 a_texCoord1;\n#endif\nvarying vec3 v_position;\nvarying vec3 v_normal;\nvarying vec3 v_tangent;\nvarying vec3 v_bitangent;\nvarying vec2 v_uv;\nvarying vec2 v_uv1;\nvarying vec4 v_color;\nvec4 vert () {\n  StandardVertInput In;\n  CCVertInput(In);\n  mat4 matWorld, matWorldIT;\n  CCGetWorldMatrixFull(matWorld, matWorldIT);\n  vec4 pos = matWorld * In.position;\n  v_position = pos.xyz;\n  v_normal = normalize((matWorldIT * vec4(In.normal, 0.0)).xyz);\n  v_tangent = normalize((matWorld * vec4(In.tangent.xyz, 0.0)).xyz);\n  v_bitangent = cross(v_normal, v_tangent) * In.tangent.w;\n  v_uv = a_texCoord;\n  #if HAS_SECOND_UV\n    v_uv1 = a_texCoord1;\n  #endif\n  v_color = a_color;\n  CC_TRANSFER_FOG(pos);\n  v_shadowPos = cc_matLightViewProj * pos;\n  return cc_matProj * (cc_matView * matWorld) * In.position;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision highp float;\nvec4 CCFragOutput (vec4 color) {\n  return color;\n}\nvarying vec2 v_uv;\nuniform sampler2D texture0;\n    uniform vec4 mainColor;\n    uniform float transFlag;\n    uniform float offset;\nvec4 frag () {\n  vec4 col = vec4(1, 1, 1, 1);\n  vec2 uv = v_uv;\n  if (transFlag > 0.0) {\n    uv.y = uv.y + (uv.y - 0.5) * uv.x * offset;\n  } else if (transFlag < 0.0) {\n    uv.y = uv.y + (uv.y - 0.5) * (1.0 - uv.x) * offset;\n  }\n  col.rgb = texture2D(texture0, uv).rgb;\n  if (uv.y < 0.0 || uv.y > 1.0) {\n    col.a = 0.0;\n  }\n  col = mainColor * col;\n  return CCFragOutput(col);\n}\nvoid main() { gl_FragColor = frag(); }"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}, {"name": "CCCamera", "defines": []}, {"name": "CCShadow", "defines": []}, {"name": "CCCSM", "defines": ["CC_SUPPORT_CASCADED_SHADOW_MAP"]}], "samplerTextures": [{"name": "cc_shadowMap", "defines": ["CC_RECEIVE_SHADOW"]}, {"name": "cc_spotShadowMap", "defines": ["CC_RECEIVE_SHADOW"]}], "buffers": [], "images": []}, "locals": {"blocks": [{"name": "CCMorph", "defines": ["CC_USE_MORPH"]}, {"name": "CCSkinningTexture", "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "CCSkinningAnimation", "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "CCSkinning", "defines": ["CC_USE_SKINNING", "!CC_USE_BAKED_ANIMATION", "!CC_USE_REAL_TIME_JOINT_TEXTURE"]}, {"name": "CCLocal", "defines": ["!USE_INSTANCING"]}], "samplerTextures": [{"name": "cc_PositionDisplacements", "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_POSITION"]}, {"name": "cc_NormalDisplacements", "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_NORMAL"]}, {"name": "cc_TangentDisplacements", "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_TANGENT"]}, {"name": "cc_jointTexture", "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "cc_realtimeJoint", "defines": ["CC_USE_SKINNING", "!CC_USE_BAKED_ANIMATION", "CC_USE_REAL_TIME_JOINT_TEXTURE"]}], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 131, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 45}}, "defines": [{"name": "USE_INSTANCING", "type": "boolean"}, {"name": "CC_USE_SKINNING", "type": "boolean"}, {"name": "CC_USE_BAKED_ANIMATION", "type": "boolean"}, {"name": "CC_USE_LIGHTMAP", "type": "boolean"}, {"name": "CC_USE_REFLECTION_PROBE", "type": "boolean"}, {"name": "CC_RECEIVE_SHADOW", "type": "boolean"}, {"name": "CC_USE_LIGHT_PROBE", "type": "boolean"}, {"name": "CC_USE_MORPH", "type": "boolean"}, {"name": "CC_MORPH_TARGET_COUNT", "type": "number", "range": [2, 8]}, {"name": "CC_MORPH_TARGET_HAS_POSITION", "type": "boolean"}, {"name": "CC_MORPH_TARGET_HAS_NORMAL", "type": "boolean"}, {"name": "CC_MORPH_TARGET_HAS_TANGENT", "type": "boolean"}, {"name": "CC_MORPH_PRECOMPUTED", "type": "boolean"}, {"name": "CC_USE_REAL_TIME_JOINT_TEXTURE", "type": "boolean"}, {"name": "CC_USE_FOG", "type": "number", "range": [0, 4]}, {"name": "CC_USE_ACCURATE_FOG", "type": "boolean"}, {"name": "CC_SUPPORT_CASCADED_SHADOW_MAP", "type": "boolean"}, {"name": "HAS_SECOND_UV", "type": "boolean"}]}], [{"name": "opaque", "passes": [{"program": "../resources/res/shader/videoRGB|unlit-vs:vert|unlit-fs:frag", "properties": {"texture0": {"value": "white", "type": 28}, "mainColor": {"type": 16, "value": [1, 1, 1, 1]}, "transFlag": {"type": 13, "value": [1]}, "offset": {"type": 13, "value": [0.5]}}}]}, {"name": "transparent", "passes": [{"program": "../resources/res/shader/videoRGB|legacy/main-functions/general-vs:vert|unlit-fs:frag", "blendState": {"targets": [{"blend": true, "blendSrc": 2, "blendDst": 4, "blendSrcAlpha": 2, "blendDstAlpha": 4}]}, "properties": {"texture0": {"value": "white", "type": 28}, "mainColor": {"type": 16, "value": [1, 1, 1, 1]}, "transFlag": {"type": 13, "value": [1]}, "offset": {"type": 13, "value": [0.5]}}}]}]]], 0, 0, [], [], []], [[[1, "videoRGBSprite", [{"transFlag": -1}], [{"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}], [{}]]], 0, 0, [0], [0], [0]]]]