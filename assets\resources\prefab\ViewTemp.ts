import { _decorator, Button, Component,   Node } from 'cc';
import { ViewBase } from '../../scripts/libs/manager/ViewBase';
 
 
const { ccclass, property } = _decorator;

@ccclass('ViewTemp')
export class ViewTemp extends ViewBase {

    @property(Button)
    private btnCut: Button = null;

    private _tag: HTMLElement
    private _txt: HTMLElement

    start() {

    }
    async onOpenCompleted() {

         
    }
    update(deltaTime: number) {

    }
}


