[1, ["52fHu7D8hGm5vLaoALoXCl", "46IdxunHlHYYawWmS60uVs@f9941", "c4QFjyHkxBQoGsOs+ecoqi@f9941", "20g1ukYUVPvKWKBRznAKo+@f9941", "54TknWPwVPqJqeCR+Y/Czo@f9941", "95EkngnxZFbYuFpsqVTaFr@f9941", "02+OF75yFB4rRm3SMMlutV"], ["node", "_font", "root", "data", "_spriteFrame", "_target", "sprBg", "lbCost", "lbName", "svContent", "btnClose", "sprFrame", "ndRoot", "_parent", "_normalSprite", "_hoverSprite", "_pressedSprite", "_disabledSprite", "itemPrefab"], [["cc.Node", ["_name", "_layer", "_components", "_prefab", "_children", "_parent", "_lpos"], 1, 9, 4, 2, 1, 5], ["cc.Label", ["_string", "_actualFontSize", "_isSystemFontUsed", "_outlineWidth", "_fontSize", "_enableOutline", "node", "__prefab", "_outlineColor", "_color", "_font"], -3, 1, 4, 5, 5, 6], ["cc.Node", ["_name", "_layer", "_parent", "_components", "_prefab", "_lpos", "_children"], 1, 1, 12, 4, 5, 2], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.Sprite", ["_type", "_sizeMode", "node", "__prefab", "_spriteFrame"], 1, 1, 4, 6], ["cc.<PERSON><PERSON>", ["_transition", "_zoomScale", "node", "__prefab", "_target", "_normalColor"], 1, 1, 4, 1, 5], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["43d136/P6xI7ranqG/bVcc1", ["node", "__prefab", "lbName", "lbCost", "sprBg"], 3, 1, 4, 1, 1, 1], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["bcb346152ZHkr5KKAwxSum+", ["node", "__prefab", "ndRoot", "sprFrame", "btnClose", "svContent"], 3, 1, 4, 1, 1, 1, 1], ["cc.Mask", ["node", "__prefab"], 3, 1, 4], ["cc.Widget", ["_alignFlags", "_left", "_right", "_originalWidth", "_originalHeight", "node", "__prefab"], -2, 1, 4], ["cc.Graphics", ["node", "__prefab", "_fillColor"], 3, 1, 4, 5], ["cc.Layout", ["_resizeMode", "_layoutType", "_spacingX", "_spacingY", "_constraintNum", "node", "__prefab"], -2, 1, 4], ["d2bd8Ybyq5JP6Lhg2nKR4ii", ["horizontal", "type", "node", "__prefab", "_content"], 1, 1, 4, 1]], [[7, 0, 2], [9, 0, 1, 2, 3, 4, 5, 5], [3, 0, 1, 2, 3, 1], [3, 0, 1, 2, 1], [2, 0, 1, 2, 3, 4, 5, 3], [6, 0, 2], [0, 0, 1, 5, 2, 3, 6, 3], [0, 0, 1, 4, 2, 3, 3], [2, 0, 1, 2, 3, 4, 3], [3, 0, 1, 1], [0, 0, 1, 4, 2, 3, 6, 3], [0, 0, 1, 5, 4, 2, 3, 3], [0, 0, 1, 5, 2, 3, 3], [2, 0, 1, 2, 6, 3, 4, 5, 3], [5, 0, 1, 2, 3, 4, 3], [5, 0, 2, 3, 5, 4, 2], [8, 0, 1, 2, 3, 4, 1], [1, 0, 1, 4, 2, 3, 6, 7, 9, 8, 10, 6], [1, 0, 1, 2, 5, 3, 6, 7, 8, 6], [1, 0, 1, 4, 2, 3, 6, 7, 9, 8, 6], [1, 0, 1, 2, 5, 3, 6, 7, 9, 8, 10, 6], [4, 2, 3, 1], [4, 0, 2, 3, 4, 2], [4, 0, 1, 2, 3, 3], [10, 0, 1, 2, 3, 4, 5, 1], [11, 0, 1, 1], [12, 0, 1, 2, 3, 4, 5, 6, 6], [13, 0, 1, 2, 1], [14, 0, 1, 2, 3, 4, 5, 6, 6], [15, 0, 1, 2, 3, 4, 3]], [[[[5, "UnitSelectGameType"], [10, "UnitSelectGameType", 33554432, [-9, -10, -11, -12], [[3, -2, [0, "2cmK8xY2pJO4jiS55ugMFB"], [5, 643, 197]], [14, 3, 1.02, -4, [0, "efdbBoU6VMHJFpEMS/AdIB"], -3], [16, -8, [0, "a8LkIpN7BIq6GtON1I4D0O"], -7, -6, -5]], [1, "334jNrgSxHG59gJkjUYNXa", null, null, null, -1, 0], [1, 0, 274.3800000000001, 0]], [8, "sprBg", 33554432, 1, [[[3, -13, [0, "69rYimeh5COqWXXc8S6FgH"], [5, 40, 36]], -14], 4, 1], [1, "0eyXsUDQ9O/rjoQPK83xGz", null, null, null, 1, 0]], [4, "lbName", 33554432, 1, [[[2, -15, [0, "3aVJhRVdVILLStoluQghLG"], [5, 86, 56.4], [0, 0, 0.5]], -16], 4, 1], [1, "75zlaSnuxFdrdD9pjG24D9", null, null, null, 1, 0], [1, -258.709, 57.31099999999992, 0]], [6, "lbSub", 33554432, 1, [[2, -17, [0, "06vwkIhLpAA4T63t0mmyvN"], [5, 180, 50.4], [0, 0, 0.5]], [17, "掉落大量碎片", 30, 30, false, 3, -18, [0, "13xVasvSFDY6v5ARjvff5Z"], [4, 4287776798], [4, 4280236409], 0]], [1, "59dNpzDi9H4pqwaXX4lKDZ", null, null, null, 1, 0], [1, -258.709, -4.533000000000129, 0]], [4, "lbCost", 33554432, 1, [[[2, -19, [0, "3bRRt3wi1J9J06z0cEymte"], [5, 120, 50.4], [0, 0, 0.5]], -20], 4, 1], [1, "bbQSpOJ4NLB4znN76t6Adf", null, null, null, 1, 0], [1, -258.709, -55.636000000000195, 0]], [21, 2, [0, "7cEyMXePxMNpc7R+V2uPUw"]], [18, "简单", 40, false, true, 3, 3, [0, "1czUIoePVNqakGCQ7cJNCJ"], [4, 4289534740]], [19, "调整次数", 30, 30, false, 3, 5, [0, "34/AU5GM1JyIWQo0BLy12N"], [4, 4287776798], [4, 4280236409]]], 0, [0, 2, 1, 0, 0, 1, 0, 5, 1, 0, 0, 1, 0, 6, 6, 0, 7, 8, 0, 8, 7, 0, 0, 1, 0, -1, 2, 0, -2, 3, 0, -3, 4, 0, -4, 5, 0, 0, 2, 0, -2, 6, 0, 0, 3, 0, -2, 7, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, -2, 8, 0, 3, 1, 20], [0, 7, 8], [1, 1, 1], [0, 0, 0]], [[[5, "ViewSelectGameType"], [7, "ViewSelectGameType", 33554432, [-8], [[9, -2, [0, "deUNmy1wBEg6X2ZBcIcyvr"]], [24, -7, [0, "08eAMPzKdI8r6sVW18SEYd"], -6, -5, -4, -3]], [1, "0aMqU+TRpDV4RCgt7E70tZ", null, null, null, -1, 0]], [11, "ndCore", 33554432, 1, [-10, -11, -12, -13], [[9, -9, [0, "f1vVGGbDBK56AHPvTTgngS"]]], [1, "ba9/Un8YtA4ZQO4S8lCjKP", null, null, null, 1, 0]], [7, "view", 33554432, [-18], [[2, -14, [0, "bfXS+dToVGn7WUOkAJal8s"], [5, 860, 980], [0, 0.5, 1]], [25, -15, [0, "efm7lvZZtFiLMNZgnK2uoC"]], [26, 45, -55, -55, 240, 250, -16, [0, "e6+AqErzVDgaLGRrxGfOL9"]], [27, -17, [0, "7arOIQRNJLZrtNhO2iKy1B"], [4, 16777215]]], [1, "afHCovsklHlpwDzfvGQ+ne", null, null, null, 1, 0]], [4, "btnClose", 33554432, 2, [[[3, -19, [0, "7aZnUl6ztGfLv3xDN8/AGg"], [5, 112, 113]], [22, 1, -20, [0, "778kV98nBGRZiinsE+P9Ok"], 0], -21], 4, 4, 1], [1, "3096IkNbhAsbtQCMKuDqrG", null, null, null, 1, 0], [1, 336.976, 534.9490000000001, 0]], [13, "svV", 33554432, 2, [3], [[[2, -22, [0, "101Gx1mqhDybRxq7HBy+I+"], [5, 750, 980], [0, 0.5, 1]], -23], 4, 1], [1, "a8pIGhPeRCHbqNeRXoeVEH", null, null, null, 1, 0], [1, 0, 464.74, 0]], [12, "content", 33554432, 3, [[2, -24, [0, "0cxefcfkROLqk36mpWKrEL"], [5, 750, -20], [0, 0.5, 1]], [28, 1, 2, 50, 20, -1.8, -25, [0, "4a1aYWv99BCpJQDKzpNoX8"]]], [1, "5e+QuVU2RBmZpHRH/QqXHN", null, null, null, 1, 0]], [8, "Sprite", 33554432, 2, [[[3, -26, [0, "19ZurFklhMA5zIFFWof2IU"], [5, 706, 1100]], -27], 4, 1], [1, "2f4Hv9BbxHoqD+OjMrYAYt", null, null, null, 1, 0]], [6, "Label", 33554432, 2, [[3, -28, [0, "ab1EzDB7ZPzIU5BHMOBDpr"], [5, 177.1999969482422, 56.4]], [20, "副本选择 ", 40, false, true, 3, -29, [0, "ec0rZv47NNS4wX0Bi6fTNR"], [4, 4292605695], [4, 4278929278], 1]], [1, "ddO/eOhnpKsJ2ed1HML4ov", null, null, null, 1, 0], [1, -15.085000000000036, 515.924, 0]], [23, 1, 0, 7, [0, "38mo3M6v5AnaCDKcqyyjvt"]], [15, 3, 4, [0, "7bBOsRTL9Po6mctipplldG"], [4, 4292269782], 4], [29, false, 1, 5, [0, "1bXKaQ+u1DNbr124i8+20h"], 6]], 0, [0, 2, 1, 0, 0, 1, 0, 9, 11, 0, 10, 10, 0, 11, 9, 0, 12, 2, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, -1, 7, 0, -2, 4, 0, -3, 8, 0, -4, 5, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -1, 6, 0, 0, 4, 0, 0, 4, 0, -3, 10, 0, 0, 5, 0, -2, 11, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, -2, 9, 0, 0, 8, 0, 0, 8, 0, 3, 1, 3, 13, 5, 29], [0, 0, 9, 10, 10, 10, 10, 11], [4, 1, 4, 14, 15, 16, 17, 18], [1, 0, 2, 1, 3, 4, 5, 6]]]]