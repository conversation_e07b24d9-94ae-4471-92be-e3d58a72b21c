type TimerDateParamsType = string | number | Date
export enum TimeStatusEnum {
    Before,
    Between,
    After
}
export default class TimeUtil {
    private static _timeOffet: number = 0

    constructor() {
    }
    /** 间隔天数 */
    public static daysBetween(time1: number | string | Date, time2: number | string | Date): number {
        if (time2 == undefined || time2 == null) {
            time2 = +new Date();
        }
        let startDate = new Date(time1).toLocaleDateString()
        let endDate = new Date(time2).toLocaleDateString()
        let startTime = new Date(startDate).getTime();
        let endTime = new Date(endDate).getTime();
        let dates = Math.abs((startTime - endTime)) / (1000 * 60 * 60 * 24);
        return dates;
    }

    /** 间隔秒数 */
    public static secsBetween(time1: number, time2: number) {
        if (time2 == undefined || time2 == null) {
            time2 = +new Date();
        }
        let dates = Math.abs((time2 - time1)) / (1000);
        return dates;
    }

    /**
     * 代码休眠时间
     * @param ms 毫秒
     */
    public static async sleep(ms: number) {
        return new Promise<void>((resolve) => {
            setTimeout(() => {
                resolve();
            }, ms)
        });
    }
    /** 设置当前服务器时间 */
    static initServerTime(serverTime: number) {
        TimeUtil._timeOffet = Date.now() - serverTime
    }

    /** 获取当前服务器时间（校正） */
    static getServerTime(): number {
        return Date.now() - TimeUtil._timeOffet
    }


    /** 获取今天运行时间 */
    static convertTodayTime(tsp: number): number {
        return tsp % 86400000 - new Date().getTimezoneOffset() * 60000
    }

    /** 判断是否在对应时间范围内 */
    static isInPeroid(currTime: number, startTime: number, endTime: number) {
        return (currTime >= startTime && currTime <= endTime)
    }

    /**
     * 时间格式转换为时间戳
     * @param time 时间格式字符串
     * @returns 时间戳
     */
    static getTimestamp(time: string) {
        // @ts-ignore
        return Date.parse(new Date(time));
    }

    /**
     * 将时间戳转换为时间格式
     * @param timestamp 时间戳
     * @param splitSymbol 分隔符
     * @returns 
     */
    static formatTimestamp(timestamp: number, splitSymbol: string) {
        const date = new Date(timestamp);
        return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`
    }

    /**
     * 将时间戳转换为日期（年月日时分秒）
     * @param timestamp 时间戳
     * @param splitSymbol 分隔符
     * @returns 
     */
    static formatTimestampToDate(timestamp: number, splitSymbol: string, filterYear: boolean = false, isHaveHours: boolean = false) {
        const date = new Date(timestamp);
        let month = date.getMonth() + 1 < 10 ? `0${date.getMonth() + 1}` : date.getMonth() + 1;
        let day = date.getDate() < 10 ? `0${date.getDate()}` : date.getDate();
        let hour = date.getHours() < 10 ? `0${date.getHours()}` : date.getHours();
        let minute = date.getMinutes() < 10 ? `0${date.getMinutes()}` : date.getMinutes();
        let second = date.getSeconds() < 10 ? `0${date.getSeconds()}` : date.getSeconds();

        let year = filterYear ? '' : `${date.getFullYear()}${splitSymbol}`
        if (isHaveHours) {
            return `${year}${month}${splitSymbol}${day} ${hour}:${minute}:${second}`
        } else {
            return `${year}${month}${splitSymbol}${day}`
        }

    }



    /** 中文时间格式化 */
    static formatTimeCN(milliSecond: number): string {
        if (milliSecond < 0) {
            return ''
        }

        let second = Math.ceil(milliSecond / 1000)
        let d = Math.floor(second / 86400)
        let h = Math.floor((second % 86400) / 3600)
        let m = Math.floor((second % 3600) / 60)
        let s = second % 60

        function generate(val: number, unit: string, isPrefixZero: boolean = false) {
            return `${isPrefixZero ? (val > 9 ? val : ('0' + val)) : val}${unit}`
        }

        let str = ''
        if (d > 0 || str.length > 0) {
            str = str + generate(d, '天')
        }
        if (h > 0 || str.length > 0) {
            str = str + generate(h, '小时')
        }
        if (m > 0 || str.length > 0) {
            str = str + generate(m, '分')
        }
        if (s > 0 || str.length > 0) {
            str = str + generate(s, '秒')
        }
        return str
    }

    /** 时间格式化 */
    static formatTime(milliSecond: number, count: number = 3, splitStr: string = ':'): string {
        if (milliSecond < 0) {
            return ''
        }

        let second = Math.floor(milliSecond / 1000)
        let h = Math.floor(second / 3600)
        let m = Math.floor((second % 3600) / 60)
        let s = second % 60

        if (count === 3) {
            return `${h > 9 ? h : '0' + h}${splitStr}${m > 9 ? m : '0' + m}${splitStr}${s > 9 ? s : '0' + s}`
        } else if (count === 2) {
            return `${m > 9 ? m : '0' + m}${splitStr}${s > 9 ? s : '0' + s}`
        } else if (count === 1) {
            return `${s > 9 ? s : '0' + s}`
        }
    }

    /**
     * 获取上个星期几（index）的日期
     * @param data 日期 "2024-04-01" 
     * @param index 星期几  1~7
     * @returns 
     */
    static getLastDayByIndex(data: string, index: number = 1) {
        if (!data || index > 7 || index < 1) {
            return null
        }
        let today = new Date(data);
        if (today.getDay() == 6) {
            return TimeUtil.formatTimestampToDate(today.getTime(), '-')
        }
        for (var i = 0; i < 7; i++) {
            today.setDate(today.getDate() - 1);
            let year = today.getFullYear();
            let mon = today.getMonth() + 1;
            let d = today.getDate();
            let data = year + "-" + (mon >= 10 ? mon : `0${mon}`) + "-" + `${d >= 10 ? d : `0${d}`}`;
            let day = TimeUtil.getDayOfWeek(data);
            if (day == `${index}`) {
                return data;
            }
        }
        return null
    }
    static getFirstDayOfTheWeek(data: string) {
        let today = new Date(data);
        const dayOfWeek = today.getDay(); // 0（周日）到6（周六）
        const diff = dayOfWeek === 0 ? -6 : 1 - dayOfWeek; // 调整到最近的周一
        const monday = new Date(today);
        monday.setDate(today.getDate() + diff);

        return monday.getFullYear() + '-' + (monday.getMonth() + 1) + '-' + monday.getDate();
    }
    static getFirstDayOfTheMonth() {
        // 创建当前日期对象
        const currentDate = new Date(TimeUtil.getServerTime());
        // 获取本月第一天的日期（月份从0开始，但日期设为1即可）
        const firstDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
        let year = firstDayOfMonth.getFullYear();
        let mon = firstDayOfMonth.getMonth() + 1;
        let d = firstDayOfMonth.getDate();
        let time = year + "-" + (mon >= 10 ? mon : `0${mon}`) + "-" + `${d >= 10 ? d : `0${d}`}`;
        return time;
    }
    /* // 示例用法
    const timeDiff = getTimeUntilNextSaturday();
    console.log(`距离下周六零点还有：${timeDiff.days}天 ${timeDiff.hours}小时 ${timeDiff.minutes}分钟 ${timeDiff.seconds}秒`); */
    static getTimeUntilNextSaturday() {
        const now = new Date(TimeUtil.getServerTime());
        const target = new Date(now);
        target.setHours(0, 0, 0, 0); // 设置为今天的零点

        const currentDay = target.getDay(); // 获取基于零点的星期几（0-6，0是周日）
        const daysToAdd = (6 - currentDay + 7) % 7; // 计算到下一个周六的天数
        target.setDate(target.getDate() + daysToAdd);

        // 如果目标时间仍早于或等于当前时间，则加7天
        if (target <= now) {
            target.setDate(target.getDate() + 7);
        }

        const diff = target.getTime() - now.getTime();
        return diff
        // 转换为天、小时、分钟、秒
        // const days = Math.floor(diff / (1000 * 60 * 60 * 24));
        // const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        // const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
        // const seconds = Math.floor((diff % (1000 * 60)) / 1000);

        // return {
        //     days,
        //     hours,
        //     minutes,
        //     seconds,
        // //     totalMilliseconds: diff
        // };
    }

    static getMInfo(shuliang = 7,) {
        var today = new Date();
        var day = today.getDay();
        day = 6 - day; // 获取当前是星期几
        var arr_all = [];
        var dangqian_obj = {} as any;
        var Yday = new Date();
        Yday.setDate(Yday.getDate() - 1);
        dangqian_obj.e = Yday.getFullYear() + "-" + (Yday.getMonth() + 1) + "-" + Yday.getDate();
        var Oday = new Date();
        Oday.setDate(Oday.getDate() - day);

        dangqian_obj.day = TimeUtil.getDayOfWeek(dangqian_obj.e);
        arr_all.push(dangqian_obj);

        var today1 = new Date();
        for (var iii = 0; iii < shuliang - 1; iii++) {
            var objj = {} as any;
            if (iii != 0) {
                day = 1;
            } else {
                day = day + 1;
            }
            today1.setDate(today1.getDate() - day);
            objj.e = today1.getFullYear() + "-" + (today1.getMonth() + 1) + "-" + today1.getDate();
            objj.day = TimeUtil.getDayOfWeek(objj.e);;
            arr_all.push(objj);
        }
        return arr_all;
    }

    //  getDayOfWeek('2023-04-01')
    static getDayOfWeek(dateString) {
        const days = ['7', '1', '2', '3', '4', '5', '6'];
        const date = new Date(dateString);
        return days[date.getDay()];
    }



    // 根据秒数切分为时分秒
    static fmtSecondsToHMS(totalSeconds: number) {
        totalSeconds = totalSeconds || 0
        const hours = Math.floor(totalSeconds / 3600)
        const minutes = Math.floor((totalSeconds % 3600) / 60)
        const seconds = totalSeconds % 60

        const h = hours.toString().padStart(2, '0')
        const m = minutes.toString().padStart(2, '0')
        const s = seconds.toString().padStart(2, '0')

        return {
            h,
            m,
            s
        }
    }
    // 根据秒数切分为分秒
    static fmtSecondsToMS(totalSeconds: number) {
        totalSeconds = totalSeconds || 0

        const minutes = Math.floor(totalSeconds / 60)
        const seconds = totalSeconds % 60

        const m = minutes.toString().padStart(2, '0')
        const s = seconds.toString().padStart(2, '0')

        return {
            m,
            s
        }
    }
    static diff(
        startTime: TimerDateParamsType,
        endTime: TimerDateParamsType,
        unit: 'milliseconds' | 'seconds' | 'minutes' | 'hours' | 'days' = 'seconds'
    ): number {
        const diffInMilliseconds = TimeUtil.date(endTime).getTime() - TimeUtil.date(startTime).getTime()

        switch (unit) {
            case 'milliseconds':
                return diffInMilliseconds
            case 'seconds':
                return Math.round(diffInMilliseconds / 1000)
            case 'minutes':
                return Math.round(diffInMilliseconds / (1000 * 60))
            case 'hours':
                return Math.round(diffInMilliseconds / (1000 * 60 * 60))
            case 'days':
                return Math.round(diffInMilliseconds / (1000 * 60 * 60 * 24))
            default:
                throw new Error('Invalid time unit.')
        }
    }
    // new Date 兼容性处理
    static date(val: TimerDateParamsType) {
        if (val instanceof Date) {
            return val
        } else if (typeof val === 'string') {
            // ios 不支持-
            //val = val.replaceAll('-', '/')
            return new Date(val)
        }
        return new Date(val)
    }

    static checkTimeStatus(startTime: TimerDateParamsType, endTime: TimerDateParamsType) {
        const now = +new Date()
        let diff = TimeUtil.diff(now, startTime)
        // 未开始
        if (diff > 0) {
            return TimeStatusEnum.Before
        }
        diff = TimeUtil.diff(now, endTime)
        // 已结束
        if (diff < 0) {
            return TimeStatusEnum.After
        }

        return TimeStatusEnum.Between
    }
    static dateFormat(time: TimerDateParamsType, fmt: string = 'YYYY-MM-DD hh:mm:ss') {
        const date = TimeUtil.date(time)
        let ret
        const opt = {
            'Y+': date.getFullYear().toString(), // 年
            'M+': (date.getMonth() + 1).toString(), // 月
            'D+': date.getDate().toString(), // 日
            'h+': date.getHours().toString(), // 时
            'm+': date.getMinutes().toString(), // 分
            's+': date.getSeconds().toString() // 秒
            // 有其他格式化字符需求可以继续添加，必须转化成字符串
        }
        for (let k in opt) {
            ret = new RegExp('(' + k + ')').exec(fmt)
            if (ret) {
                fmt = fmt.replace(
                    ret[1],
                    ret[1].length == 1 ? opt[k] : opt[k].padStart(ret[1].length, '0')
                )
            }
        }
        return fmt
    }
}